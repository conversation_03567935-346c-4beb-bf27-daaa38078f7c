#!/usr/bin/env python3
"""
Test script for Food Cavity Risk Classifier

This script demonstrates how to use the food cavity risk classifier
with a small test dataset.
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import argparse
import sys

def parse_args():
    parser = argparse.ArgumentParser(description="Test Food Cavity Risk Classifier")
    parser.add_argument("--model_path", type=str, default="food_cavity_classifier_output/model_best.pt",
                      help="Path to the trained model file")
    parser.add_argument("--output_dir", type=str, default="./food_cavity_test_results",
                      help="Directory to save test results")
    parser.add_argument("--show", action="store_true", default=False,
                      help="Show visualizations instead of saving them")
    return parser.parse_args()

def create_test_data():
    """Create a small test dataset with common food items"""
    data = {
        'Food': [
            'Apple', 'Banana', 'Orange', 
            'Chocolate Cake', 'Ice Cream', '<PERSON>', 
            '<PERSON><PERSON><PERSON><PERSON>', 'Spinach', 'Chicken Breast', 
            'Soda', 'Juice', 'Water'
        ],
        'Calories': [
            52, 89, 47, 
            371, 207, 396, 
            34, 23, 165, 
            42, 45, 0
        ],
        '<PERSON>': [
            10.4, 12.2, 9.4, 
            43.0, 21.2, 74.8, 
            1.7, 0.4, 0, 
            10.6, 9.8, 0
        ],
        'Fiber': [
            2.4, 2.6, 2.4, 
            1.3, 0.5, 0, 
            2.6, 2.2, 0, 
            0, 0.2, 0
        ],
        'Protein': [
            0.3, 1.1, 0.9, 
            5.1, 3.5, 0, 
            2.8, 2.9, 31, 
            0, 0.5, 0
        ],
        'Fat': [
            0.2, 0.3, 0.1, 
            14.4, 11, 0.2, 
            0.4, 0.4, 3.6, 
            0, 0.1, 0
        ],
        'Carbohydrates': [
            13.8, 22.8, 11.8, 
            63, 24, 98, 
            6.6, 3.6, 0, 
            10.6, 11.2, 0
        ],
        'Calcium': [
            6, 5, 40, 
            60, 137, 0, 
            47, 99, 15, 
            4, 11, 7
        ],
        'VitaminC': [
            4.6, 8.7, 53.2, 
            0, 0.6, 0, 
            89.2, 28.1, 0, 
            0, 30, 0
        ],
        'pH': [
            3.3, 4.5, 3.7, 
            7.2, 6.5, 5.4, 
            6.8, 6.7, 6.5, 
            2.5, 3.8, 7.0
        ]
    }
    
    df = pd.DataFrame(data)
    return df

def main():
    args = parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Create test data
    test_df = create_test_data()
    
    # Save test data to CSV
    test_csv_path = os.path.join(args.output_dir, 'test_foods.csv')
    test_df.to_csv(test_csv_path, index=False)
    print(f"Test data saved to {test_csv_path}")
    
    # Set device
    import torch
    device = torch.device("cuda" if torch.cuda.is_available() else 
                         "mps" if torch.backends.mps.is_available() else 
                         "cpu")
    print(f"Using device: {device}")
    
    # Try to load model and prediction functions
    try:
        # Import here to handle potential import errors gracefully
        from food_cavity_classifier import CavityRiskClassifier
        from food_cavity_predict import load_model, predict_cavity_risk, visualize_predictions
        
        # Load model
        model, feature_names, class_names, scaler = load_model(args.model_path, device)
        
        # Prepare test data for prediction
        X = test_df.drop('Food', axis=1)
        food_names = test_df['Food'].tolist()
        
        # Make predictions
        results = predict_cavity_risk(model, X, scaler, device, X.columns.tolist(), class_names)
        
        # Display results
        print("\nCavity Risk Prediction Results:")
        print("-" * 50)
        for food, result in zip(food_names, results):
            risk_name = result['risk_name']
            high_prob = result['probabilities']['High Risk'] * 100
            print(f"Food: {food}")
            print(f"Risk Level: {risk_name}")
            print(f"High Risk Probability: {high_prob:.2f}%")
            print("-" * 50)
        
        # Create visualizations
        visualize_predictions(results, food_names, args.output_dir, args.show)
        
        print(f"\nResults and visualizations saved to {args.output_dir}")
    
    except Exception as e:
        print(f"Error loading model or making predictions: {e}")
        print("If the model doesn't exist, you may need to train it first using food_cavity_classifier.py")
        
        # Create expected output visualization for demonstration
        plt.figure(figsize=(12, 8))
        
        # Sample expected output (if model was trained)
        foods = test_df['Food'].tolist()
        expected_risk = [
            'Low', 'Low', 'Low',
            'High', 'High', 'High',
            'Low', 'Low', 'Low',
            'Medium', 'Medium', 'Low'
        ]
        
        # Color code based on expected risk
        colors = ['green' if r == 'Low' else 'orange' if r == 'Medium' else 'red' for r in expected_risk]
        
        plt.bar(range(len(foods)), np.ones(len(foods)), color=colors)
        plt.xticks(range(len(foods)), foods, rotation=45, ha='right')
        plt.title('Expected Cavity Risk Classification (Simulated)')
        plt.tight_layout()
        
        if args.show:
            plt.show()
        else:
            plt.savefig(os.path.join(args.output_dir, 'expected_results.png'))
            plt.close()
            
        print(f"Expected results visualization saved to {args.output_dir}/expected_results.png")

if __name__ == "__main__":
    main() 