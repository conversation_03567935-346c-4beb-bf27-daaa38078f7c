/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      screens: {
        'xs': '375px',
        'sm': '640px',
        'md': '768px',
        'lg': '1024px',
        'xl': '1280px',
        '2xl': '1536px',
      },
      fontFamily: {
        sans: ['Plus Jakarta Sans', 'system-ui', 'sans-serif'],
        display: ['Playfair Display', 'serif'],
        heading: ['Montserrat', 'sans-serif'],
      },
      spacing: {
        'safe-top': 'env(safe-area-inset-top)',
        'safe-bottom': 'env(safe-area-inset-bottom)',
        'safe-left': 'env(safe-area-inset-left)',
        'safe-right': 'env(safe-area-inset-right)',
      },
      colors: {
        indigo: {
          50: '#eef2ff',
          100: '#e0e7ff', 
          200: '#c7d2fe',
          300: '#a5b4fc',
          400: '#818cf8',
          500: '#6366f1',
          600: '#4f46e5',
          700: '#4338ca',
          800: '#3730a3',
          900: '#312e81'
        },
        periwinkle: {
          50: '#f5f7ff',
          100: '#ebf0ff',
          200: '#d6e0ff',
          300: '#adc1ff',
          400: '#819dff',
          500: '#5475ff',
          600: '#3d5eff',
          700: '#2745ff',
          800: '#1e37e5',
          900: '#1a31cc'
        },
        royal: {
          50: '#f3f1ff',
          100: '#ebe5ff',
          200: '#d9ceff',
          300: '#bea6ff',
          400: '#9f75ff',
          500: '#843dff',
          600: '#7916ff',
          700: '#6600ff',
          800: '#5200cc',
          900: '#4400a3'
        },
        pearl: {
          50: '#fafafa',
          100: '#f5f5f5',
          200: '#eeeeee',
        },
        primary: {
          400: '#818cf8',
          500: '#6366f1',
          600: '#4f46e5'
        },
        accent: {
          400: '#9f75ff',
          500: '#843dff',
          600: '#7916ff'
        }
      },
      backgroundImage: {
        'grid-pattern': "url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath opacity='.5' d='M96 95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9zm-1 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")",
      },
      keyframes: {
        fadeInUp: {
          '0%': { opacity: '0', transform: 'translateY(10px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' }
        },
        gradient: {
          '0%, 100%': { backgroundPosition: '0% 50%' },
          '50%': { backgroundPosition: '100% 50%' }
        },
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' }
        },
        shimmer: {
          '0%': { backgroundPosition: '-200% 0' },
          '100%': { backgroundPosition: '200% 0' }
        },
        dash: {
          '0%': { strokeDashoffset: '20' },
          '100%': { strokeDashoffset: '0' }
        },
        'spin-slow': {
          '0%': { transform: 'rotate(0deg)' },
          '100%': { transform: 'rotate(360deg)' }
        },
        pulse: {
          '0%, 100%': { opacity: 0.5, transform: 'scale(1)' },
          '50%': { opacity: 1, transform: 'scale(1.2)' }
        },
        float: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-10px)' }
        },
        'float-slow': {
          '0%': { transform: 'translateY(0) translateX(0)' },
          '33%': { transform: 'translateY(-10px) translateX(5px)' },
          '66%': { transform: 'translateY(5px) translateX(-5px)' },
          '100%': { transform: 'translateY(0) translateX(0)' }
        },
        'float-medium': {
          '0%': { transform: 'translateY(0) translateX(0)' },
          '33%': { transform: 'translateY(10px) translateX(-10px)' },
          '66%': { transform: 'translateY(-5px) translateX(5px)' },
          '100%': { transform: 'translateY(0) translateX(0)' }
        },
        'float-fast': {
          '0%': { transform: 'translateY(0) translateX(0)' },
          '33%': { transform: 'translateY(-7px) translateX(-7px)' },
          '66%': { transform: 'translateY(7px) translateX(3px)' },
          '100%': { transform: 'translateY(0) translateX(0)' }
        },
        'pulse-soft': {
          '0%, 100%': { opacity: 0.4, transform: 'scale(1)' },
          '50%': { opacity: 0.8, transform: 'scale(1.05)' }
        },
        'pulse-slow': {
          '0%, 100%': { opacity: 0.3, transform: 'scale(1)' },
          '50%': { opacity: 0.7, transform: 'scale(1.1)' }
        },
        wiggle: {
          '0%, 100%': { transform: 'rotate(-3deg)' },
          '50%': { transform: 'rotate(3deg)' }
        },
        bounce: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-15px)' }
        },
        glow: {
          '0%, 100%': { textShadow: '0 0 5px rgba(255,255,255,0.2)' },
          '50%': { textShadow: '0 0 20px rgba(255,255,255,0.6), 0 0 30px rgba(99,102,241,0.4)' }
        },
        'scale-up': {
          '0%': { transform: 'scale(0.95)', opacity: '0.7' },
          '100%': { transform: 'scale(1)', opacity: '1' }
        },
        'border-glow': {
          '0%, 100%': { boxShadow: '0 0 5px rgba(99,102,241,0.3)' },
          '50%': { boxShadow: '0 0 15px rgba(99,102,241,0.7), 0 0 20px rgba(132,61,255,0.5)' }
        },
        'slide-in-left': {
          '0%': { transform: 'translateX(-20px)', opacity: '0' },
          '100%': { transform: 'translateX(0)', opacity: '1' }
        },
        'slide-in-right': {
          '0%': { transform: 'translateX(20px)', opacity: '0' },
          '100%': { transform: 'translateX(0)', opacity: '1' }
        },
        'float-orb': {
          '0%': { transform: 'translateY(0) translateX(0) scale(1)' },
          '25%': { transform: 'translateY(-15px) translateX(10px) scale(1.05)' },
          '50%': { transform: 'translateY(5px) translateX(15px) scale(0.95)' },
          '75%': { transform: 'translateY(10px) translateX(-5px) scale(1.02)' },
          '100%': { transform: 'translateY(0) translateX(0) scale(1)' }
        }
      },
      animation: {
        fadeInUp: 'fadeInUp 0.5s ease-out',
        fadeIn: 'fadeIn 0.3s ease-in',
        gradient: 'gradient 15s ease infinite',
        shimmer: 'shimmer 2s infinite linear',
        dash: 'dash 3s linear infinite',
        'spin-slow': 'spin-slow 15s linear infinite',
        pulse: 'pulse 2s ease-in-out infinite',
        float: 'float 3s ease-in-out infinite',
        'float-slow': 'float-slow 15s ease-in-out infinite',
        'float-medium': 'float-medium 12s ease-in-out infinite',
        'float-fast': 'float-fast 9s ease-in-out infinite',
        'pulse-soft': 'pulse-soft 4s ease-in-out infinite',
        'pulse-slow': 'pulse-slow 7s ease-in-out infinite',
        wiggle: 'wiggle 1s ease-in-out infinite',
        bounce: 'bounce 2s ease-in-out infinite',
        glow: 'glow 2s ease-in-out infinite',
        'scale-up': 'scale-up 0.4s ease-out',
        'border-glow': 'border-glow 3s ease-in-out infinite',
        'slide-in-left': 'slide-in-left 0.5s ease-out',
        'slide-in-right': 'slide-in-right 0.5s ease-out',
        'float-orb': 'float-orb 18s ease-in-out infinite'
      },
      typography: {
        DEFAULT: {
          css: {
            maxWidth: 'none',
            color: 'rgb(237 233 254)',
            fontFamily: 'Plus Jakarta Sans, sans-serif',
            h1: {
              color: 'rgb(129, 140, 248)',
              fontFamily: 'Montserrat, sans-serif',
              fontWeight: '600',
            },
            h2: {
              color: 'rgb(129, 140, 248)',
              fontFamily: 'Montserrat, sans-serif',
              fontWeight: '600',
            },
            h3: {
              color: 'rgb(255 255 255)',
              fontFamily: 'Montserrat, sans-serif',
              fontWeight: '500',
            },
            strong: {
              color: 'rgb(255 255 255)',
              fontWeight: '600',
            },
            a: {
              color: 'rgb(99 102 241)',
              '&:hover': {
                color: 'rgb(129 140 248)',
              },
            },
            code: {
              color: 'rgb(255 255 255)',
            },
            blockquote: {
              color: 'rgb(209 213 219)',
              fontFamily: 'Playfair Display, serif',
              fontStyle: 'italic',
            },
          },
        },
      },
    },
  },
  future: {
    hoverOnlyWhenSupported: true,
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
  ],
}