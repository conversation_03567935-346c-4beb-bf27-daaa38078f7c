@objects
    body            css body
    viewport        css body

= Responsive Behavior =
    body:
        inside viewport 0px
        
= Touch Targets =
    @on mobile
        &forEach [nav-menu, button, a] as element
            ${element}:
                width > 44px
                height > 44px
                
= Safe Area Insets =
    @on mobile
        &forEach [header, footer] as element
            ${element}:
                inside viewport 0px env(safe-area-inset-top) 0px env(safe-area-inset-bottom) 