#!/usr/bin/env python3
"""
Thermal Imaging Prediction for Dental Applications

This script analyzes thermal images to detect inflammation and abnormalities 
in dental tissues using a trained model.
"""

import os
import sys
import argparse
import json
import numpy as np
import matplotlib.pyplot as plt
import time
from datetime import datetime
from pathlib import Path

import torch
import torch.nn as nn
import torch.nn.functional as F
from torchvision import transforms, models

import cv2
from PIL import Image

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description="Thermal Imaging Prediction for Dental Applications")
    parser.add_argument("--model_path", type=str, required=True,
                      help="Path to the trained model file")
    parser.add_argument("--input", type=str, required=True,
                      help="Path to input image or directory of images")
    parser.add_argument("--output_dir", type=str, default="./thermal_predictions",
                      help="Directory to save prediction results")
    parser.add_argument("--device", type=str, default=None,
                      help="Device to use (cuda, mps, cpu, or None for auto)")
    parser.add_argument("--threshold", type=float, default=0.5,
                      help="Threshold for abnormality classification")
    parser.add_argument("--show", action="store_true", default=False,
                      help="Show visualizations instead of saving them")
    parser.add_argument("--detailed", action="store_true", default=False,
                      help="Show detailed thermal analysis")
    return parser.parse_args()

def load_model(model_path, device):
    """Load the trained model"""
    # Check if model exists
    if not os.path.exists(model_path):
        raise FileNotFoundError(f"Model not found: {model_path}")
    
    # Load model checkpoint
    checkpoint = torch.load(model_path, map_location=device)
    
    # Extract model information
    model_name = checkpoint.get("model_name", "resnet50")
    
    # Create model
    if model_name == "resnet18":
        model = models.resnet18(weights=None)
        model.fc = nn.Linear(model.fc.in_features, 2)
    elif model_name == "resnet50":
        model = models.resnet50(weights=None)
        model.fc = nn.Linear(model.fc.in_features, 2)
    elif model_name == "densenet121":
        model = models.densenet121(weights=None)
        model.classifier = nn.Linear(model.classifier.in_features, 2)
    else:
        raise ValueError(f"Unsupported model: {model_name}")
    
    # Load model weights
    model.load_state_dict(checkpoint["model_state_dict"])
    model = model.to(device)
    model.eval()
    
    print(f"Model loaded from {model_path}")
    return model

def process_image(image_path, image_size=256):
    """Process image for model input"""
    # Load image
    try:
        # Read image
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"Could not read image: {image_path}")
        
        # Convert BGR to RGB
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # Extract features from the thermal image
        features = extract_features(image)
        
        # Create a copy for visualization
        orig_image = image.copy()
        
        # Convert to PIL Image
        image_pil = Image.fromarray(image)
        
        # Apply transformations
        transform = transforms.Compose([
            transforms.Resize((image_size, image_size)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        # Transform image
        input_tensor = transform(image_pil)
        input_tensor = input_tensor.unsqueeze(0)  # Add batch dimension
        
        return orig_image, input_tensor, features
    
    except Exception as e:
        print(f"Error processing image {image_path}: {e}")
        raise

def extract_features(image):
    """Extract features from a thermal image"""
    # Convert to grayscale for analysis
    gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
    
    # Extract features
    features = {}
    
    # Use image statistics as proxy for temperature
    features["max_temp"] = float(np.max(gray) / 10)  # Scale to approximate °C
    features["min_temp"] = float(np.min(gray) / 20)  # Scale to approximate °C
    features["avg_temp"] = float(np.mean(gray) / 15)  # Scale to approximate °C
    features["temp_range"] = features["max_temp"] - features["min_temp"]
    
    # Detect hot spots (bright regions in thermal image)
    _, thresh = cv2.threshold(gray, 200, 255, cv2.THRESH_BINARY)
    contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    features["hotspot_count"] = len(contours)
    features["hotspots"] = []
    
    # Extract hotspot information
    for contour in contours:
        x, y, w, h = cv2.boundingRect(contour)
        area = cv2.contourArea(contour)
        intensity = np.mean(gray[y:y+h, x:x+w])
        
        if area > 50:  # Filter out very small hotspots
            features["hotspots"].append({
                "x": int(x),
                "y": int(y),
                "width": int(w),
                "height": int(h),
                "area": float(area),
                "intensity": float(intensity),
                "approx_temp": float(intensity / 15)  # Scale to approximate °C
            })
    
    # Calculate histogram statistics
    hist = cv2.calcHist([gray], [0], None, [256], [0, 256])
    hist_norm = hist / hist.sum()
    
    # Calculate histogram skewness
    features["histogram_skew"] = float(np.sum((np.arange(256) - features["avg_temp"] * 15)**3 * hist_norm) / 
                                (np.std(gray)**3))
    
    # Calculate histogram entropy
    hist_entropy = -np.sum(hist_norm * np.log2(hist_norm + 1e-7))
    features["histogram_entropy"] = float(hist_entropy)
    
    # Calculate regional temperature differences
    h, w = gray.shape
    regions = {
        "top_left": gray[0:h//2, 0:w//2],
        "top_right": gray[0:h//2, w//2:w],
        "bottom_left": gray[h//2:h, 0:w//2],
        "bottom_right": gray[h//2:h, w//2:w]
    }
    
    features["regions"] = {}
    for region_name, region_img in regions.items():
        features["regions"][region_name] = {
            "max": float(np.max(region_img) / 10),
            "min": float(np.min(region_img) / 20),
            "avg": float(np.mean(region_img) / 15),
            "std": float(np.std(region_img) / 30)
        }
    
    return features

def make_prediction(model, input_tensor, features, device, threshold=0.5):
    """Make prediction using the model"""
    with torch.no_grad():
        input_tensor = input_tensor.to(device)
        outputs = model(input_tensor)
        probabilities = F.softmax(outputs, dim=1)
        
        # Get probability of abnormal class
        abnormal_prob = probabilities[0, 1].item()
        
        # Determine predicted class
        predicted_class = "Abnormal" if abnormal_prob >= threshold else "Normal"
        
        # Determine severity based on model confidence and features
        severity_score = abnormal_prob
        
        # Adjust severity based on features
        if features["hotspot_count"] > 5:
            severity_score += 0.2
        elif features["hotspot_count"] > 3:
            severity_score += 0.1
        
        if features["temp_range"] > 2.0:
            severity_score += 0.2
        elif features["temp_range"] > 1.0:
            severity_score += 0.1
        
        # Clamp severity score
        severity_score = max(0.0, min(1.0, severity_score))
        
        # Map severity score to level
        severity = "HIGH" if severity_score >= 0.7 else "MEDIUM" if severity_score >= 0.4 else "LOW"
        
        # Create result dictionary
        result = {
            "predicted_class": predicted_class,
            "abnormal_probability": abnormal_prob,
            "severity_score": severity_score,
            "severity": severity,
            "features": features,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # Add dental implications
        if predicted_class == "Abnormal":
            if severity == "HIGH":
                result["dental_implications"] = [
                    "Significant inflammation detected",
                    "Possible acute pulpitis or abscess",
                    "Immediate dental consultation recommended"
                ]
            elif severity == "MEDIUM":
                result["dental_implications"] = [
                    "Moderate inflammation detected",
                    "Possible early-stage infection or pulpal inflammation",
                    "Dental evaluation recommended within 1-2 weeks"
                ]
            else:
                result["dental_implications"] = [
                    "Mild inflammation detected",
                    "Possible gingivitis or early inflammatory response",
                    "Monitor for changes in symptoms"
                ]
        else:
            result["dental_implications"] = [
                "No significant thermal abnormalities detected",
                "Regular dental check-ups recommended"
            ]
        
        return result

def visualize_prediction(image, result, output_path, show=False, detailed=False):
    """Visualize the prediction results"""
    # Create a copy of the image for visualization
    vis_image = image.copy()
    h, w, _ = vis_image.shape
    
    # Convert back to BGR for OpenCV
    vis_image = cv2.cvtColor(vis_image, cv2.COLOR_RGB2BGR)
    
    # Create a semi-transparent overlay for the visualization
    overlay = vis_image.copy()
    
    # Add title based on prediction
    title = f"Prediction: {result['predicted_class']} ({result['abnormal_probability']:.2f})"
    cv2.putText(vis_image, title, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.9, (0, 0, 255), 2)
    
    # Add severity
    severity_color = (0, 0, 255) if result["severity"] == "HIGH" else \
                    (0, 165, 255) if result["severity"] == "MEDIUM" else \
                    (0, 255, 0)
    cv2.putText(vis_image, f"Severity: {result['severity']}", (10, 70), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.9, severity_color, 2)
    
    # Add a border with color indicating severity
    border_size = 10
    vis_image = cv2.copyMakeBorder(
        vis_image, 
        border_size, border_size, border_size, border_size, 
        cv2.BORDER_CONSTANT, 
        value=severity_color
    )
    
    # Add hotspots
    if "hotspots" in result["features"]:
        for hotspot in result["features"]["hotspots"]:
            x, y, w, h = hotspot["x"], hotspot["y"], hotspot["width"], hotspot["height"]
            temp = hotspot["approx_temp"]
            
            # Color based on temperature
            color = (0, 0, 255) if temp > 38 else (0, 165, 255) if temp > 37 else (0, 255, 255)
            
            # Draw rectangle
            cv2.rectangle(vis_image, (x + border_size, y + border_size), 
                         (x + w + border_size, y + h + border_size), color, 2)
            
            # Add temperature label
            cv2.putText(vis_image, f"{temp:.1f}°C", (x + border_size, y - 5 + border_size), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
    
    # If detailed visualization is requested
    if detailed:
        # Create a larger canvas for detailed visualization
        canvas_height = vis_image.shape[0] + 300
        canvas_width = vis_image.shape[1]
        canvas = np.ones((canvas_height, canvas_width, 3), dtype=np.uint8) * 255
        
        # Copy the visualization to the canvas
        canvas[:vis_image.shape[0], :vis_image.shape[1]] = vis_image
        
        # Add detailed information
        y_offset = vis_image.shape[0] + 20
        
        # Add feature summary
        cv2.putText(canvas, "Thermal Feature Analysis:", (10, y_offset), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
        y_offset += 30
        
        features = result["features"]
        cv2.putText(canvas, f"Temperature Range: {features['temp_range']:.1f}°C", 
                   (20, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 1)
        y_offset += 25
        
        cv2.putText(canvas, f"Max Temperature: {features['max_temp']:.1f}°C", 
                   (20, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 1)
        y_offset += 25
        
        cv2.putText(canvas, f"Hotspot Count: {features['hotspot_count']}", 
                   (20, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 1)
        y_offset += 40
        
        # Add dental implications
        cv2.putText(canvas, "Dental Implications:", (10, y_offset), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
        y_offset += 30
        
        for implication in result["dental_implications"]:
            cv2.putText(canvas, f"• {implication}", 
                       (20, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 1)
            y_offset += 25
        
        # Update the visualization
        vis_image = canvas
    
    # Show or save the visualization
    if show:
        cv2.imshow("Thermal Analysis", vis_image)
        cv2.waitKey(0)
        cv2.destroyAllWindows()
    else:
        cv2.imwrite(output_path, vis_image)
        print(f"Visualization saved to {output_path}")
    
    return vis_image

def process_directory(input_dir, model, device, output_dir, threshold=0.5, show=False, detailed=False):
    """Process all images in a directory"""
    if not os.path.isdir(input_dir):
        raise ValueError(f"Input directory does not exist: {input_dir}")
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Find all images
    image_extensions = ['.jpg', '.jpeg', '.png', '.tif', '.tiff']
    image_paths = []
    
    for ext in image_extensions:
        image_paths.extend(list(Path(input_dir).glob(f"*{ext}")))
        image_paths.extend(list(Path(input_dir).glob(f"*{ext.upper()}")))
    
    if not image_paths:
        print(f"No images found in {input_dir}")
        return None
    
    print(f"Found {len(image_paths)} images")
    
    # Process each image
    results = []
    
    for image_path in image_paths:
        try:
            print(f"Processing {image_path}")
            
            # Process image
            image, input_tensor, features = process_image(str(image_path))
            
            # Make prediction
            result = make_prediction(model, input_tensor, features, device, threshold)
            
            # Add image path to result
            result["image_path"] = str(image_path)
            
            # Visualize prediction
            output_name = f"{image_path.stem}_prediction{image_path.suffix}"
            output_path = os.path.join(output_dir, output_name)
            visualize_prediction(image, result, output_path, show, detailed)
            
            # Add visualization path to result
            result["visualization_path"] = output_path
            
            # Add to results
            results.append(result)
            
        except Exception as e:
            print(f"Error processing image {image_path}: {e}")
    
    # Save results to JSON
    results_path = os.path.join(output_dir, "prediction_results.json")
    with open(results_path, "w") as f:
        json.dump(results, f, indent=2)
    
    print(f"Results saved to {results_path}")
    
    # Create summary report
    create_summary_report(results, output_dir)
    
    return results

def create_summary_report(results, output_dir):
    """Create a summary report of all predictions"""
    if not results:
        return
    
    # Count predictions by class and severity
    class_counts = {"Normal": 0, "Abnormal": 0}
    severity_counts = {"LOW": 0, "MEDIUM": 0, "HIGH": 0}
    
    for result in results:
        class_counts[result["predicted_class"]] += 1
        if result["predicted_class"] == "Abnormal":
            severity_counts[result["severity"]] += 1
    
    # Plot class distribution
    plt.figure(figsize=(15, 10))
    
    # Plot prediction class distribution
    plt.subplot(2, 2, 1)
    plt.bar(class_counts.keys(), class_counts.values(), color=["green", "red"])
    plt.title("Prediction Class Distribution")
    plt.ylabel("Count")
    
    # Plot severity distribution
    plt.subplot(2, 2, 2)
    plt.bar(severity_counts.keys(), severity_counts.values(), 
           color=["green", "orange", "red"])
    plt.title("Severity Distribution (Abnormal Cases)")
    plt.ylabel("Count")
    
    # Plot feature distributions
    if results:
        # Extract features
        temp_ranges = [r["features"]["temp_range"] for r in results]
        hotspot_counts = [r["features"]["hotspot_count"] for r in results]
        
        # Plot temperature range distribution
        plt.subplot(2, 2, 3)
        plt.hist(temp_ranges, bins=10, color="skyblue")
        plt.title("Temperature Range Distribution")
        plt.xlabel("Temperature Range (°C)")
        plt.ylabel("Count")
        
        # Plot hotspot count distribution
        plt.subplot(2, 2, 4)
        plt.hist(hotspot_counts, bins=max(10, max(hotspot_counts)), color="orange")
        plt.title("Hotspot Count Distribution")
        plt.xlabel("Number of Hotspots")
        plt.ylabel("Count")
    
    # Save figure
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "summary_report.png"))
    plt.close()
    
    # Create detailed report
    report = {
        "total_images": len(results),
        "class_counts": class_counts,
        "severity_counts": severity_counts,
        "abnormal_percentage": class_counts["Abnormal"] / len(results) * 100 if results else 0,
        "high_severity_percentage": severity_counts["HIGH"] / class_counts["Abnormal"] * 100 if class_counts["Abnormal"] > 0 else 0,
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    
    # Add feature statistics
    if results:
        report["feature_statistics"] = {
            "temp_range": {
                "mean": np.mean(temp_ranges),
                "median": np.median(temp_ranges),
                "min": np.min(temp_ranges),
                "max": np.max(temp_ranges)
            },
            "hotspot_count": {
                "mean": np.mean(hotspot_counts),
                "median": np.median(hotspot_counts),
                "min": np.min(hotspot_counts),
                "max": np.max(hotspot_counts)
            }
        }
    
    # Save report to JSON
    with open(os.path.join(output_dir, "summary_report.json"), "w") as f:
        json.dump(report, f, indent=2)
    
    print(f"Summary report saved to {output_dir}")

def main():
    """Main function"""
    # Parse arguments
    args = parse_args()
    
    # Set device
    if args.device:
        device = torch.device(args.device)
    else:
        device = torch.device("cuda" if torch.cuda.is_available() else 
                             "mps" if torch.backends.mps.is_available() else 
                             "cpu")
    print(f"Using device: {device}")
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load model
    model = load_model(args.model_path, device)
    
    # Check if input is a directory or a single image
    if os.path.isdir(args.input):
        # Process directory
        results = process_directory(
            args.input, model, device, args.output_dir, 
            args.threshold, args.show, args.detailed
        )
    else:
        # Process single image
        try:
            # Process image
            image, input_tensor, features = process_image(args.input)
            
            # Make prediction
            result = make_prediction(model, input_tensor, features, device, args.threshold)
            
            # Add image path to result
            result["image_path"] = args.input
            
            # Visualize prediction
            input_filename = os.path.basename(args.input)
            output_name = f"{os.path.splitext(input_filename)[0]}_prediction{os.path.splitext(input_filename)[1]}"
            output_path = os.path.join(args.output_dir, output_name)
            visualize_prediction(image, result, output_path, args.show, args.detailed)
            
            # Add visualization path to result
            result["visualization_path"] = output_path
            
            # Save result to JSON
            result_path = os.path.join(args.output_dir, "prediction_result.json")
            with open(result_path, "w") as f:
                json.dump(result, f, indent=2)
            
            print(f"Result saved to {result_path}")
            
            # Print prediction
            print(f"Prediction: {result['predicted_class']}")
            print(f"Abnormal Probability: {result['abnormal_probability']:.4f}")
            print(f"Severity: {result['severity']}")
            print("\nDental Implications:")
            for implication in result["dental_implications"]:
                print(f"- {implication}")
            
        except Exception as e:
            print(f"Error processing image {args.input}: {e}")
            return
    
    print("Thermal analysis prediction complete!")

if __name__ == "__main__":
    main() 