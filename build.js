// Simple build script to handle build process issues
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting enhanced build process...');

// Create temporary backup of problematic files
const backupFiles = [
  'src/App.jsx',
  'src/main.jsx',
  'src/utils/isomorphicImports.js',
  'src/lib/utils/validation.js'
];

console.log('📦 Creating backups of sensitive files...');
const backups = {};
backupFiles.forEach(file => {
  try {
    if (fs.existsSync(file)) {
      const content = fs.readFileSync(file, 'utf8');
      backups[file] = content;
      console.log(`✅ Backed up ${file}`);
    }
  } catch (err) {
    console.error(`❌ Error backing up ${file}:`, err);
  }
});

try {
  // Modify App.jsx to remove client-side only code for build
  console.log('🔧 Patching App.jsx for build compatibility...');
  const appFile = 'src/App.jsx';
  if (fs.existsSync(appFile)) {
    let appContent = fs.readFileSync(appFile, 'utf8');

    // Replace the client-side code block with a comment
    appContent = appContent.replace(
      /\/\/ Try to generate missing icons[\s\S]*?initViewportFix\(\);[\s\S]*?\}/,
      '// Browser initialization moved to main.jsx for build compatibility'
    );

    fs.writeFileSync(appFile, appContent);
    console.log('✅ App.jsx patched');
  }

  // Fix validation.js file
  console.log('🔧 Patching validation.js for build compatibility...');
  const validationFile = 'src/lib/utils/validation.js';
  if (fs.existsSync(validationFile)) {
    let validationContent = fs.readFileSync(validationFile, 'utf8');

    // Fix the export syntax - remove the semicolon at the end
    validationContent = validationContent.replace(/};$/, '}');

    fs.writeFileSync(validationFile, validationContent);
    console.log('✅ validation.js patched');
  }

  // Run the build command
  console.log('🏗️ Running build command...');
  execSync('npm run generate-icons && npm run copy-seo-content && VITE_SKIP_TF=true vite build', {
    stdio: 'inherit'
  });

  console.log('✅ Build completed successfully!');
} catch (error) {
  console.error('❌ Build failed:', error);
  process.exit(1);
} finally {
  // Restore original files
  console.log('🔄 Restoring original files...');
  Object.entries(backups).forEach(([file, content]) => {
    try {
      fs.writeFileSync(file, content);
      console.log(`✅ Restored ${file}`);
    } catch (err) {
      console.error(`❌ Error restoring ${file}:`, err);
    }
  });
}