# Article Fetching System Setup Guide

This guide explains how to set up and run the automated article fetching system for Smilo Dental. The system regularly pulls articles from trusted dental sources and makes them available on your website.

## Prerequisites

- Node.js (v16+)
- NPM or Yarn
- Supabase account with database access
- Environment variables properly configured

## Quick Start

1. **Set up environment variables**

   Ensure your `.env` file has the following variables:

   ```
   VITE_SUPABASE_URL=https://your-project.supabase.co
   VITE_SUPABASE_ANON_KEY=your-anon-key
   ```

2. **Set up database tables**

   Run the following command to create necessary database tables:

   ```
   npm run setup-article-system
   ```

   This creates:
   - `dental_articles` - Stores the actual articles
   - `article_metrics` - Tracks performance metrics
   - `system_health` - Records system health status

3. **Test article fetching**

   ```
   npm run test-article-fetch
   ```

   This will test if the article fetching works with one of the sources.

4. **Run an initial article fetch**

   ```
   npm run fetch-articles-node
   ```

   This will fetch articles from all configured sources and store them in the database.

5. **Set up automatic hourly fetching**

   ```
   npm run setup-cron
   ```

   This sets up a cron job to run the fetching script every hour.

## Monitoring

You can monitor the article system through the admin dashboard (`/admin/articles`). The dashboard shows:

- Total articles collected
- Source distribution
- Recent fetch performance
- System health status

## Customizing Sources

To add or modify article sources, edit the `TRUSTED_SOURCES` object in:

```
src/lib/services/articleFetchService.js
```

Each source needs:
- `name`: Display name of the source
- `feedUrl`: URL of the RSS feed
- `type`: Source type (usually 'rss')

The system currently includes these trusted dental sources:
- American Dental Association
- DentistryIQ
- Dental Economics
- Colgate Oral Health Network
- Dental Tribune
- Journal of the American Dental Association
- Oral Health Group
- Dental Products Report

## Troubleshooting

If articles aren't being fetched properly:

1. Check the logs in `logs/article-fetch.log`
2. Verify that all RSS feed URLs are still valid
3. Check Supabase connection and permissions
4. Make sure the tables exist and have the right structure

## Manual Operations

- **Fetch articles manually**: `npm run fetch-articles-node`
- **Test article sources**: `npm run test-article-fetch`
- **View the worker**: `npm run articles`
- **Check database tables**: Use Supabase dashboard to verify table structure

## Architecture

The article system consists of:

1. **Fetching Service** - Connects to RSS feeds and retrieves articles
2. **Database Tables** - Store articles and metrics
3. **Cron Job** - Runs the fetching on a schedule
4. **Admin Interface** - Monitors system performance

## Technical Details

- The system uses `fast-xml-parser` to parse RSS feeds
- Articles are deduplicated based on title and source
- Tags are extracted from article categories
- The system performs content cleaning and validation before storage
- Articles older than the retention limit will be automatically removed

## Need Help?

If you encounter issues or need more information, please reach out to support. 