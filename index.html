<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover" />
    <meta name="theme-color" content="#0f172a" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="format-detection" content="telephone=no">
    <meta name="color-scheme" content="dark light">
    <meta name="application-name" content="Smilo Dental">
    <meta name="msapplication-TileColor" content="#0f172a">
    <meta name="msapplication-config" content="/browserconfig.xml">
    <!-- iOS specific tags -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="Smilo Dental">
    <!-- Mobile optimization -->
    <meta name="HandheldFriendly" content="true">
    <meta name="MobileOptimized" content="width">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="msapplication-tap-highlight" content="no">
    <title>Smilo Dental Assistant - Your Personalized Guide to Oral Health</title>
    <meta name="description" content="Smilo Dental Assistant provides personalized dental guidance, expert resources, and AI-powered tools to help you maintain optimal oral health." />
    <meta name="keywords" content="dental assistant, oral health, dental resources, dental AI, dental education, dental care" />
    <meta name="author" content="Smilo Dental" />
    <meta name="robots" content="index, follow" />

    <!-- Security Meta Tags -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    <meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin">
    <meta http-equiv="Permissions-Policy" content="camera=(), microphone=(), geolocation=(self), interest-cohort=()">
    <meta http-equiv="Strict-Transport-Security" content="max-age=31536000; includeSubDomains; preload">

    <!-- Safari pre-check script - must be before any other scripts -->
    <script>
      // Check for Safari browser
      var isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
      
      if (isSafari) {
        // Apply Safari-specific styles immediately
        document.documentElement.classList.add('safari');
        
        // Improve Safari rendering performance
        document.documentElement.style.transform = 'translateZ(0)';
        document.documentElement.style.backfaceVisibility = 'hidden';
        document.documentElement.style.webkitFontSmoothing = 'antialiased';
        
        // Fix viewport height issues in iOS Safari
        function setViewportHeight() {
          document.documentElement.style.setProperty('--vh', window.innerHeight * 0.01 + 'px');
        }
        setViewportHeight();
        window.addEventListener('resize', setViewportHeight);
        window.addEventListener('orientationchange', function() {
          setTimeout(setViewportHeight, 200);
        });
        
        // Create a temporary loading indicator
        document.addEventListener('DOMContentLoaded', function() {
          var root = document.getElementById('root');
          if (root && !root.hasChildNodes()) {
            root.innerHTML = '<div style="text-align: center; padding: 2rem; color: white;">' +
              '<h1 style="font-size: 1.5rem;">Loading Smilo Dental</h1>' +
              '<p style="font-size: 1rem;">Please wait while the application initializes...</p>' +
              '<div class="loading-spinner" style="margin: 1rem auto;"></div>' +
              '</div>';
          }
        });
        
        // Add keyframe animation for loading spinner
        var style = document.createElement('style');
        style.textContent = '@keyframes safari-spin { to { transform: rotate(360deg); } }';
        document.head.appendChild(style);
        
        // Polyfill for IntersectionObserver
        if (!('IntersectionObserver' in window)) {
          var script = document.createElement('script');
          script.src = 'https://polyfill.io/v3/polyfill.min.js?features=IntersectionObserver';
          document.head.appendChild(script);
        }
        
        // Polyfill for broken fetch in some Safari versions
        if (window.fetch) {
          var originalFetch = window.fetch;
          window.fetch = function() {
            return originalFetch.apply(this, arguments)
              .catch(function(error) {
                // Retry once with cache bust for Safari fetch issues
                if (arguments[0] && typeof arguments[0] === 'string' && !arguments[0].includes('?')) {
                  arguments[0] = arguments[0] + '?cacheBust=' + Date.now();
                  return originalFetch.apply(this, arguments);
                }
                throw error;
              });
          };
        }
      }
    </script>

    <!-- Prevent loading of extension files that are causing ERR_FILE_NOT_FOUND errors -->
    <script>
      // Intercept network requests for problematic extension files
      const interceptFetch = window.fetch;
      window.fetch = function(resource, init) {
        if (resource && typeof resource === 'string' && 
            (resource.includes('heuristicsRedefinitions.js') || 
             resource.includes('extensionState.js') || 
             resource.includes('utils.js'))) {
          console.warn('Blocked request to extension file:', resource);
          return Promise.resolve(new Response('', {status: 200, headers: {'Content-Type': 'application/javascript'}}));
        }
        return interceptFetch.apply(this, arguments);
      };
    </script>

    <!-- Google tag (gtag.js) -->
    <script defer src="https://www.googletagmanager.com/gtag/js?id=G-JD4BFSFPZ8"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-JD4BFSFPZ8', {
        'send_page_view': false,
        'anonymize_ip': true,
        'transport_type': 'beacon'
      });

      window.addEventListener('load', function() {
        setTimeout(function() {
          gtag('event', 'page_view');
        }, 100);
      });
    </script>

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://smilo.dental/" />
    <meta property="og:title" content="Smilo Dental Assistant - Your Personalized Guide to Oral Health" />
    <meta property="og:description" content="Smilo Dental Assistant provides personalized dental guidance, expert resources, and AI-powered tools to help you maintain optimal oral health." />
    <meta property="og:image" content="https://smilo.dental/images/smilo-logo1.jpg" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://smilo.dental/" />
    <meta property="twitter:title" content="Smilo Dental Assistant - Your Personalized Guide to Oral Health" />
    <meta property="twitter:description" content="Smilo Dental Assistant provides personalized dental guidance, expert resources, and AI-powered tools to help you maintain optimal oral health." />
    <meta property="twitter:image" content="https://smilo.dental/images/smilo-logo1.jpg" />

    <link rel="canonical" href="https://smilo.dental/" />
    <!-- Favicons -->
    <link rel="icon" type="image/jpeg" href="/images/smilo-logo1.jpg" />
    <link rel="apple-touch-icon" href="/images/smilo-logo1.jpg">
    <link rel="icon" type="image/jpeg" sizes="32x32" href="/images/smilo-logo1.jpg">
    <link rel="icon" type="image/jpeg" sizes="16x16" href="/images/smilo-logo1.jpg">

    <!-- DNS Prefetch and Preconnect -->
    <link rel="dns-prefetch" href="https://fonts.googleapis.com">
    <link rel="dns-prefetch" href="https://fonts.gstatic.com">
    <link rel="dns-prefetch" href="https://www.googletagmanager.com">
    <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://www.googletagmanager.com" crossorigin>

    <!-- Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@400;500;600;700&display=swap">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet" media="print" onload="this.media='all'">
    <noscript><link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet"></noscript>
    <link rel="manifest" href="/manifest.json" />

    <!-- Base styles -->
    <style>
      html, body {
        margin: 0;
        padding: 0;
        font-family: 'Plus Jakarta Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background-color: #0f172a;
        color: #fff;
        min-height: 100vh;
        width: 100%;
        overflow-x: hidden;
        -webkit-text-size-adjust: 100%;
      }

      body {
        margin: 0;
        min-height: 100vh;
        background: linear-gradient(135deg, rgba(30, 41, 59, 1), rgba(30, 58, 138, 0.3), rgba(147, 51, 234, 0.2));
        background-attachment: fixed;
        color: white;
        font-family: 'Plus Jakarta Sans', system-ui, -apple-system, sans-serif;
      }

      #root {
        min-height: 100vh;
        display: flex;
        flex-direction: column;
      }

      .loading-spinner {
        display: inline-block;
        width: 2rem;
        height: 2rem;
        border-radius: 50%;
        border: 0.25rem solid rgba(255,255,255,0.3);
        border-top-color: #fff;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        to { transform: rotate(360deg); }
      }

      /* Fix 100vh issue for mobile browsers */
      @supports (-webkit-touch-callout: none) {
        html, body, #root {
          height: -webkit-fill-available;
        }
      }

      /* Media queries for responsive design */
      @media (max-width: 768px) {
        body {
          background: linear-gradient(135deg, rgba(30, 41, 59, 1), rgba(30, 58, 138, 0.3), rgba(147, 51, 234, 0.2));
          background-attachment: scroll;
        }

        #root {
          padding: 0;
        }

        .loading-spinner {
          width: 1.5rem;
          height: 1.5rem;
        }
      }

      /* Fix for notched iPhones */
      @supports (padding: max(0px)) {
        body {
          padding-top: env(safe-area-inset-top, 0);
          padding-bottom: env(safe-area-inset-bottom, 0);
          padding-left: env(safe-area-inset-left, 0);
          padding-right: env(safe-area-inset-right, 0);
        }
      }
      
      /* Safari-specific fixes */
      .safari {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        -webkit-overflow-scrolling: touch;
      }
      
      .safari body {
        background-attachment: scroll !important;
      }
      
      .safari .animate-spin,
      .safari .loading-spinner {
        animation: none !important;
        transform: translateZ(0);
        will-change: transform;
      }
      
      .safari #root {
        transform: translateZ(0);
        will-change: transform;
      }
      
      /* Simpler gradient for Safari */
      .safari body {
        background: #0f172a !important;
      }
    </style>

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Organization",
      "name": "Smilo Dental",
      "url": "https://smilo.dental",
      "logo": "https://smilo.dental/images/smilo-logo1.jpg",
      "description": "Smilo Dental provides personalized dental guidance, expert resources, and AI-powered tools to help maintain optimal oral health.",
      "sameAs": [
        "https://twitter.com/smilo.dental",
        "https://www.facebook.com/smilo.dental",
        "https://www.linkedin.com/company/smilodental"
      ]
    }
    </script>

    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "name": "Smilo Dental Assistant",
      "url": "https://smilo.dental",
      "potentialAction": {
        "@type": "SearchAction",
        "target": "https://smilo.dental/resources/seo?search={search_term_string}",
        "query-input": "required name=search_term_string"
      }
    }
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
    
    <!-- Fallback script for Safari if module loading fails -->
    <script nomodule>
      console.error('ES modules not supported in this browser');
      document.getElementById('root').innerHTML = `
        <div style="text-align: center; padding: 2rem; margin: 2rem auto; max-width: 600px; color: white;">
          <h1 style="font-size: 1.5rem;">Browser compatibility issue detected</h1>
          <p style="font-size: 1rem;">We've detected that your browser may not fully support the modern features needed to run Smilo Dental properly.</p>
          <p style="font-size: 1rem;">Please try one of the following:</p>
          <ul style="text-align: left; display: inline-block; margin: 1rem auto; font-size: 0.9rem;">
            <li>Update your browser to the latest version</li>
            <li>Try using Chrome, Firefox, or Edge</li>
            <li>If you're using Safari, enable "Modern JavaScript" in Advanced settings</li>
          </ul>
          <button onclick="window.location.reload()" style="padding: 8px 16px; margin-top: 1rem; background: #3b82f6; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 1rem;">
            Retry Loading
          </button>
        </div>
      `;
    </script>
    
    <!-- Safari performance script -->
    <script>
      // Only run in Safari
      if (navigator.userAgent.indexOf('Safari') !== -1 && navigator.userAgent.indexOf('Chrome') === -1) {
        // Detect slow loading and provide fallback
        var safariLoadTimeout = setTimeout(function() {
          var root = document.getElementById('root');
          if (root && (!root.children.length || !root.children[0].className)) {
            console.log('Safari load timeout - forcing simplified mode');
            document.documentElement.classList.add('safari-simplified');
            document.documentElement.classList.add('reduced-motion');
            // Force a reload with simplified flag
            window.location.href = window.location.href.split('?')[0] + '?simplified=true';
          }
        }, 5000);

        // If the app loads properly, clear the timeout
        window.addEventListener('load', function() {
          clearTimeout(safariLoadTimeout);
        });
      }
    </script>
  </body>
</html>