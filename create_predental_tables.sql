-- Create predental_progress table
CREATE TABLE IF NOT EXISTS predental_progress (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  overall_gpa NUMERIC(3,2) DEFAULT 0.0,
  science_gpa NUMERIC(3,2) DEFAULT 0.0,
  total_credits INTEGER DEFAULT 0,
  science_credits INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  CONSTRAINT unique_user_progress UNIQUE (user_id)
);

-- Create index on user_id for faster lookups
CREATE INDEX IF NOT EXISTS predental_progress_user_id_idx ON predental_progress (user_id);

-- Enable Row Level Security
ALTER TABLE predental_progress ENABLE ROW LEVEL SECURITY;

-- Create policies for predental_progress
CREATE POLICY "Users can view their own progress"
  ON predental_progress FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own progress"
  ON predental_progress FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own progress"
  ON predental_progress FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Create predental_courses table
CREATE TABLE IF NOT EXISTS predental_courses (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  course_name TEXT NOT NULL,
  type TEXT NOT NULL,  -- BIOLOGY, CHEMISTRY, PHYSICS, OTHER
  grade TEXT NOT NULL,
  credits INTEGER NOT NULL,
  semester TEXT,
  year INTEGER,
  school TEXT,
  notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create index on user_id for faster lookups
CREATE INDEX IF NOT EXISTS predental_courses_user_id_idx ON predental_courses (user_id);

-- Enable Row Level Security
ALTER TABLE predental_courses ENABLE ROW LEVEL SECURITY;

-- Create policies for predental_courses
CREATE POLICY "Users can view their own courses"
  ON predental_courses FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own courses"
  ON predental_courses FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own courses"
  ON predental_courses FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own courses"
  ON predental_courses FOR DELETE
  USING (auth.uid() = user_id); 