# Google Maps Review Scraper for Dental Schools

This module provides functionality to fetch, display, and analyze reviews from dental schools using the Google Places API. It integrates directly with the Smilo Dental application to help users find affordable dental care options with reliable ratings and reviews.

## Features

- 🔍 **Search for dental schools** by name or location
- 📍 **Find nearby dental schools** based on user location with customizable radius
- ⭐ **View detailed ratings and reviews** from real patients
- 📊 **Analysis of rating distributions** to help users make informed decisions
- 🖼️ **View photos of dental facilities** to get a visual impression
- 🕒 **Opening hours and contact information** for each dental school

## Setup Instructions

### 1. API Key Setup

1. Obtain a Google Maps API key from the [Google Cloud Console](https://console.cloud.google.com/)
2. Enable the following APIs:
   - Places API
   - Maps JavaScript API
   - Geocoding API

3. Add your API key to the `.env` file:
   ```
   VITE_GOOGLE_MAPS_API_KEY=your_api_key_here
   ```

### 2. Install Dependencies

Make sure you have all required dependencies by running:

```bash
npm install
```

### 3. Start the Server

The application includes a proxy server to handle API requests securely:

```bash
# Development mode with both frontend and backend
npm run dev:full

# Just the backend server
npm run dev:server

# Production mode
npm run start
```

## How It Works

### Architecture

1. **Frontend Components**:
   - `DentalSchoolMap.jsx`: Main component that displays the map and reviews
   - `AffordableOptions.jsx`: Page that integrates the map component

2. **Backend Services**:
   - `googleMapsReviewService.js`: Service to fetch and process review data
   - `placesProxy.js`: Express middleware to proxy API requests

3. **Data Flow**:
   - User searches for dental schools or shares their location
   - Frontend requests data through the proxy server
   - Server makes secure API calls to Google Places API
   - Data is processed, formatted, and returned to the frontend
   - Frontend displays the information in a user-friendly interface

### Key Components

#### Google Maps API Integration

The system uses several Google Maps API endpoints:
- Place Search: Find dental schools by name or location
- Place Details: Get detailed information about a specific dental school
- Place Photos: Retrieve photos of dental facilities
- Nearby Search: Find dental schools in a specific radius

#### Proxy Server

The proxy server handles all API requests to:
- Hide API keys from client-side code
- Avoid CORS issues
- Add caching and rate limiting
- Format and sanitize responses

#### Data Processing

The system processes raw API data to extract useful information:
- Rating distributions
- Review highlights
- Distance calculations
- Operating hours formatting

## Extending the System

### Adding New Features

1. **Additional Filters**:
   - Add filters for rating thresholds, services offered, or payment options by extending the `getNearbyDentalSchools` function

2. **Enhanced Reviews Analysis**:
   - Implement sentiment analysis on reviews by adding a processing layer to the `processReviews` function

3. **User-Generated Content**:
   - Allow users to submit their own reviews and photos by creating new API endpoints

### Troubleshooting

If you encounter issues:

1. Check that your API key is correctly set and has the necessary permissions
2. Verify that the proxy server is running and accessible
3. Check the browser console and server logs for error messages
4. Ensure you're not exceeding Google Maps API usage limits

## License

This module is part of the Smilo Dental Assistant application.

---

For more information about the full application, see the main README file. 