// Polyfills for older browsers

// classList polyfill
if (!("classList" in document.documentElement)) {
  (function () {
    var prototype = Array.prototype,
        push = prototype.push,
        splice = prototype.splice,
        join = prototype.join;

    function DOMTokenList(el) {
      this.el = el;
      var classes = el.className.replace(/^\s+|\s+$/g,'').split(/\s+/);
      for (var i = 0; i < classes.length; i++) {
        push.call(this, classes[i]);
      }
    }

    DOMTokenList.prototype = {
      add: function(token) {
        if(this.contains(token)) return;
        push.call(this, token);
        this.el.className = this.toString();
      },
      contains: function(token) {
        return this.el.className.indexOf(token) != -1;
      },
      item: function(index) {
        return this[index] || null;
      },
      remove: function(token) {
        if (!this.contains(token)) return;
        for (var i = 0; i < this.length; i++) {
          if (this[i] == token) break;
        }
        splice.call(this, i, 1);
        this.el.className = this.toString();
      },
      toString: function() {
        return join.call(this, ' ');
      },
      toggle: function(token) {
        if (!this.contains(token)) {
          this.add(token);
          return true;
        } else {
          this.remove(token);
          return false;
        }
      }
    };

    window.DOMTokenList = DOMTokenList;

    function defineElementGetter(obj, prop, getter) {
      if (Object.defineProperty) {
        Object.defineProperty(obj, prop, {
          get: getter
        });
      } else {
        obj.__defineGetter__(prop, getter);
      }
    }

    defineElementGetter(Element.prototype, 'classList', function() {
      return new DOMTokenList(this);
    });
  })();
}

// IntersectionObserver polyfill
if (!('IntersectionObserver' in window)) {
  // Simple stub implementation - not full functionality
  window.IntersectionObserver = function(callback, options) {
    this.elements = [];
    this.callback = callback;
    
    this.observe = function(element) {
      if (this.elements.indexOf(element) === -1) {
        this.elements.push(element);
        
        // Simple check on next frame
        setTimeout(() => {
          const rect = element.getBoundingClientRect();
          const isIntersecting = 
            rect.top < window.innerHeight &&
            rect.bottom > 0 &&
            rect.left < window.innerWidth &&
            rect.right > 0;
            
          callback([{
            isIntersecting: isIntersecting,
            intersectionRatio: isIntersecting ? 0.5 : 0,
            target: element
          }]);
        }, 100);
      }
    };
    
    this.unobserve = function(element) {
      const index = this.elements.indexOf(element);
      if (index !== -1) {
        this.elements.splice(index, 1);
      }
    };
    
    this.disconnect = function() {
      this.elements = [];
    };
  };
}

// ResizeObserver polyfill (simple version)
if (!('ResizeObserver' in window)) {
  window.ResizeObserver = function(callback) {
    this.elements = [];
    this.callback = callback;
    
    this.observe = function(element) {
      if (this.elements.indexOf(element) === -1) {
        this.elements.push(element);
        
        // Track size
        element._lastWidth = element.offsetWidth;
        element._lastHeight = element.offsetHeight;
        
        // Check periodically
        if (!window._resizeObserverInterval) {
          window._resizeObserverInterval = setInterval(() => {
            document.querySelectorAll('*').forEach(el => {
              if (el._lastWidth !== undefined && 
                  (el._lastWidth !== el.offsetWidth || el._lastHeight !== el.offsetHeight)) {
                const entries = [{
                  target: el,
                  contentRect: {
                    width: el.offsetWidth,
                    height: el.offsetHeight
                  }
                }];
                
                el._lastWidth = el.offsetWidth;
                el._lastHeight = el.offsetHeight;
                
                // Find observers that are watching this element
                if (window._resizeObservers) {
                  window._resizeObservers.forEach(observer => {
                    if (observer.elements.indexOf(el) !== -1) {
                      observer.callback(entries);
                    }
                  });
                }
              }
            });
          }, 250); // Check every 250ms
        }
        
        // Track observers
        if (!window._resizeObservers) {
          window._resizeObservers = [];
        }
        if (window._resizeObservers.indexOf(this) === -1) {
          window._resizeObservers.push(this);
        }
      }
    };
    
    this.unobserve = function(element) {
      const index = this.elements.indexOf(element);
      if (index !== -1) {
        this.elements.splice(index, 1);
        delete element._lastWidth;
        delete element._lastHeight;
      }
    };
    
    this.disconnect = function() {
      this.elements.forEach(element => {
        delete element._lastWidth;
        delete element._lastHeight;
      });
      this.elements = [];
    };
  };
}
