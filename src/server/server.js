import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import compression from 'compression';
import helmet from 'helmet';
import placesProxy from './placesProxy.js';
import openaiProxy from './openaiProxy.js';
import locationProxy from './locationProxy.js';
import path from 'path';
import { fileURLToPath } from 'url';
import { applySecurityHeaders } from '../middleware/securityHeaders.js';
import { sanitizeInput, sanitizeSearchQuery } from '../lib/utils/security.js';
import { 
  protectAgainstMiddlewareBypass, 
  sensitiveEndpointRateLimiting,
  sanitizeInputParameters,
  additionalAuthorizationValidation 
} from '../middleware/middlewareSecurity.js';

// Load environment variables
dotenv.config();

// Create Express app
const app = express();
const PORT = process.env.PORT || 3000;

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// CRITICAL FIX: Add enhanced middleware to catch path-to-regexp errors
// This must be added before any route definitions
app.use((req, res, next) => {
  try {
    // Only validate internal routes, skip external URLs
    if (!req.url.startsWith('http')) {
      // Clean and normalize the path
      const cleanPath = req.path.replace(/\/+/g, '/').replace(/\/$/, '');
      const pathSegments = cleanPath.split('/').filter(Boolean);
      
      // Check for malformed parameters with improved validation
      const hasInvalidParams = pathSegments.some(segment => {
        // Allow valid parameter patterns only
        if (segment.startsWith(':')) {
          return !segment.match(/^:[a-zA-Z][a-zA-Z0-9_]*$/);
        }
        // Check for partial colons that might confuse path-to-regexp
        return segment.includes(':') && !segment.startsWith(':');
      });
      
      if (hasInvalidParams) {
        console.error('Invalid route parameter detected in path:', cleanPath);
        return res.status(400).json({
          error: 'Invalid route parameter',
          message: 'The request contains an invalid route parameter',
          path: cleanPath
        });
      }
    }

    next();
  } catch (error) {
    // Enhanced error handling
    if (error.message && (
      error.message.includes('Missing parameter name') ||
      error.message.includes('pathToRegexpError') ||
      error.message.includes('path-to-regexp') ||
      error.message.includes('Cannot read properties') // Common error when URL is malformed
    )) {
      console.error('Path-to-regexp error caught:', {
        message: error.message,
        url: req.url,
        path: req.path,
        method: req.method
      });
      
      return res.status(500).json({
        error: 'Server configuration error',
        message: 'The server encountered an error processing the request path',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
    next(error);
  }
});

// Apply enhanced security headers and protections
applySecurityHeaders(app);

// Middleware
app.use(cors({
  origin: process.env.NODE_ENV === 'production' 
    ? [process.env.ALLOWED_ORIGIN || 'https://smilo.dental', 'https://*.smilo.dental'] 
    : ['http://localhost:5173', 'http://localhost:5174', 'http://localhost:4173'],
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  credentials: true,
  maxAge: 86400
}));

// Apply protection against Next.js middleware bypass vulnerability (CVE-2025-29927)
// Only for specific routes that use Next.js, to avoid breaking mobile compatibility
app.use('/api/admin', protectAgainstMiddlewareBypass);
app.use('/api/user', protectAgainstMiddlewareBypass);
app.use('/api/settings', protectAgainstMiddlewareBypass);

app.use(express.json({ limit: '16mb' })); // Reduced limit for security
app.use(express.urlencoded({ extended: true, limit: '16mb' }));
app.use(compression()); // Compress all responses

// Security headers middleware - Modified to ensure mobile compatibility
app.use((req, res, next) => {
  // Set security headers with mobile considerations
  res.setHeader('X-Frame-Options', 'SAMEORIGIN');
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  // Reduced HSTS max-age for mobile testing - can increase after confirming fix
  res.setHeader('Strict-Transport-Security', 'max-age=3600; includeSubDomains');
  next();
});

// IMPORTANT: For Cloudflare Flexible SSL, we DO NOT force HTTPS redirects
// This middleware logs the request's protocol chain so we can debug
app.use((req, res, next) => {
  const proto = req.headers['x-forwarded-proto'];
  const cf = req.headers['cf-visitor'];

  console.log(`Request headers: x-forwarded-proto=${proto || 'none'}, cf-visitor=${cf || 'none'}, userAgent=${req.headers['user-agent']}`);
  
  next();
});

// Security middleware - Relaxed helmet configuration for better mobile compatibility
app.use(helmet({
  contentSecurityPolicy: false, // We're handling CSP separately for mobile vs. desktop
  crossOriginEmbedderPolicy: false, // Needed for cross-origin resources
  crossOriginOpenerPolicy: { policy: "same-origin-allow-popups" }, // For OAuth flows
  crossOriginResourcePolicy: { policy: "cross-origin" }, // For cross-origin resources
  referrerPolicy: { policy: "strict-origin-when-cross-origin" }
}));

// Add request logging with improved security
app.use((req, res, next) => {
  const start = Date.now();
  
  // Sanitize query parameters for logging
  const sanitizedUrl = sanitizeInput(req.originalUrl.split('?')[0]);
  const sanitizedMethod = sanitizeInput(req.method);
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    if (duration > 1000) { // Log slow requests (over 1 second)
      console.warn(`Slow request: ${sanitizedMethod} ${sanitizedUrl} - ${duration}ms`);
    }
  });
  next();
});

// API input sanitization middleware
app.use('/api', (req, res, next) => {
  // Sanitize query parameters
  if (req.query) {
    Object.keys(req.query).forEach(key => {
      if (typeof req.query[key] === 'string') {
        req.query[key] = sanitizeSearchQuery(req.query[key]);
      }
    });
  }
  
  next();
});

// Only apply additional security to API routes, not to static content
// This ensures the website loads properly on mobile
app.use('/api', sensitiveEndpointRateLimiting);
app.use('/api', sanitizeInputParameters);

// Add secondary authorization validation as defense-in-depth
app.use('/api/admin', additionalAuthorizationValidation);
app.use('/api/user', additionalAuthorizationValidation);

// Serve static files from the dist directory with specific mobile handling
app.use(express.static(path.join(__dirname, '../../dist'), {
  maxAge: '1d',
  etag: true,
  lastModified: true
}));

// Handle SPA routes - redirect to index.html for client-side routing
app.use('/*', (req, res, next) => {
  // Skip API routes
  if (req.path.startsWith('/api/') || req.path === '/health' || req.path === '/mobile-check') {
    return next();
  }
  
  // For all other routes, serve index.html
  res.sendFile(path.join(__dirname, '../../dist/index.html'));
});

// API Routes
try {
  app.use('/api/places', placesProxy);
  app.use('/api/openai', openaiProxy);
  app.use('/api/location', locationProxy);
} catch (error) {
  console.error('Error registering API routes:', error);
  console.error('This might be caused by invalid route patterns');
}

// Add health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ 
    status: 'OK', 
    message: 'Server is running',
    device: {
      userAgent: req.headers['user-agent']
    }
  });
});

// Handle mobile-specific routes
app.get('/mobile-check', (req, res) => {
  res.status(200).send(`
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Mobile Compatibility Check</title>
      </head>
      <body style="background-color: #111827; color: white; font-family: Arial; text-align: center; padding: 20px;">
        <h1>Mobile Compatibility Check</h1>
        <p>If you're seeing this page, the server is running!</p>
        <a href="/" style="color: #4f88ef; text-decoration: underline;">Go to Homepage</a>
      </body>
    </html>
  `);
});

// CRITICAL FIX: Add catch-all error handler to prevent path-to-regexp crashes
app.use((err, req, res, next) => {
  // Enhanced error logging
  console.error('Global error handler caught:', {
    error: err.message,
    stack: process.env.NODE_ENV === 'development' ? err.stack : undefined,
    url: req.url,
    path: req.path,
    method: req.method,
    headers: {
      'user-agent': req.headers['user-agent'],
      'x-forwarded-for': req.headers['x-forwarded-for']
    }
  });
  
  // Check for path-to-regexp errors
  if (err.message && (
    err.message.includes('Missing parameter name') || 
    err.message.includes('pathToRegexpError') ||
    err.message.includes('path-to-regexp') ||
    err.message.includes('Cannot read properties')
  )) {
    console.error('Path-to-regexp error caught in global handler:', {
      message: err.message,
      url: req.url,
      path: req.path
    });
    return res.status(500).json({ 
      error: 'Server configuration error', 
      message: 'The server encountered an error processing the request path',
      details: process.env.NODE_ENV === 'development' ? err.message : undefined
    });
  }
  
  // Handle other errors
  res.status(500).json({
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? 
      err.message : 
      'An unexpected error occurred',
    requestId: req.id // This helps correlate errors in logs
  });
});

export default app;