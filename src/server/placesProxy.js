import express from 'express';
import axios from 'axios';
import cors from 'cors';
import dotenv from 'dotenv';
import { GOOGLE_MAPS_API_KEY } from '../lib/config.js';

dotenv.config();

const router = express.Router();

// Middleware
router.use(cors());
router.use(express.json());

// Log whether the key is available, using the centralized configuration
if (!GOOGLE_MAPS_API_KEY) {
  console.error('❌ Google Maps API key not found in centralized configuration!');
} else {
  console.log('✅ Google Maps API key loaded from centralized config!');
}

// Endpoint for place search
try {
  router.get('/search', async (req, res) => {
    try {
      const { query, location, radius, type } = req.query;
      
      // Validate required parameters
      if (!query && !location) {
        return res.status(400).json({ error: 'Either query or location is required' });
      }
      
      let url = 'https://maps.googleapis.com/maps/api/place/textsearch/json';
      const params = {
        key: GOOGLE_MAPS_API_KEY,
      };
      
      // Add parameters based on what's provided
      if (query) params.query = query;
      if (location) params.location = location;
      if (radius) params.radius = radius;
      if (type) params.type = type;
      
      const response = await axios.get(url, { params });
      
      res.json(response.data);
    } catch (error) {
      console.error('Error in place search:', error);
      res.status(500).json({ error: 'Failed to fetch places' });
    }
  });
} catch (routeError) {
  console.error('Error registering place search route:', routeError);
}

// Endpoint for place details
try {
  router.get('/details', async (req, res) => {
    try {
      const { place_id, fields } = req.query;
      
      // Validate required parameters
      if (!place_id) {
        return res.status(400).json({ error: 'Place ID is required' });
      }
      
      const url = 'https://maps.googleapis.com/maps/api/place/details/json';
      const params = {
        key: GOOGLE_MAPS_API_KEY,
        place_id,
        fields: fields || 'name,rating,review,formatted_address,formatted_phone_number,website'
      };
      
      const response = await axios.get(url, { params });
      
      res.json(response.data);
    } catch (error) {
      console.error('Error in place details:', error);
      res.status(500).json({ error: 'Failed to fetch place details' });
    }
  });
} catch (routeError) {
  console.error('Error registering place details route:', routeError);
}

// Endpoint for nearby places
try {
  router.get('/nearby', async (req, res) => {
    try {
      const { location, radius, type, keyword } = req.query;
      
      // Validate required parameters
      if (!location) {
        return res.status(400).json({ error: 'Location is required' });
      }
      
      const url = 'https://maps.googleapis.com/maps/api/place/nearbysearch/json';
      const params = {
        key: GOOGLE_MAPS_API_KEY,
        location,
        radius: radius || 5000
      };
      
      if (type) params.type = type;
      if (keyword) params.keyword = keyword;
      
      const response = await axios.get(url, { params });
      
      res.json(response.data);
    } catch (error) {
      console.error('Error in nearby search:', error);
      res.status(500).json({ error: 'Failed to fetch nearby places' });
    }
  });
} catch (routeError) {
  console.error('Error registering nearby places route:', routeError);
}

// Endpoint for place photos
try {
  router.get('/photo', async (req, res) => {
    try {
      const { photo_reference, maxwidth } = req.query;
      
      // Validate required parameters
      if (!photo_reference) {
        return res.status(400).json({ error: 'Photo reference is required' });
      }
      
      const url = 'https://maps.googleapis.com/maps/api/place/photo';
      const params = {
        key: GOOGLE_MAPS_API_KEY,
        photoreference: photo_reference,
        maxwidth: maxwidth || 400
      };
      
      // Since this returns an image, we need to proxy the image data
      const response = await axios.get(url, { 
        params,
        responseType: 'arraybuffer'
      });
      
      // Set appropriate headers
      res.set('Content-Type', response.headers['content-type']);
      res.send(response.data);
    } catch (error) {
      console.error('Error fetching photo:', error);
      res.status(500).json({ error: 'Failed to fetch photo' });
    }
  });
} catch (routeError) {
  console.error('Error registering photo route:', routeError);
}

export default router; 