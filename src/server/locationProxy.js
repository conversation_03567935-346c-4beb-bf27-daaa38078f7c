import express from 'express';
import axios from 'axios';
import cors from 'cors';
import dotenv from 'dotenv';

dotenv.config();

const router = express.Router();

// Middleware
router.use(cors());
router.use(express.json());

// OpenStreetMap Nominatim API base URL
const NOMINATIM_API = 'https://nominatim.openstreetmap.org';

// Endpoint for reverse geocoding (coordinates to address)
try {
  router.get('/reverse', async (req, res) => {
    try {
      const { lat, lon, zoom, addressdetails } = req.query;
      
      // Validate required parameters
      if (!lat || !lon) {
        return res.status(400).json({ error: 'Latitude and longitude are required' });
      }
      
      const url = `${NOMINATIM_API}/reverse`;
      const params = {
        format: 'json',
        lat,
        lon,
        zoom: zoom || 18,
        addressdetails: addressdetails || 1
      };
      
      // Set required headers for OpenStreetMap
      const response = await axios.get(url, { 
        params,
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'SmiloApp/1.0 (https://smilo.dental)' // Required by Nominatim usage policy
        }
      });
      
      res.json(response.data);
    } catch (error) {
      console.error('Error in reverse geocoding:', error);
      res.status(500).json({ error: 'Failed to fetch location data' });
    }
  });
} catch (routeError) {
  console.error('Error registering reverse geocoding route:', routeError);
}

// Endpoint for forward geocoding (address to coordinates)
try {
  router.get('/search', async (req, res) => {
    try {
      const { q, format, limit } = req.query;
      
      // Validate required parameters
      if (!q) {
        return res.status(400).json({ error: 'Search query is required' });
      }
      
      const url = `${NOMINATIM_API}/search`;
      const params = {
        q,
        format: format || 'json',
        limit: limit || 1
      };
      
      // Set required headers for OpenStreetMap
      const response = await axios.get(url, { 
        params,
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'SmiloApp/1.0 (https://smilo.dental)' // Required by Nominatim usage policy
        }
      });
      
      res.json(response.data);
    } catch (error) {
      console.error('Error in geocoding search:', error);
      res.status(500).json({ error: 'Failed to fetch location data' });
    }
  });
} catch (routeError) {
  console.error('Error registering search route:', routeError);
}

export default router; 