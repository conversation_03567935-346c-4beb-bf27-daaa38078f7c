import express from 'express';
import OpenAI from 'openai';
import cors from 'cors';
import dotenv from 'dotenv';
import rateLimit from 'express-rate-limit';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { OPENAI_API_KEY } from '../lib/config.js';

// Get current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.resolve(__dirname, '../..');

// Add logging but without exposing sensitive information
console.log('==== OpenAI Proxy Debug ====');
console.log(`Current directory: ${__dirname}`);
console.log(`Root directory: ${rootDir}`);

// Load environment variables properly
dotenv.config({ path: path.join(rootDir, '.env') });
dotenv.config({ path: path.join(rootDir, '.env.local') });

// Log status without showing the actual key
if (OPENAI_API_KEY) {
  console.log('✅ OpenAI API key loaded successfully from centralized config');
  console.log(`Key length: ${OPENAI_API_KEY.length}`);
} else {
  console.error('❌ OpenAI API key not found in centralized configuration!');
  console.error('Please make sure OPENAI_API_KEY is set in your .env or .env.local file');
}

const router = express.Router();

// Use more restrictive CORS
const corsOptions = {
  origin: process.env.NODE_ENV === 'production' 
    ? [process.env.ALLOWED_ORIGIN || 'https://smilo.dental'] 
    : 'http://localhost:3000',
  methods: ['GET', 'POST'],
  credentials: true,
  maxAge: 86400
};

router.use(cors(corsOptions));
router.use(express.json({ limit: '16mb' })); // Reduce limit for security

// Error handling middleware to ensure valid JSON responses
router.use((req, res, next) => {
  // Save the original res.json method
  const originalJson = res.json;

  // Override res.json to catch errors
  res.json = function(data) {
    // Ensure we're sending valid JSON data
    try {
      if (data === undefined || data === null) {
        console.error('Attempted to send null/undefined JSON response');
        return res.status(500).send(JSON.stringify({
          error: 'Server error: Invalid response data',
          details: 'Response contained null or undefined data'
        }));
      }

      // Test that the data can be stringified
      const jsonString = JSON.stringify(data);
      if (!jsonString) {
        throw new Error('Failed to stringify response data');
      }

      // Call the original method with the validated data
      return originalJson.call(this, data);
    } catch (error) {
      console.error('Error in JSON response:', error);
      return res.status(500).send(JSON.stringify({
        error: 'Server error: Invalid JSON response',
        details: error.message
      }));
    }
  };

  // Catch errors in the response cycle
  const originalEnd = res.end;
  res.end = function(chunk, encoding) {
    if (res.statusCode >= 400 && (!chunk || chunk.length === 0)) {
      // If we're sending an error status with no body, add a JSON error response
      if (!res.headersSent) {
        res.setHeader('Content-Type', 'application/json');
      }
      return originalEnd.call(this, JSON.stringify({
        error: `Server error: ${res.statusMessage || 'Unknown error'}`,
        status: res.statusCode
      }), 'utf8');
    }
    return originalEnd.call(this, chunk, encoding);
  };

  next();
});

// More restrictive rate limiting
const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  standardHeaders: true,
  legacyHeaders: false,
  message: { error: 'Too many requests, please try again later' },
  skip: (req) => {
    // Skip rate limiting for health checks
    return req.path === '/config-check';
  }
});

// Apply rate limiting to all routes
router.use(apiLimiter);

// Initialize OpenAI client on the server side
const openai = new OpenAI({
  apiKey: OPENAI_API_KEY
});

// Error handler for OpenAI API errors
const handleOpenAIError = (error, res) => {
  console.error('OpenAI API error:', error);

  // Log detailed error information
  console.error('Error details:', {
    message: error.message,
    status: error.status,
    type: error.type,
    code: error.code,
    param: error.param,
    stack: error.stack?.substring(0, 200)
  });

  // Check for specific error types
  if (error.status === 429) {
    return res.status(429).json({
      error: 'Rate limit exceeded. Please try again later.',
      details: error.message,
      code: 'rate_limit_exceeded'
    });
  }

  if (error.status === 400) {
    return res.status(400).json({
      error: 'Invalid request to OpenAI API',
      details: error.message,
      code: 'invalid_request'
    });
  }

  if (error.message?.includes('API key')) {
    return res.status(401).json({
      error: 'Invalid API key configuration',
      details: 'The OpenAI API key appears to be invalid or missing',
      code: 'invalid_api_key'
    });
  }

  if (error.message?.includes('timeout') || error.message?.includes('ETIMEDOUT')) {
    return res.status(504).json({
      error: 'Request to OpenAI API timed out',
      details: 'The request took too long to complete',
      code: 'timeout'
    });
  }

  if (error.message?.includes('network') || error.message?.includes('ECONNREFUSED')) {
    return res.status(503).json({
      error: 'Network error connecting to OpenAI API',
      details: 'Could not establish connection to the OpenAI service',
      code: 'network_error'
    });
  }

  // Default error response
  return res.status(500).json({
    error: 'Failed to process AI request',
    details: error.message || 'Unknown error',
    status: error.status || 500,
    code: 'internal_error'
  });
};

// Debug endpoint to check API configuration
router.get('/config-check', (req, res) => {
  const hasKey = !!OPENAI_API_KEY;
  const keyLength = OPENAI_API_KEY ? OPENAI_API_KEY.length : 0;
  const keyPrefix = OPENAI_API_KEY ? OPENAI_API_KEY.substring(0, 7) : '';

  res.json({
    hasApiKey: hasKey,
    keyLength: keyLength,
    keyPrefix: keyPrefix,
    openaiClientInitialized: !!openai
  });
});

// Chat completion endpoint
router.post('/chat', async (req, res) => {
  try {
    console.log('Chat API endpoint called');

    // Check if API key is configured
    if (!OPENAI_API_KEY) {
      console.error('Chat API error: Missing OpenAI API key');
      return res.status(500).json({ error: 'OpenAI API key is not configured', code: 'missing_api_key' });
    }

    const { messages, model, max_tokens, temperature } = req.body;
    console.log('Chat request received:', {
      messageCount: messages?.length,
      model,
      maxTokens: max_tokens
    });

    // Validate required parameters
    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      return res.status(400).json({ error: 'Valid messages array is required', code: 'invalid_messages' });
    }

    // Set default model if not provided
    const modelToUse = model || 'gpt-3.5-turbo';

    // Validate the API key with a simple test call first
    try {
      console.log('Validating API key...');
      await openai.chat.completions.create({
        messages: [{ role: 'user', content: 'Test' }],
        model: 'gpt-3.5-turbo',
        max_tokens: 5,
      });
      console.log('API key validation successful');
    } catch (testError) {
      console.error('API key validation failed:', testError.message);
      if (testError.message.includes('API key')) {
        return res.status(401).json({
          error: 'Invalid OpenAI API key',
          details: testError.message,
          code: 'invalid_api_key'
        });
      }
    }

    try {
      console.log(`Making OpenAI chat completion with model: ${modelToUse}`);
      const completion = await openai.chat.completions.create({
        messages,
        model: modelToUse,
        max_tokens: max_tokens || 500,
        temperature: temperature || 0.7,
      });

      console.log('Chat completion successful');
      return res.json(completion);
    } catch (openaiError) {
      console.error('OpenAI API error in chat completion:', openaiError);

      // Try with a fallback model if the primary model fails
      if (modelToUse !== 'gpt-3.5-turbo') {
        try {
          console.log('Attempting with fallback model gpt-3.5-turbo...');
          const fallbackCompletion = await openai.chat.completions.create({
            messages,
            model: 'gpt-3.5-turbo',
            max_tokens: max_tokens || 500,
            temperature: temperature || 0.7,
          });

          console.log('Fallback model completion successful');
          return res.json(fallbackCompletion);
        } catch (fallbackError) {
          console.error('Fallback model also failed:', fallbackError);
          throw openaiError; // Throw the original error
        }
      } else {
        throw openaiError;
      }
    }
  } catch (error) {
    return handleOpenAIError(error, res);
  }
});

// Vision model endpoint for image processing
router.post('/vision', async (req, res) => {
  console.log('Vision API endpoint called');
  try {
    // Check if API key is available
    if (!OPENAI_API_KEY) {
      console.error('Vision API error: Missing OpenAI API key');
      return res.status(500).json({ error: 'OpenAI API key is not configured' });
    }

    // Validate that the API key looks correct
    if (!OPENAI_API_KEY.startsWith('sk-') || OPENAI_API_KEY.length < 20) {
      console.error('Vision API error: API key appears invalid (wrong format)');
      return res.status(500).json({ error: 'OpenAI API key appears invalid' });
    }

    const { messages, max_tokens, model } = req.body;

    // Log the request for debugging
    console.log('Vision API request:', {
      hasMessages: !!messages,
      messageCount: messages?.length,
      maxTokens: max_tokens,
      requestedModel: model || 'not specified'
    });

    // Set default model if not provided
    const modelToUse = model || 'gpt-4o';

    // Validate required parameters
    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      console.error('Vision API error: Invalid messages array');
      return res.status(400).json({ error: 'Valid messages array is required' });
    }

    // Check for image content and fix format if needed
    let hasImage = false;
    // Create a deep copy of messages to avoid modifying the original
    const processedMessages = JSON.parse(JSON.stringify(messages));

    // Log the original messages for debugging
    console.log('Original messages:', JSON.stringify(messages, null, 2));

    for (let i = 0; i < processedMessages.length; i++) {
      const message = processedMessages[i];
      if (message.content && Array.isArray(message.content)) {
        for (let j = 0; j < message.content.length; j++) {
          const content = message.content[j];
          if (content.type === 'image_url') {
            hasImage = true;
            console.log('Found image_url content:', JSON.stringify(content, null, 2));

            // Fix image_url format if needed
            if (content.image_url) {
              console.log('Image URL type:', typeof content.image_url);
              if (content.image_url.url) {
                console.log('URL property type:', typeof content.image_url.url);
              }

              // Case 1: image_url is a string (old format)
              if (typeof content.image_url === 'string') {
                console.log('Converting string image URL to object format');
                // Check if it's too large
                if (content.image_url.length > 20 * 1024 * 1024) {
                  console.error('Vision API error: Image too large');
                  return res.status(400).json({ error: 'Image is too large (max 20MB)' });
                }
                // Convert to new format
                processedMessages[i].content[j].image_url = { url: content.image_url };
              }
              // Case 2: image_url is an object but url is an object (nested object issue)
              else if (typeof content.image_url === 'object' && content.image_url.url &&
                      typeof content.image_url.url === 'object') {
                console.error('Vision API error: Invalid image URL format (nested object)');
                console.log('Nested object structure:', JSON.stringify(content.image_url, null, 2));

                // Try to fix the nested object if possible
                if (content.image_url.url.url && typeof content.image_url.url.url === 'string') {
                  console.log('Fixing doubly nested URL structure');
                  processedMessages[i].content[j].image_url = { url: content.image_url.url.url };
                } else {
                  return res.status(400).json({
                    error: 'Invalid image URL format',
                    details: 'The image URL should be a string or an object with a string url property'
                  });
                }
              }
              // Case 3: image_url is already in correct format with string url
              else if (typeof content.image_url === 'object' && content.image_url.url &&
                      typeof content.image_url.url === 'string') {
                console.log('Image URL is in correct format');
                // No changes needed
              }
              // Case 4: image_url is an object but missing url property
              else if (typeof content.image_url === 'object' && !content.image_url.url) {
                console.error('Vision API error: Missing url property in image_url object');
                return res.status(400).json({
                  error: 'Invalid image URL format',
                  details: 'The image_url object must have a url property'
                });
              }
            } else {
              console.error('Vision API error: Missing image URL');
              return res.status(400).json({ error: 'Image URL is required for vision analysis' });
            }
          }
        }
      }
    }

    if (!hasImage) {
      console.warn('Vision API warning: No image found in messages');
    }

    console.log('Making OpenAI vision API call...');
    try {
      // First make a test call to validate the API key
      // Use a much simpler request to avoid unnecessary overhead
      try {
        const testCall = await openai.chat.completions.create({
          messages: [{ role: 'user', content: 'Test' }],
          model: 'gpt-3.5-turbo',
          max_tokens: 5,
        });

        console.log('API key validation successful');
      } catch (testError) {
        console.error('API key validation failed:', testError.message);
        if (testError.message.includes('API key')) {
          return res.status(500).json({
            error: 'Invalid OpenAI API key',
            details: testError.message
          });
        }
      }

      // Use the model specified by the client or default to gpt-4o
      // gpt-4-vision-preview has been deprecated
      console.log(`Using ${modelToUse} for vision analysis`);
      console.log('Processed messages for vision API:', JSON.stringify(processedMessages, null, 2));
      const completion = await openai.chat.completions.create({
        messages: processedMessages,
        model: modelToUse,
        max_tokens: max_tokens || 1000,
      });

      console.log('Vision API call successful');
      return res.json(completion);
    } catch (visionError) {
      console.error('Direct vision API error:', visionError);
      console.error('Error details:', {
        message: visionError.message,
        status: visionError.status,
        type: visionError.type,
        code: visionError.code,
        param: visionError.param
      });

      // Check for specific error types
      let status = 500;
      let errorMessage = 'Failed to process image with vision API';
      let errorCode = 'vision_api_error';

      if (visionError.message) {
        if (visionError.message.includes('API key')) {
          errorMessage = 'Invalid OpenAI API key';
          errorCode = 'invalid_api_key';
          status = 401;
        } else if (visionError.message.includes('rate limit')) {
          errorMessage = 'Rate limit exceeded on OpenAI API';
          errorCode = 'rate_limit_exceeded';
          status = 429;
        } else if (visionError.message.includes('billing')) {
          errorMessage = 'OpenAI billing issue - please check your account';
          errorCode = 'billing_error';
          status = 402;
        } else if (visionError.message.includes('too many tokens')) {
          errorMessage = 'Image or request too large';
          errorCode = 'content_too_large';
          status = 413;
        } else if (visionError.message.includes('not found') || visionError.message.includes('404')) {
          errorMessage = 'The vision model is not available or has been deprecated';
          errorCode = 'model_not_found';
          status = 404;

          // Try with a fallback model
          try {
            console.log('Attempting vision analysis with fallback model gpt-4o...');
            const fallbackCompletion = await openai.chat.completions.create({
              messages: processedMessages,
              model: 'gpt-4o',
              max_tokens: max_tokens || 800,
            });

            console.log('Fallback vision model successful');
            return res.json(fallbackCompletion);
          } catch (fallbackError) {
            console.error('Fallback vision model also failed:', fallbackError);
            // Continue to error response
          }
        }
      }

      return res.status(status).json({
        error: errorMessage,
        details: visionError.message || 'Unknown error',
        status: visionError.status || status,
        code: errorCode
      });
    }
  } catch (error) {
    return handleOpenAIError(error, res);
  }
});

// Assistants API endpoint - using simple path pattern to avoid path-to-regexp errors
router.post('/assistants-api', async (req, res) => {
  try {
    const { action } = req.query; // Use query parameter instead of path parameter
    const { assistant_id, thread_id, content, run_id } = req.body;

    // Validate action parameter
    if (!action) {
      return res.status(400).json({ error: 'Action parameter is required' });
    }

    // Validate OpenAI API key
    if (!OPENAI_API_KEY) {
      console.error('Assistants API error: Missing OpenAI API key');
      return res.status(500).json({ error: 'OpenAI API key is not configured' });
    }

    let result;

    switch (action) {
      case 'create-thread':
        try {
          result = await openai.beta.threads.create();
          return res.json(result);
        } catch (threadError) {
          console.error('Error creating thread:', threadError);
          return res.status(500).json({
            error: 'Failed to create thread',
            details: threadError.message || 'Unknown error'
          });
        }

      case 'create-message':
        if (!thread_id || !content) {
          return res.status(400).json({ error: 'Thread ID and content are required' });
        }
        try {
          result = await openai.beta.threads.messages.create(thread_id, {
            role: 'user',
            content
          });
        } catch (msgError) {
          console.error('Error creating message:', msgError);
          return res.status(500).json({
            error: 'Failed to create message',
            details: msgError.message || 'Unknown error'
          });
        }
        break;

      case 'run-assistant':
        if (!thread_id || !assistant_id) {
          return res.status(400).json({ error: 'Thread ID and assistant ID are required' });
        }
        try {
          result = await openai.beta.threads.runs.create(thread_id, {
            assistant_id
          });
        } catch (runError) {
          console.error('Error running assistant:', runError);
          return res.status(500).json({
            error: 'Failed to run assistant',
            details: runError.message || 'Unknown error'
          });
        }
        break;

      case 'get-run-status':
        if (!thread_id || !run_id) {
          return res.status(400).json({ error: 'Thread ID and run ID are required' });
        }
        try {
          result = await openai.beta.threads.runs.retrieve(thread_id, run_id);
        } catch (statusError) {
          console.error('Error getting run status:', statusError);
          return res.status(500).json({
            error: 'Failed to get run status',
            details: statusError.message || 'Unknown error'
          });
        }
        break;

      case 'list-messages':
        if (!thread_id) {
          return res.status(400).json({ error: 'Thread ID is required' });
        }
        try {
          result = await openai.beta.threads.messages.list(thread_id);
        } catch (listError) {
          console.error('Error listing messages:', listError);
          return res.status(500).json({
            error: 'Failed to list messages',
            details: listError.message || 'Unknown error'
          });
        }
        break;

      default:
        return res.status(400).json({ error: 'Invalid action' });
    }

    return res.json(result);
  } catch (error) {
    return handleOpenAIError(error, res);
  }
});

// Fallback endpoint - direct text completion for simple requests
router.post('/fallback', async (req, res) => {
  try {
    console.log('Fallback endpoint called');

    // Check if API key is configured
    if (!OPENAI_API_KEY) {
      console.error('Fallback API error: Missing OpenAI API key');
      return res.status(500).json({ error: 'OpenAI API key is not configured', code: 'missing_api_key' });
    }

    const { prompt } = req.body;
    console.log('Fallback prompt received:', prompt ? prompt.substring(0, 50) + '...' : 'undefined');

    if (!prompt || typeof prompt !== 'string') {
      return res.status(400).json({ error: 'Valid prompt is required', code: 'invalid_prompt' });
    }

    // Create a simple chat message structure
    const messages = [
      {
        role: "system",
        content: "You are a helpful dental AI assistant named SMILO. Provide concise, helpful information about dental health."
      },
      {
        role: "user",
        content: prompt
      }
    ];

    console.log('Making fallback OpenAI API call...');
    try {
      // First try a simple test call to validate the API key
      try {
        await openai.chat.completions.create({
          messages: [{ role: 'user', content: 'Test' }],
          model: 'gpt-3.5-turbo',
          max_tokens: 5,
        });
        console.log('Fallback API key validation successful');
      } catch (testError) {
        console.error('Fallback API key validation failed:', testError.message);
        if (testError.message.includes('API key')) {
          return res.status(401).json({
            error: 'Invalid OpenAI API key',
            details: testError.message,
            code: 'invalid_api_key'
          });
        }
      }

      // Make the actual completion call
      const completion = await openai.chat.completions.create({
        messages,
        model: 'gpt-3.5-turbo',
        max_tokens: 500,
      });

      console.log('Fallback completion successful');
      return res.json({
        text: completion.choices[0].message.content,
        model: completion.model
      });
    } catch (openaiError) {
      console.error('OpenAI API error in fallback:', openaiError);

      // Try one more time with a different model as a last resort
      try {
        console.log('Attempting fallback with alternative model...');
        const altCompletion = await openai.chat.completions.create({
          messages,
          model: 'gpt-4o',  // Try with a different model
          max_tokens: 300,
        });

        console.log('Alternative model fallback successful');
        return res.json({
          text: altCompletion.choices[0].message.content,
          model: altCompletion.model,
          fallback: true
        });
      } catch (altError) {
        console.error('Alternative model fallback also failed:', altError);
        // Continue to error handler
        throw openaiError;
      }
    }
  } catch (error) {
    return handleOpenAIError(error, res);
  }
});

export default router;