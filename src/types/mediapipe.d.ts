declare module '@mediapipe/face_mesh' {
  // Supported models enum
  export enum SupportedModels {
    MediaPipeFaceMesh = 'MediaPipeFaceMesh'
  }

  // Configuration for the detector
  export interface MediaPipeFaceMeshModelConfig {
    runtime: 'tfjs' | 'mediapipe';
    maxFaces?: number;
    refineLandmarks?: boolean;
    solutionPath?: string;
  }

  // Landmark type
  export interface Landmark {
    x: number;
    y: number;
    z: number;
  }

  // Face detection result
  export interface FaceDetection {
    landmarks: Landmark[];
    box: {
      xMin: number;
      yMin: number;
      width: number;
      height: number;
    };
  }

  // Face detector type
  export interface FaceDetector {
    estimateFaces(image: HTMLVideoElement | HTMLImageElement | HTMLCanvasElement): Promise<FaceDetection[]>;
  }

  // Create detector function
  export function createDetector(
    model: SupportedModels,
    config: MediaPipeFaceMeshModelConfig
  ): Promise<FaceDetector>;
} 