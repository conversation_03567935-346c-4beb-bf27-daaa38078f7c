/// <reference types="lodash" />
/// <reference types="node" />
/// <reference types="jest" />

declare module 'lodash' {
  export * from 'lodash';
}

// Extend window interface if needed
declare interface Window {
  _smiloAnimationsEnabled?: boolean;
  _pageLoadTime?: number;
  performance: {
    memory?: {
      usedJSHeapSize: number;
      jsHeapSizeLimit: number;
    };
  };
}

// Extend navigator interface if needed
declare interface Navigator {
  deviceMemory?: number;
  hardwareConcurrency: number;
}

// Add any other global type declarations here
declare module '*.svg' {
  const content: any;
  export default content;
}

declare module '*.png' {
  const content: any;
  export default content;
}

declare module '*.jpg' {
  const content: any;
  export default content;
}

declare module '*.jpeg' {
  const content: any;
  export default content;
}

declare module '*.gif' {
  const content: any;
  export default content;
}

declare module '*.webp' {
  const content: any;
  export default content;
}

// Add environment variables type declarations
interface ImportMetaEnv {
  VITE_SUPABASE_URL: string;
  VITE_SUPABASE_ANON_KEY: string;
  VITE_OPENAI_API_KEY: string;
  VITE_GOOGLE_MAPS_API_KEY: string;
  VITE_GOOGLE_ANALYTICS_ID?: string;
  VITE_GOOGLE_TAG_MANAGER_ID?: string;
  VITE_FACEBOOK_PIXEL_ID?: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
} 