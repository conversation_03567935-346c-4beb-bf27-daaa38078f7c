/**
 * Middleware Security Enhancements
 * 
 * This middleware adds protection against known vulnerabilities,
 * specifically targeting the Next.js middleware authorization bypass vulnerability (CVE-2025-29927)
 * and other security concerns.
 */

/**
 * Block the x-middleware-subrequest header to prevent Next.js middleware bypass
 * This mitigates the CVE-2025-29927 vulnerability
 * @param {Request} req - The request object
 * @param {Response} res - The response object
 * @param {Function} next - The next middleware function
 */
export function protectAgainstMiddlewareBypass(req, res, next) {
  // Remove the x-middleware-subrequest header if present
  // This prevents exploitation of CVE-2025-29927
  if (req.headers && req.headers['x-middleware-subrequest']) {
    delete req.headers['x-middleware-subrequest'];
    
    // Log potential attack attempt (consider sending to security monitoring)
    console.warn('Security alert: Potentially malicious request with x-middleware-subrequest header blocked');
  }
  
  next();
}

/**
 * Implement rate limiting on sensitive endpoints
 * @param {Request} req - The request object
 * @param {Response} res - The response object
 * @param {Function} next - The next middleware function
 */
export function sensitiveEndpointRateLimiting(req, res, next) {
  // This is a placeholder for more sophisticated rate limiting
  // In a real implementation, you would use a library like express-rate-limit
  // and potentially integrate with Redis or another store for distributed rate limiting
  
  // Example of paths that should be rate limited
  const sensitiveEndpoints = [
    '/api/auth',
    '/api/admin',
    '/api/user',
    '/login',
    '/signup'
  ];
  
  // Check if the current path should be rate limited
  const shouldRateLimit = sensitiveEndpoints.some(endpoint => 
    req.path.startsWith(endpoint)
  );
  
  if (shouldRateLimit) {
    // Apply specific rate limiting logic or delegate to express-rate-limit
    // For now, we just add a header to indicate rate limiting is active
    res.setHeader('X-Rate-Limit-Protected', 'true');
  }
  
  next();
}

/**
 * Sanitize input parameters to prevent injection attacks
 * @param {Request} req - The request object
 * @param {Response} res - The response object
 * @param {Function} next - The next middleware function
 */
export function sanitizeInputParameters(req, res, next) {
  // Basic sanitization for query parameters
  if (req.query) {
    Object.keys(req.query).forEach(key => {
      if (typeof req.query[key] === 'string') {
        // Remove potentially dangerous characters
        req.query[key] = req.query[key]
          .replace(/[<>]/g, '') // Remove angle brackets
          .replace(/javascript:/gi, '') // Remove javascript: protocol
          .replace(/on\w+=/gi, ''); // Remove event handlers
      }
    });
  }
  
  // Similar sanitization for request body if it exists and is an object
  if (req.body && typeof req.body === 'object') {
    Object.keys(req.body).forEach(key => {
      if (typeof req.body[key] === 'string') {
        req.body[key] = req.body[key]
          .replace(/[<>]/g, '')
          .replace(/javascript:/gi, '')
          .replace(/on\w+=/gi, '');
      }
    });
  }
  
  next();
}

/**
 * Add additional authorization validation beyond middleware
 * This provides defense in depth against middleware bypass vulnerabilities
 * @param {Request} req - The request object
 * @param {Response} res - The response object
 * @param {Function} next - The next middleware function
 */
export function additionalAuthorizationValidation(req, res, next) {
  // Protected routes that require additional validation
  const protectedRoutes = [
    '/api/admin',
    '/api/user/profile',
    '/api/settings'
  ];
  
  // Check if the current path is a protected route
  const isProtectedRoute = protectedRoutes.some(route => 
    req.path.startsWith(route)
  );
  
  if (isProtectedRoute) {
    // In a real implementation, you would validate the user session here
    // regardless of any middleware authentication
    
    // Example validation logic (implement your actual validation)
    const hasValidToken = validateAuthToken(req);
    
    if (!hasValidToken) {
      return res.status(401).json({ 
        error: 'Unauthorized access',
        message: 'Additional authentication required'
      });
    }
  }
  
  next();
}

/**
 * Dummy auth token validation function - replace with your actual implementation
 * @param {Request} req - The request object
 * @returns {boolean} Whether the token is valid
 */
function validateAuthToken(req) {
  // This is a placeholder for actual token validation logic
  // In a real implementation, you would validate the session token,
  // check user permissions, etc.
  
  // Example: Check for Authorization header
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return false;
  }
  
  // Extract and validate token (implement your actual validation)
  const token = authHeader.split(' ')[1];
  return token && token.length > 10; // Dummy validation
}

/**
 * Apply all middleware security enhancements
 * @param {Request} req - The request object
 * @param {Response} res - The response object
 * @param {Function} next - The next middleware function
 */
export function applyAllSecurityEnhancements(req, res, next) {
  // Apply all security enhancements in sequence
  protectAgainstMiddlewareBypass(req, res, () => {
    sensitiveEndpointRateLimiting(req, res, () => {
      sanitizeInputParameters(req, res, () => {
        additionalAuthorizationValidation(req, res, next);
      });
    });
  });
}

export default {
  protectAgainstMiddlewareBypass,
  sensitiveEndpointRateLimiting,
  sanitizeInputParameters,
  additionalAuthorizationValidation,
  applyAllSecurityEnhancements
}; 