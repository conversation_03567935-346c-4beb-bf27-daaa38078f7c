/**
 * Security Middleware
 * 
 * This middleware adds security headers to the application to protect against
 * common web vulnerabilities.
 */

/**
 * Apply security headers to the response
 * @param {Request} req - The request object
 * @param {Response} res - The response object
 * @param {Function} next - The next middleware function
 */
export function applySecurityHeaders(req, res, next) {
  // Content Security Policy
  // Restricts the sources from which resources can be loaded
  res.setHeader(
    'Content-Security-Policy',
    "default-src 'self'; " +
    "script-src 'self' https://apis.google.com https://*.googleapis.com https://www.googletagmanager.com https://polyfill.io 'unsafe-inline' 'unsafe-eval'; " +
    "style-src 'self' https://fonts.googleapis.com 'unsafe-inline'; " +
    "img-src 'self' data: https://*.googleapis.com https://*.gstatic.com https://twskhrwvdrebsghyczlu.supabase.co; " +
    "font-src 'self' https://fonts.gstatic.com; " +
    "connect-src 'self' https://twskhrwvdrebsghyczlu.supabase.co wss://twskhrwvdrebsghyczlu.supabase.co https://api.openai.com https://*.googleapis.com http://localhost:* wss://localhost:*; " +
    "frame-src 'self' https://www.google.com; " +
    "object-src 'none';"
  );

  // X-Content-Type-Options
  // Prevents browsers from MIME-sniffing a response away from the declared content-type
  res.setHeader('X-Content-Type-Options', 'nosniff');

  // X-Frame-Options
  // Prevents clickjacking attacks by ensuring the page can't be embedded in an iframe
  res.setHeader('X-Frame-Options', 'SAMEORIGIN');

  // X-XSS-Protection
  // Enables the Cross-site scripting (XSS) filter in browsers
  res.setHeader('X-XSS-Protection', '1; mode=block');

  // Strict-Transport-Security
  // Enforces secure (HTTPS) connections to the server
  res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');

  // Referrer-Policy
  // Controls how much referrer information should be included with requests
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');

  // Permissions-Policy
  // Controls which browser features and APIs can be used
  res.setHeader(
    'Permissions-Policy',
    'camera=(), microphone=(), geolocation=(self), interest-cohort=()'
  );

  // Cache-Control
  // Controls how the page is cached
  res.setHeader('Cache-Control', 'no-store, max-age=0');

  if (next) {
    next();
  }
}

/**
 * Create security headers for client-side fetch requests
 * @returns {Object} Security headers object
 */
export function getSecurityHeaders() {
  return {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'SAMEORIGIN',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin'
  };
}

export default {
  applySecurityHeaders,
  getSecurityHeaders
};
