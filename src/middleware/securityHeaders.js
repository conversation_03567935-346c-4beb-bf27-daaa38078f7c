/**
 * Security headers middleware
 * Adds security headers to all API responses to protect against common web vulnerabilities
 */

export const securityHeaders = (req, res, next) => {
  // Prevent browsers from interpreting files as a different MIME type
  res.setHeader('X-Content-Type-Options', 'nosniff');
  
  // Prevent clickjacking attacks
  res.setHeader('X-Frame-Options', 'SAMEORIGIN');
  
  // Enable XSS protection in browsers
  res.setHeader('X-XSS-Protection', '1; mode=block');
  
  // Limit referrer information sent in requests
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  // Prevent loading resources from external domains
  res.setHeader('Content-Security-Policy', "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; connect-src 'self' https://api.openai.com; font-src 'self'; frame-src 'self'; media-src 'self'; object-src 'none'; worker-src 'self' blob:;");
  
  // Prevent this page from being cached in browsers and proxies
  res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
  res.setHeader('Pragma', 'no-cache');
  res.setHeader('Expires', '0');
  res.setHeader('Surrogate-Control', 'no-store');
  
  // CORS headers - customize this based on your requirements
  res.setHeader('Access-Control-Allow-Origin', process.env.NODE_ENV === 'production' ? process.env.ALLOWED_ORIGIN || 'https://smilo.dental' : 'http://localhost:3000');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
  res.setHeader('Access-Control-Allow-Credentials', 'true');
  
  // Permissions Policy (formerly Feature Policy)
  // Restrict sensitive browser features
  res.setHeader('Permissions-Policy', 'camera=(), microphone=(), geolocation=(self), interest-cohort=()');
  
  next();
};

/**
 * Apply security headers to Express app
 * @param {Object} app - Express app instance
 */
export const applySecurityHeaders = (app) => {
  app.use(securityHeaders);
  
  // Enable CORS preflight for all routes
  app.options('*', (req, res) => {
    res.status(200).end();
  });
  
  // Add security middleware for routes
  app.use((req, res, next) => {
    // Block requests with suspicious patterns
    const url = req.url.toLowerCase();
    
    // Block common attack patterns
    const blockedPatterns = [
      /\.\.\//, // Directory traversal
      /\/etc\/passwd/, // System file access attempt
      /\/wp-admin/, // WordPress admin attempts
      /\/wp-login/, // WordPress login attempts
      /\/administrator/, // Common admin paths
      /\/admin.php/, // PHP admin paths
      /\/config.php/, // Configuration files
      /\/sqlmap/, // SQLMap scanner
      /\/shell/, // Shell access
      /\/cmd.php/, // Command execution
    ];
    
    if (blockedPatterns.some(pattern => pattern.test(url))) {
      return res.status(403).json({ error: 'Access denied' });
    }
    
    next();
  });
  
  // Add global error handler
  app.use((err, req, res, next) => {
    // Log the error without exposing sensitive information
    console.error('API Error:', {
      path: req.path,
      method: req.method,
      errorName: err.name,
      errorMessage: err.message
    });
    
    // Send a sanitized error response
    res.status(err.status || 500).json({
      error: 'An error occurred processing your request',
      code: 'server_error'
    });
  });
};

export default securityHeaders; 