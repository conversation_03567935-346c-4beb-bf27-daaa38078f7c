import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

// Sample data for dental schools and clinics with reviews
const DENTAL_SCHOOLS_DATA = [
  {
    id: 1,
    name: "University of California, San Francisco School of Dentistry",
    location: "San Francisco, CA",
    rating: 4.7,
    reviewCount: 128,
    description: "UCSF School of Dentistry is recognized for its excellence in dental education, patient care, and research.",
    services: ["General Dentistry", "Orthodontics", "Periodontics", "Oral Surgery"],
    reviews: [
      { id: 1, author: "<PERSON>", rating: 5, text: "Excellent care from the dental students. My procedure was supervised by experienced faculty and cost about 40% less than a private practice.", date: "2023-10-15" },
      { id: 2, author: "<PERSON>", rating: 4, text: "Good quality care, though appointments take longer than at a regular dentist. The savings are worth it if you have the time.", date: "2023-09-22" },
      { id: 3, author: "<PERSON>", rating: 5, text: "I had a complex procedure done here and was very impressed with the professionalism and care I received.", date: "2023-08-10" }
    ]
  },
  {
    id: 2,
    name: "New York University College of Dentistry",
    location: "New York, NY",
    rating: 4.5,
    reviewCount: 156,
    description: "NYU College of Dentistry is committed to providing high-quality, affordable dental care while training the next generation of dental professionals.",
    services: ["General Dentistry", "Pediatric Dentistry", "Prosthodontics", "Endodontics"],
    reviews: [
      { id: 1, author: "Jennifer K.", rating: 5, text: "The student dentists are supervised closely and provide excellent care. I saved over $1000 on my dental work.", date: "2023-11-05" },
      { id: 2, author: "Robert M.", rating: 4, text: "Very thorough examination and treatment. Appointments run long but the care is excellent.", date: "2023-10-18" },
      { id: 3, author: "Emily S.", rating: 4, text: "Great experience overall. The facility is modern and clean, and the students are knowledgeable and professional.", date: "2023-09-30" }
    ]
  },
  {
    id: 3,
    name: "University of Michigan School of Dentistry",
    location: "Ann Arbor, MI",
    rating: 4.8,
    reviewCount: 112,
    description: "The University of Michigan School of Dentistry is a leader in dental education, offering comprehensive care at reduced rates.",
    services: ["General Dentistry", "Orthodontics", "Oral Surgery", "Implant Dentistry"],
    reviews: [
      { id: 1, author: "Thomas H.", rating: 5, text: "Outstanding care at about half the cost of private practice. The students are supervised by top-notch faculty.", date: "2023-11-12" },
      { id: 2, author: "Lisa P.", rating: 5, text: "I had a dental implant procedure here and saved thousands. The quality was excellent.", date: "2023-10-25" },
      { id: 3, author: "James W.", rating: 4, text: "Very thorough care, though appointments can be lengthy. Great option if you're looking to save money.", date: "2023-09-15" }
    ]
  },
  {
    id: 4,
    name: "University of Pennsylvania School of Dental Medicine",
    location: "Philadelphia, PA",
    rating: 4.6,
    reviewCount: 98,
    description: "Penn Dental Medicine offers comprehensive dental care at reduced fees while providing students with clinical experience.",
    services: ["General Dentistry", "Pediatric Dentistry", "Periodontics", "Prosthodontics"],
    reviews: [
      { id: 1, author: "Karen L.", rating: 5, text: "Excellent care from the dental students. My procedure was supervised by experienced faculty and cost about 40% less than a private practice.", date: "2023-10-08" },
      { id: 2, author: "Mark J.", rating: 4, text: "Good quality care, though appointments take longer than at a regular dentist. The savings are worth it if you have the time.", date: "2023-09-17" },
      { id: 3, author: "Patricia D.", rating: 5, text: "I had a complex procedure done here and was very impressed with the professionalism and care I received.", date: "2023-08-22" }
    ]
  }
];

const COMMUNITY_CLINICS_DATA = [
  {
    id: 1,
    name: "Community Health Center Dental Clinic",
    location: "Boston, MA",
    rating: 4.4,
    reviewCount: 87,
    description: "A federally qualified health center offering sliding scale fees based on income and family size.",
    services: ["General Dentistry", "Preventive Care", "Emergency Services"],
    reviews: [
      { id: 1, author: "Maria G.", rating: 5, text: "This clinic has been a lifesaver for my family. We pay based on our income, and the care is excellent.", date: "2023-11-10" },
      { id: 2, author: "John T.", rating: 4, text: "Affordable dental care with caring staff. Sometimes there's a wait for appointments, but worth it.", date: "2023-10-05" },
      { id: 3, author: "Sophia R.", rating: 4, text: "I appreciate having access to quality dental care that I can afford on my limited income.", date: "2023-09-12" }
    ]
  },
  {
    id: 2,
    name: "Neighborhood Dental Access Program",
    location: "Chicago, IL",
    rating: 4.3,
    reviewCount: 76,
    description: "A non-profit dental clinic providing affordable care to uninsured and underinsured individuals.",
    services: ["General Dentistry", "Fillings", "Extractions", "Cleanings"],
    reviews: [
      { id: 1, author: "Carlos M.", rating: 5, text: "This clinic helped me when I had no insurance. They worked with me on a payment plan I could afford.", date: "2023-10-28" },
      { id: 2, author: "Aisha J.", rating: 4, text: "Caring dentists who take time to explain procedures. Very affordable compared to private practices.", date: "2023-09-30" },
      { id: 3, author: "William P.", rating: 4, text: "I was able to get emergency dental care here when I couldn't afford it elsewhere. Very grateful.", date: "2023-08-15" }
    ]
  },
  {
    id: 3,
    name: "Urban Dental Health Initiative",
    location: "Los Angeles, CA",
    rating: 4.5,
    reviewCount: 92,
    description: "A community-based dental clinic offering reduced-fee services to low-income residents.",
    services: ["General Dentistry", "Pediatric Dentistry", "Dentures", "Preventive Care"],
    reviews: [
      { id: 1, author: "Elena V.", rating: 5, text: "This clinic provides excellent care at prices I can afford. The staff is compassionate and professional.", date: "2023-11-15" },
      { id: 2, author: "Marcus L.", rating: 4, text: "I bring my whole family here. The sliding scale fees make dental care accessible for us.", date: "2023-10-20" },
      { id: 3, author: "Tanya H.", rating: 5, text: "I was able to get dentures here at a fraction of what private dentists quoted me. Life-changing!", date: "2023-09-05" }
    ]
  },
  {
    id: 4,
    name: "Rural Dental Access Program",
    location: "Boise, ID",
    rating: 4.6,
    reviewCount: 68,
    description: "A mobile dental clinic bringing affordable care to underserved rural communities.",
    services: ["General Dentistry", "Emergency Care", "Preventive Services"],
    reviews: [
      { id: 1, author: "Richard B.", rating: 5, text: "This mobile clinic comes to our small town quarterly. It's been a huge help for our community.", date: "2023-10-12" },
      { id: 2, author: "Nancy K.", rating: 4, text: "Affordable dental care without having to travel hours to the city. The dentists are skilled and kind.", date: "2023-09-25" },
      { id: 3, author: "George T.", rating: 5, text: "I was able to get a tooth extraction that I'd been putting off due to cost. Very reasonable fees.", date: "2023-08-30" }
    ]
  }
];

// Star rating component
const StarRating = ({ rating }) => {
  return (
    <div className="flex items-center">
      {[1, 2, 3, 4, 5].map((star) => (
        <svg
          key={star}
          className={`w-5 h-5 ${
            star <= rating
              ? 'text-yellow-400'
              : star - 0.5 <= rating
              ? 'text-yellow-400/50'
              : 'text-gray-400'
          }`}
          fill="currentColor"
          viewBox="0 0 20 20"
        >
          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
        </svg>
      ))}
      <span className="ml-2 text-white/80 text-sm">{rating.toFixed(1)}</span>
    </div>
  );
};

// Review card component
const ReviewCard = ({ review }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="bg-white/5 backdrop-blur-sm rounded-lg p-4 border border-white/10 hover:border-blue-500/20 transition-all duration-300"
    >
      <div className="flex justify-between items-start mb-2">
        <div className="font-medium text-white">{review.author}</div>
        <StarRating rating={review.rating} />
      </div>
      <p className="text-white/80 text-sm mb-2">{review.text}</p>
      <div className="text-white/50 text-xs">{new Date(review.date).toLocaleDateString()}</div>
    </motion.div>
  );
};

// Facility card component
const FacilityCard = ({ facility, onClick }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
      className="bg-gradient-to-br from-gray-800/80 to-slate-800/80 rounded-xl overflow-hidden border border-white/10 hover:border-blue-500/30 transition-all duration-300 shadow-lg hover:shadow-blue-500/5"
      onClick={() => onClick(facility)}
    >
      <div className="p-5">
        <h3 className="text-lg font-semibold text-white mb-1">{facility.name}</h3>
        <div className="flex items-center mb-2">
          <svg className="w-4 h-4 text-blue-400 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
          <span className="text-white/70 text-sm">{facility.location}</span>
        </div>
        <div className="flex items-center justify-between mb-3">
          <StarRating rating={facility.rating} />
          <span className="text-white/60 text-xs">{facility.reviewCount} reviews</span>
        </div>
        <p className="text-white/70 text-sm mb-3 line-clamp-2">{facility.description}</p>
        <div className="flex flex-wrap gap-1 mb-2">
          {facility.services.slice(0, 3).map((service, index) => (
            <span key={index} className="text-xs bg-blue-900/30 text-blue-300 px-2 py-1 rounded-full">
              {service}
            </span>
          ))}
          {facility.services.length > 3 && (
            <span className="text-xs bg-blue-900/30 text-blue-300 px-2 py-1 rounded-full">
              +{facility.services.length - 3} more
            </span>
          )}
        </div>
      </div>
      <div className="bg-gradient-to-r from-blue-500/10 to-indigo-500/10 px-5 py-3 border-t border-white/5">
        <button className="text-blue-400 hover:text-blue-300 text-sm font-medium flex items-center transition-colors">
          View Details
          <svg className="w-4 h-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
          </svg>
        </button>
      </div>
    </motion.div>
  );
};

// Facility detail component
const FacilityDetail = ({ facility, onBack }) => {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="bg-gradient-to-br from-gray-800/80 to-slate-800/80 rounded-xl border border-white/10 shadow-lg overflow-hidden"
    >
      <div className="p-6">
        <div className="flex justify-between items-start mb-4">
          <div>
            <h2 className="text-xl font-bold text-white mb-1">{facility.name}</h2>
            <div className="flex items-center mb-2">
              <svg className="w-4 h-4 text-blue-400 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              <span className="text-white/70 text-sm">{facility.location}</span>
            </div>
          </div>
          <button
            onClick={onBack}
            className="text-blue-400 hover:text-blue-300 transition-colors"
          >
            <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="flex items-center mb-4">
          <StarRating rating={facility.rating} />
          <span className="ml-2 text-white/60 text-sm">{facility.reviewCount} reviews</span>
        </div>

        <div className="mb-6">
          <h3 className="text-lg font-medium text-white mb-2">About</h3>
          <p className="text-white/80">{facility.description}</p>
        </div>

        <div className="mb-6">
          <h3 className="text-lg font-medium text-white mb-2">Services</h3>
          <div className="flex flex-wrap gap-2">
            {facility.services.map((service, index) => (
              <span key={index} className="text-sm bg-blue-900/30 text-blue-300 px-3 py-1 rounded-full">
                {service}
              </span>
            ))}
          </div>
        </div>

        <div>
          <h3 className="text-lg font-medium text-white mb-3">Patient Reviews</h3>
          <div className="space-y-4">
            {facility.reviews.map((review) => (
              <ReviewCard key={review.id} review={review} />
            ))}
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default function ReviewsRatingsPage() {
  const [activeTab, setActiveTab] = useState('schools');
  const [selectedFacility, setSelectedFacility] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');

  const handleFacilityClick = (facility) => {
    setSelectedFacility(facility);
  };

  const handleBackClick = () => {
    setSelectedFacility(null);
  };

  const filteredSchools = DENTAL_SCHOOLS_DATA.filter(
    (school) =>
      school.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      school.location.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredClinics = COMMUNITY_CLINICS_DATA.filter(
    (clinic) =>
      clinic.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      clinic.location.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="bg-gradient-to-b from-gray-800/90 to-indigo-900/30 rounded-xl p-4 md:p-6 shadow-xl overflow-auto h-auto min-h-[calc(100vh-12rem)] md:min-h-[75vh]">
      <h2 className="text-xl md:text-2xl font-bold text-white mb-2 text-center">Reviews & Ratings</h2>
      <p className="text-white/80 mb-6 text-center">Read patient reviews and ratings for dental schools and community clinics.</p>

      {/* Search bar */}
      <div className="mb-6 max-w-md mx-auto">
        <div className="relative">
          <input
            type="text"
            placeholder="Search by name or location..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full bg-white/10 border border-white/20 rounded-full px-5 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/50">
            <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
      </div>

      {/* Tabs */}
      {!selectedFacility && (
        <div className="flex justify-center mb-6">
          <div className="bg-slate-900/60 p-1 rounded-full backdrop-blur-sm shadow-inner border border-white/5">
            <button
              onClick={() => setActiveTab('schools')}
              className={`px-5 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                activeTab === 'schools'
                  ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-md'
                  : 'text-blue-300 hover:bg-white/10'
              }`}
            >
              Dental Schools
            </button>
            <button
              onClick={() => setActiveTab('clinics')}
              className={`px-5 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                activeTab === 'clinics'
                  ? 'bg-gradient-to-r from-sky-500 to-blue-600 text-white shadow-md'
                  : 'text-sky-300 hover:bg-white/10'
              }`}
            >
              Community Clinics
            </button>
          </div>
        </div>
      )}

      {/* Content */}
      {selectedFacility ? (
        <FacilityDetail facility={selectedFacility} onBack={handleBackClick} />
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
          {activeTab === 'schools' &&
            filteredSchools.map((school) => (
              <FacilityCard key={school.id} facility={school} onClick={handleFacilityClick} />
            ))}
          {activeTab === 'clinics' &&
            filteredClinics.map((clinic) => (
              <FacilityCard key={clinic.id} facility={clinic} onClick={handleFacilityClick} />
            ))}
          {((activeTab === 'schools' && filteredSchools.length === 0) ||
            (activeTab === 'clinics' && filteredClinics.length === 0)) && (
            <div className="col-span-2 text-center py-10">
              <p className="text-white/70">No results found for "{searchTerm}"</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
