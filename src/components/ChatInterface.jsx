import React, { useEffect, useRef, useState } from 'react';
import { motion } from 'framer-motion';
import QuestionForm from './QuestionForm';
import ResponseDisplay from './ResponseDisplay';
import DentalResources from './DentalResources';
import AuthButton from './AuthButton';
import ErrorMessage from './ErrorMessage';
import LoadingSpinner from './common/LoadingSpinner';
import VoiceAnalysisTool from './ai-tools/VoiceAnalysisTool';
import { useChat } from '../lib/hooks/useChat';
import { useUser } from '../contexts/UserContext';
import { format, isToday, isYesterday, isSameWeek, isSameMonth, isSameYear } from 'date-fns';
import { config } from '../lib/config';

const MAX_IMAGE_SIZE = 5 * 1024 * 1024; // 5MB

export default function ChatInterface() {
  const { user } = useUser();
  const {
    question,
    setQuestion,
    response,
    loading,
    image,
    setImage,
    history,
    error,
    clearError,
    handleSubmit,
    fetchHistory,
    lastSubmittedQuestion
  } = useChat(user);

  const [activeView, setActiveView] = useState('chat');
  const [showHistorySidebar, setShowHistorySidebar] = useState(false);
  const [showVoiceAnalysisTool, setShowVoiceAnalysisTool] = useState(false);
  const messagesEndRef = useRef(null);
  const [displayedHistory, setDisplayedHistory] = useState([]);
  const [selectedConversationDate, setSelectedConversationDate] = useState(null);
  const [groupedHistory, setGroupedHistory] = useState({});
  const [currentConversation, setCurrentConversation] = useState([]);

  // Function to scroll to the bottom of the messages
  const scrollToBottom = (force = false) => {
    // Only scroll if we're in chat view
    if (activeView === 'chat' && messagesEndRef.current) {
      // If force is true, scroll immediately, otherwise use the normal delayed scroll
      if (force) {
        messagesEndRef.current?.scrollIntoView({
          behavior: 'smooth',
          block: 'end'
        });
      } else {
        // Only auto-scroll for new AI responses, not when user submits a question
        const timeoutId = setTimeout(() => {
          if (messagesEndRef.current) {
            messagesEndRef.current.scrollIntoView({
              behavior: 'smooth',
              block: 'end'
            });
          }
        }, 300);

        // Store the timeout ID for cleanup
        return timeoutId;
      }
    }
    return null;
  };

  const handleImageUpload = (file) => {
    try {
      if (!file) return;

      // Validate file size
      if (file.size > MAX_IMAGE_SIZE) {
        throw new Error('Image must be less than 5MB');
      }

      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        throw new Error('Only JPEG, PNG and WebP images are supported');
      }

      setImage(file);
    } catch (err) {
      console.error('Error handling image upload:', err);
      setImage(null);
    }
  };

  const handleHistoryQuestionClick = (question) => {
    setQuestion(question);
  };

  // Group conversations by date periods
  const groupConversationsByDate = (conversations) => {
    if (!conversations || conversations.length === 0) return {};

    const grouped = {
      today: [],
      yesterday: [],
      thisWeek: [],
      thisMonth: [],
      older: []
    };

    conversations.forEach(item => {
      const date = new Date(item.created_at);

      if (isToday(date)) {
        grouped.today.push(item);
      } else if (isYesterday(date)) {
        grouped.yesterday.push(item);
      } else if (isSameWeek(date, new Date(), { weekStartsOn: 1 })) {
        grouped.thisWeek.push(item);
      } else if (isSameMonth(date, new Date())) {
        grouped.thisMonth.push(item);
      } else {
        grouped.older.push(item);
      }
    });

    return grouped;
  };

  // Load a specific conversation
  const loadConversation = (conversationId) => {
    // Find the conversation in history
    const conversation = history.find(item => item.id === conversationId);
    if (conversation) {
      setDisplayedHistory([conversation]);
      setSelectedConversationDate(conversation.created_at);
      setShowHistorySidebar(false);
    }
  };

  // Start a new conversation
  const startNewConversation = () => {
    setCurrentConversation([]);
    setDisplayedHistory([]);
    setSelectedConversationDate(null);
    setQuestion('');
    setShowHistorySidebar(false);
  };

  useEffect(() => {
    if (user) {
      fetchHistory();
    }
  }, [user, fetchHistory]);

  // Update grouped history whenever history changes
  useEffect(() => {
    if (history && history.length > 0) {
      setGroupedHistory(groupConversationsByDate(history));
    }
  }, [history]);

  // Update displayed history whenever the real history changes
  useEffect(() => {
    if (history && !selectedConversationDate) {
      setDisplayedHistory(history);
    }
  }, [history, selectedConversationDate]);

  // Add current conversation to displayed history when response is received
  useEffect(() => {
    // Only add to conversation if we have a response AND a lastSubmittedQuestion
    // This prevents adding responses without explicit user questions
    if (response && lastSubmittedQuestion && !selectedConversationDate) {
      // Get the submitted question from useChat
      const questionToShow = lastSubmittedQuestion;

      // Add new message pair to current conversation
      const newMessage = {
        question: questionToShow,
        response,
        image_url: image ? URL.createObjectURL(image) : null,
        timestamp: new Date().toISOString()
      };

      // Check if this exact message is already in the conversation to prevent duplicates
      const isDuplicate = currentConversation.some(msg =>
        msg.question === questionToShow && msg.response === response
      );

      if (!isDuplicate) {
        setCurrentConversation(prev => [...prev, newMessage]);
      }

      // Reset image after it's been added to the conversation
      if (image) {
        setImage(null);
      }
    }
  }, [response, image, selectedConversationDate, lastSubmittedQuestion, setImage, currentConversation]);

  // Scroll to bottom when messages update, but only in chat view
  useEffect(() => {
    // Only scroll if:
    // 1. We're in chat view
    // 2. We have a new response (but not when we're just loading)
    // 3. We're not in a selected conversation (history view)
    let timeoutId = null;
    if (activeView === 'chat' && response && !loading && !selectedConversationDate) {
      timeoutId = scrollToBottom();
    }

    // Clean up the timeout when the component unmounts or dependencies change
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [response, loading, activeView, selectedConversationDate]);

  // Format the date for a group header
  const formatGroupHeader = (key) => {
    switch(key) {
      case 'today': return 'Today';
      case 'yesterday': return 'Yesterday';
      case 'thisWeek': return 'This Week';
      case 'thisMonth': return 'This Month';
      case 'older': return 'Older';
      default: return '';
    }
  };

  // Handle voice analysis completion
  const handleVoiceAnalysisComplete = (results) => {
    // Create a structured message with the voice analysis results
    if (results) {
      // Process results based on which version of the voice analysis tool is being used
      let formattedResponse = '';

      if (results.clarity && results.dryMouth && results.jawAlignment) {
        // Format from tools/VoiceAnalysisTool.jsx
        formattedResponse = `
## Voice Analysis Results

### Speech Clarity: ${Math.round(results.clarity.score * 100)}%
${results.clarity.recommendation}

### Oral Moisture: ${Math.round(results.dryMouth.score * 100)}%
${results.dryMouth.recommendation}

### Jaw Alignment: ${Math.round(results.jawAlignment.score * 100)}%
${results.jawAlignment.recommendation}

### Overall Score: ${Math.round(results.overallScore * 100)}%

*This analysis is for educational purposes only. For a comprehensive evaluation, please consult a dental professional.*
        `;
      } else if (results.metrics) {
        // Format from ai-tools/VoiceAnalysisTool.jsx
        const { clarity, breath, pitch, dryness } = results.metrics;

        formattedResponse = `
## Voice Analysis Results

### Speech Clarity: ${clarity ? Math.round(clarity * 100) : 'N/A'}%
${results.recommendations?.find(r => r.includes('articulation') || r.includes('clarity')) || 'Practice clear articulation of consonants and vowels.'}

### Breath Control: ${breath ? Math.round(breath.quality) : 'N/A'}%
${results.recommendations?.find(r => r.includes('breath')) || 'Focus on steady breathing exercises during speech.'}

### Voice Stability: ${pitch ? Math.round(pitch.stability * 100) : 'N/A'}%
${results.recommendations?.find(r => r.includes('stability')) || 'Work on maintaining consistent voice tone and volume.'}

### Oral Moisture: ${dryness ? (dryness.level === 'Normal' ? 'Normal' : 'Low') : 'N/A'}
${results.recommendations?.find(r => r.includes('hydration') || r.includes('moistur')) || 'Maintain proper hydration throughout the day.'}

### Overall Score: ${results.score || Math.round(results.overallScore * 100) || 'N/A'}%

*This analysis is for educational purposes only. For a comprehensive evaluation, please consult a dental professional.*
        `;
      } else {
        // Generic format for any other result structure
        formattedResponse = `
## Voice Analysis Results

${Object.entries(results).map(([key, value]) => {
  if (typeof value === 'object') {
    return `### ${key.charAt(0).toUpperCase() + key.slice(1)}: ${typeof value.score === 'number' ? Math.round(value.score * 100) + '%' : 'Completed'}\n`;
  } else if (typeof value === 'number' && key !== 'timestamp') {
    return `### ${key.charAt(0).toUpperCase() + key.slice(1)}: ${Math.round(value * 100)}%\n`;
  }
  return '';
}).join('\n')}

*This analysis is for educational purposes only. For a comprehensive evaluation, please consult a dental professional.*
        `;
      }

      // Add the results to chat history
      const newItem = {
        id: `voice-analysis-${Date.now()}`,
        question: "I completed a voice analysis test to check for potential dental issues.",
        response: formattedResponse,
        created_at: new Date().toISOString()
      };

      setDisplayedHistory(prev => [newItem, ...prev]);
    }

    // Return to chat interface
    setShowVoiceAnalysisTool(false);

    // Scroll to the new message after a short delay to allow rendering
    const timeoutId = setTimeout(() => {
      scrollToBottom(true);
    }, 100);

    // Store the timeout ID in a ref for cleanup
    return () => {
      clearTimeout(timeoutId);
    };
  };

  // Effect to track question changes and switch to chat
  useEffect(() => {
    // THIS is what was causing the automatic switch to chat when typing
    // Since preventViewSwitch is undefined, we're removing this auto-switching behavior
    // When preventViewSwitch is true, we won't auto-switch views

    // DISABLE THIS FUNCTIONALITY TO FIX THE BUG:
    /*
    if (question.trim() && activeView !== 'chat' && !preventViewSwitch) {
      setActiveView('chat');
    }
    */
  }, [question, activeView]);

  // Global cleanup effect
  useEffect(() => {
    // This effect runs on component mount and cleans up on unmount

    // Create a function to handle cleanup of any resources
    const cleanup = () => {
      // Clean up any blob URLs that might have been created
      if (image && image instanceof File && image.preview) {
        URL.revokeObjectURL(image.preview);
      }

      // Reset state on unmount to prevent memory leaks
      messagesEndRef.current = null;
    };

    // Return the cleanup function
    return cleanup;
  }, [image]);

  return (
    <div className="max-w-5xl mx-auto responsive-container">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4 md:mb-8 bg-gradient-to-r from-gray-800/80 to-slate-800/80 rounded-xl p-3 md:p-5 shadow-lg backdrop-blur-md border border-white/5 relative overflow-hidden">
        {/* Decorative elements */}
        <div className="absolute top-0 right-0 w-64 h-64 bg-blue-500/5 rounded-full blur-3xl -translate-y-1/2 translate-x-1/2"></div>
        <div className="absolute bottom-0 left-0 w-64 h-64 bg-purple-500/5 rounded-full blur-3xl translate-y-1/2 -translate-x-1/2"></div>
        <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-blue-500/20 to-transparent"></div>
        <div className="mb-3 md:mb-0">
          <h1 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-blue-400 via-indigo-300 to-purple-400 text-transparent bg-clip-text">SMILO Dental Assistant</h1>
          <p className="text-white/70 text-xs md:text-sm mt-1">
            Ask me anything about dental health, procedures, or oral care
          </p>
        </div>
        {/* Removing the Chat and Sign In buttons */}
      </div>

      {error && (
        <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-3 md:p-4 mb-4 md:mb-6 shadow-md">
          <p className="text-red-400 text-sm md:text-base">
            <strong>Error:</strong> {error}
          </p>
        </div>
      )}

      <div className="flex flex-col h-full">
        {showVoiceAnalysisTool ? (
           <VoiceAnalysisTool
             onBack={() => setShowVoiceAnalysisTool(false)}
             onAnalysisComplete={handleVoiceAnalysisComplete}
             onResultReady={handleVoiceAnalysisComplete}
           />
        ) : activeView === 'chat' ? (
          /* Chat View */
          <div className="relative flex h-[calc(100vh-12rem)] md:h-[75vh] full-viewport-height">
            {/* History sidebar for logged-in users */}
            {user && (
              <div className={`fixed md:absolute inset-y-0 left-0 w-[85vw] md:w-64 bg-gradient-to-b from-gray-800/90 to-slate-900/90 backdrop-blur-sm border-r border-white/10 z-20 md:z-10 transition-transform duration-300 transform ${showHistorySidebar ? 'translate-x-0' : '-translate-x-full'} md:static md:translate-x-0 md:${showHistorySidebar ? 'block' : 'hidden'} shadow-xl rounded-l-xl overflow-hidden`}>
                <div className="p-4 border-b border-white/10 bg-gradient-to-r from-blue-900/30 to-indigo-900/30">
                  <div className="flex items-center justify-between">
                    <h3 className="text-white font-medium flex items-center gap-2">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      Chat History
                    </h3>
                    <button
                      onClick={() => setShowHistorySidebar(false)}
                      className="text-white/60 hover:text-white md:hidden touch-target-mobile"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                </div>

                <div className="p-3">
                  <button
                    onClick={startNewConversation}
                    className="w-full flex items-center gap-2 p-2 rounded-lg bg-gradient-to-r from-blue-500/20 to-blue-600/20 hover:from-blue-500/30 hover:to-blue-600/30 text-blue-300 text-sm font-medium mb-4 border border-blue-500/30 transition-all duration-300"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    <span>New Conversation</span>
                  </button>
                </div>

                <div className="overflow-y-auto max-h-[calc(75vh-8rem)] px-2 scrollbar-thin scrollbar-thumb-gray-700 scrollbar-track-transparent">
                  {Object.entries(groupedHistory).map(([period, items]) =>
                    items.length > 0 && (
                      <div key={period} className="mb-4">
                        <div className="px-3 py-2 text-xs font-medium text-white/50 uppercase bg-white/5 rounded-md mb-2">
                          {formatGroupHeader(period)}
                        </div>
                        <div className="space-y-1.5">
                          {items.map(item => (
                            <button
                              key={item.id}
                              onClick={() => loadConversation(item.id)}
                              className={`w-full text-left px-3 py-2.5 text-sm rounded-lg transition-colors ${selectedConversationDate === item.created_at ? 'bg-gradient-to-r from-blue-500/30 to-indigo-600/30 border border-blue-500/40 text-white' : 'text-white/70 hover:bg-white/10 hover:text-white border border-transparent'}`}
                            >
                              <div className="line-clamp-1 font-medium">{item.question}</div>
                              <div className="text-xs text-white/40 mt-1 flex items-center gap-1.5">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                {format(new Date(item.created_at), 'MMM d, h:mm a')}
                              </div>
                            </button>
                          ))}
                        </div>
                      </div>
                    )
                  )}
                </div>
              </div>
            )}

            {/* Fixed position overlay when sidebar is open on mobile */}
            {showHistorySidebar && (
              <div
                className="fixed inset-0 bg-black/50 backdrop-blur-sm z-10 md:hidden"
                onClick={() => setShowHistorySidebar(false)}
              ></div>
            )}

            {/* Main chat content */}
            <div className="flex-1 flex flex-col h-full overflow-hidden ml-0 md:ml-4">
              {/* Toggle sidebar button - visible on mobile only */}
              {user && (
                <button
                  onClick={() => setShowHistorySidebar(true)}
                  className="md:hidden absolute top-0 left-0 z-10 m-2 p-2 rounded-full bg-blue-900/30 backdrop-blur-sm border border-white/10 text-blue-300 touch-target-mobile"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                  </svg>
                </button>
              )}

              {/* If we have a selected conversation, show the date */}
              {selectedConversationDate && (
                <div className="bg-indigo-900/40 px-4 py-2 text-center text-sm text-white/70 border-b border-indigo-800/30">
                  Viewing conversation from {format(new Date(selectedConversationDate), 'MMMM d, yyyy h:mm a')}
                  <button
                    onClick={startNewConversation}
                    className="ml-2 underline text-blue-300 hover:text-blue-200"
                  >
                    Return to current chat
                  </button>
                </div>
              )}

              <div className="flex-1 p-6 space-y-6 relative overflow-y-auto" style={{ overflowY: currentConversation.length > 0 ? 'auto' : 'auto', maxHeight: currentConversation.length > 0 ? 'none' : '100%' }}>
                {/* Decorative elements */}
                <div className="absolute top-0 right-0 w-80 h-80 bg-blue-500/5 rounded-full blur-3xl -translate-y-1/2 translate-x-1/2 pointer-events-none"></div>
                <div className="absolute bottom-0 left-0 w-80 h-80 bg-purple-500/5 rounded-full blur-3xl translate-y-1/2 -translate-x-1/2 pointer-events-none"></div>
                <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-blue-500/20 to-transparent pointer-events-none"></div>
                {selectedConversationDate ? (
                  // Show selected history conversation
                  displayedHistory.slice().reverse().map((item, index) => (
                    <div key={item.id || index} className="space-y-4">
                      {/* User message */}
                      <div className="flex items-start gap-3">
                        <div className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-full w-8 h-8 flex items-center justify-center flex-shrink-0 mt-1 shadow-md">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                          </svg>
                        </div>
                        <div className="flex-1">
                          <div className="bg-gradient-to-br from-blue-600/40 to-blue-700/40 rounded-xl p-4 text-white shadow-sm border border-blue-900/40 backdrop-blur-sm relative overflow-hidden group transition-all duration-300 hover:shadow-md hover:shadow-blue-500/10">
                            <div className="absolute inset-0 bg-blue-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                            <div className="relative z-10">{item.question}</div>
                          </div>
                          {item.image_url && (
                            <div className="mt-2">
                              <img
                                src={item.image_url}
                                alt="Uploaded dental image"
                                className="max-w-xs rounded-lg border border-white/10 shadow-md"
                              />
                            </div>
                          )}
                          <div className="text-xs text-white/40 mt-1 ml-2">
                            {format(new Date(item.created_at), 'MMM d, h:mm a')}
                          </div>
                        </div>
                      </div>

                      {/* AI response */}
                      <div className="flex items-start gap-3">
                        <div className="bg-gradient-to-br from-purple-500 to-indigo-600 rounded-full w-8 h-8 flex items-center justify-center flex-shrink-0 mt-1 shadow-md relative">
                          {/* Subtle glow effect */}
                          <div className="absolute inset-0 rounded-full bg-indigo-500 opacity-30 blur-md animate-pulse-slow"></div>
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white relative z-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                          </svg>
                        </div>
                        <div className="flex-1">
                          <div className="bg-gradient-to-br from-indigo-900/40 to-purple-900/30 rounded-xl p-4 text-white prose prose-sm prose-invert max-w-none shadow-sm border border-indigo-900/40 backdrop-blur-sm relative overflow-hidden group transition-all duration-300 hover:shadow-md hover:shadow-indigo-500/10">
                            <div className="absolute inset-0 bg-indigo-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                            <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-indigo-500/20 to-transparent"></div>
                            <div className="relative z-10">{item.response}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                ) : currentConversation.length > 0 ? (
                  // Show current conversation
                  <>
                    {currentConversation.map((message, index) => (
                      <div key={index} className="space-y-4">
                        {/* User message */}
                        <div className="flex items-start gap-3">
                          <div className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-full w-8 h-8 flex items-center justify-center flex-shrink-0 mt-1 shadow-md relative">
                            <div className="absolute inset-0 rounded-full bg-blue-500 opacity-20 blur-sm"></div>
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white relative z-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                          </div>
                          <div className="flex-1">
                            <div className="bg-gradient-to-br from-blue-600/40 to-blue-700/40 rounded-xl p-4 text-white shadow-sm border border-blue-900/40 backdrop-blur-sm relative overflow-hidden group transition-all duration-300 hover:shadow-md hover:shadow-blue-500/10">
                              <div className="absolute inset-0 bg-blue-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                              <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-blue-500/20 to-transparent"></div>
                              <div className="relative z-10">{message.question}</div>
                            </div>
                            {message.image_url && (
                              <div className="mt-2">
                                <img
                                  src={message.image_url}
                                  alt="Uploaded dental image"
                                  className="max-w-xs rounded-lg border border-white/10 shadow-md"
                                />
                              </div>
                            )}
                            <div className="text-xs text-white/40 mt-1 ml-2">
                              {format(new Date(message.timestamp), 'MMM d, h:mm a')}
                            </div>
                          </div>
                        </div>

                        {/* AI response */}
                        <div className="flex items-start gap-3">
                          <div className="bg-gradient-to-br from-purple-500 to-indigo-600 rounded-full w-8 h-8 flex items-center justify-center flex-shrink-0 mt-1 shadow-md relative">
                            {/* Subtle glow effect */}
                            <div className="absolute inset-0 rounded-full bg-indigo-500 opacity-30 blur-md animate-pulse-slow"></div>
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white relative z-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                            </svg>
                          </div>
                          <div className="flex-1">
                            <div className="bg-gradient-to-br from-indigo-900/40 to-purple-900/30 rounded-xl p-4 text-white prose prose-sm prose-invert max-w-none shadow-sm border border-indigo-900/40 backdrop-blur-sm relative overflow-hidden group transition-all duration-300 hover:shadow-md hover:shadow-indigo-500/10">
                              <div className="absolute inset-0 bg-indigo-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                              <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-indigo-500/20 to-transparent"></div>
                              <div className="relative z-10">{message.response}</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}

                    {/* Typing indicator for new questions */}
                    {loading && lastSubmittedQuestion && !currentConversation.find(msg => msg.question === lastSubmittedQuestion) && (
                      <>
                        {/* User message for current question */}
                        <div className="flex items-start gap-3">
                          <div className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-full w-8 h-8 flex items-center justify-center flex-shrink-0 mt-1 shadow-md">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                          </div>
                          <div className="flex-1">
                            <div className="bg-blue-900/30 rounded-xl p-4 text-white shadow-sm border border-blue-900/40">
                              {lastSubmittedQuestion}
                            </div>
                            {image && (
                              <div className="mt-2">
                                <img
                                  src={URL.createObjectURL(image)}
                                  alt="Uploaded dental image"
                                  className="max-w-xs rounded-lg border border-white/10 shadow-md"
                                />
                              </div>
                            )}
                          </div>
                        </div>

                        {/* AI typing response */}
                        <div className="flex items-start gap-3 mt-3 animate-fade-in">
                          <div className="bg-gradient-to-br from-purple-500 to-indigo-600 rounded-full w-8 h-8 flex items-center justify-center flex-shrink-0 mt-1 shadow-md">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                            </svg>
                          </div>
                          <div className="flex-1">
                            <div className="bg-purple-900/40 rounded-xl p-4 text-white shadow-sm border border-purple-900/40 relative">
                              <div className="flex items-center">
                                <div className="typing-indicator">
                                  <span></span>
                                  <span></span>
                                  <span></span>
                                </div>
                              </div>
                              {!config?.openai?.apiKey && (
                                <div className="text-yellow-300 text-xs mt-2 bg-yellow-900/30 p-2 rounded">
                                  <strong>NOTE:</strong> Running in demo mode without an API key. Add your OpenAI API key in the .env file to receive real AI responses.
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </>
                    )}
                  </>
                ) : (
                  // Welcome screen for empty conversation
                  <div className="text-center py-10 relative">
                    {/* Decorative elements */}
                    <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-blue-500/5 rounded-full blur-3xl"></div>
                    <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-purple-500/5 rounded-full blur-3xl"></div>

                    <div className="relative z-10">
                      <div className="w-24 h-24 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-6 shadow-xl relative overflow-hidden">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-white/90 relative z-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                        </svg>
                      </div>
                    </div>

                    <motion.h3
                      initial={{ y: 20, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ duration: 0.5, delay: 0.2 }}
                      className="text-2xl font-semibold text-white mb-3 bg-gradient-to-r from-blue-400 via-indigo-300 to-purple-400 text-transparent bg-clip-text relative z-10"
                    >
                      Welcome to SMILO Dental Assistant
                    </motion.h3>
                    <motion.p
                      initial={{ y: 20, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ duration: 0.5, delay: 0.3 }}
                      className="text-white/70 max-w-md mx-auto mb-8 relative z-10"
                    >
                      Ask me anything about dental health, procedures, or oral care to get started.
                    </motion.p>
                    <motion.div
                      initial={{ y: 20, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ duration: 0.5, delay: 0.4 }}
                      className="flex justify-center gap-4 relative z-10">
                      <div className="relative max-w-md w-full px-4">
                        <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-xl blur-xl"></div>
                        <div className="relative bg-white/5 backdrop-blur-sm rounded-xl p-4 border border-white/10">
                          <ul className="text-left space-y-2">
                            <li className="flex items-start gap-2">
                              <span className="text-blue-400 text-lg leading-tight">→</span>
                              <span className="text-white/80 text-sm">How can I prevent cavities and tooth decay?</span>
                            </li>
                            <li className="flex items-start gap-2">
                              <span className="text-blue-400 text-lg leading-tight">→</span>
                              <span className="text-white/80 text-sm">What should I do for a dental emergency like a knocked-out tooth?</span>
                            </li>
                            <li className="flex items-start gap-2">
                              <span className="text-blue-400 text-lg leading-tight">→</span>
                              <span className="text-white/80 text-sm">What are the different types of dental implants and which is best?</span>
                            </li>
                          </ul>
                        </div>
                      </div>
                    </motion.div>
                    <motion.div
                      initial={{ y: 20, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ duration: 0.5, delay: 0.5 }}
                      className="mt-8 flex flex-col sm:flex-row items-center justify-center gap-4 relative z-10">
                      <button
                        onClick={() => setActiveView('resources')}
                        className="text-blue-300 hover:text-blue-200 transition-colors text-sm flex items-center gap-2 opacity-0 invisible"
                      >
                        <span>Browse our Dental Resources</span>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                        </svg>
                      </button>

                      <button
                        onClick={() => setShowVoiceAnalysisTool(true)}
                        className="text-purple-300 hover:text-purple-200 transition-colors text-sm flex items-center gap-2 relative group opacity-0 invisible"
                      >
                        <div className="absolute -inset-2 bg-purple-500/10 rounded-lg scale-0 group-hover:scale-100 transition-all duration-300"></div>
                        <span className="relative">Try Voice Analysis Tool</span>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 relative" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                        </svg>
                      </button>

                      {user && history.length > 0 && (
                        <button
                          onClick={() => setShowHistorySidebar(true)}
                          className="text-cyan-300 hover:text-cyan-200 transition-colors text-sm flex items-center gap-2 bg-white/5 hover:bg-white/10 rounded-full px-4 py-2 shadow-inner"
                        >
                          <span>View your chat history</span>
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        </button>
                      )}
                    </motion.div>
                  </div>
                )}

                {/* Only place the scroll marker at the end of content */}
                <div ref={messagesEndRef} className="h-4" />
              </div>

              <div className="border-t border-white/10 p-5 bg-slate-900/50">
                <div className="flex items-center gap-2 mb-2">
                  <div className="flex-grow relative z-10">
                    <div className="absolute -inset-1 bg-gradient-to-r from-blue-500/20 via-indigo-500/20 to-purple-500/20 rounded-lg blur-md opacity-80"></div>
                    <div className="relative">
                      <QuestionForm
                        question={question}
                        setQuestion={setQuestion}
                        placeholder="Ask SMILO a dental question..."
                        image={image}
                        onImageUpload={handleImageUpload}
                        loading={loading}
                        onSubmit={handleSubmit}
                        disabled={!!selectedConversationDate}
                        preventViewSwitch={true}
                      />
                    </div>
                  </div>

                  <button
                    onClick={() => setShowVoiceAnalysisTool(true)}
                    className="relative bg-gradient-to-r from-purple-500 to-indigo-600 hover:from-purple-600 hover:to-indigo-700 p-2.5 rounded-lg text-white transition-all duration-300 shadow-lg hover:shadow-purple-400/20 opacity-0 invisible"
                    title="Voice Analysis Tool"
                  >
                    <div className="absolute inset-0 rounded-lg opacity-0 hover:opacity-100 transition-opacity duration-300">
                      <div className="absolute inset-0 bg-white/10 rounded-lg animate-pulse"></div>
                    </div>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                    </svg>
                  </button>
                </div>

                {selectedConversationDate && (
                  <div className="text-center mt-3 text-white/60 text-xs bg-indigo-900/20 py-2 px-4 rounded-lg">
                    This is a past conversation. <button onClick={startNewConversation} className="text-blue-400 hover:text-blue-300 underline">Start a new conversation</button> to ask questions.
                  </div>
                )}
              </div>
            </div>
          </div>
        ) : activeView === 'resources' ? (
          /* Resources View */
          <DentalResources />
        ) : (
          /* Default to chat view if no other view matches */
          <div className="relative flex h-[calc(100vh-12rem)] md:h-[75vh] full-viewport-height">
            {/* History sidebar for logged-in users */}
            {user && (
              <div className={`fixed md:absolute inset-y-0 left-0 w-[85vw] md:w-64 bg-gradient-to-b from-gray-800/90 to-slate-900/90 backdrop-blur-sm border-r border-white/10 z-20 md:z-10 transition-transform duration-300 transform ${showHistorySidebar ? 'translate-x-0' : '-translate-x-full'} md:static md:translate-x-0 md:${showHistorySidebar ? 'block' : 'hidden'} shadow-xl rounded-l-xl overflow-hidden`}>
                <div className="p-4 border-b border-white/10 bg-gradient-to-r from-blue-900/30 to-indigo-900/30">
                  <div className="flex items-center justify-between">
                    <h3 className="text-white font-medium flex items-center gap-2">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      Chat History
                    </h3>
                    <button
                      onClick={() => setShowHistorySidebar(false)}
                      className="text-white/60 hover:text-white md:hidden touch-target-mobile"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                </button>
              </div>
                </div>

                <div className="p-3">
                  <button
                    onClick={startNewConversation}
                    className="w-full flex items-center gap-2 p-2 rounded-lg bg-gradient-to-r from-blue-500/20 to-blue-600/20 hover:from-blue-500/30 hover:to-blue-600/30 text-blue-300 text-sm font-medium mb-4 border border-blue-500/30 transition-all duration-300"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    <span>New Conversation</span>
                </button>
              </div>

                <div className="overflow-y-auto max-h-[calc(75vh-8rem)] px-2 scrollbar-thin scrollbar-thumb-gray-700 scrollbar-track-transparent">
                  {Object.entries(groupedHistory).map(([period, items]) =>
                    items.length > 0 && (
                      <div key={period} className="mb-4">
                        <div className="px-3 py-2 text-xs font-medium text-white/50 uppercase bg-white/5 rounded-md mb-2">
                          {formatGroupHeader(period)}
                </div>
                        <div className="space-y-1.5">
                          {items.map(item => (
                            <button
                              key={item.id}
                              onClick={() => loadConversation(item.id)}
                              className={`w-full text-left px-3 py-2.5 text-sm rounded-lg transition-colors ${selectedConversationDate === item.created_at ? 'bg-gradient-to-r from-blue-500/30 to-indigo-600/30 border border-blue-500/40 text-white' : 'text-white/70 hover:bg-white/10 hover:text-white border border-transparent'}`}
                            >
                              <div className="line-clamp-1 font-medium">{item.question}</div>
                              <div className="text-xs text-white/40 mt-1 flex items-center gap-1.5">
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                {format(new Date(item.created_at), 'MMM d, h:mm a')}
                              </div>
                </button>
                          ))}
              </div>
            </div>
                    )
                  )}
                </div>
              </div>
            )}

            {/* Fixed position overlay when sidebar is open on mobile */}
            {showHistorySidebar && (
              <div
                className="fixed inset-0 bg-black/50 backdrop-blur-sm z-10 md:hidden"
                onClick={() => setShowHistorySidebar(false)}
              ></div>
            )}

            {/* Main chat content */}
            <div className="flex-1 flex flex-col h-full overflow-hidden ml-0 md:ml-4">
              {/* Toggle sidebar button - visible on mobile only */}
              {user && (
                <button
                  onClick={() => setShowHistorySidebar(true)}
                  className="md:hidden absolute top-0 left-0 z-10 m-2 p-2 rounded-full bg-blue-900/30 backdrop-blur-sm border border-white/10 text-blue-300 touch-target-mobile"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                </button>
              )}

              {/* If we have a selected conversation, show the date */}
              {selectedConversationDate && (
                <div className="bg-indigo-900/40 px-4 py-2 text-center text-sm text-white/70 border-b border-indigo-800/30">
                  Viewing conversation from {format(new Date(selectedConversationDate), 'MMMM d, yyyy h:mm a')}
                  <button
                    onClick={startNewConversation}
                    className="ml-2 underline text-blue-300 hover:text-blue-200"
                  >
                    Return to current chat
                  </button>
                  </div>
              )}

              <div className="flex-1 p-6 space-y-6 relative overflow-y-auto" style={{ overflowY: currentConversation.length > 0 ? 'auto' : 'auto', maxHeight: currentConversation.length > 0 ? 'none' : '100%' }}>
                {/* Decorative elements */}
                <div className="absolute top-0 right-0 w-80 h-80 bg-blue-500/5 rounded-full blur-3xl -translate-y-1/2 translate-x-1/2 pointer-events-none"></div>
                <div className="absolute bottom-0 left-0 w-80 h-80 bg-purple-500/5 rounded-full blur-3xl translate-y-1/2 -translate-x-1/2 pointer-events-none"></div>
                <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-blue-500/20 to-transparent pointer-events-none"></div>
                {selectedConversationDate ? (
                  // Show selected history conversation
                  displayedHistory.slice().reverse().map((item, index) => (
                    <div key={item.id || index} className="space-y-4">
                      {/* User message */}
                      <div className="flex items-start gap-3">
                        <div className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-full w-8 h-8 flex items-center justify-center flex-shrink-0 mt-1 shadow-md">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                          </svg>
                        </div>
                        <div className="flex-1">
                          <div className="bg-gradient-to-br from-blue-600/40 to-blue-700/40 rounded-xl p-4 text-white shadow-sm border border-blue-900/40 backdrop-blur-sm relative overflow-hidden group transition-all duration-300 hover:shadow-md hover:shadow-blue-500/10">
                            <div className="absolute inset-0 bg-blue-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                            <div className="relative z-10">{item.question}</div>
                          </div>
                          {item.image_url && (
                            <div className="mt-2">
                              <img
                                src={item.image_url}
                                alt="Uploaded dental image"
                                className="max-w-xs rounded-lg border border-white/10 shadow-md"
                              />
                            </div>
                          )}
                          <div className="text-xs text-white/40 mt-1 ml-2">
                            {format(new Date(item.created_at), 'MMM d, h:mm a')}
                          </div>
                  </div>
                </div>

                      {/* AI response */}
                      <div className="flex items-start gap-3">
                        <div className="bg-gradient-to-br from-purple-500 to-indigo-600 rounded-full w-8 h-8 flex items-center justify-center flex-shrink-0 mt-1 shadow-md relative">
                          {/* Subtle glow effect */}
                          <div className="absolute inset-0 rounded-full bg-indigo-500 opacity-30 blur-md animate-pulse-slow"></div>
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white relative z-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                          </svg>
                        </div>
                        <div className="flex-1">
                          <div className="bg-gradient-to-br from-indigo-900/40 to-purple-900/30 rounded-xl p-4 text-white prose prose-sm prose-invert max-w-none shadow-sm border border-indigo-900/40 backdrop-blur-sm relative overflow-hidden group transition-all duration-300 hover:shadow-md hover:shadow-indigo-500/10">
                            <div className="absolute inset-0 bg-indigo-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                            <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-indigo-500/20 to-transparent"></div>
                            <div className="relative z-10">{item.response}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                ) : currentConversation.length > 0 ? (
                  // Show current conversation
                  <>
                    {currentConversation.map((message, index) => (
                      <div key={index} className="space-y-4">
                        {/* User message */}
                        <div className="flex items-start gap-3">
                          <div className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-full w-8 h-8 flex items-center justify-center flex-shrink-0 mt-1 shadow-md relative">
                            <div className="absolute inset-0 rounded-full bg-blue-500 opacity-20 blur-sm"></div>
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white relative z-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                          </div>
                          <div className="flex-1">
                            <div className="bg-gradient-to-br from-blue-600/40 to-blue-700/40 rounded-xl p-4 text-white shadow-sm border border-blue-900/40 backdrop-blur-sm relative overflow-hidden group transition-all duration-300 hover:shadow-md hover:shadow-blue-500/10">
                              <div className="absolute inset-0 bg-blue-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                              <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-blue-500/20 to-transparent"></div>
                              <div className="relative z-10">{message.question}</div>
                            </div>
                            {message.image_url && (
                              <div className="mt-2">
                                <img
                                  src={message.image_url}
                                  alt="Uploaded dental image"
                                  className="max-w-xs rounded-lg border border-white/10 shadow-md"
                                />
                              </div>
                            )}
                            <div className="text-xs text-white/40 mt-1 ml-2">
                              {format(new Date(message.timestamp), 'MMM d, h:mm a')}
                            </div>
                  </div>
                </div>

                        {/* AI response */}
                        <div className="flex items-start gap-3">
                          <div className="bg-gradient-to-br from-purple-500 to-indigo-600 rounded-full w-8 h-8 flex items-center justify-center flex-shrink-0 mt-1 shadow-md relative">
                            {/* Subtle glow effect */}
                            <div className="absolute inset-0 rounded-full bg-indigo-500 opacity-30 blur-md animate-pulse-slow"></div>
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white relative z-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                    </svg>
                  </div>
                          <div className="flex-1">
                            <div className="bg-gradient-to-br from-indigo-900/40 to-purple-900/30 rounded-xl p-4 text-white prose prose-sm prose-invert max-w-none shadow-sm border border-indigo-900/40 backdrop-blur-sm relative overflow-hidden group transition-all duration-300 hover:shadow-md hover:shadow-indigo-500/10">
                              <div className="absolute inset-0 bg-indigo-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                              <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-indigo-500/20 to-transparent"></div>
                              <div className="relative z-10">{message.response}</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}

                    {/* Typing indicator for new questions */}
                    {loading && lastSubmittedQuestion && !currentConversation.find(msg => msg.question === lastSubmittedQuestion) && (
                      <>
                        {/* User message for current question */}
                        <div className="flex items-start gap-3">
                          <div className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-full w-8 h-8 flex items-center justify-center flex-shrink-0 mt-1 shadow-md">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                          <div className="flex-1">
                            <div className="bg-blue-900/30 rounded-xl p-4 text-white shadow-sm border border-blue-900/40">
                              {lastSubmittedQuestion}
                  </div>
                            {image && (
                              <div className="mt-2">
                                <img
                                  src={URL.createObjectURL(image)}
                                  alt="Uploaded dental image"
                                  className="max-w-xs rounded-lg border border-white/10 shadow-md"
                                />
                </div>
                            )}
              </div>
            </div>

                        {/* AI typing response */}
                        <div className="flex items-start gap-3 mt-3 animate-fade-in">
                          <div className="bg-gradient-to-br from-purple-500 to-indigo-600 rounded-full w-8 h-8 flex items-center justify-center flex-shrink-0 mt-1 shadow-md">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                            </svg>
                          </div>
                          <div className="flex-1">
                            <div className="bg-purple-900/40 rounded-xl p-4 text-white shadow-sm border border-purple-900/40 relative">
                              <div className="flex items-center">
                                <div className="typing-indicator">
                                  <span></span>
                                  <span></span>
                                  <span></span>
                                </div>
                              </div>
                              {!config?.openai?.apiKey && (
                                <div className="text-yellow-300 text-xs mt-2 bg-yellow-900/30 p-2 rounded">
                                  <strong>NOTE:</strong> Running in demo mode without an API key. Add your OpenAI API key in the .env file to receive real AI responses.
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </>
                    )}
                  </>
                ) : (
                  // Welcome screen for empty conversation
                  <div className="text-center py-10 relative">
                    {/* Decorative elements */}
                    <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-blue-500/5 rounded-full blur-3xl"></div>
                    <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-purple-500/5 rounded-full blur-3xl"></div>

                    <div className="relative z-10">
                      <div className="w-24 h-24 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-6 shadow-xl relative overflow-hidden">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-white/90 relative z-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                        </svg>
                      </div>
                    </div>

                    <motion.h3
                      initial={{ y: 20, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ duration: 0.5, delay: 0.2 }}
                      className="text-2xl font-semibold text-white mb-3 bg-gradient-to-r from-blue-400 via-indigo-300 to-purple-400 text-transparent bg-clip-text relative z-10"
                    >
                      Welcome to SMILO Dental Assistant
                    </motion.h3>
                    <motion.p
                      initial={{ y: 20, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ duration: 0.5, delay: 0.3 }}
                      className="text-white/70 max-w-md mx-auto mb-8 relative z-10"
                    >
                      Ask me anything about dental health, procedures, or oral care to get started.
                    </motion.p>
                    <motion.div
                      initial={{ y: 20, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ duration: 0.5, delay: 0.4 }}
                      className="flex justify-center gap-4 relative z-10">
                      <div className="relative max-w-md w-full px-4">
                        <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-xl blur-xl"></div>
                        <div className="relative bg-white/5 backdrop-blur-sm rounded-xl p-4 border border-white/10">
                          <ul className="text-left space-y-2">
                            <li className="flex items-start gap-2">
                              <span className="text-blue-400 text-lg leading-tight">→</span>
                              <span className="text-white/80 text-sm">How can I prevent cavities and tooth decay?</span>
                            </li>
                            <li className="flex items-start gap-2">
                              <span className="text-blue-400 text-lg leading-tight">→</span>
                              <span className="text-white/80 text-sm">What should I do for a dental emergency like a knocked-out tooth?</span>
                            </li>
                            <li className="flex items-start gap-2">
                              <span className="text-blue-400 text-lg leading-tight">→</span>
                              <span className="text-white/80 text-sm">What are the different types of dental implants and which is best?</span>
                            </li>
                          </ul>
                        </div>
                      </div>
                    </motion.div>
                    <motion.div
                      initial={{ y: 20, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ duration: 0.5, delay: 0.5 }}
                      className="mt-8 flex flex-col sm:flex-row items-center justify-center gap-4 relative z-10">
              <button
                        onClick={() => setActiveView('resources')}
                        className="text-blue-300 hover:text-blue-200 transition-colors text-sm flex items-center gap-2 opacity-0 invisible"
              >
                        <span>Browse our Dental Resources</span>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </svg>
              </button>

                      <button
                        onClick={() => setShowVoiceAnalysisTool(true)}
                        className="text-purple-300 hover:text-purple-200 transition-colors text-sm flex items-center gap-2 relative group opacity-0 invisible"
                      >
                        <div className="absolute -inset-2 bg-purple-500/10 rounded-lg scale-0 group-hover:scale-100 transition-all duration-300"></div>
                        <span className="relative">Try Voice Analysis Tool</span>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 relative" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                        </svg>
                      </button>

                      {user && history.length > 0 && (
                        <button
                          onClick={() => setShowHistorySidebar(true)}
                          className="text-cyan-300 hover:text-cyan-200 transition-colors text-sm flex items-center gap-2 bg-white/5 hover:bg-white/10 rounded-full px-4 py-2 shadow-inner"
                        >
                          <span>View your chat history</span>
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        </button>
                      )}
                    </motion.div>
                  </div>
                )}

                {/* Only place the scroll marker at the end of content */}
                <div ref={messagesEndRef} className="h-4" />
              </div>

              <div className="border-t border-white/10 p-5 bg-slate-900/50">
                <div className="flex items-center gap-2 mb-2">
                  <div className="flex-grow relative z-10">
                    <div className="absolute -inset-1 bg-gradient-to-r from-blue-500/20 via-indigo-500/20 to-purple-500/20 rounded-lg blur-md opacity-80"></div>
                    <div className="relative">
                      <QuestionForm
                        question={question}
                        setQuestion={setQuestion}
                        placeholder="Ask SMILO a dental question..."
                        image={image}
                        onImageUpload={handleImageUpload}
                        loading={loading}
                        onSubmit={handleSubmit}
                        disabled={!!selectedConversationDate}
                        preventViewSwitch={true}
                      />
                    </div>
                  </div>

                  <button
                    onClick={() => setShowVoiceAnalysisTool(true)}
                    className="relative bg-gradient-to-r from-purple-500 to-indigo-600 hover:from-purple-600 hover:to-indigo-700 p-2.5 rounded-lg text-white transition-all duration-300 shadow-lg hover:shadow-purple-400/20 opacity-0 invisible"
                    title="Voice Analysis Tool"
                  >
                    <div className="absolute inset-0 rounded-lg opacity-0 hover:opacity-100 transition-opacity duration-300">
                      <div className="absolute inset-0 bg-white/10 rounded-lg animate-pulse"></div>
                    </div>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                    </svg>
                  </button>
                </div>

                {selectedConversationDate && (
                  <div className="text-center mt-3 text-white/60 text-xs bg-indigo-900/20 py-2 px-4 rounded-lg">
                    This is a past conversation. <button onClick={startNewConversation} className="text-blue-400 hover:text-blue-300 underline">Start a new conversation</button> to ask questions.
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}