import React from 'react';
import { format } from 'date-fns';

export default function ChatHistory({ history, onQuestionClick }) {
  if (!history.length) return null;

  return (
    <div className="mt-8">
      <h2 className="text-xl font-semibold text-white mb-4">Chat History</h2>
      <div className="space-y-4">
        {history.map((item, index) => (
          <div 
            key={index} 
            className="bg-white/5 backdrop-blur-sm rounded-lg p-4 border border-white/10 hover:bg-white/10 transition-colors cursor-pointer"
            onClick={() => onQuestionClick?.(item.question)}
          >
            <div className="text-sm text-white/60 mb-1">
              {format(new Date(item.created_at), 'MMM d, yyyy h:mm a')}
            </div>
            <div className="font-medium text-white">{item.question}</div>
            <div className="mt-2 text-white/80 line-clamp-2">{item.response}</div>
            {item.image_url && (
              <div className="mt-2">
                <img 
                  src={item.image_url} 
                  alt="Uploaded dental image" 
                  className="w-20 h-20 object-cover rounded-lg"
                />
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}