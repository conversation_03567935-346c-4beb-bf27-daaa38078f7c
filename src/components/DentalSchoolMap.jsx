import React, { useRef, useEffect, useState, useCallback } from 'react';
import { loadGoogleMapsScript } from '../lib/services/googleMapsService';

const DEFAULT_CENTER = { lat: 39.8283, lng: -98.5795 }; // Center of US
const DEFAULT_ZOOM = 4;

// Enhanced map styles to better match the website's design flow
const MAP_STYLES = [
  {
    "featureType": "water",
    "elementType": "geometry",
    "stylers": [
      { "color": "#1e293b" }, // Darker blue to match site theme
      { "lightness": -5 }
    ]
  },
  {
    "featureType": "landscape",
    "elementType": "geometry",
    "stylers": [
      { "color": "#0f172a" } // Darker background to match site theme
    ]
  },
  {
    "featureType": "road",
    "elementType": "geometry",
    "stylers": [
      { "color": "#334155" }, // Slate color for roads
      { "lightness": -10 }
    ]
  },
  {
    "featureType": "poi",
    "elementType": "geometry",
    "stylers": [
      { "color": "#1e293b" },
      { "lightness": -15 }
    ]
  },
  {
    "featureType": "administrative",
    "elementType": "geometry.stroke",
    "stylers": [
      { "color": "#6366f1" }, // Indigo color for borders
      { "lightness": 15 },
      { "weight": 1.2 }
    ]
  },
  // ENHANCED TEXT STYLING FOR BETTER READABILITY
  // Road labels - significantly improved contrast
  {
    "featureType": "road",
    "elementType": "labels.text.fill",
    "stylers": [
      { "color": "#e2e8f0" }, // Very light color for maximum readability
      { "lightness": 5 }
    ]
  },
  {
    "featureType": "road",
    "elementType": "labels.text.stroke",
    "stylers": [
      { "color": "#0f172a" },
      { "lightness": -15 },
      { "weight": 3 } // Thicker text stroke for better contrast
    ]
  },
  // Make major roads more prominent
  {
    "featureType": "road.highway",
    "elementType": "labels.text.fill",
    "stylers": [
      { "color": "#f8fafc" }, // Brighter text for highways
      { "lightness": 10 }
    ]
  },
  {
    "featureType": "road.highway",
    "elementType": "labels.text.stroke",
    "stylers": [
      { "color": "#0f172a" },
      { "weight": 4 } // Even thicker outline for highway text
    ]
  },
  // Administrative labels (cities, countries)
  {
    "featureType": "administrative",
    "elementType": "labels.text.fill",
    "stylers": [
      { "color": "#f1f5f9" }, // Very bright text for admin labels
      { "lightness": 15 }
    ]
  },
  {
    "featureType": "administrative",
    "elementType": "labels.text.stroke",
    "stylers": [
      { "color": "#0f172a" },
      { "weight": 4 },
      { "lightness": -15 }
    ]
  },
  // Make locality (city) names extra prominent
  {
    "featureType": "administrative.locality",
    "elementType": "labels.text.fill",
    "stylers": [
      { "color": "#ffffff" },
      { "lightness": 0 }
    ]
  },
  {
    "featureType": "administrative.locality",
    "elementType": "labels.text.stroke",
    "stylers": [
      { "color": "#0f172a" },
      { "weight": 5 }
    ]
  },
  // Point of interest labels
  {
    "featureType": "poi",
    "elementType": "labels.text.fill",
    "stylers": [
      { "color": "#e0e7ff" }, // Bright indigo tinted text
      { "lightness": 5 }
    ]
  },
  {
    "featureType": "poi",
    "elementType": "labels.text.stroke",
    "stylers": [
      { "color": "#0f172a" },
      { "weight": 3 }
    ]
  },
  // Water labels
  {
    "featureType": "water",
    "elementType": "labels.text.fill",
    "stylers": [
      { "color": "#e0e7ff" }, // Bright text for water features
      { "lightness": 10 }
    ]
  },
  {
    "featureType": "water",
    "elementType": "labels.text.stroke",
    "stylers": [
      { "color": "#0f172a" },
      { "weight": 3 }
    ]
  },
  // Transit labels
  {
    "featureType": "transit",
    "elementType": "labels.text.fill",
    "stylers": [
      { "color": "#e0e7ff" },
      { "lightness": 5 }
    ]
  },
  {
    "featureType": "transit",
    "elementType": "labels.text.stroke",
    "stylers": [
      { "color": "#0f172a" },
      { "weight": 3 }
    ]
  },
  // Increase size for all text to improve readability
  {
    "elementType": "labels.text",
    "stylers": [
      { "size": 1.5 } // Increased from 1.2 for better readability
    ]
  }
];

const isValidCoords = (coords) => {
  if (!coords) return false;
  const lat = parseFloat(coords.lat ?? coords.latitude);
  const lng = parseFloat(coords.lng ?? coords.longitude);
  return !isNaN(lat) && !isNaN(lng) && isFinite(lat) && isFinite(lng);
};

export default function DentalSchoolMap({ schools = [], userLocation }) {
  const mapRef = useRef(null);
  const searchInputRef = useRef(null);
  const [map, setMap] = useState(null);
  const [error, setError] = useState(null);
  const [markers, setMarkers] = useState([]);
  const [filteredSchools, setFilteredSchools] = useState(schools);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeSchool, setActiveSchool] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [zipCode, setZipCode] = useState(null);
  const [showLocationPrompt, setShowLocationPrompt] = useState(false);
  const [locationStatus, setLocationStatus] = useState(null); // 'granted', 'denied', 'prompt'
  const infoWindowRef = useRef(null);
  const googleRef = useRef(null);
  const autocompleteRef = useRef(null);

  // Immediately request location permission on component mount
  useEffect(() => {
    // Pre-request location permission when component mounts
    if (navigator.permissions && navigator.permissions.query) {
      navigator.permissions.query({ name: 'geolocation' }).then(result => {
        setLocationStatus(result.state);
        // If permission is already granted, get location immediately
        if (result.state === 'granted' && !isValidCoords(userLocation)) {
          getLocationImmediately();
        }
        
        // Listen for changes to permission state
        result.onchange = () => {
          setLocationStatus(result.state);
          if (result.state === 'granted' && !isValidCoords(userLocation)) {
            getLocationImmediately();
          }
        };
      });
    }
  }, [userLocation]);

  // Function to get location immediately with minimal delay
  const getLocationImmediately = useCallback(() => {
    if (!navigator.geolocation) {
      setError("Your browser doesn't support geolocation.");
      return;
    }

    setIsLoading(true);
    
    navigator.geolocation.getCurrentPosition(
      async (position) => {
        if (!googleRef.current) return; // Guard against unmounted component
        
        const pos = {
          lat: position.coords.latitude,
          lng: position.coords.longitude,
        };
        
        // Immediately set center to user location for instant feedback
        if (map) {
          map.setCenter(pos);
          map.setZoom(13); // Start with closer zoom for better UX
        }
        
        // Get zip code in background - don't block UI
        getZipCodeFromCoords(pos.lat, pos.lng).then(zip => {
          if (zip) setZipCode(zip);
        });
        
        // Create a quicker pulsing effect on location
        if (map) {
          const locationCircle = new googleRef.current.maps.Circle({
            strokeColor: "#6366F1",
            strokeOpacity: 0.8,
            strokeWeight: 2,
            fillColor: "#6366F1",
            fillOpacity: 0.35,
            map: map,
            center: pos,
            radius: 300,
          });
          
          // Faster animation
          let radius = 300;
          const animationInterval = setInterval(() => {
            radius += 300;
            locationCircle.setRadius(radius);
            if (radius >= 3000) {
              clearInterval(animationInterval);
              locationCircle.setMap(null);
              
              // Find nearby schools immediately
              showNearbySchools(pos);
            }
          }, 20); // Even faster animation
        }
        
        // Create user location marker
        if (map) {
          const userMarker = new googleRef.current.maps.Marker({
            position: pos,
            map: map,
            icon: {
              path: googleRef.current.maps.SymbolPath.CIRCLE,
              scale: 10,
              fillColor: '#6366F1',
              fillOpacity: 0.9,
              strokeColor: '#ffffff',
              strokeWeight: 2,
              strokeOpacity: 0.9
            },
            zIndex: 10
          });
          
          // Only show info window if we have a zip code
          if (zipCode) {
            const infoContent = `
              <div class="p-3 bg-gray-900 text-white rounded shadow">
                <p class="font-semibold">Your Location</p>
                <p class="text-sm text-indigo-300">Zip Code: ${zipCode}</p>
              </div>
            `;
            
            const userInfoWindow = new googleRef.current.maps.InfoWindow({
              content: infoContent,
              pixelOffset: new googleRef.current.maps.Size(0, -5)
            });
            
            userInfoWindow.open(map, userMarker);
            
            // Close after 4 seconds (faster)
            setTimeout(() => {
              userInfoWindow.close();
            }, 4000);
          }
        }
        
        setIsLoading(false);
        setLocationStatus('granted');
        setShowLocationPrompt(false);
      },
      (error) => {
        console.error("Geolocation error:", error);
        setError("Couldn't get your location. " + (error.message || ''));
        setIsLoading(false);
        setLocationStatus('denied');
        setShowLocationPrompt(false);
      },
      {
        enableHighAccuracy: false, // Faster results with lower accuracy
        timeout: 2000, // Much shorter timeout
        maximumAge: 60000 // Accept cache for 1 minute
      }
    );
  }, [map, zipCode, getZipCodeFromCoords]);

  // Function to show nearby schools 
  const showNearbySchools = useCallback((position) => {
    if (!map || !schools || !googleRef.current) return;
    
    const bounds = new googleRef.current.maps.LatLngBounds();
    bounds.extend(position);
    
    // Add nearby schools to bounds
    let schoolsFound = 0;
    schools.forEach(school => {
      if (isValidCoords({ lat: school.lat, lng: school.lng })) {
        const schoolPos = {
          lat: parseFloat(school.lat),
          lng: parseFloat(school.lng)
        };
        
        // Simple distance check (faster than geometry library)
        const latDiff = Math.abs(position.lat - schoolPos.lat);
        const lngDiff = Math.abs(position.lng - schoolPos.lng);
        
        // Quick approximation for nearby schools (roughly 50km at equator)
        if (latDiff < 0.5 && lngDiff < 0.5) {
          bounds.extend(schoolPos);
          schoolsFound++;
        }
      }
    });
    
    // Fit bounds to show nearby schools
    if (schoolsFound > 0) {
      map.fitBounds(bounds, { padding: 50 });
    }
  }, [map, schools]);

  // Clean up function for markers and listeners
  const cleanupMarkers = useCallback((markersToClean) => {
    markersToClean.forEach(marker => {
      if (marker) {
        googleRef.current?.maps?.event?.clearInstanceListeners(marker);
        marker.setMap(null);
      }
    });
  }, []);

  // Get zip code from coordinates
  const getZipCodeFromCoords = useCallback(async (lat, lng) => {
    if (!googleRef.current || !lat || !lng) return null;
    
    try {
      const geocoder = new googleRef.current.maps.Geocoder();
      const response = await geocoder.geocode({
        location: { lat, lng }
      });
      
      if (response.results && response.results.length > 0) {
        for (const component of response.results[0].address_components) {
          if (component.types.includes('postal_code')) {
            return component.long_name;
          }
        }
      }
      return null;
    } catch (error) {
      console.error('Error getting zip code:', error);
      return null;
    }
  }, []);

  // Filter schools based on search term
  useEffect(() => {
    if (!searchTerm) {
      setFilteredSchools(schools);
      return;
    }
    
    const filtered = schools.filter(school => 
      school.name.toLowerCase().includes(searchTerm.toLowerCase()) || 
      (school.address && school.address.toLowerCase().includes(searchTerm.toLowerCase()))
    );
    
    setFilteredSchools(filtered);
  }, [searchTerm, schools]);

  // Initialize map
  useEffect(() => {
    let mounted = true;

    const initMap = async () => {
      if (!mapRef.current) return;

      try {
        setIsLoading(true);
        const google = await loadGoogleMapsScript();
        if (!mounted) return;
        
        googleRef.current = google;
        
        const center = isValidCoords(userLocation) 
          ? { 
              lat: parseFloat(userLocation.lat ?? userLocation.latitude), 
              lng: parseFloat(userLocation.lng ?? userLocation.longitude) 
            }
          : DEFAULT_CENTER;

        // Create the map with high-resolution options
        const mapInstance = new google.maps.Map(mapRef.current, {
          center,
          zoom: isValidCoords(userLocation) ? 12 : DEFAULT_ZOOM,
          mapTypeControl: false,
          streetViewControl: false,
          fullscreenControl: true,
          gestureHandling: 'cooperative',
          styles: MAP_STYLES,
          zoomControl: true,
          zoomControlOptions: {
            position: google.maps.ControlPosition.RIGHT_BOTTOM
          },
          scaleControl: true,
          rotateControl: false,
          mapTypeId: google.maps.MapTypeId.ROADMAP,
          tilt: 0,
          clickableIcons: false,
          maxZoom: 18,
          backgroundColor: '#0f172a',
          renderingType: 'vector'
        });

        // Force high DPI rendering
        if (window.devicePixelRatio > 1) {
          const canvasElements = mapRef.current.getElementsByTagName('canvas');
          for (let i = 0; i < canvasElements.length; i++) {
            const canvas = canvasElements[i];
            canvas.style.width = canvas.width + 'px';
            canvas.style.height = canvas.height + 'px';
            canvas.width = canvas.width * window.devicePixelRatio;
            canvas.height = canvas.height * window.devicePixelRatio;
          }
        }

        // Get zip code if user location is provided
        if (isValidCoords(userLocation)) {
          const userZip = await getZipCodeFromCoords(
            parseFloat(userLocation.lat ?? userLocation.latitude),
            parseFloat(userLocation.lng ?? userLocation.longitude)
          );
          if (userZip) setZipCode(userZip);
        }

        // Smooth zoom animation
        mapInstance.addListener('zoom_changed', () => {
          mapRef.current.style.transition = 'all 0.3s ease-in-out';
        });

        // Update zip code when map center changes
        mapInstance.addListener('idle', () => {
          const center = mapInstance.getCenter();
          if (center) {
            getZipCodeFromCoords(center.lat(), center.lng()).then(zip => {
              if (zip) setZipCode(zip);
            });
          }
        });

        // Add search box functionality if input exists
        if (searchInputRef.current) {
          autocompleteRef.current = new google.maps.places.Autocomplete(searchInputRef.current);
          autocompleteRef.current.addListener('place_changed', () => {
            const place = autocompleteRef.current.getPlace();
            if (!place.geometry) return;
            
            const position = {
              lat: place.geometry.location.lat(),
              lng: place.geometry.location.lng()
            };
            
            mapInstance.setCenter(position);
            mapInstance.setZoom(13);

            // Get zip code from search result
            getZipCodeFromCoords(position.lat, position.lng).then(zip => {
              if (zip) setZipCode(zip);
            });
          });
        }

        // Add visual enhancements when map is idle
        google.maps.event.addListenerOnce(mapInstance, 'idle', () => {
          google.maps.event.trigger(mapInstance, 'resize');
          setIsLoading(false);
        });

        // Create the "Use My Location" button
        const locationButton = document.createElement("div");
        locationButton.className = "custom-map-control-button";
        locationButton.innerHTML = `
          <button type="button" class="flex items-center gap-2 rounded-lg px-4 py-2 text-base transition-all duration-200 bg-gradient-to-r from-indigo-600 via-purple-600 to-indigo-700 hover:shadow-lg hover:from-indigo-500 hover:via-purple-500 hover:to-indigo-600 text-white">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            Use My Location
          </button>
        `;
        
        locationButton.addEventListener("click", () => {
          // Show permission prompt if not already granted
          if (locationStatus === 'prompt' || !locationStatus) {
            setShowLocationPrompt(true);
          } else {
            // Get location immediately if permission already granted
            getLocationImmediately();
          }
        });
        
        mapInstance.controls[google.maps.ControlPosition.TOP_RIGHT].push(locationButton);

        setMap(mapInstance);
        setError(null);
      } catch (err) {
        console.error('Error initializing map:', err);
        setError(err.message || 'Failed to load Google Maps');
        setIsLoading(false);
      }
    };

    initMap();

    return () => {
      mounted = false;
      cleanupMarkers(markers);
      if (infoWindowRef.current) {
        infoWindowRef.current.close();
      }
    };
  }, [userLocation, cleanupMarkers, schools, getZipCodeFromCoords, locationStatus]);

  // Handle markers and schools
  useEffect(() => {
    if (!map || !googleRef.current || !filteredSchools) return;

    // Show loading indicator when updating markers
    setIsLoading(true);
    
    cleanupMarkers(markers);

    const bounds = new googleRef.current.maps.LatLngBounds();
    const newMarkers = [];

    // Add user location marker if available
    if (isValidCoords(userLocation)) {
      const position = {
        lat: parseFloat(userLocation.lat),
        lng: parseFloat(userLocation.lng)
      };

      // Pulse animation for user location
      const userMarkerOptions = {
        position,
        map,
        icon: {
          path: googleRef.current.maps.SymbolPath.CIRCLE,
          scale: 10,
          fillColor: '#6366F1', // Indigo to match site theme
          fillOpacity: 0.9,
          strokeColor: '#ffffff',
          strokeWeight: 2,
          strokeOpacity: 0.9
        },
        zIndex: 10,
        title: "Your Location"
      };

      const userMarker = new googleRef.current.maps.Marker(userMarkerOptions);
      
      // Add info window with zip code information if available
      if (zipCode) {
        const infoContent = `
          <div class="p-3 bg-gray-900 text-white rounded shadow">
            <p class="font-semibold">Your Location</p>
            <p class="text-sm text-indigo-300">Zip Code: ${zipCode}</p>
          </div>
        `;
        
        const userInfoWindow = new googleRef.current.maps.InfoWindow({
          content: infoContent,
          pixelOffset: new googleRef.current.maps.Size(0, -5)
        });
        
        userMarker.addListener('click', () => {
          userInfoWindow.open(map, userMarker);
        });
      }
      
      newMarkers.push(userMarker);
      bounds.extend(position);
    }

    // Add school markers with improved visibility and staggered animation
    if (filteredSchools && filteredSchools.length > 0) {
      filteredSchools.forEach((school, index) => {
        if (!isValidCoords({ lat: school.lat, lng: school.lng })) {
          return;
        }

        const position = {
          lat: parseFloat(school.lat),
          lng: parseFloat(school.lng)
        };

        // Add delay for staggered animation
        setTimeout(() => {
          // Custom marker icon for dental schools with improved visibility
          const schoolMarker = new googleRef.current.maps.Marker({
            position,
            map,
            title: school.name,
            animation: googleRef.current.maps.Animation.DROP,
            icon: {
              path: 'M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z', // Pin shape
              fillColor: activeSchool === school.name ? '#8B5CF6' : '#A855F7', // Purple with active state
              fillOpacity: 1.0, // Full opacity for better visibility
              strokeWeight: 2, // Thicker outline
              strokeColor: '#ffffff',
              scale: activeSchool === school.name ? 3.0 : 2.5, // Significantly larger for better visibility
              anchor: new googleRef.current.maps.Point(12, 22),
              labelOrigin: new googleRef.current.maps.Point(12, -10)
            },
            label: {
              text: school.name,
              color: '#FFFFFF',
              fontSize: '14px',
              fontWeight: 'bold',
              className: 'marker-label'
            },
            optimized: false, // Required for some custom effects
            zIndex: activeSchool === school.name ? 1000 : 100 // Active school appears on top
          });

          // Enhanced info window with more details and better styling
          const infoWindow = new googleRef.current.maps.InfoWindow({
            content: `
              <div class="p-4 max-w-sm bg-gray-900 text-white rounded-lg shadow-lg">
                <h3 class="text-xl font-bold mb-2 text-purple-300">${school.name}</h3>
                ${school.address ? `<p class="text-white/80 mb-2">${school.address}</p>` : ''}
                ${school.description ? `<p class="text-white/90 mb-2">${school.description}</p>` : ''}
                <div class="mt-3 pt-3 border-t border-gray-700">
                  <span class="text-sm text-indigo-300">Click for detailed information</span>
                </div>
              </div>
            `,
            maxWidth: 320,
            pixelOffset: new googleRef.current.maps.Size(0, -5)
          });

          // Hover effect for markers - enhanced
          schoolMarker.addListener('mouseover', () => {
            schoolMarker.setIcon({
              ...schoolMarker.getIcon(),
              fillColor: '#8B5CF6', // Lighter purple on hover
              scale: 3.0, // Larger on hover
              strokeWeight: 3 // Thicker outline on hover
            });
            
            // Change the z-index to bring it to the front
            schoolMarker.setZIndex(1000);
          });

          schoolMarker.addListener('mouseout', () => {
            // Only reset if not the active school
            if (activeSchool !== school.name) {
              schoolMarker.setIcon({
                ...schoolMarker.getIcon(),
                fillColor: '#A855F7',
                scale: 2.5,
                strokeWeight: 2
              });
              schoolMarker.setZIndex(100);
            }
          });

          // Click event for school marker
          schoolMarker.addListener('click', () => {
            // Close any open info windows
            if (infoWindowRef.current) {
              infoWindowRef.current.close();
            }
            
            // Open this info window
            infoWindow.open(map, schoolMarker);
            infoWindowRef.current = infoWindow;
            
            // Set active school and update marker appearance
            setActiveSchool(school.name);
            
            // Visually highlight the selected marker
            filteredSchools.forEach((s, i) => {
              if (s.name === school.name && newMarkers[i+1]) {
                newMarkers[i+1].setIcon({
                  ...newMarkers[i+1].getIcon(),
                  fillColor: '#8B5CF6',
                  scale: 3.0,
                  strokeWeight: 3
                });
                newMarkers[i+1].setZIndex(1000);
              } else if (newMarkers[i+1]) {
                // Reset other markers
                newMarkers[i+1].setIcon({
                  ...newMarkers[i+1].getIcon(),
                  fillColor: '#A855F7',
                  scale: 2.5,
                  strokeWeight: 2
                });
                newMarkers[i+1].setZIndex(100);
              }
            });
            
            // Smoothly pan to the school
            map.panTo(position);
            
            // Zoom in slightly if we're too far out
            if (map.getZoom() < 14) {
              map.setZoom(15);
            }
          });

          newMarkers.push(schoolMarker);
          bounds.extend(position);
          
          // If this is the last marker, finish loading and fit bounds
          if (index === filteredSchools.length - 1) {
            // Fit bounds with padding
            if (bounds.isEmpty()) {
              setIsLoading(false);
              return;
            }
            
            // If we only have one location, zoom in appropriately
            if (filteredSchools.length === 1 && !isValidCoords(userLocation)) {
              map.setCenter(position);
              map.setZoom(15); // Close enough to see details
            } else {
              // Fit all markers on screen with padding
              map.fitBounds(bounds, { padding: 50 });
              
              // For better visibility, don't zoom in too far on large areas
              const listener = googleRef.current.maps.event.addListenerOnce(map, 'idle', () => {
                if (map.getZoom() > 16) map.setZoom(16);
                if (map.getZoom() < 10 && filteredSchools.length === 1) map.setZoom(14);
                googleRef.current.maps.event.removeListener(listener);
              });
            }
            
            setIsLoading(false);
          }
        }, index * 50); // Faster staggered animation for better responsiveness
      });
    } else {
      setIsLoading(false);
    }

    setMarkers(newMarkers);

    return () => {
      newMarkers.forEach(marker => marker?.setMap(null));
    };
  }, [map, userLocation, filteredSchools, activeSchool, cleanupMarkers, zipCode]);

  // Methods for external components to interact with
  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  const handleClearSearch = () => {
    setSearchTerm('');
    setActiveSchool(null);
  };

  // Handle location permission request
  const handleLocationPermissionResponse = (allow) => {
    setShowLocationPrompt(false);
    if (allow) {
      getLocationImmediately();
    }
  };

  // Render component
  return (
    <div className="relative w-full h-full">
      {/* Map container with shadow for better visual separation */}
      <div 
        ref={mapRef} 
        className="w-full h-full rounded-lg shadow-xl overflow-hidden" 
        style={{ minHeight: '500px' }}
      ></div>
      
      {/* Zip code display */}
      {zipCode && (
        <div className="absolute top-4 left-4 z-10 bg-gray-900/90 text-white px-4 py-2 rounded-lg shadow-lg backdrop-blur-sm">
          <div className="flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            <span className="font-medium">Current Zip Code: <span className="text-indigo-300">{zipCode}</span></span>
          </div>
        </div>
      )}
      
      {/* Loading overlay */}
      {isLoading && (
        <div className="absolute inset-0 bg-gray-900/70 flex items-center justify-center rounded-lg z-20">
          <div className="text-center">
            <div className="w-12 h-12 border-4 border-t-transparent border-indigo-500 rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-white font-medium">
              {locationStatus === 'prompt' ? 'Getting your location...' : 'Loading map...'}
            </p>
          </div>
        </div>
      )}
      
      {/* Location permission prompt - made more attention-grabbing */}
      {showLocationPrompt && (
        <div className="absolute inset-0 bg-gray-900/90 flex items-center justify-center rounded-lg z-30 backdrop-blur-md">
          <div className="bg-gradient-to-br from-gray-900 to-gray-800 p-6 rounded-xl shadow-2xl max-w-md mx-4 border border-indigo-500/30">
            <div className="text-center mb-4">
              <div className="bg-indigo-600/20 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-white mb-2">Share Your Location</h3>
              <p className="text-gray-300 mb-4">
                Allow access to your location to find dental schools near you and display your current zip code.
              </p>
            </div>
            <div className="flex gap-4 justify-center">
              <button
                onClick={() => handleLocationPermissionResponse(false)}
                className="px-4 py-3 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors text-base"
              >
                Not Now
              </button>
              <button
                onClick={() => handleLocationPermissionResponse(true)}
                className="px-6 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-lg hover:from-indigo-500 hover:to-purple-500 transition-colors text-base font-medium"
              >
                Allow Location
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Error message */}
      {error && (
        <div className="absolute inset-0 bg-red-900/70 flex items-center justify-center rounded-lg z-10">
          <div className="text-center max-w-md mx-auto p-6 bg-gray-900 rounded-lg">
            <svg className="w-12 h-12 text-red-500 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h3 className="text-xl font-bold text-white mb-2">Location Error</h3>
            <p className="text-white/80 mb-4">{error}</p>
            <button 
              className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
              onClick={() => {
                setError(null);
                // Try location again
                document.querySelector('.custom-map-control-button button')?.click();
              }}
            >
              Try Again
            </button>
          </div>
        </div>
      )}
    </div>
  );
}