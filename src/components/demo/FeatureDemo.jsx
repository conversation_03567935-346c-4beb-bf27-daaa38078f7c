import React, { useEffect, useRef } from 'react';
import { motion } from 'framer-motion';

export const FeatureDemo = () => {
  const canvasRef = useRef(null);
  const animationRef = useRef(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    
    // Set canvas size with device pixel ratio for sharp rendering
    const dpr = window.devicePixelRatio || 1;
    const rect = canvas.getBoundingClientRect();
    canvas.width = rect.width * dpr;
    canvas.height = rect.height * dpr;
    ctx.scale(dpr, dpr);
    
    // Initialize teeth data
    const teeth = Array(32).fill(null).map((_, i) => ({
      x: (i % 16) * (rect.width / 16),
      y: i < 16 ? rect.height * 0.25 : rect.height * 0.75,
      health: Math.random(),
      analyzed: false
    }));

    // Initialize analysis points
    const points = Array(50).fill(null).map(() => ({
      x: Math.random() * rect.width,
      y: Math.random() * rect.height,
      vx: (Math.random() - 0.5) * 2,
      vy: (Math.random() - 0.5) * 2,
      connections: []
    }));

    let scanLine = 0;
    let frame = 0;

    const animate = () => {
      frame++;
      ctx.clearRect(0, 0, canvas.width / dpr, canvas.height / dpr);

      // Draw scan line
      const scanY = (scanLine % canvas.height);
      ctx.beginPath();
      ctx.strokeStyle = 'rgba(0, 255, 255, 0.5)';
      ctx.moveTo(0, scanY);
      ctx.lineTo(canvas.width / dpr, scanY);
      ctx.stroke();

      // Analyze teeth as scan line passes
      teeth.forEach(tooth => {
        if (Math.abs(tooth.y - scanY) < 5) {
          tooth.analyzed = true;
        }

        // Draw tooth
        ctx.beginPath();
        ctx.fillStyle = tooth.analyzed 
          ? `rgba(${tooth.health > 0.7 ? '0, 255, 0' : '255, 165, 0'}, 0.5)`
          : 'rgba(255, 255, 255, 0.2)';
        ctx.arc(tooth.x, tooth.y, 10, 0, Math.PI * 2);
        ctx.fill();
      });

      // Update and draw analysis points
      points.forEach((point, i) => {
        // Update position
        point.x += point.vx;
        point.y += point.vy;

        // Bounce off walls
        if (point.x < 0 || point.x > rect.width) point.vx *= -1;
        if (point.y < 0 || point.y > rect.height) point.vy *= -1;

        // Draw connections
        points.forEach((other, j) => {
          if (i === j) return;
          const dx = other.x - point.x;
          const dy = other.y - point.y;
          const distance = Math.sqrt(dx * dx + dy * dy);
          
          if (distance < 100) {
            ctx.beginPath();
            ctx.strokeStyle = `rgba(0, 255, 255, ${(100 - distance) / 500})`;
            ctx.moveTo(point.x, point.y);
            ctx.lineTo(other.x, other.y);
            ctx.stroke();
          }
        });

        // Draw point
        ctx.beginPath();
        ctx.fillStyle = 'rgba(0, 255, 255, 0.5)';
        ctx.arc(point.x, point.y, 3, 0, Math.PI * 2);
        ctx.fill();
      });

      // Draw processing rings
      const centerX = rect.width / 2;
      const centerY = rect.height / 2;
      const maxRadius = 50;
      const numRings = 3;
      
      for (let i = 0; i < numRings; i++) {
        const radius = ((frame + i * 20) % maxRadius);
        const alpha = (maxRadius - radius) / maxRadius;
        
        ctx.beginPath();
        ctx.strokeStyle = `rgba(0, 255, 255, ${alpha})`;
        ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
        ctx.stroke();
      }

      // Update scan line
      scanLine += 2;

      animationRef.current = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="w-full h-full"
    >
      <canvas
        ref={canvasRef}
        className="w-full h-full"
        style={{ width: '100%', height: '100%' }}
      />
    </motion.div>
  );
}; 