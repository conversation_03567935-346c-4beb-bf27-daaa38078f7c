import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Cookies from 'js-cookie';
import { supabase, getClientInfo } from '../../lib/supabase';
import { v4 as uuidv4 } from 'uuid';
import { useNavigate } from 'react-router-dom';
import SignaturePad from 'react-signature-canvas';
import { useAdmin } from '../../lib/hooks/useAdmin';
import { useAdminAuth } from '../../lib/hooks/useAdminAuth';
import { shouldBypassAuth } from '../../lib/utils/adminBypass';

// Simple fingerprint generation function
const generateFingerprint = async () => {
  try {
    const components = [
      window.navigator.userAgent,
      window.navigator.language,
      window.screen.colorDepth,
      window.screen.pixelDepth,
      window.screen.width + 'x' + window.screen.height,
      new Date().getTimezoneOffset(),
      !!window.sessionStorage,
      !!window.localStorage,
      !!window.indexedDB
    ];
    
    // Create a simple hash of the components
    const fingerprintString = components.join('|||');
    const encoder = new TextEncoder();
    const data = encoder.encode(fingerprintString);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  } catch (error) {
    console.error('Error generating fingerprint:', error);
    return uuidv4(); // Fallback to UUID if fingerprint generation fails
  }
};

export default function NDAModal({ onAccept }) {
  const navigate = useNavigate();
  const { isAdmin, adminBypass } = useAdmin();
  const { authenticate } = useAdminAuth();
  const signaturePadRef = useRef(null);
  const scrollContainerRef = useRef(null);
  const [isOpen, setIsOpen] = useState(true);
  const [hasScrolled, setHasScrolled] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showThankYou, setShowThankYou] = useState(false);
  const [fingerprint, setFingerprint] = useState('');
  const [showAdminLogin, setShowAdminLogin] = useState(false);
  const [adminCredentials, setAdminCredentials] = useState({ username: '', password: '' });
  const [adminError, setAdminError] = useState('');
  const [userInfo, setUserInfo] = useState({
    fullName: '',
    email: '',
    company: '',
    title: '',
    date: new Date().toISOString().split('T')[0],
    signature: '',
    agreementId: uuidv4(),
    dentistLicenseNumber: '',
    practiceNPI: '',
    stateOfPractice: '',
    yearsInPractice: '',
    referralSource: '',
    linkedinProfile: '',
    hasConfirmed: false
  });
  const [errors, setErrors] = useState({});

  useEffect(() => {
    const init = async () => {
      try {
        // Check if user is admin using the utility function
        if (isAdmin || shouldBypassAuth()) {
          console.log('Admin detected, bypassing NDA');
          setIsOpen(false);
          // Set cookies to prevent future NDA prompts
          Cookies.set('smilo_nda_accepted', 'true', { expires: 365 });
          Cookies.set('smilo_nda_agreement_id', 'admin-bypass', { expires: 365 });
          onAccept();
          return;
        }

        // Generate fingerprint first
        try {
          const fp = await generateFingerprint();
          setFingerprint(fp);
        } catch (error) {
          console.error('Error generating fingerprint:', error);
          // Set a fallback fingerprint
          setFingerprint(uuidv4());
        }

        // Check NDA status
        const checkApprovalStatus = async () => {
          try {
            const hasAcceptedNDA = Cookies.get('smilo_nda_accepted');
            const agreementId = Cookies.get('smilo_nda_agreement_id');
            
            if (hasAcceptedNDA && agreementId) {
              const { data, error } = await supabase
                .from('nda_acceptances')
                .select('is_approved')
                .eq('agreement_id', agreementId)
                .single();

              if (error) {
                console.error('Error checking NDA status:', error);
                return;
              }

              if (data?.is_approved) {
                setIsOpen(false);
                onAccept();
              }
            }
          } catch (error) {
            console.error('Error in checkApprovalStatus:', error);
          }
        };

        await checkApprovalStatus();
      } catch (error) {
        console.error('Error in init:', error);
      }
    };

    init();
  }, [isAdmin, onAccept]);

  const clearSignature = () => {
    if (signaturePadRef.current) {
      signaturePadRef.current.clear();
      setUserInfo({ ...userInfo, signature: '' });
    }
  };

  const handleScroll = (e) => {
    const element = e.target;
    const scrollPosition = element.scrollTop + element.clientHeight;
    const scrollHeight = element.scrollHeight;
    
    // Mark as read if user has scrolled to within 100px of the bottom
    if (scrollHeight - scrollPosition <= 100) {
      setHasScrolled(true);
      setErrors((prev) => ({ ...prev, scroll: undefined }));
    }
  };

  const validateForm = () => {
    console.log('Validating form...');
    // Admin bypass - skip validation if admin
    if (isAdmin || shouldBypassAuth()) {
      console.log('Admin bypass - skipping validation');
      return true;
    }

    const newErrors = {};
    if (!userInfo.fullName.trim()) newErrors.fullName = 'Full name is required';
    if (!userInfo.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(userInfo.email)) {
      newErrors.email = 'Email is invalid';
    }
    if (!userInfo.company.trim()) newErrors.company = 'Practice name is required';
    if (!userInfo.title.trim()) newErrors.title = 'Title is required';
    
    // Fix signature validation logic
    if (signaturePadRef.current?.isEmpty()) {
      newErrors.signature = 'Signature is required';
    } else {
      // Only set signature if it's not empty
      setUserInfo(prev => ({ ...prev, signature: signaturePadRef.current.toDataURL() }));
    }

    if (!hasScrolled) newErrors.scroll = 'Please read the entire agreement';
    if (!userInfo.hasConfirmed) newErrors.confirmation = 'You must confirm that you have read and agree to the terms';
    
    console.log('Validation errors:', newErrors);
    console.log('Form state:', {
      fullName: userInfo.fullName,
      email: userInfo.email,
      company: userInfo.company,
      title: userInfo.title,
      hasSignature: !signaturePadRef.current?.isEmpty(),
      hasScrolled,
      hasConfirmed: userInfo.hasConfirmed
    });
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleAccept = async () => {
    console.log('Handle accept clicked');
    const isValid = validateForm();
    console.log('Form validation result:', isValid);
    
    if (isValid) {
      setIsSubmitting(true);
      try {
        console.log('Starting form submission...');
        const clientInfo = await getClientInfo();
        const timestamp = new Date().toISOString();
        const currentTimestamp = Date.now().toString();
        
        // Set cookies first to ensure they're available
        console.log('Setting NDA cookies in NDAModal');
        Cookies.set('smilo_nda_accepted', 'true', { expires: 365 });
        Cookies.set('smilo_nda_agreement_id', userInfo.agreementId, { expires: 365 });
        Cookies.set('smilo_nda_timestamp', currentTimestamp, { expires: 365 });
        Cookies.set('smilo_nda_signature', userInfo.signature, { expires: 365 });

        console.log('NDA cookies verification in NDAModal:', {
          accepted: Cookies.get('smilo_nda_accepted'),
          agreementId: Cookies.get('smilo_nda_agreement_id'),
          timestamp: Cookies.get('smilo_nda_timestamp'),
          signature: Boolean(Cookies.get('smilo_nda_signature'))
        });

        // Call onAccept first to ensure successful navigation
        console.log('Calling onAccept callback');
        onAccept({
          agreementId: userInfo.agreementId,
          signature: userInfo.signature
        });

        try {
          // Try to save to database but don't block on failure
          const { data, error } = await supabase
            .from('nda_acceptances')
            .insert([
              {
                agreement_id: userInfo.agreementId,
                full_name: userInfo.fullName,
                email: userInfo.email,
                organization: userInfo.company,
                position: userInfo.title,
                signature_data: userInfo.signature,
                created_at: timestamp,
                ip_address: clientInfo?.ip_address,
                user_agent: clientInfo?.user_agent,
                is_valid: true,
                is_approved: false
              }
            ]);

          if (error) {
            console.error('Database error, but proceeding anyway:', error);
          }
        } catch (dbError) {
          console.error('Database save failed, but proceeding anyway:', dbError);
        }

        // Skip showing the thank you message and just close the modal
        // This allows immediate navigation to the partnerships page
        setIsOpen(false);
        
      } catch (error) {
        console.error('Error in handleAccept:', error);
        
        // Even if there's an error, we should still set cookies and call onAccept
        try {
          console.log('Setting cookies despite error');
          Cookies.set('smilo_nda_accepted', 'true', { expires: 365 });
          Cookies.set('smilo_nda_agreement_id', userInfo.agreementId || uuidv4(), { expires: 365 });
          Cookies.set('smilo_nda_timestamp', Date.now().toString(), { expires: 365 });
          Cookies.set('smilo_nda_signature', userInfo.signature || 'backup-signature', { expires: 365 });
          
          // Still call onAccept to navigate
          onAccept({
            agreementId: userInfo.agreementId || uuidv4(),
            signature: userInfo.signature || 'backup-signature'
          });
        } catch (cookieError) {
          console.error('Error setting cookies:', cookieError);
          setErrors({ submit: 'Failed to save agreement. Please try again.' });
        }
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  const handleBack = () => {
    navigate('/');
  };

  const handleAdminLogin = async (e) => {
    e.preventDefault();
    setAdminError('');
    
    try {
      const success = await authenticate(adminCredentials.username, adminCredentials.password);
      
      if (success) {
        // Set cookies to prevent future NDA prompts
        Cookies.set('smilo_nda_accepted', 'true', { expires: 365 });
        Cookies.set('smilo_nda_agreement_id', 'admin-bypass', { expires: 365 });
        onAccept();
        setIsOpen(false);
      }
    } catch (err) {
      setAdminError('Invalid credentials');
    }
  };

  // Exit early if admin - don't render modal at all
  if (isAdmin || shouldBypassAuth()) {
    return null;
  }

  return (
    <AnimatePresence>
      {isOpen && !showThankYou && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm p-4"
        >
          {showAdminLogin ? (
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-gradient-to-br from-gray-900 to-gray-800 rounded-xl shadow-2xl max-w-md w-full overflow-hidden border border-gray-700"
            >
              <div className="p-6">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-2xl font-bold text-white">Admin Access</h2>
                  <button
                    onClick={() => setShowAdminLogin(false)}
                    className="text-gray-400 hover:text-white"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                {adminError && (
                  <div className="mb-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg text-red-400 text-sm">
                    {adminError}
                  </div>
                )}

                <form onSubmit={handleAdminLogin} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-white/80 mb-1">
                      Username
                    </label>
                    <input
                      type="text"
                      value={adminCredentials.username}
                      onChange={(e) => setAdminCredentials(prev => ({ ...prev, username: e.target.value }))}
                      className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-white/80 mb-1">
                      Password
                    </label>
                    <input
                      type="password"
                      value={adminCredentials.password}
                      onChange={(e) => setAdminCredentials(prev => ({ ...prev, password: e.target.value }))}
                      className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white"
                      required
                    />
                  </div>

                  <button
                    type="submit"
                    className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-500 hover:to-indigo-500 text-white rounded-lg px-4 py-2 transition-all duration-300"
                  >
                    Access Admin Panel
                  </button>
                </form>
              </div>
            </motion.div>
          ) : (
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-gradient-to-br from-gray-900 to-gray-800 rounded-xl shadow-2xl max-w-4xl w-full h-[90vh] overflow-hidden border border-gray-700 flex flex-col"
            >
              {/* Header */}
              <div className="p-6 border-b border-gray-700 flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold text-white">Non-Disclosure Agreement</h2>
                  <p className="text-gray-400 mt-2">This is a legally binding agreement. Please read carefully.</p>
                </div>
                <div className="flex items-center space-x-4">
                  <button
                    onClick={() => setShowAdminLogin(true)}
                    className="px-4 py-2 bg-indigo-600 hover:bg-indigo-500 text-white rounded-lg transition-all duration-300 flex items-center"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clipRule="evenodd" />
                    </svg>
                    Admin Login
                  </button>
                  <button
                    onClick={handleBack}
                    className="px-4 py-2 bg-gray-600 hover:bg-gray-500 text-white rounded-lg transition-all duration-300 flex items-center"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
                    </svg>
                    Decline & Exit
                  </button>
                </div>
              </div>

              {/* Scrollable Content */}
              <div 
                ref={scrollContainerRef}
                className="flex-1 overflow-y-auto scroll-smooth"
                onScroll={handleScroll}
              >
                <div className="p-6">
                  <div className="prose prose-invert max-w-none">
                    <h3>MUTUAL NON-DISCLOSURE AGREEMENT</h3>
                    <p className="text-sm text-gray-300 mb-4">
                      This Mutual Non-Disclosure Agreement (this "Agreement") is entered into as of {userInfo.date}, by and between:
                    </p>

                    <p className="text-sm text-gray-300 mb-4">
                      <strong>Smilo.Dental LLC</strong>, a Delaware limited liability company ("Company"), and the individual or entity agreeing to these terms ("Recipient").
                    </p>

                    <h4>1. Definition of Confidential Information</h4>
                    <p className="text-sm text-gray-300 mb-4">
                      "Confidential Information" means all non-public, confidential, and proprietary information disclosed by Company to Recipient, including but not limited to:
                    </p>
                    <ul className="text-sm text-gray-300 mb-4">
                      <li>Business strategies, plans, and models</li>
                      <li>Technical information, algorithms, and source code</li>
                      <li>Customer and partner data</li>
                      <li>Pricing and financial information</li>
                      <li>Product designs and specifications</li>
                      <li>Marketing strategies and plans</li>
                      <li>Any information marked as "confidential" or that should reasonably be understood to be confidential</li>
                    </ul>

                    <h4>2. Non-Disclosure and Non-Use</h4>
                    <p className="text-sm text-gray-300 mb-4">
                      Recipient agrees to:
                    </p>
                    <ul className="text-sm text-gray-300 mb-4">
                      <li>Maintain strict confidentiality of all Confidential Information</li>
                      <li>Not disclose Confidential Information to any third party</li>
                      <li>Use Confidential Information solely for evaluating potential business relationships with Company</li>
                      <li>Implement and maintain security measures to prevent unauthorized access</li>
                      <li>Notify Company immediately of any unauthorized disclosure or use</li>
                    </ul>

                    <h4>3. Term and Termination</h4>
                    <p className="text-sm text-gray-300 mb-4">
                      This Agreement shall remain in effect for five (5) years from the date of acceptance. The confidentiality obligations shall survive termination.
                    </p>

                    <h4>4. Remedies</h4>
                    <p className="text-sm text-gray-300 mb-4">
                      Recipient acknowledges that monetary damages may be insufficient for a breach and that Company shall be entitled to seek injunctive relief. Recipient shall be liable for all costs, including reasonable attorneys' fees, incurred by Company in enforcing this Agreement.
                    </p>

                    <h4>5. Return of Materials</h4>
                    <p className="text-sm text-gray-300 mb-4">
                      Upon Company's request, Recipient shall promptly return or destroy all Confidential Information and certify such destruction in writing.
                    </p>

                    <h4>6. No License or Rights</h4>
                    <p className="text-sm text-gray-300 mb-4">
                      No license or rights to patents, copyrights, trademarks, trade secrets, or other intellectual property are granted or implied by this Agreement or disclosure of Confidential Information.
                    </p>

                    <h4>7. Governing Law and Jurisdiction</h4>
                    <p className="text-sm text-gray-300 mb-4">
                      This Agreement shall be governed by Delaware law. Any disputes shall be resolved exclusively in the state or federal courts located in Delaware.
                    </p>

                    <h4>8. Entire Agreement</h4>
                    <p className="text-sm text-gray-300 mb-4">
                      This Agreement constitutes the entire understanding between the parties regarding Confidential Information and supersedes all prior agreements.
                    </p>

                    <h4>9. Electronic Signature and Records</h4>
                    <p className="text-sm text-gray-300 mb-4">
                      The parties agree that electronic signatures and records of this Agreement are valid and enforceable under the Electronic Signatures in Global and National Commerce Act (E-SIGN) and the Uniform Electronic Transactions Act (UETA).
                    </p>

                    <h4>10. Data Collection and Privacy</h4>
                    <p className="text-sm text-gray-300 mb-4">
                      Recipient acknowledges and consents to the collection and storage of personal information, IP address, device information, and other relevant data for the purpose of enforcing this Agreement and maintaining records of acceptance.
                    </p>

                    <h4>11. Injunctive Relief</h4>
                    <p className="text-sm text-gray-300 mb-4">
                      Recipient acknowledges that any breach of this Agreement may cause irreparable harm to Company, and Company shall be entitled to seek immediate injunctive relief, in addition to any other remedies available at law or in equity.
                    </p>

                    <h4>12. Survival</h4>
                    <p className="text-sm text-gray-300 mb-4">
                      The obligations of confidentiality, non-use, and non-disclosure shall survive any termination or expiration of this Agreement for a period of five (5) years.
                    </p>

                    <h4>13. Legal Fees</h4>
                    <p className="text-sm text-gray-300 mb-4">
                      In any action to enforce this Agreement, the prevailing party shall be entitled to recover its reasonable attorneys' fees and costs.
                    </p>
                  </div>
                </div>

                <div className="p-6 border-t border-gray-700 space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">Full Legal Name *</label>
                      <input
                        type="text"
                        value={userInfo.fullName}
                        onChange={(e) => setUserInfo({...userInfo, fullName: e.target.value})}
                        className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:ring-2 focus:ring-indigo-500"
                        placeholder="John Doe"
                      />
                      {errors.fullName && <p className="text-red-500 text-xs mt-1">{errors.fullName}</p>}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">Email *</label>
                      <input
                        type="email"
                        value={userInfo.email}
                        onChange={(e) => setUserInfo({...userInfo, email: e.target.value})}
                        className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:ring-2 focus:ring-indigo-500"
                        placeholder="<EMAIL>"
                      />
                      {errors.email && <p className="text-red-500 text-xs mt-1">{errors.email}</p>}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">Company Name *</label>
                      <input
                        type="text"
                        value={userInfo.company}
                        onChange={(e) => setUserInfo({...userInfo, company: e.target.value})}
                        className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:ring-2 focus:ring-indigo-500"
                        placeholder="Company Inc."
                      />
                      {errors.company && <p className="text-red-500 text-xs mt-1">{errors.company}</p>}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">Title/Position *</label>
                      <input
                        type="text"
                        value={userInfo.title}
                        onChange={(e) => setUserInfo({...userInfo, title: e.target.value})}
                        className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:ring-2 focus:ring-indigo-500"
                        placeholder="CEO"
                      />
                      {errors.title && <p className="text-red-500 text-xs mt-1">{errors.title}</p>}
                    </div>

                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-300 mb-1">Digital Signature *</label>
                      <div className="border rounded-lg p-2 bg-white">
                        <SignaturePad
                          ref={signaturePadRef}
                          canvasProps={{
                            className: 'signature-canvas w-full h-40 border rounded',
                            style: { width: '100%', height: '160px' }
                          }}
                        />
                        <button
                          onClick={clearSignature}
                          className="mt-2 text-sm text-gray-600 hover:text-gray-900"
                        >
                          Clear Signature
                        </button>
                      </div>
                    </div>

                    <div className="md:col-span-2 mt-4">
                      <p className="text-xs text-gray-400">
                        Agreement ID: {userInfo.agreementId}
                      </p>
                    </div>
                  </div>

                  {errors.scroll && <p className="text-red-500 text-xs">{errors.scroll}</p>}

                  {/* Add confirmation checkbox */}
                  <div className="mt-4 mb-4">
                    <label className="flex items-start">
                      <input
                        type="checkbox"
                        checked={userInfo.hasConfirmed || false}
                        onChange={(e) => setUserInfo({...userInfo, hasConfirmed: e.target.checked})}
                        className="mt-1 form-checkbox h-5 w-5 text-indigo-600 transition duration-150 ease-in-out rounded border-gray-500 bg-gray-700 focus:ring-indigo-500"
                      />
                      <span className="ml-3 text-sm text-gray-300">
                        I confirm that I have read and understood the entire Non-Disclosure Agreement and agree to be bound by its terms and conditions.
                      </span>
                    </label>
                    {errors.confirmation && <p className="text-red-500 text-xs mt-1">{errors.confirmation}</p>}
                  </div>

                  <div className="flex items-center justify-between space-x-4">
                    <button
                      onClick={handleBack}
                      className="px-6 py-2 bg-gray-600 hover:bg-gray-500 text-white rounded-lg transition-all duration-300 flex items-center"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
                      </svg>
                      Decline & Go Back
                    </button>
                    <button
                      onClick={handleAccept}
                      disabled={isSubmitting}
                      className={`px-6 py-2 bg-gradient-to-r from-indigo-600 to-blue-600 hover:from-indigo-500 hover:to-blue-500 text-white rounded-lg transition-all duration-300 shadow-lg shadow-indigo-500/25 ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}`}
                    >
                      {isSubmitting ? (
                        <span className="flex items-center">
                          <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Processing...
                        </span>
                      ) : (
                        'I Agree & Accept'
                      )}
                    </button>
                  </div>

                  {errors.submit && (
                    <p className="text-red-500 text-sm mt-4 text-center">{errors.submit}</p>
                  )}

                  <p className="text-xs text-gray-400 mt-4">
                    By clicking "I Agree & Accept", you acknowledge that you have read and understood this agreement and agree to be bound by its terms.
                    This creates a legally binding contract between you and Smilo.Dental LLC. Your acceptance and related information will be securely stored for legal record-keeping purposes.
                    If you do not wish to proceed, click "Decline & Go Back" to return to the home page.
                  </p>
                </div>
              </div>
            </motion.div>
          )}

          {showThankYou && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm p-4"
            >
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ type: 'spring', damping: 25 }}
                className="bg-gradient-to-br from-gray-900 to-gray-800 rounded-xl shadow-2xl max-w-lg w-full p-6 text-center"
              >
                <h2 className="text-2xl font-bold text-white mb-4">Thank You!</h2>
                <p className="text-gray-300 mb-3">
                  Your NDA submission has been received and is pending review. We will notify you once it has been approved.
                </p>
                
                <div className="mb-4 p-3 bg-gray-800/50 rounded-lg text-sm text-gray-300 border border-gray-700">
                  <p><span className="font-medium text-blue-400">Signed by:</span> {userInfo.fullName}</p>
                  <p><span className="font-medium text-blue-400">Date:</span> {new Date().toLocaleDateString()}</p>
                  {userInfo.locationInfo && (
                    <p><span className="font-medium text-blue-400">Location:</span> {userInfo.locationInfo}</p>
                  )}
                  <p className="text-xs text-gray-500 mt-1">
                    This submission has been securely logged with your device information.
                  </p>
                </div>
                
                <button
                  onClick={() => navigate('/')}
                  className="px-6 py-2 bg-gradient-to-r from-indigo-600 to-blue-600 hover:from-indigo-500 hover:to-blue-500 text-white rounded-lg transition-all duration-300"
                >
                  Return to Home
                </button>
              </motion.div>
            </motion.div>
          )}
        </motion.div>
      )}
    </AnimatePresence>
  );
} 