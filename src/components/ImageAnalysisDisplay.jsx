import React from 'react';
import { Card } from './common';
import ReactMarkdown from 'react-markdown';

export default function ImageAnalysisDisplay({ results, analyzing, error }) {
  if (error) {
    return (
      <Card className="mt-6">
        <div className="p-4 bg-red-500/10 border border-red-500/20 rounded text-red-400">
          {error}
        </div>
      </Card>
    );
  }

  if (analyzing) {
    return (
      <Card className="mt-6">
        <div className="flex items-center justify-center p-6">
          <div className="flex items-center gap-3">
            <div className="w-6 h-6 border-2 border-blue-500/20 border-t-blue-500 rounded-full animate-spin" />
            <span className="text-white/80">Analyzing image...</span>
          </div>
        </div>
      </Card>
    );
  }

  if (!results) return null;

  return (
    <Card className="mt-6">
      <div className="prose prose-invert max-w-none">
        <ReactMarkdown>{results}</ReactMarkdown>
      </div>
      
      <div className="mt-6 p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
        <p className="text-sm text-white/80">
          Note: This analysis is provided for informational purposes only. 
          Please consult a dental professional for proper diagnosis and treatment.
        </p>
      </div>
    </Card>
  );
}