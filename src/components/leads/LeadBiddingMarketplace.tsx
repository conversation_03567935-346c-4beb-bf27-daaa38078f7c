import React, { useState } from 'react';
import { motion } from 'framer-motion';

// Define the Lead type
export interface Lead {
  id: string;
  patientNeeds: string;
  urgency: 'Low' | 'Medium' | 'High';
  insurance: string;
  estimatedValue: number;
  location: string;
  postedDate: string;
}

// Define the props for the LeadBiddingMarketplace component
interface LeadBiddingMarketplaceProps {
  leads: Lead[];
  onBidSubmit?: (leadId: string, bidAmount: number) => Promise<boolean>;
}

const LeadBiddingMarketplace: React.FC<LeadBiddingMarketplaceProps> = ({ 
  leads, 
  onBidSubmit = async () => true // Default implementation that resolves to true
}) => {
  // State for bid amounts
  const [bidAmounts, setBidAmounts] = useState<Record<string, string>>({});
  
  // State for bid status messages
  const [bidStatus, setBidStatus] = useState<Record<string, { message: string; type: 'success' | 'error' | 'loading' }>>({});
  
  // Handle input change for a specific lead
  const handleBidChange = (leadId: string, value: string) => {
    // Only allow numbers and decimal points
    if (/^(\d*\.?\d{0,2})?$/.test(value)) {
      setBidAmounts(prev => ({
        ...prev,
        [leadId]: value
      }));
    }
  };
  
  // Handle bid submission
  const handleBidSubmit = async (leadId: string) => {
    const bidAmount = parseFloat(bidAmounts[leadId] || '0');
    
    if (bidAmount <= 0) {
      setBidStatus(prev => ({
        ...prev,
        [leadId]: { message: 'Please enter a valid bid amount', type: 'error' }
      }));
      return;
    }
    
    // Show loading status
    setBidStatus(prev => ({
      ...prev,
      [leadId]: { message: 'Processing bid...', type: 'loading' }
    }));
    
    try {
      // Call the onBidSubmit callback with the lead ID and bid amount
      const success = await onBidSubmit(leadId, bidAmount);
      
      if (success) {
        setBidStatus(prev => ({
          ...prev,
          [leadId]: { message: 'Bid placed successfully!', type: 'success' }
        }));
        
        // Clear the bid amount after successful submission
        setBidAmounts(prev => ({
          ...prev,
          [leadId]: ''
        }));
        
        // Clear success message after 3 seconds
        setTimeout(() => {
          setBidStatus(prev => {
            const newStatus = { ...prev };
            delete newStatus[leadId];
            return newStatus;
          });
        }, 3000);
      } else {
        setBidStatus(prev => ({
          ...prev,
          [leadId]: { message: 'Failed to place bid. Please try again.', type: 'error' }
        }));
      }
    } catch (error) {
      setBidStatus(prev => ({
        ...prev,
        [leadId]: { message: 'An error occurred. Please try again.', type: 'error' }
      }));
    }
  };
  
  // Get urgency color
  const getUrgencyColor = (urgency: Lead['urgency']) => {
    switch (urgency) {
      case 'High':
        return 'bg-red-100 text-red-800';
      case 'Medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'Low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-blue-100 text-blue-800';
    }
  };
  
  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  return (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden">
      {/* Marketplace Header */}
      <div className="bg-gradient-to-r from-indigo-600 to-blue-500 px-6 py-5">
        <h2 className="text-2xl font-bold text-white">Lead Bidding Marketplace</h2>
        <p className="text-indigo-100 mt-1">Find and bid on high-value patient leads in your area</p>
      </div>
      
      {/* Filters Section - Placeholder for future implementation */}
      <div className="bg-indigo-50 px-6 py-3 border-b border-indigo-100 flex flex-wrap items-center justify-between">
        <p className="text-indigo-800 font-medium">{leads.length} active leads available</p>
        
        <div className="flex items-center space-x-2 text-sm text-indigo-700">
          <span>Filter by:</span>
          <select className="border border-indigo-300 rounded px-2 py-1 bg-white">
            <option>All Procedures</option>
            <option>Cosmetic</option>
            <option>Emergency</option>
            <option>Restorative</option>
          </select>
        </div>
      </div>
      
      {/* Leads List */}
      <div className="divide-y divide-gray-200">
        {leads.length === 0 ? (
          <div className="py-12 px-6 text-center">
            <svg className="w-12 h-12 mx-auto text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h3 className="mt-4 text-lg font-medium text-gray-900">No leads available</h3>
            <p className="mt-1 text-gray-500">Check back later for new patient leads in your area.</p>
          </div>
        ) : (
          leads.map((lead) => (
            <motion.div 
              key={lead.id} 
              className="p-6 hover:bg-indigo-50 transition-colors duration-150"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                {/* Lead Information */}
                <div className="flex-1">
                  <div className="flex items-center">
                    <h3 className="text-lg font-medium text-gray-900">{lead.patientNeeds}</h3>
                    <span className={`ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getUrgencyColor(lead.urgency)}`}>
                      {lead.urgency} Urgency
                    </span>
                  </div>
                  
                  <div className="mt-2 grid grid-cols-2 gap-x-4 gap-y-2 text-sm text-gray-600">
                    <div className="flex items-center">
                      <svg className="flex-shrink-0 mr-1.5 h-4 w-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                      </svg>
                      Insurance: {lead.insurance}
                    </div>
                    <div className="flex items-center">
                      <svg className="flex-shrink-0 mr-1.5 h-4 w-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                      </svg>
                      Location: {lead.location}
                    </div>
                    <div className="flex items-center">
                      <svg className="flex-shrink-0 mr-1.5 h-4 w-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                      </svg>
                      Posted: {lead.postedDate}
                    </div>
                    <div className="flex items-center">
                      <svg className="flex-shrink-0 mr-1.5 h-4 w-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                      </svg>
                      Est. Value: <span className="font-medium text-indigo-700">{formatCurrency(lead.estimatedValue)}</span>
                    </div>
                  </div>
                </div>
                
                {/* Bidding Section */}
                <div className="mt-4 md:mt-0 md:ml-6 flex-shrink-0">
                  <div className="flex items-end">
                    <div className="relative w-32">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <span className="text-gray-500 sm:text-sm">$</span>
                      </div>
                      <label htmlFor={`bid-amount-${lead.id}`} className="sr-only">Bid amount for {lead.patientNeeds}</label>
                      <input
                        id={`bid-amount-${lead.id}`}
                        name={`bid-amount-${lead.id}`}
                        type="text"
                        value={bidAmounts[lead.id] || ''}
                        onChange={(e) => handleBidChange(lead.id, e.target.value)}
                        className="block w-full pl-7 pr-3 py-2 text-base border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                        placeholder="Bid amount"
                      />
                    </div>
                    <button
                      onClick={() => handleBidSubmit(lead.id)}
                      className="ml-3 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                      Place Bid
                    </button>
                  </div>
                  
                  {/* Bid status message */}
                  {bidStatus[lead.id] && (
                    <div className="mt-2 text-xs">
                      <span className={`
                        ${bidStatus[lead.id].type === 'success' ? 'text-green-600' : ''}
                        ${bidStatus[lead.id].type === 'error' ? 'text-red-600' : ''}
                        ${bidStatus[lead.id].type === 'loading' ? 'text-blue-600' : ''}
                      `}>
                        {bidStatus[lead.id].message}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          ))
        )}
      </div>
    </div>
  );
};

export default LeadBiddingMarketplace; 