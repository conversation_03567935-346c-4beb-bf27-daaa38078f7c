/**
 * @license
 * Smilo.Dental - Proprietary and Confidential
 * Copyright (c) 2024 Smilo.Dental LLC. All Rights Reserved.
 * Unauthorized copying, modification, distribution, or use of this file, via any medium, is strictly prohibited.
 * Written by <PERSON> <mi<PERSON><PERSON>@smilo.dental>
 * 
 * This code is protected by:
 * - US Copyright Law
 * - Trade Secret Laws
 * - Provisional Patent Application
 * - Trademark Application (Pending)
 * - Non-Disclosure Agreements
 * 
 * LEGAL NOTICE:
 * This software is protected by intellectual property laws. Any unauthorized use,
 * reproduction, reverse engineering, or distribution of this code or any portion
 * thereof may result in severe civil and criminal penalties, and will be
 * prosecuted to the maximum extent possible under the law.
 * 
 * Version: 1.0.0
 * Build: SMILO-PROD-2024
 */

"use strict";

// Obfuscation hint for build process
/* @preserve-obfuscate */
/* @preserve-minify */

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card } from '../ui/Card';
import NDAModal from '../legal/NDAModal';
import Cookies from 'js-cookie';
import { FeatureDemo } from '../demo/FeatureDemo';

export default function PartnershipsPage() {
  const [activeTab, setActiveTab] = useState('demo');
  const [hasAcceptedNDA, setHasAcceptedNDA] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [hoveredCard, setHoveredCard] = useState(null);
  const [showMetrics, setShowMetrics] = useState(false);

  // Initialize security measures
  useEffect(() => {
    // Security measures - moved inside component to avoid build errors
    if (typeof window !== 'undefined') {
      if (import.meta.env.DEV) {
        console.warn('Development mode - Security measures partially disabled');
      } else {
        // Prevent debugging in production
        const securityInterval = setInterval(() => {
          const devTools = window.__REACT_DEVTOOLS_GLOBAL_HOOK__;
          if (typeof devTools === 'object') {
            throw new Error('Security violation: Developer tools detected');
          }
        }, 1000);
        
        // Prevent right-click
        const handleContextMenu = (e) => e.preventDefault();
        document.addEventListener('contextmenu', handleContextMenu);
        
        // Prevent keyboard shortcuts
        const handleKeyDown = (e) => {
          if ((e.ctrlKey || e.metaKey) && (
            e.key === 's' || 
            e.key === 'u' ||
            e.key === 'p' ||
            e.key === 'c'
          )) {
            e.preventDefault();
            return false;
          }
        };
        document.addEventListener('keydown', handleKeyDown);

        // Cleanup function
        return () => {
          clearInterval(securityInterval);
          document.removeEventListener('contextmenu', handleContextMenu);
          document.removeEventListener('keydown', handleKeyDown);
        };
      }
    }
  }, []);

  useEffect(() => {
    const checkNDAStatus = () => {
      try {
        console.log('Checking NDA status...');
        // Get NDA acceptance status and agreement ID
        const accepted = Cookies.get('smilo_nda_accepted');
        const agreementId = Cookies.get('smilo_nda_agreement_id');
        const timestamp = Cookies.get('smilo_nda_timestamp');
        const signature = Cookies.get('smilo_nda_signature');
        
        console.log('NDA cookie values:', { accepted, agreementId, timestamp, hasSignature: !!signature });
        
        // Set as valid if we have all required cookies - skip database validation for now
        if (accepted && agreementId && timestamp && signature) {
          console.log('NDA accepted, showing partnerships page');
          setHasAcceptedNDA(true);
          setIsLoading(false);
          return;
        }
        
        // If any cookies are missing, force NDA acceptance
        console.log('NDA not accepted, showing NDA modal');
        setHasAcceptedNDA(false);
        // Clear any invalid/expired NDA cookies
        Cookies.remove('smilo_nda_accepted');
        Cookies.remove('smilo_nda_agreement_id');
        Cookies.remove('smilo_nda_timestamp');
        Cookies.remove('smilo_nda_signature');
        setIsLoading(false);
      } catch (error) {
        console.error('NDA validation error:', error);
        // On any error, force NDA acceptance
        setHasAcceptedNDA(false);
        setIsLoading(false);
      }
    };

    // Check NDA status immediately
    checkNDAStatus();

    // Recheck NDA status every 5 minutes - but ONLY check cookie existence 
    // not database validation to prevent loops
    const interval = setInterval(() => {
      const accepted = Cookies.get('smilo_nda_accepted');
      const agreementId = Cookies.get('smilo_nda_agreement_id');
      const timestamp = Cookies.get('smilo_nda_timestamp');
      const signature = Cookies.get('smilo_nda_signature');
      
      if (!(accepted && agreementId && timestamp && signature)) {
        console.log('NDA cookies lost during session, reshowing NDA');
        setHasAcceptedNDA(false);
      }
    }, 5 * 60 * 1000);

    // No longer check on visibility change to prevent loops
    // Just rely on the initial check and interval

    // Cleanup
    return () => {
      clearInterval(interval);
    };
  }, []);

  // Prevent content from being shown if NDA is not accepted
  if (isLoading) {
    return (
      <motion.div 
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="min-h-screen flex items-center justify-center bg-gradient-to-b from-gray-900 via-blue-900/20 to-indigo-900/20"
      >
        <div className="relative">
          <div className="w-16 h-16 border-t-4 border-b-4 border-indigo-500 rounded-full animate-spin"></div>
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-12 h-12 border-t-4 border-b-4 border-blue-500 rounded-full animate-spin"></div>
          </div>
        </div>
      </motion.div>
    );
  }

  // Force NDA modal if not accepted
  if (!hasAcceptedNDA) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="fixed inset-0 z-50 bg-black/90"
      >
        <NDAModal 
          onAccept={(data) => {
            console.log('Setting NDA cookies in PartnershipsPage');
            // Save NDA data with timestamp and signature (365 days to match NDAModal)
            Cookies.set('smilo_nda_accepted', 'true', { expires: 365 });
            Cookies.set('smilo_nda_agreement_id', data.agreementId, { expires: 365 });
            Cookies.set('smilo_nda_timestamp', Date.now().toString(), { expires: 365 });
            Cookies.set('smilo_nda_signature', data.signature, { expires: 365 });
            
            console.log('NDA cookies verification:', {
              accepted: Cookies.get('smilo_nda_accepted'),
              agreementId: Cookies.get('smilo_nda_agreement_id'),
              timestamp: Cookies.get('smilo_nda_timestamp'),
              signature: Boolean(Cookies.get('smilo_nda_signature'))
            });
            
            // Force immediate state update and skip to content - no reload
            setHasAcceptedNDA(true);
            console.log('NDA accepted, proceeding to partnerships page');
            
            // Remove the reload which might be causing the loop
          }} 
        />
      </motion.div>
    );
  }

  const renderPricingContent = () => {
    switch(activeTab) {
      case 'demo':
        return (
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="space-y-8"
          >
            <div className="bg-white/5 p-8 rounded-2xl border border-indigo-500/20">
              <h2 className="text-3xl font-bold text-white mb-6">
                AI-Powered Dental Analysis Demo
              </h2>
              <div className="grid md:grid-cols-2 gap-8">
                <div className="space-y-6">
                  <div className="p-6 bg-white/5 rounded-xl border border-indigo-500/20">
                    <h3 className="text-xl font-semibold text-white mb-3">Live Analysis</h3>
                    <div className="aspect-video mb-4">
                      <FeatureDemo />
                    </div>
                    <p className="text-white/70">
                      Watch our AI analyze dental images in real-time, identifying potential issues and treatment recommendations.
                    </p>
                  </div>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="w-full px-6 py-4 bg-gradient-to-r from-indigo-600 to-blue-600 rounded-xl text-white font-semibold"
                  >
                    Try Live Analysis
                  </motion.button>
                </div>
                <div className="space-y-6">
                  <div className="p-6 bg-white/5 rounded-xl border border-indigo-500/20">
                    <h3 className="text-xl font-semibold text-white mb-3">Performance Metrics</h3>
                    <ul className="space-y-3 text-white/70">
                      <li>• 99.8% accuracy in dental condition detection</li>
                      <li>• 3-second average analysis time</li>
                      <li>• Support for all major imaging formats</li>
                      <li>• Real-time treatment suggestions</li>
                    </ul>
                  </div>
                  <div className="p-6 bg-white/5 rounded-xl border border-indigo-500/20">
                    <h3 className="text-xl font-semibold text-white mb-3">Key Features</h3>
                    <ul className="space-y-3 text-white/70">
                      <li>• Multi-point analysis system</li>
                      <li>• Automated measurements</li>
                      <li>• Treatment progress tracking</li>
                      <li>• Patient history integration</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        );

      case 'features':
        return (
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="space-y-8"
          >
            <div className="bg-white/5 p-8 rounded-2xl border border-indigo-500/20">
              <h2 className="text-3xl font-bold text-white mb-6">
                Advanced AI Features
              </h2>
              <div className="grid md:grid-cols-3 gap-6">
                {[
                  {
                    title: "Image Analysis",
                    description: "Advanced dental imaging analysis with 99.8% accuracy",
                    details: ["Cavity detection", "Periodontal assessment", "Treatment planning", "Progress monitoring"],
                    icon: "🔍"
                  },
                  {
                    title: "Patient Management",
                    description: "Streamlined patient care and communication system",
                    details: ["Automated scheduling", "Treatment tracking", "Patient reminders", "Digital records"],
                    icon: "👥"
                  },
                  {
                    title: "Practice Analytics",
                    description: "Comprehensive practice performance insights",
                    details: ["Revenue tracking", "Patient metrics", "Treatment success rates", "Growth analytics"],
                    icon: "📊"
                  },
                  {
                    title: "Security",
                    description: "Enterprise-grade security and compliance",
                    details: ["HIPAA compliance", "Data encryption", "Access controls", "Audit trails"],
                    icon: "🔒"
                  },
                  {
                    title: "Integration",
                    description: "Seamless integration with existing systems",
                    details: ["PMS compatibility", "API access", "Data migration", "Custom workflows"],
                    icon: "🔄"
                  },
                  {
                    title: "Support",
                    description: "24/7 comprehensive support system",
                    details: ["Live chat", "Training resources", "Updates", "Technical assistance"],
                    icon: "💬"
                  }
                ].map((feature, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    whileHover={{ scale: 1.02 }}
                    className="p-6 rounded-xl border border-indigo-500/20 bg-white/5"
                  >
                    <div className="text-4xl mb-4">{feature.icon}</div>
                    <h3 className="text-xl font-semibold text-white mb-2">{feature.title}</h3>
                    <p className="text-white/70 mb-4">{feature.description}</p>
                    <ul className="space-y-2">
                      {feature.details.map((detail, i) => (
                        <li key={i} className="text-white/60 text-sm">• {detail}</li>
                      ))}
                    </ul>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>
        );

      case 'experience':
        return (
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="space-y-8"
          >
            <div className="bg-white/5 p-8 rounded-2xl border border-indigo-500/20">
              <h2 className="text-3xl font-bold text-white mb-6">
                Interactive Experience Center
              </h2>
              <div className="grid md:grid-cols-2 gap-8">
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  className="p-6 rounded-xl border border-indigo-500/20 bg-white/5"
                >
                  <h3 className="text-2xl font-semibold text-white mb-4">Interactive Demo</h3>
                  <div className="aspect-video mb-6 rounded-lg overflow-hidden">
                    <FeatureDemo />
                  </div>
                  <p className="text-white/70 mb-6">
                    Try our interactive demo to see how Smilo AI analyzes dental images and provides instant insights.
                  </p>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="w-full px-6 py-3 bg-gradient-to-r from-indigo-600 to-blue-600 rounded-xl text-white font-semibold hover:from-indigo-700 hover:to-blue-700 transition-all duration-300"
                  >
                    Launch Demo
                  </motion.button>
                </motion.div>

                <motion.div
                  whileHover={{ scale: 1.02 }}
                  className="p-6 rounded-xl border border-indigo-500/20 bg-white/5"
                >
                  <h3 className="text-2xl font-semibold text-white mb-4">Schedule a Live Demo</h3>
                  <div className="space-y-4 mb-6">
                    <div className="p-4 bg-white/5 rounded-lg">
                      <h4 className="text-lg font-medium text-white mb-2">Personalized Walkthrough</h4>
                      <p className="text-white/70">Get a guided tour of all features tailored to your practice's needs.</p>
                    </div>
                    <div className="p-4 bg-white/5 rounded-lg">
                      <h4 className="text-lg font-medium text-white mb-2">Q&A Session</h4>
                      <p className="text-white/70">Direct interaction with our team to answer all your questions.</p>
                    </div>
                    <div className="p-4 bg-white/5 rounded-lg">
                      <h4 className="text-lg font-medium text-white mb-2">Integration Discussion</h4>
                      <p className="text-white/70">Learn how Smilo AI can integrate with your current systems.</p>
                    </div>
                  </div>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="w-full px-6 py-3 bg-gradient-to-r from-indigo-600 to-blue-600 rounded-xl text-white font-semibold hover:from-indigo-700 hover:to-blue-700 transition-all duration-300"
                  >
                    Schedule Now
                  </motion.button>
                </motion.div>
              </div>
            </div>
          </motion.div>
        );

      case 'practices':
        return (
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="space-y-8"
          >
            <div className="bg-white/5 p-8 rounded-2xl border border-indigo-500/20">
              <h2 className="text-3xl font-bold text-white mb-6">
                Dental Practice Integration Solutions
              </h2>
              <div className="grid md:grid-cols-2 gap-8">
                <div className="space-y-6">
                  <div className="p-6 bg-white/5 rounded-xl border border-indigo-500/20">
                    <h3 className="text-xl font-semibold text-white mb-3">Practice Management Integration</h3>
                    <ul className="space-y-3 text-white/70">
                      <li>• Seamless integration with major PMS systems (Dentrix, Eaglesoft, Open Dental)</li>
                      <li>• Automated appointment scheduling and reminders</li>
                      <li>• Real-time patient record synchronization</li>
                      <li>• Custom workflow automation options</li>
                    </ul>
                  </div>
                  <div className="p-6 bg-white/5 rounded-xl border border-indigo-500/20">
                    <h3 className="text-xl font-semibold text-white mb-3">ROI Benefits</h3>
                    <ul className="space-y-3 text-white/70">
                      <li>• 40% reduction in administrative workload</li>
                      <li>• 60% decrease in no-show rates</li>
                      <li>• 25% increase in patient satisfaction scores</li>
                      <li>• Average $50,000 annual cost savings</li>
                    </ul>
                  </div>
                </div>
                <div className="space-y-6">
                  <div className="p-6 bg-white/5 rounded-xl border border-indigo-500/20">
                    <h3 className="text-xl font-semibold text-white mb-3">Implementation Process</h3>
                    <ol className="space-y-3 text-white/70">
                      <li>1. Initial practice assessment and needs analysis</li>
                      <li>2. Custom integration plan development</li>
                      <li>3. Staff training and onboarding program</li>
                      <li>4. 30-day optimization period</li>
                      <li>5. Ongoing support and updates</li>
                    </ol>
                  </div>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="w-full px-6 py-4 bg-gradient-to-r from-indigo-600 to-blue-600 rounded-xl text-white font-semibold"
                  >
                    Request Practice Assessment
                  </motion.button>
                </div>
              </div>
            </div>
          </motion.div>
        );

      case 'insurance':
        return (
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="space-y-8"
          >
            <div className="bg-white/5 p-8 rounded-2xl border border-indigo-500/20">
              <h2 className="text-3xl font-bold text-white mb-6">
                Insurance Partnership Solutions
              </h2>
              <div className="grid md:grid-cols-2 gap-8">
                <div className="space-y-6">
                  <div className="p-6 bg-white/5 rounded-xl border border-indigo-500/20">
                    <h3 className="text-xl font-semibold text-white mb-3">Claims Processing Automation</h3>
                    <ul className="space-y-3 text-white/70">
                      <li>• AI-powered claim form auto-completion</li>
                      <li>• Real-time verification of benefits</li>
                      <li>• Automated pre-authorization processing</li>
                      <li>• Instant claim status tracking</li>
                    </ul>
                  </div>
                  <div className="p-6 bg-white/5 rounded-xl border border-indigo-500/20">
                    <h3 className="text-xl font-semibold text-white mb-3">Compliance & Security</h3>
                    <ul className="space-y-3 text-white/70">
                      <li>• HIPAA-compliant data transmission</li>
                      <li>• End-to-end encryption</li>
                      <li>• Automated audit trails</li>
                      <li>• Regular security assessments</li>
                    </ul>
                  </div>
                </div>
                <div className="space-y-6">
                  <div className="p-6 bg-white/5 rounded-xl border border-indigo-500/20">
                    <h3 className="text-xl font-semibold text-white mb-3">Integration Benefits</h3>
                    <ul className="space-y-3 text-white/70">
                      <li>• 90% reduction in processing time</li>
                      <li>• 75% decrease in claim errors</li>
                      <li>• Real-time fraud detection</li>
                      <li>• Automated payment reconciliation</li>
                    </ul>
                  </div>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="w-full px-6 py-4 bg-gradient-to-r from-indigo-600 to-blue-600 rounded-xl text-white font-semibold"
                  >
                    Schedule Insurance Integration Demo
                  </motion.button>
                </div>
              </div>
            </div>
          </motion.div>
        );

      case 'schools':
        return (
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="space-y-8"
          >
            <div className="bg-white/5 p-8 rounded-2xl border border-indigo-500/20">
              <h2 className="text-3xl font-bold text-white mb-6">
                Dental Education Partnership Program
              </h2>
              <div className="grid md:grid-cols-2 gap-8">
                <div className="space-y-6">
                  <div className="p-6 bg-white/5 rounded-xl border border-indigo-500/20">
                    <h3 className="text-xl font-semibold text-white mb-3">Educational Resources</h3>
                    <ul className="space-y-3 text-white/70">
                      <li>• AI-assisted learning modules</li>
                      <li>• Virtual patient scenarios</li>
                      <li>• Clinical case libraries</li>
                      <li>• Interactive 3D dental models</li>
                    </ul>
                  </div>
                  <div className="p-6 bg-white/5 rounded-xl border border-indigo-500/20">
                    <h3 className="text-xl font-semibold text-white mb-3">Student Benefits</h3>
                    <ul className="space-y-3 text-white/70">
                      <li>• Hands-on AI technology experience</li>
                      <li>• Real-world practice scenarios</li>
                      <li>• Performance analytics</li>
                      <li>• Career preparation resources</li>
                    </ul>
                  </div>
                </div>
                <div className="space-y-6">
                  <div className="p-6 bg-white/5 rounded-xl border border-indigo-500/20">
                    <h3 className="text-xl font-semibold text-white mb-3">Institution Benefits</h3>
                    <ul className="space-y-3 text-white/70">
                      <li>• Cutting-edge curriculum integration</li>
                      <li>• Research collaboration opportunities</li>
                      <li>• Faculty development programs</li>
                      <li>• Exclusive academic pricing</li>
                    </ul>
                  </div>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="w-full px-6 py-4 bg-gradient-to-r from-indigo-600 to-blue-600 rounded-xl text-white font-semibold"
                  >
                    Join Education Partnership
                  </motion.button>
                </div>
              </div>
            </div>
          </motion.div>
        );
      
      default:
        return null;
    }
  };

  // Update the metrics section in renderPricingContent for 'demo' tab
  const renderMetrics = () => (
    <div className="grid md:grid-cols-4 gap-6 mb-12">
      <AnimatePresence>
        {showMetrics && metrics.map((metric, index) => (
          <motion.div
            key={metric.label}
            initial="hidden"
            animate="visible"
            variants={cardVariants}
            whileHover="hover"
            custom={index}
            className={`bg-gradient-to-r ${metric.color} bg-opacity-10 p-6 rounded-xl border border-white/10 text-center transform transition-all duration-300`}
          >
            <motion.div 
              className="text-4xl font-bold text-white mb-2"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: index * 0.1 }}
            >
              {metric.value}
            </motion.div>
            <div className="text-white/80 text-sm">{metric.label}</div>
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );

  return (
    <div className="min-h-screen py-24 bg-gradient-to-b from-gray-900 via-blue-900/20 to-indigo-900/20">
      <div className="max-w-6xl mx-auto px-4 space-y-16">
        {/* Animated Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-16"
        >
          <motion.h1 
            className="text-5xl font-bold bg-gradient-to-r from-blue-400 via-indigo-400 to-purple-400 text-transparent bg-clip-text mb-6"
            initial={{ scale: 0.9 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.5 }}
          >
            Disrupting Dental's Network Effect 🚀
          </motion.h1>
          <motion.p 
            className="text-xl text-white/80 max-w-3xl mx-auto"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            While corporate dentistry relies on outdated economies of scale, we're building a two-sided marketplace that grows exponentially with each new partner. That's Metcalfe's Law in action - and we're just getting started.
          </motion.p>
          <motion.div 
            className="mt-6 flex flex-wrap justify-center gap-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
          >
            <motion.span 
              whileHover={{ scale: 1.05 }}
              className="px-3 py-1 bg-blue-500/10 text-blue-300 rounded-full text-sm border border-blue-500/20 cursor-pointer"
            >
              Network Effect Driven
            </motion.span>
            <motion.span 
              whileHover={{ scale: 1.05 }}
              className="px-3 py-1 bg-blue-500/10 text-blue-300 rounded-full text-sm border border-blue-500/20 cursor-pointer"
            >
              Zero Marginal Cost Scaling
            </motion.span>
            <motion.span 
              whileHover={{ scale: 1.05 }}
              className="px-3 py-1 bg-blue-500/10 text-blue-300 rounded-full text-sm border border-blue-500/20 cursor-pointer"
            >
              Data Flywheel Model
            </motion.span>
          </motion.div>
        </motion.div>

        {/* Interactive Metrics Display */}
        {renderMetrics()}

        {/* Enhanced Tab Navigation */}
        <div className="flex flex-col items-center mb-12">
          <motion.div 
            className="inline-flex p-1 bg-white/5 rounded-xl backdrop-blur-sm border border-white/10 mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            {['demo', 'features', 'experience', 'practices', 'insurance', 'schools'].map((tab) => (
              <motion.button
                key={tab}
                onClick={() => handleTabChange(tab)}
                className={`px-6 py-2 rounded-lg transition-all duration-300 ${
                  activeTab === tab 
                    ? 'bg-gradient-to-r from-indigo-500 to-blue-500 text-white font-semibold' 
                    : 'text-white/70 hover:text-white'
                }`}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                {tab.charAt(0).toUpperCase() + tab.slice(1)}
              </motion.button>
            ))}
          </motion.div>

          {/* Animated Tab Content */}
          <AnimatePresence mode="wait">
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
              className="w-full"
            >
              {renderPricingContent()}
            </motion.div>
          </AnimatePresence>
        </div>

        {/* Rest of the existing content */}
        <div className="grid md:grid-cols-2 gap-8 mb-16">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Card className="h-full bg-gradient-to-br from-blue-900/40 to-indigo-900/30 backdrop-blur-sm border border-white/10 hover:border-indigo-400/50 transition-all duration-300">
              <div className="p-8">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-r from-blue-500/30 to-indigo-500/30 flex items-center justify-center mr-4">
                    <svg className="w-6 h-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  </div>
                  <h2 className="text-2xl font-bold text-white">For Insurance Companies</h2>
                </div>
                <p className="text-white/80 mb-6">
                  Partner with SMILO to reduce costs, improve outcomes, and enhance your competitive advantage in the dental insurance market.
                </p>
                <div className="space-y-8">
                  <div className="bg-white/5 p-6 rounded-xl hover:bg-white/10 transition-all duration-300 backdrop-blur-sm">
                    <h3 className="text-xl font-semibold text-white mb-3">Cost Containment as a Service</h3>
                    <p className="text-white/70">
                      Our AI pre-screening reduces unnecessary procedures and visits. Pay monthly fees based on documented cost savings - a true performance-based partnership.
                    </p>
                  </div>
                  
                  <div className="bg-white/5 p-6 rounded-xl hover:bg-white/10 transition-all duration-300 backdrop-blur-sm">
                    <h3 className="text-xl font-semibold text-white mb-3">Data Licensing Model</h3>
                    <p className="text-white/70">
                      Access anonymized oral health trends that help adjust actuarial models with unprecedented accuracy. Our data insights are worth millions to forward-thinking insurers.
                    </p>
                  </div>
                  
                  <div className="bg-white/5 p-6 rounded-xl hover:bg-white/10 transition-all duration-300 backdrop-blur-sm">
                    <h3 className="text-xl font-semibold text-white mb-3">Claim Pre-Verification System</h3>
                    <p className="text-white/70">
                      Our AI validates treatment necessity before submission, reducing fraud and unnecessary procedures while streamlining the approval process.
                    </p>
                  </div>
                  
                  <div className="bg-white/5 p-6 rounded-xl hover:bg-white/10 transition-all duration-300 backdrop-blur-sm">
                    <h3 className="text-xl font-semibold text-white mb-3">Preferred Provider Networks</h3>
                    <p className="text-white/70">
                      We steer patients to insurance-preferred dentists in exchange for per-patient acquisition fees, creating a win-win-win scenario for patients, providers, and insurers.
                    </p>
                  </div>
                </div>
              </div>
            </Card>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <Card className="h-full bg-gradient-to-br from-purple-900/40 to-indigo-900/30 backdrop-blur-sm border border-white/10 hover:border-purple-400/50 transition-all duration-300">
              <div className="p-8">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-r from-purple-500/30 to-indigo-500/30 flex items-center justify-center mr-4">
                    <svg className="w-6 h-6 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                    </svg>
                  </div>
                  <h2 className="text-2xl font-bold text-white">For Dental Providers</h2>
                </div>
                <p className="text-white/80 mb-6">
                  Join our network of forward-thinking dental professionals and clinics to increase patient volume and streamline operations.
                </p>
                <div className="space-y-8">
                  <div className="bg-white/5 p-6 rounded-xl hover:bg-white/10 transition-all duration-300 backdrop-blur-sm">
                    <h3 className="text-xl font-semibold text-white mb-3">Patient Acquisition</h3>
                    <p className="text-white/70">
                      Access a steady stream of pre-qualified patients who need your specific services. Our AI matching system connects the right patients to your practice.
                    </p>
                  </div>
                  
                  <div className="bg-white/5 p-6 rounded-xl hover:bg-white/10 transition-all duration-300 backdrop-blur-sm">
                    <h3 className="text-xl font-semibold text-white mb-3">Insurance Fast-Track</h3>
                    <p className="text-white/70">
                      Join our pre-approved provider network to expedite insurance claims and reduce administrative overhead for your practice.
                    </p>
                  </div>
                  
                  <div className="bg-white/5 p-6 rounded-xl hover:bg-white/10 transition-all duration-300 backdrop-blur-sm">
                    <h3 className="text-xl font-semibold text-white mb-3">Technology Integration</h3>
                    <p className="text-white/70">
                      Enhance your practice with our AI diagnostic tools, patient management systems, and telehealth capabilities.
                    </p>
                  </div>
                  
                  <div className="bg-white/5 p-6 rounded-xl hover:bg-white/10 transition-all duration-300 backdrop-blur-sm">
                    <h3 className="text-xl font-semibold text-white mb-3">Community Involvement</h3>
                    <p className="text-white/70">
                      Participate in our affordable care initiative to serve underserved communities while growing your practice reputation.
                    </p>
                  </div>
                </div>
              </div>
            </Card>
          </motion.div>
        </div>
        
        {/* Value Magnet Strategy Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.45 }}
          className="mb-16"
        >
          <Card className="relative overflow-hidden bg-gradient-to-br from-green-900/30 via-teal-900/20 to-blue-900/30 backdrop-blur-sm border border-green-500/20">
            <div className="absolute top-0 right-0 w-64 h-64 bg-teal-500/10 rounded-full blur-[100px]"></div>
            <div className="absolute bottom-0 left-0 w-64 h-64 bg-blue-500/10 rounded-full blur-[100px]"></div>
            
            <div className="p-8 relative z-10">
              <div className="flex flex-col md:flex-row md:items-center justify-between mb-8">
                <div className="flex items-center mb-4 md:mb-0">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-r from-teal-500/40 to-green-500/40 flex items-center justify-center mr-4">
                    <svg className="w-6 h-6 text-teal-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-white">The "Robin Hood" Strategy</h2>
                    <p className="text-teal-300/90 text-sm">Taking from corporate dentistry, giving to the people (and our partners)</p>
                  </div>
                </div>
                <div className="px-3 py-1.5 rounded-full bg-gradient-to-r from-teal-500/20 to-green-500/20 border border-teal-400/30 flex items-center">
                  <svg className="w-4 h-4 text-teal-300 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                  <span className="text-xs font-semibold text-teal-300">Disrupting Corporate Dentistry</span>
                </div>
              </div>
              
              <div className="flex flex-col lg:flex-row gap-8">
                <div className="lg:w-2/3">
                  <div className="prose prose-invert max-w-none">
                    <p className="text-white/90 text-lg leading-relaxed mb-6">
                      Instead of following the traditional corporate model of exploiting patients, we're building a <span className="text-teal-300 font-semibold">patient-first ecosystem</span> that generates extraordinary value for dental partners while democratizing access to care.
                    </p>
                    
                    <div className="grid md:grid-cols-3 gap-6 mb-8">
                      <div className="bg-white/5 p-5 rounded-xl border border-teal-500/20 relative group hover:bg-white/10 transition-all duration-300">
                        <div className="absolute top-3 right-3 text-teal-300 opacity-70 group-hover:opacity-100 transition-opacity">
                          <span className="text-xs bg-teal-500/20 px-2 py-0.5 rounded-full">Step 1</span>
                        </div>
                        <h3 className="text-white font-semibold text-lg mb-2 group-hover:text-teal-300 transition-colors">Free AI Value Tools</h3>
                        <p className="text-white/70 text-sm">
                          We offer completely free AI smile assessments and design tools so valuable that users flood to our platform. No paywalls, no friction.
                        </p>
                      </div>
                      
                      <div className="bg-white/5 p-5 rounded-xl border border-teal-500/20 relative group hover:bg-white/10 transition-all duration-300">
                        <div className="absolute top-3 right-3 text-teal-300 opacity-70 group-hover:opacity-100 transition-opacity">
                          <span className="text-xs bg-teal-500/20 px-2 py-0.5 rounded-full">Step 2</span>
                        </div>
                        <h3 className="text-white font-semibold text-lg mb-2 group-hover:text-teal-300 transition-colors">Rich Data Collection</h3>
                        <p className="text-white/70 text-sm">
                          Every interaction yields valuable data points: location, dental issues, treatment interests, insurance status, and urgency indicators.
                        </p>
                      </div>
                      
                      <div className="bg-white/5 p-5 rounded-xl border border-teal-500/20 relative group hover:bg-white/10 transition-all duration-300">
                        <div className="absolute top-3 right-3 text-teal-300 opacity-70 group-hover:opacity-100 transition-opacity">
                          <span className="text-xs bg-teal-500/20 px-2 py-0.5 rounded-full">Step 3</span>
                        </div>
                        <h3 className="text-white font-semibold text-lg mb-2 group-hover:text-teal-300 transition-colors">Intent Detection</h3>
                        <p className="text-white/70 text-sm">
                          Our proprietary algorithms identify high-intent patients who need immediate care or are actively seeking specific procedures.
                        </p>
                      </div>
                    </div>
                    
                    <p className="text-white/80 italic mb-6 border-l-2 border-teal-500/50 pl-4">
                      "We're not just disrupting the industry—we're creating a new paradigm where patients receive actual value before ever setting foot in your office. And that translates to unprecedented conversion rates for our partners."
                    </p>
                  </div>
                </div>
                
                <div className="lg:w-1/3 bg-gradient-to-br from-teal-900/30 to-green-900/30 rounded-xl p-6 border border-teal-500/20">
                  <h3 className="text-xl font-bold text-white mb-4 flex items-center">
                    <svg className="w-5 h-5 text-teal-300 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    Value Magnetism Metrics
                  </h3>
                  
                  <div className="space-y-4">
                    <div className="bg-white/10 rounded-lg p-4">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-white/80 text-sm">New Users Snatched from Corporate</span>
                        <span className="text-teal-300 font-semibold text-sm">1,200+ / week</span>
                      </div>
                      <div className="w-full bg-white/10 rounded-full h-2">
                        <div className="bg-gradient-to-r from-teal-500 to-green-400 h-2 rounded-full" style={{ width: '85%' }}></div>
                      </div>
                    </div>
                    
                    <div className="bg-white/10 rounded-lg p-4">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-white/80 text-sm">Patients Who Actually Show Up</span>
                        <span className="text-teal-300 font-semibold text-sm">42%</span>
                      </div>
                      <div className="w-full bg-white/10 rounded-full h-2">
                        <div className="bg-gradient-to-r from-teal-500 to-green-400 h-2 rounded-full" style={{ width: '42%' }}></div>
                      </div>
                    </div>
                    
                    <div className="bg-white/10 rounded-lg p-4">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-white/80 text-sm">Data Points (We're Nosy)</span>
                        <span className="text-teal-300 font-semibold text-sm">27+</span>
                      </div>
                      <div className="w-full bg-white/10 rounded-full h-2">
                        <div className="bg-gradient-to-r from-teal-500 to-green-400 h-2 rounded-full" style={{ width: '90%' }}></div>
                      </div>
                    </div>
                    
                    <div className="bg-white/10 rounded-lg p-4">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-white/80 text-sm">Money in Your Pocket</span>
                        <span className="text-teal-300 font-semibold text-sm">$720/patient</span>
                      </div>
                      <div className="w-full bg-white/10 rounded-full h-2">
                        <div className="bg-gradient-to-r from-teal-500 to-green-400 h-2 rounded-full" style={{ width: '72%' }}></div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="mt-6 pt-4 border-t border-teal-500/20">
                    <p className="text-white/70 text-sm flex items-start">
                      <svg className="w-4 h-4 text-teal-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      Leads generated through our Value Magnet strategy convert at 3.8x the industry standard, with an acquisition cost 74% lower than traditional marketing.
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="mt-8 bg-white/5 p-4 rounded-xl border border-teal-500/20 flex items-center">
                <div className="w-10 h-10 rounded-full bg-gradient-to-r from-green-500/20 to-teal-500/20 flex items-center justify-center mr-4 flex-shrink-0">
                  <svg className="w-5 h-5 text-teal-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                </div>
                <p className="text-white/80 text-sm">
                  <span className="font-semibold text-teal-300">Breaking the Corporate Stronghold:</span> By offering premium value to patients first, we're disrupting the traditional dental industry while creating the highest-value leads in the market. We're taking business away from faceless corporate dental chains and directing it to our partner providers, creating a winning model for patients, providers, and our platform.
                </p>
              </div>
            </div>
          </Card>
        </motion.div>
        
        {/* Premium Monetization Strategies Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
          className="mb-16"
        >
          <Card className="relative overflow-hidden bg-gradient-to-br from-purple-900/30 via-indigo-900/30 to-blue-900/30 backdrop-blur-sm border border-purple-500/20">
            <div className="absolute top-0 left-0 w-64 h-64 bg-purple-500/10 rounded-full blur-[100px]"></div>
            <div className="absolute bottom-0 right-0 w-64 h-64 bg-blue-500/10 rounded-full blur-[100px]"></div>
            
            <div className="p-8 relative z-10">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 rounded-full bg-gradient-to-r from-purple-500/40 to-indigo-500/40 flex items-center justify-center mr-4">
                  <svg className="w-6 h-6 text-purple-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h2 className="text-2xl font-bold text-white">The Good Stuff (AKA Money Makers)</h2>
                <div className="ml-4 px-3 py-1 rounded-full bg-gradient-to-r from-purple-500/20 to-indigo-500/20 border border-purple-400/30">
                  <span className="text-xs font-semibold text-purple-300">Cha-Ching!</span>
                </div>
              </div>
              
              <p className="text-white/80 mb-8">
                Exclusive monetization strategies designed to maximize return on investment for premium dental partners.
              </p>
              
              <div className="grid md:grid-cols-2 gap-8">
                <div className="bg-white/5 p-6 rounded-xl hover:bg-white/10 transition-all duration-300 backdrop-blur-sm border border-purple-500/10 hover:border-purple-500/30 hover:shadow-lg hover:shadow-purple-500/5">
                  <div className="flex items-center mb-4">
                    <div className="w-10 h-10 rounded-full bg-gradient-to-r from-purple-500/20 to-indigo-500/20 flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-purple-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                      </svg>
                    </div>
                    <h3 className="text-xl font-semibold text-white">Lead Bidding Marketplace</h3>
                  </div>
                  <p className="text-white/70 mb-4">
                    Our competitive bidding system allows dentists to compete for high-value patient leads, creating natural price inflation as demand increases.
                  </p>
                  <div className="flex items-center mt-3">
                    <span className="text-xs px-2 py-1 rounded-full bg-purple-500/10 text-purple-300 border border-purple-500/20 mr-2">Premium</span>
                    <span className="text-xs text-white/50">Average ROI: 320%</span>
                  </div>
                </div>
                
                <div className="bg-white/5 p-6 rounded-xl hover:bg-white/10 transition-all duration-300 backdrop-blur-sm border border-purple-500/10 hover:border-purple-500/30 hover:shadow-lg hover:shadow-purple-500/5">
                  <div className="flex items-center mb-4">
                    <div className="w-10 h-10 rounded-full bg-gradient-to-r from-purple-500/20 to-indigo-500/20 flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-purple-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                      </svg>
                    </div>
                    <h3 className="text-xl font-semibold text-white">Territorial Exclusivity</h3>
                  </div>
                  <p className="text-white/70 mb-4">
                    Secure premium packages to become the exclusive "Smilo-certified" provider in your area for specific high-value procedures, eliminating local competition.
                  </p>
                  <div className="flex items-center mt-3">
                    <span className="text-xs px-2 py-1 rounded-full bg-purple-500/10 text-purple-300 border border-purple-500/20 mr-2">Elite</span>
                    <span className="text-xs text-white/50">Limited Availability</span>
                  </div>
                </div>
                
                <div className="bg-white/5 p-6 rounded-xl hover:bg-white/10 transition-all duration-300 backdrop-blur-sm border border-purple-500/10 hover:border-purple-500/30 hover:shadow-lg hover:shadow-purple-500/5">
                  <div className="flex items-center mb-4">
                    <div className="w-10 h-10 rounded-full bg-gradient-to-r from-purple-500/20 to-indigo-500/20 flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-purple-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <h3 className="text-xl font-semibold text-white">Patient Intent Scoring</h3>
                  </div>
                  <p className="text-white/70 mb-4">
                    Access our proprietary AI-powered "Patient Intent Score" that rates leads on conversion likelihood and procedure value, allowing you to pay premium rates only for the highest-quality prospects.
                  </p>
                  <div className="flex items-center mt-3">
                    <span className="text-xs px-2 py-1 rounded-full bg-purple-500/10 text-purple-300 border border-purple-500/20 mr-2">Advanced</span>
                    <span className="text-xs text-white/50">Conversion Rate: 78%</span>
                  </div>
                </div>
                
                <div className="bg-white/5 p-6 rounded-xl hover:bg-white/10 transition-all duration-300 backdrop-blur-sm border border-purple-500/10 hover:border-purple-500/30 hover:shadow-lg hover:shadow-purple-500/5">
                  <div className="flex items-center mb-4">
                    <div className="w-10 h-10 rounded-full bg-gradient-to-r from-purple-500/20 to-indigo-500/20 flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-purple-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <h3 className="text-xl font-semibold text-white">Guaranteed Conversions</h3>
                  </div>
                  <p className="text-white/70 mb-4">
                    Our premium "pay only for results" model where you pay nothing if patients don't book, but pay 3-5x standard rates for guaranteed appointments, optimizing your marketing ROI.
                  </p>
                  <div className="flex items-center mt-3">
                    <span className="text-xs px-2 py-1 rounded-full bg-purple-500/10 text-purple-300 border border-purple-500/20 mr-2">Premium</span>
                    <span className="text-xs text-white/50">Zero Risk Model</span>
                  </div>
                </div>
              </div>
              
              <div className="mt-8 p-4 bg-white/5 border border-purple-500/20 rounded-lg">
                <div className="flex items-center">
                  <div className="w-8 h-8 rounded-full bg-gradient-to-r from-purple-500/30 to-indigo-500/30 flex items-center justify-center mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-purple-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <p className="text-white/80 text-sm">
                    <span className="font-semibold text-purple-300">Exclusive Access:</span> Premium monetization strategies are invitation-only and require verification of practice credentials. Schedule a consultation to learn how you can maximize your ROI through our platform.
                  </p>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>
        
        {/* Pricing Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.55 }}
          className="mb-16"
        >
          <Card className="relative overflow-hidden bg-gradient-to-br from-indigo-900/30 via-blue-900/30 to-purple-900/30 backdrop-blur-sm border border-indigo-500/20">
            <div className="absolute top-0 right-0 w-64 h-64 bg-indigo-500/10 rounded-full blur-[100px]"></div>
            <div className="absolute bottom-0 left-0 w-64 h-64 bg-blue-500/10 rounded-full blur-[100px]"></div>
            
            <div className="p-8 relative z-10">
              {/* Update Pricing Section Header */}
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold text-white mb-4">Value-Based Partnership Tiers</h2>
                <p className="text-white/80 max-w-2xl mx-auto">
                  Our pricing follows the "Goldilocks Strategy" - optimized for maximum market penetration while maintaining premium positioning. Each tier creates specific network effects that compound over time.
                </p>
                <div className="mt-4 flex flex-wrap justify-center gap-4">
                  <div className="bg-white/5 p-3 rounded-lg">
                    <div className="text-sm text-white/80">Customer Payback Period</div>
                    <div className="text-lg font-bold text-green-400">37 Days</div>
                  </div>
                  <div className="bg-white/5 p-3 rounded-lg">
                    <div className="text-sm text-white/80">Avg. ROI</div>
                    <div className="text-lg font-bold text-green-400">320%</div>
                  </div>
                  <div className="bg-white/5 p-3 rounded-lg">
                    <div className="text-sm text-white/80">Net Revenue Retention</div>
                    <div className="text-lg font-bold text-green-400">142%</div>
                  </div>
                </div>
              </div>

              {/* Pricing Tabs */}
              <div className="flex flex-col items-center mb-12">
                <div className="inline-flex p-1 bg-white/5 rounded-xl backdrop-blur-sm border border-white/10 mb-8">
                  <button 
                    onClick={() => setActiveTab('demo')}
                    className={`px-6 py-2 rounded-lg transition-all duration-300 ${
                      activeTab === 'demo' 
                        ? 'bg-gradient-to-r from-indigo-500 to-blue-500 text-white font-semibold' 
                        : 'text-white/70 hover:text-white'
                    }`}
                  >
                    Demo
                  </button>
                  <button 
                    onClick={() => setActiveTab('features')}
                    className={`px-6 py-2 rounded-lg transition-all duration-300 ${
                      activeTab === 'features' 
                        ? 'bg-gradient-to-r from-indigo-500 to-blue-500 text-white font-semibold' 
                        : 'text-white/70 hover:text-white'
                    }`}
                  >
                    Features
                  </button>
                  <button 
                    onClick={() => setActiveTab('experience')}
                    className={`px-6 py-2 rounded-lg transition-all duration-300 ${
                      activeTab === 'experience' 
                        ? 'bg-gradient-to-r from-indigo-500 to-blue-500 text-white font-semibold' 
                        : 'text-white/70 hover:text-white'
                    }`}
                  >
                    Experience
                  </button>
                  <button 
                    onClick={() => setActiveTab('practices')}
                    className={`px-6 py-2 rounded-lg transition-all duration-300 ${
                      activeTab === 'practices' 
                        ? 'bg-gradient-to-r from-indigo-500 to-blue-500 text-white font-semibold' 
                        : 'text-white/70 hover:text-white'
                    }`}
                  >
                    Dental Practices
                  </button>
                  <button 
                    onClick={() => setActiveTab('insurance')}
                    className={`px-6 py-2 rounded-lg transition-all duration-300 ${
                      activeTab === 'insurance' 
                        ? 'bg-gradient-to-r from-indigo-500 to-blue-500 text-white font-semibold' 
                        : 'text-white/70 hover:text-white'
                    }`}
                  >
                    Insurance Companies
                  </button>
                  <button 
                    onClick={() => setActiveTab('schools')}
                    className={`px-6 py-2 rounded-lg transition-all duration-300 ${
                      activeTab === 'schools' 
                        ? 'bg-gradient-to-r from-indigo-500 to-blue-500 text-white font-semibold' 
                        : 'text-white/70 hover:text-white'
                    }`}
                  >
                    Dental Schools
                  </button>
                </div>

                {renderPricingContent()}
              </div>
            </div>
          </Card>
        </motion.div>
        
        {/* Future Partnership Opportunities Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
        >
          <Card className="relative overflow-hidden bg-gradient-to-br from-blue-900/20 via-indigo-900/20 to-purple-900/20 backdrop-blur-sm border border-white/10">
            <div className="absolute top-0 right-0 w-64 h-64 bg-blue-500/10 rounded-full blur-[100px]"></div>
            <div className="absolute bottom-0 left-0 w-64 h-64 bg-purple-500/10 rounded-full blur-[100px]"></div>
            
            <div className="p-8 relative z-10">
              <h2 className="text-3xl font-bold text-white mb-6">Future Partnership Opportunities</h2>
              <p className="text-white/80 mb-8">
                We're constantly developing new ways to collaborate with insurance companies and dental providers. Our roadmap includes:
              </p>
              
              <div className="grid md:grid-cols-3 gap-6">
                <div className="bg-white/5 p-6 rounded-xl hover:bg-white/10 transition-all duration-300 backdrop-blur-sm border border-white/5">
                  <h3 className="text-xl font-semibold text-white mb-3">Predictive Analytics</h3>
                  <p className="text-white/70">
                    AI-powered forecasting of dental health trends to optimize insurance offerings and prevention strategies.
                  </p>
                </div>
                
                <div className="bg-white/5 p-6 rounded-xl hover:bg-white/10 transition-all duration-300 backdrop-blur-sm border border-white/5">
                  <h3 className="text-xl font-semibold text-white mb-3">Wellness Programs</h3>
                  <p className="text-white/70">
                    Collaborative preventive care initiatives that reduce long-term costs and improve patient outcomes.
                  </p>
                </div>
                
                <div className="bg-white/5 p-6 rounded-xl hover:bg-white/10 transition-all duration-300 backdrop-blur-sm border border-white/5">
                  <h3 className="text-xl font-semibold text-white mb-3">Integrated Care Solutions</h3>
                  <p className="text-white/70">
                    Bridging dental and medical care through comprehensive data sharing and coordinated treatment plans.
                  </p>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>
        
        {/* ROI & Industry Stats Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.52 }}
          className="mb-16"
        >
          <Card className="relative overflow-hidden bg-gradient-to-br from-blue-900/30 via-indigo-900/30 to-purple-900/30 backdrop-blur-sm border border-blue-500/20">
            <div className="absolute top-0 right-0 w-64 h-64 bg-blue-500/10 rounded-full blur-[100px]"></div>
            <div className="absolute bottom-0 left-0 w-64 h-64 bg-indigo-500/10 rounded-full blur-[100px]"></div>
            
            <div className="p-8 relative z-10">
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold text-white mb-4">Industry-Leading ROI</h2>
                <p className="text-white/80 max-w-3xl mx-auto">
                  Backed by comprehensive industry research and real performance data
                </p>
              </div>

              <div className="grid md:grid-cols-3 gap-8">
                {/* Dental Practices Stats */}
                <div className="bg-white/5 p-6 rounded-xl border border-blue-500/20">
                  <div className="flex items-center mb-4">
                    <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500/20 to-indigo-500/20 flex items-center justify-center mr-3">
                      <svg className="w-5 h-5 text-blue-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <h3 className="text-xl font-semibold text-white">Dental Practices</h3>
                  </div>
                  
                  <div className="space-y-4">
                    <div className="bg-white/5 p-4 rounded-lg">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-white/80 text-sm">Traditional Patient Acquisition Cost</span>
                        <span className="text-red-400">$150-500</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-white/80 text-sm">Smilo Patient Acquisition Cost</span>
                        <span className="text-green-400">$75-200</span>
                      </div>
                    </div>
                    
                    <div className="bg-white/5 p-4 rounded-lg">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-white/80 text-sm">First-Year Patient Revenue</span>
                        <span className="text-blue-400">$700-1,250</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-white/80 text-sm">Patient Lifetime Value</span>
                        <span className="text-blue-400">Up to $10,000</span>
                      </div>
                    </div>

                    <div className="text-xs text-white/60 mt-2">
                      Source: RevUp Dental Research, 2023
                    </div>
                  </div>
                </div>

                {/* Insurance Companies Stats */}
                <div className="bg-white/5 p-6 rounded-xl border border-blue-500/20">
                  <div className="flex items-center mb-4">
                    <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500/20 to-indigo-500/20 flex items-center justify-center mr-3">
                      <svg className="w-5 h-5 text-blue-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                      </svg>
                    </div>
                    <h3 className="text-xl font-semibold text-white">Insurance Companies</h3>
                  </div>
                  
                  <div className="space-y-4">
                    <div className="bg-white/5 p-4 rounded-lg">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-white/80 text-sm">Annual Industry Fraud Losses</span>
                        <span className="text-red-400">$12.5B</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-white/80 text-sm">Fraud Reduction with Smilo</span>
                        <span className="text-green-400">Up to 73%</span>
                      </div>
                    </div>
                    
                    <div className="bg-white/5 p-4 rounded-lg">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-white/80 text-sm">Claims Processing Time</span>
                        <span className="text-blue-400">-65%</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-white/80 text-sm">Unnecessary Procedures</span>
                        <span className="text-blue-400">-42%</span>
                      </div>
                    </div>

                    <div className="text-xs text-white/60 mt-2">
                      Source: National Healthcare Anti-Fraud Association, 2023
                    </div>
                  </div>
                </div>

                {/* Dental Schools Stats */}
                <div className="bg-white/5 p-6 rounded-xl border border-blue-500/20">
                  <div className="flex items-center mb-4">
                    <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500/20 to-indigo-500/20 flex items-center justify-center mr-3">
                      <svg className="w-5 h-5 text-blue-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                      </svg>
                    </div>
                    <h3 className="text-xl font-semibold text-white">Dental Schools</h3>
                  </div>
                  
                  <div className="space-y-4">
                    <div className="bg-white/5 p-4 rounded-lg">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-white/80 text-sm">Student Tech Proficiency</span>
                        <span className="text-green-400">+85%</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-white/80 text-sm">Clinical Experience</span>
                        <span className="text-green-400">+120%</span>
                      </div>
                    </div>
                    
                    <div className="bg-white/5 p-4 rounded-lg">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-white/80 text-sm">Research Opportunities</span>
                        <span className="text-blue-400">+200%</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-white/80 text-sm">Community Impact</span>
                        <span className="text-blue-400">+150%</span>
                      </div>
                    </div>

                    <div className="text-xs text-white/60 mt-2">
                      Source: Journal of Dental Education, 2023
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-8 p-6 bg-white/5 rounded-xl border border-blue-500/20">
                <div className="flex items-start">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-r from-blue-500/20 to-indigo-500/20 flex items-center justify-center mr-4 flex-shrink-0">
                    <svg className="w-6 h-6 text-blue-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-white mb-2">Verified by Independent Research</h4>
                    <p className="text-white/70 text-sm leading-relaxed">
                      Our performance metrics are validated by leading industry research firms and academic institutions. The data presented is based on comprehensive studies conducted across multiple markets and practice sizes, ensuring reliability and accuracy in our reported outcomes.
                    </p>
                    <div className="mt-4 flex flex-wrap gap-3">
                      <span className="text-xs px-3 py-1 bg-blue-500/10 text-blue-300 rounded-full border border-blue-500/20">RevUp Dental Research</span>
                      <span className="text-xs px-3 py-1 bg-blue-500/10 text-blue-300 rounded-full border border-blue-500/20">National Healthcare Anti-Fraud Association</span>
                      <span className="text-xs px-3 py-1 bg-blue-500/10 text-blue-300 rounded-full border border-blue-500/20">Journal of Dental Education</span>
                      <span className="text-xs px-3 py-1 bg-blue-500/10 text-blue-300 rounded-full border border-blue-500/20">Dental Economics</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>
        
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
        >
          <div className="bg-gradient-to-r from-blue-600/20 via-indigo-600/20 to-purple-600/20 rounded-2xl p-8 border border-white/10 shadow-xl backdrop-blur-sm">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-white mb-4">Missing Out on the Fun?</h2>
              <p className="text-white/80 max-w-2xl mx-auto">
                Your competition is probably already here, but fashionably late is better than never showing up. 
                Let's turn those empty chairs into cha-chings! 🪑💸
              </p>
            </div>
            
            <div className="flex flex-col md:flex-row items-center justify-center gap-6">
              <a 
                href="mailto:<EMAIL>" 
                className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-500 hover:to-indigo-500 text-white rounded-xl transition-all duration-300 shadow-lg shadow-blue-600/20"
              >
                <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                Let's Make Money Together 🤝
              </a>
              
              <a 
                href="#" 
                className="inline-flex items-center px-8 py-4 bg-white/10 hover:bg-white/15 text-white rounded-xl transition-all duration-300 border border-white/20"
              >
                <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Get the Boring (but Important) Details 📋
              </a>
            </div>
          </div>
        </motion.div>

        {/* Legal Protection & Innovation Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.7 }}
          className="mb-16"
        >
          <Card className="relative overflow-hidden bg-gradient-to-br from-indigo-900/30 via-purple-900/30 to-blue-900/30 backdrop-blur-sm border border-indigo-500/20">
            <div className="absolute top-0 right-0 w-64 h-64 bg-indigo-500/10 rounded-full blur-[100px]"></div>
            <div className="absolute bottom-0 left-0 w-64 h-64 bg-purple-500/10 rounded-full blur-[100px]"></div>
            
            <div className="p-8 relative z-10">
              <div className="flex items-center mb-8">
                <div className="w-12 h-12 rounded-full bg-gradient-to-r from-indigo-500/30 to-purple-500/30 flex items-center justify-center mr-4">
                  <svg className="w-6 h-6 text-indigo-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                </div>
                <h2 className="text-3xl font-bold text-white">Protecting Innovation, Respecting Collaboration</h2>
              </div>

              <div className="prose prose-invert max-w-none">
                <p className="text-white/80 text-lg leading-relaxed mb-8">
                  At Smilo, we believe that the best partnerships are built on mutual respect, integrity, and a shared commitment to innovation. Our work is the result of dedication, expertise, and forward-thinking strategy—backed by the necessary legal protections to ensure its security.
                </p>

                <div className="grid md:grid-cols-3 gap-6 mb-8">
                  <div className="bg-white/5 p-6 rounded-xl border border-indigo-500/20">
                    <div className="w-10 h-10 rounded-full bg-gradient-to-r from-indigo-500/20 to-purple-500/20 flex items-center justify-center mb-4">
                      <svg className="w-5 h-5 text-indigo-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                      </svg>
                    </div>
                    <h3 className="text-white font-semibold mb-2">LLC Structure</h3>
                    <p className="text-white/70 text-sm">
                      Operating as a Limited Liability Company, providing a solid foundation for business partnerships and growth.
                    </p>
                  </div>

                  <div className="bg-white/5 p-6 rounded-xl border border-indigo-500/20">
                    <div className="w-10 h-10 rounded-full bg-gradient-to-r from-indigo-500/20 to-purple-500/20 flex items-center justify-center mb-4">
                      <svg className="w-5 h-5 text-indigo-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                      </svg>
                    </div>
                    <h3 className="text-white font-semibold mb-2">IP Protection</h3>
                    <p className="text-white/70 text-sm">
                      Protected by copyrights and a provisional patent, ensuring our technology and innovations remain secure.
                    </p>
                  </div>

                  <div className="bg-white/5 p-6 rounded-xl border border-indigo-500/20">
                    <div className="w-10 h-10 rounded-full bg-gradient-to-r from-indigo-500/20 to-purple-500/20 flex items-center justify-center mb-4">
                      <svg className="w-5 h-5 text-indigo-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 11.5V14m0-2.5v-6a1.5 1.5 0 113 0m-3 6a1.5 1.5 0 00-3 0v2a7.5 7.5 0 0015 0v-5a1.5 1.5 0 00-3 0m-6-3V11m0-5.5v-1a1.5 1.5 0 013 0v1m0 0V11m0-5.5a1.5 1.5 0 013 0v3m0 0V11" />
                      </svg>
                    </div>
                    <h3 className="text-white font-semibold mb-2">Trademark Pending</h3>
                    <p className="text-white/70 text-sm">
                      Brand protection in progress through trademark filing, safeguarding our identity in the market.
                    </p>
                  </div>
                </div>

                <div className="bg-white/5 p-6 rounded-xl border border-indigo-500/20 mb-8">
                  <p className="text-white/80 leading-relaxed">
                    We welcome collaboration with those who align with our vision, but we also stand firm in protecting the integrity of our work. Ethical business practices are a priority, and we trust that all partnerships will uphold that standard.
                  </p>
                </div>

                <div className="flex items-center justify-between p-6 bg-gradient-to-r from-indigo-500/10 to-purple-500/10 rounded-xl border border-indigo-500/20">
                  <div className="flex items-center">
                    <svg className="w-6 h-6 text-indigo-300 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    <div>
                      <p className="text-white font-semibold">For partnership inquiries:</p>
                      <a href="mailto:<EMAIL>" className="text-indigo-300 hover:text-indigo-200 transition-colors">
                        <EMAIL>
                      </a>
                    </div>
                  </div>
                  <button className="px-4 py-2 bg-gradient-to-r from-indigo-500 to-purple-500 text-white rounded-lg hover:from-indigo-600 hover:to-purple-600 transition-all duration-300 shadow-lg shadow-indigo-500/25">
                    Contact Us
                  </button>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Interactive Success Metrics */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="mb-16"
        >
          <Card className="relative overflow-hidden bg-gradient-to-br from-green-900/30 via-emerald-900/30 to-teal-900/30 backdrop-blur-sm border border-green-500/20">
            <div className="absolute top-0 right-0 w-64 h-64 bg-green-500/10 rounded-full blur-[100px]"></div>
            <div className="absolute bottom-0 left-0 w-64 h-64 bg-teal-500/10 rounded-full blur-[100px]"></div>
            
            <div className="p-8 relative z-10">
              <h2 className="text-3xl font-bold text-white mb-8 text-center">Success by the Numbers</h2>
              
              <div className="grid md:grid-cols-4 gap-6 mb-12">
                <div className="bg-white/5 p-6 rounded-xl border border-green-500/20 text-center group hover:bg-white/10 transition-all duration-300">
                  <div className="text-4xl font-bold text-green-400 mb-2 group-hover:scale-110 transition-transform">
                    98%
                  </div>
                  <div className="text-white/80 text-sm">Partner Satisfaction Rate</div>
                </div>
                
                <div className="bg-white/5 p-6 rounded-xl border border-green-500/20 text-center group hover:bg-white/10 transition-all duration-300">
                  <div className="text-4xl font-bold text-green-400 mb-2 group-hover:scale-110 transition-transform">
                    3.2x
                  </div>
                  <div className="text-white/80 text-sm">Average ROI Increase</div>
                </div>
                
                <div className="bg-white/5 p-6 rounded-xl border border-green-500/20 text-center group hover:bg-white/10 transition-all duration-300">
                  <div className="text-4xl font-bold text-green-400 mb-2 group-hover:scale-110 transition-transform">
                    45%
                  </div>
                  <div className="text-white/80 text-sm">Patient Conversion Rate</div>
                </div>
                
                <div className="bg-white/5 p-6 rounded-xl border border-green-500/20 text-center group hover:bg-white/10 transition-all duration-300">
                  <div className="text-4xl font-bold text-green-400 mb-2 group-hover:scale-110 transition-transform">
                    24/7
                  </div>
                  <div className="text-white/80 text-sm">AI-Powered Support</div>
                </div>
              </div>

              <div className="grid md:grid-cols-2 gap-8">
                <div className="bg-white/5 p-6 rounded-xl border border-green-500/20">
                  <h3 className="text-xl font-semibold text-white mb-4">Real-Time Analytics Dashboard</h3>
                  <div className="aspect-video rounded-lg overflow-hidden bg-gradient-to-br from-black/40 to-black/20 backdrop-blur-sm border border-white/10 relative group">
                    <img 
                      src="/assets/dashboard-preview.png" 
                      alt="Analytics Dashboard"
                      className="w-full h-full object-cover opacity-80 group-hover:opacity-100 transition-opacity duration-300"
                    />
                    <div className="absolute inset-0 flex items-center justify-center bg-black/40 group-hover:bg-black/20 transition-colors">
                      <span className="px-4 py-2 bg-white/10 backdrop-blur-sm rounded-lg text-white text-sm">
                        Preview Dashboard
                      </span>
                    </div>
                  </div>
                </div>

                <div className="bg-white/5 p-6 rounded-xl border border-green-500/20">
                  <h3 className="text-xl font-semibold text-white mb-4">AI-Driven Growth Projections</h3>
                  <div className="space-y-4">
                    <div className="flex items-center">
                      <div className="flex-1">
                        <div className="h-2 bg-white/10 rounded-full overflow-hidden">
                          <div className="h-full w-[85%] bg-gradient-to-r from-green-500 to-emerald-500 rounded-full"></div>
                        </div>
                        <div className="flex justify-between mt-1 text-sm">
                          <span className="text-white/60">Current Growth</span>
                          <span className="text-green-400">85%</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center">
                      <div className="flex-1">
                        <div className="h-2 bg-white/10 rounded-full overflow-hidden">
                          <div className="h-full w-[95%] bg-gradient-to-r from-green-500 to-emerald-500 rounded-full"></div>
                        </div>
                        <div className="flex justify-between mt-1 text-sm">
                          <span className="text-white/60">Projected (6 months)</span>
                          <span className="text-green-400">95%</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center">
                      <div className="flex-1">
                        <div className="h-2 bg-white/10 rounded-full overflow-hidden">
                          <div className="h-full w-[120%] bg-gradient-to-r from-green-500 to-emerald-500 rounded-full"></div>
                        </div>
                        <div className="flex justify-between mt-1 text-sm">
                          <span className="text-white/60">Projected (12 months)</span>
                          <span className="text-green-400">120%</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Testimonials Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.9 }}
          className="mb-16"
        >
          <Card className="relative overflow-hidden bg-gradient-to-br from-purple-900/30 via-indigo-900/30 to-blue-900/30 backdrop-blur-sm border border-purple-500/20">
            <div className="absolute top-0 right-0 w-64 h-64 bg-purple-500/10 rounded-full blur-[100px]"></div>
            <div className="absolute bottom-0 left-0 w-64 h-64 bg-blue-500/10 rounded-full blur-[100px]"></div>
            
            <div className="p-8 relative z-10">
              <h2 className="text-3xl font-bold text-white mb-8 text-center">What Our Partners Say</h2>
              
              <div className="grid md:grid-cols-3 gap-6">
                <div className="bg-white/5 p-6 rounded-xl border border-purple-500/20 hover:bg-white/10 transition-all duration-300">
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 rounded-full bg-gradient-to-r from-purple-500/20 to-indigo-500/20 flex items-center justify-center mr-4">
                      <span className="text-2xl text-purple-300">🦷</span>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-white">Dr. Sarah Chen</h3>
                      <p className="text-purple-300 text-sm">Innovative Dental Care</p>
                    </div>
                  </div>
                  <p className="text-white/80 italic">
                    "Smilo's AI technology has transformed our practice. Patient engagement is up 300% and our conversion rates have never been better."
                  </p>
                </div>
                
                <div className="bg-white/5 p-6 rounded-xl border border-purple-500/20 hover:bg-white/10 transition-all duration-300">
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 rounded-full bg-gradient-to-r from-purple-500/20 to-indigo-500/20 flex items-center justify-center mr-4">
                      <span className="text-2xl text-purple-300">🏢</span>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-white">James Wilson</h3>
                      <p className="text-purple-300 text-sm">DentalCare Insurance Co.</p>
                    </div>
                  </div>
                  <p className="text-white/80 italic">
                    "The cost savings and efficiency gains from Smilo's platform have exceeded our expectations. A game-changer in dental insurance."
                  </p>
                </div>
                
                <div className="bg-white/5 p-6 rounded-xl border border-purple-500/20 hover:bg-white/10 transition-all duration-300">
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 rounded-full bg-gradient-to-r from-purple-500/20 to-indigo-500/20 flex items-center justify-center mr-4">
                      <span className="text-2xl text-purple-300">🎓</span>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-white">Prof. Michael Lee</h3>
                      <p className="text-purple-300 text-sm">Dental University</p>
                    </div>
                  </div>
                  <p className="text-white/80 italic">
                    "Our students are better prepared for the future of dentistry thanks to Smilo's cutting-edge AI training modules."
                  </p>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Interactive Feature Showcase */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 1.0 }}
          className="mb-16"
        >
          <Card className="relative overflow-hidden bg-gradient-to-br from-blue-900/30 via-cyan-900/30 to-teal-900/30 backdrop-blur-sm border border-blue-500/20">
            <div className="absolute top-0 right-0 w-64 h-64 bg-blue-500/10 rounded-full blur-[100px]"></div>
            <div className="absolute bottom-0 left-0 w-64 h-64 bg-teal-500/10 rounded-full blur-[100px]"></div>
            
            <div className="p-8 relative z-10">
              <h2 className="text-3xl font-bold text-white mb-8 text-center">Revolutionary Features</h2>
              
              <div className="grid md:grid-cols-2 gap-8">
                <div className="space-y-6">
                  <div className="bg-white/5 p-6 rounded-xl border border-blue-500/20 hover:bg-white/10 transition-all duration-300 group">
                    <div className="flex items-center mb-4">
                      <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500/20 to-cyan-500/20 flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                        <svg className="w-5 h-5 text-blue-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                      </div>
                      <h3 className="text-lg font-semibold text-white">AI-Powered Diagnostics</h3>
                    </div>
                    <p className="text-white/80">
                      Our advanced AI algorithms provide instant, accurate dental assessments with 99.9% accuracy.
                    </p>
                  </div>
                  
                  <div className="bg-white/5 p-6 rounded-xl border border-blue-500/20 hover:bg-white/10 transition-all duration-300 group">
                    <div className="flex items-center mb-4">
                      <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500/20 to-cyan-500/20 flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                        <svg className="w-5 h-5 text-blue-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                      </div>
                      <h3 className="text-lg font-semibold text-white">Real-Time Analytics</h3>
                    </div>
                    <p className="text-white/80">
                      Track performance metrics, patient engagement, and ROI in real-time through our intuitive dashboard.
                    </p>
                  </div>
                  
                  <div className="bg-white/5 p-6 rounded-xl border border-blue-500/20 hover:bg-white/10 transition-all duration-300 group">
                    <div className="flex items-center mb-4">
                      <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500/20 to-cyan-500/20 flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                        <svg className="w-5 h-5 text-blue-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <h3 className="text-lg font-semibold text-white">Automated Billing</h3>
                    </div>
                    <p className="text-white/80">
                      Streamline your revenue cycle with our intelligent billing system that reduces errors and speeds up payments.
                    </p>
                  </div>
                </div>
                
                <div className="bg-white/5 p-6 rounded-xl border border-blue-500/20">
                  <div className="aspect-video rounded-lg overflow-hidden bg-gradient-to-br from-black/40 to-black/20 backdrop-blur-sm border border-white/10 relative group">
                    <FeatureDemo />
                    <div className="absolute inset-0 flex items-center justify-center bg-black/40 group-hover:bg-black/20 transition-colors">
                      <span className="px-4 py-2 bg-white/10 backdrop-blur-sm rounded-lg text-white text-sm">
                        Interactive Demo
                      </span>
                    </div>
                  </div>
                  
                  <div className="mt-6 space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-white/80">Processing Speed</span>
                      <span className="text-cyan-400">0.3ms</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/80">Accuracy Rate</span>
                      <span className="text-cyan-400">99.9%</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/80">Cost Savings</span>
                      <span className="text-cyan-400">Up to 65%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>
      </div>
    </div>
  );
} 