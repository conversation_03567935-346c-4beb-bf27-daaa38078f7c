import React from 'react';
import ContentSection from './ContentSection';
import { ABOUT_INFO } from '../../lib/constants/about';
import { motion } from 'framer-motion';

export default function AboutSection() {
  return (
    <ContentSection
      id="about"
      title={
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7 }}
          className="relative"
        >
          <div className="text-center mb-6">
            <h2 className="text-6xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-indigo-400 to-purple-500 inline-block">
              <PERSON>
            </h2>
            <motion.div 
              className="h-1 w-48 mx-auto mt-2 rounded-full bg-gradient-to-r from-blue-400 via-indigo-400 to-purple-500"
              initial={{ width: "0%" }}
              animate={{ width: "30%" }}
              transition={{ delay: 0.5, duration: 0.8 }}
            ></motion.div>
            <p className="text-xl text-blue-300 mt-2 tracking-wide">Founder & Visionary</p>
          </div>
        </motion.div>
      }
      className="bg-gradient-to-b from-gray-900/50 to-blue-900/20 backdrop-blur-sm"
      titleClassName="!p-0"
    >
      <div className="max-w-5xl mx-auto">
        <div className="grid md:grid-cols-5 gap-8 items-center">
          {/* Left column: Image */}
          <div className="md:col-span-2">
            <motion.div 
              className="relative rounded-xl overflow-hidden shadow-xl"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.7, delay: 0.2 }}
            >
              {/* Subtle gradient border */}
              <div className="absolute inset-0 bg-gradient-to-br from-blue-500/40 to-purple-500/40 p-0.5 rounded-xl">
                <div className="absolute inset-0 bg-blue-900/30 backdrop-blur-sm rounded-xl"></div>
              </div>
              
              {/* Image container */}
              <div className="relative rounded-xl overflow-hidden">
                <img
                  src={import.meta.env.BASE_URL + ABOUT_INFO.image}
                  alt={ABOUT_INFO.name}
                  className="w-full object-cover aspect-[3/4]"
                />
                
                {/* Decorative overlay elements */}
                <div className="absolute inset-0 bg-gradient-to-t from-blue-900/60 to-transparent"></div>
                
                {/* Corner accent */}
                <div className="absolute -bottom-2 -right-2 w-16 h-16 bg-blue-500/30 rounded-full blur-xl"></div>
              </div>
            </motion.div>
          </div>
          
          {/* Right column: Content */}
          <motion.div 
            className="md:col-span-3 space-y-5"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.7, delay: 0.3 }}
          >
            <p className="text-lg text-white/90 leading-relaxed">
              {ABOUT_INFO.description}
            </p>
            
            <div className="grid sm:grid-cols-2 gap-x-8 gap-y-6 mt-6">
              {ABOUT_INFO.credentials && (
                <div className="space-y-3">
                  <h4 className="text-lg font-medium text-blue-300 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    Achievements & Roles
                  </h4>
                  <ul className="space-y-2">
                    {ABOUT_INFO.credentials.map((credential, index) => (
                      <li key={index} className="text-white/80 flex items-start">
                        <span className="inline-block w-1.5 h-1.5 rounded-full bg-blue-400 mt-1.5 mr-2 flex-shrink-0"></span>
                        <span>{credential}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {ABOUT_INFO.leadership && (
                <div className="space-y-3">
                  <h4 className="text-lg font-medium text-blue-300 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
                    </svg>
                    Leadership & Impact
                  </h4>
                  <ul className="space-y-2">
                    {ABOUT_INFO.leadership.map((item, index) => (
                      <li key={index} className="text-white/80 flex items-start">
                        <span className="inline-block w-1.5 h-1.5 rounded-full bg-purple-400 mt-1.5 mr-2 flex-shrink-0"></span>
                        <span>{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>

            {ABOUT_INFO.contact && (
              <div className="pt-5 mt-5 border-t border-white/10">
                <h4 className="text-lg font-medium text-blue-300 flex items-center mb-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                  </svg>
                  Get in Touch
                </h4>
                <div className="flex space-x-4">
                  <a 
                    href={`mailto:${ABOUT_INFO.contact.email}`}
                    className="px-4 py-2 bg-blue-600/30 hover:bg-blue-600/40 text-white rounded-lg transition-colors flex items-center"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                      <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                    </svg>
                    Email
                  </a>
                  {ABOUT_INFO.contact.linkedin && (
                    <a 
                      href={ABOUT_INFO.contact.linkedin}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="px-4 py-2 bg-blue-600/30 hover:bg-blue-600/40 text-white rounded-lg transition-colors flex items-center"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/>
                      </svg>
                      LinkedIn
                    </a>
                  )}
                </div>
              </div>
            )}
          </motion.div>
        </div>
      </div>
    </ContentSection>
  );
}