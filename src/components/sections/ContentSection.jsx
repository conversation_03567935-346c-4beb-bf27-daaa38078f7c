import React from 'react';

export default function ContentSection({ 
  id, 
  title, 
  subtitle, 
  children,
  className = "" 
}) {
  return (
    <section id={id} className={`py-24 ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {(title || subtitle) && (
          <div className="text-center mb-12">
            {title && (
              <h2 className="text-3xl font-bold text-white mb-4">{title}</h2>
            )}
            {subtitle && (
              <div className="text-xl text-blue-100/80">{subtitle}</div>
            )}
          </div>
        )}
        {children}
      </div>
    </section>
  );
}