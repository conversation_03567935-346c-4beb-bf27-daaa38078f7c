import React, { useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import { ABOUT_CONTENT } from '../../../lib/constants/about';

const cardVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 }
};

export default function WhyChooseSection() {
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, { once: true, margin: "-100px" });

  return (
    <motion.section
      ref={sectionRef}
      className="py-16 bg-white/5"
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
      variants={{
        visible: {
          transition: {
            staggerChildren: 0.2
          }
        }
      }}
    >
      <div className="max-w-4xl mx-auto px-4">
        <h2 className="text-3xl font-bold text-white mb-12">{ABOUT_CONTENT.whyChoose.title}</h2>
        
        <div className="grid md:grid-cols-3 gap-8">
          {ABOUT_CONTENT.whyChoose.reasons.map((reason, index) => (
            <motion.div
              key={index}
              className="bg-white/5 rounded-lg p-6 backdrop-blur-sm hover:bg-white/10 transition-colors duration-300"
              variants={cardVariants}
              transition={{ duration: 0.4 }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <h3 className="text-xl font-semibold text-white mb-4">{reason.title}</h3>
              <p className="text-white/80">{reason.description}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </motion.section>
  );
}