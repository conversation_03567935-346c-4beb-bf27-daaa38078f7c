import React from 'react';
import { motion } from 'framer-motion';
import { ABOUT_CONTENT } from '../../../lib/constants/about';
import { useParallax } from '../../../lib/hooks/useParallax';
import { useAnimations } from '../../../contexts/AnimationContext';

export default function WelcomeSection() {
  // Get animation enablement status
  const { animationsEnabled } = useAnimations();
  
  // Only enable parallax if animations are enabled
  useParallax(animationsEnabled);

  return (
    <section className="py-16">
      <div className="max-w-4xl mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-4xl font-bold text-white mb-8">
            {ABOUT_CONTENT.welcome.title}
          </h1>
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3, duration: 0.6 }}
            className="text-lg text-white/80 leading-relaxed"
          >
            {ABOUT_CONTENT.welcome.content}
          </motion.p>
        </motion.div>
      </div>
    </section>
  );
}