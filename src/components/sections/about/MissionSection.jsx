import React from 'react';
import { ABOUT_CONTENT } from '../../../lib/constants/about';

export default function MissionSection() {
  return (
    <section className="py-16 bg-white/5">
      <div className="max-w-4xl mx-auto px-4">
        <h2 className="text-3xl font-bold text-white mb-8">{ABOUT_CONTENT.mission.title}</h2>
        
        <div className="space-y-12">
          <div className="grid md:grid-cols-3 gap-8">
            {ABOUT_CONTENT.mission.mission.map((item, index) => (
              <div key={index} className="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10 hover:border-blue-400/30 transition-all duration-300">
                <div className="text-3xl mb-4">{['🌟', '📚', '🤝'][index]}</div>
                <h3 className="text-xl font-semibold text-white mb-4">{item.title}</h3>
                <p className="text-white/80 leading-relaxed">{item.content}</p>
              </div>
            ))}
          </div>

          <div>
            <h3 className="text-2xl font-semibold text-white mb-4">Our Vision</h3>
            <div className="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10">
              <p className="text-lg text-white/80 leading-relaxed">{ABOUT_CONTENT.mission.vision}</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}