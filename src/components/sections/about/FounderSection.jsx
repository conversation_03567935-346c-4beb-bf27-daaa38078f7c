import React, { useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import { ABOUT_CONTENT } from '../../../lib/constants/about';

export default function FounderSection() {
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, { once: true, margin: "-100px" });

  return (
    <motion.section
      ref={sectionRef}
      className="py-16"
      initial={{ opacity: 0 }}
      animate={isInView ? { opacity: 1 } : { opacity: 0 }}
      transition={{ duration: 0.6 }}
    >
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
          <div className="space-y-6">
            <h2 className="text-3xl font-bold">{ABOUT_CONTENT.founder.title}</h2>
            <p className="text-gray-600">{ABOUT_CONTENT.founder.description}</p>
          </div>
          <div className="relative">
            <img
              src={ABOUT_CONTENT.founder.image}
              alt={ABOUT_CONTENT.founder.name}
              className="rounded-lg shadow-lg w-full"
            />
          </div>
        </div>
      </div>
    </motion.section>
  );
}