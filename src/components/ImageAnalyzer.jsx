import React, { useState } from 'react';
import { useUser } from '../contexts/UserContext';
import { useImageAnalysis } from '../lib/hooks/useImageAnalysis';
import ImageUpload from './ImageUpload';
import ImageAnalysisDisplay from './ImageAnalysisDisplay';
import AuthPrompt from './auth/AuthPrompt';
import { motion, AnimatePresence } from 'framer-motion';

export default function ImageAnalyzer() {
  const { user } = useUser();
  const { analyze, analyzing, results, error, clearResults } = useImageAnalysis(user?.id);
  const [currentImage, setCurrentImage] = useState(null);

  const handleImageUpload = async (imageData) => {
    try {
      setCurrentImage(imageData);
      clearResults();
      await analyze(imageData);
    } catch (err) {
      console.error('Analysis error:', err);
    }
  };

  if (!user) {
    return <AuthPrompt message="Sign in to analyze dental images" />;
  }

  return (
    <div className="space-y-6">
      <div className="bg-gradient-to-br from-blue-900/20 to-purple-900/20 backdrop-blur-sm rounded-xl p-6 border border-white/10">
        <h3 className="text-xl font-semibold text-white mb-4">
          Dental Image Analysis
        </h3>
        
        <p className="text-white/80 mb-6">
          Upload a dental image for AI-powered analysis. Our system will analyze the image and provide detailed insights.
        </p>

        <ImageUpload onImageUpload={handleImageUpload} />
      </div>

      <AnimatePresence mode="sync">
        {currentImage && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10"
          >
            <div className="aspect-video rounded-lg overflow-hidden bg-black/20">
              <img
                src={currentImage}
                alt="Uploaded dental image"
                className="w-full h-full object-contain"
              />
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      <ImageAnalysisDisplay 
        results={results}
        analyzing={analyzing}
        error={error}
      />
    </div>
  );
}