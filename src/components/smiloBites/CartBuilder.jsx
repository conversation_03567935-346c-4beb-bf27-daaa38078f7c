import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import deepseekService from '../../services/deepseekService';

const CartBuilder = ({ userData, supabase }) => {
  const [cartItems, setCartItems] = useState([]);
  const [cartTotal, setCartTotal] = useState(0);
  const [cartName, setCartName] = useState('My Weekly Cart');
  const [isLoading, setIsLoading] = useState(false);
  const [feedback, setFeedback] = useState('');
  const [showFeedback, setShowFeedback] = useState(false);

  // Update cart total when items change
  useEffect(() => {
    const total = cartItems.reduce((sum, item) => sum + (parseFloat(item.price) || 0), 0);
    setCartTotal(total.toFixed(2));
  }, [cartItems]);

  // Add item to cart
  const addItem = (item) => {
    setCartItems(prev => [...prev, { ...item, id: Date.now().toString() }]);
    
    // Show feedback from DeepSeek if the item is tooth-friendly
    if (item.benefit && item.benefit.includes('calcium') || item.benefit.includes('enamel')) {
      setFeedback(`Great choice! ${item.name} helps support strong teeth.`);
      setShowFeedback(true);
      setTimeout(() => setShowFeedback(false), 4000);
    }
  };

  // Remove item from cart
  const removeItem = (itemId) => {
    setCartItems(prev => prev.filter(item => item.id !== itemId));
  };

  // Handle drag and drop reordering
  const handleDragEnd = (result) => {
    if (!result.destination) return;
    
    const items = Array.from(cartItems);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);
    
    setCartItems(items);
  };

  // Auto-fill cart with AI suggestions
  const autoFillCart = async () => {
    setIsLoading(true);
    try {
      // In a real implementation, this would use the actual DeepSeek API
      // For now, we'll use mock data
      const suggestedItems = [
        { id: '1', name: 'Greek Yogurt', price: '3.99', benefit: 'High in calcium for stronger teeth', snapEligible: true },
        { id: '2', name: 'Eggs', price: '2.49', benefit: 'Protein and vitamin D support oral health', snapEligible: true },
        { id: '3', name: 'Broccoli', price: '1.99', benefit: 'Fiber helps clean teeth naturally', snapEligible: true },
        { id: '4', name: 'Cheddar Cheese', price: '3.79', benefit: 'Calcium and phosphates strengthen enamel', snapEligible: true },
        { id: '5', name: 'Almonds', price: '4.99', benefit: 'Calcium and low sugar protect teeth', snapEligible: true },
        { id: '6', name: 'Spinach', price: '2.29', benefit: 'Folic acid supports gum health', snapEligible: true },
        { id: '7', name: 'Salmon', price: '7.99', benefit: 'Omega-3s reduce inflammation in gums', snapEligible: true },
        { id: '8', name: 'Apples', price: '3.49', benefit: 'Natural cleaners that stimulate gums', snapEligible: true }
      ];
      
      setCartItems(suggestedItems);
      setFeedback("I've created a tooth-friendly grocery list for you! These items are all SNAP-eligible and great for your dental health.");
      setShowFeedback(true);
      setTimeout(() => setShowFeedback(false), 5000);
    } catch (error) {
      console.error('Error auto-filling cart:', error);
      setFeedback("Sorry, I couldn't create a cart right now. Please try again.");
      setShowFeedback(true);
    } finally {
      setIsLoading(false);
    }
  };

  // Save cart to Supabase
  const saveCart = async () => {
    if (!cartItems.length) return;
    
    setIsLoading(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) throw new Error('No authenticated user found');
      
      const { data, error } = await supabase
        .from('shopping_carts')
        .insert([
          {
            user_id: user.id,
            name: cartName,
            items: cartItems,
            total: cartTotal,
            created_at: new Date().toISOString()
          }
        ]);
        
      if (error) throw error;
      
      setFeedback("Your cart has been saved! You can access it anytime from your history.");
      setShowFeedback(true);
    } catch (error) {
      console.error('Error saving cart:', error);
      setFeedback("There was a problem saving your cart. Please try again.");
      setShowFeedback(true);
    } finally {
      setIsLoading(false);
      setTimeout(() => setShowFeedback(false), 3000);
    }
  };

  // Export cart as PDF
  const exportCart = () => {
    // In a real implementation, this would generate a PDF
    // For now, we'll just show feedback
    setFeedback("Your shopping list is being prepared for export...");
    setShowFeedback(true);
    setTimeout(() => {
      setShowFeedback(false);
      alert("Your shopping list has been exported! (This is a placeholder for PDF generation)");
    }, 2000);
  };

  return (
    <div className="bg-white rounded-xl shadow-md p-6 mb-8">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-bold text-indigo-800">Shopping Cart</h2>
        <div className="text-lg font-semibold text-indigo-700">Total: ${cartTotal}</div>
      </div>
      
      <div className="mb-4">
        <input
          type="text"
          value={cartName}
          onChange={(e) => setCartName(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md w-full focus:outline-none focus:ring-2 focus:ring-indigo-500"
          placeholder="Give your cart a name"
        />
      </div>
      
      {/* Cart Items */}
      <div className="mb-6 min-h-[200px] border border-gray-200 rounded-lg p-4 bg-gray-50">
        <DragDropContext onDragEnd={handleDragEnd}>
          <Droppable droppableId="cart-items">
            {(provided) => (
              <ul
                {...provided.droppableProps}
                ref={provided.innerRef}
                className="space-y-2"
              >
                {cartItems.length === 0 ? (
                  <li className="text-center py-6 text-gray-500">
                    Your cart is empty. Add items or use "Auto-Fill My Cart"
                  </li>
                ) : (
                  cartItems.map((item, index) => (
                    <Draggable key={item.id} draggableId={item.id} index={index}>
                      {(provided) => (
                        <li
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          {...provided.dragHandleProps}
                          className="bg-white rounded-lg p-3 border border-gray-200 shadow-sm flex justify-between items-center"
                        >
                          <div className="flex items-center">
                            <div className="mr-3 flex-shrink-0">
                              {item.snapEligible && (
                                <span className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded font-medium">
                                  SNAP
                                </span>
                              )}
                            </div>
                            <div>
                              <div className="font-medium">{item.name}</div>
                              <div className="text-sm text-gray-500">${item.price}</div>
                              {item.benefit && (
                                <div className="text-xs text-indigo-600">{item.benefit}</div>
                              )}
                            </div>
                          </div>
                          <button
                            onClick={() => removeItem(item.id)}
                            className="text-red-500 hover:text-red-700"
                          >
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                          </button>
                        </li>
                      )}
                    </Draggable>
                  ))
                )}
                {provided.placeholder}
              </ul>
            )}
          </Droppable>
        </DragDropContext>
      </div>
      
      {/* Feedback bubble */}
      {showFeedback && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          className="mb-4 bg-indigo-100 border-l-4 border-indigo-500 p-4 rounded-md"
        >
          <p className="text-indigo-700">{feedback}</p>
        </motion.div>
      )}
      
      {/* Action Buttons */}
      <div className="grid grid-cols-2 gap-3 mb-4">
        <button
          onClick={autoFillCart}
          disabled={isLoading}
          className="bg-indigo-600 hover:bg-indigo-700 text-white py-2 px-4 rounded-md transition-colors flex items-center justify-center"
        >
          {isLoading ? (
            <span className="inline-block h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></span>
          ) : (
            <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
          )}
          Auto-Fill My Cart
        </button>
        <button
          onClick={saveCart}
          disabled={isLoading || !cartItems.length}
          className="border border-indigo-600 text-indigo-600 hover:bg-indigo-50 py-2 px-4 rounded-md transition-colors flex items-center justify-center"
        >
          <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
          </svg>
          Save Cart
        </button>
      </div>
      
      <div className="grid grid-cols-2 gap-3">
        <button
          onClick={exportCart}
          disabled={!cartItems.length}
          className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-md transition-colors flex items-center justify-center"
        >
          <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          Export List
        </button>
        <button
          onClick={() => setCartItems([])}
          disabled={!cartItems.length}
          className="border border-red-600 text-red-600 hover:bg-red-50 py-2 px-4 rounded-md transition-colors flex items-center justify-center"
        >
          <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
          Clear Cart
        </button>
      </div>
    </div>
  );
};

export default CartBuilder; 