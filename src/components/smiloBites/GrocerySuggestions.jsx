import React, { useState } from 'react';
import { motion } from 'framer-motion';
import deepseekService from '../../services/deepseekService';
import proxyImage from '../../lib/utils/imageProxy';

const GrocerySuggestions = ({ activeFilter, onAddToCart }) => {
  const [suggestions, setSuggestions] = useState([
    // Mock data with different suggestion categories
    {
      id: 'yogurt',
      name: 'Greek Yogurt',
      price: '3.99',
      benefit: 'High in calcium for stronger teeth',
      description: 'Plain Greek yogurt is high in calcium and protein, with minimal added sugars. The probiotics may help maintain a healthy oral microbiome.',
      image: 'https://images.unsplash.com/photo-1505252585461-04db1eb84625?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=256&q=80',
      category: 'calcium',
      snapEligible: true
    },
    {
      id: 'cheese',
      name: 'Cheddar Cheese',
      price: '3.79',
      benefit: 'Calcium and phosphates strengthen enamel',
      description: 'Cheese raises pH levels in the mouth, reducing acid damage to teeth. It also provides calcium and phosphates that remineralize enamel.',
      image: 'https://images.unsplash.com/photo-1618164436241-4473940d1f5c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=256&q=80',
      category: 'calcium',
      snapEligible: true
    },
    {
      id: 'almonds',
      name: 'Almonds',
      price: '4.99',
      benefit: 'Calcium and low sugar protect teeth',
      description: 'Almonds are low in sugar and high in protein and calcium, making them an excellent tooth-friendly snack option.',
      image: 'https://images.unsplash.com/photo-1508061253366-f7da158b6d46?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=256&q=80',
      category: 'tooth-safe',
      snapEligible: true
    },
    {
      id: 'apples',
      name: 'Apples',
      price: '3.49',
      benefit: 'Natural cleaners that stimulate gums',
      description: 'Crunchy apples stimulate saliva production, which helps neutralize acids and wash away food particles. They also gently scrub teeth surfaces.',
      image: 'https://images.unsplash.com/photo-1568702846914-96b305d2aaeb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=256&q=80',
      category: 'tooth-safe',
      snapEligible: true
    },
    {
      id: 'carrots',
      name: 'Baby Carrots',
      price: '1.99',
      benefit: 'Crunchy texture cleans teeth surfaces',
      description: "Carrots act as natural abrasives, helping to remove plaque. They\'re also high in fiber and vitamin A for gum health.",
      image: 'https://images.unsplash.com/photo-1447175008436-054170c2e979?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=256&q=80',
      category: 'tooth-safe',
      snapEligible: true
    },
    {
      id: 'eggs',
      name: 'Eggs',
      price: '2.49',
      benefit: 'Vitamin D helps absorb calcium',
      description: 'Eggs contain vitamin D, which helps your body absorb calcium. They\'re also a good source of protein with no sugar or carbs that harm teeth.',
      image: 'https://images.unsplash.com/photo-1607690424560-35d967d6ad13?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=256&q=80',
      category: 'budget',
      snapEligible: true
    },
    {
      id: 'salmon',
      name: 'Canned Salmon',
      price: '3.79',
      benefit: 'Omega-3s reduce inflammation in gums',
      description: 'Salmon is rich in omega-3 fatty acids which reduce inflammation, potentially helping with gum disease. It also contains vitamin D for calcium absorption.',
      image: 'https://images.unsplash.com/photo-1599084993091-1cb5c0721cc6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=256&q=80',
      category: 'calcium',
      snapEligible: true
    },
    {
      id: 'broccoli',
      name: 'Broccoli',
      price: '1.99',
      benefit: 'High in fiber and calcium',
      description: 'Broccoli is high in fiber which helps clean teeth naturally. It also contains calcium for strong teeth and vitamin C for gum health.',
      image: 'https://images.unsplash.com/photo-1459411621453-7b03977f4bfc?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=256&q=80',
      category: 'budget',
      snapEligible: true
    },
    {
      id: 'oatmeal',
      name: 'Plain Oatmeal',
      price: '2.29',
      benefit: 'Low glycemic impact, rich in minerals',
      description: 'Plain oatmeal has a lower glycemic impact compared to refined grains, reducing acid production in the mouth. Add milk for calcium benefits.',
      image: 'https://images.unsplash.com/photo-1586511925558-a4c6376fe65f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=256&q=80',
      category: 'kids',
      snapEligible: true
    },
    {
      id: 'sweet-potatoes',
      name: 'Sweet Potatoes',
      price: '0.99',
      benefit: 'Vitamin A promotes healthy gum tissue',
      description: 'Sweet potatoes are rich in vitamin A, which helps maintain healthy mucous membranes and gum tissue, supporting overall oral health.',
      image: 'https://images.unsplash.com/photo-1596443911196-31cee60d42b6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=256&q=80',
      category: 'kids',
      snapEligible: true
    },
    {
      id: 'milk',
      name: 'Milk',
      price: '2.99',
      benefit: 'Calcium and vitamin D for strong teeth',
      description: 'Milk provides calcium and often vitamin D, both essential nutrients for developing and maintaining strong teeth and bones.',
      image: 'https://images.unsplash.com/photo-**********-e9143da7973b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=256&q=80',
      category: 'calcium',
      snapEligible: true
    },
    {
      id: 'beans',
      name: 'Black Beans',
      price: '0.99',
      benefit: 'High in fiber and protein',
      description: 'Beans provide protein without the fat of meat, and their fiber helps clean teeth. They\'re also rich in minerals that support overall health.',
      image: 'https://images.unsplash.com/photo-1536304575888-c768772732ba?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=256&q=80',
      category: 'budget',
      snapEligible: true
    }
  ]);

  // Filter suggestions based on active category
  const filteredSuggestions = activeFilter 
    ? suggestions.filter(item => item.category === activeFilter)
    : suggestions;

  return (
    <div className="mb-8">
      <h2 className="text-xl font-bold text-indigo-800 mb-4">Tooth-Friendly Suggestions</h2>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
        {filteredSuggestions.map((item) => (
          <motion.div
            key={item.id}
            whileHover={{ scale: 1.03 }}
            className="bg-white rounded-lg overflow-hidden shadow-sm border border-gray-200"
          >
            <div className="relative h-40">
              <img 
                src={proxyImage(item.image)} 
                alt={item.name} 
                className="w-full h-full object-cover"
              />
              {item.snapEligible && (
                <div className="absolute top-2 right-2 bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded">
                  SNAP Eligible
                </div>
              )}
            </div>
            <div className="p-4">
              <div className="flex justify-between items-start mb-2">
                <h3 className="font-semibold text-indigo-900">{item.name}</h3>
                <span className="text-indigo-700 font-medium">${item.price}</span>
              </div>
              <p className="text-sm text-indigo-600 mb-3">{item.benefit}</p>
              <button
                onClick={() => onAddToCart(item)}
                className="w-full bg-indigo-600 hover:bg-indigo-700 text-white text-sm py-2 rounded transition-colors"
              >
                Add to Cart
              </button>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default GrocerySuggestions; 