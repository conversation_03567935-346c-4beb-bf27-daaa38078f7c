import React from 'react';
import { motion } from 'framer-motion';

/**
 * CartItem component to display a product in the cart
 */
const CartItem = ({ 
  item, 
  onRemove, 
  onQuantityChange, 
  onProductSelect,
  showProductSelection = true
}) => {
  const {
    id,
    product,
    quantity = 1
  } = item;
  
  // Handle the case where no specific product has been selected yet
  if (!product) {
    return (
      <div className="flex justify-between items-center p-3 border-b border-gray-200">
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-800">{item.name}</p>
          <p className="text-xs text-gray-500">{item.benefit || 'From recipe'}</p>
        </div>
        
        {showProductSelection && (
          <button
            onClick={() => onProductSelect && onProductSelect(item)}
            className="ml-2 px-3 py-1 text-xs bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200"
          >
            Select Product
          </button>
        )}
        
        <button
          onClick={() => onRemove && onRemove(id)}
          className="ml-2 p-1 text-gray-400 hover:text-red-500"
          aria-label="Remove item"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    );
  }
  
  // If a specific product has been selected
  const {
    name,
    price,
    originalPrice,
    store,
    image,
    unitSize,
    unitCount,
    units,
    snapEligible
  } = product;
  
  // Format unit display
  const unitDisplay = unitCount 
    ? `${unitSize} ${units} × ${unitCount}`
    : `${unitSize} ${units}`;
  
  // Calculate total price
  const totalPrice = (parseFloat(price) * quantity).toFixed(2);
  
  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: 20 }}
      className="flex py-3 border-b border-gray-200"
    >
      {/* Product Image */}
      <div className="relative w-16 h-16 mr-3 rounded overflow-hidden flex-shrink-0">
        <img 
          src={image} 
          alt={name} 
          className="w-full h-full object-cover"
          onError={(e) => {
            e.target.onerror = null;
            e.target.src = 'https://via.placeholder.com/64?text=No+Image';
          }}
        />
        {snapEligible && (
          <div className="absolute bottom-0 left-0 right-0 bg-blue-700 text-white text-xxs text-center py-0.5">
            SNAP EBT
          </div>
        )}
      </div>
      
      {/* Product Info */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center mb-1">
          <h4 className="text-sm font-medium text-gray-800 truncate mr-1">{name}</h4>
          <div 
            className="px-1.5 py-0.5 rounded-full text-xxs font-medium ml-auto"
            style={{ backgroundColor: `${store.color}15`, color: store.color }}
          >
            {store.name}
          </div>
        </div>
        
        <div className="text-xs text-gray-500 mb-1">
          {unitDisplay}
        </div>
        
        <div className="flex items-center justify-between">
          <div className="flex items-baseline">
            <span className="text-sm font-bold text-gray-900">${price}</span>
            {originalPrice && (
              <span className="ml-1 text-xs text-gray-500 line-through">
                ${originalPrice}
              </span>
            )}
          </div>
          
          {/* Quantity Controls */}
          <div className="flex items-center">
            <button
              onClick={() => onQuantityChange && quantity > 1 && onQuantityChange(id, quantity - 1)}
              className={`p-1 rounded-full ${quantity > 1 ? 'text-gray-600 hover:bg-gray-100' : 'text-gray-300 cursor-not-allowed'}`}
              disabled={quantity <= 1}
              aria-label="Decrease quantity"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
              </svg>
            </button>
            
            <span className="mx-2 text-sm text-gray-800">{quantity}</span>
            
            <button
              onClick={() => onQuantityChange && onQuantityChange(id, quantity + 1)}
              className="p-1 rounded-full text-gray-600 hover:bg-gray-100"
              aria-label="Increase quantity"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
            </button>
          </div>
        </div>
      </div>
      
      {/* Actions */}
      <div className="ml-2 flex flex-col justify-between items-end">
        <button
          onClick={() => onRemove && onRemove(id)}
          className="p-1 text-gray-400 hover:text-red-500"
          aria-label="Remove item"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
        
        <div className="text-sm font-bold text-gray-900">
          ${totalPrice}
        </div>
      </div>
    </motion.div>
  );
};

export default CartItem; 