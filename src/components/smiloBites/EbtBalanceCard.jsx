import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

const EbtBalanceCard = ({ initialBalance, onBalanceUpdate, supabase }) => {
  const [balance, setBalance] = useState(initialBalance || '$0.00');
  const [isEditing, setIsEditing] = useState(!initialBalance);
  const [tempBalance, setTempBalance] = useState(balance.replace('$', ''));
  const [isFirstLoad, setIsFirstLoad] = useState(!initialBalance);

  useEffect(() => {
    // If there's no initial balance, show a prompt to enter balance
    if (isFirstLoad) {
      setIsEditing(true);
    }
  }, [isFirstLoad]);

  // Toggle edit mode
  const toggleEdit = () => {
    if (isEditing) {
      saveBalance();
    } else {
      setTempBalance(balance.replace('$', ''));
      setIsEditing(true);
    }
  };

  // Save updated balance
  const saveBalance = async () => {
    // Validate input is a number
    if (isNaN(parseFloat(tempBalance))) {
      alert("Please enter a valid number");
      return;
    }

    const formattedBalance = `$${parseFloat(tempBalance).toFixed(2)}`;
    setBalance(formattedBalance);
    setIsEditing(false);
    setIsFirstLoad(false);

    // Call the update handler if provided
    if (onBalanceUpdate) {
      onBalanceUpdate(formattedBalance);
    }

    // Update balance in Supabase if available
    if (supabase) {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        
        if (user) {
          const { error } = await supabase
            .from('profiles')
            .update({ ebt_balance: formattedBalance })
            .eq('id', user.id);
            
          if (error) throw error;
        }
      } catch (error) {
        console.error('Error updating EBT balance:', error);
      }
    }
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    saveBalance();
  };

  // Handle input changes
  const handleInputChange = (e) => {
    // Only allow numbers and decimal point
    const value = e.target.value.replace(/[^0-9.]/g, '');
    setTempBalance(value);
  };

  return (
    <motion.div 
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-xl shadow-md p-6 mb-6 border border-indigo-100"
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="h-12 w-12 rounded-full bg-indigo-100 flex items-center justify-center">
            <svg className="h-6 w-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
            </svg>
          </div>
          <div>
            <p className="text-sm text-indigo-600 font-medium">Your EBT Balance</p>
            {isEditing ? (
              <form onSubmit={handleSubmit} className="flex items-center">
                <span className="text-2xl font-bold text-indigo-800">$</span>
                <input
                  type="text"
                  value={tempBalance}
                  onChange={handleInputChange}
                  className="text-2xl font-bold text-indigo-800 bg-transparent border-b border-indigo-300 focus:outline-none focus:border-indigo-500 w-24"
                  placeholder="0.00"
                  autoFocus
                />
              </form>
            ) : (
              <h2 className="text-2xl font-bold text-indigo-800">{balance}</h2>
            )}
            {isFirstLoad && isEditing && (
              <p className="text-xs text-indigo-500 mt-1">Enter your current balance</p>
            )}
          </div>
        </div>
        <button 
          onClick={toggleEdit}
          className="h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-600 hover:bg-indigo-200 transition-colors"
        >
          {isEditing ? (
            <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          ) : (
            <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
            </svg>
          )}
        </button>
      </div>
      
      <div className="mt-4 text-xs text-gray-500">
        Last updated: {new Date().toLocaleDateString()}
        <span className="inline-block ml-2 px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">
          SNAP/EBT Eligible
        </span>
      </div>
    </motion.div>
  );
};

export default EbtBalanceCard; 