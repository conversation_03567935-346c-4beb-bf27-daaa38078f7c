import React, { useState } from 'react';

/**
 * Component for displaying a breakdown of recipe costs
 */
const RecipePriceSummary = ({ 
  recipe, 
  recipePrices, 
  selectedStore, 
  storeInfo,
  showCostPerServing = true 
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  
  // Calculate cost per serving
  const calculateCostPerServing = () => {
    if (!recipePrices || !recipePrices.totalPrice) return '$0.00';
    
    // Default to 4 servings if not specified
    const numServings = recipe.servings || 4;
    
    // Handle price range formatting
    let totalPrice = 0;
    
    if (typeof recipePrices.totalPrice === 'string') {
      // Handle price range string (e.g. "$10.50 - $12.75")
      const priceMatch = recipePrices.totalPrice.match(/\$(\d+(\.\d+)?)/g);
      if (priceMatch && priceMatch.length > 0) {
        // Use the average if it's a range
        if (priceMatch.length > 1) {
          const min = parseFloat(priceMatch[0].replace('$', ''));
          const max = parseFloat(priceMatch[1].replace('$', ''));
          totalPrice = (min + max) / 2;
        } else {
          totalPrice = parseFloat(priceMatch[0].replace('$', ''));
        }
      }
    }
    
    if (isNaN(totalPrice)) return '$0.00';
    
    return `$${(totalPrice / numServings).toFixed(2)}`;
  };
  
  // Get comparison with average price
  const getPriceComparison = () => {
    if (!recipePrices || !recipePrices.totalPrice) return null;
    
    // Get original estimated price if available
    let originalPrice = 0;
    if (recipe.cost) {
      const match = recipe.cost.match(/\$(\d+(\.\d+)?)/);
      if (match) {
        originalPrice = parseFloat(match[1]);
      }
    }
    
    if (originalPrice === 0) return null;
    
    // Handle price range for comparison
    let currentPrice = 0;
    
    if (typeof recipePrices.totalPrice === 'string') {
      // Handle price range string (e.g. "$10.50 - $12.75")
      const priceMatch = recipePrices.totalPrice.match(/\$(\d+(\.\d+)?)/g);
      if (priceMatch && priceMatch.length > 0) {
        // Use the average if it's a range
        if (priceMatch.length > 1) {
          const min = parseFloat(priceMatch[0].replace('$', ''));
          const max = parseFloat(priceMatch[1].replace('$', ''));
          currentPrice = (min + max) / 2;
        } else {
          currentPrice = parseFloat(priceMatch[0].replace('$', ''));
        }
      }
    }
    
    if (isNaN(currentPrice)) return null;
    
    const difference = currentPrice - originalPrice;
    const percentDiff = (difference / originalPrice) * 100;
    
    if (Math.abs(percentDiff) < 5) {
      return { type: 'about-average', message: 'About average price' };
    } else if (percentDiff < 0) {
      return { 
        type: 'good-deal', 
        message: `${Math.abs(percentDiff).toFixed(0)}% lower than average`,
        savedAmount: `Save $${Math.abs(difference).toFixed(2)}`
      };
    } else {
      return { 
        type: 'expensive', 
        message: `${percentDiff.toFixed(0)}% higher than average`
      };
    }
  };
  
  // Calculate dental health score
  const getDentalHealthScore = () => {
    if (!recipePrices || !recipePrices.dentalFriendlyPercentage) return null;
    
    const percentage = parseInt(recipePrices.dentalFriendlyPercentage, 10);
    
    if (percentage >= 90) {
      return { 
        rating: 'excellent', 
        icon: '😁', 
        message: 'Excellent for dental health'
      };
    } else if (percentage >= 70) {
      return { 
        rating: 'good', 
        icon: '🙂', 
        message: 'Good for dental health'
      };
    } else if (percentage >= 50) {
      return { 
        rating: 'fair', 
        icon: '😐', 
        message: 'Fair for dental health'
      };
    } else {
      return { 
        rating: 'poor', 
        icon: '😕', 
        message: 'May not be ideal for dental health'
      };
    }
  };
  
  // No price data available
  if (!recipePrices || !selectedStore) {
    return null;
  }
  
  const priceComparison = getPriceComparison();
  const costPerServing = calculateCostPerServing();
  const dentalHealth = getDentalHealthScore();
  
  return (
    <div className="recipe-price-summary bg-gray-50 rounded-lg p-3 mb-4 border border-gray-200">
      <div className="flex items-center justify-between">
        <div>
          <h4 className="text-sm font-semibold text-gray-700">Price Summary</h4>
          <div className="flex items-center mt-1">
            <div className="text-lg font-bold text-gray-900 mr-2">
              {recipePrices.totalPrice}
            </div>
            {recipePrices.userUpdated && (
              <span className="text-xs text-blue-600 font-medium bg-blue-50 py-0.5 px-1.5 rounded">
                Custom
              </span>
            )}
            {priceComparison && (
              <span className={`text-xs ml-2 py-0.5 px-1.5 rounded ${
                priceComparison.type === 'good-deal' 
                  ? 'bg-green-50 text-green-700' 
                  : priceComparison.type === 'expensive'
                    ? 'bg-orange-50 text-orange-700'
                    : 'bg-gray-100 text-gray-700'
              }`}>
                {priceComparison.message}
              </span>
            )}
          </div>
          {showCostPerServing && (
            <div className="text-xs text-gray-600 mt-1">
              {costPerServing} per serving
            </div>
          )}
        </div>
        
        <div className="text-right">
          <div className="text-sm font-medium text-gray-700">
            {storeInfo?.name || 'Local Store'}
          </div>
          <div className="text-xs text-gray-500">
            {recipePrices.inStockPercentage} ingredients available
          </div>
          <button 
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-xs text-indigo-600 mt-1 hover:underline"
          >
            {isExpanded ? 'Hide details' : 'Show details'}
          </button>
        </div>
      </div>
      
      {isExpanded && (
        <div className="mt-3 pt-3 border-t border-gray-200">
          {priceComparison && priceComparison.type === 'good-deal' && (
            <div className="mb-3">
              <div className="flex items-center text-green-700">
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span className="font-medium text-sm">{priceComparison.savedAmount} at {storeInfo?.name || 'this store'}</span>
              </div>
              <div className="text-xs text-gray-600 mt-1">
                Compared to the average price for this recipe
              </div>
            </div>
          )}
          
          {/* SNAP Eligibility */}
          {recipePrices.snapEligiblePercentage && (
            <div className="flex items-center my-2">
              <div className="w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center mr-2">
                <span className="text-blue-700 text-xs">🛒</span>
              </div>
              <div>
                <div className="text-sm font-medium text-gray-700">
                  SNAP/EBT Eligible
                </div>
                <div className="text-xs text-gray-600">
                  {recipePrices.snapEligiblePercentage} of ingredients qualify for SNAP/EBT
                </div>
              </div>
            </div>
          )}
          
          {/* Dental Health Rating */}
          {dentalHealth && (
            <div className="flex items-center mt-2">
              <div className={`w-5 h-5 rounded-full flex items-center justify-center mr-2 ${
                dentalHealth.rating === 'excellent' ? 'bg-green-100' : 
                dentalHealth.rating === 'good' ? 'bg-blue-100' :
                dentalHealth.rating === 'fair' ? 'bg-yellow-100' : 'bg-red-100'
              }`}>
                <span className="text-xs">{dentalHealth.icon}</span>
              </div>
              <div>
                <div className={`text-sm font-medium ${
                  dentalHealth.rating === 'excellent' ? 'text-green-700' : 
                  dentalHealth.rating === 'good' ? 'text-blue-700' :
                  dentalHealth.rating === 'fair' ? 'text-yellow-700' : 'text-red-700'
                }`}>
                  {dentalHealth.message}
                </div>
                <div className="text-xs text-gray-600">
                  {recipePrices.dentalFriendlyPercentage} of ingredients are tooth-friendly
                </div>
              </div>
            </div>
          )}
          
          {/* Check Price Link */}
          {recipePrices.searchUrl && (
            <div className="mt-3">
              <a 
                href={recipePrices.searchUrl.replace('{searchTerm}', encodeURIComponent(recipe.title))} 
                target="_blank"
                rel="noopener noreferrer"
                className="text-sm text-indigo-600 hover:text-indigo-800 flex items-center"
              >
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                </svg>
                Check price at {storeInfo?.brand ? 
                  storeInfo.brand.charAt(0).toUpperCase() + storeInfo.brand.slice(1) : 
                  storeInfo?.name || 'store'}
              </a>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default RecipePriceSummary; 