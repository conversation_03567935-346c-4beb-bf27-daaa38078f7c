import React, { useEffect, useRef } from 'react';

/**
 * Simple store location map component
 * Uses Leaflet.js for a lightweight map display of store locations
 */
const StoreLocationMap = ({ 
  userLocation,
  stores = [],
  selectedStoreId = null,
  onStoreSelect = () => {},
  radius = 5
}) => {
  const mapContainerRef = useRef(null);
  const mapInstanceRef = useRef(null);
  const markersRef = useRef([]);
  
  // Initialize the map when the component mounts
  useEffect(() => {
    // Only load and initialize Leaflet in browser environment
    if (typeof window === 'undefined' || !userLocation) return;
    
    // Dynamically load Leaflet CSS
    if (!document.getElementById('leaflet-css')) {
      const leafletCss = document.createElement('link');
      leafletCss.id = 'leaflet-css';
      leafletCss.rel = 'stylesheet';
      leafletCss.href = 'https://unpkg.com/leaflet@1.9.3/dist/leaflet.css';
      leafletCss.integrity = 'sha512-mD70nAW2ThLsWH0zif8JPbfraZ8hbCtjQ/5oW7Fr84xmw0xQJE+8EiBJ5zA9ro+KFW6g8Qmm8Qpj8lEXBBV1Jw==';
      leafletCss.crossOrigin = '';
      document.head.appendChild(leafletCss);
    }
    
    // Dynamically load Leaflet JS
    const loadLeaflet = async () => {
      if (!window.L) {
        await new Promise((resolve) => {
          const script = document.createElement('script');
          script.src = 'https://unpkg.com/leaflet@1.9.3/dist/leaflet.js';
          script.integrity = 'sha512-Dqm3h1Y4qiHUjbhxTuBGQsza0Tfppn53SHlu/uj1BWRMHWHIi+eCw5WeGABWkPG/SiC6aQIdgMjCtm6zXCtJA==';
          script.crossOrigin = '';
          script.onload = resolve;
          document.body.appendChild(script);
        });
      }
      
      initializeMap();
    };
    
    loadLeaflet();
    
    return () => {
      // Clean up map instance on unmount
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
      }
    };
  }, []);
  
  // Initialize the map with Leaflet
  const initializeMap = () => {
    if (!window.L || !mapContainerRef.current || !userLocation) return;
    
    // If map already exists, remove it
    if (mapInstanceRef.current) {
      mapInstanceRef.current.remove();
    }
    
    // Create map centered on user location
    const map = window.L.map(mapContainerRef.current).setView(
      [userLocation.latitude, userLocation.longitude],
      12 // Initial zoom level
    );
    
    // Add OpenStreetMap tiles
    window.L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);
    
    // Add user location marker
    const userIcon = window.L.divIcon({
      html: `<div style="background-color: #4f46e5; width: 12px; height: 12px; border-radius: 50%; border: 2px solid white;"></div>`,
      className: 'user-location-marker',
      iconSize: [16, 16],
      iconAnchor: [8, 8]
    });
    
    window.L.marker(
      [userLocation.latitude, userLocation.longitude],
      { icon: userIcon }
    ).addTo(map)
      .bindTooltip('Your Location', { permanent: false });
    
    // Draw radius circle
    const radiusMeters = radius * 1609.34; // Convert miles to meters
    window.L.circle(
      [userLocation.latitude, userLocation.longitude],
      {
        radius: radiusMeters,
        color: '#4f46e5',
        fillColor: '#818cf8',
        fillOpacity: 0.1,
        weight: 1
      }
    ).addTo(map);
    
    // Store the map instance
    mapInstanceRef.current = map;
    
    // Update markers when stores or selection changes
    updateStoreMarkers();
  };
  
  // Update store markers when stores, selected store, or user location changes
  useEffect(() => {
    updateStoreMarkers();
  }, [stores, selectedStoreId, userLocation, radius]);
  
  // Update the markers on the map
  const updateStoreMarkers = () => {
    if (!window.L || !mapInstanceRef.current || !userLocation) return;
    
    // Clear existing markers
    markersRef.current.forEach(marker => {
      mapInstanceRef.current.removeLayer(marker);
    });
    markersRef.current = [];
    
    // Add store markers
    stores.forEach(store => {
      // Skip if store doesn't have coordinates
      if (!store.coordinates && !store.latitude) return;
      
      const lat = store.coordinates ? store.coordinates.latitude : store.latitude;
      const lng = store.coordinates ? store.coordinates.longitude : store.longitude;
      
      // Skip if coordinates are invalid
      if (!lat || !lng) return;
      
      // Create store icon with different color for selected store
      const isSelected = store.id === selectedStoreId;
      const storeIcon = window.L.divIcon({
        html: `<div style="
          background-color: ${isSelected ? '#f59e0b' : '#10b981'}; 
          width: 10px; 
          height: 10px; 
          border-radius: 50%; 
          border: 2px solid white;
          ${isSelected ? 'box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.5);' : ''}
        "></div>`,
        className: 'store-location-marker',
        iconSize: [14, 14],
        iconAnchor: [7, 7]
      });
      
      const marker = window.L.marker([lat, lng], { icon: storeIcon })
        .addTo(mapInstanceRef.current)
        .bindTooltip(`${store.name} (${store.distance})`, { 
          permanent: isSelected,
          className: isSelected ? 'selected-store-tooltip' : ''
        });
      
      // Open popup on click and notify parent
      marker.on('click', () => {
        onStoreSelect(store.id);
      });
      
      markersRef.current.push(marker);
    });
    
    // Update radius circle
    if (radius && userLocation) {
      // Remove existing radius circles
      mapInstanceRef.current.eachLayer(layer => {
        if (layer instanceof window.L.Circle) {
          mapInstanceRef.current.removeLayer(layer);
        }
      });
      
      // Add new radius circle
      const radiusMeters = radius * 1609.34; // Convert miles to meters
      window.L.circle(
        [userLocation.latitude, userLocation.longitude],
        {
          radius: radiusMeters,
          color: '#4f46e5',
          fillColor: '#818cf8',
          fillOpacity: 0.1,
          weight: 1
        }
      ).addTo(mapInstanceRef.current);
    }
    
    // Fit bounds to include all markers and user location
    if (markersRef.current.length > 0) {
      const bounds = window.L.latLngBounds([
        [userLocation.latitude, userLocation.longitude],
        ...markersRef.current.map(marker => marker.getLatLng())
      ]);
      mapInstanceRef.current.fitBounds(bounds, { padding: [20, 20] });
    }
  };
  
  return (
    <div className="store-map-container">
      <div ref={mapContainerRef} className="store-map"></div>
    </div>
  );
};

export default StoreLocationMap; 