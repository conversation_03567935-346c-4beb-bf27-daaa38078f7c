import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import deepseekService from '../../services/deepseekService';

const ChatAssistant = ({ userData, supabase }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState([]);
  const [currentMessage, setCurrentMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef(null);

  // Scroll to bottom of messages
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Fetch chat history from Supabase
  useEffect(() => {
    const fetchChatHistory = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        
        if (!user) return;
        
        const { data, error } = await supabase
          .from('chat_history')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: true });
          
        if (error) throw error;
        
        if (data && data.length > 0) {
          setMessages(data[0].messages || []);
        } else {
          // Add welcome message if no chat history
          setMessages([
            {
              id: Date.now(),
              sender: 'assistant',
              text: `Hi ${userData.name || 'there'}! I'm your SmiloSnap assistant. Ask me anything about tooth-friendly foods on SNAP/EBT or how to build a healthy grocery list for your smile!`,
              timestamp: new Date().toISOString()
            }
          ]);
        }
      } catch (error) {
        console.error('Error fetching chat history:', error);
      }
    };
    
    if (isOpen) fetchChatHistory();
  }, [isOpen, supabase, userData.name]);

  // Save chat history to Supabase
  const saveChatHistory = async (updatedMessages) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) return;
      
      // Check if chat history exists for user
      const { data, error } = await supabase
        .from('chat_history')
        .select('id')
        .eq('user_id', user.id);
        
      if (error) throw error;
      
      if (data && data.length > 0) {
        // Update existing chat history
        await supabase
          .from('chat_history')
          .update({ messages: updatedMessages })
          .eq('id', data[0].id);
      } else {
        // Create new chat history
        await supabase
          .from('chat_history')
          .insert([
            {
              user_id: user.id,
              messages: updatedMessages,
              created_at: new Date().toISOString()
            }
          ]);
      }
    } catch (error) {
      console.error('Error saving chat history:', error);
    }
  };

  // Handle sending a message
  const handleSendMessage = async (e) => {
    e.preventDefault();
    
    if (!currentMessage.trim()) return;
    
    // Add user message
    const userMessage = {
      id: Date.now(),
      sender: 'user',
      text: currentMessage,
      timestamp: new Date().toISOString()
    };
    
    const updatedMessages = [...messages, userMessage];
    setMessages(updatedMessages);
    setCurrentMessage('');
    setIsLoading(true);
    
    try {
      // In a real implementation, this would call the DeepSeek API
      // For now we'll simulate a response
      setTimeout(() => {
        let response;
        const lowerCaseMessage = currentMessage.toLowerCase();
        
        if (lowerCaseMessage.includes('braces') || lowerCaseMessage.includes('kids')) {
          response = "For kids with braces, I recommend soft foods like yogurt, mashed potatoes, soft fruits (bananas, berries), eggs, and soft-cooked vegetables. Avoid sticky, hard foods like caramel, popcorn, nuts, and hard candies that can damage braces. Cottage cheese is excellent as it's high in calcium and safe for braces!";
        } else if (lowerCaseMessage.includes('budget') || lowerCaseMessage.includes('$30')) {
          response = "Here's a $30 weekly cart for 2 people with cavity concerns:\n\n- Plain Greek yogurt ($3.99) - calcium strengthens teeth\n- Eggs ($2.49) - protein for gum health\n- Frozen spinach ($1.99) - vitamin K for tooth decay prevention\n- Canned tuna ($1.79) - phosphorus helps protect enamel\n- Brown rice ($2.49) - low-glycemic carbs\n- Apples ($3.49) - natural teeth cleaners\n- Sweet potatoes ($2.99) - vitamin A for enamel\n- Broccoli ($1.99) - fiber helps clean teeth\n- Cheese block ($3.99) - calcium + neutralizes acids\n- Tap water (free) - fluoride if your area adds it\n\nTotal: $25.21 with room for spices/seasonings";
        } else if (lowerCaseMessage.includes('bad') || lowerCaseMessage.includes('teeth')) {
          response = "Foods to avoid for dental health:\n\n1. Sugary drinks (soda, juice) - they bathe teeth in acid-producing sugars\n2. Sticky candies - they cling to teeth and extend sugar exposure\n3. Potato chips - they break into small pieces that get trapped between teeth\n4. Dried fruits - concentrated sugar that sticks to teeth\n5. White bread - breaks down into simple sugars quickly\n\nWhen possible, rinse with water after consuming these foods, and wait at least 30 minutes before brushing (acids temporarily soften enamel).";
        } else {
          response = "That's a great question about dental nutrition! Foods rich in calcium (dairy, leafy greens), phosphorus (eggs, fish), vitamin C (bell peppers, citrus), and crunchy vegetables (carrots, celery) all support dental health. Most of these are SNAP-eligible and should be part of your regular grocery list. Would you like specific recommendations based on your dietary preferences?";
        }

        const assistantMessage = {
          id: Date.now(),
          sender: 'assistant',
          text: response,
          timestamp: new Date().toISOString()
        };
        
        const finalMessages = [...updatedMessages, assistantMessage];
        setMessages(finalMessages);
        setIsLoading(false);
        
        // Save chat history
        saveChatHistory(finalMessages);
      }, 1500);
    } catch (error) {
      console.error('Error getting response:', error);
      setIsLoading(false);
      
      // Add error message
      const errorMessage = {
        id: Date.now(),
        sender: 'assistant',
        text: "I'm sorry, I'm having trouble processing your request right now. Please try again later.",
        timestamp: new Date().toISOString(),
        isError: true
      };
      
      setMessages([...updatedMessages, errorMessage]);
    }
  };

  // Toggle chat window
  const toggleChat = () => {
    setIsOpen(!isOpen);
  };

  // Suggested prompts
  const suggestedPrompts = [
    "What foods are good for kids with braces?",
    "Plan a $30 weekly cart for 2 people with cavities",
    "Which foods should I avoid for my teeth?",
    "Recommend tooth-safe snacks under $5"
  ];

  return (
    <>
      {/* Floating chat button */}
      <div className="fixed bottom-6 right-6 z-50">
        <button
          onClick={toggleChat}
          className={`w-14 h-14 rounded-full flex items-center justify-center shadow-lg transition-colors ${
            isOpen ? 'bg-red-500 hover:bg-red-600' : 'bg-indigo-600 hover:bg-indigo-700'
          }`}
        >
          {isOpen ? (
            <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          ) : (
            <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
            </svg>
          )}
        </button>
      </div>
      
      {/* Chat window */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: 20, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 20, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="fixed bottom-24 right-6 w-full max-w-sm bg-white rounded-xl shadow-xl z-50 overflow-hidden flex flex-col"
            style={{ maxHeight: 'calc(100vh - 150px)' }}
          >
            {/* Chat header */}
            <div className="bg-indigo-600 p-4">
              <h3 className="text-white font-medium flex items-center">
                <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                </svg>
                Smilo Assistant
              </h3>
            </div>
            
            {/* Messages */}
            <div className="flex-1 p-4 overflow-y-auto">
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`mb-4 ${
                    message.sender === 'user'
                      ? 'flex justify-end'
                      : 'flex justify-start'
                  }`}
                >
                  <div
                    className={`rounded-lg p-3 max-w-[80%] ${
                      message.sender === 'user'
                        ? 'bg-indigo-600 text-white'
                        : message.isError
                        ? 'bg-red-100 text-red-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}
                  >
                    <p className="whitespace-pre-line">{message.text}</p>
                    <span className="text-xs opacity-70 block mt-1">
                      {new Date(message.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </span>
                  </div>
                </div>
              ))}
              
              {isLoading && (
                <div className="flex justify-start mb-4">
                  <div className="bg-gray-100 rounded-lg p-3">
                    <div className="flex space-x-2 items-center">
                      <div className="w-2 h-2 rounded-full bg-gray-400 animate-bounce" style={{ animationDelay: '0ms' }}></div>
                      <div className="w-2 h-2 rounded-full bg-gray-400 animate-bounce" style={{ animationDelay: '150ms' }}></div>
                      <div className="w-2 h-2 rounded-full bg-gray-400 animate-bounce" style={{ animationDelay: '300ms' }}></div>
                    </div>
                  </div>
                </div>
              )}
              
              <div ref={messagesEndRef} />
            </div>
            
            {/* Suggested prompts */}
            {messages.length <= 2 && (
              <div className="p-3 border-t border-gray-200">
                <p className="text-xs text-gray-500 mb-2">Try asking:</p>
                <div className="flex flex-wrap gap-2">
                  {suggestedPrompts.map((prompt, index) => (
                    <button
                      key={index}
                      onClick={() => {
                        setCurrentMessage(prompt);
                      }}
                      className="text-xs bg-indigo-50 text-indigo-700 rounded-full px-3 py-1 hover:bg-indigo-100 transition-colors"
                    >
                      {prompt}
                    </button>
                  ))}
                </div>
              </div>
            )}
            
            {/* Message input */}
            <form onSubmit={handleSendMessage} className="border-t border-gray-200 p-3 flex items-center">
              <input
                type="text"
                value={currentMessage}
                onChange={(e) => setCurrentMessage(e.target.value)}
                placeholder="Ask about tooth-friendly foods..."
                className="flex-1 border border-gray-300 rounded-full px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                disabled={isLoading}
              />
              <button
                type="submit"
                disabled={!currentMessage.trim() || isLoading}
                className={`ml-2 rounded-full w-10 h-10 flex items-center justify-center ${
                  !currentMessage.trim() || isLoading
                    ? 'bg-gray-300 text-gray-600'
                    : 'bg-indigo-600 text-white hover:bg-indigo-700'
                } transition-colors`}
              >
                <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                </svg>
              </button>
            </form>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default ChatAssistant; 