import React, { useState, useEffect } from 'react';
import productService from '../../lib/services/productService';
import { motion, AnimatePresence } from 'framer-motion';

/**
 * Modal for selecting products for a specific ingredient
 */
const ProductSelectionModal = ({ 
  isOpen, 
  onClose, 
  ingredient, 
  stores, 
  onProductSelect,
  selectedProductId
}) => {
  const [products, setProducts] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [sortBy, setSortBy] = useState('price');
  const [selectedStores, setSelectedStores] = useState([]);
  const [filters, setFilters] = useState({
    onlyInStock: true,
    onlySnapEligible: false,
    onlyOrganic: false,
    onlySale: false,
  });
  
  // Fetch products when the modal opens
  useEffect(() => {
    if (isOpen && ingredient) {
      fetchProducts();
    }
  }, [isOpen, ingredient, sortBy, selectedStores, filters]);
  
  // Fetch products for the ingredient from selected stores
  const fetchProducts = async () => {
    if (!ingredient) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      // Use all available stores if none are selected
      const storeIds = selectedStores.length > 0 
        ? selectedStores 
        : stores.map(store => store.id);
      
      // Fetch products with sorting and filtering options
      const fetchedProducts = await productService.searchProductsByIngredient(
        ingredient,
        storeIds,
        {
          sortBy,
          sortDirection: 'asc',
          filters: {
            ...filters,
            stores: selectedStores.length > 0 ? selectedStores : undefined
          }
        }
      );
      
      setProducts(fetchedProducts);
    } catch (error) {
      console.error(`Error fetching products for ${ingredient}:`, error);
      setError('Failed to load products. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle store filter selection
  const handleStoreSelect = (storeId) => {
    setSelectedStores(prev => {
      if (prev.includes(storeId)) {
        return prev.filter(id => id !== storeId);
      } else {
        return [...prev, storeId];
      }
    });
  };
  
  // Handle filter toggle
  const handleFilterToggle = (filterName) => {
    setFilters(prev => ({
      ...prev,
      [filterName]: !prev[filterName]
    }));
  };
  
  // Handle sort change
  const handleSortChange = (event) => {
    setSortBy(event.target.value);
  };
  
  // Handle product selection
  const handleSelectProduct = (product) => {
    if (onProductSelect) {
      onProductSelect(product);
    }
  };
  
  if (!isOpen) {
    return null;
  }
  
  // If no products found after loading
  const showNoProducts = !isLoading && products.length === 0 && !error;
  
  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50"
          onClick={onClose}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="relative w-full max-w-4xl max-h-[90vh] bg-white rounded-xl shadow-2xl overflow-hidden"
            onClick={e => e.stopPropagation()}
          >
            {/* Header */}
            <div className="bg-indigo-600 text-white px-6 py-4 flex items-center justify-between shadow-md">
              <h2 className="text-xl font-semibold">
                Select {ingredient}
              </h2>
              <button
                onClick={onClose}
                className="p-1 rounded-full hover:bg-indigo-500 transition-colors"
                aria-label="Close"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            {/* Filters and Sorting */}
            <div className="bg-gray-50 border-b border-gray-200 p-4">
              <div className="flex flex-wrap gap-4 items-center">
                {/* Store filters */}
                <div className="flex flex-wrap gap-2 mr-4">
                  {stores.map(store => (
                    <button
                      key={store.id}
                      onClick={() => handleStoreSelect(store.id)}
                      className={`px-3 py-1.5 text-sm rounded-full flex items-center ${
                        selectedStores.includes(store.id) || selectedStores.length === 0
                          ? `bg-${store.id}-100 text-${store.id}-800 border border-${store.id}-300`
                          : 'bg-white text-gray-600 border border-gray-300'
                      }`}
                      style={selectedStores.includes(store.id) || selectedStores.length === 0 ? {
                        backgroundColor: `${store.color}15`,
                        color: store.color,
                        borderColor: `${store.color}40`
                      } : {}}
                    >
                      {/*<img src={store.logo} alt={store.name} className="w-4 h-4 mr-1" />*/}
                      <span>{store.name}</span>
                    </button>
                  ))}
                </div>
                
                {/* Feature filters */}
                <div className="flex flex-wrap gap-2">
                  <button
                    onClick={() => handleFilterToggle('onlyInStock')}
                    className={`px-3 py-1.5 text-sm rounded-full ${
                      filters.onlyInStock
                        ? 'bg-green-100 text-green-800 border border-green-300'
                        : 'bg-white text-gray-600 border border-gray-300'
                    }`}
                  >
                    In Stock Only
                  </button>
                  <button
                    onClick={() => handleFilterToggle('onlySnapEligible')}
                    className={`px-3 py-1.5 text-sm rounded-full ${
                      filters.onlySnapEligible
                        ? 'bg-blue-100 text-blue-800 border border-blue-300'
                        : 'bg-white text-gray-600 border border-gray-300'
                    }`}
                  >
                    SNAP Eligible
                  </button>
                  <button
                    onClick={() => handleFilterToggle('onlyOrganic')}
                    className={`px-3 py-1.5 text-sm rounded-full ${
                      filters.onlyOrganic
                        ? 'bg-green-100 text-green-800 border border-green-300'
                        : 'bg-white text-gray-600 border border-gray-300'
                    }`}
                  >
                    Organic
                  </button>
                  <button
                    onClick={() => handleFilterToggle('onlySale')}
                    className={`px-3 py-1.5 text-sm rounded-full ${
                      filters.onlySale
                        ? 'bg-red-100 text-red-800 border border-red-300'
                        : 'bg-white text-gray-600 border border-gray-300'
                    }`}
                  >
                    On Sale
                  </button>
                </div>
                
                {/* Sort options */}
                <div className="ml-auto">
                  <select
                    value={sortBy}
                    onChange={handleSortChange}
                    className="bg-white border border-gray-300 rounded-md px-3 py-1.5 text-sm"
                  >
                    <option value="price">Sort by: Price</option>
                    <option value="pricePerUnit">Sort by: Price Per Unit</option>
                    <option value="rating">Sort by: Rating</option>
                  </select>
                </div>
              </div>
            </div>
            
            {/* Products list */}
            <div className="overflow-y-auto max-h-[calc(90vh-12rem)]">
              {isLoading ? (
                <div className="flex items-center justify-center p-12">
                  <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
                </div>
              ) : error ? (
                <div className="text-center p-12 text-red-500">
                  <p>{error}</p>
                  <button 
                    onClick={fetchProducts}
                    className="mt-4 px-4 py-2 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200"
                  >
                    Try Again
                  </button>
                </div>
              ) : showNoProducts ? (
                <div className="text-center p-12 text-gray-500">
                  <p>No products found with the selected filters.</p>
                  <button 
                    onClick={() => {
                      setFilters({
                        onlyInStock: true,
                        onlySnapEligible: false,
                        onlyOrganic: false,
                        onlySale: false,
                      });
                      setSelectedStores([]);
                    }}
                    className="mt-4 px-4 py-2 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200"
                  >
                    Reset Filters
                  </button>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
                  {products.map(product => (
                    <ProductCard 
                      key={product.id}
                      product={product}
                      isSelected={product.id === selectedProductId}
                      onSelect={() => handleSelectProduct(product)}
                    />
                  ))}
                </div>
              )}
            </div>
            
            {/* Footer */}
            <div className="bg-gray-50 border-t border-gray-200 p-4 flex justify-end">
              <button
                onClick={onClose}
                className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 mr-2"
              >
                Cancel
              </button>
              <button
                onClick={onClose}
                className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
              >
                Done
              </button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

/**
 * Product card component for displaying individual products
 */
const ProductCard = ({ product, isSelected, onSelect }) => {
  const {
    name,
    store,
    price,
    originalPrice,
    unitSize,
    unitCount,
    units,
    image,
    inStock,
    isOrganic,
    onSale,
    nutritionTags,
    snapEligible,
    rating,
    reviewCount,
    pricePerUnit
  } = product;
  
  // Format unit display
  const unitDisplay = unitCount 
    ? `${unitSize} ${units} × ${unitCount}`
    : `${unitSize} ${units}`;
  
  return (
    <motion.div
      whileHover={{ y: -4 }}
      className={`relative bg-white rounded-lg shadow-md overflow-hidden border-2 ${
        isSelected 
          ? 'border-indigo-500' 
          : 'border-transparent hover:border-gray-200'
      }`}
    >
      {/* Selection indicator */}
      {isSelected && (
        <div className="absolute top-3 right-3 z-10 bg-indigo-500 text-white rounded-full p-1">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </div>
      )}
      
      {/* Store badge */}
      <div 
        className="absolute top-3 left-3 z-10 px-2 py-1 rounded-full text-xs font-medium"
        style={{ backgroundColor: `${store.color}20`, color: store.color }}
      >
        {store.name}
      </div>
      
      {/* Out of stock overlay */}
      {!inStock && (
        <div className="absolute inset-0 bg-white bg-opacity-70 z-10 flex items-center justify-center">
          <div className="bg-red-500 text-white px-3 py-1 rounded-full text-sm font-medium transform -rotate-12">
            Out of Stock
          </div>
        </div>
      )}
      
      {/* Product Image */}
      <div className="relative w-full h-36 overflow-hidden">
        <img 
          src={image} 
          alt={name} 
          className="w-full h-full object-cover"
          onError={(e) => {
            e.target.onerror = null;
            e.target.src = 'https://via.placeholder.com/150?text=No+Image';
          }}
        />
        
        {/* SNAP Badge */}
        {snapEligible && (
          <div className="absolute bottom-2 left-2 bg-blue-700 text-white text-xs font-bold px-2 py-1 rounded">
            SNAP EBT
          </div>
        )}
        
        {/* Sale Badge */}
        {onSale && (
          <div className="absolute bottom-2 right-2 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded">
            SALE
          </div>
        )}
      </div>
      
      {/* Product Info */}
      <div className="p-4">
        <h3 className="text-sm font-medium text-gray-900 mb-1 line-clamp-2 h-10">
          {name}
        </h3>
        
        {/* Price */}
        <div className="flex items-baseline mb-1">
          <span className="text-lg font-bold text-gray-900">${price}</span>
          {originalPrice && (
            <span className="ml-2 text-sm text-gray-500 line-through">
              ${originalPrice}
            </span>
          )}
        </div>
        
        {/* Unit price */}
        <div className="text-xs text-gray-500 mb-2">
          {unitDisplay} • ${pricePerUnit.value}/{pricePerUnit.unit}
        </div>
        
        {/* Rating */}
        <div className="flex items-center mb-3">
          <div className="flex items-center text-yellow-400">
            <svg className="w-4 h-4 fill-current" viewBox="0 0 24 24">
              <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
            </svg>
            <span className="ml-1 text-xs text-gray-600">{rating} ({reviewCount})</span>
          </div>
        </div>
        
        {/* Tags */}
        <div className="flex flex-wrap gap-1 mb-3">
          {isOrganic && (
            <span className="inline-block bg-green-100 text-green-800 text-xs px-2 py-0.5 rounded">
              Organic
            </span>
          )}
          {nutritionTags.map((tag, index) => (
            <span 
              key={index} 
              className="inline-block bg-indigo-100 text-indigo-800 text-xs px-2 py-0.5 rounded"
            >
              {tag}
            </span>
          ))}
        </div>
        
        {/* Select Button */}
        <button
          onClick={onSelect}
          disabled={!inStock}
          className={`w-full py-2 rounded-md text-sm font-medium ${
            isSelected
              ? 'bg-indigo-600 text-white hover:bg-indigo-700'
              : inStock
                ? 'bg-indigo-100 text-indigo-700 hover:bg-indigo-200'
                : 'bg-gray-100 text-gray-400 cursor-not-allowed'
          }`}
        >
          {isSelected ? 'Selected' : inStock ? 'Select' : 'Out of Stock'}
        </button>
      </div>
    </motion.div>
  );
};

export default ProductSelectionModal; 