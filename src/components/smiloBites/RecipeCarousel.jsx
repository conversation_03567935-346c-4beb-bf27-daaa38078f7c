import React, { useEffect, useState, useRef, useMemo } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay, A11y, Keyboard, EffectFade } from 'swiper/modules';
import proxyImage from '../../lib/utils/imageProxy';
import groceryPriceService from '../../lib/services/groceryPriceService';
import locationService from '../../lib/services/locationService';
import RecipeIngredientPrice from './RecipeIngredientPrice';
import RecipePriceSummary from './RecipePriceSummary';
import StoreLocationMap from './StoreLocationMap';
import RecipeFilters from './RecipeFilters';

// Check if we're in a browser environment
const isBrowser = typeof window !== 'undefined';

// Import Swiper styles handled in main.jsx for global application

const RecipeCarousel = ({ recipes, title = "Featured Recipes", onAddIngredientsToCart }) => {
  const [showingRecipeDetails, setShowingRecipeDetails] = useState({});
  const [isMobile, setIsMobile] = useState(isBrowser ? window.innerWidth < 768 : false);
  const [isBeginning, setIsBeginning] = useState(true);
  const [isEnd, setIsEnd] = useState(false);
  const [activeIndex, setActiveIndex] = useState(0);
  const [localStores, setLocalStores] = useState([]);
  const [selectedStore, setSelectedStore] = useState(null);
  const [recipePrices, setRecipePrices] = useState({});
  const [zipCode, setZipCode] = useState('');
  const [searchRadius, setSearchRadius] = useState(5); // Default 5 miles radius
  const [showZipPrompt, setShowZipPrompt] = useState(false);
  const [isLoadingPrices, setIsLoadingPrices] = useState(false);
  const [isDetectingLocation, setIsDetectingLocation] = useState(false);
  const [currentCoordinates, setCurrentCoordinates] = useState(null);
  const [locationError, setLocationError] = useState('');
  const [showDetailedPrices, setShowDetailedPrices] = useState({});
  const [userUpdatedPrices, setUserUpdatedPrices] = useState({});
  const [showMap, setShowMap] = useState(false);
  const swiperRef = useRef(null);
  const prevButtonRef = useRef(null);
  const nextButtonRef = useRef(null);
  const [filters, setFilters] = useState({
    dentalFriendly: false,
    snapEligible: false,
    priceRange: 'all',
    alternatives: null
  });

  // Handle responsive behavior
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(isBrowser ? window.innerWidth < 768 : false);
    };

    if (isBrowser) {
      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }
  }, []);

  // Auto-detect location on component mount
  useEffect(() => {
    if (isBrowser) {
      autoDetectLocation();
    }
  }, []);

  // Fetch prices when selected store changes
  useEffect(() => {
    if (selectedStore && recipes.length > 0) {
      fetchRecipePrices();
    }
  }, [selectedStore, recipes]);

  // Try to auto-detect the user's location
  const autoDetectLocation = async () => {
    setIsDetectingLocation(true);
    setLocationError('');
    
    try {
      // First try to get precise coordinates
      const coords = await locationService.getUserLocation();
      setCurrentCoordinates(coords);
      
      const locationInfo = await locationService.getPostalCodeFromCoordinates(
        coords.latitude, 
        coords.longitude
      );
      
      if (locationInfo) {
        setZipCode(locationInfo);
        fetchLocalStores(locationInfo, searchRadius, coords);
        setShowZipPrompt(false);
      } else {
        // If we couldn't determine postal code, show the ZIP input but keep coords
        setShowZipPrompt(true);
      }
    } catch (error) {
      console.error('Failed to auto-detect location:', error);
      setLocationError('Could not detect your location automatically. Please enter your ZIP code.');
      setShowZipPrompt(true);
    } finally {
      setIsDetectingLocation(false);
    }
  };

  // Fetch local grocery stores based on zip code
  const fetchLocalStores = async (zip, radius, coordinates = null) => {
    try {
      const stores = await groceryPriceService.getLocalGroceryStores(zip, radius, coordinates);
      setLocalStores(stores);
      if (stores.length > 0) {
        setSelectedStore(stores[0].id); // Select first store by default
      }
    } catch (error) {
      console.error('Error fetching local stores:', error);
    }
  };

  // Fetch prices for all recipes at the selected store
  const fetchRecipePrices = async () => {
    if (!selectedStore) return;
    
    setIsLoadingPrices(true);
    const newPrices = {};
    
    try {
      for (const recipe of recipes) {
        if (recipe.ingredients && recipe.ingredients.length > 0) {
          const priceInfo = await groceryPriceService.getRecipePriceRange(
            recipe.ingredients,
            selectedStore
          );
          newPrices[recipe.id] = priceInfo;
        }
      }
      setRecipePrices(newPrices);
    } catch (error) {
      console.error('Error fetching recipe prices:', error);
    } finally {
      setIsLoadingPrices(false);
    }
  };

  // Handle zip code submission
  const handleZipSubmit = async (e) => {
    e.preventDefault();
    if (zipCode.match(/^\d{5}$/)) {
      if (isBrowser && localStorage) {
        localStorage.setItem('userZipCode', zipCode);
      }
      
      try {
        // Try to get coordinates for the entered ZIP code
        const coords = await locationService.getCoordinatesFromZipCode(zipCode);
        setCurrentCoordinates(coords);
        fetchLocalStores(zipCode, searchRadius, coords);
      } catch (error) {
        console.error('Error geocoding ZIP code:', error);
        // Try to fetch stores with just the ZIP code
        fetchLocalStores(zipCode, searchRadius);
      }
      
      setShowZipPrompt(false);
      setLocationError('');
    }
  };

  // Handle radius change
  const handleRadiusChange = (e) => {
    const newRadius = parseInt(e.target.value, 10);
    setSearchRadius(newRadius);
    
    // If we already have zip code, update stores with new radius
    if (zipCode && zipCode.match(/^\d{5}$/)) {
      fetchLocalStores(zipCode, newRadius, currentCoordinates);
    }
  };

  // Handle store selection change
  const handleStoreChange = (e) => {
    setSelectedStore(e.target.value);
  };
  
  // Toggle map visibility
  const toggleMap = () => {
    setShowMap(!showMap);
  };
  
  // Handle store selection from the map
  const handleStoreSelect = (storeId) => {
    setSelectedStore(storeId);
  };

  // Add keyboard event handlers for accessibility
  useEffect(() => {
    const handlePrevKeyDown = (e) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        if (swiperRef.current && swiperRef.current.swiper) {
          swiperRef.current.swiper.slidePrev();
        }
      }
    };

    const handleNextKeyDown = (e) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        if (swiperRef.current && swiperRef.current.swiper) {
          swiperRef.current.swiper.slideNext();
        }
      }
    };

    if (prevButtonRef.current) {
      prevButtonRef.current.addEventListener('keydown', handlePrevKeyDown);
    }

    if (nextButtonRef.current) {
      nextButtonRef.current.addEventListener('keydown', handleNextKeyDown);
    }

    return () => {
      if (prevButtonRef.current) {
        prevButtonRef.current.removeEventListener('keydown', handlePrevKeyDown);
      }
      if (nextButtonRef.current) {
        nextButtonRef.current.removeEventListener('keydown', handleNextKeyDown);
      }
    };
  }, []);

  const toggleRecipeDetails = (recipeId) => {
    setShowingRecipeDetails(prev => ({
      ...prev,
      [recipeId]: !prev[recipeId]
    }));
  };

  const handleAddIngredientsToCart = (recipe) => {
    if (onAddIngredientsToCart && recipe.ingredients) {
      // Convert ingredients into cart items
      const cartItems = recipe.ingredients.map(ingredient => ({
        id: `${recipe.id}-${ingredient.substring(0, 10).replace(/\s/g, '-')}`,
        name: ingredient,
        price: 'varies',
        benefit: `Part of the ${recipe.title} recipe`,
        category: 'recipe-ingredient',
        snapEligible: true
      }));
      
      onAddIngredientsToCart(cartItems, recipe.title);
    }
  };

  const handleSlideChange = (swiper) => {
    setIsBeginning(swiper.isBeginning);
    setIsEnd(swiper.isEnd);
    setActiveIndex(swiper.activeIndex);
  };

  // Toggle detailed prices view for a recipe
  const toggleDetailedPrices = (recipeId) => {
    setShowDetailedPrices(prev => ({
      ...prev,
      [recipeId]: !prev[recipeId]
    }));
  };

  // Handle price update for an individual ingredient
  const handleIngredientPriceUpdate = (recipeId, ingredient, priceData) => {
    // Update our local state with the user's price
    setUserUpdatedPrices(prev => ({
      ...prev,
      [`${recipeId}-${ingredient}`]: priceData
    }));
    
    // Recalculate the recipe total
    if (selectedStore && recipePrices[recipeId]) {
      // Get all ingredients for this recipe
      const currentRecipe = recipes.find(r => r.id === recipeId);
      if (!currentRecipe) return;
      
      // Calculate a new total price based on updated ingredient prices
      let totalPrice = 0;
      let itemsInStock = 0;
      
      currentRecipe.ingredients.forEach(ing => {
        // Check if we have a user-updated price for this ingredient
        const updatedPriceKey = `${recipeId}-${ing}`;
        const updatedPrice = userUpdatedPrices[updatedPriceKey];
        
        if (updatedPrice && updatedPrice.stores[selectedStore]) {
          // User has updated this price
          const priceStr = updatedPrice.stores[selectedStore].price;
          const priceValue = parseFloat(priceStr.replace('$', ''));
          
          if (!isNaN(priceValue)) {
            totalPrice += priceValue;
            if (updatedPrice.stores[selectedStore].inStock) {
              itemsInStock++;
            }
          }
        } else {
          // Use default price logic (this is simplified and would need to match your actual price calculation)
          // For demo purposes, we'll add a random amount between $1-5
          const defaultPrice = 1 + Math.random() * 4;
          totalPrice += defaultPrice;
          itemsInStock++;
        }
      });
      
      // Update the recipe price
      setRecipePrices(prev => ({
        ...prev,
        [recipeId]: {
          ...prev[recipeId],
          totalPrice: `$${totalPrice.toFixed(2)}`,
          inStockPercentage: `${Math.round((itemsInStock / currentRecipe.ingredients.length) * 100)}%`,
          userUpdated: true
        }
      }));
    }
  };

  // Handle filter changes
  const handleFilterChange = (filterName, value) => {
    setFilters(prev => ({
      ...prev,
      [filterName]: value
    }));
  };

  // Filter recipes based on current filters
  const filteredRecipes = useMemo(() => {
    if (!recipes || recipes.length === 0) return [];
    
    return recipes.filter(recipe => {
      // Skip filtering if no prices yet
      if (!recipePrices[recipe.id]) return true;
      
      const recipePrice = recipePrices[recipe.id];
      
      // Dental friendly filter
      if (filters.dentalFriendly && recipePrice.dentalFriendlyPercentage) {
        const dentalPercentage = parseInt(recipePrice.dentalFriendlyPercentage, 10);
        if (dentalPercentage < 70) return false;
      }
      
      // SNAP eligible filter
      if (filters.snapEligible && recipePrice.snapEligiblePercentage) {
        const snapPercentage = parseInt(recipePrice.snapEligiblePercentage, 10);
        if (snapPercentage < 90) return false;
      }
      
      // Price range filter
      if (filters.priceRange !== 'all' && recipePrice.totalPriceRange) {
        const averagePrice = (recipePrice.totalPriceRange.min + recipePrice.totalPriceRange.max) / 2;
        
        if (filters.priceRange === 'under5' && averagePrice >= 5) return false;
        if (filters.priceRange === '5to10' && (averagePrice < 5 || averagePrice > 10)) return false;
        if (filters.priceRange === '10to20' && (averagePrice < 10 || averagePrice > 20)) return false;
        if (filters.priceRange === 'over20' && averagePrice <= 20) return false;
      }
      
      // Alternative suggestions filter
      if (filters.alternatives) {
        const ingredients = recipe.ingredients ? recipe.ingredients.join(' ').toLowerCase() : '';
        
        if (filters.alternatives === 'low-sugar') {
          // Filter out recipes with sugar, honey, syrup, etc.
          if (ingredients.includes('sugar') || 
              ingredients.includes('honey') || 
              ingredients.includes('syrup') || 
              ingredients.includes('candy')) {
            return false;
          }
        } else if (filters.alternatives === 'high-calcium') {
          // Keep recipes with dairy, leafy greens, nuts, etc.
          const hasCacliumRichFood = 
            ingredients.includes('milk') || 
            ingredients.includes('cheese') || 
            ingredients.includes('yogurt') ||
            ingredients.includes('kale') || 
            ingredients.includes('spinach') ||
            ingredients.includes('almond') || 
            ingredients.includes('broccoli');
            
          if (!hasCacliumRichFood) return false;
        } else if (filters.alternatives === 'teeth-cleaning') {
          // Keep recipes with crunchy vegetables and fruits
          const hasTeethCleaningFood = 
            ingredients.includes('apple') || 
            ingredients.includes('carrot') || 
            ingredients.includes('celery') ||
            ingredients.includes('cucumber') || 
            ingredients.includes('pear');
            
          if (!hasTeethCleaningFood) return false;
        }
      }
      
      return true;
    });
  }, [recipes, recipePrices, filters]);

  // Get the selected store object
  const selectedStoreObj = useMemo(() => {
    return localStores.find(store => store.id === selectedStore) || null;
  }, [localStores, selectedStore]);

  return (
    <div className="recipe-carousel">
      <div className="recipe-carousel-header">
        <h2 className="text-xl font-bold text-indigo-800">{title}</h2>
        
        {recipes.length > 1 && (
          <div className="recipe-counter">
            Recipe {activeIndex + 1} of {recipes.length}
          </div>
        )}
      </div>
      
      {/* Location and Store Selector */}
      <div className="store-selector mb-3">
        {showZipPrompt ? (
          <form onSubmit={handleZipSubmit} className="zip-form flex flex-wrap items-center">
            <input
              type="text"
              placeholder="Enter ZIP code"
              value={zipCode}
              onChange={(e) => setZipCode(e.target.value)}
              className="zip-input p-2 border border-indigo-300 rounded text-sm mr-2 mb-2 sm:mb-0 bg-white text-gray-800 w-28"
              maxLength={5}
              pattern="\d{5}"
              required
            />
            
            <div className="flex items-center mr-2 mb-2 sm:mb-0">
              <label htmlFor="radius-select" className="text-sm text-indigo-700 mr-2">
                Radius:
              </label>
              <select
                id="radius-select"
                value={searchRadius}
                onChange={handleRadiusChange}
                className="p-2 border border-indigo-300 rounded text-sm bg-white text-gray-800"
              >
                <option value="1">1 mile</option>
                <option value="3">3 miles</option>
                <option value="5">5 miles</option>
                <option value="10">10 miles</option>
                <option value="15">15 miles</option>
                <option value="25">25 miles</option>
              </select>
            </div>
            
            <button
              type="submit"
              className="zip-submit bg-indigo-500 text-white px-3 py-2 rounded text-sm hover:bg-indigo-600 mr-2 mb-2 sm:mb-0"
            >
              Find Stores
            </button>
            <button
              type="button"
              onClick={autoDetectLocation}
              disabled={isDetectingLocation}
              className="zip-auto-detect text-indigo-700 border border-indigo-300 px-3 py-2 rounded text-sm hover:bg-indigo-50 mb-2 sm:mb-0 flex items-center"
            >
              {isDetectingLocation ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-indigo-700" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Detecting...
                </>
              ) : (
                <>
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  </svg>
                  Auto-detect
                </>
              )}
            </button>
            {locationError && (
              <div className="w-full mt-2 text-red-600 text-xs">{locationError}</div>
            )}
          </form>
        ) : (
          <div className="flex items-center flex-wrap">
            {localStores.length > 0 && (
              <>
                <div className="text-sm text-indigo-700 mr-2 mb-2 sm:mb-0">
                  Showing prices at:
                </div>
                <select
                  value={selectedStore || ''}
                  onChange={handleStoreChange}
                  className="store-dropdown border border-indigo-300 rounded p-1 text-sm bg-white mr-2 mb-2 sm:mb-0"
                >
                  {localStores.map(store => (
                    <option key={store.id} value={store.id}>
                      {store.name} ({store.distance})
                    </option>
                  ))}
                </select>
                
                <div className="flex items-center mr-2 mb-2 sm:mb-0">
                  <label htmlFor="radius-select-active" className="text-sm text-indigo-700 mr-2">
                    Radius:
                  </label>
                  <select
                    id="radius-select-active"
                    value={searchRadius}
                    onChange={handleRadiusChange}
                    className="p-1 border border-indigo-300 rounded text-sm bg-white mr-2"
                  >
                    <option value="1">1 mile</option>
                    <option value="3">3 miles</option>
                    <option value="5">5 miles</option>
                    <option value="10">10 miles</option>
                    <option value="15">15 miles</option>
                    <option value="25">25 miles</option>
                  </select>
                </div>
                
                <button
                  onClick={() => setShowZipPrompt(true)}
                  className="text-xs text-indigo-500 underline mb-2 sm:mb-0 mr-3"
                >
                  Change Location
                </button>
                
                <button 
                  onClick={toggleMap}
                  className="store-map-toggle mb-2 sm:mb-0"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6-3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
                  </svg>
                  {showMap ? 'Hide Map' : 'Show Map'}
                </button>
              </>
            )}
          </div>
        )}
        
        {/* Store Location Map */}
        {showMap && currentCoordinates && localStores.length > 0 && (
          <StoreLocationMap 
            userLocation={currentCoordinates}
            stores={localStores}
            selectedStoreId={selectedStore}
            onStoreSelect={handleStoreSelect}
            radius={searchRadius}
          />
        )}
      </div>
      
      {/* Recipe Filters */}
      {localStores.length > 0 && (
        <RecipeFilters 
          filters={filters}
          onFilterChange={handleFilterChange}
        />
      )}
      
      {/* Custom navigation buttons with ARIA labels */}
      <div 
        ref={prevButtonRef}
        className={`recipe-nav-button recipe-nav-prev ${isBeginning ? 'recipe-nav-disabled' : ''}`}
        aria-label="Previous recipe" 
        role="button"
        tabIndex="0"
      ></div>
      <div 
        ref={nextButtonRef}
        className={`recipe-nav-button recipe-nav-next ${isEnd ? 'recipe-nav-disabled' : ''}`}
        aria-label="Next recipe" 
        role="button"
        tabIndex="0"
      ></div>
      
      <Swiper
        ref={swiperRef}
        modules={[Navigation, Pagination, Autoplay, A11y, Keyboard, EffectFade]}
        spaceBetween={30}
        slidesPerView={1}
        navigation={{
          prevEl: '.recipe-nav-prev',
          nextEl: '.recipe-nav-next',
          disabledClass: 'recipe-nav-disabled'
        }}
        pagination={{ 
          clickable: true,
          dynamicBullets: true,
          type: 'bullets'
        }}
        autoplay={{ 
          delay: 7000,
          disableOnInteraction: true,
          pauseOnMouseEnter: true
        }}
        a11y={{
          prevSlideMessage: 'Previous recipe',
          nextSlideMessage: 'Next recipe',
          firstSlideMessage: 'This is the first recipe',
          lastSlideMessage: 'This is the last recipe'
        }}
        keyboard={{
          enabled: true,
          onlyInViewport: true
        }}
        speed={600}
        threshold={5}
        loop={false}
        grabCursor={true}
        touchRatio={1.5}
        simulateTouch={true}
        onSlideChange={handleSlideChange}
        onSwiper={(swiper) => {
          // Initialize slide position states
          setIsBeginning(swiper.isBeginning);
          setIsEnd(swiper.isEnd);
          setActiveIndex(swiper.activeIndex);
        }}
        className="recipe-swiper single-recipe-view"
        effect="slide"
      >
        {filteredRecipes.length > 0 ? (
          filteredRecipes.map((recipe) => (
            <SwiperSlide key={recipe.id || recipe.title} className="recipe-slide">
              <div className="recipe-card">
                <div className="recipe-card-inner">
                  <div className="recipe-header">
                    <div className="recipe-image-container">
                      <img 
                        src={proxyImage(recipe.image)} 
                        alt={recipe.title} 
                        className="recipe-image"
                      />
                    </div>
                    
                    {/* Price display - moved outside the image container */}
                    {recipePrices[recipe.id] ? (
                      <div className="recipe-cost local-price">
                        <span className="price-value">{recipePrices[recipe.id].totalPrice}</span>
                        <span className="price-store">at {recipePrices[recipe.id].storeName}</span>
                        <span className="price-availability">
                          {recipePrices[recipe.id].inStockPercentage} ingredients in stock
                        </span>
                        
                        {recipePrices[recipe.id].snapEligiblePercentage && (
                          <div className="snap-eligible-badge">
                            <span className="snap-icon">🛒</span> 
                            <span>{recipePrices[recipe.id].snapEligiblePercentage} SNAP eligible</span>
                          </div>
                        )}
                        
                        {recipePrices[recipe.id].note && (
                          <div className="price-note text-xs italic">{recipePrices[recipe.id].note}</div>
                        )}
                        
                        {recipePrices[recipe.id].searchUrl && (
                          <a 
                            href={recipePrices[recipe.id].searchUrl.replace('{searchTerm}', encodeURIComponent(recipe.title))} 
                            target="_blank"
                            rel="noopener noreferrer"
                            className="check-price-btn bg-white text-sm text-green-700 rounded px-2 py-1 mt-1 inline-block hover:bg-green-50"
                          >
                            Check price at {recipePrices[recipe.id].brand ? 
                              recipePrices[recipe.id].brand.charAt(0).toUpperCase() + 
                              recipePrices[recipe.id].brand.slice(1) : 
                              recipePrices[recipe.id].storeName}
                          </a>
                        )}
                      </div>
                    ) : (
                      <div className="recipe-cost">
                        {isLoadingPrices ? 
                          <span className="loading-text">Loading price estimate...</span> : 
                          recipe.cost
                        }
                      </div>
                    )}
                    
                    <div className="recipe-main-content">
                      <h3 className="recipe-title">{recipe.title}</h3>
                      
                      {recipe.tags && recipe.tags.length > 0 && (
                        <div className="recipe-tags">
                          {recipe.tags.map((tag, index) => (
                            <span key={index} className="recipe-tag">
                              {tag}
                            </span>
                          ))}
                        </div>
                      )}
                      
                      {recipePrices[recipe.id] && recipePrices[recipe.id].dentalFriendlyPercentage && (
                        <div className={`dental-health-rating dental-${
                          parseInt(recipePrices[recipe.id].dentalFriendlyPercentage) >= 90 ? 'excellent' :
                          parseInt(recipePrices[recipe.id].dentalFriendlyPercentage) >= 70 ? 'good' :
                          parseInt(recipePrices[recipe.id].dentalFriendlyPercentage) >= 50 ? 'fair' : 'poor'
                        }`}>
                          <span className="dental-icon">
                            {parseInt(recipePrices[recipe.id].dentalFriendlyPercentage) >= 90 ? '😁' : 
                             parseInt(recipePrices[recipe.id].dentalFriendlyPercentage) >= 70 ? '🙂' : 
                             parseInt(recipePrices[recipe.id].dentalFriendlyPercentage) >= 50 ? '😐' : '😕'}
                          </span>
                          <span>Dental Health: {
                            parseInt(recipePrices[recipe.id].dentalFriendlyPercentage) >= 90 ? 'Excellent' :
                            parseInt(recipePrices[recipe.id].dentalFriendlyPercentage) >= 70 ? 'Good' :
                            parseInt(recipePrices[recipe.id].dentalFriendlyPercentage) >= 50 ? 'Fair' : 'Poor'
                          }</span>
                        </div>
                      )}
                      
                      <p className="recipe-description">{recipe.description}</p>
                    </div>
                  </div>
                  
                  {showingRecipeDetails[recipe.id || recipe.title] && (
                    <div className="recipe-details">
                      {/* Price Summary - shown when we have price data */}
                      {selectedStore && recipePrices[recipe.id] && (
                        <RecipePriceSummary
                          recipe={recipe}
                          recipePrices={recipePrices[recipe.id]}
                          selectedStore={selectedStore}
                          storeInfo={localStores.find(store => store.id === selectedStore)}
                          showCostPerServing={true}
                        />
                      )}
                      
                      {recipe.ingredients && recipe.ingredients.length > 0 && (
                        <div className="recipe-ingredients">
                          <h4 className="recipe-section-title">
                            Ingredients
                            {selectedStore && (
                              <button
                                onClick={() => toggleDetailedPrices(recipe.id)}
                                className="text-xs text-indigo-500 ml-2 underline"
                              >
                                {showDetailedPrices[recipe.id] ? 'Hide prices' : 'Show prices'}
                              </button>
                            )}
                          </h4>
                          <ul className="recipe-list">
                            {recipe.ingredients.map((ingredient, index) => (
                              <li key={index} className={showDetailedPrices[recipe.id] ? "flex justify-between items-center mb-2" : ""}>
                                <span>{ingredient}</span>
                                {showDetailedPrices[recipe.id] && selectedStore && (
                                  <RecipeIngredientPrice 
                                    ingredient={ingredient}
                                    storeId={selectedStore}
                                    onPriceUpdate={(ingredient, priceData) => 
                                      handleIngredientPriceUpdate(recipe.id, ingredient, priceData)
                                    }
                                  />
                                )}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                      
                      {recipe.instructions && recipe.instructions.length > 0 && (
                        <div className="recipe-instructions">
                          <h4 className="recipe-section-title">Instructions</h4>
                          <ol className="recipe-list recipe-steps">
                            {recipe.instructions.map((step, index) => (
                              <li key={index}>{step}</li>
                            ))}
                          </ol>
                        </div>
                      )}
                      
                      {recipe.dentalBenefits && (
                        <div className="recipe-benefits">
                          <h4 className="recipe-section-title">Dental Benefits</h4>
                          <p className="recipe-benefits-text">{recipe.dentalBenefits}</p>
                        </div>
                      )}
                    </div>
                  )}
                  
                  <div className="recipe-actions">
                    <button
                      onClick={() => toggleRecipeDetails(recipe.id || recipe.title)}
                      className="recipe-details-btn"
                    >
                      {showingRecipeDetails[recipe.id || recipe.title] ? 'Hide Details' : 'Show Details'}
                    </button>
                    <button
                      onClick={() => handleAddIngredientsToCart(recipe)}
                      className="recipe-add-btn"
                    >
                      Add Ingredients to Cart
                    </button>
                  </div>
                </div>
              </div>
            </SwiperSlide>
          ))
        ) : (
          <SwiperSlide className="recipe-slide">
            <div className="recipe-card">
              <div className="recipe-card-inner p-8 text-center">
                <div className="text-gray-500 text-lg mb-4">No recipes match your filters</div>
                <button
                  onClick={() => setFilters({
                    dentalFriendly: false,
                    snapEligible: false,
                    priceRange: 'all',
                    alternatives: null
                  })}
                  className="bg-indigo-500 text-white px-4 py-2 rounded hover:bg-indigo-600"
                >
                  Reset Filters
                </button>
              </div>
            </div>
          </SwiperSlide>
        )}
      </Swiper>
    </div>
  );
};

// Recipe Card with price range display and dental info
const RecipeCard = ({ recipe, recipePrices, showingDetails, onToggleDetails, onAddToCart, showDetailedPrices, onToggleDetailedPrices, store }) => {
  return (
    <div className="recipe-card">
      <div className="recipe-card-inner">
        <div className="recipe-header">
          <div className="recipe-image-container">
            <img 
              src={proxyImage(recipe.image)} 
              alt={recipe.title} 
              className="recipe-image"
            />
          </div>
          
          {/* Price display - moved outside the image container */}
          {recipePrices ? (
            <div className="recipe-cost local-price">
              <span className="price-value">{recipePrices.totalPrice}</span>
              <span className="price-store">at {recipePrices.storeName}</span>
              <span className="price-availability">
                {recipePrices.inStockPercentage} ingredients in stock
              </span>
              
              {recipePrices.snapEligiblePercentage && (
                <div className="snap-eligible-badge">
                  <span className="snap-icon">🛒</span> 
                  <span>{recipePrices.snapEligiblePercentage} SNAP eligible</span>
                </div>
              )}
              
              {recipePrices.note && (
                <div className="price-note text-xs italic">{recipePrices.note}</div>
              )}
              
              {recipePrices.searchUrl && (
                <a 
                  href={recipePrices.searchUrl.replace('{searchTerm}', encodeURIComponent(recipe.title))} 
                  target="_blank"
                  rel="noopener noreferrer"
                  className="check-price-btn bg-white text-sm text-green-700 rounded px-2 py-1 mt-1 inline-block hover:bg-green-50"
                >
                  Check price at {recipePrices.brand ? 
                    recipePrices.brand.charAt(0).toUpperCase() + 
                    recipePrices.brand.slice(1) : 
                    recipePrices.storeName}
                </a>
              )}
            </div>
          ) : (
            <div className="recipe-cost">
              {isLoadingPrices ? 
                <span className="loading-text">Loading price estimate...</span> : 
                recipe.cost
              }
            </div>
          )}
          
          <div className="recipe-main-content">
            <h3 className="recipe-title">{recipe.title}</h3>
            
            {recipe.tags && recipe.tags.length > 0 && (
              <div className="recipe-tags">
                {recipe.tags.map((tag, index) => (
                  <span key={index} className="recipe-tag">
                    {tag}
                  </span>
                ))}
              </div>
            )}
            
            {recipe.dentalHealthRating && (
              <div className={`dental-health-rating dental-${recipe.dentalHealthRating}`}>
                <span className="dental-icon">
                  {recipe.dentalHealthRating === 'excellent' ? '😁' : 
                   recipe.dentalHealthRating === 'good' ? '🙂' : 
                   recipe.dentalHealthRating === 'fair' ? '😐' : '😕'}
                </span>
                <span>Dental Health: {recipe.dentalHealthRating.charAt(0).toUpperCase() + recipe.dentalHealthRating.slice(1)}</span>
              </div>
            )}
            
            <p className="recipe-description">{recipe.description}</p>
          </div>
        </div>
        
        {showingDetails && (
          <div className="recipe-details">
            {/* Price Summary - shown when we have price data */}
            {store && recipePrices && (
              <RecipePriceSummary
                recipe={recipe}
                recipePrices={recipePrices}
                selectedStore={store.id}
                storeInfo={store}
                showCostPerServing={true}
              />
            )}
            
            {recipe.ingredients && recipe.ingredients.length > 0 && (
              <div className="recipe-ingredients">
                <h4 className="recipe-section-title">
                  Ingredients
                  {store && (
                    <button
                      onClick={onToggleDetailedPrices}
                      className="text-xs text-indigo-500 ml-2 underline"
                    >
                      {showDetailedPrices ? 'Hide details' : 'Show details'}
                    </button>
                  )}
                </h4>
                <ul className="recipe-list">
                  {recipe.ingredients.map((ingredient, index) => (
                    <li key={index} className={showDetailedPrices ? "flex justify-between items-center mb-2" : ""}>
                      <span>{ingredient}</span>
                      {showDetailedPrices && store && (
                        <RecipeIngredientPrice 
                          ingredient={ingredient}
                          storeId={store.id}
                          onPriceUpdate={(ingredient, priceData) => 
                            handleIngredientPriceUpdate(recipe.id, ingredient, priceData)
                          }
                        />
                      )}
                    </li>
                  ))}
                </ul>
              </div>
            )}
            
            {recipe.instructions && recipe.instructions.length > 0 && (
              <div className="recipe-instructions">
                <h4 className="recipe-section-title">Instructions</h4>
                <ol className="recipe-list recipe-steps">
                  {recipe.instructions.map((step, index) => (
                    <li key={index}>{step}</li>
                  ))}
                </ol>
              </div>
            )}
            
            {recipe.dentalBenefits && (
              <div className="recipe-benefits">
                <h4 className="recipe-section-title">Dental Benefits</h4>
                <p className="recipe-benefits-text">{recipe.dentalBenefits}</p>
              </div>
            )}
          </div>
        )}
        
        <div className="recipe-actions">
          <button
            onClick={onToggleDetails}
            className="recipe-details-btn"
          >
            {showingDetails ? 'Hide Details' : 'Show Details'}
          </button>
          <button
            onClick={onAddToCart}
            className="recipe-add-btn"
          >
            Add Ingredients to Cart
          </button>
        </div>
      </div>
    </div>
  );
};

export default RecipeCarousel; 