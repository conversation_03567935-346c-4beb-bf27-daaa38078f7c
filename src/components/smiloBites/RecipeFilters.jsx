import React from 'react';

/**
 * Recipe filtering component
 * Allows users to filter recipes by dental health, SNAP eligibility, and price range
 */
const RecipeFilters = ({ filters, onFilterChange }) => {
  return (
    <div className="recipe-filters bg-gray-50 rounded-md p-3 mb-4 border border-gray-200">
      <h3 className="text-sm font-semibold text-gray-700 mb-2">Filter Recipes</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
        {/* Dental Health Filter */}
        <div className="filter-group">
          <label className="flex items-center cursor-pointer">
            <input 
              type="checkbox" 
              checked={filters.dentalFriendly} 
              onChange={() => onFilterChange('dentalFriendly', !filters.dentalFriendly)}
              className="form-checkbox h-4 w-4 text-indigo-600 transition duration-150 ease-in-out"
            />
            <span className="ml-2 text-sm text-gray-700 flex items-center">
              <span className="mr-1">😁</span>
              Dental-Friendly Only
            </span>
          </label>
          {filters.dentalFriendly && (
            <p className="text-xs text-gray-500 ml-6 mt-1">
              Showing recipes that are good for your teeth
            </p>
          )}
        </div>
        
        {/* SNAP Eligibility Filter */}
        <div className="filter-group">
          <label className="flex items-center cursor-pointer">
            <input 
              type="checkbox" 
              checked={filters.snapEligible} 
              onChange={() => onFilterChange('snapEligible', !filters.snapEligible)}
              className="form-checkbox h-4 w-4 text-indigo-600 transition duration-150 ease-in-out"
            />
            <span className="ml-2 text-sm text-gray-700 flex items-center">
              <span className="mr-1">🛒</span>
              SNAP/EBT Eligible Only
            </span>
          </label>
          {filters.snapEligible && (
            <p className="text-xs text-gray-500 ml-6 mt-1">
              Showing recipes with SNAP/EBT eligible ingredients
            </p>
          )}
        </div>
        
        {/* Price Range Filter */}
        <div className="filter-group">
          <label className="block text-sm text-gray-700 mb-1">Price Range:</label>
          <select 
            value={filters.priceRange} 
            onChange={(e) => onFilterChange('priceRange', e.target.value)}
            className="form-select block w-full pl-3 pr-10 py-1 text-sm leading-6 border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 rounded-md"
          >
            <option value="all">All Prices</option>
            <option value="under5">Under $5</option>
            <option value="5to10">$5 - $10</option>
            <option value="10to20">$10 - $20</option>
            <option value="over20">Over $20</option>
          </select>
        </div>
      </div>
      
      {/* Alternative Suggestions */}
      <div className="mt-3 pt-3 border-t border-gray-200">
        <h4 className="text-xs font-medium text-gray-700 mb-1">Try these healthy alternatives:</h4>
        <div className="flex flex-wrap gap-2">
          <button 
            onClick={() => onFilterChange('alternatives', 'low-sugar')}
            className="px-2 py-1 bg-green-50 text-green-700 rounded-full text-xs hover:bg-green-100 transition-colors"
          >
            Low Sugar Options
          </button>
          <button 
            onClick={() => onFilterChange('alternatives', 'high-calcium')}
            className="px-2 py-1 bg-blue-50 text-blue-700 rounded-full text-xs hover:bg-blue-100 transition-colors"
          >
            Calcium Rich
          </button>
          <button 
            onClick={() => onFilterChange('alternatives', 'teeth-cleaning')}
            className="px-2 py-1 bg-indigo-50 text-indigo-700 rounded-full text-xs hover:bg-indigo-100 transition-colors"
          >
            Natural Teeth Cleaners
          </button>
        </div>
      </div>
    </div>
  );
};

export default RecipeFilters; 