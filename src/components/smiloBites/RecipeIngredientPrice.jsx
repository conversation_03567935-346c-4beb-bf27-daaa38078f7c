import React, { useState, useEffect } from 'react';
import groceryPriceService from '../../lib/services/groceryPriceService';

/**
 * Component for displaying individual ingredient prices from local grocery stores
 * Also allows users to update prices with their own values
 */
const RecipeIngredientPrice = ({ ingredient, storeId, onPriceUpdate }) => {
  const [ingredientPrice, setIngredientPrice] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [showEditPrice, setShowEditPrice] = useState(false);
  const [userPrice, setUserPrice] = useState('');
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    fetchIngredientPrice();
  }, [ingredient, storeId]);

  const fetchIngredientPrice = async () => {
    setIsLoading(true);
    setError('');
    
    try {
      // Use the new price range API
      const priceData = await groceryPriceService.getIngredientPriceRange(ingredient, storeId);
      setIngredientPrice(priceData);
    } catch (error) {
      console.error(`Error fetching price for ${ingredient}:`, error);
      setError('Could not fetch price');
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditToggle = () => {
    setShowEditPrice(!showEditPrice);
    if (!showEditPrice && ingredientPrice?.stores[storeId]) {
      // Pre-fill with current price, removing the $ sign
      // Handle price range by using the min value
      const priceValue = ingredientPrice.stores[storeId].price;
      const priceMatch = priceValue.match(/\$(\d+(\.\d+)?)/);
      if (priceMatch) {
        setUserPrice(priceMatch[1]);
      }
    }
  };

  const handlePriceSubmit = (e) => {
    e.preventDefault();
    
    // Validate price format
    const priceRegex = /^\d+(\.\d{1,2})?$/;
    if (!priceRegex.test(userPrice)) {
      setError('Please enter a valid price (e.g., 3.99)');
      return;
    }
    
    // Update the price in our state
    const userPriceValue = parseFloat(userPrice);
    const updatedPrice = {
      ...ingredientPrice,
      stores: {
        ...ingredientPrice.stores,
        [storeId]: {
          ...ingredientPrice.stores[storeId],
          priceRange: { min: userPriceValue, max: userPriceValue },
          price: `$${userPrice}`,
          // Mark as user-updated
          userUpdated: true
        }
      }
    };
    
    setIngredientPrice(updatedPrice);
    setShowEditPrice(false);
    setError('');
    
    // Notify parent component about the price update
    if (onPriceUpdate) {
      onPriceUpdate(ingredient, updatedPrice);
    }
  };

  const toggleDetails = () => {
    setShowDetails(!showDetails);
  };

  if (isLoading) {
    return <div className="text-sm text-gray-500 italic">Loading price...</div>;
  }

  if (error && !ingredientPrice) {
    return <div className="text-sm text-red-500">{error}</div>;
  }

  if (!ingredientPrice || !ingredientPrice.stores[storeId]) {
    return <div className="text-sm text-gray-500">Price unavailable</div>;
  }

  const price = ingredientPrice.stores[storeId];

  return (
    <div className="ingredient-price-container">
      {showEditPrice ? (
        <form onSubmit={handlePriceSubmit} className="flex items-center">
          <span className="text-gray-600 mr-1">$</span>
          <input
            type="text"
            value={userPrice}
            onChange={(e) => setUserPrice(e.target.value)}
            className="border border-indigo-300 rounded px-2 py-1 w-16 text-sm"
            placeholder="0.00"
            autoFocus
          />
          <button
            type="submit"
            className="ml-2 text-xs bg-indigo-500 text-white px-2 py-1 rounded"
          >
            Save
          </button>
          <button
            type="button"
            onClick={handleEditToggle}
            className="ml-1 text-xs text-indigo-500 px-2 py-1"
          >
            Cancel
          </button>
          {error && <div className="text-xs text-red-500 ml-2">{error}</div>}
        </form>
      ) : (
        <div className="flex flex-col">
          <div className="flex items-center">
            <span className={`text-sm font-medium ${price.inStock ? 'text-gray-700' : 'text-red-500'}`}>
              {price.price}
              {!price.inStock && <span className="ml-1 text-xs bg-red-100 text-red-800 px-1 rounded">Out of stock</span>}
            </span>
            {price.userUpdated && <span className="text-xs text-blue-500 ml-1">(Your price)</span>}
            <button
              onClick={handleEditToggle}
              className="ml-2 text-xs text-indigo-500"
              title="Update price"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
              </svg>
            </button>
            <button
              onClick={toggleDetails}
              className="ml-1 text-xs text-gray-500"
              title="Show details"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </button>
          </div>
          
          {showDetails && (
            <div className="text-xs mt-1 ml-1 bg-gray-50 p-1 rounded border border-gray-200">
              {/* SNAP Eligibility */}
              {price.snapEligible !== undefined && (
                <div className="flex items-center mb-1">
                  <span className={`w-3 h-3 rounded-full ${price.snapEligible ? 'bg-green-100' : 'bg-red-100'} flex items-center justify-center mr-1`}>
                    <span className={`text-xs ${price.snapEligible ? 'text-green-700' : 'text-red-700'}`}>
                      {price.snapEligible ? '✓' : '✗'}
                    </span>
                  </span>
                  <span className={price.snapEligible ? 'text-green-700' : 'text-red-700'}>
                    {price.snapEligible ? 'SNAP eligible' : 'Not SNAP eligible'}
                  </span>
                </div>
              )}
              
              {/* Dental Benefits */}
              {price.dentalBenefits && (
                <div className="mb-1">
                  <span className={`text-xs ${price.dentalBenefits.isFriendly ? 'text-green-700' : 'text-yellow-700'}`}>
                    {price.dentalBenefits.isFriendly ? (
                      <>😁 {price.dentalBenefits.benefit}</>
                    ) : (
                      <>😕 {price.dentalBenefits.reason}</>
                    )}
                  </span>
                </div>
              )}
              
              {/* Store Search Link */}
              {price.searchUrl && (
                <a 
                  href={price.searchUrl} 
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-indigo-600 hover:underline block"
                >
                  Check price online
                </a>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default RecipeIngredientPrice; 