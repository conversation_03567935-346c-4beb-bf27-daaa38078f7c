import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import groceryPriceService from '../../lib/services/groceryPriceService';
import proxyImage from '../../lib/utils/imageProxy';

/**
 * Component for searching and displaying SNAP-eligible healthy groceries
 * that support dental health
 */
const HealthyGrocerySearch = ({ onAddToCart, initialSearchTerm = '' }) => {
  const [searchTerm, setSearchTerm] = useState(initialSearchTerm);
  const [searchResults, setSearchResults] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [filterOptions, setFilterOptions] = useState({
    onlySnapEligible: true,
    onlyDentalFriendly: true,
    priceRange: 'all',
  });
  const [selectedStore, setSelectedStore] = useState('walmart-1'); // Default store
  const [healthyFoods, setHealthyFoods] = useState([]);

  // Initial load of healthy food options
  useEffect(() => {
    // Load a default list of healthy foods
    loadHealthyFoods();
  }, []);

  // This would typically fetch from an API or database
  // For now, we're hard-coding based on the dental health information found in the codebase
  const loadHealthyFoods = async () => {
    setIsLoading(true);
    
    // Generate a comprehensive list of healthy, SNAP-eligible foods
    const foodCategories = [
      // Calcium-rich foods
      { category: 'calcium', items: [
        'milk', 'yogurt', 'cheese', 'leafy greens', 'kale', 'spinach', 'almonds', 'fortified plant milk',
        'sardines', 'canned salmon', 'tofu', 'white beans', 'chia seeds'
      ]},
      // Vitamin D rich foods
      { category: 'vitamin-d', items: [
        'salmon', 'tuna', 'mackerel', 'egg yolks', 'mushrooms', 'fortified milk', 'fortified cereal'
      ]},
      // Phosphorus-rich foods
      { category: 'phosphorus', items: [
        'fish', 'lean meat', 'eggs', 'dairy', 'nuts', 'beans', 'lentils', 'whole grains'
      ]},
      // Vitamin C rich foods
      { category: 'vitamin-c', items: [
        'oranges', 'strawberries', 'kiwi', 'bell peppers', 'broccoli', 'tomatoes', 'brussels sprouts'
      ]},
      // Fibrous detergent foods
      { category: 'detergent', items: [
        'apples', 'carrots', 'celery', 'pears', 'cucumbers', 'raw broccoli', 'cauliflower'
      ]},
      // Antioxidant-rich foods
      { category: 'antioxidant', items: [
        'berries', 'green tea', 'dark chocolate', 'beans', 'artichokes', 'kale', 'beets'
      ]},
      // Foods with antimicrobial properties
      { category: 'antimicrobial', items: [
        'garlic', 'onions', 'ginger', 'cinnamon', 'turmeric', 'cloves', 'raw honey'
      ]},
      // Probiotics
      { category: 'probiotic', items: [
        'yogurt', 'kefir', 'sauerkraut', 'kimchi', 'tempeh', 'miso', 'kombucha'
      ]}
    ];
    
    // Flatten the list for searching
    const allHealthyFoods = [];
    
    for (const category of foodCategories) {
      for (const item of category.items) {
        // Get price estimate and other details
        try {
          const priceData = await groceryPriceService.getIngredientPriceRange(item, selectedStore);
          
          // If we have price data and it's SNAP eligible
          if (priceData.stores[selectedStore] && priceData.stores[selectedStore].snapEligible) {
            const storeData = priceData.stores[selectedStore];
            
            allHealthyFoods.push({
              id: `${item.replace(/\s+/g, '-')}-${category.category}`,
              name: item.charAt(0).toUpperCase() + item.slice(1),
              category: category.category,
              price: storeData.price,
              priceValue: storeData.priceRange.min,
              benefit: storeData.dentalBenefits.isFriendly 
                ? storeData.dentalBenefits.benefit 
                : 'Supports overall dental health',
              dentalFriendly: storeData.dentalBenefits.isFriendly,
              snapEligible: storeData.snapEligible,
              image: getRandomFoodImage(item, category.category),
              searchUrl: storeData.searchUrl,
              store: selectedStore
            });
          }
        } catch (error) {
          console.error(`Error getting price for ${item}:`, error);
        }
      }
    }
    
    // Sort by price
    allHealthyFoods.sort((a, b) => a.priceValue - b.priceValue);
    
    setHealthyFoods(allHealthyFoods);
    setSearchResults(allHealthyFoods.slice(0, 12)); // Show first 12 by default
    setIsLoading(false);
  };
  
  // Get a placeholder image for a food item
  const getRandomFoodImage = (food, category) => {
    // In a real app, this would be replaced with actual food images
    // For now, we're using Unsplash random images based on the food name
    return `https://source.unsplash.com/featured/?${encodeURIComponent(food)},food`;
  };
  
  // Handle search input changes
  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };
  
  // Handle searching for foods
  const handleSearch = (e) => {
    e.preventDefault();
    performSearch();
  };
  
  // Handle filter changes
  const handleFilterChange = (filterName, value) => {
    setFilterOptions(prev => ({
      ...prev,
      [filterName]: value
    }));
  };
  
  // Perform the search and filtering
  const performSearch = () => {
    setIsLoading(true);
    
    // Filter the list based on search term and filter options
    let results = [...healthyFoods];
    
    // Apply search term filter
    if (searchTerm.trim() !== '') {
      const searchLower = searchTerm.toLowerCase();
      results = results.filter(food => 
        food.name.toLowerCase().includes(searchLower) ||
        food.benefit.toLowerCase().includes(searchLower) ||
        food.category.toLowerCase().includes(searchLower)
      );
    }
    
    // Apply SNAP eligibility filter
    if (filterOptions.onlySnapEligible) {
      results = results.filter(food => food.snapEligible);
    }
    
    // Apply dental-friendly filter
    if (filterOptions.onlyDentalFriendly) {
      results = results.filter(food => food.dentalFriendly);
    }
    
    // Apply price range filter
    if (filterOptions.priceRange !== 'all') {
      switch (filterOptions.priceRange) {
        case 'under2':
          results = results.filter(food => food.priceValue < 2);
          break;
        case '2to5':
          results = results.filter(food => food.priceValue >= 2 && food.priceValue <= 5);
          break;
        case 'over5':
          results = results.filter(food => food.priceValue > 5);
          break;
        default:
          break;
      }
    }
    
    setSearchResults(results);
    setIsLoading(false);
  };
  
  // When filters change, re-run the search
  useEffect(() => {
    performSearch();
  }, [filterOptions, selectedStore]);
  
  // When initialSearchTerm changes, update the search term and perform a search
  useEffect(() => {
    if (initialSearchTerm) {
      setSearchTerm(initialSearchTerm);
      // Wait for healthy foods to load before searching
      if (healthyFoods.length > 0) {
        performSearch();
      }
    }
  }, [initialSearchTerm, healthyFoods.length]);
  
  // Handle adding an item to the cart
  const handleAddToCart = (item) => {
    if (onAddToCart) {
      const cartItem = {
        id: item.id,
        name: item.name,
        price: item.price,
        benefit: item.benefit,
        category: 'grocery',
        snapEligible: item.snapEligible,
        dentalFriendly: item.dentalFriendly
      };
      
      onAddToCart(cartItem);
    }
  };
  
  // Handle store selection change
  const handleStoreChange = (e) => {
    setSelectedStore(e.target.value);
    // Reload food data with the new store
    loadHealthyFoods();
  };
  
  // Show a loading message while data is being fetched
  if (isLoading && healthyFoods.length === 0) {
    return (
      <div className="p-4 bg-white rounded-lg shadow">
        <h2 className="text-xl font-bold text-indigo-800 mb-4">Healthy Grocery Search</h2>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
          <span className="ml-3 text-indigo-600">Loading healthy foods...</span>
        </div>
      </div>
    );
  }
  
  return (
    <div className="p-4 bg-white rounded-lg shadow">
      <h2 className="text-xl font-bold text-indigo-800 mb-4">
        {initialSearchTerm ? 
          `Search Results for "${initialSearchTerm}"` : 
          'SNAP-Eligible Dental-Healthy Foods'}
      </h2>
      
      {/* Search and filter controls */}
      <div className="mb-6">
        <form onSubmit={handleSearch} className="flex flex-wrap gap-2 mb-4">
          <input
            type="text"
            placeholder="Search for SNAP-eligible healthy foods..."
            value={searchTerm}
            onChange={handleSearchChange}
            className="flex-grow p-2 border border-indigo-300 rounded text-gray-800"
          />
          <button
            type="submit"
            className="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700 transition-colors"
          >
            Search
          </button>
          
          <select
            value={selectedStore}
            onChange={handleStoreChange}
            className="p-2 border border-indigo-300 rounded text-gray-800"
          >
            <option value="walmart-1">Walmart</option>
            <option value="target-1">Target</option>
            <option value="kroger-1">Kroger</option>
            <option value="wholefoods-1">Whole Foods</option>
          </select>
        </form>
        
        <div className="flex flex-wrap gap-4">
          {/* SNAP Eligibility Filter */}
          <label className="flex items-center text-sm">
            <input 
              type="checkbox" 
              checked={filterOptions.onlySnapEligible} 
              onChange={() => handleFilterChange('onlySnapEligible', !filterOptions.onlySnapEligible)}
              className="mr-2"
            />
            SNAP/EBT Eligible Only
          </label>
          
          {/* Dental Friendly Filter */}
          <label className="flex items-center text-sm">
            <input 
              type="checkbox" 
              checked={filterOptions.onlyDentalFriendly} 
              onChange={() => handleFilterChange('onlyDentalFriendly', !filterOptions.onlyDentalFriendly)}
              className="mr-2"
            />
            Dental-Friendly Only
          </label>
          
          {/* Price Range Filter */}
          <div className="flex items-center text-sm">
            <span className="mr-2">Price:</span>
            <select
              value={filterOptions.priceRange}
              onChange={(e) => handleFilterChange('priceRange', e.target.value)}
              className="p-1 border border-indigo-300 rounded text-gray-800"
            >
              <option value="all">All Prices</option>
              <option value="under2">Under $2</option>
              <option value="2to5">$2 - $5</option>
              <option value="over5">Over $5</option>
            </select>
          </div>
        </div>
      </div>
      
      {/* Results count */}
      <div className="mb-4 text-sm text-gray-600">
        Showing {searchResults.length} dental-healthy foods
        {filterOptions.onlySnapEligible ? ' that are SNAP/EBT eligible' : ''}
      </div>
      
      {/* Results grid */}
      {isLoading ? (
        <div className="flex justify-center items-center h-40">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-500"></div>
          <span className="ml-3 text-indigo-600">Searching...</span>
        </div>
      ) : searchResults.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {searchResults.map((item) => (
            <motion.div
              key={item.id}
              whileHover={{ scale: 1.03 }}
              className="bg-white rounded-lg overflow-hidden shadow-sm border border-gray-200"
            >
              <div className="relative h-40">
                <img 
                  src={proxyImage(item.image)} 
                  alt={item.name} 
                  className="w-full h-full object-cover"
                />
                {item.snapEligible && (
                  <div className="absolute top-2 right-2 bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded">
                    SNAP Eligible
                  </div>
                )}
                {item.dentalFriendly && (
                  <div className="absolute top-2 left-2 bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded flex items-center">
                    <span className="mr-1">😁</span> Dental-Friendly
                  </div>
                )}
              </div>
              <div className="p-4">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="font-semibold text-indigo-900">{item.name}</h3>
                  <span className="text-indigo-700 font-medium">{item.price}</span>
                </div>
                <p className="text-sm text-indigo-600 mb-3">{item.benefit}</p>
                <div className="flex flex-col gap-2">
                  <button
                    onClick={() => handleAddToCart(item)}
                    className="w-full bg-indigo-600 hover:bg-indigo-700 text-white text-sm py-2 rounded transition-colors"
                  >
                    Add to Cart
                  </button>
                  {item.searchUrl && (
                    <a 
                      href={item.searchUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-center w-full bg-white border border-indigo-600 text-indigo-600 hover:bg-indigo-50 text-sm py-2 rounded transition-colors"
                    >
                      Check Store Price
                    </a>
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      ) : (
        <div className="text-center p-8 bg-gray-50 rounded-lg">
          <p className="text-gray-600 mb-4">No matching foods found. Try adjusting your search or filters.</p>
          <button
            onClick={() => {
              setSearchTerm('');
              setFilterOptions({
                onlySnapEligible: true,
                onlyDentalFriendly: true,
                priceRange: 'all',
              });
            }}
            className="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700 transition-colors"
          >
            Reset Filters
          </button>
        </div>
      )}
    </div>
  );
};

export default HealthyGrocerySearch; 