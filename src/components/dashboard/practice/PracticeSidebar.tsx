import React from 'react';

interface PracticeSidebarProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
  isMobileOpen: boolean;
  closeMobileSidebar: () => void;
  practiceData: any;
}

const PracticeSidebar: React.FC<PracticeSidebarProps> = ({ 
  activeTab, 
  setActiveTab, 
  isMobileOpen, 
  closeMobileSidebar, 
  practiceData 
}) => {
  const tabs = [
    { id: 'overview', label: 'Overview' },
    { id: 'leads', label: 'Lead Management' },
    { id: 'patients', label: 'Patient Directory' },
    { id: 'appointments', label: 'Appointments' },
    { id: 'financials', label: 'Financial Metrics' },
    { id: 'settings', label: 'Settings' }
  ];

  return (
    <div className={`bg-white shadow-lg ${isMobileOpen ? 'block' : 'hidden'} md:block w-64 min-h-full`}>
      <div className="p-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">{practiceData.name}</h3>
        <nav className="space-y-2">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => {
                setActiveTab(tab.id);
                closeMobileSidebar();
              }}
              className={`w-full text-left px-3 py-2 rounded-md text-sm font-medium ${
                activeTab === tab.id
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>
    </div>
  );
};

export default PracticeSidebar; 