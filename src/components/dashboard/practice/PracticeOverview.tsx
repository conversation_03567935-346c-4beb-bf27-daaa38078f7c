import React from 'react';

interface PracticeOverviewProps {
  practiceData: any;
}

const PracticeOverview: React.FC<PracticeOverviewProps> = ({ practiceData }) => {
  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h2 className="text-2xl font-bold text-gray-900 mb-4">Practice Overview</h2>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-blue-50 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-blue-900">Total Patients</h3>
          <p className="text-2xl font-bold text-blue-600">{practiceData.totalPatients}</p>
        </div>
        <div className="bg-green-50 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-green-900">Monthly Revenue</h3>
          <p className="text-2xl font-bold text-green-600">${practiceData.monthlyRevenue.toLocaleString()}</p>
        </div>
        <div className="bg-purple-50 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-purple-900">Average Rating</h3>
          <p className="text-2xl font-bold text-purple-600">{practiceData.avgRating}/5</p>
        </div>
      </div>
    </div>
  );
};

export default PracticeOverview; 