import React from 'react';

interface AppointmentCalendarProps {
  practiceId: string;
}

const AppointmentCalendar: React.FC<AppointmentCalendarProps> = ({ practiceId }) => {
  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h2 className="text-2xl font-bold text-gray-900 mb-4">Appointment Calendar</h2>
      <p className="text-gray-600">Appointment calendar for practice {practiceId} will be displayed here.</p>
    </div>
  );
};

export default AppointmentCalendar; 