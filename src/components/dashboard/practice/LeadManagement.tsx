import React, { useState } from 'react';
import { motion } from 'framer-motion';

interface LeadManagementProps {
  practiceId: string;
}

// Mock lead data
const mockLeads = [
  {
    id: 'lead-001',
    patientName: '<PERSON>',
    treatmentType: 'Invisalign',
    location: 'San Francisco, CA',
    distance: '2.3 miles',
    insuranceType: 'Delta Dental',
    urgency: 'High',
    createdAt: '2024-05-20T14:30:00',
    averageBid: 1250,
    bidCount: 3,
    status: 'Open',
    expiresAt: '2024-05-22T23:59:59'
  },
  {
    id: 'lead-002',
    patientName: '<PERSON>',
    treatmentType: 'Root Canal',
    location: 'San Francisco, CA',
    distance: '1.7 miles',
    insuranceType: 'Cigna',
    urgency: 'High',
    createdAt: '2024-05-20T10:15:00',
    averageBid: 980,
    bidCount: 4,
    status: 'Open',
    expiresAt: '2024-05-22T23:59:59'
  },
  {
    id: 'lead-003',
    patientName: '<PERSON>',
    treatmentType: 'Dental Implants',
    location: 'San Francisco, CA',
    distance: '3.5 miles',
    insuranceType: 'Aetna',
    urgency: 'Medium',
    createdAt: '2024-05-19T16:45:00',
    averageBid: 2400,
    bidCount: 5,
    status: 'Open',
    expiresAt: '2024-05-21T23:59:59'
  },
  {
    id: 'lead-004',
    patientName: 'Sophia Martinez',
    treatmentType: 'Full Mouth Restoration',
    location: 'San Francisco, CA',
    distance: '4.1 miles',
    insuranceType: 'MetLife',
    urgency: 'Medium',
    createdAt: '2024-05-19T09:30:00',
    averageBid: 8500,
    bidCount: 2,
    status: 'Open',
    expiresAt: '2024-05-21T23:59:59'
  },
  {
    id: 'lead-005',
    patientName: 'William Brown',
    treatmentType: 'Teeth Whitening',
    location: 'San Francisco, CA',
    distance: '1.2 miles',
    insuranceType: 'BlueCross',
    urgency: 'Low',
    createdAt: '2024-05-18T13:20:00',
    averageBid: 350,
    bidCount: 6,
    status: 'Claimed',
    expiresAt: '2024-05-20T23:59:59'
  }
];

// My active bids
const mockMyBids = [
  {
    leadId: 'lead-001',
    bidAmount: 1150,
    status: 'Pending',
    bidDate: '2024-05-20T15:45:00',
    notes: 'Same-day appointment available'
  },
  {
    leadId: 'lead-003',
    bidAmount: 2300,
    status: 'Pending',
    bidDate: '2024-05-19T17:30:00',
    notes: 'Free consultation included'
  }
];

const LeadManagement: React.FC<LeadManagementProps> = ({ practiceId }) => {
  const [activeTab, setActiveTab] = useState('marketplace');
  const [searchQuery, setSearchQuery] = useState('');
  const [treatmentFilter, setTreatmentFilter] = useState('all');
  const [urgencyFilter, setUrgencyFilter] = useState('all');
  const [selectedLead, setSelectedLead] = useState<string | null>(null);
  const [isBidModalOpen, setIsBidModalOpen] = useState(false);
  const [activeBidLead, setActiveBidLead] = useState<string | null>(null);
  const [bidAmount, setBidAmount] = useState('');
  const [bidNotes, setBidNotes] = useState('');

  // Filter leads based on filters and search
  const filteredLeads = mockLeads.filter((lead) => {
    // Filter by status based on active tab
    if (activeTab === 'marketplace' && lead.status !== 'Open') return false;
    if (activeTab === 'my-bids' && !mockMyBids.some(bid => bid.leadId === lead.id)) return false;
    
    // Filter by treatment type
    if (treatmentFilter !== 'all' && lead.treatmentType !== treatmentFilter) return false;
    
    // Filter by urgency
    if (urgencyFilter !== 'all' && lead.urgency !== urgencyFilter) return false;
    
    // Filter by search query
    if (searchQuery && !lead.patientName.toLowerCase().includes(searchQuery.toLowerCase()) && 
        !lead.treatmentType.toLowerCase().includes(searchQuery.toLowerCase()) && 
        !lead.location.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false;
    }
    
    return true;
  });

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Format time remaining
  const formatTimeRemaining = (expiresAt: string) => {
    const now = new Date();
    const expiryDate = new Date(expiresAt);
    const timeRemaining = expiryDate.getTime() - now.getTime();
    
    if (timeRemaining <= 0) {
      return 'Expired';
    }
    
    const hours = Math.floor(timeRemaining / (1000 * 60 * 60));
    const minutes = Math.floor((timeRemaining % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours > 24) {
      const days = Math.floor(hours / 24);
      return `${days} day${days > 1 ? 's' : ''} left`;
    }
    
    return `${hours}h ${minutes}m left`;
  };

  // Handle bid submission
  const handleBidSubmit = () => {
    // In a real app, this would make an API call to submit the bid
    console.log('Submitting bid:', {
      leadId: activeBidLead,
      practiceId,
      amount: parseFloat(bidAmount),
      notes: bidNotes
    });
    
    // Close the modal and reset values
    setIsBidModalOpen(false);
    setActiveBidLead(null);
    setBidAmount('');
    setBidNotes('');
  };

  // Get my bid for a lead
  const getMyBidForLead = (leadId: string) => {
    return mockMyBids.find(bid => bid.leadId === leadId);
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Lead Management</h1>
        
        <div className="flex space-x-2">
          <div className="relative">
            <label htmlFor="lead-search" className="sr-only">Search leads</label>
            <input
              id="lead-search"
              name="lead-search"
              type="text"
              placeholder="Search leads..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="px-4 py-2 pr-8 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            />
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400 absolute top-2.5 right-2.5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
            </svg>
          </div>
          
          <select
            value={treatmentFilter}
            onChange={(e) => setTreatmentFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
          >
            <option value="all">All Treatments</option>
            <option value="Invisalign">Invisalign</option>
            <option value="Root Canal">Root Canal</option>
            <option value="Dental Implants">Dental Implants</option>
            <option value="Teeth Whitening">Teeth Whitening</option>
            <option value="Full Mouth Restoration">Full Mouth Restoration</option>
          </select>
          
          <select
            value={urgencyFilter}
            onChange={(e) => setUrgencyFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
          >
            <option value="all">All Urgency</option>
            <option value="High">High Urgency</option>
            <option value="Medium">Medium Urgency</option>
            <option value="Low">Low Urgency</option>
          </select>
        </div>
      </div>

      {/* Tab navigation */}
      <div className="flex border-b border-gray-200 mb-6">
        <button
          onClick={() => setActiveTab('marketplace')}
          className={`px-4 py-2 text-sm font-medium ${
            activeTab === 'marketplace'
              ? 'text-indigo-600 border-b-2 border-indigo-600'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          Lead Marketplace
        </button>
        <button
          onClick={() => setActiveTab('my-bids')}
          className={`px-4 py-2 text-sm font-medium ${
            activeTab === 'my-bids'
              ? 'text-indigo-600 border-b-2 border-indigo-600'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          My Active Bids ({mockMyBids.length})
        </button>
      </div>

      {/* Leads list */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        {filteredLeads.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
            <p className="mb-1">No leads found with the current filters</p>
            <button 
              onClick={() => {
                setSearchQuery('');
                setTreatmentFilter('all');
                setUrgencyFilter('all');
              }}
              className="mt-2 text-indigo-600 hover:text-indigo-800 text-sm font-medium"
            >
              Clear all filters
            </button>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {filteredLeads.map((lead) => (
              <div key={lead.id} className="p-0">
                <div 
                  onClick={() => setSelectedLead(lead.id === selectedLead ? null : lead.id)}
                  className={`px-4 py-4 cursor-pointer ${
                    lead.id === selectedLead ? 'bg-indigo-50' : 'hover:bg-gray-50'
                  }`}
                >
                  <div className="flex flex-col md:flex-row justify-between">
                    <div className="mb-3 md:mb-0">
                      <div className="flex items-center">
                        <span className={`inline-block w-2 h-2 rounded-full mr-2 ${
                          lead.urgency === 'High' ? 'bg-red-500' : 
                          lead.urgency === 'Medium' ? 'bg-yellow-500' : 'bg-green-500'
                        }`}></span>
                        <h3 className="text-lg font-bold text-gray-800">{lead.treatmentType}</h3>
                        <span className="ml-2 px-2 py-0.5 text-xs rounded-full bg-indigo-100 text-indigo-800">
                          {lead.urgency} Urgency
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 mt-1">
                        {lead.patientName} • {lead.location} ({lead.distance})
                      </p>
                      <p className="text-sm text-gray-500 mt-1">
                        Insurance: {lead.insuranceType}
                      </p>
                    </div>
                    
                    <div className="flex flex-col items-end">
                      <div className="text-right">
                        <p className="text-sm text-gray-500">Average Bid</p>
                        <p className="text-lg font-bold text-gray-800">${lead.averageBid}</p>
                      </div>
                      <div className="flex items-center mt-2">
                        <div className="text-right mr-4">
                          <p className="text-xs text-gray-500">{formatTimeRemaining(lead.expiresAt)}</p>
                          <p className="text-xs text-gray-500">{lead.bidCount} bids so far</p>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" className={`h-5 w-5 text-gray-400 transform transition-transform ${
                          lead.id === selectedLead ? 'rotate-180' : ''
                        }`} viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Expanded lead details */}
                {lead.id === selectedLead && (
                  <motion.div
                    initial={{ height: 0 }}
                    animate={{ height: 'auto' }}
                    exit={{ height: 0 }}
                    className="bg-indigo-50 px-4 py-4 overflow-hidden"
                  >
                    <div className="border-t border-indigo-100 pt-3">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                        <div>
                          <p className="text-xs text-gray-500 mb-1">Lead Created</p>
                          <p className="text-sm font-medium text-gray-800">{formatDate(lead.createdAt)}</p>
                        </div>
                        <div>
                          <p className="text-xs text-gray-500 mb-1">Expires</p>
                          <p className="text-sm font-medium text-gray-800">{formatDate(lead.expiresAt)}</p>
                        </div>
                        <div>
                          <p className="text-xs text-gray-500 mb-1">Current Status</p>
                          <p className="text-sm font-medium text-gray-800">{lead.status}</p>
                        </div>
                      </div>
                      
                      <div className="mb-4">
                        <p className="text-xs text-gray-500 mb-1">Patient Requirements</p>
                        <p className="text-sm text-gray-700">
                          Patient is looking for a {lead.treatmentType} procedure and is available weekdays 
                          after 5 PM. {lead.urgency === 'High' ? 'They are seeking treatment as soon as possible.' : 
                          lead.urgency === 'Medium' ? 'They would like to schedule within 2 weeks.' : 
                          'They are looking to schedule within the next month.'}
                        </p>
                      </div>
                      
                      {activeTab === 'marketplace' ? (
                        <div>
                          {getMyBidForLead(lead.id) ? (
                            <div className="bg-white p-3 rounded-lg border border-indigo-200 mb-4">
                              <div className="flex justify-between items-center">
                                <div>
                                  <p className="text-sm font-medium text-gray-800">Your Current Bid</p>
                                  <p className="text-lg font-bold text-indigo-600">${getMyBidForLead(lead.id)?.bidAmount}</p>
                                  <p className="text-xs text-gray-500 mt-1">Submitted on {formatDate(getMyBidForLead(lead.id)?.bidDate || '')}</p>
                                </div>
                                <span className="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800 font-medium">
                                  {getMyBidForLead(lead.id)?.status}
                                </span>
                              </div>
                              <p className="text-sm text-gray-600 mt-2">Notes: {getMyBidForLead(lead.id)?.notes}</p>
                            </div>
                          ) : (
                            <button
                              onClick={() => {
                                setActiveBidLead(lead.id);
                                setIsBidModalOpen(true);
                              }}
                              className="w-full bg-indigo-600 hover:bg-indigo-700 text-white text-sm font-medium py-2 px-4 rounded-lg transition-colors"
                            >
                              Place Bid for This Lead
                            </button>
                          )}
                        </div>
                      ) : (
                        <div className="flex space-x-2">
                          <button
                            onClick={() => {
                              setActiveBidLead(lead.id);
                              setBidAmount(getMyBidForLead(lead.id)?.bidAmount.toString() || '');
                              setBidNotes(getMyBidForLead(lead.id)?.notes || '');
                              setIsBidModalOpen(true);
                            }}
                            className="bg-white border border-gray-300 hover:bg-gray-50 text-gray-700 text-sm font-medium py-2 px-4 rounded-lg transition-colors"
                          >
                            Update Bid
                          </button>
                          <button
                            className="bg-red-50 border border-red-200 hover:bg-red-100 text-red-600 text-sm font-medium py-2 px-4 rounded-lg transition-colors"
                          >
                            Withdraw Bid
                          </button>
                        </div>
                      )}
                    </div>
                  </motion.div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Bid Modal */}
      {isBidModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <motion.div 
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className="bg-white rounded-xl shadow-xl max-w-md w-full p-6"
          >
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-bold text-gray-800">
                {getMyBidForLead(activeBidLead || '') ? 'Update Your Bid' : 'Place a Bid'}
              </h3>
              <button 
                onClick={() => setIsBidModalOpen(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <div className="mb-4">
              <p className="text-gray-600 mb-2">
                {activeBidLead && mockLeads.find(l => l.id === activeBidLead)?.treatmentType} for {activeBidLead && mockLeads.find(l => l.id === activeBidLead)?.patientName}
              </p>
              <div className="flex items-center">
                <span className="text-sm text-gray-500">Current average bid: </span>
                <span className="ml-1 text-sm font-medium text-gray-800">
                  ${activeBidLead && mockLeads.find(l => l.id === activeBidLead)?.averageBid}
                </span>
                <span className="mx-2 text-sm text-gray-500">•</span>
                <span className="text-sm text-gray-500">
                  {activeBidLead && mockLeads.find(l => l.id === activeBidLead)?.bidCount} bids so far
                </span>
              </div>
            </div>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="bid-amount">Your Bid Amount ($)</label>
              <input 
                id="bid-amount"
                name="bid-amount"
                type="number" 
                value={bidAmount}
                onChange={(e) => setBidAmount(e.target.value)}
                placeholder="Enter your bid amount"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" 
              />
              {activeBidLead && getMyBidForLead(activeBidLead) && (
                <p className="text-xs text-gray-500 mt-1">
                  Your previous bid: ${getMyBidForLead(activeBidLead)?.bidAmount}
                </p>
              )}
            </div>
            
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="bid-notes">Notes (Optional)</label>
              <textarea 
                id="bid-notes"
                name="bid-notes"
                value={bidNotes}
                onChange={(e) => setBidNotes(e.target.value)}
                placeholder="Add any special offers or notes for the patient"
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" 
              ></textarea>
              <p className="text-xs text-gray-500 mt-1">
                Include information about availability, special offers, or why you're a good fit for this patient.
              </p>
            </div>
            
            <div className="flex space-x-3">
              <button 
                onClick={() => setIsBidModalOpen(false)}
                className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-800 text-sm font-medium py-2 px-4 rounded-lg transition-colors"
              >
                Cancel
              </button>
              <button 
                onClick={handleBidSubmit}
                className="flex-1 bg-indigo-600 hover:bg-indigo-700 text-white text-sm font-medium py-2 px-4 rounded-lg transition-colors"
              >
                {getMyBidForLead(activeBidLead || '') ? 'Update Bid' : 'Submit Bid'}
              </button>
            </div>
          </motion.div>
        </div>
      )}

      {/* Stats summary */}
      <div className="mt-8 grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <p className="text-sm text-gray-500">Total Active Leads</p>
          <p className="text-2xl font-bold text-gray-800">{mockLeads.filter(l => l.status === 'Open').length}</p>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <p className="text-sm text-gray-500">My Active Bids</p>
          <p className="text-2xl font-bold text-gray-800">{mockMyBids.length}</p>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <p className="text-sm text-gray-500">Won This Month</p>
          <p className="text-2xl font-bold text-gray-800">3</p>
        </div>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <p className="text-sm text-gray-500">Average Conversion</p>
          <p className="text-2xl font-bold text-gray-800">32%</p>
        </div>
      </div>
    </div>
  );
};

export default LeadManagement; 