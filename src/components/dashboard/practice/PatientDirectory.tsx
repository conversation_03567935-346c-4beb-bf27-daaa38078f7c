import React from 'react';

interface PatientDirectoryProps {
  practiceId: string;
}

const PatientDirectory: React.FC<PatientDirectoryProps> = ({ practiceId }) => {
  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h2 className="text-2xl font-bold text-gray-900 mb-4">Patient Directory</h2>
      <p className="text-gray-600">Patient directory for practice {practiceId} will be displayed here.</p>
    </div>
  );
};

export default PatientDirectory; 