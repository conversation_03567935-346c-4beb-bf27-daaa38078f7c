import React, { useState } from 'react';
import { motion } from 'framer-motion';
import PracticeHeader from './PracticeHeader';
import PracticeSidebar from './PracticeSidebar';
import PracticeOverview from './PracticeOverview';
import LeadManagement from './LeadManagement';
import PatientDirectory from './PatientDirectory';
import AppointmentCalendar from './AppointmentCalendar';
import FinancialMetrics from './FinancialMetrics';
import PracticeSettings from './PracticeSettings';

// Mock practice data - in a real app, this would come from your backend
const mockPracticeData = {
  id: 'prc_123456',
  name: '<PERSON> Smile Dental',
  ownerName: 'Dr. <PERSON>',
  email: '<EMAIL>',
  profileImage: 'https://randomuser.me/api/portraits/men/42.jpg',
  address: '123 Main Street, Suite 500, San Francisco, CA 94105',
  phone: '(*************',
  website: 'brightsmile.dental',
  specialty: 'General Dentistry',
  founded: '2010',
  totalPatients: 2875,
  activePatients: 1625,
  avgRating: 4.8,
  reviewCount: 342,
  monthlyRevenue: 58750,
  appointmentsToday: 14,
  leadsThisWeek: 23,
  conversionRate: 52
};

const PracticeDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);

  const renderContent = () => {
    switch (activeTab) {
      case 'overview':
        return <PracticeOverview practiceData={mockPracticeData} />;
      case 'leads':
        return <LeadManagement practiceId={mockPracticeData.id} />;
      case 'patients':
        return <PatientDirectory practiceId={mockPracticeData.id} />;
      case 'appointments':
        return <AppointmentCalendar practiceId={mockPracticeData.id} />;
      case 'financials':
        return <FinancialMetrics practiceId={mockPracticeData.id} />;
      case 'settings':
        return <PracticeSettings practiceData={mockPracticeData} />;
      default:
        return <PracticeOverview practiceData={mockPracticeData} />;
    }
  };

  return (
    <div className="bg-gray-50">
      {/* Dashboard header */}
      <div className="sticky top-16 z-10"> {/* Adjusted to account for Navbar height */}
        <PracticeHeader 
          practiceData={mockPracticeData} 
          toggleSidebar={() => setIsMobileSidebarOpen(!isMobileSidebarOpen)} 
        />
      </div>

      <div className="flex min-h-[calc(100vh-10rem)]"> {/* Adjusted height calculation */}
        {/* Sidebar - hidden on mobile unless toggled */}
        <PracticeSidebar 
          activeTab={activeTab}
          setActiveTab={setActiveTab}
          isMobileOpen={isMobileSidebarOpen}
          closeMobileSidebar={() => setIsMobileSidebarOpen(false)}
          practiceData={mockPracticeData}
        />

        {/* Main content area */}
        <main className="flex-1 p-4 md:p-6 overflow-auto">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="mb-6"
          >
            {renderContent()}
          </motion.div>
        </main>
      </div>
    </div>
  );
};

export default PracticeDashboard; 