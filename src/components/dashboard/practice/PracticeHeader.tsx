import React, { useState } from 'react';

interface PracticeHeaderProps {
  practiceData: {
    name: string;
    ownerName: string;
    profileImage: string;
    appointmentsToday: number;
    leadsThisWeek: number;
  };
  toggleSidebar: () => void;
}

const PracticeHeader: React.FC<PracticeHeaderProps> = ({ practiceData, toggleSidebar }) => {
  const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false);
  const [isNotificationsOpen, setIsNotificationsOpen] = useState(false);

  return (
    <header className="bg-white border-b border-gray-200 sticky top-0 z-30">
      <div className="px-4 md:px-6 py-3 flex items-center justify-between">
        <div className="flex items-center">
          {/* Mobile menu button */}
          <button
            onClick={toggleSidebar}
            className="md:hidden mr-3 text-gray-500 hover:text-gray-700"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>

          {/* Logo */}
          <div className="flex items-center">
            <svg className="h-8 w-8 text-indigo-600" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 15h-2v-6h2v6zm4 0h-2v-6h2v6zm-6-8H7V7h2v2zm4 0h-2V7h2v2zm4 0h-2V7h2v2z" />
            </svg>
            <span className="ml-2 text-lg font-bold text-gray-800">Smilo.Dental Partner</span>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="hidden md:flex items-center space-x-6">
          <div className="flex items-center">
            <div className="p-2 bg-blue-50 rounded-lg">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-600" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-2">
              <p className="text-xs text-gray-500">Today</p>
              <p className="text-sm font-semibold text-gray-800">{practiceData.appointmentsToday} Appointments</p>
            </div>
          </div>
          
          <div className="flex items-center">
            <div className="p-2 bg-indigo-50 rounded-lg">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
                <path d="M10.3 5.3l-4 4L5 10l1.3.7 4 4 .7-.7-4-4 4-4-.7-.7z" />
                <path d="M13.3 5.3l4 4 1.3.7-1.3.7-4 4-.7-.7 4-4-4-4 .7-.7z" />
              </svg>
            </div>
            <div className="ml-2">
              <p className="text-xs text-gray-500">This Week</p>
              <p className="text-sm font-semibold text-gray-800">{practiceData.leadsThisWeek} New Leads</p>
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          {/* Notifications */}
          <div className="relative">
            <button
              onClick={() => setIsNotificationsOpen(!isNotificationsOpen)}
              className="relative p-1 text-gray-500 hover:text-gray-700 focus:outline-none"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
              </svg>
              <span className="absolute top-0 right-0 h-2 w-2 rounded-full bg-red-500"></span>
            </button>

            {/* Notifications dropdown */}
            {isNotificationsOpen && (
              <div className="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg py-2 z-40 border border-gray-200">
                <div className="px-4 py-2 border-b border-gray-200">
                  <h3 className="text-sm font-medium text-gray-700">Notifications</h3>
                </div>
                <div className="max-h-60 overflow-y-auto">
                  <div className="px-4 py-3 hover:bg-gray-50">
                    <p className="text-sm font-medium text-gray-800">New Lead: Invisalign Treatment</p>
                    <p className="text-xs text-gray-500 mt-1">
                      A new high-value lead for Invisalign treatment is available in your area.
                    </p>
                    <p className="text-xs text-gray-400 mt-1">15 minutes ago</p>
                  </div>
                  
                  <div className="px-4 py-3 hover:bg-gray-50">
                    <p className="text-sm font-medium text-gray-800">Appointment Reminder</p>
                    <p className="text-xs text-gray-500 mt-1">
                      You have 3 appointments scheduled for tomorrow.
                    </p>
                    <p className="text-xs text-gray-400 mt-1">1 hour ago</p>
                  </div>
                  
                  <div className="px-4 py-3 hover:bg-gray-50">
                    <p className="text-sm font-medium text-gray-800">Insurance Verification Required</p>
                    <p className="text-xs text-gray-500 mt-1">
                      Please verify insurance details for patient Emily Johnson before tomorrow's appointment.
                    </p>
                    <p className="text-xs text-gray-400 mt-1">3 hours ago</p>
                  </div>
                </div>
                <div className="px-4 py-2 border-t border-gray-200">
                  <a href="#" className="text-xs text-indigo-600 hover:text-indigo-800 font-medium">
                    View all notifications
                  </a>
                </div>
              </div>
            )}
          </div>

          {/* Profile Dropdown */}
          <div className="relative">
            <button
              onClick={() => setIsProfileMenuOpen(!isProfileMenuOpen)}
              className="flex items-center space-x-2 focus:outline-none"
            >
              <img
                src={practiceData.profileImage}
                alt={practiceData.name}
                className="h-8 w-8 rounded-full object-cover border border-gray-200"
              />
              <span className="hidden md:block text-sm font-medium text-gray-700">
                {practiceData.ownerName}
              </span>
              <svg xmlns="http://www.w3.org/2000/svg" className="hidden md:block h-4 w-4 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>

            {/* Profile menu dropdown */}
            {isProfileMenuOpen && (
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg py-2 z-40 border border-gray-200">
                <div className="px-4 py-2 border-b border-gray-200">
                  <p className="text-xs text-gray-500">Logged in as</p>
                  <p className="text-sm font-medium text-gray-700">{practiceData.name}</p>
                </div>
                <a href="#" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  Practice Profile
                </a>
                <a href="#" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  Account Settings
                </a>
                <a href="#" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  Billing & Subscription
                </a>
                <div className="border-t border-gray-200 my-1"></div>
                <a href="/" className="block px-4 py-2 text-sm text-red-600 hover:bg-gray-100">
                  Sign Out
                </a>
              </div>
            )}
          </div>
        </div>
      </div>
      
      {/* Mobile Stats Bar - only visible on mobile */}
      <div className="md:hidden flex items-center justify-around px-4 py-2 bg-gray-50 border-t border-gray-200">
        <div className="text-center">
          <p className="text-xs text-gray-500">Today</p>
          <p className="text-sm font-semibold text-gray-800">{practiceData.appointmentsToday} Appts</p>
        </div>
        <div className="text-center">
          <p className="text-xs text-gray-500">This Week</p>
          <p className="text-sm font-semibold text-gray-800">{practiceData.leadsThisWeek} Leads</p>
        </div>
      </div>
    </header>
  );
};

export default PracticeHeader; 