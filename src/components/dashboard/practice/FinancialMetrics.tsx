import React from 'react';

interface FinancialMetricsProps {
  practiceId: string;
}

const FinancialMetrics: React.FC<FinancialMetricsProps> = ({ practiceId }) => {
  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h2 className="text-2xl font-bold text-gray-900 mb-4">Financial Metrics</h2>
      <p className="text-gray-600">Financial metrics for practice {practiceId} will be displayed here.</p>
    </div>
  );
};

export default FinancialMetrics; 