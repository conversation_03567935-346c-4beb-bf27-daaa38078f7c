import React, { useState } from 'react';
import { motion } from 'framer-motion';
import PatientHeader from './PatientHeader';
import PatientSidebar from './PatientSidebar';
import PatientOverview from './PatientOverview';
import AppointmentHistory from './AppointmentHistory';
import TreatmentPlans from './TreatmentPlans';
import InsuranceClaims from './InsuranceClaims';
import PatientMessages from './PatientMessages';
import PersonalDocuments from './PersonalDocuments';

// Mock user data - in a real app, this would come from your backend
const mockUserData = {
  id: 'pat_123456',
  name: '<PERSON>',
  email: '<EMAIL>',
  profileImage: 'https://randomuser.me/api/portraits/women/44.jpg',
  dateOfBirth: '1985-06-12',
  phoneNumber: '(*************',
  insuranceProvider: 'Delta Dental',
  insuranceMemberID: '**********',
  primaryDentist: 'Dr. <PERSON>',
  lastCheckup: '2023-11-15',
  upcomingAppointment: '2024-05-22T14:30:00',
  outstandingBalance: 75.50,
  preferredLocation: 'Downtown Clinic'
};

const PatientDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);

  const renderContent = () => {
    switch (activeTab) {
      case 'overview':
        return <PatientOverview userData={mockUserData} />;
      case 'appointments':
        return <AppointmentHistory patientId={mockUserData.id} />;
      case 'treatments':
        return <TreatmentPlans patientId={mockUserData.id} />;
      case 'insurance':
        return <InsuranceClaims patientId={mockUserData.id} />;
      case 'messages':
        return <PatientMessages patientId={mockUserData.id} />;
      case 'documents':
        return <PersonalDocuments patientId={mockUserData.id} />;
      default:
        return <PatientOverview userData={mockUserData} />;
    }
  };

  return (
    <div className="bg-gray-50">
      {/* Dashboard header */}
      <div className="sticky top-16 z-10"> {/* Adjusted to account for Navbar height */}
        <PatientHeader 
          userData={mockUserData} 
          toggleSidebar={() => setIsMobileSidebarOpen(!isMobileSidebarOpen)} 
        />
      </div>

      <div className="flex min-h-[calc(100vh-10rem)]"> {/* Adjusted height calculation */}
        {/* Sidebar - hidden on mobile unless toggled */}
        <PatientSidebar 
          activeTab={activeTab}
          setActiveTab={setActiveTab}
          isMobileOpen={isMobileSidebarOpen}
          closeMobileSidebar={() => setIsMobileSidebarOpen(false)}
          userData={mockUserData}
        />

        {/* Main content area */}
        <main className="flex-1 p-4 md:p-6 overflow-auto">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="mb-6"
          >
            {renderContent()}
          </motion.div>
        </main>
      </div>
    </div>
  );
};

export default PatientDashboard; 