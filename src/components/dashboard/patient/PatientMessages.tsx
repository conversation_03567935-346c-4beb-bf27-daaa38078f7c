import React from 'react';

interface PatientMessagesProps {
  patientId: string;
}

const PatientMessages: React.FC<PatientMessagesProps> = ({ patientId }) => {
  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h2 className="text-2xl font-bold text-gray-900 mb-4">Messages</h2>
      <p className="text-gray-600">Messages for patient {patientId} will be displayed here.</p>
    </div>
  );
};

export default PatientMessages; 