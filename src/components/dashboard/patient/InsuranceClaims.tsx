import React from 'react';

interface InsuranceClaimsProps {
  patientId: string;
}

const InsuranceClaims: React.FC<InsuranceClaimsProps> = ({ patientId }) => {
  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h2 className="text-2xl font-bold text-gray-900 mb-4">Insurance Claims</h2>
      <p className="text-gray-600">Insurance claims for patient {patientId} will be displayed here.</p>
    </div>
  );
};

export default InsuranceClaims; 