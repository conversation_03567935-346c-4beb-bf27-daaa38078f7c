import React from 'react';

interface PersonalDocumentsProps {
  patientId: string;
}

const PersonalDocuments: React.FC<PersonalDocumentsProps> = ({ patientId }) => {
  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h2 className="text-2xl font-bold text-gray-900 mb-4">Personal Documents</h2>
      <p className="text-gray-600">Personal documents for patient {patientId} will be displayed here.</p>
    </div>
  );
};

export default PersonalDocuments; 