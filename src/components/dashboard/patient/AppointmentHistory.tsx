import React, { useState } from 'react';
import { motion } from 'framer-motion';

interface AppointmentHistoryProps {
  patientId: string;
}

// Mock appointment data
const mockAppointments = [
  {
    id: 'apt-001',
    date: '2024-05-22T14:30:00',
    type: 'Regular Checkup',
    dentist: 'Dr. <PERSON>',
    location: 'Downtown Clinic',
    status: 'Upcoming',
    notes: 'Bi-annual checkup and cleaning'
  },
  {
    id: 'apt-002',
    date: '2024-02-15T10:00:00',
    type: 'Filling',
    dentist: 'Dr. <PERSON>',
    location: 'Downtown Clinic',
    status: 'Completed',
    notes: 'Cavity filling on lower right molar'
  },
  {
    id: 'apt-003',
    date: '2023-11-15T09:15:00',
    type: 'Regular Checkup',
    dentist: 'Dr. <PERSON>',
    location: 'Downtown Clinic',
    status: 'Completed',
    notes: 'Regular checkup with X-rays'
  },
  {
    id: 'apt-004',
    date: '2023-06-03T13:45:00',
    type: 'Wisdom Tooth Consultation',
    dentist: 'Dr. <PERSON>',
    location: 'Surgical Center',
    status: 'Completed',
    notes: 'Consultation for wisdom teeth removal'
  },
  {
    id: 'apt-005',
    date: '2023-05-17T11:30:00',
    type: 'Regular Checkup',
    dentist: 'Dr. <PERSON> <PERSON>',
    location: 'Downtown Clinic',
    status: 'Completed',
    notes: 'Regular checkup and cleaning'
  }
];

const AppointmentHistory: React.FC<AppointmentHistoryProps> = ({ patientId }) => {
  const [activeTab, setActiveTab] = useState('upcoming');
  const [selectedAppointment, setSelectedAppointment] = useState<string | null>(null);
  const [isRescheduleModalOpen, setIsRescheduleModalOpen] = useState(false);

  // Filter appointments based on active tab
  const filteredAppointments = mockAppointments.filter(appointment => {
    if (activeTab === 'upcoming') {
      return appointment.status === 'Upcoming';
    } else if (activeTab === 'past') {
      return appointment.status === 'Completed';
    }
    return true;
  });

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Format time for display
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit'
    });
  };

  // Get appointment details by ID
  const getAppointmentById = (id: string) => {
    return mockAppointments.find(appointment => appointment.id === id);
  };

  // Handle appointment selection
  const handleAppointmentSelect = (id: string) => {
    setSelectedAppointment(id === selectedAppointment ? null : id);
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">My Appointments</h1>
        <button className="bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-2 px-4 rounded-lg transition-colors">
          Schedule New Appointment
        </button>
      </div>

      {/* Tab navigation */}
      <div className="flex border-b border-gray-200 mb-6">
        <button
          onClick={() => setActiveTab('upcoming')}
          className={`px-4 py-2 text-sm font-medium ${
            activeTab === 'upcoming'
              ? 'text-blue-600 border-b-2 border-blue-600'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          Upcoming Appointments
        </button>
        <button
          onClick={() => setActiveTab('past')}
          className={`px-4 py-2 text-sm font-medium ${
            activeTab === 'past'
              ? 'text-blue-600 border-b-2 border-blue-600'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          Past Appointments
        </button>
        <button
          onClick={() => setActiveTab('all')}
          className={`px-4 py-2 text-sm font-medium ${
            activeTab === 'all'
              ? 'text-blue-600 border-b-2 border-blue-600'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          All Appointments
        </button>
      </div>

      {/* Appointments list */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        {filteredAppointments.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <p className="mb-1">No {activeTab} appointments found</p>
            {activeTab === 'upcoming' && (
              <button className="mt-2 text-blue-600 hover:text-blue-800 text-sm font-medium">
                Schedule a new appointment
              </button>
            )}
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {filteredAppointments.map((appointment) => (
              <div key={appointment.id} className="p-0">
                <div 
                  onClick={() => handleAppointmentSelect(appointment.id)}
                  className={`flex flex-col md:flex-row md:items-center justify-between p-4 cursor-pointer ${
                    appointment.id === selectedAppointment ? 'bg-blue-50' : 'hover:bg-gray-50'
                  }`}
                >
                  <div className="flex items-start md:items-center mb-3 md:mb-0">
                    <div className={`
                      flex-shrink-0 w-3 h-3 rounded-full mt-1.5 md:mt-0 mr-3
                      ${appointment.status === 'Upcoming' ? 'bg-blue-500' : 'bg-green-500'}
                    `}></div>
                    <div>
                      <p className="font-medium text-gray-800">{appointment.type}</p>
                      <p className="text-sm text-gray-500">
                        {formatDate(appointment.date)} at {formatTime(appointment.date)}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center ml-6 md:ml-0">
                    <div className="text-right md:text-left mr-4">
                      <p className="text-sm font-medium text-gray-700">{appointment.dentist}</p>
                      <p className="text-xs text-gray-500">{appointment.location}</p>
                    </div>
                    <svg xmlns="http://www.w3.org/2000/svg" className={`h-5 w-5 text-gray-400 transform transition-transform ${
                      appointment.id === selectedAppointment ? 'rotate-180' : ''
                    }`} viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>

                {/* Expanded appointment details */}
                {appointment.id === selectedAppointment && (
                  <motion.div
                    initial={{ height: 0 }}
                    animate={{ height: 'auto' }}
                    exit={{ height: 0 }}
                    className="bg-blue-50 px-4 py-4 overflow-hidden"
                  >
                    <div className="border-t border-blue-100 pt-3">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                          <p className="text-xs text-gray-500 mb-1">Appointment Type</p>
                          <p className="text-sm font-medium text-gray-800">{appointment.type}</p>
                        </div>
                        <div>
                          <p className="text-xs text-gray-500 mb-1">Dentist</p>
                          <p className="text-sm font-medium text-gray-800">{appointment.dentist}</p>
                        </div>
                        <div>
                          <p className="text-xs text-gray-500 mb-1">Date & Time</p>
                          <p className="text-sm font-medium text-gray-800">
                            {formatDate(appointment.date)} at {formatTime(appointment.date)}
                          </p>
                        </div>
                        <div>
                          <p className="text-xs text-gray-500 mb-1">Location</p>
                          <p className="text-sm font-medium text-gray-800">{appointment.location}</p>
                        </div>
                      </div>
                      <div className="mb-4">
                        <p className="text-xs text-gray-500 mb-1">Notes</p>
                        <p className="text-sm text-gray-700">{appointment.notes}</p>
                      </div>
                      {appointment.status === 'Upcoming' && (
                        <div className="flex flex-wrap space-x-2">
                          <button
                            onClick={() => setIsRescheduleModalOpen(true)}
                            className="bg-white border border-gray-300 hover:bg-gray-50 text-gray-700 text-sm font-medium py-1.5 px-3 rounded-lg transition-colors"
                          >
                            Reschedule
                          </button>
                          <button className="bg-white border border-gray-300 hover:bg-gray-50 text-gray-700 text-sm font-medium py-1.5 px-3 rounded-lg transition-colors">
                            Add to Calendar
                          </button>
                          <button className="bg-red-50 border border-red-200 hover:bg-red-100 text-red-600 text-sm font-medium py-1.5 px-3 rounded-lg transition-colors">
                            Cancel Appointment
                          </button>
                        </div>
                      )}
                      {appointment.status === 'Completed' && (
                        <div className="flex flex-wrap space-x-2">
                          <button className="bg-white border border-gray-300 hover:bg-gray-50 text-gray-700 text-sm font-medium py-1.5 px-3 rounded-lg transition-colors">
                            View Treatment Notes
                          </button>
                          <button className="bg-white border border-gray-300 hover:bg-gray-50 text-gray-700 text-sm font-medium py-1.5 px-3 rounded-lg transition-colors">
                            Download Receipt
                          </button>
                        </div>
                      )}
                    </div>
                  </motion.div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Reschedule Modal */}
      {isRescheduleModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <motion.div 
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className="bg-white rounded-xl shadow-xl max-w-md w-full p-6"
          >
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-bold text-gray-800">Reschedule Appointment</h3>
              <button 
                onClick={() => setIsRescheduleModalOpen(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <p className="text-gray-600 mb-4">
              Please select a new date and time for your {
                selectedAppointment ? getAppointmentById(selectedAppointment)?.type : ''
              } appointment.
            </p>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="reschedule-date">New Date</label>
              <input 
                id="reschedule-date"
                name="reschedule-date"
                type="date" 
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
              />
            </div>
            
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="reschedule-time">New Time</label>
              <select 
                id="reschedule-time"
                name="reschedule-time"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Select a time</option>
                <option value="08:00">8:00 AM</option>
                <option value="09:00">9:00 AM</option>
                <option value="10:00">10:00 AM</option>
                <option value="11:00">11:00 AM</option>
                <option value="13:00">1:00 PM</option>
                <option value="14:00">2:00 PM</option>
                <option value="15:00">3:00 PM</option>
                <option value="16:00">4:00 PM</option>
              </select>
            </div>
            
            <div className="flex space-x-3">
              <button 
                onClick={() => setIsRescheduleModalOpen(false)}
                className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-800 text-sm font-medium py-2 px-4 rounded-lg transition-colors"
              >
                Cancel
              </button>
              <button 
                onClick={() => setIsRescheduleModalOpen(false)}
                className="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-2 px-4 rounded-lg transition-colors"
              >
                Confirm Reschedule
              </button>
            </div>
          </motion.div>
        </div>
      )}

      {/* Print and Export Options */}
      <div className="mt-6 flex justify-end">
        <button className="mr-3 flex items-center text-sm text-gray-600 hover:text-gray-800">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
          </svg>
          Print Appointments
        </button>
        <button className="flex items-center text-sm text-gray-600 hover:text-gray-800">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
          </svg>
          Export to Calendar
        </button>
      </div>
    </div>
  );
};

export default AppointmentHistory; 