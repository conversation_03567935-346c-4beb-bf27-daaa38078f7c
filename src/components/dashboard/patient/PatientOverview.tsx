import React from 'react';
import { motion } from 'framer-motion';

interface PatientOverviewProps {
  userData: {
    name: string;
    dateOfBirth: string;
    insuranceProvider: string;
    insuranceMemberID: string;
    primaryDentist: string;
    lastCheckup: string;
    upcomingAppointment?: string;
    outstandingBalance: number;
    preferredLocation: string;
  };
}

const PatientOverview: React.FC<PatientOverviewProps> = ({ userData }) => {
  const hasUpcomingAppointment = !!userData.upcomingAppointment;
  const appointmentDate = hasUpcomingAppointment && userData.upcomingAppointment
    ? new Date(userData.upcomingAppointment) 
    : null;
  
  // Calculate days since last checkup
  const lastCheckupDate = new Date(userData.lastCheckup);
  const today = new Date();
  const daysSinceCheckup = Math.floor((today.getTime() - lastCheckupDate.getTime()) / (1000 * 60 * 60 * 24));
  
  // Format date of birth
  const dob = new Date(userData.dateOfBirth);
  const formattedDOB = dob.toLocaleDateString('en-US', {
    month: 'long',
    day: 'numeric',
    year: 'numeric',
  });

  return (
    <div>
      <h1 className="text-2xl font-bold text-gray-800 mb-6">Dashboard Overview</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {/* Upcoming Appointment Card */}
        <motion.div 
          whileHover={{ y: -5 }}
          className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden"
        >
          <div className="bg-blue-50 px-4 py-3 border-b border-blue-100">
            <h2 className="font-semibold text-blue-800">Upcoming Appointment</h2>
          </div>
          <div className="p-4">
            {hasUpcomingAppointment ? (
              <div>
                <div className="flex items-center mb-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                  </svg>
                  <div className="ml-3">
                    <p className="text-lg font-bold text-gray-800">
                      {appointmentDate?.toLocaleDateString('en-US', { 
                        month: 'long', 
                        day: 'numeric' 
                      })}
                    </p>
                    <p className="text-gray-500">
                      {appointmentDate?.toLocaleTimeString('en-US', { 
                        hour: 'numeric', 
                        minute: '2-digit' 
                      })}
                    </p>
                  </div>
                </div>
                <div className="text-sm text-gray-600">
                  <p className="mb-1">Location: {userData.preferredLocation}</p>
                  <p>Dentist: {userData.primaryDentist}</p>
                </div>
                <button className="mt-3 w-full bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-2 px-3 rounded-lg transition-colors">
                  Manage Appointment
                </button>
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-500 mb-3">No upcoming appointments</p>
                <button className="bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-2 px-4 rounded-lg transition-colors">
                  Schedule Checkup
                </button>
              </div>
            )}
          </div>
        </motion.div>

        {/* Last Checkup Card */}
        <motion.div 
          whileHover={{ y: -5 }}
          className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden"
        >
          <div className="bg-green-50 px-4 py-3 border-b border-green-100">
            <h2 className="font-semibold text-green-800">Dental Health</h2>
          </div>
          <div className="p-4">
            <div className="mb-3">
              <p className="text-sm text-gray-500">Last checkup</p>
              <div className="flex items-center">
                <p className="text-lg font-bold text-gray-800">
                  {new Date(userData.lastCheckup).toLocaleDateString('en-US', {
                    month: 'long',
                    day: 'numeric',
                    year: 'numeric',
                  })}
                </p>
                <span className="ml-2 text-xs text-gray-500">
                  ({daysSinceCheckup} days ago)
                </span>
              </div>
            </div>
            
            <div className="mb-3">
              <p className="text-sm text-gray-500">Primary dentist</p>
              <p className="font-medium text-gray-800">{userData.primaryDentist}</p>
            </div>
            
            <div className="mb-4">
              <p className="text-sm text-gray-500 mb-1">Recommended next visit</p>
              <div className={`text-sm font-medium ${daysSinceCheckup > 180 ? 'text-red-600' : 'text-green-600'}`}>
                {daysSinceCheckup > 180 ? 'Overdue for checkup' : 'In good standing'}
              </div>
            </div>
            
            <button className="w-full bg-green-600 hover:bg-green-700 text-white text-sm font-medium py-2 px-3 rounded-lg transition-colors">
              View Dental Records
            </button>
          </div>
        </motion.div>

        {/* Insurance Card */}
        <motion.div 
          whileHover={{ y: -5 }}
          className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden"
        >
          <div className="bg-purple-50 px-4 py-3 border-b border-purple-100">
            <h2 className="font-semibold text-purple-800">Insurance Details</h2>
          </div>
          <div className="p-4">
            <div className="mb-3">
              <p className="text-sm text-gray-500">Provider</p>
              <p className="text-lg font-bold text-gray-800">{userData.insuranceProvider}</p>
            </div>
            
            <div className="mb-3">
              <p className="text-sm text-gray-500">Member ID</p>
              <p className="font-medium text-gray-800">{userData.insuranceMemberID}</p>
            </div>
            
            <div className="mb-4">
              <p className="text-sm text-gray-500">Outstanding balance</p>
              <p className={`text-lg font-bold ${userData.outstandingBalance > 0 ? 'text-red-600' : 'text-green-600'}`}>
                ${userData.outstandingBalance.toFixed(2)}
              </p>
            </div>
            
            <div className="flex space-x-2">
              <button className="flex-1 bg-purple-600 hover:bg-purple-700 text-white text-sm font-medium py-2 px-3 rounded-lg transition-colors">
                View Coverage
              </button>
              {userData.outstandingBalance > 0 && (
                <button className="flex-1 bg-white border border-gray-300 hover:bg-gray-50 text-gray-700 text-sm font-medium py-2 px-3 rounded-lg transition-colors">
                  Make Payment
                </button>
              )}
            </div>
          </div>
        </motion.div>
      </div>

      {/* Personal Information */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
        <h2 className="text-xl font-bold text-gray-800 mb-4">Personal Information</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-sm font-medium text-gray-500 mb-1">Date of Birth</h3>
            <p className="text-gray-800">{formattedDOB}</p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-500 mb-1">Preferred Location</h3>
            <p className="text-gray-800">{userData.preferredLocation}</p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-500 mb-1">Primary Dentist</h3>
            <p className="text-gray-800">{userData.primaryDentist}</p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-500 mb-1">Insurance</h3>
            <p className="text-gray-800">{userData.insuranceProvider}</p>
          </div>
        </div>
        <button className="mt-6 bg-white border border-gray-300 hover:bg-gray-50 text-gray-700 text-sm font-medium py-2 px-4 rounded-lg transition-colors">
          Edit Personal Information
        </button>
      </div>

      {/* Recent Activity */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h2 className="text-xl font-bold text-gray-800 mb-4">Recent Activity</h2>
        <div className="space-y-4">
          <div className="flex items-start">
            <div className="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-800">Appointment Confirmed</p>
              <p className="text-xs text-gray-500">Your appointment on May 22, 2024 has been confirmed.</p>
              <p className="text-xs text-gray-400 mt-1">3 days ago</p>
            </div>
          </div>
          
          <div className="flex items-start">
            <div className="flex-shrink-0 h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-800">Insurance Verification Completed</p>
              <p className="text-xs text-gray-500">Your insurance coverage has been verified for your upcoming treatment.</p>
              <p className="text-xs text-gray-400 mt-1">1 week ago</p>
            </div>
          </div>
          
          <div className="flex items-start">
            <div className="flex-shrink-0 h-10 w-10 rounded-full bg-purple-100 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-800">Message from Dr. Chen</p>
              <p className="text-xs text-gray-500">Dr. Chen sent you a message regarding your recent X-rays.</p>
              <p className="text-xs text-gray-400 mt-1">2 weeks ago</p>
            </div>
          </div>
        </div>
        <button className="mt-6 text-sm text-blue-600 hover:text-blue-800 font-medium">
          View All Activity →
        </button>
      </div>
    </div>
  );
};

export default PatientOverview; 