import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FiX, FiCheck } from 'react-icons/fi';
import { supabase } from '../../lib/supabase';

const WaitlistSignup = ({ selectedPlan, isOpen, onClose }) => {
  const [email, setEmail] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [practiceName, setPracticeName] = useState('');
  const [practiceSize, setPracticeSize] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState(null);

  // Reset form when selected<PERSON><PERSON> changes
  useEffect(() => {
    if (selectedPlan) {
      setError(null);
      setSuccess(false);
    }
  }, [selectedPlan, isOpen]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!email || !selectedPlan) {
      setError("Email and plan selection are required");
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      
      // Insert into Supabase waitlist table
      const { error: insertError } = await supabase
        .from('waitlist_entries')
        .insert([
          { 
            email,
            first_name: firstName,
            last_name: lastName,
            selected_plan: selectedPlan.toLowerCase(),
            practice_name: practiceName,
            practice_size: practiceSize,
            phone_number: phoneNumber,
            message
          }
        ]);
      
      if (insertError) throw insertError;
      
      setSuccess(true);
      
      // Reset form
      setEmail('');
      setFirstName('');
      setLastName('');
      setPracticeName('');
      setPracticeSize('');
      setPhoneNumber('');
      setMessage('');
      
    } catch (err) {
      console.error("Error joining waitlist:", err);
      if (err.code === '23505') {
        setError("This email is already on our waitlist");
      } else {
        setError(err.message || "Failed to join waitlist. Please try again.");
      }
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/80 backdrop-blur-sm"
        onClick={onClose}
      >
        <motion.div
          initial={{ scale: 0.95, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.95, opacity: 0 }}
          className="bg-gray-900 w-full max-w-lg rounded-xl overflow-hidden border border-gray-700 shadow-2xl"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="bg-gradient-to-r from-indigo-800/30 to-purple-800/30 p-4 flex justify-between items-center">
            <h3 className="text-xl font-bold text-white">
              Join Waitlist - {selectedPlan} Plan
            </h3>
            <button 
              onClick={onClose}
              className="text-gray-400 hover:text-white transition-colors"
            >
              <FiX className="w-6 h-6" />
            </button>
          </div>
          
          <div className="p-6">
            {success ? (
              <div className="text-center py-8">
                <div className="mx-auto w-16 h-16 bg-green-500/10 rounded-full flex items-center justify-center mb-4">
                  <FiCheck className="w-8 h-8 text-green-500" />
                </div>
                <h4 className="text-xl font-semibold text-white mb-2">You're on the list!</h4>
                <p className="text-gray-300 mb-6">
                  Thank you for your interest in Smilo Assist. We'll contact you when we're ready to onboard new customers.
                </p>
                <button
                  onClick={onClose}
                  className="px-6 py-2 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-md hover:from-indigo-500 hover:to-purple-500 transition-all duration-300"
                >
                  Close
                </button>
              </div>
            ) : (
              <form onSubmit={handleSubmit} className="space-y-4">
                {error && (
                  <div className="p-3 bg-red-500/10 border border-red-500/20 rounded-md text-red-400 text-sm">
                    {error}
                  </div>
                )}
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-gray-300 text-sm font-medium mb-1">
                      First Name
                    </label>
                    <input
                      type="text"
                      value={firstName}
                      onChange={(e) => setFirstName(e.target.value)}
                      className="w-full bg-gray-800/60 border border-gray-700 rounded-md px-3 py-2 text-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                    />
                  </div>
                  <div>
                    <label className="block text-gray-300 text-sm font-medium mb-1">
                      Last Name
                    </label>
                    <input
                      type="text"
                      value={lastName}
                      onChange={(e) => setLastName(e.target.value)}
                      className="w-full bg-gray-800/60 border border-gray-700 rounded-md px-3 py-2 text-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                    />
                  </div>
                </div>
                
                <div>
                  <label className="block text-gray-300 text-sm font-medium mb-1">
                    Email Address <span className="text-red-400">*</span>
                  </label>
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="w-full bg-gray-800/60 border border-gray-700 rounded-md px-3 py-2 text-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                    required
                  />
                </div>
                
                <div>
                  <label className="block text-gray-300 text-sm font-medium mb-1">
                    Practice Name
                  </label>
                  <input
                    type="text"
                    value={practiceName}
                    onChange={(e) => setPracticeName(e.target.value)}
                    className="w-full bg-gray-800/60 border border-gray-700 rounded-md px-3 py-2 text-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                  />
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-gray-300 text-sm font-medium mb-1">
                      Practice Size
                    </label>
                    <select
                      value={practiceSize}
                      onChange={(e) => setPracticeSize(e.target.value)}
                      className="w-full bg-gray-800/60 border border-gray-700 rounded-md px-3 py-2 text-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                    >
                      <option value="">Select size</option>
                      <option value="Solo practitioner">Solo practitioner</option>
                      <option value="2-5 providers">2-5 providers</option>
                      <option value="6+ providers">6+ providers</option>
                      <option value="DSO/Group">DSO/Group</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-gray-300 text-sm font-medium mb-1">
                      Phone Number
                    </label>
                    <input
                      type="tel"
                      value={phoneNumber}
                      onChange={(e) => setPhoneNumber(e.target.value)}
                      className="w-full bg-gray-800/60 border border-gray-700 rounded-md px-3 py-2 text-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                    />
                  </div>
                </div>
                
                <div>
                  <label className="block text-gray-300 text-sm font-medium mb-1">
                    Message (Optional)
                  </label>
                  <textarea
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    rows={3}
                    className="w-full bg-gray-800/60 border border-gray-700 rounded-md px-3 py-2 text-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                    placeholder="Let us know if you have any specific questions or requirements..."
                  ></textarea>
                </div>
                
                <div className="flex justify-end pt-4">
                  <button
                    type="button"
                    onClick={onClose}
                    className="px-4 py-2 border border-gray-600 text-gray-300 rounded-md hover:bg-gray-800 mr-2"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={loading}
                    className="px-6 py-2 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-md hover:from-indigo-500 hover:to-purple-500 transition-all duration-300 disabled:opacity-50"
                  >
                    {loading ? 'Submitting...' : 'Join Waitlist'}
                  </button>
                </div>
              </form>
            )}
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default WaitlistSignup; 