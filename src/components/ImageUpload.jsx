import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { motion, AnimatePresence } from 'framer-motion';

export default function ImageUpload({ onImageUpload, className = '' }) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState(null);

  const onDrop = useCallback(async (acceptedFiles, rejectedFiles) => {
    setError(null);
    
    if (rejectedFiles.length > 0) {
      const rejection = rejectedFiles[0];
      if (rejection.errors[0].code === 'file-invalid-type') {
        setError('Only JPEG, PNG, and JPG images are supported');
      } else if (rejection.errors[0].code === 'file-too-large') {
        setError('Image size must be less than 5MB');
      } else {
        setError('Invalid file. Please try again');
      }
      return;
    }

    const file = acceptedFiles[0];
    if (!file) return;

    try {
      setIsProcessing(true);
      
      // Validate the file
      if (!file.type.startsWith('image/')) {
        setError('Please select an image file');
        setIsProcessing(false);
        return;
      }
      
      // Pass the raw File object directly to onImageUpload
      onImageUpload(file);
      setError(null);
    } catch (error) {
      console.error('Error processing image:', error);
      setError('Error processing image. Please try again');
    } finally {
      setIsProcessing(false);
    }
  }, [onImageUpload]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/jpeg': [],
      'image/png': [],
      'image/jpg': []
    },
    maxFiles: 1,
    maxSize: 5 * 1024 * 1024 // 5MB
  });

  return (
    <div className={className}>
      <div
        {...getRootProps()}
        className={`
          border-2 border-dashed rounded-lg p-6 text-center cursor-pointer
          transition-colors duration-200
          ${isDragActive
            ? 'border-blue-500 bg-blue-500/10'
            : 'border-white/20 hover:border-blue-500/50 hover:bg-white/5'
          }
          ${isProcessing ? 'opacity-50 pointer-events-none' : ''}
        `}
      >
        <input {...getInputProps()} />
        <div className="space-y-2">
          {isDragActive ? (
            <p className="text-blue-400">Drop the image here...</p>
          ) : isProcessing ? (
            <div className="flex flex-col items-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mb-2"></div>
              <p className="text-white/70">Processing image...</p>
            </div>
          ) : (
            <>
              <div className="flex justify-center mb-2">
                <svg className="w-8 h-8 text-white/60" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <p className="text-white/80">
                Drag & drop a dental image here, or click to select
              </p>
              <p className="text-sm text-white/60">
                Supported formats: JPEG, PNG (max 5MB)
              </p>
            </>
          )}
        </div>
      </div>
      
      {error && (
        <div className="mt-2 text-red-400 text-sm">
          <span className="flex items-center">
            <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            {error}
          </span>
        </div>
      )}
    </div>
  );
}