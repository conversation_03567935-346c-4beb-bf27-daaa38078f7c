import React, { useEffect } from 'react';
import PropTypes from 'prop-types';

/**
 * SeoHead component for managing document head meta tags
 * 
 * This component handles setting document title, meta description,
 * Open Graph tags, Twitter cards, and canonical URLs.
 */
const SeoHead = ({ 
  title, 
  description, 
  canonicalUrl,
  ogImage,
  ogType = 'website',
  twitterCard = 'summary_large_image'
}) => {
  useEffect(() => {
    // Set document title
    const defaultTitle = 'Smilo Dental Assistant';
    document.title = title ? `${title} | ${defaultTitle}` : defaultTitle;
    
    // Set meta description
    let metaDescription = document.querySelector('meta[name="description"]');
    if (!metaDescription) {
      metaDescription = document.createElement('meta');
      metaDescription.name = 'description';
      document.head.appendChild(metaDescription);
    }
    metaDescription.content = description || 'Smilo Dental Assistant - Your personalized guide to oral health';
    
    // Set Open Graph tags
    updateMetaTag('og:title', title || defaultTitle);
    updateMetaTag('og:description', description || 'Smilo Dental Assistant - Your personalized guide to oral health');
    updateMetaTag('og:type', ogType);
    if (ogImage) {
      updateMetaTag('og:image', ogImage);
    }
    
    // Set Twitter card tags
    updateMetaTag('twitter:card', twitterCard);
    updateMetaTag('twitter:title', title || defaultTitle);
    updateMetaTag('twitter:description', description || 'Smilo Dental Assistant - Your personalized guide to oral health');
    if (ogImage) {
      updateMetaTag('twitter:image', ogImage);
    }
    
    // Set canonical URL if provided
    let canonicalTag = document.querySelector('link[rel="canonical"]');
    if (canonicalUrl) {
      if (!canonicalTag) {
        canonicalTag = document.createElement('link');
        canonicalTag.rel = 'canonical';
        document.head.appendChild(canonicalTag);
      }
      canonicalTag.href = canonicalUrl;
    } else if (canonicalTag) {
      // Remove canonical tag if no URL provided
      canonicalTag.remove();
    }
    
    // Cleanup function
    return () => {
      // Reset title on unmount
      document.title = defaultTitle;
      
      // Reset meta description
      if (metaDescription) {
        metaDescription.content = 'Smilo Dental Assistant - Your personalized guide to oral health';
      }
      
      // Remove canonical URL
      if (canonicalTag) {
        canonicalTag.remove();
      }
    };
  }, [title, description, canonicalUrl, ogImage, ogType, twitterCard]);
  
  // Helper function to update or create meta tags
  const updateMetaTag = (name, content) => {
    let metaTag = document.querySelector(`meta[property="${name}"]`);
    if (!metaTag) {
      metaTag = document.createElement('meta');
      metaTag.setAttribute('property', name);
      document.head.appendChild(metaTag);
    }
    metaTag.content = content;
  };
  
  // This component doesn't render anything visible
  return null;
};

SeoHead.propTypes = {
  title: PropTypes.string,
  description: PropTypes.string,
  canonicalUrl: PropTypes.string,
  ogImage: PropTypes.string,
  ogType: PropTypes.string,
  twitterCard: PropTypes.string
};

export default SeoHead;
