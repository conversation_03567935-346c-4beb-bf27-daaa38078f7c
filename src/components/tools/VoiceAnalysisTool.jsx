import React, { useState, useEffect, useRef } from 'react';
import VoiceAnalysisService from '../../lib/services/voiceAnalysisService';

const VoiceAnalysisTool = ({ onAnalysisComplete }) => {
  const [isRecording, setIsRecording] = useState(false);
  const [timeLeft, setTimeLeft] = useState(15);
  const [recordingComplete, setRecordingComplete] = useState(false);
  const [analysisResults, setAnalysisResults] = useState(null);
  const [error, setError] = useState(null);
  const [audioData, setAudioData] = useState(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  
  const mediaRecorderRef = useRef(null);
  const audioChunksRef = useRef([]);
  const timerRef = useRef(null);
  const voiceAnalysisService = useRef(new VoiceAnalysisService());
  
  // Function to start recording
  const startRecording = async () => {
    try {
      audioChunksRef.current = [];
      setError(null);
      setAnalysisResults(null);
      setRecordingComplete(false);
      setAudioData(null);
      
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      mediaRecorderRef.current = new MediaRecorder(stream);
      
      mediaRecorderRef.current.addEventListener('dataavailable', (event) => {
        audioChunksRef.current.push(event.data);
      });
      
      mediaRecorderRef.current.addEventListener('stop', () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/wav' });
        const audioUrl = URL.createObjectURL(audioBlob);
        
        // Convert blob to audio data for analysis
        const reader = new FileReader();
        reader.readAsArrayBuffer(audioBlob);
        reader.onloadend = () => {
          const arrayBuffer = reader.result;
          // Create Float32Array from the buffer for audio processing
          const audioContext = new (window.AudioContext || window.webkitAudioContext)();
          audioContext.decodeAudioData(arrayBuffer).then(audioBuffer => {
            // Get audio data from first channel
            const channelData = audioBuffer.getChannelData(0);
            setAudioData(channelData);
            analyzeAudio(channelData);
          }).catch(err => {
            setError("Could not decode audio data: " + err.message);
            setIsAnalyzing(false);
          });
        };
      });
      
      mediaRecorderRef.current.start();
      setIsRecording(true);
      setTimeLeft(15);
      
      // Start the countdown timer
      timerRef.current = setInterval(() => {
        setTimeLeft(prevTime => {
          if (prevTime <= 1) {
            stopRecording();
            return 0;
          }
          return prevTime - 1;
        });
      }, 1000);
      
    } catch (error) {
      setError(`Microphone access error: ${error.message}. Please ensure your microphone is connected and you've granted permission.`);
    }
  };
  
  // Function to stop recording
  const stopRecording = () => {
    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
      mediaRecorderRef.current.stop();
      
      // Stop all tracks in the stream
      if (mediaRecorderRef.current.stream) {
        mediaRecorderRef.current.stream.getTracks().forEach(track => track.stop());
      }
      
      clearInterval(timerRef.current);
      setIsRecording(false);
      setRecordingComplete(true);
      setIsAnalyzing(true);
    }
  };
  
  // Function to analyze the recorded audio
  const analyzeAudio = (audioData) => {
    try {
      // Perform the analysis using our voice analysis service
      const clarityAnalysis = voiceAnalysisService.current.analyzeSpeechClarity(audioData);
      const dryMouthAnalysis = voiceAnalysisService.current.analyzeDryMouth(audioData);
      const jawAlignmentAnalysis = voiceAnalysisService.current.analyzeJawAlignment(audioData);
      
      const results = {
        clarity: clarityAnalysis,
        dryMouth: dryMouthAnalysis,
        jawAlignment: jawAlignmentAnalysis,
        timestamp: new Date().toISOString(),
        overallScore: (clarityAnalysis.score + dryMouthAnalysis.score + jawAlignmentAnalysis.score) / 3
      };
      
      setAnalysisResults(results);
      
      // Call the callback if provided
      if (onAnalysisComplete) {
        onAnalysisComplete(results);
      }
      
      setIsAnalyzing(false);
    } catch (error) {
      setError(`Analysis error: ${error.message}`);
      setIsAnalyzing(false);
    }
  };
  
  // Clean up on unmount
  useEffect(() => {
    return () => {
      clearInterval(timerRef.current);
      if (mediaRecorderRef.current && mediaRecorderRef.current.stream) {
        mediaRecorderRef.current.stream.getTracks().forEach(track => track.stop());
      }
    };
  }, []);
  
  // Render analysis results
  const renderAnalysisResults = () => {
    if (!analysisResults) return null;
    
    const getScoreColor = (score) => {
      if (score >= 0.7) return 'text-green-400';
      if (score >= 0.5) return 'text-yellow-400';
      return 'text-red-400';
    };
    
    return (
      <div className="mt-4 p-4 bg-blue-900/30 rounded-lg">
        <h3 className="text-lg font-semibold text-blue-200 mb-3">Voice Analysis Results</h3>
        
        <div className="space-y-4">
          {/* Speech Clarity Section */}
          <div className="p-3 bg-blue-900/20 rounded-lg">
            <div className="flex justify-between items-center mb-2">
              <h4 className="font-medium text-white">Speech Clarity</h4>
              <span className={`text-sm font-mono ${getScoreColor(analysisResults.clarity.score)}`}>
                {Math.round(analysisResults.clarity.score * 100)}%
              </span>
            </div>
            <p className="text-sm text-blue-100/70">{analysisResults.clarity.recommendation}</p>
          </div>
          
          {/* Dry Mouth Section */}
          <div className="p-3 bg-blue-900/20 rounded-lg">
            <div className="flex justify-between items-center mb-2">
              <h4 className="font-medium text-white">Oral Moisture</h4>
              <span className={`text-sm font-mono ${getScoreColor(analysisResults.dryMouth.score)}`}>
                {Math.round(analysisResults.dryMouth.score * 100)}%
              </span>
            </div>
            <p className="text-sm text-blue-100/70">{analysisResults.dryMouth.recommendation}</p>
          </div>
          
          {/* Jaw Alignment Section */}
          <div className="p-3 bg-blue-900/20 rounded-lg">
            <div className="flex justify-between items-center mb-2">
              <h4 className="font-medium text-white">Jaw Alignment</h4>
              <span className={`text-sm font-mono ${getScoreColor(analysisResults.jawAlignment.score)}`}>
                {Math.round(analysisResults.jawAlignment.score * 100)}%
              </span>
            </div>
            <p className="text-sm text-blue-100/70">{analysisResults.jawAlignment.recommendation}</p>
          </div>
          
          {/* Overall Assessment */}
          <div className="mt-4 text-center">
            <p className="text-white/80 text-sm">
              <span className="italic">Note:</span> This analysis is for educational purposes only. 
              For a comprehensive evaluation, please consult a dental professional.
            </p>
          </div>
        </div>
      </div>
    );
  };
  
  return (
    <div className="p-4 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold text-white">
          Dental Voice Analysis
        </h2>
        {recordingComplete && !isAnalyzing && !analysisResults && (
          <button
            className="px-3 py-1 text-sm bg-indigo-500 hover:bg-indigo-600 text-white rounded-md transition-colors"
            onClick={() => analyzeAudio(audioData)}
          >
            Re-analyze
          </button>
        )}
      </div>
      
      <p className="text-blue-100/80 text-sm mb-4">
        Record your voice for 15 seconds to analyze speech patterns that may indicate dental issues. 
        Count from 1 to 20 and say the phrase "She sells seashells by the seashore" during the recording.
      </p>
      
      {error && (
        <div className="mb-4 p-3 bg-red-900/30 border border-red-500/30 rounded-lg text-sm text-red-300">
          {error}
        </div>
      )}
      
      <div className="flex justify-center mb-4">
        {isRecording ? (
          <div className="text-center">
            <div className="inline-block w-24 h-24 rounded-full bg-red-500/20 border-2 border-red-500 animate-pulse relative mb-2">
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-2xl font-semibold text-white">{timeLeft}</span>
              </div>
            </div>
            <div className="flex justify-center space-x-2">
              <button
                className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-md transition-colors"
                onClick={stopRecording}
              >
                Stop Recording
              </button>
            </div>
          </div>
        ) : isAnalyzing ? (
          <div className="text-center">
            <div className="inline-block w-24 h-24 rounded-full bg-blue-500/20 border-2 border-blue-500 relative mb-2">
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
              </div>
            </div>
            <p className="text-blue-300">Analyzing your voice...</p>
          </div>
        ) : (
          <button
            className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg shadow-md transition-colors flex items-center"
            onClick={startRecording}
            disabled={isRecording}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
            </svg>
            Start 15-Second Recording
          </button>
        )}
      </div>
      
      {!isRecording && !isAnalyzing && recordingComplete && !analysisResults && (
        <div className="text-center py-4">
          <div className="animate-pulse flex space-x-2 justify-center">
            <div className="h-2 w-2 bg-blue-400 rounded-full"></div>
            <div className="h-2 w-2 bg-blue-400 rounded-full" style={{ animationDelay: '0.2s' }}></div>
            <div className="h-2 w-2 bg-blue-400 rounded-full" style={{ animationDelay: '0.4s' }}></div>
          </div>
          <p className="text-blue-300 mt-2">Processing your recording...</p>
        </div>
      )}
      
      {renderAnalysisResults()}
      
      {analysisResults && (
        <div className="mt-4 text-center">
          <button
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors"
            onClick={startRecording}
          >
            Record Again
          </button>
        </div>
      )}
    </div>
  );
};

export default VoiceAnalysisTool; 