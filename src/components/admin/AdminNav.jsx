import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { Link, useLocation, NavLink } from 'react-router-dom';
import { 
  ChartBarIcon, 
  DocumentTextIcon, 
  UserIcon, 
  UsersIcon, 
  Cog6ToothIcon, 
  DocumentCheckIcon,
  BuildingOffice2Icon,
  HomeIcon,
  PaintBrushIcon,
  BeakerIcon,
  AcademicCapIcon,
  ClipboardDocumentCheckIcon,
  UserGroupIcon
} from '@heroicons/react/24/outline';
import { useAdminAuth } from '../../lib/hooks/useAdminAuth';
import { supabase } from '../../lib/supabase';
import Modal from '../ui/Modal';

const NAV_ITEMS = [
  {
    id: 'overview',
    label: 'Dashboard',
    icon: <HomeIcon className="w-5 h-5" />,
    path: '/admin'
  },
  {
    id: 'patients',
    label: 'Patient Records',
    icon: <UserGroupIcon className="w-5 h-5" />,
    new: true,
    path: '/admin?section=patients'
  },
  {
    id: 'cases',
    label: 'Dental Cases',
    icon: <ClipboardDocumentCheckIcon className="w-5 h-5" />,
    new: true,
    path: '/admin?section=cases'
  },
  {
    id: 'content',
    label: 'Content',
    icon: <DocumentTextIcon className="w-5 h-5" />,
    path: '/admin?section=content'
  },
  {
    id: 'design',
    label: 'Website Design',
    icon: <PaintBrushIcon className="w-5 h-5" />,
    path: '/admin?section=design'
  },
  {
    id: 'clinical',
    label: 'Clinical Tools',
    icon: <BeakerIcon className="w-5 h-5" />,
    new: true,
    path: '/admin?section=clinical'
  },
  {
    id: 'education',
    label: 'Education',
    icon: <AcademicCapIcon className="w-5 h-5" />,
    new: true,
    path: '/admin?section=education'
  },
  {
    id: 'users',
    label: 'Users',
    icon: <UsersIcon className="w-5 h-5" />,
    path: '/admin?section=users'
  },
  {
    id: 'ndas',
    label: 'NDA Management',
    icon: <DocumentCheckIcon className="w-5 h-5" />,
    path: '/admin?section=nda'
  },
  {
    id: 'partnerships',
    label: 'Partnerships',
    icon: <BuildingOffice2Icon className="w-5 h-5" />,
    highlight: true,
    path: '/admin?section=partnerships'
  },
  {
    id: 'system',
    label: 'System',
    icon: <Cog6ToothIcon className="w-5 h-5" />,
    path: '/admin?section=system'
  },
  {
    id: 'analytics',
    label: 'Analytics',
    icon: <ChartBarIcon className="w-5 h-5" />,
    path: '/admin?section=analytics'
  },
];

export default function AdminNav({ activeSection, onSectionChange, integrated = false }) {
  const location = useLocation();
  const isAdminPage = location.pathname.startsWith('/admin');
  const { adminLogout } = useAdminAuth();
  const [activePatients, setActivePatients] = useState({ today: 12, total: 245, percentage: 65 });
  const [isLoading, setIsLoading] = useState(true);
  const [showLogoutModal, setShowLogoutModal] = useState(false);
  
  // Fetch active patients data
  useEffect(() => {
    const fetchActivePatients = async () => {
      try {
        setIsLoading(true);
        
        // Get today's date range
        const today = new Date();
        const startOfToday = new Date(today.setHours(0, 0, 0, 0)).toISOString();
        const endOfToday = new Date(today.setHours(23, 59, 59, 999)).toISOString();
        
        // Query patients active today
        const { data: activeToday, error: activeError } = await supabase
          .from('dental_patients')
          .select('count')
          .gte('last_activity', startOfToday)
          .lte('last_activity', endOfToday);
        
        // Query total patients
        const { data: totalData, error: totalError } = await supabase
          .from('dental_patients')
          .select('count');
        
        if (!activeError && !totalError) {
          const todayCount = activeToday?.[0]?.count || 0;
          const totalCount = totalData?.[0]?.count || 0;
          const percentage = totalCount > 0 ? Math.round((todayCount / totalCount) * 100) : 0;
          
          setActivePatients({
            today: todayCount,
            total: totalCount,
            percentage
          });
        } else {
          // Fallback to demo data
          setActivePatients({
            today: 12,
            total: 245,
            percentage: 65
          });
        }
      } catch (err) {
        console.error('Error fetching active patients:', err);
        // Fallback to demo data on error
        setActivePatients({
          today: 12,
          total: 245,
          percentage: 65
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchActivePatients();
    
    // Set up a refresh interval (every 5 minutes)
    const refreshInterval = setInterval(fetchActivePatients, 5 * 60 * 1000);
    
    return () => clearInterval(refreshInterval);
  }, []);
  
  // Get the section from URL query params if we're on an admin page
  const getActiveSection = () => {
    if (isAdminPage && !activeSection) {
      const searchParams = new URLSearchParams(location.search);
      return searchParams.get('section') || 'overview';
    }
    return activeSection || 'overview';
  };

  // Handle navigation differently based on context
  const handleNavigation = (item) => {
    if (onSectionChange && typeof onSectionChange === 'function') {
      // If we're in the admin dashboard, use the provided function
      onSectionChange(item.id);
    } else if (integrated && !isAdminPage) {
      // If we're integrated with the main site, navigate to the admin page
      window.location.href = item.path;
    }
  };
  
  const currentSection = getActiveSection();

  const handleLogout = (e) => {
    e.preventDefault();
    setShowLogoutModal(true);
  };
  
  const confirmLogout = () => {
    // Proceed with logout
    adminLogout();
    // Set explicit logout flag
    localStorage.setItem('admin_logged_out', 'true');
    // Clear any admin data from window object
    if (window.smiloAdmin) {
      window.smiloAdmin.isLoggedIn = false;
    }
    console.log("Admin logout confirmed and executed");
    setShowLogoutModal(false);
  };
  
  const cancelLogout = () => {
    console.log("Admin logout cancelled");
    setShowLogoutModal(false);
  };

  // Ensure reconnection to Supabase when navigation changes
  // This helps maintain active connections to the database
  useEffect(() => {
    const reconnectToSupabase = async () => {
      try {
        // Test connection to Supabase
        const { data, error } = await supabase
          .from('user_profiles')
          .select('count')
          .limit(1);
          
        if (error) {
          console.warn('Reconnecting to Supabase...');
          // Force refresh the client
          await supabase.auth.refreshSession();
        }
      } catch (err) {
        console.error('Error reconnecting to Supabase:', err);
      }
    };
    
    reconnectToSupabase();
  }, [location.pathname]);

  return (
    <div className={`${integrated ? 'w-full' : 'w-64'} border-r border-gray-700/40 bg-gradient-to-b from-gray-800 to-gray-900 flex flex-col`}>
      <div className="p-4 border-b border-gray-700/40">
        <div className="flex items-center space-x-2 mb-4">
          <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
            <svg className="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          </div>
          <h1 className="text-lg font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-400">
            Smilo Admin
          </h1>
        </div>
      </div>
      
      <div className="p-4 flex-1 overflow-y-auto">
        <h2 className="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3">
          Dental Management
        </h2>
        <ul className="space-y-2">
          {NAV_ITEMS.map((item) => {
            const isActive = currentSection === item.id;
            const isHighlighted = item.highlight;
            const isNew = item.new;
            
            return (
              <li key={item.id}>
                {integrated && !isAdminPage ? (
                  <Link to={item.path}>
                    <motion.div
                      className={`w-full flex items-center px-3 py-2 rounded-lg transition-colors ${
                        isActive
                          ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg shadow-blue-700/30'
                          : isHighlighted 
                            ? 'bg-blue-500/20 text-blue-300 hover:bg-blue-500/30'
                            : 'text-white/70 hover:bg-white/10 hover:text-white'
                      }`}
                      whileHover={{ x: 5 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <span className="mr-3">{item.icon}</span>
                      <span className={isHighlighted && !isActive ? 'font-semibold' : ''}>
                        {item.label}
                      </span>
                      {isNew && (
                        <span className="ml-auto bg-green-500 text-white text-xs px-1.5 py-0.5 rounded-full">
                          New
                        </span>
                      )}
                    </motion.div>
                  </Link>
                ) : (
                  <motion.button
                    onClick={() => handleNavigation(item)}
                    className={`w-full flex items-center px-3 py-2 rounded-lg transition-colors ${
                      isActive
                        ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg shadow-blue-700/30'
                        : isHighlighted 
                          ? 'bg-blue-500/20 text-blue-300 hover:bg-blue-500/30'
                          : 'text-white/70 hover:bg-white/10 hover:text-white'
                    }`}
                    whileHover={{ x: 5 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <span className="mr-3">{item.icon}</span>
                    <span className={isHighlighted && !isActive ? 'font-semibold' : ''}>
                      {item.label}
                    </span>
                    {isNew && (
                      <span className="ml-auto bg-green-500 text-white text-xs px-1.5 py-0.5 rounded-full">
                        New
                      </span>
                    )}
                  </motion.button>
                )}
              </li>
            );
          })}
        </ul>
        
        <div className="mt-8 pt-6 border-t border-gray-700/40">
          <h2 className="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3">
            Active Patients
          </h2>
          <div className="bg-gray-800/50 rounded-lg p-3">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm text-gray-300">Today</span>
              {isLoading ? (
                <div className="w-5 h-5 rounded-full border-2 border-blue-400 border-t-transparent animate-spin"></div>
              ) : (
                <span className="text-sm font-semibold text-blue-400">{activePatients.today}</span>
              )}
            </div>
            <div className="w-full bg-gray-700/30 h-1.5 rounded-full overflow-hidden">
              <div 
                className="bg-gradient-to-r from-blue-500 to-purple-500 h-full rounded-full transition-all duration-700" 
                style={{width: `${activePatients.percentage}%`}}
              ></div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Positioned fixed at the bottom of the sidebar */}
      <div className="mt-auto pt-4 sticky bottom-0">
        <button
          data-action="sign-out"
          className="w-full flex items-center justify-center px-3 py-3 bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors shadow-lg"
          onClick={handleLogout}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
          </svg>
          <span className="font-medium">Logout Admin</span>
        </button>
      </div>
      
      {/* Logout Confirmation Modal */}
      <Modal 
        isOpen={showLogoutModal} 
        onClose={cancelLogout}
        title="Confirm Logout"
      >
        <div className="logout-confirmation-modal">
          <p>Are you sure you want to logout from the admin area?</p>
          <div className="logout-modal-buttons flex justify-end space-x-3 mt-6">
            <button 
              className="px-4 py-2 bg-gray-700 text-gray-300 rounded-lg hover:bg-gray-600"
              onClick={cancelLogout}
            >
              No
            </button>
            <button 
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-500"
              onClick={confirmLogout}
            >
              Yes
            </button>
          </div>
        </div>
      </Modal>
    </div>
  );
}