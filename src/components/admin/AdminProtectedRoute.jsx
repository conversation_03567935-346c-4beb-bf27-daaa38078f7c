import React, { useEffect, useState, useCallback } from 'react';
import { Navigate, Outlet, useLocation } from 'react-router-dom';
import { useAdmin } from '../../lib/hooks/useAdmin';
import LoadingScreen from '../common/LoadingScreen';

export default function AdminProtectedRoute() {
  const { isAdmin, loading } = useAdmin();
  const location = useLocation();
  const [redirectPath, setRedirectPath] = useState(null);
  
  // Memoize the authentication check to avoid repeated work
  const checkAuthStatus = useCallback(() => {
    // Check for explicit logout flag
    const hasLoggedOut = localStorage.getItem('admin_logged_out') === 'true';
    
    // First priority: if user has explicitly logged out, redirect to home and clear any cached admin data
    if (hasLoggedOut) {
      // Additional cleanup to ensure complete logout before redirect
      if (window && window.smiloAdmin) {
        window.smiloAdmin.isLoggedIn = false;
      }
      
      return '/';
    }

    // If not admin, redirect to admin login page instead of home
    if (!loading && !isAdmin) {
      // Only redirect to login if not already on the login page (prevent infinite loops)
      if (location.pathname !== '/admin/login') {
        return '/admin/login';
      }
    }
    
    // No redirection needed
    return null;
  }, [isAdmin, loading, location.pathname]);
  
  // Determine if we need to redirect
  useEffect(() => {
    const path = checkAuthStatus();
    setRedirectPath(path);
  }, [checkAuthStatus]);

  // If still loading, show a simplified loading indicator with no animations
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-900">
        <div className="text-center">
          <div className="w-12 h-12 border-t-3 border-blue-500 border-solid rounded-full animate-spin mx-auto mb-3"></div>
          <p className="text-white text-lg">Verifying access...</p>
        </div>
      </div>
    );
  }

  // If we need to redirect, do it
  if (redirectPath) {
    return <Navigate to={redirectPath} replace />;
  }

  // Otherwise, render the admin route
  return <Outlet />;
} 