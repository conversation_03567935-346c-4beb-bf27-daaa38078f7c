import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  PlusIcon, 
  MagnifyingGlassIcon,
  ChevronDownIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon
} from '@heroicons/react/24/outline';

// Sample patient data
const PATIENTS = [
  { 
    id: 'PT-10001', 
    name: '<PERSON>', 
    photo: 'https://randomuser.me/api/portraits/women/44.jpg',
    age: 32, 
    lastVisit: '2023-11-15', 
    treatment: 'Orthodontic Adjustment',
    status: 'Active',
    nextAppointment: '2023-12-02'
  },
  { 
    id: 'PT-10002', 
    name: '<PERSON>', 
    photo: 'https://randomuser.me/api/portraits/men/32.jpg',
    age: 45, 
    lastVisit: '2023-10-28', 
    treatment: 'Crown Replacement',
    status: 'Active',
    nextAppointment: '2023-12-10'
  },
  { 
    id: 'PT-10003', 
    name: '<PERSON>', 
    photo: 'https://randomuser.me/api/portraits/women/67.jpg',
    age: 29, 
    lastVisit: '2023-11-20', 
    treatment: 'General Cleaning',
    status: 'Completed',
    nextAppointment: '2024-05-20'
  },
  { 
    id: 'PT-10004', 
    name: '<PERSON> <PERSON>', 
    photo: 'https://randomuser.me/api/portraits/men/11.jpg',
    age: 58, 
    lastVisit: '2023-11-05', 
    treatment: 'Root Canal',
    status: 'Follow-up',
    nextAppointment: '2023-12-05'
  },
  { 
    id: 'PT-10005', 
    name: 'Jennifer Adams', 
    photo: 'https://randomuser.me/api/portraits/women/23.jpg',
    age: 41, 
    lastVisit: '2023-11-12', 
    treatment: 'Invisalign Check',
    status: 'Active',
    nextAppointment: '2023-12-12'
  }
];

// Status color mapping
const STATUS_COLORS = {
  'Active': 'bg-green-500',
  'Completed': 'bg-blue-500',
  'Follow-up': 'bg-amber-500',
  'Delayed': 'bg-red-500',
  'Scheduled': 'bg-purple-500'
};

export default function DentalPatientManager() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('All');
  
  // Filter patients based on search term and status
  const filteredPatients = PATIENTS.filter(patient => {
    const matchesSearch = patient.name.toLowerCase().includes(searchTerm.toLowerCase()) || 
                        patient.id.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = selectedStatus === 'All' || patient.status === selectedStatus;
    return matchesSearch && matchesStatus;
  });

  return (
    <motion.div 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="bg-gray-800/50 rounded-xl backdrop-blur-sm border border-gray-700/50 overflow-hidden"
    >
      <div className="p-6 border-b border-gray-700/50">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-white">Patient Records</h2>
          <div className="flex items-center space-x-3">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg flex items-center shadow-lg shadow-blue-700/30"
            >
              <PlusIcon className="w-5 h-5 mr-2" />
              New Patient
            </motion.button>
          </div>
        </div>
        
        <div className="bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-lg p-4 mb-8">
          <div className="flex items-center text-blue-300">
            <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>This module is currently under active development. Some features may be limited.</span>
          </div>
        </div>

        <div className="flex flex-wrap gap-4 mb-6">
          <div className="relative flex-1 min-w-[250px]">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              className="block w-full pl-10 pr-3 py-2 rounded-lg bg-gray-700/50 border border-gray-600 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Search patients by name or ID..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          
          <div className="relative min-w-[180px]">
            <select
              className="appearance-none block w-full px-3 py-2 rounded-lg bg-gray-700/50 border border-gray-600 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 pr-10"
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
            >
              <option value="All">All Status</option>
              <option value="Active">Active</option>
              <option value="Completed">Completed</option>
              <option value="Follow-up">Follow-up</option>
              <option value="Delayed">Delayed</option>
              <option value="Scheduled">Scheduled</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400">
              <ChevronDownIcon className="h-5 w-5" />
            </div>
          </div>
        </div>
      </div>
      
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-700/30">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Patient</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">ID</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Last Visit</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Treatment</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Next Appointment</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-700/30">
            {filteredPatients.length > 0 ? (
              filteredPatients.map((patient, index) => (
                <motion.tr 
                  key={patient.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.05 }}
                  className="hover:bg-gray-700/30"
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="h-10 w-10 flex-shrink-0">
                        <img className="h-10 w-10 rounded-full object-cover" src={patient.photo} alt="" />
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-white">{patient.name}</div>
                        <div className="text-sm text-gray-400">{patient.age} years</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">{patient.id}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">{patient.lastVisit}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">{patient.treatment}</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${STATUS_COLORS[patient.status]} text-white`}>
                      {patient.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">{patient.nextAppointment}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button className="text-blue-400 hover:text-blue-300">
                        <EyeIcon className="h-5 w-5" />
                      </button>
                      <button className="text-amber-400 hover:text-amber-300">
                        <PencilIcon className="h-5 w-5" />
                      </button>
                      <button className="text-red-400 hover:text-red-300">
                        <TrashIcon className="h-5 w-5" />
                      </button>
                    </div>
                  </td>
                </motion.tr>
              ))
            ) : (
              <tr>
                <td colSpan="7" className="px-6 py-4 text-center text-gray-400">
                  No patients found matching your criteria.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      
      <div className="p-6 border-t border-gray-700/50 flex justify-between items-center">
        <div className="text-sm text-gray-400">
          Showing <span className="font-medium text-white">{filteredPatients.length}</span> out of <span className="font-medium text-white">{PATIENTS.length}</span> patients
        </div>
        <div className="flex space-x-2">
          <button className="px-3 py-1 rounded bg-gray-700/50 text-gray-300 hover:bg-gray-700">Previous</button>
          <button className="px-3 py-1 rounded bg-blue-600 text-white hover:bg-blue-700">Next</button>
        </div>
      </div>
    </motion.div>
  );
} 