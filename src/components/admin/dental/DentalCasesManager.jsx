import React from 'react';
import { motion } from 'framer-motion';

export default function DentalCasesManager() {
  return (
    <motion.div 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="bg-gray-800/50 rounded-xl backdrop-blur-sm border border-gray-700/50 overflow-hidden p-6"
    >
      <div className="text-center py-12">
        <div className="w-24 h-24 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
          <svg className="w-12 h-12 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
        </div>
        
        <h2 className="text-2xl font-bold text-white mb-2">Dental Cases Management</h2>
        <p className="text-gray-400 max-w-2xl mx-auto mb-8">
          This module is currently under development. The dental cases management system will allow you to track complex patient cases, treatment plans, and collaborate with specialists.
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
          <div className="bg-gray-700/30 border border-gray-700 rounded-lg p-5">
            <h3 className="text-lg font-semibold text-blue-400 mb-2">Case Tracking</h3>
            <p className="text-gray-400 text-sm">
              Monitor treatment progress with multi-phase tracking and visual timelines.
            </p>
            <div className="mt-4 bg-gray-800/70 h-2 rounded-full overflow-hidden">
              <div className="bg-blue-500 h-full" style={{width: '75%'}}></div>
            </div>
            <p className="text-xs text-right mt-1 text-gray-500">Coming in Q1 2024</p>
          </div>
          
          <div className="bg-gray-700/30 border border-gray-700 rounded-lg p-5">
            <h3 className="text-lg font-semibold text-purple-400 mb-2">Specialist Network</h3>
            <p className="text-gray-400 text-sm">
              Streamlined referrals and collaboration with dental specialists.
            </p>
            <div className="mt-4 bg-gray-800/70 h-2 rounded-full overflow-hidden">
              <div className="bg-purple-500 h-full" style={{width: '40%'}}></div>
            </div>
            <p className="text-xs text-right mt-1 text-gray-500">Coming in Q2 2024</p>
          </div>
          
          <div className="bg-gray-700/30 border border-gray-700 rounded-lg p-5">
            <h3 className="text-lg font-semibold text-green-400 mb-2">Treatment Outcomes</h3>
            <p className="text-gray-400 text-sm">
              Document and analyze successful treatment outcomes for case studies.
            </p>
            <div className="mt-4 bg-gray-800/70 h-2 rounded-full overflow-hidden">
              <div className="bg-green-500 h-full" style={{width: '25%'}}></div>
            </div>
            <p className="text-xs text-right mt-1 text-gray-500">Coming in Q3 2024</p>
          </div>
        </div>
        
        <div className="mt-12">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg shadow-lg shadow-blue-700/20"
          >
            Get Early Access
          </motion.button>
          
          <p className="text-sm text-gray-500 mt-4">
            Contact us to join the beta testing program for this module
          </p>
        </div>
      </div>
    </motion.div>
  );
} 