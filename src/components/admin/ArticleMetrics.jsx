import React, { useState, useEffect } from 'react';
import { Card } from '../common';
import {
  BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer,
  LineChart, Line, PieChart, Pie, Cell
} from 'recharts';
import { supabase } from '../../lib/supabase';
import { getSystemStatus } from '../../lib/services/monitoring';

// Colors for charts
const COLORS = ['#10B981', '#3B82F6', '#8B5CF6', '#EF4444', '#F59E0B'];

export default function ArticleMetrics() {
  const [metrics, setMetrics] = useState([]);
  const [articles, setArticles] = useState({ total: 0, sources: [] });
  const [status, setStatus] = useState(null);
  const [lastRun, setLastRun] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadData();
    const interval = setInterval(loadData, 300000); // Refresh every 5 minutes
    return () => clearInterval(interval);
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      
      // Fetch metrics, article stats, system status in parallel
      const [metricsResult, articlesCountResult, sourcesResult, statusResult, lastRunResult] = await Promise.all([
        supabase
          .from('article_metrics')
          .select('*')
          .order('created_at', { ascending: false })
          .limit(10),
        supabase
          .from('dental_articles')
          .select('*', { count: 'exact', head: true }),
        supabase
          .from('dental_articles')
          .select('source, count')
          .group('source'),
        getSystemStatus(),
        supabase
          .from('system_logs')
          .select('created_at, component, level, message')
          .eq('component', 'article_fetcher_script')
          .order('created_at', { ascending: false })
          .limit(1)
      ]);

      // Process data
      setMetrics(metricsResult.data || []);
      
      setArticles({
        total: articlesCountResult.count || 0,
        sources: sourcesResult.data || []
      });
      
      setStatus(statusResult);
      
      if (lastRunResult.data?.length > 0) {
        setLastRun(lastRunResult.data[0]);
      }
      
    } catch (error) {
      console.error('Error loading article metrics:', error);
    } finally {
      setLoading(false);
    }
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'Unknown';
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Calculate source distribution data for pie chart
  const getSourceData = () => {
    if (!articles.sources?.length) return [];
    
    // Sort by count and take top 4 + group the rest as "Other"
    const sortedSources = [...articles.sources].sort((a, b) => b.count - a.count);
    
    if (sortedSources.length <= 5) return sortedSources;
    
    const topSources = sortedSources.slice(0, 4);
    const otherCount = sortedSources.slice(4).reduce((sum, src) => sum + src.count, 0);
    
    return [
      ...topSources,
      { source: 'Other', count: otherCount }
    ];
  };

  if (loading && !metrics.length) {
    return (
      <div className="p-6 bg-white/5 rounded-lg animate-pulse">
        <div className="h-8 bg-white/10 rounded mb-4"></div>
        <div className="grid grid-cols-3 gap-4 mb-6">
          <div className="h-24 bg-white/10 rounded-lg"></div>
          <div className="h-24 bg-white/10 rounded-lg"></div>
          <div className="h-24 bg-white/10 rounded-lg"></div>
        </div>
        <div className="h-64 bg-white/10 rounded-lg mb-6"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <h2 className="text-xl font-semibold text-white mb-4">Article System Dashboard</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-white/5 rounded-lg p-4">
            <h3 className="text-sm font-medium text-white/80 mb-1">Total Articles</h3>
            <div className="text-2xl font-bold text-white">
              {articles.total}
            </div>
            <div className="text-xs text-white/60 mt-1">
              From {articles.sources?.length || 0} different sources
            </div>
          </div>
          
          <div className="bg-white/5 rounded-lg p-4">
            <h3 className="text-sm font-medium text-white/80 mb-1">System Status</h3>
            <div className={`text-lg font-bold ${
              status?.articleUpdates.healthy ? 'text-green-400' : 'text-red-400'
            }`}>
              {status?.articleUpdates.healthy ? 'Healthy' : 'Issues Detected'}
            </div>
            <div className="text-xs text-white/60 mt-1">
              Last check: {formatDate(status?.lastCheck)}
            </div>
          </div>
          
          <div className="bg-white/5 rounded-lg p-4">
            <h3 className="text-sm font-medium text-white/80 mb-1">Last Fetch Run</h3>
            <div className={`text-lg font-bold ${
              lastRun?.level === 'error' ? 'text-red-400' : 'text-green-400'
            }`}>
              {lastRun?.level === 'error' ? 'Failed' : 'Successful'}
            </div>
            <div className="text-xs text-white/60 mt-1">
              {formatDate(lastRun?.created_at)}
            </div>
          </div>
        </div>
        
        {/* Article Sources Distribution */}
        <div className="mb-8">
          <h3 className="text-lg font-medium text-white mb-3">Article Sources</h3>
          <div className="h-72">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={getSourceData()}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="count"
                  nameKey="source"
                  label={({ source, percent }) => `${source}: ${(percent * 100).toFixed(0)}%`}
                >
                  {getSourceData().map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => [`${value} articles`, 'Count']} />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>
        
        {/* Recent Fetching Performance */}
        <div className="mb-8">
          <h3 className="text-lg font-medium text-white mb-3">Recent Fetching Performance</h3>
          <div className="h-72">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={metrics}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#444" />
                <XAxis 
                  dataKey="created_at" 
                  tickFormatter={time => new Date(time).toLocaleDateString()} 
                  stroke="#aaa"
                />
                <YAxis stroke="#aaa" />
                <Tooltip 
                  formatter={(value, name) => [value, name.split('_').map(w => w.charAt(0).toUpperCase() + w.slice(1)).join(' ')]}
                  labelFormatter={label => new Date(label).toLocaleString()}
                />
                <Bar dataKey="fetched" name="Articles Fetched" fill="#3B82F6" />
                <Bar dataKey="stored" name="Articles Stored" fill="#10B981" />
                <Bar dataKey="failed" name="Failed" fill="#EF4444" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
        
        {/* Run Duration Trend */}
        <div>
          <h3 className="text-lg font-medium text-white mb-3">Run Duration (ms)</h3>
          <div className="h-72">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={metrics}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#444" />
                <XAxis 
                  dataKey="created_at" 
                  tickFormatter={time => new Date(time).toLocaleDateString()} 
                  stroke="#aaa"
                />
                <YAxis stroke="#aaa" />
                <Tooltip
                  formatter={(value) => [`${value} ms`, 'Duration']}
                  labelFormatter={label => new Date(label).toLocaleString()}
                />
                <Line 
                  type="monotone" 
                  dataKey="duration_ms" 
                  stroke="#8B5CF6" 
                  activeDot={{ r: 8 }}
                  name="Duration" 
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>
      </Card>
      
      {/* Manual Trigger Button */}
      <div className="flex justify-end">
        <button
          onClick={() => {
            // We'll implement this on a backend endpoint later
            alert('This feature is under development. In the future, you will be able to manually trigger an article fetch.');
          }}
          className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors"
        >
          Run Article Fetch Now
        </button>
      </div>
    </div>
  );
}