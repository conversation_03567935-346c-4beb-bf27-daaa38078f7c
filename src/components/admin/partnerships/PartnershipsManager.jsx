import React, { useState, useEffect } from 'react';
import { Card } from '../../common';
import { motion } from 'framer-motion';
import { EyeIcon, PencilIcon, TrashIcon, PlusIcon, LinkIcon, ChevronDownIcon } from '@heroicons/react/24/outline';

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      type: "spring",
      stiffness: 100
    }
  }
};

export default function PartnershipsManager() {
  const [partnerships, setPartnerships] = useState([
    {
      id: 1,
      name: "OrthoSync Technologies",
      status: "Active",
      type: "Technology Integration",
      contact: "Dr. <PERSON>",
      email: "<EMAIL>",
      phone: "(*************",
      agreementDate: "2023-08-22",
      details: "Integration of Smilo AI with orthodontic treatment planning software",
      revenue: 125000,
      location: "New York, Boston, Chicago",
      clinics: 18,
      logo: "https://via.placeholder.com/40/3B82F6/FFFFFF?text=OS"
    },
    {
      id: 2,
      name: "PerioDental Network",
      status: "Active",
      type: "Clinical Research",
      contact: "Dr. Michael Chen",
      email: "<EMAIL>",
      phone: "(*************",
      agreementDate: "2023-06-15",
      details: "Joint research on periodontal disease detection using AI",
      revenue: 87500,
      location: "Los Angeles, Seattle, San Diego",
      clinics: 12,
      logo: "https://via.placeholder.com/40/8B5CF6/FFFFFF?text=PD"
    },
    {
      id: 3,
      name: "KidSmile Pediatric Group",
      status: "Pending",
      type: "Educational Partnership",
      contact: "Dr. Emma Rodriguez",
      email: "<EMAIL>",
      phone: "(*************",
      agreementDate: "2023-11-10",
      details: "Educational content and training for pediatric dental care",
      revenue: 50000,
      location: "Denver, Phoenix, Austin",
      clinics: 8,
      logo: "https://via.placeholder.com/40/EC4899/FFFFFF?text=KS"
    },
    {
      id: 4,
      name: "Dental Innovations Institute",
      status: "Active",
      type: "Research & Development",
      contact: "Dr. James Wilson",
      email: "<EMAIL>",
      phone: "(*************",
      agreementDate: "2023-09-05",
      details: "Co-development of next-gen dental imaging solutions",
      revenue: 105000,
      location: "San Francisco, Portland",
      clinics: 4,
      logo: "https://via.placeholder.com/40/10B981/FFFFFF?text=DI"
    },
    {
      id: 5,
      name: "GlobalDent Alliance",
      status: "Negotiation",
      type: "Distribution Agreement",
      contact: "Alexandra Park",
      email: "<EMAIL>",
      phone: "(*************",
      agreementDate: "2023-12-01",
      details: "International distribution of Smilo dental products",
      revenue: 200000,
      location: "London, Toronto, Sydney",
      clinics: 27,
      logo: "https://via.placeholder.com/40/F59E0B/FFFFFF?text=GD"
    }
  ]);
  
  const [filterStatus, setFilterStatus] = useState('All');
  const [filterType, setFilterType] = useState('All');
  const [expandedId, setExpandedId] = useState(null);

  // Fix to ensure proper display of the component
  useEffect(() => {
    const fixDisplayIssues = () => {
      // Force re-render after brief delay to ensure styles are applied correctly
      setTimeout(() => {
        document.querySelector('.partnerships-container')?.classList.add('ready');
      }, 100);
    };

    fixDisplayIssues();
    
    // If window.smiloAdmin exists, register this component
    if (window.smiloAdmin) {
      window.smiloAdmin.partnershipsLoaded = true;
    }
  }, []);

  // Calculate statistics
  const totalPartnerships = partnerships.length;
  const activePartnerships = partnerships.filter(p => p.status === "Active").length;
  const totalRevenue = partnerships.reduce((sum, p) => sum + p.revenue, 0);
  const totalClinics = partnerships.reduce((sum, p) => sum + p.clinics, 0);
  
  // Filter partnerships
  const filteredPartnerships = partnerships.filter(partnership => {
    return (
      (filterStatus === 'All' || partnership.status === filterStatus) &&
      (filterType === 'All' || partnership.type === filterType)
    );
  });
  
  // Toggle expanded row
  const toggleExpand = (id) => {
    setExpandedId(expandedId === id ? null : id);
  };
  
  // Get unique statuses and types for filters
  const statuses = ['All', ...new Set(partnerships.map(p => p.status))];
  const types = ['All', ...new Set(partnerships.map(p => p.type))];

  return (
    <motion.div
      className="space-y-8 partnerships-container"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <motion.div
        variants={itemVariants}
        className="flex justify-between items-center"
      >
        <h1 className="text-3xl font-bold text-white">
          Dental Partnerships
        </h1>
        
        <button className="flex items-center space-x-2 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white py-2 px-4 rounded-lg shadow-lg transition-colors">
          <PlusIcon className="w-5 h-5" />
          <span>Add Partnership</span>
        </button>
      </motion.div>
      
      <motion.div
        variants={containerVariants}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
      >
        <motion.div variants={itemVariants}>
          <Card>
            <div className="p-6">
              <h3 className="text-sm font-medium text-gray-400 mb-2">Total Partnerships</h3>
              <p className="text-3xl font-bold text-white">{totalPartnerships}</p>
              <div className="mt-2 flex items-center text-sm">
                <span className="text-green-400">+2</span>
                <span className="text-gray-400 ml-1">from last month</span>
              </div>
            </div>
          </Card>
        </motion.div>
        
        <motion.div variants={itemVariants}>
          <Card>
            <div className="p-6">
              <h3 className="text-sm font-medium text-gray-400 mb-2">Active Partnerships</h3>
              <p className="text-3xl font-bold text-white">{activePartnerships}</p>
              <div className="mt-2 flex items-center text-sm">
                <span className="text-gray-400">
                  {Math.round((activePartnerships / totalPartnerships) * 100)}% activation rate
                </span>
              </div>
            </div>
          </Card>
        </motion.div>
        
        <motion.div variants={itemVariants}>
          <Card>
            <div className="p-6">
              <h3 className="text-sm font-medium text-gray-400 mb-2">Total Revenue</h3>
              <p className="text-3xl font-bold text-white">${(totalRevenue / 1000).toFixed(0)}k</p>
              <div className="mt-2 flex items-center text-sm">
                <span className="text-green-400">+$18k</span>
                <span className="text-gray-400 ml-1">from last quarter</span>
              </div>
            </div>
          </Card>
        </motion.div>
        
        <motion.div variants={itemVariants}>
          <Card>
            <div className="p-6">
              <h3 className="text-sm font-medium text-gray-400 mb-2">Dental Clinics</h3>
              <p className="text-3xl font-bold text-white">{totalClinics}</p>
              <div className="mt-2 flex items-center text-sm">
                <span className="text-gray-400">
                  Across {partnerships.length} partnerships
                </span>
              </div>
            </div>
          </Card>
        </motion.div>
      </motion.div>
      
      <motion.div variants={itemVariants}>
        <Card>
          <div className="p-6">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
              <h2 className="text-xl font-semibold text-white mb-4 md:mb-0">Partnership Agreements</h2>
              
              <div className="flex flex-wrap gap-3">
                <select 
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="bg-gray-700 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5"
                >
                  {statuses.map(status => (
                    <option key={status} value={status}>{status} Status</option>
                  ))}
                </select>
                
                <select
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value)}
                  className="bg-gray-700 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5"
                >
                  {types.map(type => (
                    <option key={type} value={type}>{type === 'All' ? 'All Types' : type}</option>
                  ))}
                </select>
              </div>
            </div>
            
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-700">
                <thead className="bg-gray-800">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Organization
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Type
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Status
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Revenue
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Date
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-gray-800 divide-y divide-gray-700">
                  {filteredPartnerships.length === 0 ? (
                    <tr>
                      <td colSpan="6" className="px-6 py-4 text-center text-gray-400">
                        No partnerships found matching the selected filters
                      </td>
                    </tr>
                  ) : (
                    filteredPartnerships.map((partnership) => (
                      <React.Fragment key={partnership.id}>
                        <tr className="hover:bg-gray-700/50 transition-colors cursor-pointer" onClick={() => toggleExpand(partnership.id)}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="h-10 w-10 flex-shrink-0 rounded-full overflow-hidden">
                                <div className="w-full h-full flex items-center justify-center">
                                  {partnership.logo}
                                </div>
                              </div>
                              <div className="ml-4">
                                <div className="text-sm font-medium text-white">{partnership.name}</div>
                                <div className="text-sm text-gray-400">{partnership.clinics} clinics</div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-200">{partnership.type}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                              partnership.status === 'Active' ? 'bg-green-900/20 text-green-400' :
                              partnership.status === 'Pending' ? 'bg-blue-900/20 text-blue-400' :
                              'bg-yellow-900/20 text-yellow-400'
                            }`}>
                              {partnership.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-200">
                            ${(partnership.revenue / 1000).toFixed(0)}k
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                            {partnership.agreementDate}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div className="flex justify-end space-x-3">
                              <button className="text-gray-400 hover:text-blue-400">
                                <EyeIcon className="w-5 h-5" />
                              </button>
                              <button className="text-gray-400 hover:text-blue-400">
                                <PencilIcon className="w-5 h-5" />
                              </button>
                              <button 
                                className="text-gray-400 hover:text-red-400"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  console.log('Delete partnership', partnership.id);
                                }}
                              >
                                <TrashIcon className="w-5 h-5" />
                              </button>
                              <button 
                                className="text-gray-400 hover:text-blue-400"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  toggleExpand(partnership.id);
                                }}
                              >
                                <ChevronDownIcon className={`w-5 h-5 transition-transform ${expandedId === partnership.id ? 'rotate-180' : ''}`} />
                              </button>
                            </div>
                          </td>
                        </tr>
                        
                        {expandedId === partnership.id && (
                          <tr className="bg-gray-800/50">
                            <td colSpan="6" className="px-6 py-4">
                              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div>
                                  <h4 className="text-sm font-medium text-gray-400 mb-2">Contact Details</h4>
                                  <p className="text-sm text-white mb-1">{partnership.contact}</p>
                                  <p className="text-sm text-blue-400 mb-1">{partnership.email}</p>
                                  <p className="text-sm text-gray-300">{partnership.phone}</p>
                                </div>
                                <div>
                                  <h4 className="text-sm font-medium text-gray-400 mb-2">Locations</h4>
                                  <p className="text-sm text-gray-300">{partnership.location}</p>
                                </div>
                                <div>
                                  <h4 className="text-sm font-medium text-gray-400 mb-2">Agreement Details</h4>
                                  <p className="text-sm text-gray-300">{partnership.details}</p>
                                </div>
                              </div>
                              <div className="mt-4 flex justify-end">
                                <button className="flex items-center space-x-1 text-sm text-blue-400 hover:text-blue-300">
                                  <LinkIcon className="w-4 h-4" />
                                  <span>View full agreement</span>
                                </button>
                              </div>
                            </td>
                          </tr>
                        )}
                      </React.Fragment>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </Card>
      </motion.div>
    </motion.div>
  );
} 