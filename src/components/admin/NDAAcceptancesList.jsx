import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { supabase } from '../../lib/supabase';
import { formatDistanceToNow } from 'date-fns';
import { MapPinIcon, IdentificationIcon, DocumentTextIcon, GlobeAltIcon, ClipboardDocumentCheckIcon } from '@heroicons/react/24/outline';

const NDAAcceptancesList = () => {
  const [acceptances, setAcceptances] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedAcceptance, setSelectedAcceptance] = useState(null);

  useEffect(() => {
    const fetchAcceptances = async () => {
      try {
        setLoading(true);
        const { data, error } = await supabase
          .from('nda_acceptances')
          .select('*')
          .order('acceptance_date', { ascending: false });

        if (error) throw error;
        setAcceptances(data || []);
      } catch (err) {
        console.error('Error fetching NDA acceptances:', err);
        setError('Failed to load NDA acceptances');
      } finally {
        setLoading(false);
      }
    };

    fetchAcceptances();
  }, []);

  const approveAcceptance = async (id) => {
    try {
      const { error } = await supabase
        .from('nda_acceptances')
        .update({ is_approved: true })
        .eq('id', id);

      if (error) throw error;

      // Update local state
      setAcceptances(
        acceptances.map(acceptance => 
          acceptance.id === id ? { ...acceptance, is_approved: true } : acceptance
        )
      );
    } catch (err) {
      console.error('Error approving NDA acceptance:', err);
      alert('Failed to approve NDA acceptance');
    }
  };

  const formatDate = (dateString) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (e) {
      return 'Invalid date';
    }
  };

  const formatTimeAgo = (dateString) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true });
    } catch (e) {
      return '';
    }
  };

  return (
    <motion.div 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="max-w-6xl mx-auto p-4"
    >
      <h2 className="text-2xl font-bold mb-6 text-gray-800">NDA Acceptances</h2>
      
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : error ? (
        <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4 rounded">
          <p>{error}</p>
        </div>
      ) : acceptances.length === 0 ? (
        <div className="bg-gray-50 p-6 rounded-lg shadow-sm text-center">
          <p className="text-gray-500">No NDA acceptances found</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-6">
          {selectedAcceptance ? (
            <AcceptanceDetail 
              acceptance={selectedAcceptance}
              onClose={() => setSelectedAcceptance(null)}
              onApprove={approveAcceptance}
            />
          ) : (
            <div className="bg-white rounded-lg shadow-sm overflow-hidden">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Dentist
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Location
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Action
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {acceptances.map((acceptance) => {
                      const locationText = acceptance.geolocation ? 
                        `${acceptance.geolocation.city || ''}, ${acceptance.geolocation.region || ''}, ${acceptance.geolocation.country || ''}`.replace(/, ,/g, ',').replace(/^,/, '').replace(/,$/, '') : 
                        'Unknown';
                        
                      return (
                        <tr 
                          key={acceptance.id} 
                          onClick={() => setSelectedAcceptance(acceptance)}
                          className="hover:bg-gray-50 cursor-pointer transition-colors"
                        >
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="font-medium text-gray-900">{acceptance.full_name}</div>
                            <div className="text-sm text-gray-500">{acceptance.email}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <MapPinIcon className="h-4 w-4 text-gray-400 mr-1" />
                              <span className="text-sm text-gray-700">{locationText}</span>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">{formatDate(acceptance.acceptance_date)}</div>
                            <div className="text-xs text-gray-500">{formatTimeAgo(acceptance.acceptance_date)}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${acceptance.is_approved ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>
                              {acceptance.is_approved ? 'Approved' : 'Pending'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <button 
                              onClick={(e) => {
                                e.stopPropagation();
                                setSelectedAcceptance(acceptance);
                              }}
                              className="text-blue-600 hover:text-blue-900 mr-4"
                            >
                              View
                            </button>
                            {!acceptance.is_approved && (
                              <button 
                                onClick={(e) => {
                                  e.stopPropagation();
                                  approveAcceptance(acceptance.id);
                                }}
                                className="text-green-600 hover:text-green-900"
                              >
                                Approve
                              </button>
                            )}
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>
      )}
    </motion.div>
  );
};

const AcceptanceDetail = ({ acceptance, onClose, onApprove }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0 }}
      className="bg-white rounded-lg shadow-md overflow-hidden"
    >
      <div className="p-6 border-b border-gray-200">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-bold text-gray-900">{acceptance.full_name}</h3>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-3">Personal Information</h4>
            <div className="space-y-3">
              <div>
                <div className="flex items-center text-sm">
                  <IdentificationIcon className="h-5 w-5 text-gray-400 mr-2" />
                  <span className="font-medium text-gray-700">Full Name:</span>
                </div>
                <p className="ml-7 text-gray-900">{acceptance.full_name}</p>
              </div>
              
              <div>
                <div className="flex items-center text-sm">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                  <span className="font-medium text-gray-700">Email:</span>
                </div>
                <p className="ml-7 text-gray-900">{acceptance.email}</p>
              </div>
              
              {acceptance.dentist_license_number && (
                <div>
                  <div className="flex items-center text-sm">
                    <DocumentTextIcon className="h-5 w-5 text-gray-400 mr-2" />
                    <span className="font-medium text-gray-700">License Number:</span>
                  </div>
                  <p className="ml-7 text-gray-900">{acceptance.dentist_license_number}</p>
                </div>
              )}
              
              {acceptance.practice_npi && (
                <div>
                  <div className="flex items-center text-sm">
                    <ClipboardDocumentCheckIcon className="h-5 w-5 text-gray-400 mr-2" />
                    <span className="font-medium text-gray-700">Practice NPI:</span>
                  </div>
                  <p className="ml-7 text-gray-900">{acceptance.practice_npi}</p>
                </div>
              )}
              
              {acceptance.state_of_practice && (
                <div>
                  <div className="flex items-center text-sm">
                    <GlobeAltIcon className="h-5 w-5 text-gray-400 mr-2" />
                    <span className="font-medium text-gray-700">State of Practice:</span>
                  </div>
                  <p className="ml-7 text-gray-900">{acceptance.state_of_practice}</p>
                </div>
              )}
            </div>
          </div>
          
          <div>
            <h4 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-3">Submission Details</h4>
            <div className="space-y-3">
              <div>
                <div className="flex items-center text-sm">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  <span className="font-medium text-gray-700">Signed Date:</span>
                </div>
                <p className="ml-7 text-gray-900">{new Date(acceptance.acceptance_date).toLocaleString()}</p>
              </div>
              
              <div>
                <div className="flex items-center text-sm">
                  <MapPinIcon className="h-5 w-5 text-gray-400 mr-2" />
                  <span className="font-medium text-gray-700">Location:</span>
                </div>
                {acceptance.geolocation ? (
                  <div className="ml-7">
                    <p className="text-gray-900">
                      {acceptance.geolocation.city && `${acceptance.geolocation.city}, `}
                      {acceptance.geolocation.region && `${acceptance.geolocation.region}, `}
                      {acceptance.geolocation.country || ''}
                    </p>
                    {acceptance.geolocation.latitude && acceptance.geolocation.longitude && (
                      <p className="text-xs text-gray-500 mt-1">
                        Coordinates: {acceptance.geolocation.latitude}, {acceptance.geolocation.longitude}
                      </p>
                    )}
                  </div>
                ) : (
                  <p className="ml-7 text-gray-900">Unknown location</p>
                )}
              </div>
              
              <div>
                <div className="flex items-center text-sm">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <span className="font-medium text-gray-700">IP Address:</span>
                </div>
                <p className="ml-7 text-gray-900">{acceptance.ip_address || 'Not captured'}</p>
              </div>
              
              <div>
                <div className="flex items-center text-sm">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                  <span className="font-medium text-gray-700">Browser:</span>
                </div>
                <p className="ml-7 text-gray-900 text-sm line-clamp-2">{acceptance.user_agent || 'Not captured'}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="px-6 py-4 bg-gray-50">
        <div className="flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 mr-3"
          >
            Close
          </button>
          {!acceptance.is_approved && (
            <button
              onClick={() => {
                onApprove(acceptance.id);
                onClose();
              }}
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Approve NDA
            </button>
          )}
        </div>
      </div>
    </motion.div>
  );
};

export default NDAAcceptancesList; 