import React, { useState } from 'react';
import { Navigate, useNavigate } from 'react-router-dom';
import { supabase } from '../../lib/supabase';
import { useAdmin } from '../../lib/hooks/useAdmin';
import Logo from '../common/Logo';

export default function AdminLoginForm() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const { isAdmin, isAuthenticated, authenticate } = useAdmin();
  
  // Redirect if already authenticated
  if (isAuthenticated && isAdmin) {
    return <Navigate to="/admin" replace />;
  }

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setLoading(true);
    
    try {
      // First try to authenticate with our admin service
      const success = await authenticate(username, password);
      
      if (success) {
        console.log('Admin authentication successful');
        navigate('/admin');
        return;
      }
      
      // If local auth fails, try Supabase as fallback for role-based auth
      try {
        console.log('Trying Supabase authentication...');
        
        // Admin credentials should be in email format for Supabase
        let adminEmail = username;
        if (!adminEmail.includes('@')) {
          adminEmail = `${username}@admin.smilo.dental`;
        }
        
        const { data, error: authError } = await supabase.auth.signInWithPassword({
          email: adminEmail,
          password
        });
        
        if (authError) throw authError;
        
        // Check if user has admin role in Supabase
        const { data: profileData, error: profileError } = await supabase
          .from('user_profiles')
          .select('role')
          .eq('user_id', data.user.id)
          .single();
          
        if (profileError) throw profileError;
        
        if (profileData?.role !== 'admin') {
          throw new Error('Unauthorized: User does not have admin role');
        }
        
        console.log('Supabase admin authentication successful');
        navigate('/admin');
      } catch (supabaseError) {
        console.error('Supabase authentication failed:', supabaseError);
        setError('Invalid credentials or insufficient permissions');
      }
    } catch (err) {
      console.error('Authentication error:', err);
      setError('Authentication failed. Please check your credentials and try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-900 px-4">
      <div className="w-full max-w-md">
        <div className="bg-gray-800 rounded-lg shadow-xl overflow-hidden">
          <div className="px-10 py-8 bg-gradient-to-r from-blue-600 to-purple-600">
            <div className="flex justify-center">
              <Logo size="large" />
            </div>
            <h2 className="text-2xl text-white font-bold text-center mt-4">
              Admin Login
            </h2>
          </div>
          
          <div className="px-10 py-8">
            {error && (
              <div className="mb-6 p-3 bg-red-400/10 border border-red-400/20 rounded text-red-400">
                {error}
              </div>
            )}
            
            <form onSubmit={handleSubmit}>
              <div className="mb-6">
                <label 
                  className="block text-gray-300 text-sm font-semibold mb-2" 
                  htmlFor="username"
                >
                  Username
                </label>
                <input
                  id="username"
                  type="text"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  className="w-full bg-gray-700 text-white border border-gray-600 rounded px-3 py-2 focus:outline-none focus:border-blue-500"
                  placeholder="Enter admin username"
                  required
                  disabled={loading}
                />
              </div>
              
              <div className="mb-6">
                <label 
                  className="block text-gray-300 text-sm font-semibold mb-2" 
                  htmlFor="password"
                >
                  Password
                </label>
                <input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full bg-gray-700 text-white border border-gray-600 rounded px-3 py-2 focus:outline-none focus:border-blue-500"
                  placeholder="••••••••"
                  required
                  disabled={loading}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <button
                  type="submit"
                  className={`w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition-colors ${
                    loading ? 'opacity-75 cursor-not-allowed' : 'hover:from-blue-600 hover:to-purple-700'
                  }`}
                  disabled={loading}
                >
                  {loading ? 'Authenticating...' : 'Sign In'}
                </button>
              </div>
            </form>
          </div>
          
          <div className="px-10 py-4 bg-gray-700/30 border-t border-gray-700">
            <div className="text-center text-sm text-gray-400">
              <a href="/" className="hover:text-blue-400 transition-colors">
                Return to homepage
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 