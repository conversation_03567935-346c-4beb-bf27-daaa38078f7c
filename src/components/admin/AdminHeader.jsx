import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import Logo from '../common/Logo';
import { useUser } from '../../contexts/UserContext';
import { motion, AnimatePresence } from 'framer-motion';
import { BellIcon, UserCircleIcon, ChevronDownIcon, HomeIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { useAdminAuth } from '../../lib/hooks/useAdminAuth';
import './AdminHeader.css';
import Modal from '../ui/Modal';

const AdminHeader = ({ userEmail }) => {
  const { user } = useUser();
  const navigate = useNavigate();
  const [showProfileMenu, setShowProfileMenu] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const [showLogoutModal, setShowLogoutModal] = useState(false);
  const [showReturnToSiteModal, setShowReturnToSiteModal] = useState(false);
  const { adminLogout } = useAdminAuth();

  const handleLogout = () => {
    // Clear all admin sessions
    try {
      // Clear all localStorage item by item
      localStorage.removeItem('admin_session');
      localStorage.removeItem('smilo_admin_session');
      localStorage.removeItem('supabase.auth.token');
      
      // Force clear all admin and session related items
      const keysToRemove = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && (key.includes('admin') || key.includes('session') || key.includes('supabase') || key.includes('token'))) {
          keysToRemove.push(key);
        }
      }
      
      // Remove identified keys
      keysToRemove.forEach(key => {
        localStorage.removeItem(key);
      });
      
      // Reset any admin state in window object
      if (window && window.smiloAdmin) {
        window.smiloAdmin.isLoggedIn = false;
      }
      
      // Also clear sessionStorage
      sessionStorage.clear();
    } catch (e) {
      console.error('Error during logout storage clearing:', e);
    }
    
    // Force navigation to home page with cache-busting parameter
    const timestamp = new Date().getTime();
    console.log('Admin logout - redirecting to home');
    window.location.href = `/?logout=${timestamp}`; 
    
    // Fallback in case the above doesn't work
    setTimeout(() => {
      console.log('Fallback redirect triggered');
      try {
        window.location.replace('/');
      } catch (e) {
        console.error('Redirect failed:', e);
      }
      
      // Third attempt with different approach
      setTimeout(() => {
        try {
          document.location.href = '/';
        } catch (e) {
          console.error('All redirects failed:', e);
          // Last resort - try to open in new tab/window
          window.open('/', '_self');
        }
      }, 100);
    }, 100);
  };

  const handleLogoutClick = () => {
    setShowLogoutModal(true);
  };

  const confirmLogout = () => {
    adminLogout();
    handleLogout();
    setShowLogoutModal(false);
  };

  const cancelLogout = () => {
    setShowLogoutModal(false);
  };
  
  const handleReturnToSiteClick = () => {
    setShowReturnToSiteModal(true);
  };
  
  const confirmReturnToSite = () => {
    // Set the admin logged out flag to prevent auto-redirects back to admin
    localStorage.setItem('admin_logged_out', 'true');
    navigate('/');
    setShowReturnToSiteModal(false);
  };
  
  const cancelReturnToSite = () => {
    setShowReturnToSiteModal(false);
  };

  // Display MikeyMouse as the admin if no email is available
  const displayName = userEmail || 'MikeyMouse (Admin)';
  
  // Sample notifications
  const notifications = [
    { id: 1, title: 'New patient registered', time: '10 minutes ago', read: false },
    { id: 2, title: 'Partnership agreement updated', time: '1 hour ago', read: false },
    { id: 3, title: 'System maintenance completed', time: '3 hours ago', read: true },
  ];
  
  // Get notification count
  const unreadCount = notifications.filter(n => !n.read).length;

  return (
    <header className="sticky top-0 z-20 bg-gray-800/90 backdrop-blur-md border-b border-gray-700/40 shadow-md">
      <div className="flex justify-between items-center py-3 px-6">
        <div className="flex items-center space-x-2">
          <Link to="/admin" className="flex items-center">
            <motion.div
              initial={{ rotate: -10 }}
              animate={{ rotate: 0 }}
              transition={{ duration: 0.5 }}
              className="text-2xl text-blue-400 mr-2"
            >
              🦷
            </motion.div>
            <motion.h1 
              className="text-xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-400"
              initial={{ opacity: 0, y: -5 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              Smilo Dental Admin
            </motion.h1>
          </Link>
        </div>
        
        <div className="flex items-center space-x-4">
          {/* Return to Site Button */}
          <motion.button
            onClick={handleReturnToSiteClick}
            className="flex items-center space-x-1 bg-gradient-to-r from-green-600 to-green-700 px-3 py-1.5 rounded-lg text-white shadow-md hover:from-green-500 hover:to-green-600 transition-all"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <HomeIcon className="w-4 h-4" />
            <span>Return to Site</span>
          </motion.button>
          
          {/* Search Bar */}
          <div className="hidden md:flex items-center relative">
            <input
              type="text"
              placeholder="Search records, patients, tools..."
              className="bg-gray-700/50 border border-gray-600/50 rounded-full pl-10 pr-4 py-1.5 text-sm text-gray-200 placeholder-gray-400 w-64 focus:outline-none focus:ring-2 focus:ring-blue-500/50"
            />
            <svg className="w-4 h-4 text-gray-400 absolute left-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          
          {/* Notifications */}
          <div className="relative">
            <button 
              onClick={() => setShowNotifications(!showNotifications)}
              className="p-1.5 rounded-full text-white/70 hover:text-white hover:bg-gray-700/50 relative"
            >
              <BellIcon className="w-5 h-5" />
              {unreadCount > 0 && (
                <span className="absolute top-0 right-0 bg-red-500 text-white text-xs w-4 h-4 flex items-center justify-center rounded-full">
                  {unreadCount}
                </span>
              )}
            </button>
            
            {showNotifications && (
              <motion.div 
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.2 }}
                className="absolute right-0 mt-2 w-72 bg-gray-800 border border-gray-700 rounded-lg shadow-lg py-2 z-50"
              >
                <h3 className="px-4 py-2 text-sm font-semibold text-gray-300 border-b border-gray-700">Notifications</h3>
                <div className="max-h-64 overflow-y-auto">
                  {notifications.length === 0 ? (
                    <p className="px-4 py-3 text-sm text-gray-400">No notifications</p>
                  ) : (
                    notifications.map(notification => (
                      <div 
                        key={notification.id} 
                        className={`px-4 py-2 hover:bg-gray-700/50 border-b border-gray-700/50 last:border-0 ${!notification.read ? 'bg-blue-900/10' : ''}`}
                      >
                        <div className="flex justify-between">
                          <p className="text-sm font-medium text-white">{notification.title}</p>
                          {!notification.read && <span className="h-2 w-2 rounded-full bg-blue-400"></span>}
                        </div>
                        <p className="text-xs text-gray-400 mt-1">{notification.time}</p>
                      </div>
                    ))
                  )}
                </div>
                <div className="px-4 py-2 border-t border-gray-700">
                  <button className="text-sm text-blue-400 hover:text-blue-300 w-full text-center">
                    View all notifications
                  </button>
                </div>
              </motion.div>
            )}
          </div>
          
          {/* User Menu */}
          <div className="relative">
            <button 
              onClick={() => setShowProfileMenu(!showProfileMenu)}
              className="flex items-center space-x-2 text-white/70 hover:text-white transition-colors"
            >
              <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                <UserCircleIcon className="w-6 h-6 text-white" />
              </div>
              <span className="hidden md:inline text-sm">{displayName}</span>
              <ChevronDownIcon className="w-4 h-4" />
            </button>
            
            {showProfileMenu && (
              <motion.div 
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.2 }}
                className="absolute right-0 mt-2 w-48 bg-gray-800 border border-gray-700 rounded-lg shadow-lg py-1 z-50"
              >
                <Link to="/admin/profile" className="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700">
                  Profile Settings
                </Link>
                <Link to="/admin/preferences" className="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700">
                  Preferences
                </Link>
                <button
                  onClick={handleLogoutClick}
                  className="block w-full text-left px-4 py-2 text-sm text-red-400 hover:bg-gray-700"
                  data-action="sign-out"
                >
                  Sign out
                </button>
              </motion.div>
            )}
          </div>
        </div>
      </div>

      {/* Logout Confirmation Modal */}
      <Modal 
        isOpen={showLogoutModal} 
        onClose={cancelLogout}
        title="Confirm Logout"
      >
        <div className="logout-confirmation-modal">
          <p>Are you sure you want to logout from the admin area?</p>
          <div className="logout-modal-buttons">
            <button 
              className="modal-btn-yes" 
              onClick={confirmLogout}
            >
              Yes
            </button>
            <button 
              className="modal-btn-no" 
              onClick={cancelLogout}
            >
              No
            </button>
          </div>
        </div>
      </Modal>
      
      {/* Return to Site Confirmation Modal */}
      <Modal 
        isOpen={showReturnToSiteModal} 
        onClose={cancelReturnToSite}
        title="Return to Main Site"
      >
        <div className="return-confirmation-modal">
          <p>Are you sure you want to return to the main site?</p>
          <div className="return-modal-buttons">
            <button 
              className="modal-btn-yes" 
              onClick={confirmReturnToSite}
            >
              Yes
            </button>
            <button 
              className="modal-btn-no" 
              onClick={cancelReturnToSite}
            >
              No
            </button>
          </div>
        </div>
      </Modal>
    </header>
  );
};

export default AdminHeader;