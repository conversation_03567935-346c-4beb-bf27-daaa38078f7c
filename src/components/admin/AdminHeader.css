/* AdminHeader.css - Styles for the admin header component */

/* Modal button styles */
.logout-confirmation-modal,
.return-confirmation-modal {
  display: flex;
  flex-direction: column;
  text-align: center;
}

.logout-modal-buttons,
.return-modal-buttons {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 1.5rem;
}

.modal-btn-yes,
.modal-btn-no {
  padding: 0.5rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.2s;
}

.modal-btn-yes {
  background-color: #ef4444; /* red-500 */
  color: white;
}

.modal-btn-yes:hover {
  background-color: #dc2626; /* red-600 */
}

.modal-btn-no {
  background-color: #374151; /* gray-700 */
  color: #d1d5db; /* gray-300 */
}

.modal-btn-no:hover {
  background-color: #4b5563; /* gray-600 */
}

/* Header animation effects */
.header-logo-animation {
  transition: transform 0.3s ease;
}

.header-logo-animation:hover {
  transform: rotate(10deg);
}

/* Notification badge pulse animation */
@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
  }
  
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 5px rgba(239, 68, 68, 0);
  }
  
  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
  }
}

.notification-badge-new {
  animation: pulse 2s infinite;
} 