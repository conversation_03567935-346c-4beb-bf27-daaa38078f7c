import React, { useEffect, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';

/**
 * This component provides automatic admin authentication and redirects
 * to the partnerships section in the admin dashboard.
 * It injects into the global window object for access from anywhere.
 */
const AutoRedirectScript = () => {
  const navigate = useNavigate();
  const location = useLocation();
  // Use ref to track if redirects have been attempted
  const redirectAttempted = useRef(false);

  useEffect(() => {
    // Helper to check existing admin session
    const checkAdminSession = () => {
      // Check if user has explicitly logged out
      if (localStorage.getItem('admin_logged_out') === 'true') {
        return null;
      }
      
      try {
        const sessionData = localStorage.getItem('admin_session');
        if (!sessionData) {
          return null;
        }
        
        const adminSession = JSON.parse(sessionData);
        
        // Validate if session is still valid
        if (adminSession.exp && adminSession.exp > Date.now()) {
          return adminSession;
        } else {
          // Clear expired session
          localStorage.removeItem('admin_session');
          localStorage.removeItem('smilo_admin_session');
          return null;
        }
      } catch (err) {
        console.error('Error checking admin session:', err);
        return null;
      }
    };

    // Helper to handle logout
    const handleLogout = () => {
      // Show confirmation dialog
      const confirmLogout = window.confirm("Are you sure you want to log out from admin? \n\nPress 'OK' to log out or 'Cancel' to stay logged in.");
      
      if (!confirmLogout) {
        return;
      }
      
      // Set logout flag
      localStorage.setItem('admin_logged_out', 'true');
      
      // Clear all admin sessions
      localStorage.removeItem('admin_session');
      localStorage.removeItem('smilo_admin_session');
      localStorage.removeItem('supabase.auth.token');
      
      // Systematically clear all admin-related tokens
      const keysToRemove = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && (
          key.includes('admin') || 
          key.includes('session') || 
          key.includes('supabase') || 
          key.includes('token') ||
          key.includes('smilo')
        )) {
          if (key !== 'admin_logged_out') { // Keep the logout flag
            keysToRemove.push(key);
          }
        }
      }
      
      // Remove identified keys
      keysToRemove.forEach(key => {
        localStorage.removeItem(key);
      });
      
      // Clear any other admin-related items
      if (window.smiloAdmin) {
        window.smiloAdmin.isLoggedIn = false;
      }
      
      // Also clear sessionStorage
      sessionStorage.clear();
      
      // Redirect to home using window.location for a full page reload
      const timestamp = new Date().getTime();
      window.location.href = `/?logout=${timestamp}`;
    };

    // Fix admin display issues - only add style once
    const fixDisplayIssues = () => {
      // Only add style if not already added
      if (!document.getElementById('admin-display-fixes')) {
        const style = document.createElement('style');
        style.id = 'admin-display-fixes';
        style.textContent = `
          /* Fix for admin UI */
          .admin-dashboard-container .partnerships-container .text-green-800,
          .admin-dashboard-container .partnerships-container .text-yellow-800,
          .admin-dashboard-container .partnerships-container .text-red-800 {
            background: rgba(255, 255, 255, 0.1) !important;
            color: rgba(255, 255, 255, 0.9) !important;
          }
        `;
        document.head.appendChild(style);
      }
    };

    // Check if user has explicitly logged out
    const hasLoggedOut = localStorage.getItem('admin_logged_out') === 'true';
    
    // Get existing admin session
    const adminSession = checkAdminSession();
    
    // Create global admin helper object - but respect logout status
    window.smiloAdmin = {
      isLoggedIn: adminSession !== null,
      
      // Fix admin access - redirects to login page to get proper credentials
      fixAdminAccess: () => {
        if (location.pathname.includes('/admin/login')) {
          // Already on login page, do nothing
          return false;
        }
        
        navigate('/admin/login');
        return false;
      },

      // Logout helper
      logout: handleLogout,
      
      // Get current session info
      getSession: () => {
        try {
          return JSON.parse(localStorage.getItem('admin_session'));
        } catch (e) {
          return null;
        }
      },

      // Initialize admin session 
      initAdminSession: () => {
        return checkAdminSession();
      },

      // Optimized partnerships redirect
      forceRedirectToPartnerships: () => {
        // Only redirect if we're on admin page, not logged out, and not already redirected
        if (
          window.location.pathname.includes('/admin') && 
          !hasLoggedOut && 
          window.smiloAdmin && 
          window.smiloAdmin.isLoggedIn && 
          !redirectAttempted.current
        ) {
          // Mark redirect as attempted to prevent multiple redirects
          redirectAttempted.current = true;
          
          // Use URL parameter approach (most reliable)
          if (!location.search.includes('section=partnerships')) {
            navigate('/admin?section=partnerships', { replace: true });
          }
          
          // Much shorter timeout, and only try once with more targeted selector
          setTimeout(() => {
            try {
              // More efficient selector - only look for nav items with specific attributes
              const partnershipsItem = document.querySelector('[data-section="partnerships"], #partnerships-nav');
              
              if (partnershipsItem && typeof partnershipsItem.click === 'function') {
                partnershipsItem.click();
              }
            } catch (error) {
              // Silent fail - URL parameter approach should still work
            }
          }, 200);
        }
      }
    };

    // Fix display issues if we have a valid session - only once
    if (adminSession) {
      fixDisplayIssues();

      // Only redirect once on mount if needed
      if (
        location.pathname === '/admin' && 
        !location.search.includes('section=') && 
        !redirectAttempted.current
      ) {
        setTimeout(() => {
          window.smiloAdmin.forceRedirectToPartnerships();
        }, 200);
      }
    } else if (location.pathname.startsWith('/admin') && location.pathname !== '/admin/login') {
      navigate('/admin/login');
    }

    // Find and update logout buttons - do this once with event delegation instead of many listeners
    const handlePotentialLogoutClick = (e) => {
      const target = e.target.closest('[data-action="sign-out"]');
      if (target) {
        e.preventDefault();
        handleLogout();
      }
    };
    
    document.addEventListener('click', handlePotentialLogoutClick);
    
    // Simplified navigation handler
    const handleNavigation = () => {
      if (
        adminSession && 
        location.pathname === '/admin' && 
        !location.search.includes('section=') &&
        !redirectAttempted.current
      ) {
        redirectAttempted.current = true;
        window.smiloAdmin.forceRedirectToPartnerships();
      }
    };
    
    // Handle history state changes
    window.addEventListener('popstate', handleNavigation);
    
    return () => {
      window.removeEventListener('popstate', handleNavigation);
      document.removeEventListener('click', handlePotentialLogoutClick);
    };
  }, [navigate, location]);

  // This component doesn't render anything visible
  return null;
};

export default AutoRedirectScript;
