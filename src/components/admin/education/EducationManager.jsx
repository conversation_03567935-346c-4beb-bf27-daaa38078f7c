import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  AcademicCapIcon, 
  ClockIcon,
  BookOpenIcon,
  PlayCircleIcon,
  DocumentTextIcon,
  StarIcon,
  ChevronDownIcon
} from '@heroicons/react/24/outline';

// Sample education resources data
const EDUCATION_RESOURCES = [
  {
    id: 'course-1',
    title: 'Advanced Techniques in Cosmetic Dentistry',
    type: 'Course',
    duration: '8 hours',
    level: 'Advanced',
    author: 'Dr. <PERSON>',
    rating: 4.8,
    reviews: 124,
    thumbnail: 'https://images.unsplash.com/photo-1606811971618-4486d14f3f99?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
    tags: ['Cosmetic', 'Techniques', 'Certification']
  },
  {
    id: 'webinar-1',
    title: 'Latest Innovations in Dental Implants',
    type: 'Webinar',
    duration: '90 minutes',
    level: 'Intermediate',
    author: 'Dr. <PERSON>',
    rating: 4.6,
    reviews: 86,
    thumbnail: 'https://images.unsplash.com/photo-1598256989800-fe5f95da9787?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
    tags: ['Implants', 'Innovation', 'Live Discussion']
  },
  {
    id: 'article-1',
    title: 'The Future of Preventive Dental Care',
    type: 'Article',
    duration: '15 minutes',
    level: 'All Levels',
    author: 'Dr. Maria Garcia',
    rating: 4.5,
    reviews: 42,
    thumbnail: 'https://images.unsplash.com/photo-1588776814546-1ffcf47267a5?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
    tags: ['Preventive', 'Research', 'Patient Care']
  },
  {
    id: 'video-1',
    title: 'Pediatric Dental Examination Procedures',
    type: 'Video',
    duration: '45 minutes',
    level: 'Beginner',
    author: 'Dr. Thomas Brown',
    rating: 4.9,
    reviews: 215,
    thumbnail: 'https://images.unsplash.com/photo-1581594549595-35f6fa67d7ed?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
    tags: ['Pediatric', 'Clinical', 'Procedures']
  },
  {
    id: 'course-2',
    title: 'Dental Practice Management Essentials',
    type: 'Course',
    duration: '6 hours',
    level: 'Intermediate',
    author: 'Dr. Sarah Johnson',
    rating: 4.7,
    reviews: 98,
    thumbnail: 'https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
    tags: ['Management', 'Business', 'Staff Training']
  }
];

// Type icons mapping
const TYPE_ICONS = {
  'Course': <AcademicCapIcon className="w-5 h-5" />,
  'Webinar': <ClockIcon className="w-5 h-5" />,
  'Article': <DocumentTextIcon className="w-5 h-5" />,
  'Video': <PlayCircleIcon className="w-5 h-5" />,
  'Book': <BookOpenIcon className="w-5 h-5" />
};

// Type color mapping
const TYPE_COLORS = {
  'Course': 'bg-blue-500',
  'Webinar': 'bg-purple-500',
  'Article': 'bg-green-500',
  'Video': 'bg-red-500',
  'Book': 'bg-amber-500'
};

export default function EducationManager() {
  const [activeFilter, setActiveFilter] = useState('all');
  
  // Filter resources based on active filter
  const filteredResources = activeFilter === 'all' 
    ? EDUCATION_RESOURCES 
    : EDUCATION_RESOURCES.filter(resource => resource.type.toLowerCase() === activeFilter);

  return (
    <motion.div 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="space-y-8"
    >
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-white">Education Resources</h2>
          <p className="text-gray-400 mt-1">Continuing education materials for dental professionals</p>
        </div>
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          className="px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg flex items-center shadow-lg shadow-blue-700/30"
        >
          <span>Add New Resource</span>
        </motion.button>
      </div>
      
      <div className="bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-lg p-4">
        <div className="flex items-center text-blue-300">
          <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>This module is currently under active development. We're working to expand our educational offerings.</span>
        </div>
      </div>
      
      <div className="flex flex-wrap gap-3 mb-6">
        <button 
          onClick={() => setActiveFilter('all')}
          className={`px-4 py-2 rounded-lg text-sm ${
            activeFilter === 'all' 
              ? 'bg-blue-600 text-white' 
              : 'bg-gray-700/50 text-gray-300 hover:bg-gray-700'
          }`}
        >
          All Resources
        </button>
        <button 
          onClick={() => setActiveFilter('course')}
          className={`px-4 py-2 rounded-lg text-sm ${
            activeFilter === 'course' 
              ? 'bg-blue-600 text-white' 
              : 'bg-gray-700/50 text-gray-300 hover:bg-gray-700'
          }`}
        >
          Courses
        </button>
        <button 
          onClick={() => setActiveFilter('webinar')}
          className={`px-4 py-2 rounded-lg text-sm ${
            activeFilter === 'webinar' 
              ? 'bg-blue-600 text-white' 
              : 'bg-gray-700/50 text-gray-300 hover:bg-gray-700'
          }`}
        >
          Webinars
        </button>
        <button 
          onClick={() => setActiveFilter('article')}
          className={`px-4 py-2 rounded-lg text-sm ${
            activeFilter === 'article' 
              ? 'bg-blue-600 text-white' 
              : 'bg-gray-700/50 text-gray-300 hover:bg-gray-700'
          }`}
        >
          Articles
        </button>
        <button 
          onClick={() => setActiveFilter('video')}
          className={`px-4 py-2 rounded-lg text-sm ${
            activeFilter === 'video' 
              ? 'bg-blue-600 text-white' 
              : 'bg-gray-700/50 text-gray-300 hover:bg-gray-700'
          }`}
        >
          Videos
        </button>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredResources.map((resource, index) => (
          <motion.div
            key={resource.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-gray-800/50 rounded-xl backdrop-blur-sm border border-gray-700/50 overflow-hidden h-full flex flex-col"
          >
            <div className="relative h-48 overflow-hidden">
              <img 
                src={resource.thumbnail} 
                alt={resource.title} 
                className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
              />
              <div className="absolute top-3 left-3">
                <span className={`${TYPE_COLORS[resource.type]} text-white text-xs font-semibold px-2 py-1 rounded-full flex items-center`}>
                  <span className="mr-1">{TYPE_ICONS[resource.type]}</span>
                  {resource.type}
                </span>
              </div>
            </div>
            
            <div className="p-5 flex-1 flex flex-col">
              <h3 className="text-lg font-semibold text-white mb-2">{resource.title}</h3>
              
              <div className="text-sm text-gray-400 mb-3 flex items-center">
                <ClockIcon className="w-4 h-4 mr-1" />
                <span>{resource.duration}</span>
                <span className="mx-2">•</span>
                <span>{resource.level}</span>
              </div>
              
              <div className="flex flex-wrap gap-2 mb-4">
                {resource.tags.map(tag => (
                  <span key={tag} className="text-xs bg-gray-700/50 text-gray-300 px-2 py-0.5 rounded">
                    {tag}
                  </span>
                ))}
              </div>
              
              <div className="mt-auto">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <StarIcon className="w-4 h-4 text-yellow-400" />
                    <span className="ml-1 text-sm text-white">{resource.rating}</span>
                    <span className="ml-1 text-xs text-gray-400">({resource.reviews})</span>
                  </div>
                  <span className="text-sm text-gray-400">By {resource.author}</span>
                </div>
              </div>
            </div>
            
            <div className="bg-gray-700/30 p-4 flex justify-between">
              <button className="text-blue-400 text-sm hover:text-blue-300">View Details</button>
              <button className="text-sm bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded">
                Enroll
              </button>
            </div>
          </motion.div>
        ))}
      </div>
      
      {filteredResources.length === 0 && (
        <div className="text-center py-12 text-gray-400 bg-gray-800/30 rounded-lg border border-gray-700/50">
          No educational resources found in this category.
        </div>
      )}
      
      <div className="bg-gray-800/50 rounded-xl backdrop-blur-sm border border-gray-700/50 overflow-hidden p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Certification Progress</h3>
        
        <div className="space-y-4">
          <div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm text-gray-300">Continuing Education Credits</span>
              <span className="text-sm font-semibold text-blue-400">24/40 hours completed</span>
            </div>
            <div className="w-full bg-gray-700/30 h-2.5 rounded-full overflow-hidden">
              <div className="bg-gradient-to-r from-blue-500 to-indigo-600 h-full rounded-full" style={{width: '60%'}}></div>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
            <div className="bg-gray-700/30 border border-gray-700 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-white font-medium">Clinical Skills</h4>
                <span className="text-xs text-green-400">12/15 hrs</span>
              </div>
              <div className="w-full bg-gray-800/70 h-1.5 rounded-full overflow-hidden">
                <div className="bg-green-500 h-full rounded-full" style={{width: '80%'}}></div>
              </div>
            </div>
            
            <div className="bg-gray-700/30 border border-gray-700 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-white font-medium">Patient Management</h4>
                <span className="text-xs text-blue-400">8/10 hrs</span>
              </div>
              <div className="w-full bg-gray-800/70 h-1.5 rounded-full overflow-hidden">
                <div className="bg-blue-500 h-full rounded-full" style={{width: '80%'}}></div>
              </div>
            </div>
            
            <div className="bg-gray-700/30 border border-gray-700 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-white font-medium">Ethics & Regulations</h4>
                <span className="text-xs text-amber-400">4/15 hrs</span>
              </div>
              <div className="w-full bg-gray-800/70 h-1.5 rounded-full overflow-hidden">
                <div className="bg-amber-500 h-full rounded-full" style={{width: '27%'}}></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
} 