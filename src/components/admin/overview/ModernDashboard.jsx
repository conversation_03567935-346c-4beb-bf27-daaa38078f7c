import React from 'react';
import { motion } from 'framer-motion';
import { 
  UserGroupIcon, 
  CalendarIcon, 
  CurrencyDollarIcon, 
  ChartBarIcon,
  ClipboardDocumentCheckIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  BuildingOffice2Icon
} from '@heroicons/react/24/outline';

// Sample data for the dashboard
const DASHBOARD_STATS = {
  patients: {
    total: 1458,
    active: 876,
    growth: 12.4,
    positive: true
  },
  appointments: {
    today: 24,
    upcoming: 86,
    growth: 8.2,
    positive: true
  },
  revenue: {
    monthly: "$48,290",
    yearly: "$578,480",
    growth: -3.6,
    positive: false
  },
  treatments: {
    inProgress: 42,
    completed: 128,
    growth: 5.8,
    positive: true
  },
  practices: {
    active: 3,
    partnered: 12,
    growth: 25.0,
    positive: true
  }
};

// Recent activities
const RECENT_ACTIVITIES = [
  {
    id: 1,
    action: "New patient registered",
    name: "<PERSON>",
    time: "10 minutes ago",
    icon: <UserGroupIcon className="w-5 h-5" />,
    iconBg: "bg-blue-500/20",
    iconColor: "text-blue-500"
  },
  {
    id: 2,
    action: "Treatment plan created",
    name: "<PERSON>",
    time: "32 minutes ago",
    icon: <ClipboardDocumentCheckIcon className="w-5 h-5" />,
    iconBg: "bg-green-500/20",
    iconColor: "text-green-500"
  },
  {
    id: 3,
    action: "Appointment rescheduled",
    name: "Sarah Wilson",
    time: "1 hour ago",
    icon: <CalendarIcon className="w-5 h-5" />,
    iconBg: "bg-amber-500/20",
    iconColor: "text-amber-500"
  },
  {
    id: 4,
    action: "Payment received",
    name: "James Rodriguez",
    time: "2 hours ago",
    icon: <CurrencyDollarIcon className="w-5 h-5" />,
    iconBg: "bg-purple-500/20",
    iconColor: "text-purple-500"
  },
  {
    id: 5,
    action: "New partnership inquiry",
    name: "Sunshine Dental Group",
    time: "4 hours ago",
    icon: <BuildingOffice2Icon className="w-5 h-5" />,
    iconBg: "bg-indigo-500/20",
    iconColor: "text-indigo-500"
  }
];

// Performance metrics
const PERFORMANCE_METRICS = [
  { id: 1, name: "Patient Satisfaction", value: 92, color: "from-green-500 to-emerald-500" },
  { id: 2, name: "Treatment Success Rate", value: 88, color: "from-blue-500 to-cyan-500" },
  { id: 3, name: "Appointment Adherence", value: 76, color: "from-purple-500 to-indigo-500" },
  { id: 4, name: "Practice Growth", value: 68, color: "from-amber-500 to-orange-500" }
];

export default function ModernDashboard() {
  return (
    <motion.div 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="space-y-8"
    >
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">Dashboard Overview</h2>
          <p className="text-gray-400 mt-1">Welcome to Smilo Dental Admin</p>
        </div>
        <div className="flex space-x-3">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="px-4 py-2 bg-gray-800/70 text-gray-300 hover:text-white rounded-lg border border-gray-700"
          >
            Export Report
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg shadow-lg shadow-blue-700/30"
          >
            View All Analytics
          </motion.button>
        </div>
      </div>
      
      {/* Main stats cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-xl p-5"
        >
          <div className="flex justify-between items-start">
            <div>
              <p className="text-gray-400 text-sm">Total Patients</p>
              <h3 className="text-2xl font-bold text-white mt-1">{DASHBOARD_STATS.patients.total}</h3>
              <div className="flex items-center mt-2">
                <span className={`text-xs ${DASHBOARD_STATS.patients.positive ? 'text-green-500' : 'text-red-500'} flex items-center`}>
                  {DASHBOARD_STATS.patients.positive ? <ArrowUpIcon className="w-3 h-3 mr-1" /> : <ArrowDownIcon className="w-3 h-3 mr-1" />}
                  {Math.abs(DASHBOARD_STATS.patients.growth)}%
                </span>
                <span className="text-xs text-gray-500 ml-1">vs last month</span>
              </div>
            </div>
            <div className="p-3 rounded-lg bg-blue-500/20 text-blue-500">
              <UserGroupIcon className="w-6 h-6" />
            </div>
          </div>
          <div className="mt-4 pt-4 border-t border-gray-700/30">
            <p className="text-sm text-gray-400">
              <span className="text-white font-medium">{DASHBOARD_STATS.patients.active}</span> active patients
            </p>
          </div>
        </motion.div>
        
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-xl p-5"
        >
          <div className="flex justify-between items-start">
            <div>
              <p className="text-gray-400 text-sm">Appointments</p>
              <h3 className="text-2xl font-bold text-white mt-1">{DASHBOARD_STATS.appointments.today}</h3>
              <div className="flex items-center mt-2">
                <span className={`text-xs ${DASHBOARD_STATS.appointments.positive ? 'text-green-500' : 'text-red-500'} flex items-center`}>
                  {DASHBOARD_STATS.appointments.positive ? <ArrowUpIcon className="w-3 h-3 mr-1" /> : <ArrowDownIcon className="w-3 h-3 mr-1" />}
                  {Math.abs(DASHBOARD_STATS.appointments.growth)}%
                </span>
                <span className="text-xs text-gray-500 ml-1">vs last week</span>
              </div>
            </div>
            <div className="p-3 rounded-lg bg-purple-500/20 text-purple-500">
              <CalendarIcon className="w-6 h-6" />
            </div>
          </div>
          <div className="mt-4 pt-4 border-t border-gray-700/30">
            <p className="text-sm text-gray-400">
              <span className="text-white font-medium">{DASHBOARD_STATS.appointments.upcoming}</span> upcoming
            </p>
          </div>
        </motion.div>
        
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-xl p-5"
        >
          <div className="flex justify-between items-start">
            <div>
              <p className="text-gray-400 text-sm">Monthly Revenue</p>
              <h3 className="text-2xl font-bold text-white mt-1">{DASHBOARD_STATS.revenue.monthly}</h3>
              <div className="flex items-center mt-2">
                <span className={`text-xs ${DASHBOARD_STATS.revenue.positive ? 'text-green-500' : 'text-red-500'} flex items-center`}>
                  {DASHBOARD_STATS.revenue.positive ? <ArrowUpIcon className="w-3 h-3 mr-1" /> : <ArrowDownIcon className="w-3 h-3 mr-1" />}
                  {Math.abs(DASHBOARD_STATS.revenue.growth)}%
                </span>
                <span className="text-xs text-gray-500 ml-1">vs last month</span>
              </div>
            </div>
            <div className="p-3 rounded-lg bg-green-500/20 text-green-500">
              <CurrencyDollarIcon className="w-6 h-6" />
            </div>
          </div>
          <div className="mt-4 pt-4 border-t border-gray-700/30">
            <p className="text-sm text-gray-400">
              <span className="text-white font-medium">{DASHBOARD_STATS.revenue.yearly}</span> yearly
            </p>
          </div>
        </motion.div>
        
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-xl p-5"
        >
          <div className="flex justify-between items-start">
            <div>
              <p className="text-gray-400 text-sm">Treatments</p>
              <h3 className="text-2xl font-bold text-white mt-1">{DASHBOARD_STATS.treatments.inProgress}</h3>
              <div className="flex items-center mt-2">
                <span className={`text-xs ${DASHBOARD_STATS.treatments.positive ? 'text-green-500' : 'text-red-500'} flex items-center`}>
                  {DASHBOARD_STATS.treatments.positive ? <ArrowUpIcon className="w-3 h-3 mr-1" /> : <ArrowDownIcon className="w-3 h-3 mr-1" />}
                  {Math.abs(DASHBOARD_STATS.treatments.growth)}%
                </span>
                <span className="text-xs text-gray-500 ml-1">completion rate</span>
              </div>
            </div>
            <div className="p-3 rounded-lg bg-amber-500/20 text-amber-500">
              <ClipboardDocumentCheckIcon className="w-6 h-6" />
            </div>
          </div>
          <div className="mt-4 pt-4 border-t border-gray-700/30">
            <p className="text-sm text-gray-400">
              <span className="text-white font-medium">{DASHBOARD_STATS.treatments.completed}</span> completed
            </p>
          </div>
        </motion.div>
        
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-xl p-5"
        >
          <div className="flex justify-between items-start">
            <div>
              <p className="text-gray-400 text-sm">Practices</p>
              <h3 className="text-2xl font-bold text-white mt-1">{DASHBOARD_STATS.practices.active}</h3>
              <div className="flex items-center mt-2">
                <span className={`text-xs ${DASHBOARD_STATS.practices.positive ? 'text-green-500' : 'text-red-500'} flex items-center`}>
                  {DASHBOARD_STATS.practices.positive ? <ArrowUpIcon className="w-3 h-3 mr-1" /> : <ArrowDownIcon className="w-3 h-3 mr-1" />}
                  {Math.abs(DASHBOARD_STATS.practices.growth)}%
                </span>
                <span className="text-xs text-gray-500 ml-1">growth</span>
              </div>
            </div>
            <div className="p-3 rounded-lg bg-indigo-500/20 text-indigo-500">
              <BuildingOffice2Icon className="w-6 h-6" />
            </div>
          </div>
          <div className="mt-4 pt-4 border-t border-gray-700/30">
            <p className="text-sm text-gray-400">
              <span className="text-white font-medium">{DASHBOARD_STATS.practices.partnered}</span> partnerships
            </p>
          </div>
        </motion.div>
      </div>
      
      {/* Second row */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Performance metrics */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6 lg:col-span-2"
        >
          <h3 className="text-lg font-semibold text-white mb-6">Performance Metrics</h3>
          
          <div className="space-y-6">
            {PERFORMANCE_METRICS.map(metric => (
              <div key={metric.id} className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-300">{metric.name}</span>
                  <span className="text-sm font-medium text-white">{metric.value}%</span>
                </div>
                <div className="w-full bg-gray-700/30 h-2 rounded-full overflow-hidden">
                  <div className={`h-full rounded-full bg-gradient-to-r ${metric.color}`} style={{width: `${metric.value}%`}}></div>
                </div>
              </div>
            ))}
          </div>
          
          <div className="mt-6 pt-6 border-t border-gray-700/30 flex justify-between items-center">
            <span className="text-sm text-gray-400">Last updated: Today at 10:23 AM</span>
            <button className="text-sm text-blue-400 hover:text-blue-300">View Details</button>
          </div>
        </motion.div>
        
        {/* Recent activity */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
          className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6"
        >
          <h3 className="text-lg font-semibold text-white mb-6">Recent Activity</h3>
          
          <div className="space-y-5">
            {RECENT_ACTIVITIES.map((activity) => (
              <div key={activity.id} className="flex items-start">
                <div className={`p-2 rounded-lg ${activity.iconBg} ${activity.iconColor} mr-3 mt-0.5`}>
                  {activity.icon}
                </div>
                <div>
                  <p className="text-sm font-medium text-white">{activity.action}</p>
                  <p className="text-xs text-gray-400 mt-0.5">{activity.name}</p>
                  <p className="text-xs text-gray-500 mt-1">{activity.time}</p>
                </div>
              </div>
            ))}
          </div>
          
          <div className="mt-6 pt-5 border-t border-gray-700/30 text-center">
            <button className="text-sm text-blue-400 hover:text-blue-300">View All Activity</button>
          </div>
        </motion.div>
      </div>
      
      {/* Third row */}
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
        className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6"
      >
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-semibold text-white">Recent Updates</h3>
          <div className="flex space-x-2">
            <button className="text-xs bg-blue-500/20 text-blue-400 px-3 py-1 rounded-full">Platform</button>
            <button className="text-xs bg-gray-700/70 text-gray-300 px-3 py-1 rounded-full hover:bg-gray-700">Features</button>
            <button className="text-xs bg-gray-700/70 text-gray-300 px-3 py-1 rounded-full hover:bg-gray-700">Security</button>
          </div>
        </div>
        
        <div className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg p-4 mb-4">
          <div className="flex items-start space-x-4">
            <div className="p-3 rounded-lg bg-blue-500/20 text-blue-400">
              <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <div>
              <h4 className="text-md font-medium text-white">New Patient Dashboard Launched</h4>
              <p className="text-sm text-gray-400 mt-1">
                We've launched a redesigned patient dashboard with improved treatment tracking and appointment management.
              </p>
              <div className="mt-3 flex items-center text-xs text-gray-500">
                <span>Released 2 days ago</span>
                <span className="mx-2">•</span>
                <button className="text-blue-400 hover:text-blue-300">Read Release Notes</button>
              </div>
            </div>
          </div>
        </div>
        
        <div className="bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-lg p-4">
          <div className="flex items-start space-x-4">
            <div className="p-3 rounded-lg bg-green-500/20 text-green-400">
              <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <h4 className="text-md font-medium text-white">Clinical Tools Integration Completed</h4>
              <p className="text-sm text-gray-400 mt-1">
                Major integration with dental imaging systems and intraoral scanners has been completed successfully.
              </p>
              <div className="mt-3 flex items-center text-xs text-gray-500">
                <span>Released 5 days ago</span>
                <span className="mx-2">•</span>
                <button className="text-blue-400 hover:text-blue-300">View Clinical Tools</button>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
} 