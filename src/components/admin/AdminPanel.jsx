import React from 'react';
import { supabase } from '../../lib/supabase';
import { useUser } from '../../contexts/UserContext';
import { useSystemSettings } from '../../lib/hooks/useSystemSettings';

export default function AdminPanel() {
  const { user } = useUser();
  const { maintenanceMode, loading } = useSystemSettings();

  const toggleMaintenanceMode = async () => {
    try {
      const { error } = await supabase
        .from('system_settings')
        .upsert({ 
          id: 1, 
          maintenance_mode: !maintenanceMode,
          updated_by: user.id,
          updated_at: new Date().toISOString()
        });

      if (error) throw error;
    } catch (error) {
      console.error('Error toggling maintenance mode:', error);
    }
  };

  if (loading) {
    return <div className="text-white">Loading...</div>;
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-2xl font-semibold text-white mb-8">Admin Panel</h1>
      
      <div className="bg-white/5 backdrop-blur-lg rounded-lg border border-white/10 p-6">
        <h2 className="text-xl text-white mb-4">Maintenance Mode</h2>
        
        <div className="flex items-center justify-between">
          <div>
            <p className="text-white/80 mb-2">
              {maintenanceMode 
                ? 'Maintenance mode is currently active. The site is not accessible to regular users.'
                : 'Site is currently live and accessible to all users.'
              }
            </p>
            <p className="text-sm text-white/60">
              Last updated: {new Date().toLocaleDateString()}
            </p>
          </div>
          
          <button
            onClick={toggleMaintenanceMode}
            className={`
              px-4 py-2 rounded-lg transition-colors duration-200
              ${maintenanceMode 
                ? 'bg-green-600 hover:bg-green-500' 
                : 'bg-red-600 hover:bg-red-500'
              } text-white
            `}
          >
            {maintenanceMode ? 'Disable Maintenance Mode' : 'Enable Maintenance Mode'}
          </button>
        </div>
      </div>
    </div>
  );
}