import React, { useState } from 'react';
import { Card } from '../../common';
import DatabaseSetup from './DatabaseSetup';

export default function SystemManager() {
  const [activeTab, setActiveTab] = useState('database');
  
  const renderTabContent = () => {
    switch (activeTab) {
      case 'database':
        return <DatabaseSetup />;
      case 'config':
        return (
          <Card>
            <div className="p-6">
              <h3 className="text-lg font-semibold text-white mb-4">System Configuration</h3>
              <p className="text-white/70">Configure system-wide settings and parameters.</p>
              {/* Configuration settings will go here */}
            </div>
          </Card>
        );
      case 'logs':
        return (
          <Card>
            <div className="p-6">
              <h3 className="text-lg font-semibold text-white mb-4">System Logs</h3>
              <p className="text-white/70">View system logs and activity.</p>
              {/* Logs will go here */}
            </div>
          </Card>
        );
      default:
        return <DatabaseSetup />;
    }
  };
  
  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-white">System Settings</h2>
      
      <div className="bg-gray-800 rounded-lg overflow-hidden">
        <div className="flex border-b border-gray-700">
          <button
            className={`px-4 py-3 text-sm font-medium ${activeTab === 'database' ? 'text-blue-400 border-b-2 border-blue-400' : 'text-white/70 hover:text-white'}`}
            onClick={() => setActiveTab('database')}
          >
            Database
          </button>
          <button
            className={`px-4 py-3 text-sm font-medium ${activeTab === 'config' ? 'text-blue-400 border-b-2 border-blue-400' : 'text-white/70 hover:text-white'}`}
            onClick={() => setActiveTab('config')}
          >
            Configuration
          </button>
          <button
            className={`px-4 py-3 text-sm font-medium ${activeTab === 'logs' ? 'text-blue-400 border-b-2 border-blue-400' : 'text-white/70 hover:text-white'}`}
            onClick={() => setActiveTab('logs')}
          >
            Logs
          </button>
        </div>
        
        <div className="p-6">
          {renderTabContent()}
        </div>
      </div>
    </div>
  );
}