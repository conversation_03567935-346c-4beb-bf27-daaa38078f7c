import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card } from '../../common';
import { supabase } from '../../../lib/supabase';
import { checkDatabaseStatus } from '../../../lib/database/initDatabase';

const DatabaseSetup = () => {
  const [dbStatus, setDbStatus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [copied, setCopied] = useState(false);
  const [activeTab, setActiveTab] = useState('status');

  useEffect(() => {
    const checkDb = async () => {
      try {
        setLoading(true);
        const status = await checkDatabaseStatus();
        setDbStatus(status);
      } catch (err) {
        console.error('Error checking database status:', err);
      } finally {
        setLoading(false);
      }
    };
    
    checkDb();
  }, []);

  const refreshStatus = async () => {
    setLoading(true);
    try {
      const status = await checkDatabaseStatus();
      setDbStatus(status);
    } catch (err) {
      console.error('Error refreshing database status:', err);
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    });
  };

  const ndaNotificationsSQL = `-- Create NDA notifications table
CREATE TABLE IF NOT EXISTS public.nda_notifications (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  agreement_id TEXT REFERENCES public.nda_acceptances(agreement_id),
  recipient_emails TEXT NOT NULL,
  notification_type TEXT NOT NULL,
  sent_at TIMESTAMPTZ NOT NULL,
  dentist_name TEXT,
  dentist_email TEXT,
  ip_address TEXT,
  location TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add appropriate indexes
CREATE INDEX IF NOT EXISTS idx_nda_notifications_agreement_id 
  ON public.nda_notifications(agreement_id);
CREATE INDEX IF NOT EXISTS idx_nda_notifications_notification_type 
  ON public.nda_notifications(notification_type);`;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-white mb-4">Database Setup</h2>
        <button 
          onClick={refreshStatus}
          disabled={loading}
          className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm flex items-center"
        >
          {loading ? (
            <svg className="animate-spin h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          ) : (
            <svg className="h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          )}
          {loading ? 'Refreshing...' : 'Refresh Status'}
        </button>
      </div>
      
      <div className="bg-gray-800 rounded-lg overflow-hidden">
        <div className="flex border-b border-gray-700">
          <button
            className={`px-4 py-2 text-sm font-medium ${activeTab === 'status' ? 'text-blue-400 border-b-2 border-blue-400' : 'text-white/70 hover:text-white'}`}
            onClick={() => setActiveTab('status')}
          >
            Status
          </button>
          <button
            className={`px-4 py-2 text-sm font-medium ${activeTab === 'setup' ? 'text-blue-400 border-b-2 border-blue-400' : 'text-white/70 hover:text-white'}`}
            onClick={() => setActiveTab('setup')}
          >
            Setup
          </button>
          <button
            className={`px-4 py-2 text-sm font-medium ${activeTab === 'troubleshooting' ? 'text-blue-400 border-b-2 border-blue-400' : 'text-white/70 hover:text-white'}`}
            onClick={() => setActiveTab('troubleshooting')}
          >
            Troubleshooting
          </button>
        </div>
        
        <div className="p-4">
          {loading ? (
            <div className="flex justify-center py-10">
              <svg className="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </div>
          ) : activeTab === 'status' ? (
            <Card>
              <div className="p-4">
                <h3 className="text-lg font-semibold text-white mb-4">Database Connection Status</h3>
                <div className="flex items-center mb-4">
                  <div className={`w-3 h-3 rounded-full mr-2 ${dbStatus?.connected ? 'bg-green-500' : 'bg-red-500'}`}></div>
                  <span className="text-white">
                    {dbStatus?.connected ? 'Connected to Supabase' : 'Failed to connect to Supabase'}
                  </span>
                </div>
                
                {dbStatus?.errors.length > 0 && dbStatus.errors.some(e => e.component === 'connection') && (
                  <div className="mt-2 p-3 bg-red-900/30 border border-red-700/50 rounded-md">
                    <h4 className="text-red-300 font-medium mb-1">Connection Error</h4>
                    {dbStatus.errors.map((error, i) => (
                      error.component === 'connection' && (
                        <div key={i} className="text-sm text-red-200">
                          <div><strong>Message:</strong> {error.message}</div>
                          {error.code && <div><strong>Code:</strong> {error.code}</div>}
                        </div>
                      )
                    ))}
                  </div>
                )}
                
                {dbStatus?.connected && (
                  <div className="mt-4">
                    <h4 className="text-md font-medium text-white mb-2">Required Tables</h4>
                    <div className="bg-gray-800 rounded-md p-3">
                      <ul className="space-y-1">
                        {dbStatus.tables.map(table => (
                          <li key={table} className="flex items-center">
                            <div className="w-2 h-2 rounded-full mr-2 bg-green-500"></div>
                            <span className="text-gray-300">{table}</span>
                            {table === 'nda_notifications' && (
                              <span className="ml-2 text-xs px-2 py-0.5 bg-green-900 text-green-200 rounded-full">Required</span>
                            )}
                          </li>
                        ))}
                        
                        {dbStatus.missingTables.map(table => (
                          <li key={table} className="flex items-center text-red-400">
                            <div className="w-2 h-2 rounded-full mr-2 bg-red-500"></div>
                            {table} <span className="ml-2 text-xs px-2 py-0.5 bg-red-900 text-red-200 rounded-full">Missing</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                )}
              </div>
            </Card>
          ) : activeTab === 'setup' ? (
            <Card>
              <div className="p-4">
                <h3 className="text-lg font-semibold text-white mb-4">Required Setup</h3>
                
                {dbStatus?.missingTables.includes('nda_notifications') && (
                  <div className="mb-4">
                    <p className="text-white/80 mb-2">
                      The <code className="px-1 py-0.5 bg-gray-800 rounded">nda_notifications</code> table is missing.
                      This table is required for the NDA email notification system to work properly.
                    </p>
                    <p className="text-white/80 mb-4">
                      Please run the following SQL in your Supabase SQL Editor to create it:
                    </p>
                    
                    <div className="relative">
                      <pre className="bg-gray-800 p-4 rounded-md text-sm text-gray-300 overflow-x-auto">
                        {ndaNotificationsSQL}
                      </pre>
                      <button
                        onClick={() => copyToClipboard(ndaNotificationsSQL)}
                        className="absolute top-2 right-2 p-2 bg-gray-700 hover:bg-gray-600 rounded-md text-white text-xs transition-colors"
                      >
                        {copied ? 'Copied!' : 'Copy SQL'}
                      </button>
                    </div>
                  </div>
                )}
                
                <div className="mt-6 space-y-2">
                  <h4 className="text-md font-medium text-white">Instructions</h4>
                  <ol className="list-decimal list-inside text-white/80 space-y-2 pl-2">
                    <li>Go to your <a href="https://app.supabase.io/" target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:underline">Supabase Project Dashboard</a></li>
                    <li>Click on "SQL Editor" in the left sidebar</li>
                    <li>Create a new query or open an existing one</li>
                    <li>Paste the SQL above</li>
                    <li>Click "Run" to execute the query</li>
                    <li>Return to this page and click "Refresh Status" to verify the table was created successfully</li>
                  </ol>
                </div>
              </div>
            </Card>
          ) : (
            <Card>
              <div className="p-4">
                <h3 className="text-lg font-semibold text-white mb-4">Troubleshooting</h3>
                
                <div className="space-y-4">
                  <div>
                    <h4 className="text-md font-medium text-white mb-2">Common Issues</h4>
                    <ul className="list-disc list-inside text-white/80 space-y-2 pl-2">
                      <li>
                        <strong>Connection errors:</strong> Check that your Supabase URL and API key are correctly configured in the environment variables.
                      </li>
                      <li>
                        <strong>Missing tables:</strong> You need to create required tables manually in the Supabase dashboard using SQL commands provided in the Setup tab.
                      </li>
                      <li>
                        <strong>Permission errors:</strong> Make sure your service role or anon key has the correct permissions to access the tables.
                      </li>
                    </ul>
                  </div>
                  
                  {dbStatus?.errors.length > 0 && (
                    <div>
                      <h4 className="text-md font-medium text-red-300 mb-2">Error Details</h4>
                      <div className="bg-gray-800 p-3 rounded-md">
                        {dbStatus.errors.map((error, i) => (
                          <div key={i} className="mb-2 pb-2 border-b border-gray-700 last:border-0 last:pb-0 last:mb-0">
                            <div className="text-gray-300"><strong>Component:</strong> {error.component}</div>
                            <div className="text-gray-300"><strong>Message:</strong> {error.message}</div>
                            {error.code && <div className="text-gray-300"><strong>Code:</strong> {error.code}</div>}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  <div>
                    <h4 className="text-md font-medium text-white mb-2">Still Having Issues?</h4>
                    <p className="text-white/80">
                      If you're still experiencing problems with your database setup, you may need to:
                    </p>
                    <ul className="list-disc list-inside text-white/80 space-y-1 pl-2 mt-2">
                      <li>Check Supabase dashboard for any service issues</li>
                      <li>Verify your database has not reached its size or connection limits</li>
                      <li>Ensure all required environment variables are correctly set</li>
                      <li>Review Supabase logs for any errors related to your project</li>
                    </ul>
                  </div>
                </div>
              </div>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

export default DatabaseSetup; 