import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  PaintBrushIcon,
  CodeBracketIcon,
  PhotoIcon,
  DocumentTextIcon,
  ArrowPathIcon,
  EyeIcon,
  Squares2X2Icon,
  SwatchIcon,
  PlusIcon
} from '@heroicons/react/24/outline';

// Sample design components
const DESIGN_COMPONENTS = [
  {
    id: 'component-1',
    name: 'Hero Section',
    description: 'Main landing section with background imagery and call-to-action',
    image: 'https://images.unsplash.com/photo-1588345921523-c2dcdb7f1dcd?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
    lastModified: '2 days ago',
    status: 'Live'
  },
  {
    id: 'component-2',
    name: 'Treatment Gallery',
    description: 'Showcase of dental treatments with before/after imagery',
    image: 'https://images.unsplash.com/photo-1588345921523-c2dcdb7f1dcd?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
    lastModified: '5 days ago',
    status: 'Live'
  },
  {
    id: 'component-3',
    name: 'Testimonials Carousel',
    description: 'Rotating display of patient testimonials with photos',
    image: 'https://images.unsplash.com/photo-1588345921523-c2dcdb7f1dcd?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
    lastModified: '1 week ago',
    status: 'Live'
  },
  {
    id: 'component-4',
    name: 'Contact Form',
    description: 'Patient inquiry form with appointment scheduling',
    image: 'https://images.unsplash.com/photo-1588345921523-c2dcdb7f1dcd?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
    lastModified: '3 days ago',
    status: 'Draft'
  },
  {
    id: 'component-5',
    name: 'Footer Design',
    description: 'Website footer with contact information and navigation',
    image: 'https://images.unsplash.com/photo-1588345921523-c2dcdb7f1dcd?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
    lastModified: '1 day ago',
    status: 'Live'
  }
];

// Color palette
const COLOR_PALETTE = [
  { name: 'Primary', color: '#3b82f6', hex: '#3b82f6' },
  { name: 'Secondary', color: '#8b5cf6', hex: '#8b5cf6' },
  { name: 'Accent', color: '#10b981', hex: '#10b981' },
  { name: 'Background', color: '#1e293b', hex: '#1e293b' },
  { name: 'Text', color: '#f3f4f6', hex: '#f3f4f6' },
  { name: 'Highlight', color: '#f59e0b', hex: '#f59e0b' }
];

export default function WebsiteDesignManager() {
  const [activeTab, setActiveTab] = useState('components');
  
  return (
    <motion.div 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="space-y-8"
    >
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-white">Website Design</h2>
          <p className="text-gray-400 mt-1">Manage visual elements of the Smilo Dental website</p>
        </div>
        <div className="flex space-x-3">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="px-4 py-2 bg-gray-800/70 text-gray-300 hover:text-white rounded-lg flex items-center space-x-2 border border-gray-700"
          >
            <EyeIcon className="w-5 h-5 mr-2" />
            Preview Site
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg flex items-center shadow-lg shadow-blue-700/30"
          >
            <ArrowPathIcon className="w-5 h-5 mr-2" />
            Publish Changes
          </motion.button>
        </div>
      </div>
      
      <div className="bg-gray-800/50 rounded-xl backdrop-blur-sm border border-gray-700/50 overflow-hidden">
        <div className="border-b border-gray-700">
          <div className="flex">
            <button 
              onClick={() => setActiveTab('components')}
              className={`px-6 py-3 text-sm font-medium ${activeTab === 'components' ? 'text-blue-400 border-b-2 border-blue-400' : 'text-gray-400 hover:text-white'}`}
            >
              <Squares2X2Icon className="w-5 h-5 inline mr-2" />
              Components
            </button>
            <button
              onClick={() => setActiveTab('colors')}
              className={`px-6 py-3 text-sm font-medium ${activeTab === 'colors' ? 'text-blue-400 border-b-2 border-blue-400' : 'text-gray-400 hover:text-white'}`}
            >
              <SwatchIcon className="w-5 h-5 inline mr-2" />
              Color Palette
            </button>
            <button
              onClick={() => setActiveTab('images')}
              className={`px-6 py-3 text-sm font-medium ${activeTab === 'images' ? 'text-blue-400 border-b-2 border-blue-400' : 'text-gray-400 hover:text-white'}`}
            >
              <PhotoIcon className="w-5 h-5 inline mr-2" />
              Images
            </button>
            <button
              onClick={() => setActiveTab('code')}
              className={`px-6 py-3 text-sm font-medium ${activeTab === 'code' ? 'text-blue-400 border-b-2 border-blue-400' : 'text-gray-400 hover:text-white'}`}
            >
              <CodeBracketIcon className="w-5 h-5 inline mr-2" />
              Custom CSS
            </button>
          </div>
        </div>
        
        <div className="p-6">
          {activeTab === 'components' && (
            <div>
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-lg font-semibold text-white">Page Components</h3>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="px-3 py-1.5 bg-blue-600 text-white rounded-lg flex items-center text-sm"
                >
                  <PlusIcon className="w-4 h-4 mr-1" />
                  Add Component
                </motion.button>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {DESIGN_COMPONENTS.map((component, index) => (
                  <motion.div
                    key={component.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="bg-gray-700/30 border border-gray-700 rounded-lg overflow-hidden"
                  >
                    <div className="h-40 overflow-hidden relative">
                      <img 
                        src={component.image} 
                        alt={component.name}
                        className="w-full h-full object-cover"
                      />
                      <div className="absolute top-2 right-2">
                        <span className={`text-xs px-2 py-1 rounded-full ${
                          component.status === 'Live' 
                            ? 'bg-green-500 text-white' 
                            : 'bg-amber-500 text-white'
                        }`}>
                          {component.status}
                        </span>
                      </div>
                    </div>
                    
                    <div className="p-4">
                      <h4 className="text-white font-medium">{component.name}</h4>
                      <p className="text-gray-400 text-sm mt-1">{component.description}</p>
                      <div className="flex justify-between items-center mt-4 pt-3 border-t border-gray-700/50">
                        <span className="text-xs text-gray-500">Updated {component.lastModified}</span>
                        <div className="flex space-x-2">
                          <button className="text-blue-400 hover:text-blue-300">
                            <EyeIcon className="w-4 h-4" />
                          </button>
                          <button className="text-blue-400 hover:text-blue-300">
                            <PaintBrushIcon className="w-4 h-4" />
                          </button>
                          <button className="text-blue-400 hover:text-blue-300">
                            <CodeBracketIcon className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          )}
          
          {activeTab === 'colors' && (
            <div>
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-lg font-semibold text-white">Brand Color Palette</h3>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="px-3 py-1.5 bg-blue-600 text-white rounded-lg flex items-center text-sm"
                >
                  <PlusIcon className="w-4 h-4 mr-1" />
                  Add Color
                </motion.button>
              </div>
              
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                {COLOR_PALETTE.map((color, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="bg-gray-700/30 border border-gray-700 rounded-lg overflow-hidden"
                  >
                    <div 
                      className="h-24 w-full" 
                      style={{ backgroundColor: color.color }}
                    ></div>
                    <div className="p-3">
                      <h4 className="text-sm font-medium text-white">{color.name}</h4>
                      <p className="text-xs text-gray-400 mt-1">{color.hex}</p>
                    </div>
                  </motion.div>
                ))}
              </div>
              
              <div className="mt-8 pt-6 border-t border-gray-700/30">
                <h3 className="text-lg font-semibold text-white mb-4">Theme Preview</h3>
                <div className="bg-gray-700/30 border border-gray-700 rounded-lg p-6">
                  <div className="mb-6">
                    <h4 className="text-lg font-bold text-white mb-3">Heading Example</h4>
                    <p className="text-gray-300 mb-3">This is a paragraph with a <a href="#" className="text-blue-400 hover:underline">hyperlink example</a> that demonstrates how the text will appear on your website.</p>
                    <button className="mr-3 px-4 py-2 bg-blue-600 text-white rounded-lg text-sm">Primary Button</button>
                    <button className="px-4 py-2 bg-gray-800 text-gray-300 border border-gray-700 rounded-lg text-sm">Secondary Button</button>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-3">
                    <div className="bg-gray-800 border border-gray-700 rounded-lg p-4">
                      <h5 className="text-sm font-medium text-white mb-2">Card Title</h5>
                      <p className="text-xs text-gray-400">Card example with the current color scheme applied.</p>
                    </div>
                    <div className="bg-blue-600/20 border border-blue-700/30 rounded-lg p-4">
                      <h5 className="text-sm font-medium text-white mb-2">Highlighted Card</h5>
                      <p className="text-xs text-gray-300">Highlighted card using the primary color.</p>
                    </div>
                    <div className="bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg p-4">
                      <h5 className="text-sm font-medium text-white mb-2">Gradient Card</h5>
                      <p className="text-xs text-gray-100">Card with gradient background.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          {activeTab === 'images' && (
            <div className="text-center py-12">
              <div className="w-24 h-24 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
                <PhotoIcon className="w-12 h-12 text-blue-400" />
              </div>
              
              <h3 className="text-xl font-bold text-white mb-2">Image Gallery Management</h3>
              <p className="text-gray-400 max-w-2xl mx-auto mb-8">
                The dental-specific image management tools are currently being integrated. This section will allow you to upload, organize, and optimize images for your dental practice website.
              </p>
              
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg shadow-lg shadow-blue-700/20"
              >
                Get Early Access
              </motion.button>
            </div>
          )}
          
          {activeTab === 'code' && (
            <div className="text-center py-12">
              <div className="w-24 h-24 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
                <CodeBracketIcon className="w-12 h-12 text-blue-400" />
              </div>
              
              <h3 className="text-xl font-bold text-white mb-2">Custom CSS Editor</h3>
              <p className="text-gray-400 max-w-2xl mx-auto mb-8">
                Advanced CSS customization is coming soon. This feature will allow you to add custom styling to your dental practice website for more personalized design control.
              </p>
              
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg shadow-lg shadow-blue-700/20"
              >
                Request Access
              </motion.button>
            </div>
          )}
        </div>
      </div>
      
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="bg-gray-800/50 rounded-xl backdrop-blur-sm border border-gray-700/50 overflow-hidden p-6"
      >
        <h3 className="text-lg font-semibold text-white mb-4">Recent Design Activity</h3>
        
        <div className="space-y-4">
          <div className="flex items-start">
            <div className="p-2 rounded-lg bg-purple-500/20 text-purple-400 mr-3">
              <PaintBrushIcon className="w-5 h-5" />
            </div>
            <div>
              <p className="text-sm font-medium text-white">Updated Hero Section Colors</p>
              <p className="text-xs text-gray-400 mt-0.5">Changed gradient background to match new brand colors</p>
              <p className="text-xs text-gray-500 mt-1">Today at 2:34 PM</p>
            </div>
          </div>
          
          <div className="flex items-start">
            <div className="p-2 rounded-lg bg-green-500/20 text-green-400 mr-3">
              <DocumentTextIcon className="w-5 h-5" />
            </div>
            <div>
              <p className="text-sm font-medium text-white">Updated Treatment Descriptions</p>
              <p className="text-xs text-gray-400 mt-0.5">Added dental-specific terminology and information</p>
              <p className="text-xs text-gray-500 mt-1">Yesterday at 11:20 AM</p>
            </div>
          </div>
          
          <div className="flex items-start">
            <div className="p-2 rounded-lg bg-blue-500/20 text-blue-400 mr-3">
              <PhotoIcon className="w-5 h-5" />
            </div>
            <div>
              <p className="text-sm font-medium text-white">Added New Patient Case Photos</p>
              <p className="text-xs text-gray-400 mt-0.5">Uploaded before/after treatment images with patient consent</p>
              <p className="text-xs text-gray-500 mt-1">2 days ago</p>
            </div>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
} 