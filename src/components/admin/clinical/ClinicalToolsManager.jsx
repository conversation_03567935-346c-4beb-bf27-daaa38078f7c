import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Cog6ToothIcon, 
  ArrowTopRightOnSquareIcon,
  BeakerIcon,
  DocumentChartBarIcon,
  BoltIcon,
  ComputerDesktopIcon
} from '@heroicons/react/24/outline';

// Sample clinical tools data
const CLINICAL_TOOLS = [
  {
    id: 'tool-1',
    name: 'Treatment Planning AI',
    description: 'AI-powered treatment planning assistant that helps create personalized patient treatment options',
    icon: <BoltIcon className="w-6 h-6" />,
    status: 'Active',
    lastUsed: '2 days ago',
    usageCount: 248
  },
  {
    id: 'tool-2',
    name: 'Imaging Analysis',
    description: 'Advanced image analysis for dental X-rays and 3D scans with automated detection capabilities',
    icon: <ComputerDesktopIcon className="w-6 h-6" />,
    status: 'Active',
    lastUsed: '1 day ago',
    usageCount: 312
  },
  {
    id: 'tool-3',
    name: 'Digital Impression System',
    description: 'Connects with intraoral scanners to create precise digital impressions for restorations',
    icon: <DocumentChartBarIcon className="w-6 h-6" />,
    status: 'Maintenance',
    lastUsed: '1 week ago',
    usageCount: 156
  },
  {
    id: 'tool-4',
    name: 'Occlusion Analyzer',
    description: 'Digital occlusion analysis tool to identify and resolve bite issues before treatment',
    icon: <BeakerIcon className="w-6 h-6" />,
    status: 'Beta',
    lastUsed: '3 days ago',
    usageCount: 87
  },
  {
    id: 'tool-5',
    name: 'Patient Education Visualizer',
    description: 'Interactive 3D models to educate patients about procedures and expected outcomes',
    icon: <ComputerDesktopIcon className="w-6 h-6" />,
    status: 'Active',
    lastUsed: '4 hours ago',
    usageCount: 421
  }
];

// Status color mapping
const STATUS_COLORS = {
  'Active': 'bg-green-500',
  'Maintenance': 'bg-amber-500',
  'Beta': 'bg-blue-500',
  'Deprecated': 'bg-red-500',
  'Development': 'bg-purple-500'
};

export default function ClinicalToolsManager() {
  const [activeTab, setActiveTab] = useState('all');

  // Filter tools based on active tab
  const filteredTools = activeTab === 'all' 
    ? CLINICAL_TOOLS 
    : CLINICAL_TOOLS.filter(tool => {
        if (activeTab === 'active') return tool.status === 'Active';
        if (activeTab === 'development') return ['Beta', 'Development'].includes(tool.status);
        return true;
      });

  return (
    <motion.div 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="space-y-8"
    >
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-white">Clinical Tools</h2>
          <p className="text-gray-400 mt-1">Manage and access dental-specific clinical tools</p>
        </div>
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          className="px-4 py-2 bg-gray-800/70 text-gray-300 hover:text-white rounded-lg flex items-center space-x-2 border border-gray-700"
        >
          <Cog6ToothIcon className="w-5 h-5" />
          <span>Settings</span>
        </motion.button>
      </div>
      
      <div className="bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-lg p-4">
        <div className="flex items-center text-blue-300">
          <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>Clinical tools integration requires compatible hardware and software configurations.</span>
        </div>
      </div>
      
      <div className="bg-gray-800/50 rounded-xl backdrop-blur-sm border border-gray-700/50 overflow-hidden">
        <div className="border-b border-gray-700">
          <div className="flex">
            <button 
              onClick={() => setActiveTab('all')}
              className={`px-6 py-3 text-sm font-medium ${activeTab === 'all' ? 'text-blue-400 border-b-2 border-blue-400' : 'text-gray-400 hover:text-white'}`}
            >
              All Tools
            </button>
            <button
              onClick={() => setActiveTab('active')}
              className={`px-6 py-3 text-sm font-medium ${activeTab === 'active' ? 'text-blue-400 border-b-2 border-blue-400' : 'text-gray-400 hover:text-white'}`}
            >
              Active
            </button>
            <button
              onClick={() => setActiveTab('development')}
              className={`px-6 py-3 text-sm font-medium ${activeTab === 'development' ? 'text-blue-400 border-b-2 border-blue-400' : 'text-gray-400 hover:text-white'}`}
            >
              In Development
            </button>
          </div>
        </div>
        
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {filteredTools.map((tool, index) => (
              <motion.div
                key={tool.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-gray-700/30 rounded-lg border border-gray-700 overflow-hidden"
              >
                <div className="p-5">
                  <div className="flex items-start">
                    <div className="p-3 rounded-lg bg-gray-800/70 mr-4 text-blue-400">
                      {tool.icon}
                    </div>
                    <div className="flex-1">
                      <div className="flex justify-between items-start">
                        <h3 className="text-lg font-semibold text-white">{tool.name}</h3>
                        <span className={`px-2 py-1 text-xs font-semibold rounded-full ${STATUS_COLORS[tool.status]} text-white`}>
                          {tool.status}
                        </span>
                      </div>
                      <p className="text-gray-400 text-sm mt-1">{tool.description}</p>
                    </div>
                  </div>
                </div>
                
                <div className="bg-gray-800/60 px-5 py-3 flex justify-between items-center">
                  <div className="text-xs text-gray-400">
                    <span>Used {tool.usageCount} times</span>
                    <span className="mx-2">•</span>
                    <span>Last used: {tool.lastUsed}</span>
                  </div>
                  
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="text-blue-400 hover:text-blue-300 flex items-center text-sm"
                    disabled={tool.status === 'Maintenance'}
                  >
                    <span className="mr-1">Launch</span>
                    <ArrowTopRightOnSquareIcon className="w-4 h-4" />
                  </motion.button>
                </div>
              </motion.div>
            ))}
          </div>
          
          {filteredTools.length === 0 && (
            <div className="text-center py-12 text-gray-400">
              No clinical tools found in this category.
            </div>
          )}
        </div>
      </div>
      
      <div className="bg-gray-800/50 rounded-xl backdrop-blur-sm border border-gray-700/50 overflow-hidden p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Integration Status</h3>
        
        <div className="space-y-4">
          <div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm text-gray-300">Digital Imaging Systems</span>
              <span className="text-sm font-semibold text-green-400">Connected</span>
            </div>
            <div className="w-full bg-gray-700/30 h-1.5 rounded-full overflow-hidden">
              <div className="bg-green-500 h-full rounded-full" style={{width: '100%'}}></div>
            </div>
          </div>
          
          <div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm text-gray-300">Intraoral Scanners</span>
              <span className="text-sm font-semibold text-green-400">Connected</span>
            </div>
            <div className="w-full bg-gray-700/30 h-1.5 rounded-full overflow-hidden">
              <div className="bg-green-500 h-full rounded-full" style={{width: '100%'}}></div>
            </div>
          </div>
          
          <div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm text-gray-300">Practice Management System</span>
              <span className="text-sm font-semibold text-amber-400">Partial</span>
            </div>
            <div className="w-full bg-gray-700/30 h-1.5 rounded-full overflow-hidden">
              <div className="bg-amber-500 h-full rounded-full" style={{width: '65%'}}></div>
            </div>
          </div>
          
          <div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm text-gray-300">Treatment Planning Software</span>
              <span className="text-sm font-semibold text-blue-400">In Progress</span>
            </div>
            <div className="w-full bg-gray-700/30 h-1.5 rounded-full overflow-hidden">
              <div className="bg-blue-500 h-full rounded-full" style={{width: '40%'}}></div>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
} 