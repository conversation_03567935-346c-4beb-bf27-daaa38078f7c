import React, { useState, useEffect } from 'react';
import { checkApiKey } from '../../lib/utils/apiKeyTest';
import { getDentalAdvice } from '../../lib/api/openai';
import { config } from '../../lib/config';

export default function ChatTest() {
  const [apiKeyStatus, setApiKeyStatus] = useState(null);
  const [testMessage, setTestMessage] = useState('');
  const [response, setResponse] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [logs, setLogs] = useState([]);

  // Add log entry
  const addLog = (message, type = 'info') => {
    setLogs(prev => [...prev, { message, type, timestamp: new Date().toISOString() }]);
    console.log(`[ChatTest ${type}]:`, message);
  };

  useEffect(() => {
    // Check API key on component mount
    try {
      addLog('Checking API key...');
      const status = checkApiKey();
      setApiKeyStatus(status);
      addLog(`API key status: ${status?.isValid ? 'Valid' : 'Invalid'} - ${status?.message}`);
      
      // Check OpenAI configuration
      addLog(`OpenAI API Key from config: ${config.openai.apiKey ? 'Present' : 'Missing'}`);
    } catch (err) {
      addLog(`Error checking API key: ${err.message}`, 'error');
      setError(err.message);
    }
  }, []);

  const handleTestChat = async () => {
    setLoading(true);
    setError(null);
    setResponse('');
    setLogs([]);
    
    try {
      const testQuestion = "What is a dental cavity?";
      addLog(`Sending test question: "${testQuestion}"`);
      
      // Verify API key one more time
      if (!config.openai.apiKey) {
        throw new Error('API key is missing. Please check your environment configuration.');
      }
      
      addLog('Calling getDentalAdvice function...');
      const result = await getDentalAdvice(testQuestion);
      
      setResponse(result);
      addLog('Test completed successfully!');
      setTestMessage('Test completed successfully!');
    } catch (err) {
      console.error('Chat test error:', err);
      addLog(`Test failed: ${err.message}`, 'error');
      setError(err.message || 'An error occurred during the test');
      setTestMessage('Test failed. See error details below.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10">
      <h2 className="text-xl font-bold text-white mb-4">AI Chat Functionality Test</h2>
      
      <div className="mb-6">
        <h3 className="text-lg font-medium text-white mb-2">API Key Status</h3>
        {apiKeyStatus ? (
          <div className={`p-3 rounded-lg ${apiKeyStatus.isValid ? 'bg-green-500/20 text-green-300' : 'bg-red-500/20 text-red-300'}`}>
            {apiKeyStatus.isValid ? '✅ ' : '❌ '}
            {apiKeyStatus.message}
          </div>
        ) : (
          <div className="p-3 rounded-lg bg-gray-500/20 text-gray-300">
            Checking API key...
          </div>
        )}
      </div>
      
      <div className="mb-6">
        <button
          onClick={handleTestChat}
          disabled={loading}
          className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? 'Testing...' : 'Test Chat Functionality'}
        </button>
        
        {testMessage && (
          <p className={`mt-2 ${error ? 'text-red-300' : 'text-green-300'}`}>
            {testMessage}
          </p>
        )}
        
        {error && (
          <div className="mt-2 p-3 rounded-lg bg-red-500/20 text-red-300">
            <strong>Error:</strong> {error}
          </div>
        )}
      </div>
      
      {/* Debug logs */}
      <div className="mb-6">
        <h3 className="text-lg font-medium text-white mb-2">Debug Logs</h3>
        <div className="bg-gray-900/50 p-3 rounded-lg max-h-32 overflow-y-auto">
          {logs.length === 0 ? (
            <p className="text-gray-400">No logs yet. Click "Test Chat Functionality" to begin.</p>
          ) : (
            <div className="space-y-1">
              {logs.map((log, index) => (
                <div key={index} className={`font-mono text-xs ${log.type === 'error' ? 'text-red-300' : 'text-blue-300'}`}>
                  [{new Date(log.timestamp).toLocaleTimeString()}] {log.message}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
      
      {response && (
        <div className="mt-4">
          <h3 className="text-lg font-medium text-white mb-2">AI Response</h3>
          <div className="p-4 bg-indigo-900/30 rounded-xl text-white prose prose-sm prose-invert max-w-none">
            {response}
          </div>
        </div>
      )}
    </div>
  );
}
