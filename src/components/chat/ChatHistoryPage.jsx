import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { format } from 'date-fns';
import { useUser } from '../../contexts/UserContext';
import { useChatHistory } from '../../lib/hooks/useChatHistory';
import { Card } from '../common';
import Button from '../common/Button';
import AuthPrompt from '../auth/AuthPrompt';
import ChatFolderList from './ChatFolderList';

const DATE_RANGES = [
  { label: 'Last 7 Days', days: 7 },
  { label: 'Last 30 Days', days: 30 },
  { label: 'All Time', days: null }
];

export default function ChatHistoryPage() {
  const { user } = useUser();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFolder, setSelectedFolder] = useState(null);
  const [selectedRange, setSelectedRange] = useState(DATE_RANGES[0]);
  const [selectedType, setSelectedType] = useState('all');
  const { history, loading, error, loadHistory } = useChatHistory(user?.id);
  const [filteredHistory, setFilteredHistory] = useState([]);
  const [showNewFolderModal, setShowNewFolderModal] = useState(false);

  useEffect(() => {
    if (user) {
      loadHistory();
    }
  }, [user, loadHistory]);

  useEffect(() => {
    if (!history) return;

    let filtered = [...history];

    // Apply date range filter
    if (selectedRange.days) {
      const cutoff = new Date();
      cutoff.setDate(cutoff.getDate() - selectedRange.days);
      filtered = filtered.filter(item => new Date(item.created_at) >= cutoff);
    }

    // Apply type filter
    if (selectedType !== 'all') {
      filtered = filtered.filter(item => item.message_type === selectedType);
    }

    // Apply search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(item =>
        item.content.toLowerCase().includes(term)
      );
    }

    setFilteredHistory(filtered);
  }, [history, selectedRange, selectedType, searchTerm]);

  if (!user) {
    return <AuthPrompt message="Sign in to view your chat history" />;
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <div className="grid grid-cols-12 gap-6">
        {/* Folder Sidebar */}
        <div className="col-span-3">
          <Card>
            <div className="p-4 border-b border-white/10 flex justify-between items-center">
              <h2 className="text-lg font-semibold text-white">Folders</h2>
              <button
                onClick={() => setShowNewFolderModal(true)}
                className="p-2 text-white/60 hover:text-white rounded-lg hover:bg-white/5"
              >
                <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
              </button>
            </div>
            <ChatFolderList
              selectedFolder={selectedFolder}
              onSelectFolder={setSelectedFolder}
            />
          </Card>
        </div>

        {/* Main Content */}
        <div className="col-span-9">
          <Card>
            {/* Header Section */}
            <div className="p-6 border-b border-white/10">
              <div className="flex justify-between items-start mb-6">
                <div>
                  <h1 className="text-2xl font-bold text-white">Chat History</h1>
                  <p className="text-white/60">
                    {selectedFolder ? `Folder: ${selectedFolder.name}` : `Total Interactions: ${history.length}`}
                  </p>
                </div>
                <Link to="/chat">
                  <Button variant="primary">
                    New Chat
                  </Button>
                </Link>
              </div>

              {/* Filters */}
              <div className="grid gap-4 md:grid-cols-3">
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="Search conversations..."
                  className="bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white placeholder-white/50"
                />

                <select
                  value={selectedRange.days}
                  onChange={(e) => setSelectedRange(DATE_RANGES.find(r => r.days === Number(e.target.value)))}
                  className="bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white"
                >
                  {DATE_RANGES.map(range => (
                    <option key={range.label} value={range.days || ''}>
                      {range.label}
                    </option>
                  ))}
                </select>

                <select
                  value={selectedType}
                  onChange={(e) => setSelectedType(e.target.value)}
                  className="bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white"
                >
                  <option value="all">All Types</option>
                  <option value="text">Text Only</option>
                  <option value="image">With Images</option>
                </select>
              </div>
            </div>

            {/* Chat History List */}
            <div className="divide-y divide-white/10">
              {loading ? (
                <div className="flex justify-center items-center p-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                </div>
              ) : filteredHistory.length === 0 ? (
                <div className="text-center py-12">
                  <p className="text-white/60">No chat history found</p>
                </div>
              ) : (
                filteredHistory.map((item, index) => (
                  <div key={index} className="p-6 hover:bg-white/5 transition-colors">
                    <div className="flex justify-between items-start mb-4">
                      <div className="flex items-center gap-4">
                        <span className="text-white/60">
                          {format(new Date(item.created_at), 'MMM d, yyyy h:mm a')}
                        </span>
                        {item.metadata?.has_image && (
                          <span className="px-2 py-1 bg-blue-500/10 text-blue-400 text-xs rounded-full">
                            Image Analysis
                          </span>
                        )}
                      </div>
                      <div className="flex gap-2">
                        <button
                          onClick={() => window.print()}
                          className="p-2 text-white/60 hover:text-white rounded-lg hover:bg-white/5"
                        >
                          <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
                          </svg>
                        </button>
                        <button
                          onClick={() => handleDelete(item.id)}
                          className="p-2 text-red-400 hover:text-red-300 rounded-lg hover:bg-white/5"
                        >
                          <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div className="bg-white/5 rounded-lg p-4">
                        <div className="text-sm text-white/60 mb-2">You asked:</div>
                        <p className="text-white">{item.content}</p>
                      </div>

                      {item.response && (
                        <div className="bg-blue-500/10 rounded-lg p-4">
                          <div className="text-sm text-blue-400 mb-2">AI Response:</div>
                          <p className="text-white">{item.response}</p>
                        </div>
                      )}

                      {item.metadata?.has_image && (
                        <div className="mt-4">
                          <img
                            src={item.metadata.image_url}
                            alt="Uploaded dental image"
                            className="max-w-xs rounded-lg"
                          />
                        </div>
                      )}
                    </div>
                  </div>
                ))
              )}
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}