import React, { useState, useEffect } from 'react';
import { useChatFolders } from '../../lib/hooks/useChatFolders';
import Button from '../common/Button';

export default function ChatFolderList({ selectedFolder, onSelectFolder }) {
  const { 
    folders, 
    loading, 
    error,
    createFolder,
    updateFolder,
    deleteFolder 
  } = useChatFolders();

  const [editingFolder, setEditingFolder] = useState(null);
  const [newFolderName, setNewFolderName] = useState('');

  const handleCreateFolder = async () => {
    if (!newFolderName.trim()) return;
    try {
      await createFolder(newFolderName);
      setNewFolderName('');
    } catch (err) {
      console.error('Error creating folder:', err);
    }
  };

  const handleUpdateFolder = async (folderId, newName) => {
    try {
      await updateFolder(folderId, newName);
      setEditingFolder(null);
    } catch (err) {
      console.error('Error updating folder:', err);
    }
  };

  const handleDeleteFolder = async (folderId) => {
    if (!confirm('Are you sure you want to delete this folder?')) return;
    try {
      await deleteFolder(folderId);
      if (selectedFolder?.id === folderId) {
        onSelectFolder(null);
      }
    } catch (err) {
      console.error('Error deleting folder:', err);
    }
  };

  if (loading) {
    return (
      <div className="p-4 text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
      </div>
    );
  }

  return (
    <div className="space-y-2 p-4">
      {/* New Folder Input */}
      <div className="flex gap-2 mb-4">
        <input
          type="text"
          value={newFolderName}
          onChange={(e) => setNewFolderName(e.target.value)}
          placeholder="New folder name..."
          className="flex-1 bg-white/5 border border-white/10 rounded-lg px-3 py-2 text-white text-sm"
        />
        <Button
          onClick={handleCreateFolder}
          disabled={!newFolderName.trim()}
          className="px-3 py-2"
        >
          Add
        </Button>
      </div>

      {/* Folder List */}
      <div className="space-y-1">
        <button
          onClick={() => onSelectFolder(null)}
          className={`w-full px-3 py-2 rounded-lg text-left flex items-center gap-2 ${
            !selectedFolder
              ? 'bg-blue-500 text-white'
              : 'text-white/80 hover:bg-white/5'
          }`}
        >
          <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
          </svg>
          All Chats
        </button>

        {folders.map(folder => (
          <div
            key={folder.id}
            className={`group rounded-lg ${
              selectedFolder?.id === folder.id
                ? 'bg-blue-500'
                : 'hover:bg-white/5'
            }`}
          >
            {editingFolder?.id === folder.id ? (
              <div className="flex items-center gap-2 p-2">
                <input
                  type="text"
                  value={editingFolder.name}
                  onChange={(e) => setEditingFolder(prev => ({ ...prev, name: e.target.value }))}
                  className="flex-1 bg-white/10 border border-white/20 rounded px-2 py-1 text-white text-sm"
                  autoFocus
                />
                <button
                  onClick={() => handleUpdateFolder(folder.id, editingFolder.name)}
                  className="p-1 text-green-400 hover:text-green-300"
                >
                  <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </button>
                <button
                  onClick={() => setEditingFolder(null)}
                  className="p-1 text-red-400 hover:text-red-300"
                >
                  <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            ) : (
              <div className="flex items-center">
                <button
                  onClick={() => onSelectFolder(folder)}
                  className="flex-1 px-3 py-2 text-left flex items-center gap-2"
                >
                  <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
                  </svg>
                  <span className={selectedFolder?.id === folder.id ? 'text-white' : 'text-white/80'}>
                    {folder.name}
                  </span>
                </button>
                <div className="opacity-0 group-hover:opacity-100 flex pr-2">
                  <button
                    onClick={() => setEditingFolder(folder)}
                    className="p-1 text-white/60 hover:text-white"
                  >
                    <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                  </button>
                  <button
                    onClick={() => handleDeleteFolder(folder.id)}
                    className="p-1 text-red-400 hover:text-red-300"
                  >
                    <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}