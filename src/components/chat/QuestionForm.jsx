import React, { useState, useRef, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { MicrophoneIcon, StopIcon, PaperAirplaneIcon } from '@heroicons/react/24/outline';

const QuestionForm = ({ onSubmit }) => {
  const [question, setQuestion] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [mediaRecorder, setMediaRecorder] = useState(null);
  const [analysisResults, setAnalysisResults] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const timerRef = useRef(null);
  const chunksRef = useRef([]);
  const audioContextRef = useRef(null);
  const microphoneStreamRef = useRef(null);

  useEffect(() => {
    // Cleanup function to handle component unmount
    return () => {
      stopRecording();
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
      if (microphoneStreamRef.current) {
        microphoneStreamRef.current.getTracks().forEach(track => track.stop());
      }
      if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
        audioContextRef.current.close();
      }
    };
  }, []);

  const startRecording = async () => {
    chunksRef.current = [];
    setRecordingTime(0);
    setIsProcessing(false);
    setAnalysisResults(null);

    try {
      console.log('Requesting microphone permission...');
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      microphoneStreamRef.current = stream;
      
      console.log('Microphone permission granted, creating audio context...');
      if (!audioContextRef.current) {
        audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();
      }

      // Determine the correct MIME type
      const mimeType = getMimeType();
      console.log(`Using MIME type: ${mimeType}`);

      const recorder = new MediaRecorder(stream, { mimeType });
      setMediaRecorder(recorder);

      recorder.addEventListener('dataavailable', (event) => {
        console.log('Data chunk available', event.data.size);
        if (event.data.size > 0) {
          chunksRef.current.push(event.data);
        }
      });

      recorder.addEventListener('stop', () => {
        console.log('Recorder stopped, processing data...');
        processAudioData();
      });

      recorder.addEventListener('error', (event) => {
        console.error('Recorder error:', event.error);
        toast.error(`Recording error: ${event.error.message || 'Unknown error'}`);
        cleanupRecording();
      });

      console.log('Starting recorder...');
      recorder.start();
      setIsRecording(true);

      // Start timer
      timerRef.current = setInterval(() => {
        setRecordingTime((prevTime) => prevTime + 1);
      }, 1000);

      console.log('Recording started successfully');
    } catch (error) {
      console.error('Error starting recording:', error);
      toast.error(`Couldn't access microphone: ${error.message}`);
      cleanupRecording();
    }
  };

  const stopRecording = () => {
    console.log('Stopping recording...');
    if (mediaRecorder && mediaRecorder.state !== 'inactive') {
      try {
        mediaRecorder.stop();
        console.log('MediaRecorder stopped');
      } catch (error) {
        console.error('Error stopping recorder:', error);
      }
    }

    cleanupRecording();
  };

  const cleanupRecording = () => {
    console.log('Cleaning up recording resources...');
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }

    if (microphoneStreamRef.current) {
      microphoneStreamRef.current.getTracks().forEach(track => {
        try {
          track.stop();
          console.log('Microphone track stopped');
        } catch (error) {
          console.error('Error stopping track:', error);
        }
      });
      microphoneStreamRef.current = null;
    }

    setIsRecording(false);
  };

  const processAudioData = async () => {
    if (chunksRef.current.length === 0) {
      console.error('No audio data available');
      toast.error('No audio data recorded');
      return;
    }

    setIsProcessing(true);
    console.log(`Processing ${chunksRef.current.length} audio chunks...`);

    try {
      // Create blob from chunks
      const audioBlob = new Blob(chunksRef.current, { type: getMimeType() });
      console.log(`Audio blob created, size: ${audioBlob.size} bytes`);

      if (audioBlob.size < 100) {
        toast.error('Audio recording too short or empty');
        setIsProcessing(false);
        return;
      }

      await handleAudioData(audioBlob);
    } catch (error) {
      console.error('Error processing audio:', error);
      toast.error(`Error processing audio: ${error.message}`);
    } finally {
      setIsProcessing(false);
    }
  };

  const getMimeType = () => {
    const types = [
      'audio/webm',
      'audio/webm;codecs=opus',
      'audio/ogg;codecs=opus',
      'audio/mp4'
    ];

    for (const type of types) {
      if (MediaRecorder.isTypeSupported(type)) {
        return type;
      }
    }

    console.warn('No preferred MIME type supported, using default');
    return '';  // Let the browser choose
  };

  const handleAudioData = async (audioBlob) => {
    console.log('Preparing to send audio for processing...');
    const formData = new FormData();
    formData.append('audio', audioBlob, 'recording.webm');

    try {
      console.log('Sending audio to speech-to-text API...');
      const response = await fetch('/api/speech-to-text', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('API response error:', response.status, errorData);
        throw new Error(errorData.error || `Server error: ${response.status}`);
      }

      const data = await response.json();
      console.log('Received response:', data);
      
      if (data.text) {
        setQuestion(data.text);
        toast.success('Speech converted to text');
      } else {
        toast.warning('No speech detected');
      }
      
      if (data.analysis) {
        setAnalysisResults(data.analysis);
        console.log('Analysis results:', data.analysis);
      }
    } catch (error) {
      console.error('Error in speech-to-text processing:', error);
      toast.error(`Failed to process speech: ${error.message}`);
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto">
      {analysisResults && (
        <div className="mb-4 p-4 bg-white/10 backdrop-blur-lg rounded-lg shadow-lg animate-fadeIn">
          <h3 className="text-lg font-semibold mb-2 text-blue-100">Voice Analysis Results</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <AnalysisCard
              title="Speech Clarity"
              score={analysisResults.clarity.score}
              issues={analysisResults.clarity.issues}
              recommendation={analysisResults.clarity.recommendation}
            />
            <AnalysisCard
              title="Dry Mouth"
              score={analysisResults.dryMouth.score}
              severity={analysisResults.dryMouth.severity}
              recommendation={analysisResults.dryMouth.recommendation}
            />
            <AnalysisCard
              title="Jaw Alignment"
              score={analysisResults.jawAlignment.score}
              issues={analysisResults.jawAlignment.issues}
              recommendation={analysisResults.jawAlignment.recommendation}
            />
          </div>
          <div className="mt-4 p-3 rounded bg-white/5">
            <h4 className="font-medium text-blue-100">Overall Health: {analysisResults.overallHealth.status}</h4>
            <p className="text-sm text-gray-300 mt-1">{analysisResults.overallHealth.recommendation}</p>
          </div>
        </div>
      )}
      
      <div className="flex items-center gap-2">
        <input
          type="text"
          value={question}
          onChange={(e) => setQuestion(e.target.value)}
          placeholder="Ask me anything about dental health..."
          className="flex-1 p-3 rounded-lg bg-white/10 backdrop-blur-lg border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        <button
          onClick={isRecording ? stopRecording : startRecording}
          disabled={isProcessing}
          className={`p-3 rounded-lg transition-colors duration-200 ${
            isProcessing 
              ? 'bg-gray-500 cursor-not-allowed' 
              : isRecording
                ? 'bg-red-500 hover:bg-red-600 animate-pulse'
                : 'bg-blue-500 hover:bg-blue-600'
          } text-white`}
          aria-label={isRecording ? "Stop recording" : "Start recording"}
        >
          {isProcessing ? (
            <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin" />
          ) : isRecording ? (
            <>
              <span className="mr-2">{recordingTime}s</span>
              <StopIcon className="w-6 h-6" />
            </>
          ) : (
            <MicrophoneIcon className="w-6 h-6" />
          )}
        </button>
        <button
          onClick={() => {
            onSubmit(question);
            setQuestion('');
            setAnalysisResults(null);
          }}
          disabled={!question.trim() || isProcessing}
          className="p-3 rounded-lg bg-gradient-to-r from-blue-500 to-purple-600 text-white disabled:opacity-50 disabled:cursor-not-allowed hover:from-blue-600 hover:to-purple-700 transition-all duration-200"
          aria-label="Send message"
        >
          <PaperAirplaneIcon className="w-6 h-6" />
        </button>
      </div>
    </div>
  );
};

const AnalysisCard = ({ title, score, issues, recommendation, severity }) => {
  const scorePercent = Math.round(score * 100);
  const scoreColor = 
    scorePercent >= 80 ? 'text-green-400' : 
    scorePercent >= 60 ? 'text-yellow-400' : 
    'text-red-400';
  
  return (
    <div className="p-3 bg-white/5 rounded-lg transition-all duration-300 hover:bg-white/10">
      <h4 className="font-medium text-blue-100">{title}</h4>
      <div className={`text-lg font-bold ${scoreColor} flex items-center gap-2`}>
        <div className="relative h-2 w-full bg-gray-700 rounded-full overflow-hidden">
          <div 
            className={`absolute top-0 left-0 h-full rounded-full ${
              scorePercent >= 80 ? 'bg-green-400' : 
              scorePercent >= 60 ? 'bg-yellow-400' : 
              'bg-red-400'
            }`}
            style={{ width: `${scorePercent}%` }}
          />
        </div>
        <span>{scorePercent}%</span>
      </div>
      {severity && (
        <p className="text-sm text-gray-300 mt-1">Severity: {severity}</p>
      )}
      {issues && issues.length > 0 && (
        <ul className="text-sm text-gray-300 mt-1 list-disc list-inside">
          {issues.map((issue, index) => (
            <li key={index}>{issue}</li>
          ))}
        </ul>
      )}
      <p className="text-sm text-gray-300 mt-2">{recommendation}</p>
    </div>
  );
};

export default QuestionForm; 