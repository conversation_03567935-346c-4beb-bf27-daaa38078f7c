import React from 'react';
import ChatInterface from '../ChatInterface';
import { useUser } from '../../contexts/UserContext';
import { Link } from 'react-router-dom';

function ChatPage() {
  const { user } = useUser();
  return (
    <div className="min-h-screen bg-gradient-to-b from-indigo-900 via-blue-950 to-slate-900 py-16">
      {/* Enhanced animated background elements */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-900/20 via-slate-900 to-indigo-900/20" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_top_right,rgba(79,70,229,0.15),transparent_40%)]" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_bottom_left,rgba(99,102,241,0.15),transparent_40%)]" />
        
        {/* Floating orbs for visual interest */}
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-indigo-500/5 rounded-full blur-3xl animate-float-slow"></div>
        <div className="absolute bottom-1/3 right-1/5 w-96 h-96 bg-blue-400/5 rounded-full blur-3xl animate-float-medium"></div>
        <div className="absolute top-2/3 left-2/3 w-48 h-48 bg-purple-500/5 rounded-full blur-3xl animate-float-fast"></div>
        
        {/* Subtle grid overlay */}
        <div className="absolute inset-0 bg-grid-pattern opacity-[0.03]"></div>
      </div>
      
      <div className="relative max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header area with glass morphism */}
        <div className="text-center mb-12 bg-white/5 backdrop-blur-lg rounded-2xl border border-white/10 p-8 shadow-xl">
          <h1 className="text-5xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-indigo-300 via-blue-300 to-purple-300 mb-4">
            SMILO Dental Assistant
          </h1>
          <p className="text-xl text-blue-100/90 max-w-2xl mx-auto">
            Ask me anything about dental health, procedures, or oral care
          </p>
        </div>

        {/* Main chat interface with enhanced styling */}
        <div className="bg-white/[0.07] backdrop-blur-xl rounded-2xl border border-white/10 hover:border-indigo-400/30 transition-all duration-300 p-8 shadow-2xl relative overflow-hidden group">
          {/* Decorative glowing elements */}
          <div className="absolute top-0 right-0 w-64 h-64 bg-indigo-500/10 rounded-full blur-3xl -translate-y-1/2 translate-x-1/2 group-hover:bg-indigo-500/20 transition-all duration-700"></div>
          <div className="absolute bottom-0 left-0 w-64 h-64 bg-blue-500/10 rounded-full blur-3xl translate-y-1/2 -translate-x-1/2 group-hover:bg-blue-500/20 transition-all duration-700"></div>
          
          {/* Chat interface component */}
          <ChatInterface />
        </div>

        {/* Resources section with redesigned cards */}
        <div className="mt-16 space-y-10">
          {/* Daily Dental Care Section */}
          <section>
            <div className="flex items-center mb-4">
              <span className="h-px flex-1 bg-gradient-to-r from-transparent via-indigo-500/30 to-transparent"></span>
              <h2 className="text-xl font-bold text-white px-4">Daily Dental Care</h2>
              <span className="h-px flex-1 bg-gradient-to-r from-transparent via-indigo-500/30 to-transparent"></span>
            </div>
            <p className="text-white/70 text-sm text-center mb-6">Essential resources for maintaining optimal oral health</p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-5">
              {[
                {title: "Brushing Techniques", link: "/resources/brushing"},
                {title: "Flossing Essentials", link: "/resources/flossing"},
                {title: "Oral Health Basics", link: "/resources/oral-health"}
              ].map((item, index) => (
                <div key={index} className="group bg-white/[0.06] backdrop-blur-md rounded-xl border border-white/10 hover:border-indigo-400/50 transition-all duration-300 p-5 relative overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-br from-indigo-600/5 to-purple-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <h4 className="text-white font-medium mb-3 relative z-10">{item.title}</h4>
                  <Link to={item.link} className="text-indigo-400 hover:text-indigo-300 text-sm flex items-center relative z-10 group-hover:translate-x-1 transition-transform duration-300">
                    Read Article 
                    <svg className="w-3.5 h-3.5 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                  </Link>
                </div>
              ))}
            </div>
          </section>

          {/* Emergency Care Section */}
          <section>
            <div className="flex items-center mb-4">
              <span className="h-px flex-1 bg-gradient-to-r from-transparent via-indigo-500/30 to-transparent"></span>
              <h2 className="text-xl font-bold text-white px-4">Emergency Care</h2>
              <span className="h-px flex-1 bg-gradient-to-r from-transparent via-indigo-500/30 to-transparent"></span>
            </div>
            <p className="text-white/70 text-sm text-center mb-6">Resources for dental emergencies and urgent care</p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-5">
              {[
                {title: "Emergency Dental Care", link: "/resources/emergency-dental"},
                {title: "Find Low-Cost Dental Care", link: "/resources/find-care"},
                {title: "Emergency Care Guidelines", link: "/resources/emergency-guide"}
              ].map((item, index) => (
                <div key={index} className="group bg-white/[0.06] backdrop-blur-md rounded-xl border border-white/10 hover:border-indigo-400/50 transition-all duration-300 p-5 relative overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-br from-indigo-600/5 to-purple-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <h4 className="text-white font-medium mb-3 relative z-10">{item.title}</h4>
                  <Link to={item.link} className="text-indigo-400 hover:text-indigo-300 text-sm flex items-center relative z-10 group-hover:translate-x-1 transition-transform duration-300">
                    Read Article 
                    <svg className="w-3.5 h-3.5 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                  </Link>
                </div>
              ))}
            </div>
          </section>

          {/* Affordable Care Section */}
          <section>
            <div className="flex items-center mb-4">
              <span className="h-px flex-1 bg-gradient-to-r from-transparent via-indigo-500/30 to-transparent"></span>
              <h2 className="text-xl font-bold text-white px-4">Affordable Care</h2>
              <span className="h-px flex-1 bg-gradient-to-r from-transparent via-indigo-500/30 to-transparent"></span>
            </div>
            <p className="text-white/70 text-sm text-center mb-6">Find affordable dental care options near you</p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-5">
              {[
                {title: "Dental School Clinics", link: "/affordable-care", isNew: false},
                {title: "Community Health Centers", link: "/affordable-care", isNew: false},
                {title: "Care Reviews & Ratings", link: "/affordable-care", isNew: false}
              ].map((item, index) => (
                <div key={index} className="group bg-white/[0.06] backdrop-blur-md rounded-xl border border-white/10 hover:border-indigo-400/50 transition-all duration-300 p-5 relative overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-br from-indigo-600/5 to-purple-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <h4 className="text-white font-medium mb-3 relative z-10">{item.title}</h4>
                  <Link to={item.link} className="text-indigo-400 hover:text-indigo-300 text-sm flex items-center relative z-10 group-hover:translate-x-1 transition-transform duration-300">
                    Find Options
                    <svg className="w-3.5 h-3.5 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                  </Link>
                </div>
              ))}
            </div>
          </section>
        </div>
      </div>
    </div>
  );
}

export default ChatPage;