import React, { useState, useEffect } from 'react';
import Logo from './common/Logo';
import { isOnline } from '../lib/utils/network';

export default function MaintenanceMode({ offline }) {
  const [isOffline, setIsOffline] = useState(!isOnline());

  useEffect(() => {
    const handleOnlineStatus = () => setIsOffline(!isOnline());
    window.addEventListener('online', handleOnlineStatus);
    window.addEventListener('offline', handleOnlineStatus);
    return () => {
      window.removeEventListener('online', handleOnlineStatus);
      window.removeEventListener('offline', handleOnlineStatus);
    };
  }, []);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-b from-gray-900 via-gray-800 to-gray-900">
      <div className="max-w-md w-full p-8 text-center">
        {/* Logo with glow effect */}
        <div className="relative mb-8 inline-block">
          <div className="absolute -inset-2 bg-gradient-to-r from-blue-500/20 to-cyan-500/20 blur-xl animate-pulse rounded-full" />
          <img
            src="/images/smilo-logo copy 2.jpg"
            alt="Smilo Logo"
            className="h-32 w-32 object-contain relative mx-auto"
          />
        </div>
        
        <h1 className="text-3xl font-semibold text-white mb-4">
          {isOffline ? 'You\'re Offline 📡' : 'Taking a Quick Break! 🦷✨'}
        </h1>
        
        <p className="text-white/80 mb-8 text-lg">
          {isOffline 
            ? 'Please check your internet connection. We\'ll reconnect automatically when you\'re back online.'
            : 'We\'re making Smilo even better for you! Our dental AI assistant is getting a check-up and will be back with an even brighter smile.'
          }
        </p>

        <div className="bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10">
          <div className="flex items-center justify-center mb-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          </div>
          <p className="text-white/60">
            {isOffline ? 'Waiting for connection...' : 'Expected to return shortly'}
          </p>
        </div>
      </div>
    </div>
  );
}