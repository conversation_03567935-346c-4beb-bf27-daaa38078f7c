import React, { useState, useEffect, useRef } from 'react';
import ReactMarkdown from 'react-markdown';
import { motion } from 'framer-motion';

export default function ResponseDisplay({ response, typingSpeed = 25 }) {
  const [displayedResponse, setDisplayedResponse] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [showFullResponse, setShowFullResponse] = useState(false);
  const responseRef = useRef('');
  const typingRef = useRef(null);

  // Clean up the response formatting
  const cleanResponse = (text) => {
    if (!text) return '';

    // Minimal formatting to maintain consistency with standard AI chat models
    return text
      // Convert bullet points to proper markdown list items
      .replace(/\n\s*•\s+([^\n]+)/g, '\n- $1')
      // Ensure proper spacing for headings
      .replace(/\n#+\s*([^\n]+)\n(?!\n)/g, '\n# $1\n\n')
      .trim();
  };

  useEffect(() => {
    if (!response) {
      setDisplayedResponse('');
      return;
    }

    // Store full response with cleaned formatting
    responseRef.current = cleanResponse(response);

    // Clear any existing typing interval
    if (typingRef.current) {
      clearInterval(typingRef.current);
    }

    // Handle showing full response if requested
    if (showFullResponse) {
      setDisplayedResponse(responseRef.current);
      setIsTyping(false);
      return;
    }

    setIsTyping(true);
    setDisplayedResponse('');

    // More natural typing animation that varies speed based on punctuation
    // and adds chunks of text instead of single characters
    const typeNextChunk = (index = 0, delay = typingSpeed) => {
      if (index >= responseRef.current.length) {
        setIsTyping(false);
        // Ensure complete response is displayed
        setDisplayedResponse(responseRef.current);
        return;
      }

      // Determine chunk size (1-5 characters, smaller near punctuation)
      let chunkSize = Math.floor(Math.random() * 5) + 1;
      const nextPunctuation = responseRef.current.substring(index).search(/[.,!?;:\n]/);

      // If we're approaching punctuation, slow down
      if (nextPunctuation >= 0 && nextPunctuation < 5) {
        chunkSize = Math.min(chunkSize, nextPunctuation + 1);
        delay = typingSpeed * 1.5; // Slow down near punctuation
      } else if (responseRef.current[index] === ' ') {
        delay = typingSpeed * 0.8; // Speed up slightly at spaces
      }

      // Don't exceed the string length
      chunkSize = Math.min(chunkSize, responseRef.current.length - index);

      // Add the next chunk
      const nextChunk = responseRef.current.substring(index, index + chunkSize);
      setDisplayedResponse(prev => prev + nextChunk);

      // Schedule the next chunk
      typingRef.current = setTimeout(() => {
        typeNextChunk(index + chunkSize);
      }, delay);
    };

    // Start typing
    typingRef.current = setTimeout(() => {
      typeNextChunk();
    }, typingSpeed);

    return () => {
      if (typingRef.current) {
        clearTimeout(typingRef.current);
      }
    };
  }, [response, typingSpeed, showFullResponse]);

  const handleShowFullResponse = () => {
    if (typingRef.current) {
      clearTimeout(typingRef.current);
    }
    setShowFullResponse(true);
    setDisplayedResponse(responseRef.current);
    setIsTyping(false);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, type: "spring", stiffness: 100, damping: 15 }}
      className="mt-8 p-8 bg-gradient-to-br from-indigo-900/30 to-blue-900/20 backdrop-blur-xl rounded-2xl border border-white/10 shadow-[0_8px_30px_rgb(0,0,0,0.12)] hover:shadow-[0_8px_30px_rgba(79,70,229,0.15)] transition-all duration-500 relative overflow-hidden"
    >
      {/* Enhanced decorative elements */}
      <div className="absolute -top-10 -right-10 w-48 h-48 bg-indigo-500/10 rounded-full blur-3xl"></div>
      <div className="absolute -bottom-10 -left-10 w-48 h-48 bg-blue-500/10 rounded-full blur-3xl"></div>
      <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-indigo-500/30 to-transparent"></div>
      <div className="absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-indigo-500/20 to-transparent"></div>
      
      <div className="relative">
        {isTyping && (
          <div className="absolute top-3 right-3 z-10">
            <button
              onClick={handleShowFullResponse}
              className="text-xs bg-indigo-500/20 hover:bg-indigo-500/30 text-indigo-200 hover:text-indigo-100 flex items-center px-3 py-1.5 rounded-full transition-all duration-200 backdrop-blur-sm border border-indigo-500/20 hover:border-indigo-400/30 shadow-sm hover:shadow-indigo-500/10"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              <span>Skip</span>
            </button>
          </div>
        )}

        {displayedResponse && (
          <ReactMarkdown
            className="prose prose-invert prose-lg max-w-none
              prose-headings:text-indigo-300
              prose-headings:font-semibold
              prose-headings:mb-3
              prose-h2:text-2xl
              prose-h2:bg-clip-text 
              prose-h2:text-transparent
              prose-h2:bg-gradient-to-r 
              prose-h2:from-indigo-300 
              prose-h2:to-blue-300
              prose-h2:font-bold
              prose-h2:pb-2
              prose-h3:text-xl
              prose-h3:text-indigo-200
              prose-h3:border-b
              prose-h3:border-indigo-800/50
              prose-h3:pb-2
              prose-h3:mt-6
              prose-h4:text-lg
              prose-h4:text-blue-300
              prose-p:text-white/90
              prose-p:leading-relaxed
              prose-ul:my-4
              prose-li:text-white/90
              prose-li:my-1.5
              prose-li:marker:text-indigo-400
              prose-strong:text-indigo-200
              prose-strong:font-medium
              prose-a:text-indigo-300
              prose-a:hover:text-indigo-200
              prose-a:transition-colors
              prose-a:duration-200
              prose-a:border-b
              prose-a:border-indigo-500/30
              prose-a:hover:border-indigo-400
              prose-a:no-underline
              prose-code:bg-indigo-900/30
              prose-code:px-1.5
              prose-code:py-0.5
              prose-code:rounded
              prose-code:text-indigo-200
              prose-code:before:content-none
              prose-code:after:content-none
              prose-blockquote:border-l-indigo-500
              prose-blockquote:text-white/80
              prose-blockquote:italic"
          >
            {displayedResponse}
          </ReactMarkdown>
        )}
        {isTyping &&
          <div className="inline-flex items-center ml-2">
            <div className="flex space-x-1.5">
              <div className="w-2 h-2 bg-indigo-400 rounded-full animate-pulse" style={{ animationDuration: '0.8s' }} />
              <div className="w-2 h-2 bg-indigo-400 rounded-full animate-pulse" style={{ animationDuration: '0.8s', animationDelay: '0.2s' }} />
              <div className="w-2 h-2 bg-indigo-400 rounded-full animate-pulse" style={{ animationDuration: '0.8s', animationDelay: '0.4s' }} />
            </div>
          </div>
        }
      </div>
    </motion.div>
  );
}