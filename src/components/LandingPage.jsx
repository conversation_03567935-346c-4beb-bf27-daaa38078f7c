import React from 'react';
import HeroS<PERSON><PERSON> from './landing/HeroSection';
import FeatureSection from './landing/FeatureSection';
import SymptomCheckerSection from './symptom-checker/SymptomCheckerSection';
import ResourceLocatorSection from './resources/ResourceLocatorSection';
import AIToolsSection from './ai-tools/AIToolsSection';
import { useUser } from '../contexts/UserContext';

export default function LandingPage() {
  const { user } = useUser();

  return (
    <div>
      <HeroSection user={user} />
      <FeatureSection />
      <SymptomCheckerSection />
      {/* Temporarily hidden until ready */}
      {/* <AIToolsSection /> */}
      <ResourceLocatorSection />
    </div>
  );
}