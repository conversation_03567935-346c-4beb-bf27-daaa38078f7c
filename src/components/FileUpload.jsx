import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { supabase } from '../lib/supabase';
import Button from './common/Button';

const FileUpload = ({ onUpload }) => {
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState(null);

  const onDrop = useCallback(async (acceptedFiles) => {
    const file = acceptedFiles[0];
    if (!file) return;

    try {
      setUploading(true);
      setError(null);

      // Validate file
      if (file.size > 50 * 1024 * 1024) { // 50MB limit
        throw new Error('File size must be less than 50MB');
      }
      
      if (!file.name.toLowerCase().endsWith('.stl')) {
        throw new Error('Only STL files are supported');
      }

      // Upload to Supabase storage
      const filename = `${Date.now()}-${file.name}`;
      const { data, error: uploadError } = await supabase.storage
        .from('dental-scans')
        .upload(filename, file, {
          cacheControl: '3600',
          upsert: false,
          contentType: 'model/stl'
        });

      if (uploadError) throw uploadError;

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('dental-scans')
        .getPublicUrl(filename);

      // Create blob URL for local preview
      const blobUrl = URL.createObjectURL(file);
      onUpload(blobUrl);
    } catch (err) {
      console.error('Upload error:', err);
      setError(err.message);
    } finally {
      setUploading(false);
    }
  }, [onUpload]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/sla': ['.stl'],
      'application/octet-stream': ['.stl']
    },
    maxFiles: 1
  });

  return (
    <div className="space-y-4">
      <div
        {...getRootProps()}
        className={`
          border-2 border-dashed rounded-lg p-6 text-center cursor-pointer
          transition-colors duration-200
          ${isDragActive 
            ? 'border-blue-500 bg-blue-500/10' 
            : 'border-white/20 hover:border-blue-500/50 hover:bg-white/5'
          }
        `}
      >
        <input {...getInputProps()} />
        <div className="space-y-2">
          {uploading ? (
            <div className="flex justify-center items-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            </div>
          ) : isDragActive ? (
            <p className="text-blue-400">Drop the STL file here...</p>
          ) : (
            <>
              <div className="flex justify-center mb-2">
                <svg className="w-8 h-8 text-white/60" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
              </div>
              <p className="text-white/80">
                Drag & drop an STL file here, or click to select
              </p>
              <p className="text-sm text-white/60">
                Maximum file size: 50MB
              </p>
            </>
          )}
        </div>
      </div>

      {error && (
        <div className="p-3 bg-red-500/10 border border-red-500/20 rounded text-red-400 text-sm">
          {error}
        </div>
      )}
    </div>
  );
};

export default FileUpload;