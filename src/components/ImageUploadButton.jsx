import React, { useRef } from 'react';
import { useImageUpload } from '../lib/hooks/useImageUpload';
import { useUser } from '../contexts/UserContext';
import Button from './common/Button';

export default function ImageUploadButton({ onUpload, className = '' }) {
  const fileInputRef = useRef(null);
  const { user } = useUser();
  const { handleUpload, uploading, error, progress, clearError } = useImageUpload(user?.id);

  const handleClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = async (e) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      clearError();
      const imageUrl = await handleUpload(file);
      onUpload?.(imageUrl);
      // Clear input to allow uploading the same file again
      e.target.value = '';
    } catch (err) {
      console.error('Upload error:', err);
    }
  };

  return (
    <div className="relative">
      <input
        ref={fileInputRef}
        type="file"
        accept="image/jpeg,image/png,image/webp"
        onChange={handleFileChange}
        className="hidden"
      />
      
      <Button
        onClick={handleClick}
        disabled={uploading}
        className={className}
      >
        {uploading ? (
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 border-2 border-white/20 border-t-white rounded-full animate-spin" />
            <span>Uploading... {progress}%</span>
          </div>
        ) : (
          <div className="flex items-center gap-2">
            <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <span>Upload Image</span>
          </div>
        )}
      </Button>

      {error && (
        <div className="absolute top-full left-0 right-0 mt-2 p-2 bg-red-500/10 border border-red-500/20 rounded text-red-400 text-sm">
          {error}
        </div>
      )}
    </div>
  );
}