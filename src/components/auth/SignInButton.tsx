import React from 'react';
import NewSignInButton from './NewSignInButton';

interface SignInButtonProps {
  userType?: 'patient' | 'practice';
  className?: string;
  children?: React.ReactNode;
}

const SignInButton: React.FC<SignInButtonProps> = ({
  userType = 'patient',
  className = 'px-16 py-4 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white rounded-full transition-all shadow-lg font-medium text-lg min-w-[180px]',
  children = 'Sign In'
}) => {
  return (
    <NewSignInButton
      userType={userType}
      className={className}
    >
      {children || 'Sign In'}
    </NewSignInButton>
  );
};

export default SignInButton;