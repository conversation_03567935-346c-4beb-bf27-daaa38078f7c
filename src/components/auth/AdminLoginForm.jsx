import React, { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useAdminAuth } from '../../lib/hooks/useAdminAuth';
import { Card } from '../common';
import Button from '../common/Button';
import Logo from '../common/Logo';

export default function AdminLoginForm() {
  const [formData, setFormData] = useState({
    username: '',
    password: '',
  });
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);
  const { authenticate } = useAdminAuth();
  const navigate = useNavigate();

  // Log if we're on the login page
  useEffect(() => {
    console.log("AdminLoginForm mounted");
    
    // Check if we already have a valid admin session
    const existingSession = localStorage.getItem('admin_session');
    if (existingSession && localStorage.getItem('admin_logged_out') !== 'true') {
      try {
        const parsedSession = JSON.parse(existingSession);
        if (parsedSession.exp && parsedSession.exp > Date.now()) {
          console.log("Valid admin session found, redirecting to admin panel");
          navigate('/admin');
        }
      } catch (e) {
        console.error("Error parsing session:", e);
      }
    }
  }, [navigate]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError(null);
    
    if (!formData.username || !formData.password) {
      setError("Please enter both username and password");
      return;
    }
    
    setLoading(true);
    
    console.log("Login attempt with credentials");
    
    try {
      // Use the authentication from useAdminAuth to ensure strict credential checking
      console.log(`Attempting to authenticate with username: ${formData.username}`);
      const success = await authenticate(formData.username, formData.password);
      
      if (success) {
        console.log("Authentication successful - redirecting to admin panel");
        setLoading(false);
        navigate('/admin');
      } else {
        console.log("Authentication failed - invalid credentials");
        setError("Invalid username or password. Only authorized administrators can access this area.");
        setLoading(false);
      }
    } catch (error) {
      console.error("Login error:", error);
      setError("An error occurred during login. Please try again.");
      setLoading(false);
    }
  };

  return (
    <Card className="max-w-md mx-auto">
      <div className="text-center mb-8">
        <Logo size="small" className="mx-auto mb-4" />
        <h2 className="text-2xl font-semibold text-white">Admin Access</h2>
        <p className="text-white/60 mt-2">Enter your admin credentials to continue</p>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-500/10 border border-red-500/20 rounded-lg text-red-400 text-sm">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-white/80 mb-1">
            Username
          </label>
          <input
            type="text"
            value={formData.username}
            onChange={(e) => setFormData(prev => ({ ...prev, username: e.target.value }))}
            className="w-full h-11 bg-white/5 border border-white/10 rounded-lg px-4 text-white focus:ring-2 focus:ring-blue-500"
            placeholder="MikeyMouse"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-white/80 mb-1">
            Password
          </label>
          <input
            type="password"
            value={formData.password}
            onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
            className="w-full h-11 bg-white/5 border border-white/10 rounded-lg px-4 text-white focus:ring-2 focus:ring-blue-500"
            placeholder="Admin password"
            required
          />
        </div>

        <Button
          type="submit"
          disabled={loading}
          className="w-full rounded-full"
        >
          {loading ? 'Verifying...' : 'Sign In'}
        </Button>
      </form>

      <div className="mt-6 text-center">
        <Link to="/" className="text-white/60 hover:text-white text-sm">
          Return to Site
        </Link>
      </div>
    </Card>
  );
}