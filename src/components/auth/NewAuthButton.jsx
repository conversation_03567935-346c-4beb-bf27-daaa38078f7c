import React, { useState, useCallback } from 'react';
import NewSignInButton from './NewSignInButton';
import Button from '../common/Button';
import { useAuth } from '../../contexts/AuthContext';

export default function NewAuthButton({ onAuthChange, className = '', mode = 'signin' }) {
  const { user, signOut } = useAuth();

  const handleSignOut = useCallback(async () => {
    try {
      await signOut();
      onAuthChange?.(null);
    } catch (error) {
      console.error('Error signing out:', error.message);
    }
  }, [signOut, onAuthChange]);

  // Format email to show only first part if it's too long
  const formatEmail = (email) => {
    if (!email) return '';
    if (email.length <= 20) return email;
    
    const parts = email.split('@');
    if (parts.length !== 2) return email;
    
    return `${parts[0].substring(0, 8)}...@${parts[1]}`;
  };

  return (
    <>
      {user ? (
        <div className={`flex items-center space-x-3 py-1 px-3 bg-white/5 rounded-full ${className}`}>
          <div className="flex items-center">
            <div className="h-6 w-6 rounded-full bg-indigo-600 flex items-center justify-center mr-2">
              <span className="text-xs font-medium text-white">
                {user.email ? user.email.charAt(0).toUpperCase() : 'U'}
              </span>
            </div>
            <span className="text-white text-sm font-medium truncate max-w-[140px]">
              {formatEmail(user.email || 'Signed In')}
            </span>
          </div>
          <Button
            variant="tertiary"
            size="sm"
            onClick={handleSignOut}
            className="text-white/70 hover:text-white hover:bg-white/10"
          >
            Sign Out
          </Button>
        </div>
      ) : (
        <NewSignInButton
          userType="patient"
          className={`w-full sm:w-auto ${className}`}
        >
          Sign In
        </NewSignInButton>
      )}
    </>
  );
}
