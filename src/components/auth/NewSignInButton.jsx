import React, { useState } from 'react';
import { createPortal } from 'react-dom';
import { useNavigate, Link } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { validatePassword, validateEmail } from '../../lib/utils/validation';

// Removing previous attempt and creating a more direct solution
// with a completely custom styled button

/**
 * @typedef {Object} NewSignInButtonProps
 * @property {'patient' | 'practice'} [userType='patient']
 * @property {string} [className]
 * @property {React.ReactNode} [children]
 * @property {boolean} [compact=false]
 */

/**
 * @param {NewSignInButtonProps} props
 */
const NewSignInButton = ({
  userType = 'patient',
  className = 'px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-full transition-all duration-200 shadow-md font-medium text-sm',
  children = 'Sign In',
  compact = false
}) => {
  const { signIn, signUp, error: authError, clearError } = useAuth();
  const [showModal, setShowModal] = useState(false);
  const [showSignUp, setShowSignUp] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const navigate = useNavigate();

  // Sign in form state
  const [signInData, setSignInData] = useState({
    email: '',
    password: '',
    rememberMe: false
  });

  // Sign up form state
  const [signUpData, setSignUpData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    username: '',
    acceptTerms: false,
    acceptMedicalDisclaimer: false
  });

  const openModal = () => {
    document.body.style.overflow = 'hidden';
    setShowModal(true);
    setShowSignUp(false);
    clearError();
    setError(null);
  };

  const closeModal = () => {
    document.body.style.overflow = 'auto';
    setShowModal(false);
    setShowSignUp(false);
    clearError();
    setError(null);
  };

  const switchToSignUp = (e) => {
    e.preventDefault();
    setShowSignUp(true);
    clearError();
    setError(null);
  };

  const handleSignInChange = (e) => {
    const { name, value, type, checked } = e.target;
    setSignInData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSignUpChange = (e) => {
    const { name, value, type, checked } = e.target;
    setSignUpData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSignIn = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    
    try {
      if (!signInData.email || !signInData.password) {
        throw new Error('Please fill in all required fields');
      }

      const result = await signIn({
        email: signInData.email,
        password: signInData.password,
        remember: signInData.rememberMe
      });

      if (result) {
        closeModal();
      }
    } catch (err) {
      console.error('Sign in error:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSignUp = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    
    try {
      // Validate email
      if (!validateEmail(signUpData.email)) {
        throw new Error('Please enter a valid email address');
      }

      // Validate password
      if (!validatePassword(signUpData.password)) {
        throw new Error('Password must be at least 8 characters with an uppercase letter, number, and special character');
      }

      // Check password match
      if (signUpData.password !== signUpData.confirmPassword) {
        throw new Error('Passwords do not match');
      }

      // Check terms acceptance
      if (!signUpData.acceptTerms) {
        throw new Error('Please accept the Terms of Service and Privacy Policy');
      }
      
      // Check medical disclaimer acceptance
      if (!signUpData.acceptMedicalDisclaimer) {
        throw new Error('Please acknowledge the Medical Disclaimer');
      }

      // Create username if not provided
      const username = signUpData.username || `user_${Date.now().toString(36)}`;

      const result = await signUp({
        email: signUpData.email,
        password: signUpData.password,
        username: username,
        firstName: signUpData.firstName,
        lastName: signUpData.lastName,
        acceptedTerms: true,
        acceptedMedicalDisclaimer: true,
        acceptedAt: new Date().toISOString()
      });

      if (result) {
        // Show success message and switch back to sign in
        setError({ type: 'success', message: 'Account created successfully! Please check your email to confirm your account.' });
        setShowSignUp(false);
        
        // Reset sign in form with the email
        setSignInData(prev => ({
          ...prev,
          email: signUpData.email,
          password: ''
        }));
      }
    } catch (err) {
      console.error('Sign up error:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      {/* Button with blue-to-purple gradient matching the design */}
      <button
        type="button"
        onClick={openModal}
        style={{
          background: 'linear-gradient(to right, #4a7bf7, #9364ef)', // Blue to purple gradient
          color: 'white',
          padding: compact ? '0.375rem 1rem' : '0.5rem 1.5rem', // Smaller padding for compact
          borderRadius: '9999px',
          fontWeight: '500',
          fontSize: compact ? '0.8125rem' : '0.875rem', // Smaller font for compact
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
          border: 'none',
          cursor: 'pointer',
          transition: 'all 0.2s ease',
          display: 'inline-block'
        }}
        onMouseOver={(e) => { e.currentTarget.style.filter = 'brightness(1.1)' }}
        onMouseOut={(e) => { e.currentTarget.style.filter = 'brightness(1)' }}
      >
        {children}
      </button>

      {showModal && createPortal(
        <div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75 backdrop-blur-sm"
          onClick={(e) => e.target === e.currentTarget && closeModal()}
        >
          <div
            className="bg-[#1f2538] rounded-xl shadow-xl w-full max-w-md mx-4 overflow-hidden"
            onClick={(e) => e.stopPropagation()}
          >
            {!showSignUp ? (
              <>
                {/* Sign In Form */}
                <div className="bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-8 text-white text-center">
                  <div className="mx-auto w-12 h-12 bg-white rounded-full flex items-center justify-center mb-4">
                    <svg className="h-8 w-8 text-blue-600" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 15h-2v-6h2v6zm4 0h-2v-6h2v6zm-6-8H7V7h2v2zm4 0h-2V7h2v2zm4 0h-2V7h2v2z" />
                    </svg>
                  </div>
                  <h2 className="text-2xl font-bold">
                    {userType === 'patient' ? 'Patient Sign In' : 'Practice Sign In'}
                  </h2>
                </div>

                <div className="p-6">
                  {(error || authError) && (
                    <div className={`mb-4 p-3 rounded-lg text-sm ${error?.type === 'success' ? 'bg-green-500/20 text-green-400 border border-green-500/30' : 'bg-red-500/20 text-red-400 border border-red-500/30'}`}>
                      {error?.type === 'success' ? error.message : (error || authError)}
                    </div>
                  )}
                  
                  <form onSubmit={handleSignIn}>
                    <div className="mb-4">
                      <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-1">
                        Email Address
                      </label>
                      <input
                        id="email"
                        name="email"
                        type="email"
                        value={signInData.email}
                        onChange={handleSignInChange}
                        className="w-full px-3 py-2 bg-gray-700/30 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white"
                        placeholder="<EMAIL>"
                        required
                        disabled={loading}
                      />
                    </div>

                    <div className="mb-6">
                      <div className="flex items-center justify-between mb-1">
                        <label htmlFor="password" className="block text-sm font-medium text-gray-300">
                          Password
                        </label>
                        <a href="#forgot" className="text-xs text-blue-400 hover:text-blue-300">
                          Forgot password?
                        </a>
                      </div>
                      <input
                        id="password"
                        name="password"
                        type="password"
                        value={signInData.password}
                        onChange={handleSignInChange}
                        className="w-full px-3 py-2 bg-gray-700/30 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white"
                        placeholder="••••••••"
                        required
                        disabled={loading}
                      />
                    </div>

                    <div className="flex items-center mb-6">
                      <input
                        id="rememberMe"
                        name="rememberMe"
                        type="checkbox"
                        checked={signInData.rememberMe}
                        onChange={handleSignInChange}
                        className="w-4 h-4 text-blue-600 border-gray-600 rounded focus:ring-blue-500"
                        disabled={loading}
                      />
                      <label htmlFor="rememberMe" className="ml-2 text-sm text-gray-300">
                        Remember me
                      </label>
                    </div>

                    <button
                      type="submit"
                      className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium py-2.5 px-4 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200 disabled:opacity-70"
                      disabled={loading}
                    >
                      {loading ? "Signing In..." : "Sign In"}
                    </button>

                    <div className="mt-6 text-center">
                      <p className="text-sm text-gray-400">
                        Don&apos;t have an account?{' '}
                        <a
                          href="#register"
                          onClick={switchToSignUp}
                          className="text-blue-400 hover:text-blue-300 font-medium"
                        >
                          Create Account
                        </a>
                      </p>
                    </div>
                  </form>
                </div>
              </>
            ) : (
              <>
                {/* Sign Up Form */}
                <div className="bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-8 text-white text-center">
                  <div className="mx-auto w-12 h-12 bg-white rounded-full flex items-center justify-center mb-4">
                    <svg className="h-8 w-8 text-blue-600" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M15 4c-4.42 0-8 3.58-8 8s3.58 8 8 8 8-3.58 8-8-3.58-8-8-8zm0 14c-3.31 0-6-2.69-6-6s2.69-6 6-6 6 2.69 6 6-2.69 6-6 6zM7 6.5C7 8.43 5.43 10 3.5 10S0 8.43 0 6.5 1.57 3 3.5 3 7 4.57 7 6.5zm-2 0C5 5.67 4.33 5 3.5 5S2 5.67 2 6.5 2.67 8 3.5 8 5 7.33 5 6.5z" />
                    </svg>
                  </div>
                  <h2 className="text-2xl font-bold">Create Account</h2>
                </div>

                <div className="p-6 max-h-[70vh] overflow-y-auto">
                  {(error || authError) && (
                    <div className={`mb-4 p-3 rounded-lg text-sm ${error?.type === 'success' ? 'bg-green-500/20 text-green-400 border border-green-500/30' : 'bg-red-500/20 text-red-400 border border-red-500/30'}`}>
                      {error?.type === 'success' ? error.message : (error || authError)}
                    </div>
                  )}

                  <form onSubmit={handleSignUp}>
                    <div className="grid grid-cols-2 gap-4 mb-4">
                      <div>
                        <label htmlFor="firstName" className="block text-sm font-medium text-gray-300 mb-1">
                          First Name
                        </label>
                        <input
                          id="firstName"
                          name="firstName"
                          type="text"
                          value={signUpData.firstName}
                          onChange={handleSignUpChange}
                          className="w-full px-3 py-2 bg-gray-700/30 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white"
                          placeholder="First name"
                          required
                          disabled={loading}
                        />
                      </div>
                      <div>
                        <label htmlFor="lastName" className="block text-sm font-medium text-gray-300 mb-1">
                          Last Name
                        </label>
                        <input
                          id="lastName"
                          name="lastName"
                          type="text"
                          value={signUpData.lastName}
                          onChange={handleSignUpChange}
                          className="w-full px-3 py-2 bg-gray-700/30 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white"
                          placeholder="Last name"
                          required
                          disabled={loading}
                        />
                      </div>
                    </div>

                    <div className="mb-4">
                      <label htmlFor="username" className="block text-sm font-medium text-gray-300 mb-1">
                        Username
                      </label>
                      <input
                        id="username"
                        name="username"
                        type="text"
                        value={signUpData.username}
                        onChange={handleSignUpChange}
                        className="w-full px-3 py-2 bg-gray-700/30 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white"
                        placeholder="Choose a username"
                        disabled={loading}
                      />
                      <p className="mt-1 text-xs text-gray-500">Optional - will be auto-generated if not provided</p>
                    </div>

                    <div className="mb-4">
                      <label htmlFor="signupEmail" className="block text-sm font-medium text-gray-300 mb-1">
                        Email Address
                      </label>
                      <input
                        id="signupEmail"
                        name="email"
                        type="email"
                        value={signUpData.email}
                        onChange={handleSignUpChange}
                        className="w-full px-3 py-2 bg-gray-700/30 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white"
                        placeholder="<EMAIL>"
                        required
                        disabled={loading}
                      />
                    </div>

                    <div className="mb-4">
                      <label htmlFor="signupPassword" className="block text-sm font-medium text-gray-300 mb-1">
                        Password
                      </label>
                      <input
                        id="signupPassword"
                        name="password"
                        type="password"
                        value={signUpData.password}
                        onChange={handleSignUpChange}
                        className="w-full px-3 py-2 bg-gray-700/30 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white"
                        placeholder="••••••••"
                        required
                        disabled={loading}
                      />
                      <p className="mt-1 text-xs text-gray-500">
                        Must be at least 8 characters with an uppercase letter, number, and special character
                      </p>
                    </div>

                    <div className="mb-4">
                      <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-300 mb-1">
                        Confirm Password
                      </label>
                      <input
                        id="confirmPassword"
                        name="confirmPassword"
                        type="password"
                        value={signUpData.confirmPassword}
                        onChange={handleSignUpChange}
                        className="w-full px-3 py-2 bg-gray-700/30 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white"
                        placeholder="••••••••"
                        required
                        disabled={loading}
                      />
                    </div>

                    {/* Terms acceptance checkbox */}
                    <div className="p-3 border border-gray-600 rounded-lg bg-gray-800/30 mb-4">
                      <label className="flex items-start">
                        <input
                          type="checkbox"
                          name="acceptTerms"
                          checked={signUpData.acceptTerms}
                          onChange={handleSignUpChange}
                          className="w-4 h-4 mt-1 rounded border-gray-600 text-blue-600 focus:ring-blue-500"
                          required
                          disabled={loading}
                        />
                        <span className="ml-2 text-sm text-gray-400">
                          I have read and agree to the{' '}
                          <Link to="/terms" target="_blank" className="text-blue-400 hover:text-blue-300">
                            Terms of Service
                          </Link>
                          {' '}and{' '}
                          <Link to="/privacy" target="_blank" className="text-blue-400 hover:text-blue-300">
                            Privacy Policy
                          </Link>
                        </span>
                      </label>
                    </div>

                    {/* Medical disclaimer checkbox */}
                    <div className="p-3 border border-red-500/30 rounded-lg bg-red-900/10 mb-4">
                      <label className="flex items-start">
                        <input
                          type="checkbox"
                          name="acceptMedicalDisclaimer"
                          checked={signUpData.acceptMedicalDisclaimer}
                          onChange={handleSignUpChange}
                          className="w-4 h-4 mt-1 rounded border-red-300 text-red-600 focus:ring-red-500"
                          required
                          disabled={loading}
                        />
                        <div className="ml-2">
                          <span className="text-sm text-red-300 font-medium block mb-1">MEDICAL DISCLAIMER ACKNOWLEDGMENT</span>
                          <span className="text-xs text-gray-400">
                            I acknowledge that Smilo is not a licensed healthcare provider and does not provide medical or dental advice, 
                            diagnosis, or treatment. All information is for educational purposes only and not a substitute for professional care. 
                            I will consult a qualified healthcare provider for any medical or dental concerns.
                          </span>
                        </div>
                      </label>
                    </div>

                    <button
                      type="submit"
                      className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium py-2.5 px-4 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200 disabled:opacity-70"
                      disabled={loading}
                    >
                      {loading ? "Creating Account..." : "Create Account"}
                    </button>

                    <div className="mt-6 text-center">
                      <p className="text-sm text-gray-400">
                        Already have an account?{' '}
                        <a
                          href="#signin"
                          onClick={() => setShowSignUp(false)}
                          className="text-blue-400 hover:text-blue-300 font-medium"
                        >
                          Sign In
                        </a>
                      </p>
                    </div>
                  </form>
                </div>
              </>
            )}
          </div>
        </div>,
        document.body
      )}
    </>
  );
};

export default NewSignInButton;
