import React from 'react';
import { Card } from '../common';

export default function TermsAgreement({ onAccept, onDecline }) {
  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
      <Card className="w-full max-w-2xl max-h-[80vh] overflow-y-auto">
        <h2 className="text-2xl font-semibold text-white mb-6">Terms of Service & Privacy Policy</h2>
        
        <div className="prose prose-invert mb-6">
          <h3>1. Data Collection and Usage</h3>
          <p>
            By using <PERSON><PERSON><PERSON>AI Assistant, you agree that we may collect and use your personal information, including but not limited to:
          </p>
          <ul>
            <li>Account information (email, profile data)</li>
            <li>Course history and academic progress</li>
            <li>Search history and preferences</li>
            <li>Usage patterns and interactions</li>
            <li>Device and browser information</li>
          </ul>

          <h3>2. Data Storage and Security</h3>
          <p>
            We implement industry-standard security measures to protect your data. However, no method of transmission over the internet is 100% secure.
          </p>

          <h3>3. User Rights</h3>
          <p>You have the right to:</p>
          <ul>
            <li>Access your personal data</li>
            <li>Request data correction</li>
            <li>Request data deletion</li>
            <li>Export your data</li>
            <li>Opt-out of certain data collection</li>
          </ul>

          <h3>4. Cookie Policy</h3>
          <p>
            We use cookies and similar technologies to enhance your experience and collect usage data. By using our service, you consent to our use of cookies.
          </p>

          <h3>5. Data Sharing</h3>
          <p>
            We may share anonymized and aggregated data with third parties for research, analytics, and service improvement purposes.
          </p>

          <h3>6. Changes to Terms</h3>
          <p>
            We reserve the right to modify these terms at any time. Users will be notified of significant changes.
          </p>
        </div>

        <div className="flex justify-end space-x-4">
          <button
            onClick={onDecline}
            className="px-4 py-2 text-white/80 hover:text-white"
          >
            Decline
          </button>
          <button
            onClick={onAccept}
            className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-400 transition-colors"
          >
            Accept & Continue
          </button>
        </div>
      </Card>
    </div>
  );
}