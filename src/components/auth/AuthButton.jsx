import React, { useState, useCallback } from 'react';
import NewSignInButton from './NewSignInButton';
import Button from '../common/Button';
import { useAuth } from '../../contexts/AuthContext';
import LogoutConfirmationModal from '../common/LogoutConfirmationModal';

export default function AuthButton({ onAuthChange, className = '', mode = 'signin', compact = false }) {
  const { user, signOut } = useAuth();
  const [showLogoutConfirmation, setShowLogoutConfirmation] = useState(false);

  const handleSignOutClick = () => {
    setShowLogoutConfirmation(true);
  };

  const handleSignOut = useCallback(async () => {
    try {
      await signOut();
      onAuthChange?.(null);
    } catch (error) {
      console.error('Error signing out:', error.message);
    }
  }, [signOut, onAuthChange]);

  return (
    <>
      {user ? (
        <div className={`flex items-center gap-2 ${className}`}>
          <span className="text-white/80 hidden sm:inline">
            {user.email || 'Signed In'}
          </span>
          <Button
            variant="secondary"
            onClick={handleSignOutClick}
            className="w-auto hover:bg-red-500/10"
            size={compact ? "small" : "default"}
          >
            {compact ? "Sign Out" : "Sign Out"}
          </Button>

          <LogoutConfirmationModal
            isOpen={showLogoutConfirmation}
            onClose={() => setShowLogoutConfirmation(false)}
            onConfirm={handleSignOut}
          />
        </div>
      ) : (
        <NewSignInButton
          userType="patient"
          className={`${className}`}
          compact={compact}
        >
          Sign In
        </NewSignInButton>
      )}
    </>
  );
}
