import React, { useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import Button from '../common/Button';
import { validatePassword } from '../../lib/utils/validation';
import { Link } from 'react-router-dom';

export default function RegisterForm({ onSuccess, onSignInClick, setMessage }) {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    username: '',
    acceptTerms: false,
    acceptMedicalDisclaimer: false
  });
  const [loading, setLoading] = useState(false);
  const { signUp } = useAuth();

  const handleSubmit = async (e) => {
    e.preventDefault();
    e.stopPropagation();
    setLoading(true);

    try {
      // Validate password
      if (!validatePassword(formData.password)) {
        throw new Error('Password must be at least 8 characters with an uppercase letter, number, and special character');
      }

      // Check password match
      if (formData.password !== formData.confirmPassword) {
        throw new Error('Passwords do not match');
      }

      // Check terms acceptance
      if (!formData.acceptTerms) {
        throw new Error('Please accept the Terms of Service and Privacy Policy');
      }

      // Check medical disclaimer acceptance
      if (!formData.acceptMedicalDisclaimer) {
        throw new Error('Please acknowledge the Medical Disclaimer');
      }

      await signUp({
        email: formData.email,
        password: formData.password,
        username: formData.username,
        firstName: formData.firstName,
        lastName: formData.lastName,
        acceptedTerms: true,
        acceptedMedicalDisclaimer: true,
        acceptedAt: new Date().toISOString()
      });

      onSuccess?.();
    } catch (err) {
      console.error('Registration error:', err);
      setMessage?.({
        type: 'error',
        text: err.message
      });
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  return (
    <div className="space-y-6" onClick={(e) => e.stopPropagation()}>
      <div className="text-center">
        <h2 className="text-2xl font-bold text-white mb-2">Create Account</h2>
        <p className="text-gray-400">
          Join us today and get started
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-white/80 mb-1">
              First Name
            </label>
            <input
              type="text"
              name="firstName"
              value={formData.firstName}
              onChange={handleChange}
              className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-white/80 mb-1">
              Last Name
            </label>
            <input
              type="text"
              name="lastName"
              value={formData.lastName}
              onChange={handleChange}
              className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-white/80 mb-1">
            Username
          </label>
          <input
            type="text"
            name="username"
            value={formData.username}
            onChange={handleChange}
            className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-white/80 mb-1">
            Email Address
          </label>
          <input
            type="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-white/80 mb-1">
            Password
          </label>
          <input
            type="password"
            name="password"
            value={formData.password}
            onChange={handleChange}
            className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500"
            required
          />
          <p className="mt-1 text-xs text-gray-400">
            Must be at least 8 characters with an uppercase letter, number, and special character
          </p>
        </div>

        <div>
          <label className="block text-sm font-medium text-white/80 mb-1">
            Confirm Password
          </label>
          <input
            type="password"
            name="confirmPassword"
            value={formData.confirmPassword}
            onChange={handleChange}
            className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-blue-500"
            required
          />
        </div>

        {/* Terms acceptance checkbox */}
        <div className="p-3 border border-white/10 rounded-lg bg-white/5">
          <label className="flex items-start">
            <input
              type="checkbox"
              name="acceptTerms"
              checked={formData.acceptTerms}
              onChange={handleChange}
              className="w-4 h-4 mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              required
            />
            <span className="ml-2 text-sm text-gray-400">
              I have read and agree to the{' '}
              <Link to="/terms" target="_blank" className="text-blue-400 hover:text-blue-300">
                Terms of Service
              </Link>
              {' '}and{' '}
              <Link to="/privacy" target="_blank" className="text-blue-400 hover:text-blue-300">
                Privacy Policy
              </Link>
            </span>
          </label>
        </div>

        {/* Medical disclaimer checkbox */}
        <div className="p-3 border border-red-500/30 rounded-lg bg-red-900/10">
          <label className="flex items-start">
            <input
              type="checkbox"
              name="acceptMedicalDisclaimer"
              checked={formData.acceptMedicalDisclaimer}
              onChange={handleChange}
              className="w-4 h-4 mt-1 rounded border-red-300 text-red-600 focus:ring-red-500"
              required
            />
            <div className="ml-2">
              <span className="text-sm text-red-300 font-medium block mb-1">MEDICAL DISCLAIMER ACKNOWLEDGMENT</span>
              <span className="text-xs text-gray-400">
                I acknowledge that Smilo is not a licensed healthcare provider and does not provide medical or dental advice, 
                diagnosis, or treatment. All information is for educational purposes only and not a substitute for professional care. 
                I will consult a qualified healthcare provider for any medical or dental concerns.
              </span>
            </div>
          </label>
        </div>

        <Button
          type="submit"
          disabled={loading}
          className="w-full"
        >
          {loading ? 'Creating Account...' : 'Create Account'}
        </Button>

        <div className="text-center">
          <p className="text-sm text-gray-400">
            Already have an account?{' '}
            <button
              type="button"
              onClick={onSignInClick}
              className="text-blue-400 hover:text-blue-300"
            >
              Sign in instead
            </button>
          </p>
        </div>
      </form>
    </div>
  );
}