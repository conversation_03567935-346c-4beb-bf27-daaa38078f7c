import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';

const UserTypeSelection: React.FC = () => {
  const [selectedType, setSelectedType] = useState<string | null>(null);
  const navigate = useNavigate();

  const handleContinue = () => {
    console.log(`User selected type: ${selectedType}`);
    
    if (selectedType === 'patient') {
      console.log('Navigating to patient sign-in');
      navigate('/auth/sign-in/patient');
    } else if (selectedType === 'dentist') {
      console.log('Navigating to practice sign-in');
      navigate('/auth/sign-in/practice');
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-50 px-4">
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-md w-full bg-white rounded-2xl shadow-xl overflow-hidden"
      >
        <div className="bg-gradient-to-r from-blue-600 to-indigo-700 py-6 px-6">
          <h2 className="text-2xl font-bold text-white text-center">Welcome to Smilo.Dental</h2>
          <p className="text-blue-100 text-center mt-2">Select how you'd like to proceed</p>
        </div>

        <div className="p-6">
          <p className="text-gray-600 text-center mb-6">
            Are you a patient looking for dental care, or a dental practice seeking to grow your business?
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
            {/* Patient Option */}
            <motion.div
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => setSelectedType('patient')}
              className={`cursor-pointer p-5 rounded-xl border-2 transition-all ${
                selectedType === 'patient' 
                  ? 'border-blue-500 bg-blue-50' 
                  : 'border-gray-200 hover:border-blue-300'
              }`}
            >
              <div className="flex flex-col items-center text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-blue-600" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                  </svg>
                </div>
                <h3 className="font-semibold text-gray-800 mb-1">I'm a Patient</h3>
                <p className="text-sm text-gray-500">Looking for dental care services</p>
              </div>
            </motion.div>

            {/* Dental Practice Option */}
            <motion.div
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => setSelectedType('dentist')}
              className={`cursor-pointer p-5 rounded-xl border-2 transition-all ${
                selectedType === 'dentist' 
                  ? 'border-indigo-500 bg-indigo-50' 
                  : 'border-gray-200 hover:border-indigo-300'
              }`}
            >
              <div className="flex flex-col items-center text-center">
                <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mb-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a1 1 0 01-1 1v1h-3v-1H8v1H5v-1a1 1 0 01-1-1V4zm3 1h6v4H7V5zm3 11a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
                  </svg>
                </div>
                <h3 className="font-semibold text-gray-800 mb-1">I'm a Dental Provider</h3>
                <p className="text-sm text-gray-500">Dental practice or insurance provider</p>
              </div>
            </motion.div>
          </div>

          <button
            onClick={handleContinue}
            disabled={!selectedType}
            className={`w-full py-3 rounded-lg font-medium text-white transition-all ${
              selectedType 
                ? 'bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 shadow-md' 
                : 'bg-gray-300 cursor-not-allowed'
            }`}
          >
            Continue
          </button>

          <p className="text-xs text-gray-500 text-center mt-6">
            By continuing, you agree to our Terms of Service and Privacy Policy.
          </p>
        </div>
      </motion.div>
    </div>
  );
};

export default UserTypeSelection; 