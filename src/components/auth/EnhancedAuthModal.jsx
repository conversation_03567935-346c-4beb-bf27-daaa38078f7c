import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '../../contexts/AuthContext';
import SignInForm from './SignInForm';
import RegisterForm from './RegisterForm';
import ForgotPasswordForm from './ForgotPasswordForm';
import { XMarkIcon } from '@heroicons/react/24/outline';

const modalVariants = {
  hidden: { 
    opacity: 0,
    scale: 0.95,
    y: -10
  },
  visible: { 
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      type: "spring",
      duration: 0.3,
      bounce: 0.2
    }
  },
  exit: { 
    opacity: 0,
    scale: 0.95,
    y: 10,
    transition: {
      duration: 0.2
    }
  }
};

const overlayVariants = {
  hidden: { opacity: 0 },
  visible: { 
    opacity: 1,
    transition: {
      duration: 0.2
    }
  },
  exit: { 
    opacity: 0,
    transition: {
      duration: 0.2
    }
  }
};

export default function EnhancedAuthModal({ onClose, onAuthChange, initialMode = 'signin' }) {
  const [view, setView] = useState(initialMode);
  const [message, setMessage] = useState(null);
  const { error, clearError } = useAuth();
  const [isClosing, setIsClosing] = useState(false);

  // Handle keyboard events
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        handleCloseIntent();
      }
    };
    
    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, []);

  // Handle auth errors
  useEffect(() => {
    if (error) {
      setMessage({ type: 'error', text: error });
      clearError();
    }
  }, [error, clearError]);

  const handleViewChange = useCallback((newView) => {
    setMessage(null);
    setView(newView);
  }, []);

  // Implement a confirmation for closing the modal
  const handleCloseIntent = useCallback(() => {
    // Don't prompt when successfully logged in
    if (message?.type === 'success') {
      onClose();
      return;
    }
    
    setIsClosing(true);
    // Auto-hide the confirmation after 3 seconds
    setTimeout(() => setIsClosing(false), 3000);
  }, [message, onClose]);

  // Safe close handler that confirms before closing
  const handleSafeClose = useCallback((e) => {
    e.stopPropagation();
    onClose();
  }, [onClose]);

  // For the overlay click - only close if directly clicking the overlay
  const handleOverlayClick = useCallback((e) => {
    if (e.target === e.currentTarget) {
      handleCloseIntent();
    }
  }, [handleCloseIntent]);

  return (
    <AnimatePresence mode="sync">
      <div className="fixed inset-0 z-[100] overflow-y-auto">
        <div className="min-h-screen px-4 text-center">
          {/* Background overlay */}
          <motion.div
            className="fixed inset-0 bg-black/70 backdrop-blur-sm"
            variants={overlayVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            onClick={handleOverlayClick}
          />

          {/* This element is to trick the browser into centering the modal contents. */}
          <span
            className="inline-block h-screen align-middle"
            aria-hidden="true"
          >
            &#8203;
          </span>

          {/* Modal panel */}
          <motion.div
            className="inline-block w-full max-w-md p-6 my-8 text-left align-middle bg-gradient-to-b from-gray-900 to-gray-800 rounded-2xl shadow-xl transform transition-all relative"
            variants={modalVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Close button */}
            <button
              onClick={handleCloseIntent}
              className="absolute right-4 top-4 rounded-full p-1 text-gray-400 hover:bg-white/10 hover:text-white transition-colors"
            >
              <XMarkIcon className="h-5 w-5" />
            </button>

            {/* Message display */}
            {message && (
              <div
                className={`mb-4 rounded-lg p-3 text-sm ${
                  message.type === 'error'
                    ? 'bg-red-500/10 border border-red-500/20 text-red-400'
                    : 'bg-green-500/10 border border-green-500/20 text-green-400'
                }`}
              >
                {message.text}
              </div>
            )}

            {/* Close confirmation */}
            {isClosing && (
              <div className="mb-4 rounded-lg p-3 bg-yellow-500/10 border border-yellow-500/20 text-yellow-400 text-sm">
                <p className="mb-2">Are you sure you want to close? Your changes will be lost.</p>
                <div className="flex justify-end space-x-2">
                  <button 
                    onClick={() => setIsClosing(false)}
                    className="px-3 py-1 rounded bg-white/5 hover:bg-white/10 transition-colors"
                  >
                    Cancel
                  </button>
                  <button 
                    onClick={handleSafeClose}
                    className="px-3 py-1 rounded bg-red-500/20 hover:bg-red-500/30 transition-colors"
                  >
                    Close
                  </button>
                </div>
              </div>
            )}

            {/* Auth forms */}
            <div className="space-y-6">
              {view === 'signin' && !isClosing && (
                <SignInForm
                  onSuccess={() => {
                    setMessage({ type: 'success', text: 'Successfully signed in!' });
                    setTimeout(onClose, 1500);
                  }}
                  onRegisterClick={() => handleViewChange('register')}
                  onForgotPassword={() => handleViewChange('forgot')}
                  setMessage={setMessage}
                />
              )}

              {view === 'register' && !isClosing && (
                <RegisterForm
                  onSuccess={() => {
                    setMessage({
                      type: 'success',
                      text: 'Registration successful! Please check your email to confirm your account.'
                    });
                    setTimeout(() => handleViewChange('signin'), 1500);
                  }}
                  onSignInClick={() => handleViewChange('signin')}
                  setMessage={setMessage}
                />
              )}

              {view === 'forgot' && !isClosing && (
                <ForgotPasswordForm
                  onSuccess={() => {
                    setMessage({
                      type: 'success',
                      text: 'Password reset instructions have been sent to your email.'
                    });
                    setTimeout(() => handleViewChange('signin'), 1500);
                  }}
                  onBackToSignIn={() => handleViewChange('signin')}
                  setMessage={setMessage}
                />
              )}
            </div>
          </motion.div>
        </div>
      </div>
    </AnimatePresence>
  );
} 