import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '@/contexts/AuthContext';
import SignInForm from './SignInForm';
import RegisterForm from './RegisterForm';
import ForgotPasswordForm from './ForgotPasswordForm';
import { XMarkIcon } from '@heroicons/react/24/outline';

const modalVariants = {
  hidden: {
    opacity: 0,
    scale: 0.95,
    y: -10
  },
  visible: {
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      type: "spring",
      duration: 0.3,
      bounce: 0.2
    }
  },
  exit: {
    opacity: 0,
    scale: 0.95,
    y: 10,
    transition: {
      duration: 0.2
    }
  }
};

const overlayVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      duration: 0.2
    }
  },
  exit: {
    opacity: 0,
    transition: {
      duration: 0.2
    }
  }
};

export default function AuthModal({ onClose, onAuthChange, initialMode = 'signin' }) {
  const [view, setView] = useState(initialMode);
  const [message, setMessage] = useState(null);
  const { error, clearError } = useAuth();
  const [mounted, setMounted] = useState(false);
  const [isExiting, setIsExiting] = useState(false);

  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  useEffect(() => {
    if (error) {
      setMessage({ type: 'error', text: error });
      clearError();
    }
  }, [error, clearError]);

  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        handleClose();
      }
    };
    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [onClose]);

  const handleViewChange = (newView) => {
    setMessage(null);
    setView(newView);
  };

  // Add a controlled closing mechanism
  const handleClose = () => {
    // Only close if we're showing a success message or if we're forcing a close
    if (message?.type === 'success') {
      onClose();
      return;
    }

    // Set exiting state to prevent multiple clicks
    setIsExiting(true);

    // Call the onClose callback directly
    onClose();
  };

  const modalContent = (
    <div className="fixed inset-0 z-[100] overflow-y-auto">
      <div className="min-h-screen px-4 text-center">
        {/* Background overlay */}
        <motion.div
          className="fixed inset-0 bg-black/70 backdrop-blur-sm"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.2 }}
          onClick={(e) => {
            // Only close if the background was clicked directly and we're not already exiting
            if (e.target === e.currentTarget && !isExiting) {
              handleClose();
            }
          }}
        />

        {/* This element is to trick the browser into centering the modal contents. */}
        <span
          className="inline-block h-screen align-middle"
          aria-hidden="true"
        >
          &#8203;
        </span>

        {/* Modal panel */}
        <motion.div
          className="inline-block w-full max-w-md p-6 my-8 text-left align-middle bg-gradient-to-b from-gray-900 to-gray-800 rounded-2xl shadow-xl transform transition-all"
          initial={{ opacity: 0, scale: 0.95, y: -10 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          transition={{ type: "spring", duration: 0.3, bounce: 0.2 }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Close button */}
          <button
            onClick={handleClose}
            className="absolute right-4 top-4 rounded-full p-1 text-gray-400 hover:bg-white/10 hover:text-white transition-colors"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>

          {/* Message display */}
          {message && (
            <div
              className={`mb-4 rounded-lg p-3 text-sm ${
                message.type === 'error'
                  ? 'bg-red-500/10 border border-red-500/20 text-red-400'
                  : 'bg-green-500/10 border border-green-500/20 text-green-400'
              }`}
            >
              {message.text}
            </div>
          )}

          {/* Auth forms */}
          <div className="space-y-6">
            {view === 'signin' && (
              <SignInForm
                onSuccess={() => {
                  setMessage({ type: 'success', text: 'Successfully signed in!' });
                  setTimeout(handleClose, 1500);
                }}
                onRegisterClick={() => handleViewChange('register')}
                onForgotPassword={() => handleViewChange('forgot')}
                setMessage={setMessage}
              />
            )}

            {view === 'register' && (
              <RegisterForm
                onSuccess={() => {
                  setMessage({
                    type: 'success',
                    text: 'Registration successful! Please check your email to confirm your account.'
                  });
                  setTimeout(() => handleViewChange('signin'), 1500);
                }}
                onSignInClick={() => handleViewChange('signin')}
                setMessage={setMessage}
              />
            )}

            {view === 'forgot' && (
              <ForgotPasswordForm
                onSuccess={() => {
                  setMessage({
                    type: 'success',
                    text: 'Password reset instructions have been sent to your email.'
                  });
                  setTimeout(() => handleViewChange('signin'), 1500);
                }}
                onBackToSignIn={() => handleViewChange('signin')}
                setMessage={setMessage}
              />
            )}
          </div>
        </motion.div>
      </div>
    </div>
  );

  if (!mounted) return null;

  return createPortal(modalContent, document.body);
}