import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card } from '../common';

export default function MirrorModeTool({ onBack }) {
  // State management
  const [isActive, setIsActive] = useState(false);
  const [results, setResults] = useState(null);
  const [stream, setStream] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [brushingSession, setBrushingSession] = useState(null);
  const [brushingTime, setBrushingTime] = useState(0);
  const [brushingScore, setBrushingScore] = useState(0);
  const [missedAreas, setMissedAreas] = useState([]);
  const [brushingTechnique, setBrushingTechnique] = useState('good');
  const [sessionHistory, setSessionHistory] = useState([]);
  const [currentStep, setCurrentStep] = useState('intro'); // intro, setup, active, results
  const [streakCount, setStreakCount] = useState(0);
  const [showHeatmap, setShowHeatmap] = useState(true);

  // References
  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const heatmapRef = useRef(null);
  const timerRef = useRef(null);
  const modelRef = useRef(null);

  // Constants for tracking and analysis
  const RECOMMENDED_BRUSHING_TIME = 120; // 2 minutes in seconds
  const MOUTH_REGIONS = [
    { id: 'upper-left', name: 'Upper Left', covered: false },
    { id: 'upper-front', name: 'Upper Front', covered: false },
    { id: 'upper-right', name: 'Upper Right', covered: false },
    { id: 'lower-left', name: 'Lower Left', covered: false },
    { id: 'lower-front', name: 'Lower Front', covered: false },
    { id: 'lower-right', name: 'Lower Right', covered: false }
  ];

  // Initialize video processing
  useEffect(() => {
    async function initializeVideoProcessing() {
      try {
        // In a real implementation, this would initialize ML models
        console.log('Video processing initialized');
      } catch (err) {
        console.error('Failed to initialize video processing:', err);
        setError('Failed to initialize AI components. Please try again.');
      }
    }

    initializeVideoProcessing();

    // Cleanup function
    return () => {
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
      }
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);

  // Start webcam function
  const startWebcam = async () => {
    try {
      setError(null);
      setLoading(true);

      // Stop any existing stream
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
      }

      // Set up video constraints for front-facing camera with high quality
      const constraints = {
        video: {
          facingMode: 'user',
          width: { ideal: 1280, min: 640 },
          height: { ideal: 720, min: 480 },
          frameRate: { ideal: 30 }
        }
      };

      // Request camera access
      const mediaStream = await navigator.mediaDevices.getUserMedia(constraints);

      setStream(mediaStream);
      setLoading(false);

      // Set video source and prepare canvas
      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
        videoRef.current.onloadedmetadata = () => {
          videoRef.current.play();
          prepareCanvas();
          setCurrentStep('setup');
        };
      }
    } catch (err) {
      console.error('Error accessing camera:', err);
      setError('Unable to access camera. Please ensure camera permissions are granted and no other application is using your camera.');
      setLoading(false);
    }
  };

  // Prepare canvas for drawing
  const prepareCanvas = () => {
    if (videoRef.current && canvasRef.current && heatmapRef.current) {
      const video = videoRef.current;
      const canvas = canvasRef.current;
      const heatmap = heatmapRef.current;

      // Set canvas dimensions to match video
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      heatmap.width = video.videoWidth;
      heatmap.height = video.videoHeight;

      // Initialize canvas context
      const ctx = canvas.getContext('2d');
      ctx.strokeStyle = '#00FFFF';
      ctx.lineWidth = 3;

      // Initialize heatmap
      const heatmapCtx = heatmap.getContext('2d');
      heatmapCtx.fillStyle = 'rgba(255, 0, 0, 0.3)';
    }
  };

  // Start brushing session
  const startBrushingSession = () => {
    setBrushingTime(0);
    setBrushingScore(0);
    setMissedAreas(MOUTH_REGIONS.map(region => ({ ...region })));
    setBrushingTechnique('good');
    setIsActive(true);
    setCurrentStep('active');

    // Start timer
    timerRef.current = setInterval(() => {
      setBrushingTime(prevTime => {
        if (prevTime >= RECOMMENDED_BRUSHING_TIME) {
          clearInterval(timerRef.current);
          finishBrushingSession();
          return prevTime;
        }
        return prevTime + 1;
      });
    }, 1000);

    // Start frame analysis
    requestAnimationFrame(analyzeFrame);
  };

  // Function to be called in future implementation
  const analyzeFrame = () => {
    if (!isActive || !videoRef.current || !canvasRef.current || !heatmapRef.current) return;

    // In a real implementation, this would use TensorFlow for analysis
    // Here we're simulating analysis with random data

    // Request next frame
    if (isActive) {
      requestAnimationFrame(analyzeFrame);
    }
  };

  // Finish brushing session
  const finishBrushingSession = () => {
    clearInterval(timerRef.current);
    setIsActive(false);

    // Generate results
    const completeness = Math.floor(Math.random() * 30) + 70; // 70-100%
    const technique = Math.random() > 0.3 ? 'good' : 'needs improvement';
    const pressure = Math.random() > 0.7 ? 'too hard' : 'appropriate';

    // Update streak count
    const newStreakCount = completeness > 80 ? streakCount + 1 : 0;
    setStreakCount(newStreakCount);

    // Create session result
    const sessionResult = {
      id: Date.now(),
      date: new Date().toISOString(),
      duration: brushingTime,
      completeness,
      technique,
      pressure,
      missedAreas: missedAreas.filter(area => !area.covered).map(area => area.name),
      streak: newStreakCount
    };

    // Update session history
    setSessionHistory(prev => [...prev, sessionResult]);
    setResults(sessionResult);
    setCurrentStep('results');
  };

  // Reset and start a new session
  const startNewSession = () => {
    setResults(null);
    setCurrentStep('intro');
  };

  // Render intro screen
  const renderIntroScreen = () => (
    <div className="text-center py-8">
      <motion.div
        className="w-32 h-32 mx-auto mb-6 relative"
        animate={{
          scale: [1, 1.05, 1],
          opacity: [0.8, 1, 0.8]
        }}
        transition={{
          repeat: Infinity,
          duration: 3
        }}
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="w-full h-full text-cyan-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
        </svg>
      </motion.div>

      <h3 className="text-2xl font-medium text-white mb-4">AI Mirror Mode</h3>
      <p className="text-white/70 max-w-2xl mx-auto mb-8">
        Transform your device into a smart dental mirror with real-time AI coaching for brushing and flossing.
        Get feedback on technique, track missed areas, and build healthy habits.
      </p>

      <motion.button
        onClick={startWebcam}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        className="px-8 py-3 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-lg text-white font-medium shadow-lg hover:shadow-cyan-500/20 transition"
      >
        Start Mirror Mode
      </motion.button>
    </div>
  );

  // Render setup screen
  const renderSetupScreen = () => (
    <div className="py-6">
      <h3 className="text-xl font-medium text-white mb-4 text-center">Position Your Camera</h3>

      <div className="relative w-full max-w-3xl mx-auto mb-6 rounded-xl overflow-hidden bg-gray-900 shadow-lg">
        <video
          ref={videoRef}
          className="w-full h-auto"
          autoPlay
          playsInline
          muted
        />
        <canvas
          ref={canvasRef}
          className="absolute inset-0 w-full h-full pointer-events-none"
        />
        <canvas
          ref={heatmapRef}
          className={`absolute inset-0 w-full h-full pointer-events-none ${showHeatmap ? 'opacity-50' : 'opacity-0'}`}
        />

        {loading && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/70">
            <div className="text-cyan-400 flex items-center">
              <svg className="animate-spin h-8 w-8 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <span>Loading camera...</span>
            </div>
          </div>
        )}

        {error && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/70">
            <div className="text-red-400 text-center max-w-md p-4">
              <svg className="h-12 w-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
              <div className="text-lg font-medium mb-2">Camera Error</div>
              <p>{error}</p>
            </div>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700">
          <h4 className="text-lg font-medium text-white mb-3">For Best Results</h4>
          <ul className="space-y-2 text-white/70">
            <li className="flex items-start">
              <svg className="w-5 h-5 text-cyan-400 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Position your device at eye level and 12-18 inches away
            </li>
            <li className="flex items-start">
              <svg className="w-5 h-5 text-cyan-400 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Ensure good lighting so your face is clearly visible
            </li>
            <li className="flex items-start">
              <svg className="w-5 h-5 text-cyan-400 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Have your toothbrush ready before starting
            </li>
          </ul>
        </div>

        <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700">
          <h4 className="text-lg font-medium text-white mb-3">What to Expect</h4>
          <ul className="space-y-2 text-white/70">
            <li className="flex items-start">
              <svg className="w-5 h-5 text-cyan-400 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              AI will track your brushing motion and coverage
            </li>
            <li className="flex items-start">
              <svg className="w-5 h-5 text-cyan-400 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Red overlay will show missed areas in real-time
            </li>
            <li className="flex items-start">
              <svg className="w-5 h-5 text-cyan-400 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              You'll receive guidance on technique and timing
            </li>
          </ul>
        </div>
      </div>

      <div className="flex justify-between">
        <button
          onClick={() => setCurrentStep('intro')}
          className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg text-white/80 font-medium transition"
        >
          Back
        </button>

        <button
          onClick={startBrushingSession}
          className="px-6 py-2 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-lg text-white font-medium hover:shadow-lg hover:shadow-cyan-500/20 transition"
        >
          Start Brushing
        </button>
      </div>
    </div>
  );

  // Render active brushing screen
  const renderActiveScreen = () => (
    <div className="py-6">
      <div className="flex justify-between items-center mb-4">
        <div className="text-xl font-medium text-white">Brushing Session Active</div>
        <div className="text-cyan-400 text-lg font-mono">
          {Math.floor(brushingTime / 60).toString().padStart(2, '0')}:{(brushingTime % 60).toString().padStart(2, '0')}
        </div>
      </div>

      <div className="relative w-full max-w-3xl mx-auto mb-6 rounded-xl overflow-hidden bg-gray-900 shadow-lg">
        <video
          ref={videoRef}
          className="w-full h-auto"
          autoPlay
          playsInline
          muted
        />
        <canvas
          ref={canvasRef}
          className="absolute inset-0 w-full h-full pointer-events-none"
        />
        <canvas
          ref={heatmapRef}
          className={`absolute inset-0 w-full h-full pointer-events-none ${showHeatmap ? 'opacity-50' : 'opacity-0'}`}
        />

        {/* Timer progress bar */}
        <div className="absolute top-0 left-0 right-0 h-2 bg-gray-800">
          <motion.div
            className="h-full bg-gradient-to-r from-cyan-500 to-blue-500"
            initial={{ width: '0%' }}
            animate={{ width: `${Math.min(100, (brushingTime / RECOMMENDED_BRUSHING_TIME) * 100)}%` }}
            transition={{ duration: 0.5 }}
          />
        </div>

        {/* Real-time feedback banner */}
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
          <div className="flex items-center">
            <div className="w-8 h-8 rounded-full bg-cyan-500/20 flex items-center justify-center mr-3">
              <svg className="w-5 h-5 text-cyan-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="text-white">
              {brushingTime < 10 && "Position your toothbrush to start brushing"}
              {brushingTime >= 10 && brushingTime < 30 && "Great! Now focus on your upper-right teeth"}
              {brushingTime >= 30 && brushingTime < 60 && "Move to your upper-front teeth"}
              {brushingTime >= 60 && brushingTime < 90 && "Now brush your upper-left teeth"}
              {brushingTime >= 90 && brushingTime < 120 && "Move to your lower teeth - front and sides"}
              {brushingTime >= 120 && "Well done! You've completed the recommended brushing time"}
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-3 gap-3 mb-6">
        {MOUTH_REGIONS.map((region, index) => (
          <div
            key={region.id}
            className={`rounded-lg p-3 ${
              (brushingTime > index * 20) ? 'bg-green-500/20 border border-green-500/30' : 'bg-gray-800/50 border border-gray-700'
            }`}
          >
            <div className="flex items-center">
              {(brushingTime > index * 20) ? (
                <svg className="w-5 h-5 text-green-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              ) : (
                <svg className="w-5 h-5 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              )}
              <span className={`text-sm ${(brushingTime > index * 20) ? 'text-green-400' : 'text-white/70'}`}>
                {region.name}
              </span>
            </div>
          </div>
        ))}
      </div>

      <div className="flex justify-between">
        <button
          onClick={() => {
            clearInterval(timerRef.current);
            setIsActive(false);
            setCurrentStep('setup');
          }}
          className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg text-white/80 font-medium transition"
        >
          Cancel
        </button>

        <button
          onClick={finishBrushingSession}
          className="px-6 py-2 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-lg text-white font-medium hover:shadow-lg hover:shadow-cyan-500/20 transition"
        >
          Finish
        </button>
      </div>
    </div>
  );

  // Render results screen
  const renderResultsScreen = () => (
    <div className="py-6">
      <div className="mb-8 text-center">
        <div className="inline-block rounded-full bg-cyan-500/20 p-6 mb-4">
          <svg className="w-12 h-12 text-cyan-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <h3 className="text-2xl font-medium text-white mb-2">Brushing Session Complete!</h3>
        <p className="text-white/70">
          {brushingTime >= RECOMMENDED_BRUSHING_TIME ?
            "Great job! You've reached the recommended brushing time." :
            "Good effort! Try to brush for the full 2 minutes next time."}
        </p>

        {results?.streak > 0 && (
          <div className="mt-4 inline-flex items-center bg-amber-500/20 px-4 py-2 rounded-full">
            <svg className="w-5 h-5 text-amber-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            <span className="text-amber-400 font-medium">{results.streak} day streak!</span>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div className="bg-gray-800/50 rounded-lg p-5 border border-gray-700">
          <h4 className="text-lg font-medium text-white mb-4">Session Stats</h4>

          <div className="space-y-4">
            <div>
              <div className="flex justify-between text-sm text-white/70 mb-1">
                <span>Brushing Time</span>
                <span>{Math.floor(brushingTime / 60)}:{(brushingTime % 60).toString().padStart(2, '0')}</span>
              </div>
              <div className="w-full h-2 bg-gray-700 rounded-full overflow-hidden">
                <div
                  className="h-full bg-gradient-to-r from-cyan-500 to-blue-500 rounded-full"
                  style={{ width: `${Math.min(100, (brushingTime / RECOMMENDED_BRUSHING_TIME) * 100)}%` }}
                />
              </div>
            </div>

            <div>
              <div className="flex justify-between text-sm text-white/70 mb-1">
                <span>Coverage</span>
                <span>{results?.completeness || 0}%</span>
              </div>
              <div className="w-full h-2 bg-gray-700 rounded-full overflow-hidden">
                <div
                  className="h-full bg-gradient-to-r from-cyan-500 to-blue-500 rounded-full"
                  style={{ width: `${results?.completeness || 0}%` }}
                />
              </div>
            </div>

            <div>
              <div className="flex justify-between text-sm text-white/70 mb-1">
                <span>Technique</span>
                <span className={results?.technique === 'good' ? 'text-green-400' : 'text-amber-400'}>
                  {results?.technique === 'good' ? 'Good' : 'Needs Improvement'}
                </span>
              </div>
            </div>

            <div>
              <div className="flex justify-between text-sm text-white/70 mb-1">
                <span>Pressure</span>
                <span className={results?.pressure === 'appropriate' ? 'text-green-400' : 'text-amber-400'}>
                  {results?.pressure === 'appropriate' ? 'Appropriate' : 'Too Hard'}
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-gray-800/50 rounded-lg p-5 border border-gray-700">
          <h4 className="text-lg font-medium text-white mb-4">Recommendations</h4>

          <ul className="space-y-3">
            {results?.missedAreas.length > 0 ? (
              <li className="flex items-start">
                <svg className="w-5 h-5 text-amber-400 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                <span className="text-white/90">
                  Focus more on {results.missedAreas.join(', ')} next time
                </span>
              </li>
            ) : (
              <li className="flex items-start">
                <svg className="w-5 h-5 text-green-400 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span className="text-white/90">
                  Great coverage! You've reached all areas of your mouth.
                </span>
              </li>
            )}

            {results?.technique !== 'good' && (
              <li className="flex items-start">
                <svg className="w-5 h-5 text-amber-400 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                <span className="text-white/90">
                  Use small circular motions at a 45-degree angle to your gums
                </span>
              </li>
            )}

            {results?.pressure !== 'appropriate' && (
              <li className="flex items-start">
                <svg className="w-5 h-5 text-amber-400 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                <span className="text-white/90">
                  Try using lighter pressure - just enough to feel the bristles
                </span>
              </li>
            )}

            {brushingTime < RECOMMENDED_BRUSHING_TIME && (
              <li className="flex items-start">
                <svg className="w-5 h-5 text-amber-400 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                <span className="text-white/90">
                  Aim for 2 full minutes of brushing (you did {Math.floor(brushingTime / 60)}:{(brushingTime % 60).toString().padStart(2, '0')})
                </span>
              </li>
            )}

            <li className="flex items-start">
              <svg className="w-5 h-5 text-cyan-400 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="text-white/90">
                Don't forget to floss daily and clean your tongue
              </span>
            </li>
          </ul>
        </div>
      </div>

      {sessionHistory.length > 1 && (
        <div className="bg-gray-800/50 rounded-lg p-5 border border-gray-700 mb-8">
          <h4 className="text-lg font-medium text-white mb-4">Progress Tracker</h4>

          <div className="h-32 flex items-end space-x-2">
            {sessionHistory.slice(-7).map((session, index) => (
              <div key={index} className="flex-1 flex flex-col items-center">
                <div className="text-xs text-white/50 mb-1">{new Date(session.date).toLocaleDateString('en-US', { weekday: 'short' })}</div>
                <motion.div
                  className="w-full bg-gradient-to-t from-cyan-500 to-blue-500 rounded-t opacity-80"
                  initial={{ height: 0 }}
                  animate={{ height: `${session.completeness / 100 * 100}%` }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                />
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="flex justify-center">
        <button
          onClick={startNewSession}
          className="px-6 py-2 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-lg text-white font-medium hover:shadow-lg hover:shadow-cyan-500/20 transition"
        >
          Start New Session
        </button>
      </div>
    </div>
  );

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="w-full max-w-5xl mx-auto"
    >
      <Card className="p-6 md:p-8 w-full">
        <div className="flex flex-col items-center mb-8">
          <div className="w-16 h-16 mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="w-full h-full text-cyan-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
          </div>
          <h2 className="text-2xl md:text-3xl font-bold text-white text-center mb-2">
            AI Mirror Mode - Dental Coach
          </h2>
          <p className="text-white/70 text-center max-w-2xl">
            Get real-time AI coaching while you brush and floss using just your webcam.
            Track progress, identify missed areas, and improve your technique.
          </p>
        </div>

        <div className="flex justify-end">
          <button
            onClick={onBack}
            className="flex items-center text-white/70 hover:text-white transition"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
            </svg>
            Back to Tools
          </button>
        </div>

        <AnimatePresence mode="wait">
          {currentStep === 'intro' && (
            <motion.div
              key="intro"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              {renderIntroScreen()}
            </motion.div>
          )}

          {currentStep === 'setup' && (
            <motion.div
              key="setup"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              {renderSetupScreen()}
            </motion.div>
          )}

          {currentStep === 'active' && (
            <motion.div
              key="active"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              {renderActiveScreen()}
            </motion.div>
          )}

          {currentStep === 'results' && (
            <motion.div
              key="results"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              {renderResultsScreen()}
            </motion.div>
          )}
        </AnimatePresence>
      </Card>
    </motion.div>
  );
}