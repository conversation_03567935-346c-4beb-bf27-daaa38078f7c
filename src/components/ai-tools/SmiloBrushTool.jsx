import React from 'react';
import { motion } from 'framer-motion';
import BrushingAnalysisService from '../bluetooth/BrushingAnalysisService';
import SmiloBrushSync from '../bluetooth/SmiloBrushSync';
import { Card } from '../common';

const SmiloBrushTool = ({ onBack }) => {
  return (
    <div className="space-y-6">
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <button
          onClick={onBack}
          className="inline-flex items-center text-sm text-white/70 hover:text-white transition-colors mb-4"
        >
          <svg className="w-4 h-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 111.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
          </svg>
          Back to Tools
        </button>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4, delay: 0.1 }}
      >
        <Card className="overflow-hidden shadow-xl">
          <div className="p-6">
            <h2 className="text-2xl font-bold text-white mb-4">SmiloBrush AI Analysis</h2>
            <p className="text-white/70 mb-6">
              Connect your SmiloBrush smart toothbrush to sync brushing data, analyze your oral health, and receive personalized AI recommendations.
            </p>

            <BrushingAnalysisService>
              <div className="space-y-6">
                <SmiloBrushSync userId="demo-user-123" />
                
                <div className="bg-indigo-50/10 p-4 rounded-lg border border-indigo-500/20 mt-6">
                  <h3 className="text-lg font-medium text-white flex items-center">
                    <svg className="w-5 h-5 mr-2 text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    How It Works
                  </h3>
                  <div className="mt-3 text-white/70 space-y-2 text-sm">
                    <p>1. Connect your SmiloBrush via Bluetooth</p>
                    <p>2. Sync your brushing sessions data</p>
                    <p>3. AI analyzes your brushing patterns and captured images</p>
                    <p>4. Receive personalized recommendations for optimal oral health</p>
                  </div>
                </div>
                
                <div className="bg-gradient-to-r from-indigo-500/20 to-blue-500/20 p-4 rounded-lg">
                  <h3 className="text-lg font-medium text-white flex items-center">
                    <svg className="w-5 h-5 mr-2 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                    Advanced Detection
                  </h3>
                  <div className="mt-2 grid grid-cols-2 gap-2">
                    <div className="bg-blue-500/10 p-2 rounded-lg flex items-center">
                      <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                      <span className="text-white/90 text-sm">Cavity Detection</span>
                    </div>
                    <div className="bg-blue-500/10 p-2 rounded-lg flex items-center">
                      <div className="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
                      <span className="text-white/90 text-sm">Plaque Analysis</span>
                    </div>
                    <div className="bg-blue-500/10 p-2 rounded-lg flex items-center">
                      <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                      <span className="text-white/90 text-sm">Gum Inflammation</span>
                    </div>
                    <div className="bg-blue-500/10 p-2 rounded-lg flex items-center">
                      <div className="w-3 h-3 bg-purple-500 rounded-full mr-2"></div>
                      <span className="text-white/90 text-sm">Tooth Movement</span>
                    </div>
                  </div>
                </div>
              </div>
            </BrushingAnalysisService>
          </div>
        </Card>
      </motion.div>
    </div>
  );
};

export default SmiloBrushTool; 