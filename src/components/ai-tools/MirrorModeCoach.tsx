import React, { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { tf } from '../../lib/tensorflow-import';
import * as facemesh from '@mediapipe/face_mesh';

interface MouthRegion {
  id: string;
  name: string;
  isCovered: boolean;
  needsAttention: boolean;
}

const MirrorModeCoach: React.FC = () => {
  // State for webcam and detection
  const [isWebcamActive, setIsWebcamActive] = useState(false);
  const [isModelLoaded, setIsModelLoaded] = useState(false);
  const [detectionError, setDetectionError] = useState<string | null>(null);

  // State for brushing session
  const [isSessionActive, setIsSessionActive] = useState(false);
  const [sessionTime, setSessionTime] = useState(0);
  const [currentFeedback, setCurrentFeedback] = useState('Starting brushing session...');

  // State for tracking mouth regions
  const [mouthRegions, setMouthRegions] = useState<MouthRegion[]>([
    { id: 'upper-left', name: 'Upper Left Molars', isCovered: false, needsAttention: false },
    { id: 'upper-front', name: 'Upper Front Teeth', isCovered: false, needsAttention: false },
    { id: 'upper-right', name: 'Upper Right Molars', isCovered: false, needsAttention: false },
    { id: 'lower-left', name: 'Lower Left Molars', isCovered: false, needsAttention: false },
    { id: 'lower-front', name: 'Lower Front Teeth', isCovered: false, needsAttention: false },
    { id: 'lower-right', name: 'Lower Right Molars', isCovered: false, needsAttention: false }
  ]);

  // References
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const overlayCanvasRef = useRef<HTMLCanvasElement>(null);
  const modelRef = useRef<any>(null);
  const animationFrameRef = useRef<number | null>(null);
  const timerIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Constants
  const RECOMMENDED_BRUSHING_TIME = 120; // 2 minutes in seconds
  const REGION_COVERAGE_THRESHOLD = 5; // Number of frames to consider a region covered
  const DETECTION_INTERVAL = 100; // milliseconds between face detections

  // Setup TensorFlow and initialize model
  useEffect(() => {
    const setupTensorFlow = async () => {
      try {
        await tf.ready();
        console.log('TensorFlow.js is ready');

        // Load face landmarks detection model
        modelRef.current = await facemesh.createDetector(
          facemesh.SupportedModels.MediaPipeFaceMesh,
          { runtime: 'tfjs' }
        );

        setIsModelLoaded(true);
        console.log('Face detection model loaded');
      } catch (error) {
        console.error('Failed to initialize TensorFlow or load model:', error);
        setDetectionError('Failed to initialize facial tracking. Please try reloading the page.');
      }
    };

    setupTensorFlow();

    // Cleanup function
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      if (timerIntervalRef.current) {
        clearInterval(timerIntervalRef.current);
      }
      stopWebcam();
    };
  }, []);

  // Start webcam function
  const startWebcam = async () => {
    try {
      setDetectionError(null);

      // Request camera access
      const constraints = {
        video: {
          facingMode: 'user',
          width: { ideal: 640 },
          height: { ideal: 480 }
        }
      };

      const stream = await navigator.mediaDevices.getUserMedia(constraints);

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        await videoRef.current.play();

        // Set canvas dimensions to match video
        if (canvasRef.current && overlayCanvasRef.current) {
          canvasRef.current.width = videoRef.current.videoWidth;
          canvasRef.current.height = videoRef.current.videoHeight;
          overlayCanvasRef.current.width = videoRef.current.videoWidth;
          overlayCanvasRef.current.height = videoRef.current.videoHeight;
        }

        setIsWebcamActive(true);
      }
    } catch (error) {
      console.error('Error accessing camera:', error);
      setDetectionError('Unable to access camera. Please ensure you have granted camera permissions.');
    }
  };

  // Stop webcam function
  const stopWebcam = () => {
    if (videoRef.current && videoRef.current.srcObject) {
      const tracks = (videoRef.current.srcObject as MediaStream).getTracks();
      tracks.forEach(track => track.stop());
      videoRef.current.srcObject = null;
      setIsWebcamActive(false);
    }
  };

  // Start brushing session
  const startBrushingSession = () => {
    // Reset session state
    setSessionTime(0);
    setMouthRegions(regions => regions.map(region => ({
      ...region,
      isCovered: false,
      needsAttention: false
    })));
    setIsSessionActive(true);
    setCurrentFeedback('Position your toothbrush and start brushing slowly');

    // Start timer
    timerIntervalRef.current = setInterval(() => {
      setSessionTime(prev => {
        // Update feedback based on time
        const newTime = prev + 1;
        updateFeedbackBasedOnTime(newTime);

        // Set random region to need attention periodically
        if (newTime % 20 === 0) {
          setRandomRegionNeedsAttention();
        }

        // End session after recommended time
        if (newTime >= RECOMMENDED_BRUSHING_TIME) {
          finishBrushingSession();
        }

        return newTime;
      });
    }, 1000);

    // Start detection loop
    startDetectionLoop();
  };

  // Update feedback based on brushing time
  const updateFeedbackBasedOnTime = (time: number) => {
    if (time < 10) {
      setCurrentFeedback('Position your toothbrush and start brushing slowly');
    } else if (time < 30) {
      setCurrentFeedback('Great! Focus on your upper teeth');
    } else if (time < 60) {
      setCurrentFeedback('Now brush your molars with circular motions');
    } else if (time < 90) {
      setCurrentFeedback('Move to your lower teeth');
    } else if (time < 110) {
      setCurrentFeedback('Don\'t forget the inside surfaces of your teeth');
    } else {
      setCurrentFeedback('Almost done! Final pass on all surfaces');
    }
  };

  // Set a random region to need attention
  const setRandomRegionNeedsAttention = () => {
    setMouthRegions(regions => {
      const uncoveredRegions = regions.filter(r => !r.isCovered);
      if (uncoveredRegions.length === 0) return regions;

      const randomIndex = Math.floor(Math.random() * uncoveredRegions.length);
      const targetId = uncoveredRegions[randomIndex].id;

      return regions.map(region =>
        region.id === targetId
          ? { ...region, needsAttention: true }
          : region
      );
    });
  };

  // Face detection loop
  const startDetectionLoop = () => {
    if (!modelRef.current || !videoRef.current || !isModelLoaded) return;

    let lastDetectionTime = 0;

    const detectFace = async (timestamp: number) => {
      if (!isSessionActive) return;

      // Throttle detections to improve performance
      if (timestamp - lastDetectionTime > DETECTION_INTERVAL) {
        lastDetectionTime = timestamp;

        try {
          // Detect face landmarks
          const predictions = await modelRef.current.estimateFaces(videoRef.current);

          if (predictions.length > 0) {
            // We have a face! Process the landmarks
            const landmarks = predictions[0].keypoints;
            processFaceLandmarks(landmarks);
          }
        } catch (error) {
          console.error('Error during face detection:', error);
        }
      }

      // Draw overlay
      drawOverlay();

      // Continue loop
      animationFrameRef.current = requestAnimationFrame(detectFace);
    };

    animationFrameRef.current = requestAnimationFrame(detectFace);
  };

  // Process face landmarks to track brushing coverage
  const processFaceLandmarks = (landmarks: any[]) => {
    // In a full implementation, we would use the landmarks to:
    // 1. Detect mouth position and size
    // 2. Track toothbrush position relative to mouth regions
    // 3. Update region coverage based on brush movement

    // Simulated brush detection for demo purposes
    const simulatedBrushPosition = {
      x: Math.random() * (videoRef.current?.videoWidth || 640),
      y: Math.random() * (videoRef.current?.videoHeight || 480)
    };

    // Find the mouth region
    const mouthLandmarks = landmarks.filter(l =>
      l.name && (l.name.includes('lips') || l.name.includes('mouth'))
    );

    if (mouthLandmarks.length > 0) {
      // Determine which region the simulated brush is in
      // In this demo we'll just randomly mark regions as covered over time
      if (Math.random() > 0.9) {
        markRandomRegionAsCovered();
      }

      // If there's a region that needs attention, prioritize feedback about it
      const regionNeedingAttention = mouthRegions.find(r => r.needsAttention);
      if (regionNeedingAttention) {
        setCurrentFeedback(`Brush your ${regionNeedingAttention.name} more thoroughly`);
      }
    }
  };

  // Mark a random mouth region as covered
  const markRandomRegionAsCovered = () => {
    setMouthRegions(regions => {
      const uncoveredRegions = regions.filter(r => !r.isCovered);
      if (uncoveredRegions.length === 0) return regions;

      const randomIndex = Math.floor(Math.random() * uncoveredRegions.length);
      const targetId = uncoveredRegions[randomIndex].id;

      return regions.map(region =>
        region.id === targetId
          ? { ...region, isCovered: true, needsAttention: false }
          : region
      );
    });
  };

  // Draw overlay with feedback and highlighting
  const drawOverlay = () => {
    const overlayCanvas = overlayCanvasRef.current;
    if (!overlayCanvas) return;

    const ctx = overlayCanvas.getContext('2d');
    if (!ctx) return;

    // Clear previous drawings
    ctx.clearRect(0, 0, overlayCanvas.width, overlayCanvas.height);

    // Mirror the context for selfie view
    ctx.save();
    ctx.scale(-1, 1);
    ctx.translate(-overlayCanvas.width, 0);

    // Draw mirrored video frame (not needed in this demo as we show the video element directly)

    // Draw mouth region indicators
    // For demonstration, we'll just draw color-coded rectangles in the mouth region

    const canvasWidth = overlayCanvas.width;
    const canvasHeight = overlayCanvas.height;

    // Approximate mouth area based on typical face proportions
    const mouthAreaWidth = canvasWidth * 0.4;
    const mouthAreaHeight = canvasHeight * 0.15;
    const mouthAreaLeft = (canvasWidth - mouthAreaWidth) / 2;
    const mouthAreaTop = canvasHeight * 0.65;

    // Define regions within mouth area
    const regionWidth = mouthAreaWidth / 3;
    const regionHeight = mouthAreaHeight / 2;

    // Draw regions with different colors based on coverage
    mouthRegions.forEach((region, index) => {
      const row = Math.floor(index / 3);
      const col = index % 3;

      const x = mouthAreaLeft + col * regionWidth;
      const y = mouthAreaTop + row * regionHeight;

      // Set color based on region status
      if (region.needsAttention) {
        ctx.fillStyle = 'rgba(255, 50, 50, 0.5)'; // Red for attention needed
      } else if (region.isCovered) {
        ctx.fillStyle = 'rgba(50, 255, 50, 0.3)'; // Green for covered
      } else {
        ctx.fillStyle = 'rgba(255, 255, 50, 0.3)'; // Yellow for not yet covered
      }

      ctx.fillRect(x, y, regionWidth, regionHeight);
      ctx.strokeStyle = 'rgba(255, 255, 255, 0.7)';
      ctx.strokeRect(x, y, regionWidth, regionHeight);
    });

    // Restore context
    ctx.restore();
  };

  // Finish brushing session
  const finishBrushingSession = () => {
    if (timerIntervalRef.current) {
      clearInterval(timerIntervalRef.current);
    }
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }

    setIsSessionActive(false);
    setCurrentFeedback('Brushing session completed! Great job!');
  };

  // Calculate progress percentage
  const calculateProgress = () => {
    return Math.min(100, (sessionTime / RECOMMENDED_BRUSHING_TIME) * 100);
  };

  // Calculate coverage percentage
  const calculateCoverage = () => {
    const coveredCount = mouthRegions.filter(r => r.isCovered).length;
    return Math.round((coveredCount / mouthRegions.length) * 100);
  };

  return (
    <div className="w-full max-w-4xl mx-auto p-4 bg-gray-900 rounded-xl shadow-lg">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-white mb-2">Smart Mirror Brushing Coach</h2>
        <p className="text-gray-300">
          Use your camera as a smart mirror while brushing. Our AI will track your brushing and provide real-time feedback.
        </p>
      </div>

      <div className="relative w-full aspect-video bg-black rounded-lg overflow-hidden mb-6">
        {/* Main video feed (mirrored) */}
        <video
          ref={videoRef}
          className="absolute inset-0 w-full h-full object-cover transform scale-x-[-1]"
          autoPlay
          playsInline
          muted
        />

        {/* Face detection canvas */}
        <canvas
          ref={canvasRef}
          className="absolute inset-0 w-full h-full object-cover pointer-events-none"
        />

        {/* Overlay canvas for feedback */}
        <canvas
          ref={overlayCanvasRef}
          className="absolute inset-0 w-full h-full object-cover pointer-events-none"
        />

        {/* Feedback overlay at bottom */}
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
          <p className="text-white text-lg font-medium text-center">
            {currentFeedback}
          </p>
        </div>

        {/* Timer and progress at top */}
        {isSessionActive && (
          <div className="absolute top-0 left-0 right-0 p-2 bg-black/50">
            <div className="flex justify-between items-center px-2">
              <div className="text-white font-mono">
                {Math.floor(sessionTime / 60).toString().padStart(2, '0')}:
                {(sessionTime % 60).toString().padStart(2, '0')}
              </div>
              <div className="text-white">
                Coverage: {calculateCoverage()}%
              </div>
            </div>
            <div className="w-full h-2 bg-gray-700 rounded-full mt-1">
              <motion.div
                className="h-full bg-gradient-to-r from-cyan-500 to-blue-500 rounded-full"
                style={{ width: `${calculateProgress()}%` }}
              />
            </div>
          </div>
        )}

        {/* Start overlay for webcam activation */}
        {!isWebcamActive && (
          <div className="absolute inset-0 flex flex-col items-center justify-center bg-black/75">
            <h3 className="text-xl font-medium text-white mb-4">
              Begin Your Smart Brushing Session
            </h3>
            <button
              onClick={startWebcam}
              className="px-6 py-3 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-lg text-white font-medium shadow-lg hover:shadow-cyan-500/20 transition"
              disabled={!isModelLoaded}
            >
              {isModelLoaded ? "Enable Camera" : "Loading AI Model..."}
            </button>
            {detectionError && (
              <p className="text-red-400 mt-4 max-w-md text-center">
                {detectionError}
              </p>
            )}
          </div>
        )}
      </div>

      {/* Brushing regions status */}
      <div className="grid grid-cols-3 gap-2 mb-6">
        {mouthRegions.map((region) => (
          <div
            key={region.id}
            className={`p-3 rounded-lg border ${
              region.needsAttention
                ? 'bg-red-900/20 border-red-700'
                : region.isCovered
                  ? 'bg-green-900/20 border-green-700'
                  : 'bg-gray-800 border-gray-700'
            }`}
          >
            <div className="flex items-center">
              {region.isCovered ? (
                <svg className="w-5 h-5 text-green-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              ) : region.needsAttention ? (
                <svg className="w-5 h-5 text-red-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
              ) : (
                <svg className="w-5 h-5 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              )}
              <span className={`text-sm ${
                region.isCovered
                  ? 'text-green-400'
                  : region.needsAttention
                    ? 'text-red-400'
                    : 'text-white/70'
              }`}>
                {region.name}
              </span>
            </div>
          </div>
        ))}
      </div>

      {/* Controls */}
      <div className="flex justify-center">
        {isWebcamActive && !isSessionActive && (
          <button
            onClick={startBrushingSession}
            className="px-6 py-3 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-lg text-white font-medium shadow-lg hover:shadow-cyan-500/20 transition"
          >
            Start Brushing Session
          </button>
        )}

        {isSessionActive && (
          <button
            onClick={finishBrushingSession}
            className="px-6 py-3 bg-amber-500 rounded-lg text-white font-medium shadow-lg transition"
          >
            End Session Early
          </button>
        )}
      </div>
    </div>
  );
};

export default MirrorModeCoach;