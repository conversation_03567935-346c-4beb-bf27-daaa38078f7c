import React, { useState, useEffect, useRef, useCallback, memo } from 'react';
import { motion, useAnimation, AnimatePresence } from 'framer-motion';
import ContentSection from '../sections/ContentSection';
import WebcamOralScanner from './WebcamOralScanner';
import VoiceAnalysisTool from './VoiceAnalysisTool';
import ThermalImagingTool from './ThermalImagingTool';
import BreathAnalysisTool from './BreathAnalysisTool';
import MirrorModeTool from './MirrorModeTool';
import { Card } from '../common';

// Add CSS for animations and background patterns
const gridPatternStyle = {
  backgroundImage: `
    linear-gradient(to right, rgba(60, 60, 80, 0.05) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(60, 60, 80, 0.05) 1px, transparent 1px)
  `,
  backgroundSize: '40px 40px',
};

const floatAnimation = {
  "0%": { transform: "translateY(0px)" },
  "50%": { transform: "translateY(-10px)" },
  "100%": { transform: "translateY(0px)" }
};

// Tool types
const TOOLS = {
  WEBCAM: 'webcam',
  VOICE: 'voice',
  THERMAL: 'thermal',
  BREATH: 'breath',
  MIRROR: 'mirror'
};

// Tool data array for carousel
const toolsData = [
  {
    id: TOOLS.WEBCAM,
    title: 'AI-Powered Webcam Oral Scanner',
    description: 'Uses your webcam to detect potential dental issues by analyzing tooth color, gum inflammation, and plaque buildup.',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
      </svg>
    ),
    gradient: 'from-blue-500 to-indigo-600',
    hoverColor: 'text-blue-400',
    features: [
      'Real-time visual analysis',
      'Plaque and gum health detection',
      'Personalized oral care recommendations'
    ]
  },
  {
    id: TOOLS.MIRROR,
    title: 'AI Mirror Mode - Dental Coach',
    description: 'Turn your webcam into a smart dental mirror with real-time AI coaching while you brush and floss.',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
      </svg>
    ),
    gradient: 'from-emerald-500 to-teal-600',
    hoverColor: 'text-emerald-400',
    features: [
      'Real-time brushing feedback',
      'Heatmap shows missed areas',
      'Track habits & progress over time'
    ]
  },
  {
    id: TOOLS.VOICE,
    title: 'AI Voice Analysis',
    description: 'Analyzes your speech patterns to detect potential dental problems affecting pronunciation and oral function.',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
      </svg>
    ),
    gradient: 'from-purple-500 to-pink-600',
    hoverColor: 'text-purple-400',
    features: [
      'Speech clarity assessment',
      'Detection of jaw misalignment',
      'Dry mouth indicators analysis'
    ]
  },
  {
    id: TOOLS.THERMAL,
    title: 'Thermal Imaging Analysis',
    description: 'Uses your webcam to detect temperature variations in your mouth that may indicate inflammation or infection.',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 18.657A8 8 0 016.343 7.343S7 9 9 10c0-2 .5-5 2.986-7C14 5 16.09 5.777 17.656 7.343A7.975 7.975 0 0120 13a7.975 7.975 0 01-2.343 5.657z" />
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.879 16.121A3 3 0 1012.015 11L11 14H9c0 .768.293 1.536.879 2.121z" />
      </svg>
    ),
    gradient: 'from-red-500 to-amber-600',
    hoverColor: 'text-amber-400',
    features: [
      'Hotspot detection for inflammation',
      'Early-stage infection identification',
      'Oral inflammation mapping'
    ]
  },
  {
    id: TOOLS.BREATH,
    title: 'AI Breath Analysis',
    description: 'Analyzes breath patterns through your microphone to detect potential halitosis and underlying health issues.',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
      </svg>
    ),
    gradient: 'from-cyan-500 to-blue-500',
    hoverColor: 'text-cyan-400',
    features: [
      'Breath pattern analysis',
      'Oral hygiene assessment',
      'Digestive health indicators'
    ]
  }
];

// Memoized feature list item to reduce re-renders
const FeatureListItem = memo(({ feature, featureIdx, toolGradient }) => (
  <motion.li 
    key={featureIdx} 
    className="flex items-start group/feature"
    initial={{ opacity: 0, x: -15, y: 10 }}
    animate={{ opacity: 1, x: 0, y: 0 }}
    transition={{ 
      delay: 0.5 + (featureIdx * 0.1),
      type: "spring",
      stiffness: 50,
      damping: 8
    }}
    whileHover={{ x: 5 }}
  >
    <div className={`relative flex-shrink-0 w-8 h-8 rounded-lg bg-gradient-to-r ${toolGradient} mt-0.5 mr-4 flex items-center justify-center group-hover/feature:scale-110 transition-all duration-300 shadow-lg`}
      style={{
        boxShadow: `0 0 15px rgba(${
          toolGradient.includes('blue') ? '59, 130, 246' : 
          toolGradient.includes('purple') ? '168, 85, 247' : 
          toolGradient.includes('red') ? '245, 158, 11' : 
          '6, 182, 212'
        }, 0.3)`
      }}
    >
      {/* Inner glow effect */}
      <div className="absolute inset-0.5 rounded-md bg-gradient-to-br from-white/30 to-transparent opacity-60"></div>
      
      {/* Enhanced checkmark with animation */}
      <motion.svg 
        className="w-4 h-4 text-white relative z-10" 
        fill="none" 
        viewBox="0 0 24 24" 
        stroke="currentColor"
        strokeWidth={3}
        initial={{ pathLength: 0 }}
        animate={{ pathLength: 1 }}
        transition={{ 
          delay: 0.7 + (featureIdx * 0.1), 
          duration: 0.6, 
          ease: "easeOut" 
        }}
      >
        <motion.path 
          strokeLinecap="round" 
          strokeLinejoin="round" 
          d="M5 13l4 4L19 7"
          initial={{ pathLength: 0 }}
          animate={{ pathLength: 1 }}
          transition={{ 
            delay: 0.7 + (featureIdx * 0.1), 
            duration: 0.6, 
            ease: "easeOut" 
          }}
        />
      </motion.svg>
    </div>
    
    <div className="flex-1">
      <span className="text-white/90 font-medium group-hover/feature:text-white transition-colors duration-300 bg-gradient-to-r from-white to-white/80 bg-clip-text">
        {feature}
      </span>
      
      {/* Subtle animated underline on hover */}
      <motion.div 
        className={`h-px w-0 bg-gradient-to-r ${toolGradient} rounded-full mt-1 opacity-0 group-hover/feature:opacity-70 group-hover/feature:w-full transition-all duration-300`}
        initial={{ width: 0 }}
        whileHover={{ width: "100%" }}
      />
    </div>
  </motion.li>
));

// Memoized tool card component to prevent unnecessary re-renders
const ToolCard = memo(({ tool, isVisible, maxWidth, onSelect }) => {
  // Enhanced card transitions with spring physics
  const cardVariants = {
    enter: { 
      opacity: 0,
      scale: 0.95,
      y: 30,
      transition: { 
        type: "spring", 
        stiffness: 100, 
        damping: 15,
        mass: 1
      }
    },
    center: {
      opacity: 1,
      scale: 1,
      y: 0,
      transition: { 
        type: "spring", 
        stiffness: 100, 
        damping: 15,
        mass: 1,
        delay: 0.1
      }
    },
    exit: { 
      opacity: 0,
      scale: 0.95,
      y: -30,
      transition: { 
        type: "spring", 
        stiffness: 100, 
        damping: 15,
        mass: 1
      }
    }
  };

  return (
    <motion.div
      className="w-full absolute top-0 left-0 right-0"
      style={{ 
        maxWidth,
        margin: '0 auto',
        willChange: 'transform, opacity'
      }}
      variants={cardVariants}
      initial="enter"
      animate={isVisible ? "center" : "enter"}
      exit="exit"
      layoutId={`card-${tool.id}`}
      whileHover={{ scale: 1.02 }}
      transition={{ type: "spring", stiffness: 400, damping: 30 }}
    >
      <Card 
        className="h-full cursor-pointer group transition-all duration-500 backdrop-blur-md bg-gradient-to-b from-gray-900/90 via-gray-800/80 to-gray-900/95 border border-gray-700/50 shadow-2xl shadow-blue-500/20 rounded-3xl overflow-hidden"
        onClick={() => onSelect(tool.id)}
        style={{ willChange: 'transform' }}
      >
        {/* Enhanced card decorative elements */}
        <div className="absolute inset-0 overflow-hidden">
          {/* Animated glow effect that moves based on tool gradient */}
          <motion.div 
            className={`absolute -inset-[100%] opacity-20 blur-3xl`}
            style={{
              background: `radial-gradient(circle, ${tool.id === TOOLS.WEBCAM ? 'rgba(59, 130, 246, 0.3)' : 
                tool.id === TOOLS.VOICE ? 'rgba(168, 85, 247, 0.3)' : 
                tool.id === TOOLS.THERMAL ? 'rgba(245, 158, 11, 0.3)' : 
                'rgba(6, 182, 212, 0.3)'} 0%, transparent 70%)`
            }}
            animate={{
              x: ['-20%', '120%'],
              y: ['40%', '-10%']
            }}
            transition={{
              duration: 15,
              repeat: Infinity,
              repeatType: 'reverse',
              ease: 'easeInOut'
            }}
          />
        </div>
        
        {/* Top gradient bar with dynamic pulse animation */}
        <div className={`absolute top-0 left-0 right-0 h-1.5 bg-gradient-to-r ${tool.gradient}`}>
          <motion.div 
            className="absolute inset-0 opacity-50"
            animate={{
              background: [
                `linear-gradient(to right, transparent, rgba(255,255,255,0.8), transparent)`,
                `linear-gradient(to right, transparent, rgba(255,255,255,0), transparent)`
              ]
            }}
            transition={{
              duration: 2.5,
              repeat: Infinity,
              repeatDelay: 1,
            }}
            style={{
              backgroundSize: '200% 100%',
              backgroundPosition: '-100% 0'
            }}
          />
        </div>
        
        <div className="p-8 h-full flex flex-col relative">
          {/* Title section with enhanced glow effect */}
          <div className="flex flex-col md:flex-row md:items-center mb-8 relative">
            {/* Improved icon container with 3D-like effects */}
            <div className={`relative z-10 bg-gradient-to-br ${tool.gradient} p-4 md:p-5 rounded-2xl text-white mb-5 md:mb-0 md:mr-6 flex items-center justify-center group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 shadow-lg shadow-${tool.gradient.split('-')[2]}/30`}
              style={{
                boxShadow: `0 10px 25px -5px rgba(${
                  tool.id === TOOLS.WEBCAM ? '59, 130, 246' : 
                  tool.id === TOOLS.VOICE ? '168, 85, 247' : 
                  tool.id === TOOLS.THERMAL ? '245, 158, 11' : 
                  '6, 182, 212'
                }, 0.3), 0 8px 10px -6px rgba(${
                  tool.id === TOOLS.WEBCAM ? '59, 130, 246' : 
                  tool.id === TOOLS.VOICE ? '168, 85, 247' : 
                  tool.id === TOOLS.THERMAL ? '245, 158, 11' : 
                  '6, 182, 212'
                }, 0.2)`
              }}
            >
              {/* Subtle inner shadow for 3D effect */}
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-white/20 to-transparent opacity-70"></div>
              
              {/* Enhanced icon animation */}
              <motion.div 
                className="text-3xl md:text-4xl"
                style={{ willChange: 'transform' }}
                animate={{ 
                  scale: [1, 1.1, 1],
                  rotate: [0, 3, 0, -3, 0]
                }}
                transition={{ 
                  duration: 5,
                  repeat: Infinity,
                  ease: "easeInOut",
                  repeatDelay: 0.5
                }}
              >
                {tool.icon}
              </motion.div>
            </div>
            
            {/* Enhanced title with animated gradient */}
            <div>
              <motion.h3 
                className={`text-2xl md:text-3xl lg:text-4xl font-bold relative z-10 bg-clip-text text-transparent transition-colors duration-300`}
                style={{
                  backgroundImage: `linear-gradient(to right, white, ${
                    tool.id === TOOLS.WEBCAM ? '#60a5fa' : 
                    tool.id === TOOLS.VOICE ? '#c084fc' : 
                    tool.id === TOOLS.THERMAL ? '#fbbf24' : 
                    '#22d3ee'
                  })`
                }}
                animate={{
                  backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']
                }}
                transition={{
                  duration: 8,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              >
                {tool.title}
              </motion.h3>
              
              {/* Animated underline */}
              <motion.div 
                className={`mt-2 h-0.5 rounded-full bg-gradient-to-r ${tool.gradient} opacity-70`}
                initial={{ width: "0%" }}
                animate={{ width: ["0%", "100%", "40%"] }}
                transition={{
                  duration: 2,
                  times: [0, 0.7, 1],
                  ease: "easeOut"
                }}
              />
            </div>
          </div>
          
          {/* Enhanced description */}
          <motion.p 
            className="text-base md:text-lg text-white/80 mb-8 leading-relaxed"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2, duration: 0.5 }}
          >
            {tool.description}
          </motion.p>
          
          <div className="mt-auto">
            {/* Features section with better design */}
            <div className="flex items-center mb-5">
              <div className={`h-0.5 w-5 bg-gradient-to-r ${tool.gradient} mr-3 rounded-full`}></div>
              <div className="text-sm uppercase tracking-wider font-medium bg-clip-text text-transparent bg-gradient-to-r from-white/90 to-white/50">Features</div>
              <div className={`h-0.5 flex-grow bg-gradient-to-r from-white/10 to-transparent ml-3 rounded-full`}></div>
            </div>
            
            {/* Enhanced feature list */}
            <motion.ul 
              className="space-y-4 text-base md:text-lg text-white/80 mb-8"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3 }}
            >
              {tool.features.map((feature, featureIdx) => (
                <FeatureListItem 
                  key={`${tool.id}-feature-${featureIdx}`}
                  feature={feature}
                  featureIdx={featureIdx}
                  toolGradient={tool.gradient}
                />
              ))}
            </motion.ul>
          </div>
          
          {/* Enhanced call-to-action button */}
          <motion.div 
            className="mt-6"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.98 }}
          >
            <button
              className={`w-full py-3 px-6 rounded-xl bg-gradient-to-r ${tool.gradient} text-white font-semibold flex items-center justify-center group/button relative overflow-hidden`}
            >
              {/* Shine effect */}
              <div className="absolute inset-0 opacity-0 group-hover/button:opacity-100 transition-opacity duration-1000 overflow-hidden">
                <div className="absolute inset-0 translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
              </div>
              
              <span className="relative z-10 mr-2">Try Now</span>
              <motion.svg 
                className="w-5 h-5 relative z-10" 
                fill="none" 
                viewBox="0 0 24 24" 
                stroke="currentColor"
                animate={{ x: [0, 5, 0] }}
                transition={{ 
                  duration: 1.5, 
                  repeat: Infinity, 
                  repeatType: "loop", 
                  ease: "easeInOut" 
                }}
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
              </motion.svg>
            </button>
          </motion.div>
        </div>
      </Card>
    </motion.div>
  );
});

// Optimized background decorations component
const BackgroundDecorations = memo(() => (
  <motion.div 
    className="absolute inset-0 pointer-events-none overflow-hidden"
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    transition={{ duration: 1.5 }}
  >
    {/* Enhanced animated gradient orbs */}
    <motion.div 
      className="absolute -top-20 -right-20 w-[40rem] h-[40rem] bg-blue-500/10 rounded-full blur-[100px]"
      animate={{
        scale: [1, 1.2, 1],
        opacity: [0.1, 0.15, 0.1],
      }}
      transition={{
        duration: 8,
        repeat: Infinity,
        repeatType: "reverse",
        ease: "easeInOut"
      }}
    />
    <motion.div 
      className="absolute -bottom-40 -left-20 w-[40rem] h-[40rem] bg-purple-500/10 rounded-full blur-[100px]"
      animate={{
        scale: [1.2, 1, 1.2],
        opacity: [0.12, 0.08, 0.12],
      }}
      transition={{
        duration: 10,
        repeat: Infinity,
        repeatType: "reverse",
        ease: "easeInOut",
        delay: 1
      }}
    />
    <motion.div 
      className="absolute top-1/3 left-1/4 w-80 h-80 bg-cyan-500/10 rounded-full blur-[80px]"
      animate={{
        scale: [1, 1.4, 1],
        opacity: [0.1, 0.15, 0.1],
        y: [0, -30, 0]
      }}
      transition={{
        duration: 12,
        repeat: Infinity,
        repeatType: "reverse",
        ease: "easeInOut",
        delay: 2
      }}
    />
    
    {/* Enhanced grid decoration with subtle animation */}
    <motion.div 
      className="absolute inset-0" 
      style={gridPatternStyle}
      animate={{
        opacity: [0.4, 0.8, 0.4]
      }}
      transition={{
        duration: 8,
        repeat: Infinity,
        ease: "easeInOut"
      }}
    />
    
    {/* Enhanced particle effects */}
    {[...Array(12)].map((_, i) => (
      <motion.div
        key={`particle-${i}`}
        className={`absolute rounded-full ${
          i % 3 === 0 ? 'bg-blue-400' : i % 3 === 1 ? 'bg-purple-400' : 'bg-cyan-400'
        } opacity-70`}
        style={{
          width: Math.random() * 3 + 1,
          height: Math.random() * 3 + 1,
          left: `${Math.random() * 100}%`,
          top: `${Math.random() * 100}%`,
        }}
        animate={{
          y: [0, -Math.random() * 100 - 50],
          x: [0, (Math.random() - 0.5) * 50],
          opacity: [0, 0.7, 0],
        }}
        transition={{
          duration: Math.random() * 8 + 7,
          repeat: Infinity,
          delay: Math.random() * 5,
          ease: "easeInOut",
        }}
      />
    ))}
    
    {/* Animated network lines */}
    {[...Array(6)].map((_, i) => (
      <motion.div
        key={`line-${i}`}
        className="absolute h-px bg-gradient-to-r from-transparent via-blue-500/20 to-transparent"
        style={{
          width: `${Math.random() * 30 + 20}%`,
          left: `${Math.random() * 70}%`,
          top: `${10 + (i * 15)}%`,
          transform: `rotate(${Math.random() * 20 - 10}deg)`,
        }}
        animate={{
          opacity: [0, 0.5, 0],
          width: [`${Math.random() * 30 + 20}%`, `${Math.random() * 40 + 30}%`, `${Math.random() * 30 + 20}%`],
        }}
        transition={{
          duration: Math.random() * 5 + 8,
          repeat: Infinity,
          repeatType: "reverse",
          ease: "easeInOut",
          delay: Math.random() * 3,
        }}
      />
    ))}
  </motion.div>
));

export default function AIToolsSection() {
  const [selectedTool, setSelectedTool] = useState(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const carouselRef = useRef(null);
  const [windowWidth, setWindowWidth] = useState(typeof window !== 'undefined' ? window.innerWidth : 1200);
  const cardMaxWidth = Math.min(windowWidth * 0.80, 800);
  const autoPlayIntervalRef = useRef(null);
  
  // Handle window resize for responsive behavior
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  
  // Memoized navigation handler for better performance
  const navigateTo = useCallback((index) => {
    if (isTransitioning || index === currentIndex) return;
    
    setIsTransitioning(true);
    setCurrentIndex(index);
    
    // Reset transition state after animation completes
    setTimeout(() => {
      setIsTransitioning(false);
    }, 500); // Match this with your animation duration
  }, [currentIndex, isTransitioning]);
  
  // Auto-scroll the carousel with improved performance
  useEffect(() => {
    const startAutoPlay = () => {
      autoPlayIntervalRef.current = setInterval(() => {
        const nextIndex = (currentIndex + 1) % toolsData.length;
        navigateTo(nextIndex);
      }, 5000); // Change slide every 5 seconds
    };
    
    if (!isTransitioning) {
      startAutoPlay();
    }
    
    return () => {
      if (autoPlayIntervalRef.current) {
        clearInterval(autoPlayIntervalRef.current);
      }
    };
  }, [currentIndex, navigateTo, isTransitioning]);
  
  // Enhanced title animation variants
  const titleVariants = {
    hidden: { 
      opacity: 0, 
      y: -30,
      scale: 0.9
    },
    visible: { 
      opacity: 1, 
      y: 0,
      scale: 1,
      transition: { 
        duration: 0.8,
        ease: "easeOut",
        when: "beforeChildren",
        staggerChildren: 0.1
      }
    }
  };
  
  // Text character animation for title
  const characterAnimation = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        type: "spring",
        damping: 12,
        stiffness: 200
      }
    }
  };
  
  // Split title text into characters for animation
  const titleText = "AI-Powered Oral Health Tools";
  const titleChars = titleText.split("");

  const renderSelectedTool = () => {
    switch (selectedTool) {
      case TOOLS.WEBCAM:
        return <WebcamOralScanner onBack={() => setSelectedTool(null)} />;
      case TOOLS.VOICE:
        return <VoiceAnalysisTool onBack={() => setSelectedTool(null)} />;
      case TOOLS.THERMAL:
        return <ThermalImagingTool onBack={() => setSelectedTool(null)} />;
      case TOOLS.BREATH:
        return <BreathAnalysisTool onBack={() => setSelectedTool(null)} />;
      case TOOLS.MIRROR:
        return <MirrorModeTool onBack={() => setSelectedTool(null)} />;
      default:
        return null;
    }
  };

  return (
    <ContentSection
      id="ai-tools"
      title={
        <motion.div 
          className="relative"
          variants={titleVariants}
          initial="hidden"
          animate="visible"
        >
          {/* Enhanced glowing background effect for title */}
          <div className="absolute -inset-4 rounded-2xl bg-gradient-to-r from-blue-500/10 via-indigo-500/20 to-purple-500/10 blur-2xl opacity-70"></div>
          
          {/* Main title with enhanced animations */}
          <h2 className="text-3xl md:text-5xl lg:text-6xl xl:text-7xl font-bold relative z-10 tracking-tight leading-tight m-0 p-0">
            <span className="sr-only">AI-Powered Oral Health Tools</span>
            <span aria-hidden="true" className="inline-block overflow-hidden">
              {titleChars.map((char, index) => (
                <motion.span
                  key={`title-char-${index}`}
                  className="inline-block"
                  variants={characterAnimation}
                  style={{
                    color: index < 3 ? "#60a5fa" : 
                          index < 9 ? "#7d95f5" :
                          index < 16 ? "#818cf8" :
                          index < 24 ? "#9180fa" : "#a78bfa",
                    transition: "color 0.3s ease"
                  }}
                >
                  {char === " " ? "\u00A0" : char}
                </motion.span>
              ))}
            </span>
            
            {/* Dynamic underline animation */}
            <motion.div
              className="h-1 bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500 rounded-full mt-2 md:mt-4 mx-auto"
              initial={{ width: 0, opacity: 0 }}
              animate={{ width: "60%", opacity: 1 }}
              transition={{ delay: 1, duration: 1, ease: "easeOut" }}
              style={{ marginLeft: "auto", marginRight: "auto" }}
            />
          </h2>
        </motion.div>
      }
      subtitle={
        <motion.p 
          className="text-lg md:text-xl lg:text-2xl text-blue-100/90 mt-6 md:mt-8 max-w-3xl mx-auto font-light"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6, duration: 0.8 }}
        >
          Advanced technology for dental health monitoring at home
        </motion.p>
      }
      className="py-20"
    >
      {selectedTool ? (
        renderSelectedTool()
      ) : (
        <div className="relative max-w-5xl mx-auto py-12 md:py-20">
          {/* Enhanced background decorations */}
          <BackgroundDecorations />
          
          {/* Single card display with improved transitions */}
          <div 
            className="flex justify-center items-center relative z-10 h-[550px] md:h-[600px] lg:h-[650px]"
            ref={carouselRef}
          >
            <AnimatePresence initial={false} mode="wait">
              {toolsData.map((tool, idx) => (
                currentIndex === idx && (
                  <ToolCard
                    key={`tool-card-${tool.id}`}
                    tool={tool}
                    isVisible={true}
                    maxWidth={cardMaxWidth}
                    onSelect={setSelectedTool}
                  />
                )
              ))}
            </AnimatePresence>
          </div>
          
          {/* Enhanced carousel controls */}
          <motion.div 
            className="flex justify-center mt-12 mb-4 space-x-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.7 }}
          >
            {toolsData.map((tool, idx) => (
              <button
                key={idx}
                onClick={() => navigateTo(idx)}
                className={`group relative transition-all duration-300 focus:outline-none ${isTransitioning ? 'pointer-events-none' : ''}`}
                aria-label={`Go to slide ${idx + 1}`}
                disabled={isTransitioning}
              >
                <div className={`w-4 h-4 rounded-full transition-all duration-500 ${
                  idx === currentIndex 
                    ? `bg-gradient-to-r ${tool.gradient} scale-125 shadow-lg shadow-${tool.gradient.split('-')[2]}/30` 
                    : 'bg-gray-600/50 group-hover:bg-gray-400/80'
                }`}>
                  {idx === currentIndex && (
                    <motion.div
                      className="absolute inset-0 rounded-full -z-10"
                      initial={{ opacity: 0, scale: 1 }}
                      animate={{ opacity: [0, 0.5, 0], scale: [1, 2, 1] }}
                      transition={{ 
                        duration: 2, 
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                      style={{ 
                        background: `radial-gradient(circle, ${
                          tool.id === TOOLS.WEBCAM ? 'rgba(59, 130, 246, 0.5)' : 
                          tool.id === TOOLS.VOICE ? 'rgba(168, 85, 247, 0.5)' : 
                          tool.id === TOOLS.THERMAL ? 'rgba(245, 158, 11, 0.5)' : 
                          'rgba(6, 182, 212, 0.5)'
                        } 0%, transparent 70%)`
                      }}
                    />
                  )}
                </div>
                <div className={`absolute -inset-3 rounded-full bg-gray-500/0 group-hover:bg-gray-500/10 group-focus:bg-gray-500/20 transition-all duration-300`}></div>
                
                {/* Enhanced tooltip on hover */}
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 -translate-y-2 opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none">
                  <div className="bg-gradient-to-r from-gray-900/90 to-gray-800/90 backdrop-blur-md text-white text-xs py-2 px-3 rounded-lg whitespace-nowrap border border-gray-700/50 shadow-xl">
                    {tool.title.replace('AI ', '')}
                    <div className="w-2 h-2 bg-gray-800 transform rotate-45 absolute -bottom-1 left-1/2 -translate-x-1/2 border-r border-b border-gray-700/50"></div>
                  </div>
                </div>
              </button>
            ))}
          </motion.div>
        </div>
      )}
    </ContentSection>
  );
}