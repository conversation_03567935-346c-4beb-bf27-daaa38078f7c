import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { matchPatientToProvider } from '../../services/matching/index.js';

// Data structure for user intent:
// - geoLocation: string | null - User's detected location
// - condition: string | null - Dental condition/concern
// - urgency: 'low' | 'medium' | 'high' | null - How urgent their need is
// - insurance: string | null - Their insurance provider
// - lastVisit: string | null - When they last visited a dentist
// - intentSignals: Object containing:
//   - viewedConditions: string[] - Conditions they've viewed
//   - timeOnPage: number - Time spent on page in seconds
//   - interactionScore: number - Score based on user interactions
//   - toolsUsed: string[] - Tools/features they've used on the site

// Mock intent scoring model (would be replaced with actual ML model)
const scoreIntent = (intent) => {
  let score = 0;
  
  // Geographic scoring
  if (intent.geoLocation) score += 10;
  
  // Condition scoring
  if (intent.condition) score += 15;
  
  // Urgency scoring
  if (intent.urgency === 'high') score += 25;
  else if (intent.urgency === 'medium') score += 15;
  else if (intent.urgency === 'low') score += 5;
  
  // Insurance scoring
  if (intent.insurance) score += 20;
  
  // Last visit scoring
  if (intent.lastVisit === 'never' || intent.lastVisit === '> 2 years') score += 20;
  else if (intent.lastVisit === '1-2 years') score += 15;
  else if (intent.lastVisit === '6-12 months') score += 5;
  
  // Behavior signals
  score += Math.min(intent.intentSignals.viewedConditions.length * 5, 20);
  score += Math.min(Math.floor(intent.intentSignals.timeOnPage / 30), 10); // Score per 30 seconds
  score += Math.min(intent.intentSignals.interactionScore, 20);
  score += Math.min(intent.intentSignals.toolsUsed.length * 10, 30);

  return Math.min(score, 100); // Cap at 100
};

// Find best partner match based on intent
const findBestPartner = (intent, intentScore) => {
  // Convert UserIntent format to patient data format for the matching service
  const patientData = {
    condition: intent.condition || 'General checkup',
    location: intent.geoLocation || 'Unknown',
    insurance: intent.insurance || 'None',
    urgency: intent.urgency || 'Medium'
  };
  
  // Use our matching service to find the best partners
  const topMatches = matchPatientToProvider(patientData);
  
  // Return the best match (first in the sorted array)
  return topMatches[0] || null;
};

// Main component
const ValueMagnetEngine = ({ children }) => {
  const navigate = useNavigate();
  const [userIntent, setUserIntent] = useState({
    geoLocation: null,
    condition: null,
    urgency: null,
    insurance: null,
    lastVisit: null,
    intentSignals: {
      viewedConditions: [],
      timeOnPage: 0,
      interactionScore: 0,
      toolsUsed: []
    }
  });
  const [intentScore, setIntentScore] = useState(0);
  const [pageTimer, setPageTimer] = useState(0);
  const [showLeadForm, setShowLeadForm] = useState(false);
  
  // Collect geo-location on load
  useEffect(() => {
    // Get geo-location (simplified for demo)
    const getGeoLocation = () => {
      // In production, use actual geolocation API
      setUserIntent(prev => ({
        ...prev,
        geoLocation: 'San Francisco'
      }));
    };
    
    getGeoLocation();
    
    // Setup page timer for time on page tracking
    const timer = setInterval(() => {
      setPageTimer(prev => prev + 1);
      
      // Update intent signals with time
      setUserIntent(prev => ({
        ...prev,
        intentSignals: {
          ...prev.intentSignals,
          timeOnPage: prev.intentSignals.timeOnPage + 1
        }
      }));
    }, 1000);
    
    // Add interaction tracking
    const trackInteraction = () => {
      setUserIntent(prev => ({
        ...prev,
        intentSignals: {
          ...prev.intentSignals,
          interactionScore: prev.intentSignals.interactionScore + 1
        }
      }));
    };
    
    document.addEventListener('click', trackInteraction);
    document.addEventListener('scroll', trackInteraction);
    
    return () => {
      clearInterval(timer);
      document.removeEventListener('click', trackInteraction);
      document.removeEventListener('scroll', trackInteraction);
    };
  }, []);
  
  // Track tool usage
  const trackToolUsage = (toolId) => {
    if (!userIntent.intentSignals.toolsUsed.includes(toolId)) {
      setUserIntent(prev => ({
        ...prev,
        intentSignals: {
          ...prev.intentSignals,
          toolsUsed: [...prev.intentSignals.toolsUsed, toolId]
        }
      }));
    }
  };
  
  // Track condition view
  const trackConditionView = (condition) => {
    if (!userIntent.intentSignals.viewedConditions.includes(condition)) {
      setUserIntent(prev => ({
        ...prev,
        intentSignals: {
          ...prev.intentSignals,
          viewedConditions: [...prev.intentSignals.viewedConditions, condition]
        }
      }));
    }
  };
  
  // Update specific intent signals
  const updateIntentData = (key, value) => {
    setUserIntent(prev => ({
      ...prev,
      [key]: value
    }));
  };
  
  // Calculate intent score
  useEffect(() => {
    const score = scoreIntent(userIntent);
    setIntentScore(score);
    
    // Show lead form if score is high enough
    if (score > 60 && !showLeadForm) {
      setShowLeadForm(true);
    }
  }, [userIntent]);
  
  // Handle lead form submit
  const handleLeadSubmit = (formData) => {
    // Process lead data
    const updatedIntent = {
      ...userIntent,
      ...formData
    };
    
    // Score the updated intent
    const finalScore = scoreIntent(updatedIntent);
    
    // Find best partner match
    const bestMatch = findBestPartner(updatedIntent, finalScore);
    
    // In production, send to backend
    console.log('Lead generated:', {
      intent: updatedIntent,
      score: finalScore,
      partnerMatch: bestMatch
    });
    
    // Redirect to thank you or partner page
    navigate(`/partnerships/match/${bestMatch?.id}`);
  };
  
  // Create context provider value
  const magnet = {
    userIntent,
    intentScore,
    trackToolUsage,
    trackConditionView,
    updateIntentData
  };
  
  // Provide context to children
  return (
    <ValueMagnetContext.Provider value={magnet}>
      {children}
      
      {/* Conditional lead capture form */}
      {showLeadForm && (
        <LeadCaptureModal 
          onSubmit={handleLeadSubmit}
          onClose={() => setShowLeadForm(false)}
        />
      )}
    </ValueMagnetContext.Provider>
  );
};

// Context and hook for consuming the engine
export const ValueMagnetContext = React.createContext(null);

export const useValueMagnet = () => {
  const context = React.useContext(ValueMagnetContext);
  if (!context) {
    throw new Error('useValueMagnet must be used within a ValueMagnetEngine');
  }
  return context;
};

// Lead capture modal component
const LeadCaptureModal = ({ onSubmit, onClose }) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    condition: '',
    urgency: 'medium',
    insurance: '',
    lastVisit: ''
  });
  
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(formData);
  };
  
  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <motion.div 
        className="bg-white rounded-xl shadow-xl max-w-md w-full mx-4"
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
      >
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-xl font-bold text-gray-900">Connect with a Dental Expert</h3>
            <button 
              onClick={onClose}
              className="text-gray-400 hover:text-gray-500"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          <p className="text-gray-600 mb-4">
            Based on your activity, we can connect you with a dental professional. Fill out the form to get personalized help.
          </p>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700">Name</label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              />
            </div>
            
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">Email</label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                required
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              />
            </div>
            
            <div>
              <label htmlFor="phone" className="block text-sm font-medium text-gray-700">Phone (optional)</label>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              />
            </div>
            
            <div>
              <label htmlFor="condition" className="block text-sm font-medium text-gray-700">Dental Concern</label>
              <select
                id="condition"
                name="condition"
                value={formData.condition}
                onChange={handleChange}
                required
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              >
                <option value="">Select a concern</option>
                <option value="toothache">Toothache</option>
                <option value="cavity">Cavity / Filling</option>
                <option value="cleaning">Cleaning / Checkup</option>
                <option value="cosmetic">Cosmetic Dentistry</option>
                <option value="orthodontics">Orthodontics</option>
                <option value="other">Other</option>
              </select>
            </div>
            
            <div>
              <label htmlFor="urgency" className="block text-sm font-medium text-gray-700">Urgency</label>
              <select
                id="urgency"
                name="urgency"
                value={formData.urgency}
                onChange={handleChange}
                required
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              >
                <option value="low">Low - Within a month</option>
                <option value="medium">Medium - Within a week</option>
                <option value="high">High - As soon as possible</option>
              </select>
            </div>
            
            <div>
              <label htmlFor="insurance" className="block text-sm font-medium text-gray-700">Insurance Provider</label>
              <select
                id="insurance"
                name="insurance"
                value={formData.insurance}
                onChange={handleChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              >
                <option value="">Select insurance (optional)</option>
                <option value="Delta Dental">Delta Dental</option>
                <option value="Cigna">Cigna</option>
                <option value="Aetna">Aetna</option>
                <option value="MetLife">MetLife</option>
                <option value="United Healthcare">United Healthcare</option>
                <option value="Guardian">Guardian</option>
                <option value="None">No Insurance</option>
              </select>
            </div>
            
            <div>
              <label htmlFor="lastVisit" className="block text-sm font-medium text-gray-700">Last Dental Visit</label>
              <select
                id="lastVisit"
                name="lastVisit"
                value={formData.lastVisit}
                onChange={handleChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              >
                <option value="">Select timeframe (optional)</option>
                <option value="< 6 months">Less than 6 months ago</option>
                <option value="6-12 months">6-12 months ago</option>
                <option value="1-2 years">1-2 years ago</option>
                <option value="> 2 years">More than 2 years ago</option>
                <option value="never">Never visited a dentist</option>
              </select>
            </div>
            
            <div className="pt-2">
              <button
                type="submit"
                className="w-full py-3 px-4 rounded-md shadow-sm text-white bg-gradient-to-r from-indigo-600 to-blue-600 hover:from-indigo-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Find My Dental Match
              </button>
            </div>
            
            <p className="text-xs text-gray-500">
              By submitting this form, you agree to our Privacy Policy and Terms of Service.
              We'll connect you with a suitable dental provider based on your needs.
            </p>
          </form>
        </div>
      </motion.div>
    </div>
  );
};

export default ValueMagnetEngine; 