import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card } from '../common';

export default function WebcamOralScanner({ onBack }) {
  const [scanning, setScanning] = useState(false);
  const [results, setResults] = useState(null);
  const [stream, setStream] = useState(null);
  const [loading, setLoading] = useState(false);
  const [showPermissionPrompt, setShowPermissionPrompt] = useState(false);
  const [error, setError] = useState(null);
  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const [deviceList, setDeviceList] = useState([]);
  const [selectedDevice, setSelectedDevice] = useState(null);
  const [qualityScore, setQualityScore] = useState(0);
  const [isFocused, setIsFocused] = useState(false);

  // Simulate AI analysis of the webcam frame
  const simulateAIAnalysis = () => {
    setLoading(true);
    
    // Simulate processing time
    setTimeout(() => {
      // Generate mock results
      const mockResults = {
        oralHealthScore: Math.floor(Math.random() * 30) + 70, // 70-100 score
        findings: [
          {
            type: 'Plaque Detection',
            severity: Math.random() > 0.5 ? 'Low' : 'Moderate',
            areas: ['Upper left molars', 'Lower front teeth'],
            confidence: Math.floor(Math.random() * 10) + 90, // 90-100% confidence
          },
          {
            type: 'Gum Health',
            severity: Math.random() > 0.7 ? 'Good' : 'Needs Attention',
            areas: ['Upper right gumline'],
            confidence: Math.floor(Math.random() * 15) + 85, // 85-100% confidence
          },
          {
            type: 'Enamel Analysis',
            severity: 'Good',
            areas: ['All visible surfaces'],
            confidence: Math.floor(Math.random() * 10) + 90, // 90-100% confidence
          }
        ],
        recommendations: [
          'Focus on brushing the upper left molars more thoroughly',
          'Consider using an interdental brush for better cleaning between teeth',
          'Schedule a professional cleaning within the next 3 months'
        ],
        timestamp: new Date().toISOString()
      };
      
      setResults(mockResults);
      setLoading(false);
      setScanning(false);
      
      // Stop the webcam stream
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
        setStream(null);
      }
    }, 3000);
  };

  // Enhanced device detection and handling
  const getConnectedDevices = async () => {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      const videoDevices = devices.filter(device => device.kind === 'videoinput');
      setDeviceList(videoDevices);
      
      // Select the best quality camera if available
      const preferredDevice = videoDevices.find(device => 
        device.label.toLowerCase().includes('hd') || 
        device.label.toLowerCase().includes('1080p')
      ) || videoDevices[0];
      
      setSelectedDevice(preferredDevice);
    } catch (err) {
      console.error('Error getting devices:', err);
      setError('Unable to detect camera devices. Please ensure your camera is connected.');
    }
  };

  // Monitor device changes
  useEffect(() => {
    getConnectedDevices();
    navigator.mediaDevices.addEventListener('devicechange', getConnectedDevices);
    return () => {
      navigator.mediaDevices.removeEventListener('devicechange', getConnectedDevices);
    };
  }, []);

  // Enhanced quality assessment
  const assessImageQuality = (imageData) => {
    const { width, height, data } = imageData;
    let score = 100;
    
    // Check brightness
    const brightness = calculateAverageBrightness(data);
    if (brightness < 50) {
      score -= 30;
      setError('Warning: Low lighting detected. Please move to a brighter area.');
    } else if (brightness > 240) {
      score -= 20;
      setError('Warning: Image may be overexposed. Please reduce lighting.');
    }
    
    // Check for motion blur
    const blurScore = detectMotionBlur(imageData);
    if (blurScore > 50) {
      score -= 25;
      setError('Warning: Image may be blurry. Please hold the camera steady.');
    }
    
    // Check resolution
    if (width < 720 || height < 480) {
      score -= 15;
      setError('Warning: Low resolution detected. Consider using a better camera.');
    }
    
    setQualityScore(score);
    return score > 60;
  };

  // Motion blur detection
  const detectMotionBlur = (imageData) => {
    const { width, height, data } = imageData;
    let diffSum = 0;
    
    // Simple edge detection
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width - 1; x++) {
        const idx = (y * width + x) * 4;
        const nextIdx = (y * width + x + 1) * 4;
        
        // Compare adjacent pixels
        diffSum += Math.abs(data[idx] - data[nextIdx]);
      }
    }
    
    return 100 - (diffSum / (width * height)); // Higher value indicates more blur
  };

  // Enhanced capture frame with quality checks
  const captureFrame = async () => {
    if (videoRef.current && canvasRef.current) {
      const video = videoRef.current;
      const canvas = canvasRef.current;
      const context = canvas.getContext('2d');
      
      // Enhanced readiness check
      if (video.readyState !== video.HAVE_ENOUGH_DATA) {
        setError('Camera stream is not ready. Please wait a moment.');
        return;
      }
      
      try {
        // Auto-focus if supported
        if (stream) {
          const track = stream.getVideoTracks()[0];
          if (track.getCapabilities().focusMode) {
            await track.applyConstraints({ advanced: [{ focusMode: 'continuous' }] });
            setIsFocused(true);
          }
        }
        
        // Set canvas dimensions
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        
        // Draw frame with proper scaling
        context.drawImage(video, 0, 0, canvas.width, canvas.height);
        
        // Get image data for quality assessment
        const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
        
        // Perform quality checks
        if (assessImageQuality(imageData)) {
          simulateAIAnalysis();
        } else {
          // If quality is poor, provide guidance but don't block capture
          if (qualityScore < 40) {
            if (!confirm('Image quality is poor. Would you like to try again with better conditions?')) {
              simulateAIAnalysis();
            }
          } else {
            simulateAIAnalysis();
          }
        }
      } catch (err) {
        console.error('Error capturing frame:', err);
        setError(`Capture failed: ${err.message}. Please try again.`);
      }
    }
  };

  // Helper function to calculate image brightness
  const calculateAverageBrightness = (data) => {
    let sum = 0;
    for (let i = 0; i < data.length; i += 4) {
      // Convert RGB to brightness value
      sum += (data[i] * 0.299 + data[i + 1] * 0.587 + data[i + 2] * 0.114);
    }
    return sum / (data.length / 4);
  };

  // Enhanced start scanning with device selection
  const startScanning = async () => {
    try {
      setError(null);
      setShowPermissionPrompt(true);
      
      // Stop any existing stream first
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
      }

      // Add explicit width/height constraints
      const constraints = {
        video: {
          facingMode: 'user',
          width: { ideal: 1280, min: 640 },
          height: { ideal: 720, min: 360 }, // Reduced minimum for better compatibility
          aspectRatio: 16/9
        }
      };

      // Add timeout for device access
      const mediaStream = await Promise.race([
        navigator.mediaDevices.getUserMedia(constraints),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Camera access timeout')), 5000)
        )
      ]);

      // Verify video track exists
      if (!mediaStream.getVideoTracks().length) {
        throw new Error('No video track found in stream');
      }

      setStream(mediaStream);
      setShowPermissionPrompt(false);
      setScanning(true);

      // Reset video element and force play
      if (videoRef.current) {
        videoRef.current.srcObject = null; // Clear previous stream
        videoRef.current.srcObject = mediaStream;
        await videoRef.current.play();
      }

    } catch (err) {
      console.error('Camera error:', err);
      setShowPermissionPrompt(false);
      setError(err.message || 'Failed to access camera');
    }
  };

  // Add this useEffect for stream changes
  useEffect(() => {
    if (stream && videoRef.current) {
      const video = videoRef.current;
      const onPlay = () => video.classList.add('video-active');
      
      video.addEventListener('play', onPlay);
      return () => video.removeEventListener('play', onPlay);
    }
  }, [stream]);

  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (stream) {
        stream.getTracks().forEach(track => {
          track.stop();
          track.onended = null; // Clean up event listener
        });
      }
    };
  }, [stream]);

  // Restart the scanning process
  const restartScan = () => {
    setResults(null);
    startScanning();
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 15
      }
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-6"
      >
        <button
          onClick={onBack}
          className="flex items-center text-blue-400 hover:text-blue-300 transition-colors"
        >
          <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          Back to Tools
        </button>
      </motion.div>

      <AnimatePresence mode="sync">
        {!scanning && !results && !showPermissionPrompt && (
          <motion.div
            key="intro"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            exit={{ opacity: 0, y: -20 }}
          >
            <motion.div variants={itemVariants}>
              <Card className="mb-8 overflow-hidden">
                <div className="p-6 md:p-8">
                  <h2 className="text-2xl font-bold text-white mb-4">Webcam Oral Scanner</h2>
                  <p className="text-white/70 mb-6">
                    This tool uses your device's camera and AI to analyze your oral health. 
                    It can detect potential issues like plaque buildup, gum inflammation, and more.
                  </p>
                  
                  <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4 mb-6">
                    <h3 className="text-lg font-semibold text-blue-300 mb-2">How to use:</h3>
                    <ul className="list-disc list-inside text-white/70 space-y-2">
                      <li>Position your face in good lighting</li>
                      <li>Open your mouth to show your teeth clearly</li>
                      <li>Hold still while the scan is in progress</li>
                      <li>Follow the on-screen instructions</li>
                    </ul>
                  </div>
                  
                  <div className="flex justify-center">
                    <motion.button
                      onClick={startScanning}
                      className="px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-lg text-white font-medium flex items-center"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                      </svg>
                      Start Oral Scan
                    </motion.button>
                  </div>
                </div>
              </Card>
            </motion.div>

            <motion.div variants={itemVariants}>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-gradient-to-br from-blue-900/30 to-blue-800/30 rounded-xl p-5 border border-blue-700/20">
                  <div className="text-blue-300 mb-3">
                    <svg className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-white mb-2">Privacy Protected</h3>
                  <p className="text-white/70">Your scan data is processed locally and never stored on external servers.</p>
                </div>
                
                <div className="bg-gradient-to-br from-indigo-900/30 to-indigo-800/30 rounded-xl p-5 border border-indigo-700/20">
                  <div className="text-indigo-300 mb-3">
                    <svg className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-white mb-2">Instant Results</h3>
                  <p className="text-white/70">Get immediate feedback on your oral health with AI-powered analysis.</p>
                </div>
                
                <div className="bg-gradient-to-br from-purple-900/30 to-purple-800/30 rounded-xl p-5 border border-purple-700/20">
                  <div className="text-purple-300 mb-3">
                    <svg className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-white mb-2">Track Progress</h3>
                  <p className="text-white/70">Monitor your oral health improvements over time with saved reports.</p>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}

        {showPermissionPrompt && (
          <motion.div
            key="permission"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            className="text-center"
          >
            <Card className="p-8 max-w-md mx-auto">
              <div className="animate-pulse text-blue-400 mb-4">
                <svg className="w-16 h-16 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-white mb-2">Camera Access Required</h3>
              <p className="text-white/70 mb-4">
                Please allow access to your camera when prompted to continue with the oral scan.
              </p>
              <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4 text-left">
                <p className="text-white/70 text-sm">
                  Your privacy is important to us. Camera access is only used for the duration of the scan and no images are stored on our servers.
                </p>
              </div>
            </Card>
          </motion.div>
        )}

        {scanning && !loading && (
          <motion.div
            key="scanning"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <Card className="p-6 mb-6">
              <div className="text-center mb-4">
                <h3 className="text-xl font-bold text-white">Position Your Face</h3>
                <p className="text-white/70">
                  Center your face in the frame and open your mouth to show your teeth clearly.
                </p>
              </div>
              
              <div className="relative rounded-lg overflow-hidden bg-black aspect-video mb-6">
                <video
                  ref={videoRef}
                  autoPlay
                  playsInline
                  muted
                  className="w-full h-full object-contain"
                  onLoadedMetadata={() => {
                    // Add error handling for video playback
                    videoRef.current.play().catch(err => {
                      console.error('Error playing video:', err);
                      setError('Failed to start camera preview. Please refresh the page.');
                    });
                  }}
                  // Add error handler for video element
                  onError={(e) => {
                    console.error('Video error:', e);
                    setError('Camera feed unavailable. Please check device connections.');
                  }}
                />
                
                {/* Overlay guide */}
                <div className="absolute inset-0 border-2 border-dashed border-blue-400/50 pointer-events-none"></div>
                <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                  <div className="w-1/2 h-1/2 border-2 border-dashed border-blue-400/70 rounded-full"></div>
                </div>
              </div>
              
              <div className="flex justify-center">
                <motion.button
                  onClick={captureFrame}
                  className="px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-lg text-white font-medium flex items-center"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  Capture & Analyze
                </motion.button>
              </div>
              
              {/* Hidden canvas for processing */}
              <canvas ref={canvasRef} className="hidden" />
            </Card>
            
            <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
              <h4 className="text-blue-300 font-medium mb-2 flex items-center">
                <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Tips for Best Results
              </h4>
              <ul className="list-disc list-inside text-white/70 space-y-1 text-sm">
                <li>Ensure good lighting on your face</li>
                <li>Remove any face coverings</li>
                <li>Open your mouth wide enough to show teeth</li>
                <li>Hold still during the capture</li>
              </ul>
            </div>
          </motion.div>
        )}

        {loading && (
          <motion.div
            key="loading"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="text-center"
          >
            <Card className="p-8">
              <div className="flex flex-col items-center">
                <div className="relative w-24 h-24 mb-6">
                  <motion.div
                    className="absolute inset-0 rounded-full border-4 border-blue-500/30"
                    animate={{ rotate: 360 }}
                    transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                  />
                  <motion.div
                    className="absolute inset-0 rounded-full border-4 border-transparent border-t-blue-500"
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
                  />
                </div>
                
                <h3 className="text-xl font-bold text-white mb-2">Analyzing Your Oral Health</h3>
                <p className="text-white/70 mb-6">
                  Our AI is processing your scan to provide detailed insights...
                </p>
                
                <div className="w-full max-w-md bg-white/10 rounded-full h-2.5 mb-4">
                  <motion.div 
                    className="bg-gradient-to-r from-blue-500 to-indigo-500 h-2.5 rounded-full"
                    initial={{ width: "0%" }}
                    animate={{ width: "100%" }}
                    transition={{ duration: 3, ease: "easeInOut" }}
                  />
                </div>
                
                <p className="text-white/50 text-sm">This will take just a moment...</p>
              </div>
            </Card>
          </motion.div>
        )}

        {results && (
          <motion.div
            key="results"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            exit={{ opacity: 0 }}
          >
            <motion.div variants={itemVariants}>
              <Card className="p-6 mb-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-xl font-bold text-white">Oral Health Analysis</h3>
                  <div className="text-sm text-white/60">
                    {new Date(results.timestamp).toLocaleString()}
                  </div>
                </div>
                
                <div className="mb-8">
                  <div className="flex justify-between items-center mb-2">
                    <div className="text-lg font-medium text-white">Overall Health Score</div>
                    <div className="text-2xl font-bold text-blue-400">{results.oralHealthScore}/100</div>
                  </div>
                  <div className="w-full bg-white/10 rounded-full h-2.5">
                    <div 
                      className={`h-2.5 rounded-full ${
                        results.oralHealthScore > 80 ? 'bg-green-500' : 
                        results.oralHealthScore > 60 ? 'bg-yellow-500' : 
                        'bg-red-500'
                      }`}
                      style={{ width: `${results.oralHealthScore}%` }}
                    />
                  </div>
                </div>
                
                <div className="mb-8">
                  <h4 className="text-lg font-medium text-white mb-4">Findings</h4>
                  <div className="space-y-4">
                    {results.findings.map((finding, index) => (
                      <motion.div 
                        key={index}
                        className="bg-white/5 rounded-lg p-4 border border-white/10"
                        variants={{
                          hidden: { opacity: 0, y: 10 },
                          visible: { 
                            opacity: 1, 
                            y: 0,
                            transition: { delay: index * 0.1 + 0.2 }
                          }
                        }}
                      >
                        <div className="flex justify-between mb-2">
                          <div className="font-medium text-blue-300">{finding.type}</div>
                          <div className={`text-sm font-medium px-2 py-0.5 rounded ${
                            finding.severity === 'Good' ? 'bg-green-500/20 text-green-300' : 
                            finding.severity === 'Low' ? 'bg-blue-500/20 text-blue-300' : 
                            finding.severity === 'Moderate' ? 'bg-yellow-500/20 text-yellow-300' : 
                            'bg-red-500/20 text-red-300'
                          }`}>
                            {finding.severity}
                          </div>
                        </div>
                        <div className="text-white/70 text-sm mb-2">
                          Areas: {finding.areas.join(', ')}
                        </div>
                        <div className="text-xs text-white/50">
                          Confidence: {finding.confidence}%
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </div>
                
                <div>
                  <h4 className="text-lg font-medium text-white mb-4">Recommendations</h4>
                  <ul className="space-y-2">
                    {results.recommendations.map((recommendation, index) => (
                      <motion.li 
                        key={index}
                        className="flex items-start"
                        variants={{
                          hidden: { opacity: 0, x: -10 },
                          visible: { 
                            opacity: 1, 
                            x: 0,
                            transition: { delay: index * 0.1 + 0.5 }
                          }
                        }}
                      >
                        <svg className="w-5 h-5 text-blue-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span className="text-white/80">{recommendation}</span>
                      </motion.li>
                    ))}
                  </ul>
                </div>
              </Card>
            </motion.div>
            
            <motion.div 
              variants={itemVariants}
              className="flex justify-center space-x-4"
            >
              <motion.button
                onClick={restartScan}
                className="px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-lg text-white font-medium flex items-center"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Scan Again
              </motion.button>
              
              <motion.button
                onClick={onBack}
                className="px-6 py-3 bg-white/10 hover:bg-white/20 rounded-lg text-white font-medium flex items-center"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Back to Tools
              </motion.button>
            </motion.div>
          </motion.div>
        )}

        {error && (
          <motion.div
            key="error"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <Card className="p-6 mb-6">
              <div className="text-center">
                <div className="text-red-400 mb-4">
                  <svg className="w-16 h-16 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">Camera Access Error</h3>
                <p className="text-white/70 mb-6">
                  {error}
                </p>
                <div className="flex justify-center space-x-4">
                  <motion.button
                    onClick={() => {
                      setError(null);
                      startScanning();
                    }}
                    className="px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-lg text-white font-medium flex items-center"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    Try Again
                  </motion.button>
                  
                  <motion.button
                    onClick={onBack}
                    className="px-6 py-3 bg-white/10 hover:bg-white/20 rounded-lg text-white font-medium flex items-center"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                    </svg>
                    Back to Tools
                  </motion.button>
                </div>
              </div>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
} 