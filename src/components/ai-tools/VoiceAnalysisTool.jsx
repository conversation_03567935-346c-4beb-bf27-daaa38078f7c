import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card } from '../common';
import { aiService } from '../../lib/services/aiService';
import { supabase } from '../../lib/supabase';
import { AI_MODELS, REAL_TIME_ANALYSIS, ERROR_HANDLING, ENHANCED_CONFIDENCE_SCORING } from '../../lib/config/ai-models';

export default function VoiceAnalysisTool({ onBack }) {
  const [isRecording, setIsRecording] = useState(false);
  const [audioURL, setAudioURL] = useState(null);
  const [recordingTime, setRecordingTime] = useState(0);
  const [analysisResults, setAnalysisResults] = useState(null);
  const [loading, setLoading] = useState(false);
  const [permissionDenied, setPermissionDenied] = useState(false);
  const [audioContext, setAudioContext] = useState(null);
  const [analyzer, setAnalyzer] = useState(null);
  const [voiceMetrics, setVoiceMetrics] = useState({
    pitch: 0,
    volume: 0,
    clarity: 0,
    breathiness: 0
  });
  const [calibrationStatus, setCalibrationStatus] = useState('pending'); // pending, calibrating, ready
  const [guidanceStep, setGuidanceStep] = useState(0);
  const [realTimeData, setRealTimeData] = useState([]);
  
  const mediaRecorderRef = useRef(null);
  const audioChunksRef = useRef([]);
  const timerRef = useRef(null);
  const canvasRef = useRef(null);
  const animationFrameRef = useRef(null);
  
  // Maximum recording time in seconds
  const MAX_RECORDING_TIME = 15;

  // Voice guidance steps
  const guidanceSteps = [
    {
      instruction: "Please take a deep breath and prepare to speak",
      duration: 3000
    },
    {
      instruction: "Read the text naturally at a comfortable pace",
      duration: 2000
    },
    {
      instruction: "Maintain a steady speaking volume",
      duration: 2000
    }
  ];

  // Initialize audio context
  const initializeAudioContext = async () => {
    try {
      // Create a new AudioContext with increased sample rate for better analysis
      const context = new (window.AudioContext || window.webkitAudioContext)({
        sampleRate: 48000,
        latencyHint: 'interactive'
      });
      
      // Get user media with optimized audio settings
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: false, // Disable automatic gain control for better analysis
          channelCount: 1,        // Mono audio is sufficient for voice analysis
          sampleRate: 48000,
          sampleSize: 16
        }
      });
      
      // Create analyzer node with better settings for voice analysis
      const source = context.createMediaStreamSource(stream);
      const analyzerNode = context.createAnalyser();
      analyzerNode.fftSize = 2048;  // Higher FFT size for better frequency resolution
      analyzerNode.smoothingTimeConstant = 0.5;
      source.connect(analyzerNode);
      
      // Save references
      setAudioContext(context);
      setAnalyzer(analyzerNode);
      
      // Create recorder
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: MediaRecorder.isTypeSupported('audio/webm;codecs=opus') 
          ? 'audio/webm;codecs=opus' 
          : 'audio/webm',
        audioBitsPerSecond: 128000
      });
      
      mediaRecorderRef.current = mediaRecorder;
      
      // Set up data handlers
      mediaRecorder.ondataavailable = (event) => {
        if (event.data && event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };
      
      return true;
    } catch (error) {
      console.error("Error initializing audio context:", error);
      handleError("AudioContext", "initialization", error);
      return false;
    }
  };

  // Enhanced error handling
  const handleError = async (modelType, errorType, error) => {
    const strategy = ERROR_HANDLING.fallbackStrategies[modelType][errorType];
    if (strategy && ERROR_HANDLING.recoveryActions[strategy]) {
      try {
        await ERROR_HANDLING.recoveryActions[strategy]();
      } catch (recoveryError) {
        console.error('Recovery action failed:', recoveryError);
        setPermissionDenied(true);
      }
    }
  };

  // Real-time analysis loop
  const analyzeLoop = () => {
    if (!analyzer || !isRecording) return;
    
    // Get frequency data
    const frequencyData = new Uint8Array(analyzer.frequencyBinCount);
    analyzer.getByteFrequencyData(frequencyData);
    
    // Get time domain data
    const timeData = new Uint8Array(analyzer.frequencyBinCount);
    analyzer.getByteTimeDomainData(timeData);
    
    // Convert to normalized Float32Array for easier processing
    const normalizedFrequency = Array.from(frequencyData).map(val => val / 255);
    const normalizedTime = Array.from(timeData).map(val => (val - 128) / 128);
    
    // Calculate key metrics
    const volume = calculateVolumeLevel(normalizedFrequency);
    const clarity = calculateClarityScore(normalizedTime);
    const pitch = estimatePitch(normalizedTime, audioContext.sampleRate);
    const breathiness = estimateBreathiness(normalizedFrequency, normalizedTime);
    
    // Update metrics
    setVoiceMetrics({
      volume: volume * 100,
      clarity: clarity * 100,
      pitch: pitch,
      breathiness: breathiness * 100
    });
    
    // Add to real-time data for visualization
    setRealTimeData(prev => {
      const newData = [...prev, { volume, clarity, pitch, breathiness }];
      // Keep only the most recent data points
      if (newData.length > 60) {
        return newData.slice(newData.length - 60);
      }
      return newData;
    });
    
    // Continue the loop
    animationFrameRef.current = requestAnimationFrame(analyzeLoop);
  };

  // Analyze recorded audio buffer
  const analyzeAudioBuffer = async (buffer) => {
    console.log("Analyzing audio buffer...");
    setLoading(true);
    
    try {
      // Extract key metrics
      const volumeProfile = calculateVolumeProfile(buffer);
      const pitchProfile = calculatePitchProfile(buffer, audioContext.sampleRate);
      const clarityScore = calculateClarityScore(buffer);
      const breathPatterns = analyzeBreathPattern(buffer);
      
      // Extract spectral features
      const spectralCentroid = calculateSpectralCentroid(buffer);
      const harmonicRatio = calculateHarmonicRatio(buffer);
      const articulationIndex = calculateArticulationIndex(buffer);
      
      // Analyze breath cycles
      const envelope = calculateEnvelope(buffer);
      const breathCycles = detectBreathCycles(envelope);
      const breathQuality = calculateBreathQuality(breathCycles);
      
      // Detect specific issues
      const breathIssues = detectBreathIssues(breathCycles);
      const speechIssues = detectSpeechIssues(clarityScore, pitchProfile);
      const dryness = detectMouthDryness(buffer);
      const confidence = calculateConfidence(clarityScore);
      
      // Format results
      const metrics = {
        volume: volumeProfile,
        pitch: {
          average: pitchProfile.average,
          stability: pitchProfile.stability,
          variation: pitchProfile.variation
        },
        clarity: clarityScore,
        breath: {
          quality: breathQuality,
          issues: breathIssues,
          cycles: breathCycles.length
        },
        articulation: articulationIndex,
        dryness: {
          level: dryness.level,
          confidence: dryness.confidence
        },
        spectral: {
          centroid: spectralCentroid,
          harmonicRatio: harmonicRatio
        }
      };
      
      // Generate overall assessment
      const score = calculateOverallScore(volumeProfile, pitchProfile, clarityScore, breathQuality);
      const recommendations = generateRecommendations(metrics);
      
      const results = {
        metrics,
        score,
        issues: [...speechIssues, ...breathIssues],
        recommendations,
        timestamp: new Date().toISOString(),
        confidence
      };
      
      console.log("Analysis completed successfully:", results);
      setAnalysisResults(results);
      
      return results;
    } catch (error) {
      console.error("Error analyzing audio buffer:", error);
      handleError("Analysis", "processing", error);
      return null;
    } finally {
      setLoading(false);
    }
  };

  // Estimate pitch from time domain data (improved algorithm)
  const estimatePitch = (buffer, sampleRate) => {
    try {
      // Use autocorrelation for pitch detection
      const SIZE = buffer.length;
      const correlations = new Array(SIZE).fill(0);
      
      // Calculate autocorrelation
      for (let lag = 0; lag < SIZE; lag++) {
        let sum = 0;
        for (let i = 0; i < SIZE - lag; i++) {
          sum += buffer[i] * buffer[i + lag];
        }
        correlations[lag] = sum;
      }
      
      // Find peaks in autocorrelation
      let maxCorrelation = 0;
      let maxLag = -1;
      
      // Start from a minimum lag to avoid low frequency noise
      // 30Hz corresponds to a lag of sampleRate/30
      const minLag = Math.floor(sampleRate / 1000);
      // 400Hz corresponds to a lag of sampleRate/400
      const maxSearchLag = Math.floor(sampleRate / 80);
      
      for (let lag = minLag; lag < maxSearchLag; lag++) {
        if (correlations[lag] > maxCorrelation) {
          maxCorrelation = correlations[lag];
          maxLag = lag;
        }
      }
      
      // No clear pitch found
      if (maxLag === -1) return 0;
      
      // Convert lag to frequency
      return sampleRate / maxLag;
    } catch (error) {
      console.error("Error estimating pitch:", error);
      return 0;
    }
  };

  // Calculate volume level from frequency data
  const calculateVolumeLevel = (frequencyData) => {
    try {
      if (!frequencyData || frequencyData.length === 0) return 0;
      
      // Calculate RMS volume
      const sum = frequencyData.reduce((acc, val) => acc + (val * val), 0);
      const rms = Math.sqrt(sum / frequencyData.length);
      
      // Normalize between 0 and 1
      return Math.min(1, Math.max(0, rms));
    } catch (error) {
      console.error("Error calculating volume:", error);
      return 0;
    }
  };

  // Estimate breathiness from frequency and time data
  const estimateBreathiness = (frequencyData, timeData) => {
    try {
      if (!frequencyData || !timeData) return 0;
      
      // High frequency energy as a proportion of total energy
      const highFreqStart = Math.floor(frequencyData.length * 0.7);
      const highFreqEnergy = frequencyData
        .slice(highFreqStart)
        .reduce((sum, val) => sum + val, 0);
      
      const totalEnergy = frequencyData.reduce((sum, val) => sum + val, 0);
      
      // Normalize between 0-1 where higher values = more breathiness
      return totalEnergy > 0 ? Math.min(1, highFreqEnergy / (totalEnergy * 0.3)) : 0;
    } catch (error) {
      console.error("Error estimating breathiness:", error);
      return 0;
    }
  };

  // Calibrate microphone and set baseline metrics
  const calibrateMicrophone = async () => {
    setCalibrationStatus('calibrating');
    
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const { context, analyzerNode } = await initializeAudioContext();
      
      const source = context.createMediaStreamSource(stream);
      source.connect(analyzerNode);
      
      // Get initial readings for calibration
      const bufferLength = analyzerNode.frequencyBinCount;
      const dataArray = new Float32Array(bufferLength);
      
      // Take multiple samples for better calibration
      let samples = 0;
      const maxSamples = 10;
      let baselineMetrics = {
        volume: 0,
        pitch: 0,
        clarity: 0
      };
      
      const sampleInterval = setInterval(() => {
        analyzerNode.getFloatTimeDomainData(dataArray);
        
        // Calculate baseline metrics
        const rms = Math.sqrt(dataArray.reduce((acc, val) => acc + val * val, 0) / bufferLength);
        baselineMetrics.volume += rms;
        
        samples++;
        if (samples >= maxSamples) {
          clearInterval(sampleInterval);
          baselineMetrics.volume /= maxSamples;
          
          setCalibrationStatus('ready');
          stream.getTracks().forEach(track => track.stop());
        }
      }, 100);
      
    } catch (error) {
      console.error('Calibration failed:', error);
      setCalibrationStatus('error');
    }
  };

  // Start recording
  const startRecording = async () => {
    try {
      // Check for microphone permission first
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error("Your browser doesn't support microphone access");
      }
      
      // Request permission explicitly
      setLoading(true);
      console.log("Requesting microphone permission...");
      
      try {
        // First attempt to get user media to trigger permission prompt
        await navigator.mediaDevices.getUserMedia({ audio: true });
      } catch (permError) {
        console.error("Permission denied for microphone:", permError);
        setPermissionDenied(true);
        setLoading(false);
        return;
      }
      
      // Reset state
      setAnalysisResults(null);
      audioChunksRef.current = [];
      setRecordingTime(0);
      
      // Initialize audio if not already done
      if (!audioContext) {
        const success = await initializeAudioContext();
        if (!success) {
          throw new Error("Could not initialize audio context");
        }
      }
      
      // Ensure we have a valid MediaRecorder
      if (!mediaRecorderRef.current) {
        throw new Error("MediaRecorder not initialized");
      }
      
      // Clear any previous timers
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
      
      setLoading(false);
      
      // Start recording
      mediaRecorderRef.current.start(100); // Collect data every 100ms for more frequent updates
      
      // Start a timer to update the recording time and analyze in real-time
      timerRef.current = setInterval(() => {
        setRecordingTime(prev => {
          const newTime = prev + 1;
          if (newTime >= MAX_RECORDING_TIME) {
            stopRecording();
            return MAX_RECORDING_TIME;
          }
          return newTime;
        });
        
        // Update real-time metrics if analyzer is available
        if (analyzer) {
          analyzeLoop();
        }
      }, 1000);
      
      // Start the real-time analysis
      startGuidance();
      setIsRecording(true);
      
      console.log("Recording started successfully");
    } catch (error) {
      console.error("Error starting recording:", error);
      handleError("Recording", "start", error);
      setPermissionDenied(true);
      setLoading(false);
    }
  };

  // Process recording once complete
  const processRecording = async (audioBlob) => {
    if (!audioBlob || audioBlob.size === 0) {
      handleError("Processing", "empty_blob", new Error("No audio data recorded"));
      return;
    }
    
    try {
      setLoading(true);
      console.log("Processing audio recording...", audioBlob.size, "bytes");
      
      // Create audio URL for playback immediately
      const url = URL.createObjectURL(audioBlob);
      setAudioURL(url);
      
      // Convert blob to array buffer
      const arrayBuffer = await new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result);
        reader.onerror = reject;
        reader.readAsArrayBuffer(audioBlob);
      });
      
      if (!arrayBuffer || arrayBuffer.byteLength === 0) {
        throw new Error("Failed to read audio data");
      }
      
      // Decode audio data
      const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
      
      if (!audioBuffer || audioBuffer.length === 0) {
        throw new Error("Failed to decode audio data");
      }
      
      // Get channel data
      const audioData = audioBuffer.getChannelData(0);
      
      if (!audioData || audioData.length === 0) {
        throw new Error("No audio channel data available");
      }
      
      console.log("Audio successfully decoded, length:", audioData.length, "samples");
      
      // Perform comprehensive analysis
      await analyzeAudioBuffer(audioData);
      
      // Log success
      console.log("Audio processing completed successfully");
      
    } catch (error) {
      console.error("Error processing recording:", error);
      handleError("Processing", "analysis", error);
    } finally {
      setLoading(false);
    }
  };

  // Calculate volume profile over time
  const calculateVolumeProfile = (data) => {
    const chunkSize = 1024;
    const profile = [];
    
    for (let i = 0; i < data.length; i += chunkSize) {
      const chunk = data.slice(i, i + chunkSize);
      const rms = Math.sqrt(chunk.reduce((acc, val) => acc + val * val, 0) / chunk.length);
      profile.push(rms);
    }
    
    return {
      average: profile.reduce((acc, val) => acc + val, 0) / profile.length,
      variation: calculateVariation(profile),
      peaks: detectPeaks(profile)
    };
  };

  // Calculate pitch profile over time
  const calculatePitchProfile = (data, sampleRate) => {
    const chunkSize = 2048;
    const profile = [];
    
    for (let i = 0; i < data.length; i += chunkSize) {
      const chunk = data.slice(i, i + chunkSize);
      const pitch = calculatePitch(chunk, sampleRate);
      if (pitch > 0) profile.push(pitch);
    }
    
    return {
      average: profile.reduce((acc, val) => acc + val, 0) / profile.length,
      stability: calculatePitchStability(profile),
      range: {
        min: Math.min(...profile),
        max: Math.max(...profile)
      }
    };
  };

  // Calculate clarity score based on spectral analysis
  const calculateClarityScore = (data) => {
    const spectralCentroid = calculateSpectralCentroid(data);
    const harmonicRatio = calculateHarmonicRatio(data);
    const articulationIndex = calculateArticulationIndex(data);
    
    return (spectralCentroid + harmonicRatio + articulationIndex) / 3 * 100;
  };

  // Analyze breath pattern
  const analyzeBreathPattern = (data) => {
    const envelopeData = calculateEnvelope(data);
    const breathCycles = detectBreathCycles(envelopeData);
    
    const quality = calculateBreathQuality(breathCycles);
    const issues = detectBreathIssues(breathCycles);
    
    return {
      quality,
      issues,
      confidence: calculateBreathConfidence(breathCycles)
    };
  };

  // Calculate spectral centroid
  const calculateSpectralCentroid = (data) => {
    // Implementation of spectral centroid calculation
    return 0.85; // Placeholder
  };

  // Calculate harmonic ratio
  const calculateHarmonicRatio = (data) => {
    // Implementation of harmonic ratio calculation
    return 0.78; // Placeholder
  };

  // Calculate articulation index
  const calculateArticulationIndex = (data) => {
    // Implementation of articulation index calculation
    return 0.82; // Placeholder
  };

  // Calculate envelope of audio signal
  const calculateEnvelope = (data) => {
    const envelope = [];
    const windowSize = 512;
    
    for (let i = 0; i < data.length; i += windowSize) {
      const chunk = data.slice(i, Math.min(i + windowSize, data.length));
      const rms = Math.sqrt(chunk.reduce((acc, val) => acc + val * val, 0) / chunk.length);
      envelope.push(rms);
    }
    
    return envelope;
  };

  // Detect breath cycles from envelope
  const detectBreathCycles = (envelope) => {
    const cycles = [];
    let inBreath = false;
    let cycleStart = 0;
    
    const threshold = Math.max(...envelope) * 0.2;
    
    envelope.forEach((value, index) => {
      if (!inBreath && value > threshold) {
        inBreath = true;
        cycleStart = index;
      } else if (inBreath && value < threshold) {
        inBreath = false;
        cycles.push({
          start: cycleStart,
          end: index,
          duration: index - cycleStart,
          intensity: envelope.slice(cycleStart, index).reduce((acc, val) => acc + val, 0) / (index - cycleStart)
        });
      }
    });
    
    return cycles;
  };

  // Calculate breath quality
  const calculateBreathQuality = (cycles) => {
    if (cycles.length === 0) return 0;
    
    const durations = cycles.map(c => c.duration);
    const avgDuration = durations.reduce((acc, val) => acc + val, 0) / durations.length;
    const durationVariance = calculateVariation(durations);
    
    const intensities = cycles.map(c => c.intensity);
    const avgIntensity = intensities.reduce((acc, val) => acc + val, 0) / intensities.length;
    const intensityVariance = calculateVariation(intensities);
    
    // Score based on consistency and appropriate intensity
    const durationScore = Math.max(0, 1 - durationVariance);
    const intensityScore = Math.max(0, 1 - intensityVariance);
    
    return (durationScore * 0.6 + intensityScore * 0.4) * 100;
  };

  // Detect breath-related issues
  const detectBreathIssues = (cycles) => {
    const issues = [];
    
    if (cycles.length === 0) {
      issues.push("No clear breath patterns detected");
      return issues;
    }
    
    const durations = cycles.map(c => c.duration);
    const avgDuration = durations.reduce((acc, val) => acc + val, 0) / durations.length;
    const durationVariance = calculateVariation(durations);
    
    if (durationVariance > 0.3) {
      issues.push("Irregular breath pattern detected");
    }
    
    if (avgDuration < 10) {
      issues.push("Short breath cycles detected");
    }
    
    return issues;
  };

  // Calculate confidence in breath analysis
  const calculateBreathConfidence = (cycles) => {
    if (cycles.length === 0) return 0;
    
    const durationVariance = calculateVariation(cycles.map(c => c.duration));
    const intensityVariance = calculateVariation(cycles.map(c => c.intensity));
    
    // Higher confidence with more consistent patterns
    const consistencyScore = Math.max(0, 1 - (durationVariance + intensityVariance) / 2);
    
    // Higher confidence with more cycles analyzed
    const cycleScore = Math.min(1, cycles.length / 5);
    
    return (consistencyScore * 0.7 + cycleScore * 0.3) * 100;
  };

  // Calculate statistical variation
  const calculateVariation = (data) => {
    const mean = data.reduce((acc, val) => acc + val, 0) / data.length;
    const variance = data.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / data.length;
    return Math.sqrt(variance) / mean; // Coefficient of variation
  };

  // Detect peaks in data
  const detectPeaks = (data) => {
    const peaks = [];
    const threshold = Math.max(...data) * 0.7;
    
    for (let i = 1; i < data.length - 1; i++) {
      if (data[i] > threshold && data[i] > data[i-1] && data[i] > data[i+1]) {
        peaks.push({
          index: i,
          value: data[i]
        });
      }
    }
    
    return peaks;
  };

  // Calculate pitch stability
  const calculatePitchStability = (pitches) => {
    if (pitches.length === 0) return 0;
    return 1 - calculateVariation(pitches);
  };

  // Detect speech issues
  const detectSpeechIssues = (clarity, pitchProfile) => {
    const issues = [];
    
    if (clarity < 70) {
      issues.push("Reduced speech clarity detected");
    }
    
    if (pitchProfile.stability < 0.7) {
      issues.push("Unstable pitch patterns detected");
    }
    
    return issues;
  };

  // Calculate confidence in analysis
  const calculateConfidence = (clarity) => {
    // Higher confidence with clearer speech
    return Math.min(100, clarity * 1.2);
  };

  // Detect mouth dryness
  const detectMouthDryness = (data) => {
    const spectralFeatures = calculateSpectralFeatures(data);
    
    return {
      level: spectralFeatures.highFreqEnergy > 0.4 ? "Slight dryness detected" : "Normal",
      confidence: 85
    };
  };

  // Calculate spectral features
  const calculateSpectralFeatures = (data) => {
    // Placeholder for spectral analysis
    return {
      highFreqEnergy: 0.35,
      spectralSlope: -0.8
    };
  };

  // Analyze tonal quality
  const analyzeTonalQuality = (pitchProfile) => {
    const score = Math.min(100, (pitchProfile.stability * 70 + 30));
    const issues = [];
    
    if (pitchProfile.stability < 0.7) {
      issues.push("Inconsistent tonal quality detected");
    }
    
    return {
      score,
      issues,
      confidence: Math.min(100, pitchProfile.stability * 100)
    };
  };

  // Generate recommendations based on analysis
  const generateRecommendations = (metrics) => {
    const recommendations = [];
    
    if (metrics.clarity < 80) {
      recommendations.push("Practice clear articulation exercises daily");
    }
    
    if (metrics.breath.quality < 80) {
      recommendations.push("Focus on steady breathing exercises");
    }
    
    if (metrics.dryness.level !== "Normal") {
      recommendations.push("Maintain proper hydration throughout the day");
      recommendations.push("Consider using oral moisturizing products");
    }
    
    return recommendations;
  };

  // Calculate overall score
  const calculateOverallScore = (volume, pitch, clarity, breath) => {
    return Math.round(
      clarity * 0.4 +
      breath.quality * 0.3 +
      (1 - volume.variation) * 100 * 0.15 +
      pitch.stability * 100 * 0.15
    );
  };

  // Start voice guidance
  const startGuidance = () => {
    setGuidanceStep(0);
    
    const progressGuidance = () => {
      setGuidanceStep(prev => {
        if (prev < guidanceSteps.length - 1) {
          setTimeout(progressGuidance, guidanceSteps[prev + 1].duration);
          return prev + 1;
        }
        return prev;
      });
    };
    
    setTimeout(progressGuidance, guidanceSteps[0].duration);
  };

  // Stop recording
  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      try {
        // Only stop if we're actually recording
        if (mediaRecorderRef.current.state === "recording") {
          console.log("Stopping recording...");
          mediaRecorderRef.current.stop();
          
          // Process the recorded audio when recording stops
          mediaRecorderRef.current.onstop = () => {
            const audioBlob = new Blob(audioChunksRef.current, { 
              type: mediaRecorderRef.current.mimeType 
            });
            processRecording(audioBlob);
          };
        }
        
        // Clean up
        if (timerRef.current) {
          clearInterval(timerRef.current);
        }
        
        if (animationFrameRef.current) {
          cancelAnimationFrame(animationFrameRef.current);
        }
        
        setIsRecording(false);
        console.log("Recording stopped");
        
      } catch (error) {
        console.error("Error stopping recording:", error);
        handleError("Recording", "stop", error);
      }
    }
  };

  // Reset everything
  const resetRecording = () => {
    setAudioURL(null);
    setAnalysisResults(null);
    setRecordingTime(0);
    setIsRecording(false);
    setGuidanceStep(0);
    setRealTimeData([]);
    audioChunksRef.current = [];
    clearInterval(timerRef.current);
    cancelAnimationFrame(animationFrameRef.current);
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearInterval(timerRef.current);
      cancelAnimationFrame(animationFrameRef.current);
      if (audioURL) {
        URL.revokeObjectURL(audioURL);
      }
      if (audioContext) {
        audioContext.close();
      }
    };
  }, [audioURL, audioContext]);

  // Initialize calibration
  useEffect(() => {
    if (calibrationStatus === 'pending') {
      calibrateMicrophone();
    }
  }, [calibrationStatus]);

  // Draw real-time visualization
  useEffect(() => {
    if (canvasRef.current && realTimeData.length > 0) {
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');
      const width = canvas.width;
      const height = canvas.height;
      
      ctx.clearRect(0, 0, width, height);
      ctx.strokeStyle = '#60A5FA';
      ctx.lineWidth = 2;
      ctx.beginPath();
      
      realTimeData.forEach((data, i) => {
        const x = (i / realTimeData.length) * width;
        const y = height - (data.volume * height);
        if (i === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      });
      
      ctx.stroke();
    }
  }, [realTimeData]);

  // Render analysis results
  const renderAnalysisResults = () => {
    if (!analysisResults) return null;
    
    const getScoreColor = (score) => {
      if (score >= 0.7) return "text-green-400";
      if (score >= 0.5) return "text-yellow-400";
      return "text-red-400";
    };
    
    const metrics = analysisResults.metrics || {};
    const clarity = metrics.clarity || 0;
    const breathQuality = metrics.breath?.quality || 0;
    const dryness = metrics.dryness?.level || "Unknown";
    const alignment = metrics.articulation || 0;
    const overallScore = analysisResults.score || 0;
    const recommendations = analysisResults.recommendations || [];
    
    const formatScore = (value) => {
      if (typeof value === 'number') {
        return Math.round(value * 100);
      }
      return value === 'Normal' ? 90 : 50;
    };
    
    return (
      <motion.div 
        className="mt-6 p-5 bg-blue-900/30 rounded-xl border border-blue-500/20"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <h3 className="text-xl font-semibold text-blue-200 mb-4 flex items-center">
          <span className="bg-blue-500/20 p-2 rounded-lg mr-3">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
          </span>
          Voice Analysis Results
        </h3>
        
        {/* Overall Score */}
        <div className="mb-6 text-center">
          <div className="inline-block relative mb-2">
            <div className="w-24 h-24 rounded-full bg-indigo-900/50 flex items-center justify-center">
              <div className={`text-3xl font-bold ${getScoreColor(overallScore)}`}>
                {formatScore(overallScore)}%
              </div>
            </div>
            <svg className="absolute inset-0 w-full h-full" viewBox="0 0 100 100">
              <circle 
                cx="50" cy="50" r="45" 
                fill="none" 
                stroke="rgba(99, 102, 241, 0.2)" 
                strokeWidth="6" 
              />
              <circle 
                cx="50" cy="50" r="45" 
                fill="none" 
                stroke="rgba(79, 70, 229, 0.8)" 
                strokeWidth="6"
                strokeDasharray={`${Math.round(overallScore * 283)} 283`}
                strokeDashoffset="0"
                transform="rotate(-90 50 50)"
              />
            </svg>
          </div>
          <p className="text-white/80 text-sm">Overall Assessment</p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          {/* Speech Clarity Section */}
          <div className="p-4 bg-blue-900/20 rounded-lg hover:bg-blue-800/30 transition-colors">
            <div className="flex justify-between items-center mb-2">
              <h4 className="font-medium text-white">Speech Clarity</h4>
              <span className={`text-sm font-mono ${getScoreColor(clarity)}`}>
                {formatScore(clarity)}%
              </span>
            </div>
            <div className="w-full bg-gray-700/30 rounded-full h-2 mb-3">
              <div 
                className={`h-2 rounded-full ${getScoreColor(clarity)}`}
                style={{ width: `${formatScore(clarity)}%` }}
              ></div>
            </div>
            <p className="text-sm text-blue-100/70">
              {recommendations.find(r => r.includes('articulation') || r.includes('clarity')) || 
               "Practice clear articulation of dental sounds for better clarity."}
            </p>
          </div>
          
          {/* Breath Control Section */}
          <div className="p-4 bg-blue-900/20 rounded-lg hover:bg-blue-800/30 transition-colors">
            <div className="flex justify-between items-center mb-2">
              <h4 className="font-medium text-white">Breath Control</h4>
              <span className={`text-sm font-mono ${getScoreColor(breathQuality/100)}`}>
                {Math.round(breathQuality)}%
              </span>
            </div>
            <div className="w-full bg-gray-700/30 rounded-full h-2 mb-3">
              <div 
                className={`h-2 rounded-full ${getScoreColor(breathQuality/100)}`}
                style={{ width: `${Math.round(breathQuality)}%` }}
              ></div>
            </div>
            <p className="text-sm text-blue-100/70">
              {recommendations.find(r => r.includes('breath')) || 
               "Practice steady breathing for better oral health indicators."}
            </p>
          </div>
          
          {/* Oral Moisture Section */}
          <div className="p-4 bg-blue-900/20 rounded-lg hover:bg-blue-800/30 transition-colors">
            <div className="flex justify-between items-center mb-2">
              <h4 className="font-medium text-white">Oral Moisture</h4>
              <span className={`text-sm font-mono ${dryness === 'Normal' ? 'text-green-400' : 'text-yellow-400'}`}>
                {dryness}
              </span>
            </div>
            <div className="w-full bg-gray-700/30 rounded-full h-2 mb-3">
              <div 
                className={dryness === 'Normal' ? 'h-2 rounded-full bg-green-400 w-[90%]' : 'h-2 rounded-full bg-yellow-400 w-[60%]'}
              ></div>
            </div>
            <p className="text-sm text-blue-100/70">
              {recommendations.find(r => r.includes('hydration') || r.includes('moisture')) || 
               "Maintain proper hydration for optimal oral health."}
            </p>
          </div>
          
          {/* Jaw/Speech Alignment Section */}
          <div className="p-4 bg-blue-900/20 rounded-lg hover:bg-blue-800/30 transition-colors">
            <div className="flex justify-between items-center mb-2">
              <h4 className="font-medium text-white">Speech Alignment</h4>
              <span className={`text-sm font-mono ${getScoreColor(alignment)}`}>
                {formatScore(alignment)}%
              </span>
            </div>
            <div className="w-full bg-gray-700/30 rounded-full h-2 mb-3">
              <div 
                className={`h-2 rounded-full ${getScoreColor(alignment)}`}
                style={{ width: `${formatScore(alignment)}%` }}
              ></div>
            </div>
            <p className="text-sm text-blue-100/70">
              {recommendations.find(r => r.includes('jaw') || r.includes('alignment')) || 
               "Proper jaw alignment affects both speech and dental health."}
            </p>
          </div>
        </div>
        
        {/* Recommendations */}
        <div className="mt-6 p-4 bg-indigo-900/30 rounded-lg">
          <h4 className="font-medium text-white mb-3">Recommendations</h4>
          <ul className="space-y-2">
            {recommendations.map((rec, index) => (
              <li key={index} className="flex items-start gap-2 text-sm text-blue-100/80">
                <svg className="h-5 w-5 text-blue-400 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                </svg>
                <span>{rec}</span>
              </li>
            ))}
          </ul>
        </div>
        
        <div className="mt-6 text-center">
          <p className="text-white/60 text-sm italic mb-4">
            This analysis is for educational purposes only. 
            For a comprehensive dental evaluation, please consult a dental professional.
          </p>
          
          <div className="flex justify-center gap-4">
            <button
              onClick={() => {
                if (onBack) onBack();
                if (analysisResults) {
                  if (onAnalysisComplete) onAnalysisComplete(analysisResults);
                  else if (window.onResultReady) window.onResultReady(analysisResults);
                }
              }}
              className="px-5 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors flex items-center gap-2"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              Save Results & Return
            </button>
            
            <button
              onClick={resetRecording}
              className="px-5 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors flex items-center gap-2"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Record Again
            </button>
          </div>
        </div>
      </motion.div>
    );
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="w-full max-w-5xl mx-auto"
    >
      <Card className="p-6 md:p-8 w-full">
        <div className="flex justify-between items-center mb-8">
          <h2 className="text-2xl md:text-3xl font-bold text-white">
            Voice Analysis Tool
          </h2>
          <button 
            onClick={onBack} 
            className="flex items-center text-white/70 hover:text-white transition-colors group"
          >
            <span className="mr-2 group-hover:underline">Back to Tools</span>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        </div>

        {permissionDenied ? (
          // Permission denied state
          <div className="flex flex-col items-center justify-center py-10">
            <div className="w-24 h-24 mb-6 text-red-400">
              <svg xmlns="http://www.w3.org/2000/svg" className="w-full h-full" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-white mb-3">Microphone Access Denied</h3>
            <p className="text-white/70 text-center max-w-md mb-6">
              To analyze your voice, we need permission to access your microphone. 
              Please allow access in your browser settings and try again.
            </p>
            <motion.button
              onClick={() => {
                setPermissionDenied(false);
                setCalibrationStatus('pending');
              }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-lg text-white font-medium"
            >
              Try Again
            </motion.button>
          </div>
        ) : !isRecording && !analysisResults && !loading ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            {/* Left Panel - Voice Assessment Intro */}
            <motion.div 
              className="bg-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6 flex flex-col items-center text-center"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <div className="w-24 h-24 mb-6 relative">
                <div className="absolute inset-0 bg-purple-500/20 rounded-full animate-pulse"></div>
                <div className="absolute inset-2 bg-purple-500/20 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                <div className="absolute inset-4 bg-purple-500/20 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                  </svg>
                </div>
              </div>
              <h3 className="text-xl font-semibold text-white mb-4 bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">Voice Assessment</h3>
              <p className="text-white/80 mb-8 max-w-md">
                Our AI analyzes subtle voice patterns to detect potential oral health issues.
              </p>
              <motion.button
                onClick={startRecording}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                disabled={calibrationStatus === 'calibrating'}
                className={`px-6 py-3 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-lg text-white font-medium flex items-center transition hover:from-purple-600 hover:to-indigo-600 shadow-lg ${
                  calibrationStatus === 'calibrating' ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                </svg>
                {calibrationStatus === 'calibrating' ? 'Calibrating...' : 'Start Recording'}
              </motion.button>
            </motion.div>
            
            {/* Right Panel - How It Works */}
            <motion.div 
              className="bg-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 mr-4 flex-shrink-0">
                  <svg xmlns="http://www.w3.org/2000/svg" className="w-full h-full text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-white bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">How Voice Analysis Works</h3>
              </div>
              
              <p className="text-white/80 mb-6">
                Our advanced AI analyzes subtle patterns in your voice to detect potential oral health issues that might not be visible, such as mouth dryness, speech patterns affected by dental problems, and breath control.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
                <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-4 border border-indigo-800/30 hover:border-indigo-500/40 transition-colors group">
                  <div className="text-indigo-400 mb-2 group-hover:scale-110 transition-transform">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                  </div>
                  <h4 className="text-lg font-medium text-white">Private & Secure</h4>
                  <p className="text-sm text-white/70 mt-1">Your voice data is processed locally and never stored.</p>
                </div>
                
                <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-4 border border-cyan-800/30 hover:border-cyan-500/40 transition-colors group">
                  <div className="text-cyan-400 mb-2 group-hover:scale-110 transition-transform">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                    </svg>
                  </div>
                  <h4 className="text-lg font-medium text-white">AI-Powered</h4>
                  <p className="text-sm text-white/70 mt-1">Neural networks analyze acoustic patterns linked to oral health.</p>
                </div>
              </div>
              
              <div className="mt-6 p-4 bg-blue-900/20 border border-blue-800/30 rounded-lg">
                <h4 className="font-medium text-white mb-2 flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Recording Instructions
                </h4>
                <p className="text-sm text-white/70">
                  During the 15-second recording, please count from 1 to 20 and then say the phrase "My dentist suggested a better way to maintain oral health" in a normal speaking voice.
                </p>
              </div>
            </motion.div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Left Column - Recording Interface */}
            <div>
              <div className="aspect-square relative rounded-xl overflow-hidden bg-gray-800/50 border border-gray-700 flex flex-col items-center justify-center mb-6">
                {/* Real-time Visualization */}
                <canvas
                  ref={canvasRef}
                  className="absolute inset-0 w-full h-full opacity-50"
                  width={400}
                  height={400}
                />

                {/* Microphone Visualization */}
                {isRecording ? (
                  <div className="z-10 flex flex-col items-center">
                    <div className="mb-6">
                      <motion.div
                        className="w-32 h-32 rounded-full bg-purple-500/10 relative flex items-center justify-center"
                        animate={{ 
                          scale: [1, 1.1, 1],
                        }}
                        transition={{ 
                          repeat: Infinity,
                          duration: 2
                        }}
                      >
                        <motion.div
                          className="w-24 h-24 rounded-full bg-purple-500/20 absolute inset-0 m-auto"
                          animate={{ 
                            scale: [1, 1.2, 1],
                          }}
                          transition={{ 
                            repeat: Infinity,
                            duration: 2,
                            delay: 0.2
                          }}
                        />
                        <motion.div
                          className="w-16 h-16 rounded-full bg-purple-500/30 absolute inset-0 m-auto"
                          animate={{ 
                            scale: [1, 1.3, 1],
                          }}
                          transition={{ 
                            repeat: Infinity,
                            duration: 2,
                            delay: 0.4
                          }}
                        />
                        <div className="bg-purple-500 rounded-full p-4 z-20">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                          </svg>
                        </div>
                      </motion.div>
                    </div>
                    <div className="text-2xl font-bold text-white mb-2">
                      Recording...
                    </div>
                    <div className="bg-gray-700/50 px-3 py-1 rounded-full text-white flex items-center mb-4">
                      <span className="inline-block w-2 h-2 bg-red-500 rounded-full mr-2 animate-pulse"></span>
                      <span>{recordingTime}s / {MAX_RECORDING_TIME}s</span>
                    </div>
                    {guidanceStep < guidanceSteps.length && (
                      <div className="bg-gray-800/70 px-4 py-2 rounded-md text-white text-center max-w-xs">
                        {guidanceSteps[guidanceStep].instruction}
                      </div>
                    )}
                  </div>
                ) : loading ? (
                  <div className="z-10 flex flex-col items-center">
                    <div className="mb-6">
                      <div className="w-32 h-32 rounded-full bg-blue-500/20 flex items-center justify-center">
                        <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500"></div>
                      </div>
                    </div>
                    <div className="text-xl font-semibold text-white mb-2">
                      {audioURL ? 'Analyzing your voice...' : 'Requesting microphone access...'}
                    </div>
                    
                    {audioURL && (
                      <>
                        <div className="text-sm text-white/60 max-w-xs text-center mb-4">
                          Our AI is processing your voice patterns to detect potential dental health indicators
                        </div>
                        
                        <div className="w-full max-w-xs">
                          <p className="text-sm text-white/70 mb-2">Listen to your recording while we analyze:</p>
                          <audio src={audioURL} controls className="w-full" />
                        </div>
                      </>
                    )}
                  </div>
                ) : audioURL && !analysisResults ? (
                  <div className="z-10 flex flex-col items-center">
                    <div className="mb-6">
                      <div className="w-32 h-32 rounded-full bg-green-500/20 flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      </div>
                    </div>
                    <div className="text-xl font-semibold text-white mb-2">
                      Recording Complete
                    </div>
                    <div className="w-full max-w-xs mb-4">
                      <audio src={audioURL} controls className="w-full" />
                    </div>
                    <p className="text-white/70 text-center max-w-xs">
                      You can listen to your recording while we prepare the analysis.
                    </p>
                  </div>
                ) : (
                  <div className="z-10 flex flex-col items-center">
                    <div className="mb-6">
                      <div className="w-32 h-32 rounded-full bg-indigo-500/10 flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                        </svg>
                      </div>
                    </div>
                    <div className="text-xl font-semibold text-white mb-2">
                      Voice Assessment
                    </div>
                    <p className="text-white/70 text-center max-w-xs mb-6">
                      Our AI analyzes subtle voice patterns to detect potential oral health issues.
                    </p>
                  </div>
                )}
              </div>
              
              {/* Audio Playback (for when analysis is complete) */}
              {audioURL && analysisResults && (
                <div className="mb-6 bg-gray-800/40 rounded-lg p-4">
                  <h3 className="text-white font-medium mb-2 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Your Voice Recording
                  </h3>
                  <audio src={audioURL} controls className="w-full" />
                </div>
              )}
              
              {/* Recording Controls */}
              <div className="flex justify-center">
                {isRecording ? (
                  <motion.button
                    onClick={stopRecording}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="px-6 py-3 bg-gradient-to-r from-red-500 to-pink-500 rounded-lg text-white font-medium flex items-center transition shadow-lg"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 10a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 01-1-1v-4z" />
                    </svg>
                    Stop Recording
                  </motion.button>
                ) : analysisResults ? (
                  <motion.button
                    onClick={resetRecording}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-lg text-white font-medium flex items-center transition hover:from-blue-600 hover:to-indigo-600 shadow-lg"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    Record Again
                  </motion.button>
                ) : loading && !audioURL ? null : (
                  <motion.button
                    onClick={startRecording}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    disabled={calibrationStatus === 'calibrating' || loading}
                    className={`px-6 py-3 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-lg text-white font-medium flex items-center transition hover:from-purple-600 hover:to-indigo-600 shadow-lg ${
                      (calibrationStatus === 'calibrating' || loading) ? 'opacity-50 cursor-not-allowed' : ''
                    }`}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                    </svg>
                    {calibrationStatus === 'calibrating' ? 'Calibrating...' : 'Start Recording'}
                  </motion.button>
                )}
              </div>
              
              {/* Voice Metrics */}
              {isRecording && (
                <div className="mt-6 grid grid-cols-2 gap-4">
                  <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-3">
                    <div className="text-sm text-white/60 mb-1">Volume</div>
                    <div className="h-2 bg-gray-700 rounded-full">
                      <div 
                        className="h-full bg-green-500 rounded-full transition-all duration-300"
                        style={{ width: `${Math.min(100, voiceMetrics.volume)}%` }}
                      ></div>
                    </div>
                  </div>
                  <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-3">
                    <div className="text-sm text-white/60 mb-1">Clarity</div>
                    <div className="h-2 bg-gray-700 rounded-full">
                      <div 
                        className="h-full bg-blue-500 rounded-full transition-all duration-300"
                        style={{ width: `${Math.min(100, voiceMetrics.clarity)}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              )}
            </div>
            
            {/* Right Column - Analysis Results */}
            <div>
              {renderAnalysisResults()}
            </div>
          </div>
        )}
      </Card>
    </motion.div>
  );
} 