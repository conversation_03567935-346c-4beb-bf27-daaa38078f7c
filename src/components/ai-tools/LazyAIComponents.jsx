import React, { lazy, Suspense } from 'react';

// Loading component
const LoadingComponent = () => (
  <div className="flex items-center justify-center p-8">
    <div className="animate-pulse flex flex-col items-center">
      <div className="h-12 w-12 rounded-full bg-indigo-500/30 mb-4"></div>
      <div className="h-4 w-48 bg-indigo-500/30 rounded"></div>
      <div className="mt-2 h-3 w-32 bg-indigo-500/20 rounded"></div>
    </div>
  </div>
);

// Lazy load AI components
export const BreathAnalysisTool = lazy(() => import('./BreathAnalysisTool'));
export const MirrorModeCoach = lazy(() => import('./MirrorModeCoach'));
export const WebcamOralScanner = lazy(() => import('./WebcamOralScanner'));
export const MirrorModeTool = lazy(() => import('./MirrorModeTool'));

// Wrapper components with Suspense
export const LazyBreathAnalysisTool = (props) => (
  <Suspense fallback={<LoadingComponent />}>
    <BreathAnalysisTool {...props} />
  </Suspense>
);

export const LazyMirrorModeCoach = (props) => (
  <Suspense fallback={<LoadingComponent />}>
    <MirrorModeCoach {...props} />
  </Suspense>
);

export const LazyWebcamOralScanner = (props) => (
  <Suspense fallback={<LoadingComponent />}>
    <WebcamOralScanner {...props} />
  </Suspense>
);

export const LazyMirrorModeTool = (props) => (
  <Suspense fallback={<LoadingComponent />}>
    <MirrorModeTool {...props} />
  </Suspense>
);
