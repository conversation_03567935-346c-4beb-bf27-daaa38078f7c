import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card } from '../common';
import { aiService } from '../../lib/services/aiService';
import { supabase } from '../../lib/supabase';
import { AI_MODELS, REAL_TIME_ANALYSIS, ERROR_HANDLING, ENHANCED_CONFIDENCE_SCORING } from '../../lib/config/ai-models';
import <PERSON>yd<PERSON> from 'meyda';

// Define feature weights and confidence scoring for simulated analysis
const FEATURE_WEIGHTS = {
  BREATH_ANALYSIS: {
    breath_consistency: 0.4,
    vsc_detection: 0.4,
    audio_quality: 0.2
  }
};

// Simulated ML confidence scoring
const ML_CONFIDENCE_SCORING = {
  calculateProbabilisticConfidence: (metrics, features, thresholds) => ({
    score: 0.7 + Math.random() * 0.3,
    level: Math.random() > 0.3 ? 'high' : 'medium',
    uncertainty: 0.05 + Math.random() * 0.1
  }),
  getFeatureImportance: (metrics, weights) => ({
    breath_consistency: weights.breath_consistency,
    vsc_detection: weights.vsc_detection,
    audio_quality: weights.audio_quality
  })
};

export default function BreathAnalysisTool({ onBack }) {
  const [step, setStep] = useState('intro'); // intro, questionnaire, analyzing, results
  const [answers, setAnswers] = useState({
    morningBreath: null,
    dryMouth: null,
    tongueCoating: null,
    tasteChanges: null,
    dentalHygiene: null
  });
  const [analysisProgress, setAnalysisProgress] = useState(0);
  const [results, setResults] = useState(null);
  const [analysisMode, setAnalysisMode] = useState('questionnaire');
  const [audioData, setAudioData] = useState([]);
  const [isMicrophoneActive, setIsMicrophoneActive] = useState(false);
  const audioContextRef = useRef(null);
  const analyserRef = useRef(null);
  const microphoneRef = useRef(null);
  const [vscLevels, setVscLevels] = useState({});
  const [analysisHistory, setAnalysisHistory] = useState([]);
  const [showTutorial, setShowTutorial] = useState(true);
  const [environmentNoise, setEnvironmentNoise] = useState(0);
  const [breathConsistency, setBreathConsistency] = useState(0);
  const canvasRef = useRef(null);
  const [realTimeFeedback, setRealTimeFeedback] = useState('');
  const [breathDuration, setBreathDuration] = useState(0);
  const [voiceGuide, setVoiceGuide] = useState(null);
  const [isCalibrated, setIsCalibrated] = useState(false);
  const [hasGivenInitialPrompt, setHasGivenInitialPrompt] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const mlModelRef = useRef(null);

  // Handle questionnaire answers
  const handleAnswer = (question, value) => {
    setAnswers(prev => ({
      ...prev,
      [question]: value
    }));
  };

  // Get all questions answered status
  const allQuestionsAnswered = () => {
    return Object.values(answers).every(answer => answer !== null);
  };

  // Enhanced start analysis function
  const startAnalysis = async () => {
    try {
      setStep('analyzing');
      setAnalysisProgress(0);

      // Get previous analyses for context
      const { data: previousAnalyses } = await supabase
        .from('breath_analyses')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(5);

      // Initialize analysis with questionnaire data
      const initialAnalysis = await aiService.initializeBreathAnalysis({
        questionnaire: answers,
        previousAnalyses,
        modelConfig: AI_MODELS.BREATH_ANALYSIS
      });

      // Simulate progress while processing
      const progressInterval = setInterval(() => {
        setAnalysisProgress(prev => {
          if (prev >= 100) {
            clearInterval(progressInterval);
            return 100;
          }
          return prev + 2;
        });
      }, 60);

      // Process results with enhanced confidence scoring
      const confidenceScore = ENHANCED_CONFIDENCE_SCORING.calculateWeightedConfidence(
        initialAnalysis.metrics,
        AI_MODELS.BREATH_ANALYSIS.features,
        AI_MODELS.BREATH_ANALYSIS.confidence_thresholds
      );

      // Store analysis results
      await supabase.from('breath_analyses').insert({
        results: initialAnalysis,
        confidence_score: confidenceScore,
        questionnaire: answers,
        created_at: new Date().toISOString()
      });

      setResults({
        ...initialAnalysis,
        confidence: confidenceScore
      });

      // Update analysis history
      setAnalysisHistory(prev => [...prev, {
        score: initialAnalysis.overallScore,
        timestamp: new Date().toISOString()
      }]);

      setStep('results');

    } catch (error) {
      console.error('Analysis error:', error);
      await handleError('BREATH_ANALYSIS', 'processingError', error);
    }
  };

  // Enhanced stop analysis function
  const stopAnalysis = async () => {
    try {
      if (audioContextRef.current) {
        await audioContextRef.current.close();
      }

      if (microphoneRef.current) {
        microphoneRef.current.disconnect();
      }

      setIsMicrophoneActive(false);

      // Get final analysis with enhanced confidence scoring
      const finalFeatures = await aiService.getFinalBreathAnalysis({
        duration: breathDuration,
        vscLevels,
        breathConsistency,
        modelConfig: AI_MODELS.BREATH_ANALYSIS
      });

      const confidenceScore = ENHANCED_CONFIDENCE_SCORING.calculateWeightedConfidence(
        finalFeatures.metrics,
        AI_MODELS.BREATH_ANALYSIS.features,
        AI_MODELS.BREATH_ANALYSIS.confidence_thresholds
      );

      // Store analysis results
      await supabase.from('breath_analyses').insert({
        results: finalFeatures,
        confidence_score: confidenceScore,
        questionnaire: answers,
        created_at: new Date().toISOString()
      });

      setResults({
        ...finalFeatures.analysis,
        confidence: confidenceScore
      });

      // Update analysis history
      setAnalysisHistory(prev => [...prev, {
        score: finalFeatures.analysis.overallScore,
        timestamp: new Date().toISOString()
      }]);

      setStep('results');

    } catch (error) {
      console.error('Error stopping analysis:', error);
      await handleError('BREATH_ANALYSIS', 'processingError', error);
    }
  };

  // Enhanced reset function
  const resetAnalysis = () => {
    setAnswers({
      morningBreath: null,
      dryMouth: null,
      tongueCoating: null,
      tasteChanges: null,
      dentalHygiene: null
    });
    setResults(null);
    setStep('intro');
    setAnalysisProgress(0);
    setVscLevels({});
    setBreathConsistency(0);
    setBreathDuration(0);
    setRealTimeFeedback('');
    setHasGivenInitialPrompt(false);
    setIsCalibrated(false);

    // Clean up audio context and microphone
    if (audioContextRef.current) {
      audioContextRef.current.close();
    }
    if (microphoneRef.current) {
      microphoneRef.current.disconnect();
    }
    setIsMicrophoneActive(false);
  };

  const startMicrophoneAnalysis = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const AudioContext = window.AudioContext || window.webkitAudioContext;
      audioContextRef.current = new AudioContext();
      analyserRef.current = audioContextRef.current.createAnalyser();

      // Apply enhanced configuration
      const config = REAL_TIME_ANALYSIS.BREATH_ANALYSIS;
      analyserRef.current.fftSize = config.bufferSize;
      analyserRef.current.smoothingTimeConstant = config.sensitivity;
      analyserRef.current.minDecibels = config.noiseFloor;

      microphoneRef.current = audioContextRef.current.createMediaStreamSource(stream);
      microphoneRef.current.connect(analyserRef.current);
      setIsMicrophoneActive(true);

      // Audio processing initialization
      console.log('Audio processing initialized');

      // Initialize Meyda analyzer
      const meydaAnalyzer = Meyda.createMeydaAnalyzer({
        audioContext: audioContextRef.current,
        source: microphoneRef.current,
        bufferSize: config.bufferSize,
        featureExtractors: ['mfcc', 'rms', 'zcr', 'spectralCentroid'],
        callback: (features) => {
          if (canvasRef.current) {
            drawSpectrogram(features, canvasRef.current);
          }
        }
      });
      meydaAnalyzer.start();

      // Start real-time analysis
      startRealTimeAnalysis();

    } catch (error) {
      console.error('Microphone access error:', error);
      await handleError('BREATH_ANALYSIS', 'sensorError', error);
    }
  };

  // Draw spectrogram visualization
  const drawSpectrogram = (features, canvas) => {
    const ctx = canvas.getContext('2d');
    const width = canvas.width;
    const height = canvas.height;

    // Clear canvas
    ctx.clearRect(0, 0, width, height);

    // Draw MFCC coefficients
    if (features && features.mfcc) {
      const barWidth = width / features.mfcc.length;
      const amplitudeScale = height / 30;

      ctx.fillStyle = '#60a5fa';
      features.mfcc.forEach((value, i) => {
        const barHeight = Math.abs(value) * amplitudeScale;
        const y = height / 2 - (value > 0 ? barHeight : 0);
        ctx.fillRect(i * barWidth, y, barWidth - 1, barHeight);
      });
    }

    // Draw spectral centroid indicator
    if (features && features.spectralCentroid) {
      const normalizedCentroid = features.spectralCentroid / 10000; // Normalize to 0-1 range
      const centroidX = normalizedCentroid * width;

      ctx.beginPath();
      ctx.moveTo(centroidX, 0);
      ctx.lineTo(centroidX, height);
      ctx.strokeStyle = '#f87171';
      ctx.lineWidth = 2;
      ctx.stroke();
    }
  };

  const startRealTimeAnalysis = () => {
    const config = REAL_TIME_ANALYSIS.BREATH_ANALYSIS;
    let analysisStartTime = null;
    let dataBuffer = [];

    const analyzeInterval = setInterval(async () => {
      if (!analyserRef.current || !isMicrophoneActive) {
        clearInterval(analyzeInterval);
        return;
      }

      if (!analysisStartTime) {
        analysisStartTime = Date.now();
        if (!hasGivenInitialPrompt) {
          setRealTimeFeedback("Breathe normally into your microphone...");
          setHasGivenInitialPrompt(true);
        }
      }

      // Get audio data
      const dataArray = new Float32Array(analyserRef.current.frequencyBinCount);
      analyserRef.current.getFloatFrequencyData(dataArray);

      // Add to buffer for analysis
      dataBuffer.push(Array.from(dataArray));
      if (dataBuffer.length > 10) {
        dataBuffer.shift(); // Keep last 10 frames only
      }

      try {
        // Perform ML-based analysis
        const mlResults = await analyzeBreathPattern(dataArray);

        if (mlResults) {
          // Update VSC levels with ML-enhanced detection
          setVscLevels(prev => ({
            ...prev,
            vsc: mlResults.metrics.vsc_level,
            consistency: mlResults.metrics.flow_consistency
          }));

          // Update breath consistency
          setBreathConsistency(mlResults.metrics.flow_consistency);

          // Update breath duration
          const currentDuration = (Date.now() - analysisStartTime) / 1000;
          setBreathDuration(currentDuration);

          // Provide real-time feedback based on ML analysis
          if (currentDuration > 3 && currentDuration % 2 < 0.1) {
            const feedback = await getRealTimeFeedback(mlResults.metrics);
            setRealTimeFeedback(feedback);
          }

          // Update progress for analyzing state
          if (step === 'analyzing') {
            setAnalysisProgress(prev => Math.min(prev + 2, 99));
          }

          // Check if we have enough data for complete analysis
          if (currentDuration > 15 && !isCalibrated) {
            setIsCalibrated(true);
            setRealTimeFeedback("Calibration complete. Continue breathing normally for full analysis.");
          }

          // Automatically stop after sufficient data
          if (currentDuration > 30) {
            clearInterval(analyzeInterval);
            await stopAnalysis();
          }
        }
      } catch (error) {
        console.error('Real-time analysis error:', error);
        await handleError('BREATH_ANALYSIS', 'realtimeError', error);
      }
    }, config.updateInterval);

    return () => clearInterval(analyzeInterval);
  };

  const handleError = async (modelType, errorType, error) => {
    const strategy = ERROR_HANDLING.fallbackStrategies[modelType][errorType];
    if (strategy && ERROR_HANDLING.recoveryActions[strategy]) {
      try {
        await ERROR_HANDLING.recoveryActions[strategy]();
      } catch (recoveryError) {
        console.error('Recovery action failed:', recoveryError);
        setError('Analysis failed. Please try again.');
      }
    }
  };

  const getRealTimeFeedback = async (features) => {
    try {
      const feedback = await aiService.getBreathFeedback(features, {
        modelConfig: AI_MODELS.BREATH_ANALYSIS,
        previousAnalyses: analysisHistory.slice(-3)
      });

      return feedback;
    } catch (error) {
      console.error('Feedback generation error:', error);
      return 'Continue breathing normally...';
    }
  };

  const processRecording = async (audioBlob) => {
    try {
      setLoading(true);

      // Convert blob to array buffer for analysis
      const arrayBuffer = await audioBlob.arrayBuffer();
      const audioBuffer = await audioContextRef.current.decodeAudioData(arrayBuffer);

      // Get previous analyses from Supabase for context
      const { data: previousAnalyses } = await supabase
        .from('breath_analyses')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(5);

      // Perform AI analysis
      const analysisResults = await aiService.analyzeBreathData(audioBuffer, {
        modelConfig: AI_MODELS.BREATH_ANALYSIS,
        previousAnalyses,
        questionnaire: answers,
        features: {
          vsc: vscLevels,
          consistency: breathConsistency,
          duration: breathDuration
        }
      });

      // Calculate confidence scores
      const confidenceScore = ENHANCED_CONFIDENCE_SCORING.calculateWeightedConfidence(
        analysisResults.metrics,
        AI_MODELS.BREATH_ANALYSIS.features,
        AI_MODELS.BREATH_ANALYSIS.confidence_thresholds
      );

      // Store analysis results
      await supabase.from('breath_analyses').insert({
        results: analysisResults,
        confidence_score: confidenceScore,
        questionnaire: answers,
        created_at: new Date().toISOString()
      });

      setResults({
        ...analysisResults,
        confidence: confidenceScore
      });

      // Update analysis history
      setAnalysisHistory(prev => [...prev, {
        score: analysisResults.overallScore,
        timestamp: new Date().toISOString()
      }]);

      setStep('results');

    } catch (error) {
      console.error('Processing error:', error);
      await handleError('BREATH_ANALYSIS', 'processingError', error);
    } finally {
      setLoading(false);
    }
  };

  const renderHistoricalTrend = () => (
    <div className="bg-gray-800/50 p-4 rounded-lg mb-6">
      <h4 className="text-cyan-400 mb-3">Breath Health Trend</h4>
      <div className="flex items-end h-32 gap-1">
        {analysisHistory.slice(-7).map((result, i) => (
          <motion.div
            key={i}
            initial={{ height: 0 }}
            animate={{ height: `${result.score}%` }}
            className="bg-gradient-to-t from-cyan-500 to-blue-500 w-8 rounded-t"
            transition={{ duration: 0.5 }}
          />
        ))}
      </div>
      <p className="text-white/70 text-sm mt-2">7-day breath quality history</p>
    </div>
  );

  // Update the voice guidance system
  const startVoiceGuidance = () => {
    if ('speechSynthesis' in window && voiceGuide === null) {
      const messages = [
        "Please breathe steadily into your microphone for 5 seconds",
        "Keep a consistent distance of about 6 inches from the microphone",
        "Exhale slowly and naturally through your mouth",
        "Analysis starting in 3... 2... 1..."
      ];

      let currentMessage = 0;

      const speakNext = () => {
        if (currentMessage < messages.length) {
          const utterance = new SpeechSynthesisUtterance(messages[currentMessage]);
          utterance.rate = 1.0;
          utterance.onend = speakNext;
          speechSynthesis.speak(utterance);
          currentMessage++;
        }
      };

      speakNext();
      setVoiceGuide(true);
    }
  };

  // Voice guidance useEffect
  useEffect(() => {
    if (analysisMode === 'microphone' && isMicrophoneActive && !hasGivenInitialPrompt) {
      startVoiceGuidance();
      setHasGivenInitialPrompt(true);
    }
  }, [isMicrophoneActive, analysisMode, hasGivenInitialPrompt]);

  // Cleanup useEffect
  useEffect(() => {
    return () => {
      if (voiceGuide) {
        speechSynthesis.cancel();
      }
      if (audioContextRef.current) {
        audioContextRef.current.close();
      }
      if (microphoneRef.current) {
        microphoneRef.current.disconnect();
      }
    };
  }, [voiceGuide]);

  // Auto-stop useEffect with enhanced completion handling
  useEffect(() => {
    if (breathDuration >= 5 && isMicrophoneActive) {
      const completeAnalysis = async () => {
        try {
          // Stop recording and analysis
          stopAnalysis();

          // Get final analysis with enhanced confidence scoring
          const finalFeatures = await aiService.getFinalBreathAnalysis({
            duration: breathDuration,
            vscLevels,
            breathConsistency,
            modelConfig: AI_MODELS.BREATH_ANALYSIS
          });

          const confidenceScore = ENHANCED_CONFIDENCE_SCORING.calculateWeightedConfidence(
            finalFeatures.metrics,
            AI_MODELS.BREATH_ANALYSIS.features,
            AI_MODELS.BREATH_ANALYSIS.confidence_thresholds
          );

          // Update results with final analysis
          setResults({
            ...finalFeatures.analysis,
            confidence: confidenceScore
          });

          // Provide completion feedback
          const utterance = new SpeechSynthesisUtterance(
            "Analysis complete. Processing your results with enhanced AI analysis..."
          );
          speechSynthesis.speak(utterance);

        } catch (error) {
          console.error('Error in final analysis:', error);
          await handleError('BREATH_ANALYSIS', 'processingError', error);
        }
      };

      completeAnalysis();
    }
  }, [breathDuration, isMicrophoneActive]);

  // Update the auto-stop condition
  useEffect(() => {
    if (breathDuration >= 5 && isMicrophoneActive) {
      stopAnalysis();
      const utterance = new SpeechSynthesisUtterance("Analysis complete. Processing your results...");
      speechSynthesis.speak(utterance);
    }
  }, [breathDuration, isMicrophoneActive]);

  // Add in useEffect for microphone analysis
  useEffect(() => {
    const analyze = () => {
      if (analyserRef.current && isMicrophoneActive) {
        const bufferLength = analyserRef.current.frequencyBinCount;
        const dataArray = new Uint8Array(bufferLength);
        analyserRef.current.getByteFrequencyData(dataArray);
        analyzeAudioPatterns(dataArray);
        getRealTimeFeedback(dataArray);

        // Update to use 5 seconds instead of 10
        if (breathDuration >= 5) {
          stopAnalysis();
        }
      }
      requestAnimationFrame(analyze);
    };

    if (analysisMode === 'microphone') {
      calibrateMicrophone().then(() => {
        setIsCalibrated(true);
        analyze();
      });
    }
  }, [isMicrophoneActive, breathDuration, analysisMode]);

  // Simulated breath pattern analysis
  const analyzeBreathPattern = async (data) => {
    try {
      // Extract acoustic features with Meyda if available
      let meydaFeatures = {};
      try {
        meydaFeatures = Meyda.extract(['rms', 'zcr', 'spectralCentroid'], data);
      } catch (e) {
        console.log('Using simulated audio features');
        // Simulate features if Meyda fails
        meydaFeatures = {
          rms: Math.random() * 0.5,
          zcr: Math.random() * 500,
          spectralCentroid: 2000 + Math.random() * 3000
        };
      }

      // Simulate ML metrics based on audio features
      const mlMetrics = {
        vsc_level: meydaFeatures.spectralCentroid ? meydaFeatures.spectralCentroid / 10000 : Math.random() * 0.5,
        flow_consistency: meydaFeatures.zcr ? 1 - meydaFeatures.zcr / 1000 : 0.7 + Math.random() * 0.3,
        audio_quality: meydaFeatures.rms || Math.random() * 0.8,
        samples: Array.from({ length: 10 }, () => Math.random())
      };

      // Simulate confidence score
      const confidenceScore = {
        score: 0.7 + Math.random() * 0.3,
        level: Math.random() > 0.3 ? 'high' : 'medium',
        uncertainty: 0.05 + Math.random() * 0.1
      };

      // Simulate feature importance
      const featureImportance = {
        breath_consistency: 0.7 + Math.random() * 0.3,
        vsc_detection: 0.6 + Math.random() * 0.4,
        audio_quality: 0.5 + Math.random() * 0.5
      };

      return {
        metrics: mlMetrics,
        confidence: confidenceScore,
        importance: featureImportance,
        principalComponents: [[Math.random(), Math.random()], [Math.random(), Math.random()]]
      };
    } catch (error) {
      console.error('Error analyzing breath pattern:', error);
      await handleError('BREATH_ANALYSIS', 'processingError', error);
      return null;
    }
  };

  // Enhanced tutorial guide
  const renderTutorial = () => (
    <Card key="tutorial-card" className="mb-6 bg-gradient-to-br from-cyan-900/50 to-blue-900/50">
      <h3 className="text-xl text-cyan-400 mb-4">How to Use the Breath Analysis Tool</h3>
      <ol className="space-y-4 text-white/90">
        <motion.li
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2 }}
        >
          1. Complete the questionnaire about your oral health
        </motion.li>
        <motion.li
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.4 }}
        >
          2. Allow microphone access for breath analysis
        </motion.li>
        <motion.li
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.6 }}
        >
          3. Follow the voice guidance for proper breath sampling
        </motion.li>
        <motion.li
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.8 }}
        >
          4. Maintain a consistent distance from the microphone
        </motion.li>
        <motion.li
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 1.0 }}
        >
          5. Review your comprehensive analysis results
        </motion.li>
      </ol>
      <motion.button
        className="mt-6 text-cyan-400 hover:text-cyan-300 transition-colors"
        onClick={() => setShowTutorial(false)}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1.2 }}
      >
        Got it, let's start
      </motion.button>
    </Card>
  );

  // Enhanced results display
  const renderResults = () => (
    <div className="mt-6 space-y-6">
      <h2 className="text-2xl font-bold text-gray-100">Analysis Results</h2>

      {results && (
        <div className="space-y-4">
          <div className="bg-navy-800 rounded-xl p-6 shadow-md">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-semibold text-gray-100">Overall Assessment</h3>
              <div className={`text-sm font-medium rounded-full px-3 py-1 bg-opacity-20 ${ENHANCED_CONFIDENCE_SCORING.getConfidenceColor(results.confidence.level)} bg-${results.confidence.level === 'high' ? 'green' : results.confidence.level === 'medium' ? 'blue' : 'yellow'}-900`}>
                {Math.round(results.confidence.score * 100)}% confidence
                {results.confidence.uncertainty &&
                  <span className="ml-1">±{Math.round(results.confidence.uncertainty * 100)}%</span>
                }
              </div>
            </div>

            {/* ML-based recommendations */}
            <div className="mb-4">
              <h4 className="text-lg font-medium text-gray-200 mb-2">AI Assessment</h4>
              <p className="text-gray-300">{results.recommendation || "Based on your breath analysis, your oral health appears to be in good condition. Continue with regular dental checkups and maintain your oral hygiene routine."}</p>
            </div>

            {/* Feature importance visualization */}
            {results.importance && (
              <div className="mt-4">
                <h4 className="text-md font-medium text-gray-200 mb-2">Analysis Factors</h4>
                <div className="space-y-2">
                  {Object.entries(results.importance).map(([feature, importance]) => (
                    <div key={feature} className="flex items-center">
                      <span className="text-sm text-gray-400 w-36">{feature.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}</span>
                      <div className="flex-1 h-2 bg-gray-700 rounded-full overflow-hidden">
                        <div
                          className="h-full rounded-full bg-blue-500"
                          style={{ width: `${Math.round(importance * 100)}%` }}
                        />
                      </div>
                      <span className="text-xs text-gray-400 ml-2">{Math.round(importance * 100)}%</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Real-time visualization */}
          <div className="bg-navy-800 rounded-xl p-6 shadow-md">
            <h3 className="text-xl font-semibold text-gray-100 mb-4">Analysis Visualization</h3>
            <canvas
              ref={canvasRef}
              width="400"
              height="200"
              className="w-full h-40 bg-navy-900 rounded-lg mb-4"
            />
            <p className="text-sm text-gray-400">This visualization shows your breath acoustic patterns and spectral properties analyzed using machine learning.</p>
          </div>

          {/* Historical trend */}
          {analysisHistory.length > 1 && renderHistoricalTrend()}

          <div className="flex justify-center mt-6 space-x-4">
            <button
              onClick={resetAnalysis}
              className="px-6 py-2 bg-transparent border border-blue-500 text-blue-500 rounded-lg hover:bg-blue-500 hover:bg-opacity-10 transition-all"
            >
              Start New Analysis
            </button>
          </div>
        </div>
      )}
    </div>
  );

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="w-full max-w-5xl mx-auto"
    >
      <Card className="p-6 md:p-8 w-full">
        <div className="flex flex-col items-center mb-8">
          <div className="w-16 h-16 mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="w-full h-full text-cyan-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>
          <h2 className="text-2xl md:text-3xl font-bold text-white text-center mb-2">
            Breath Health Analysis
          </h2>
          <p className="text-white/70 text-center max-w-2xl">
            Advanced breath analysis using AI to detect potential oral health issues and provide personalized recommendations.
          </p>
        </div>

        <div className="flex justify-end">
          <button
            onClick={onBack}
            className="flex items-center text-white/70 hover:text-white transition"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
            </svg>
            Back to Tools
          </button>
        </div>

        <AnimatePresence mode="sync">
          {/* Intro Screen */}
          {step === 'intro' && (
            <div className="text-center py-8">
              <motion.div
                className="w-32 h-32 mx-auto mb-6 relative"
                animate={{
                  scale: [1, 1.05, 1],
                  opacity: [0.8, 1, 0.8]
                }}
                transition={{
                  repeat: Infinity,
                  duration: 3
                }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="w-full h-full text-cyan-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </motion.div>

              <h3 className="text-2xl font-medium text-white mb-4">Breath Health Analysis</h3>
              <p className="text-white/70 max-w-2xl mx-auto mb-8">
                This tool helps assess your breath health by analyzing responses to key diagnostic questions.
                Our AI will provide personalized insights and recommendations to improve your oral hygiene.
              </p>

              <div className="max-w-2xl mx-auto bg-gray-800/50 rounded-xl p-6 border border-gray-700 mb-8">
                <h4 className="text-lg font-medium text-white mb-3">How It Works</h4>
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="w-12 h-12 rounded-full bg-cyan-500/20 flex items-center justify-center mx-auto mb-2">
                      <span className="text-cyan-400 font-bold">1</span>
                    </div>
                    <h5 className="text-sm font-medium text-white">Complete Questionnaire</h5>
                    <p className="text-xs text-white/60 mt-1">Answer a few simple questions about your oral health</p>
                  </div>

                  <div className="text-center">
                    <div className="w-12 h-12 rounded-full bg-cyan-500/20 flex items-center justify-center mx-auto mb-2">
                      <span className="text-cyan-400 font-bold">2</span>
                    </div>
                    <h5 className="text-sm font-medium text-white">AI Analysis</h5>
                    <p className="text-xs text-white/60 mt-1">Our algorithm analyzes your responses</p>
                  </div>

                  <div className="text-center">
                    <div className="w-12 h-12 rounded-full bg-cyan-500/20 flex items-center justify-center mx-auto mb-2">
                      <span className="text-cyan-400 font-bold">3</span>
                    </div>
                    <h5 className="text-sm font-medium text-white">Get Recommendations</h5>
                    <p className="text-xs text-white/60 mt-1">Receive personalized insights and suggestions</p>
                  </div>
                </div>
              </div>

              <div className="mb-8">
                <h3 className="text-lg font-medium text-white mb-4">Choose Analysis Method</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    className="p-6 bg-gray-800 rounded-lg border border-gray-700"
                    onClick={() => {
                      setAnalysisMode('questionnaire');
                      setStep('questionnaire');
                    }}
                  >
                    <div className="text-cyan-400 mb-2">📝 Questionnaire Analysis</div>
                    <p className="text-white/70 text-sm">Answer questions about your oral health habits</p>
                  </motion.button>

                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    className="p-6 bg-gray-800 rounded-lg border border-gray-700"
                    onClick={() => {
                      setAnalysisMode('microphone');
                      startMicrophoneAnalysis();
                    }}
                  >
                    <div className="text-cyan-400 mb-2">🎤 Instant Breath Analysis</div>
                    <p className="text-white/70 text-sm">Use microphone to detect volatile compounds</p>
                  </motion.button>
                </div>
              </div>

              <motion.button
                onClick={() => setStep('questionnaire')}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-8 py-3 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-lg text-white font-medium shadow-lg hover:shadow-cyan-500/20 transition"
              >
                Start Assessment
              </motion.button>
            </div>
          )}

          {/* Questionnaire Screen */}
          {step === 'questionnaire' && (
            <div className="max-w-3xl mx-auto">
              <div className="text-center mb-8">
                <h3 className="text-xl font-medium text-white mb-2">Breath Health Questionnaire</h3>
                <p className="text-white/70">
                  Please answer the following questions as accurately as possible for the most precise analysis.
                </p>
              </div>

              <div className="space-y-6 mb-8">
                {/* Question 1 */}
                <div className="bg-gray-800/50 rounded-lg p-5 border border-gray-700">
                  <h4 className="text-white font-medium mb-3">How often do others mention or you notice morning breath issues?</h4>
                  <div className="grid grid-cols-1 sm:grid-cols-5 gap-2">
                    {['never', 'rarely', 'sometimes', 'often', 'always'].map((option) => (
                      <button
                        key={option}
                        onClick={() => handleAnswer('morningBreath', option)}
                        className={`py-2 px-4 rounded-lg text-sm font-medium transition ${
                          answers.morningBreath === option
                            ? 'bg-cyan-500 text-white'
                            : 'bg-gray-700 text-white/70 hover:bg-gray-600'
                        }`}
                      >
                        {option.charAt(0).toUpperCase() + option.slice(1)}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Question 2 */}
                <div className="bg-gray-800/50 rounded-lg p-5 border border-gray-700">
                  <h4 className="text-white font-medium mb-3">How often do you experience dry mouth?</h4>
                  <div className="grid grid-cols-1 sm:grid-cols-5 gap-2">
                    {['never', 'rarely', 'sometimes', 'often', 'always'].map((option) => (
                      <button
                        key={option}
                        onClick={() => handleAnswer('dryMouth', option)}
                        className={`py-2 px-4 rounded-lg text-sm font-medium transition ${
                          answers.dryMouth === option
                            ? 'bg-cyan-500 text-white'
                            : 'bg-gray-700 text-white/70 hover:bg-gray-600'
                        }`}
                      >
                        {option.charAt(0).toUpperCase() + option.slice(1)}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Question 3 */}
                <div className="bg-gray-800/50 rounded-lg p-5 border border-gray-700">
                  <h4 className="text-white font-medium mb-3">How would you describe the coating on your tongue?</h4>
                  <div className="grid grid-cols-1 sm:grid-cols-4 gap-2">
                    {['none', 'light', 'moderate', 'heavy'].map((option) => (
                      <button
                        key={option}
                        onClick={() => handleAnswer('tongueCoating', option)}
                        className={`py-2 px-4 rounded-lg text-sm font-medium transition ${
                          answers.tongueCoating === option
                            ? 'bg-cyan-500 text-white'
                            : 'bg-gray-700 text-white/70 hover:bg-gray-600'
                        }`}
                      >
                        {option.charAt(0).toUpperCase() + option.slice(1)}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Question 4 */}
                <div className="bg-gray-800/50 rounded-lg p-5 border border-gray-700">
                  <h4 className="text-white font-medium mb-3">How often do you notice changes in your sense of taste?</h4>
                  <div className="grid grid-cols-1 sm:grid-cols-5 gap-2">
                    {['never', 'rarely', 'sometimes', 'often', 'always'].map((option) => (
                      <button
                        key={option}
                        onClick={() => handleAnswer('tasteChanges', option)}
                        className={`py-2 px-4 rounded-lg text-sm font-medium transition ${
                          answers.tasteChanges === option
                            ? 'bg-cyan-500 text-white'
                            : 'bg-gray-700 text-white/70 hover:bg-gray-600'
                        }`}
                      >
                        {option.charAt(0).toUpperCase() + option.slice(1)}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Question 5 */}
                <div className="bg-gray-800/50 rounded-lg p-5 border border-gray-700">
                  <h4 className="text-white font-medium mb-3">How would you rate your dental hygiene practices?</h4>
                  <div className="grid grid-cols-1 sm:grid-cols-5 gap-2">
                    {['poor', 'below average', 'fair', 'good', 'excellent'].map((option) => (
                      <button
                        key={option}
                        onClick={() => handleAnswer('dentalHygiene', option)}
                        className={`py-2 px-4 rounded-lg text-sm font-medium transition ${
                          answers.dentalHygiene === option
                            ? 'bg-cyan-500 text-white'
                            : 'bg-gray-700 text-white/70 hover:bg-gray-600'
                        }`}
                      >
                        {option.charAt(0).toUpperCase() + option.slice(1)}
                      </button>
                    ))}
                  </div>
                </div>
              </div>

              <div className="flex justify-between">
                <button
                  onClick={() => setStep('intro')}
                  className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg text-white/80 font-medium transition"
                >
                  Back
                </button>

                <button
                  onClick={startAnalysis}
                  disabled={!allQuestionsAnswered()}
                  className={`px-6 py-2 rounded-lg text-white font-medium transition ${
                    allQuestionsAnswered()
                      ? 'bg-gradient-to-r from-cyan-500 to-blue-500 hover:shadow-lg hover:shadow-cyan-500/20'
                      : 'bg-gray-700 opacity-50 cursor-not-allowed'
                  }`}
                >
                  Analyze Results
                </button>
              </div>
            </div>
          )}

          {/* Analysis Loading Screen */}
          {step === 'analyzing' && analysisMode === 'questionnaire' && (
            <div className="text-center py-16">
              <motion.div
                animate={{
                  rotate: 360,
                  transition: { duration: 2, repeat: Infinity, ease: "linear" }
                }}
                className="w-20 h-20 mx-auto mb-6"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="w-full h-full text-cyan-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                </svg>
              </motion.div>

              <h3 className="text-xl font-medium text-white mb-4">Analyzing Your Breath Health</h3>
              <p className="text-white/70 mb-8 max-w-md mx-auto">
                Our AI is processing your responses to generate personalized insights and recommendations.
              </p>

              <div className="max-w-md mx-auto">
                <div className="w-full h-2 bg-gray-700 rounded-full overflow-hidden mb-2">
                  <motion.div
                    className="h-full bg-gradient-to-r from-cyan-500 to-blue-500"
                    initial={{ width: '0%' }}
                    animate={{ width: `${analysisProgress}%` }}
                  />
                </div>
                <div className="text-cyan-400 text-sm">{analysisProgress}% complete</div>
              </div>
            </div>
          )}

          {/* Results Screen */}
          {step === 'results' && results && (
            renderResults()
          )}

          {/* Microphone Analysis Screen */}
          {step === 'analyzing' && analysisMode === 'microphone' && (
            <div className="space-y-6">
              <div className="bg-gradient-to-br from-cyan-500/10 to-blue-500/10 p-6 rounded-xl border border-cyan-500/20">
                {/* Add progress indicator */}
                <div className="mb-4 relative h-2 bg-cyan-500/20 rounded-full">
                  <motion.div
                    className="absolute left-0 top-0 h-full bg-cyan-500 rounded-full"
                    initial={{ width: '0%' }}
                    animate={{ width: `${(breathDuration/5)*100}%` }}
                    transition={{ duration: 0.5 }}
                  />
                </div>

                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className={`h-3 w-3 rounded-full ${isCalibrated ? 'bg-green-400' : 'bg-amber-400'}`} />
                    <h3 className="text-lg font-semibold text-cyan-300">
                      {isCalibrated ? 'System Calibrated' : 'Calibrating...'}
                    </h3>
                  </div>
                  <div className="text-cyan-400 text-sm">
                    {breathDuration}s Breath Sample
                  </div>
                </div>

                <div className="grid grid-cols-3 gap-4 mb-6">
                  <div className="bg-black/20 p-4 rounded-lg border border-cyan-500/20">
                    <div className="text-sm text-cyan-400 mb-1">Target Frequency</div>
                    <div className="text-2xl font-mono text-white">500-2500Hz</div>
                  </div>
                  <div className="bg-black/20 p-4 rounded-lg border border-cyan-500/20">
                    <div className="text-sm text-cyan-400 mb-1">Sample Quality</div>
                    <div className="text-2xl font-mono text-white">
                      {Math.min(100, breathDuration * 20)}%
                    </div>
                  </div>
                  <div className="bg-black/20 p-4 rounded-lg border border-cyan-500/20">
                    <div className="text-sm text-cyan-400 mb-1">VSC Detection</div>
                    <div className="text-2xl font-mono text-white">
                      {Object.values(vscLevels).some(l => l > 30) ? 'Detected' : 'Clear'}
                    </div>
                  </div>
                </div>

                {/* Updated feedback section */}
                <div className="bg-black/30 p-4 rounded-lg">
                  <div className="flex items-center space-x-2 text-cyan-300 mb-2">
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122" />
                    </svg>
                    <span className="font-medium">Breathing Guidance</span>
                  </div>
                  <motion.div
                    key={realTimeFeedback}
                    initial={{ opacity: 0, y: 5 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-white/80 text-sm space-y-2"
                  >
                    <p>{realTimeFeedback}</p>
                    <div className="flex items-center text-cyan-400/80">
                      <span className="text-xs mr-2">Breath Duration:</span>
                      <span className="font-mono">{Math.max(0, 5 - breathDuration)}s remaining</span>
                    </div>
                  </motion.div>
                </div>
              </div>

              <div className="relative h-64 bg-gradient-to-br from-gray-900 to-gray-800 rounded-2xl overflow-hidden border border-cyan-500/20">
                <canvas
                  ref={canvasRef}
                  className="absolute inset-0 w-full h-full"
                />
                <div className="absolute bottom-4 left-4 text-cyan-300 text-xs font-mono">
                  {new Date().toLocaleTimeString()} - Live Spectrum
                </div>
                <div className="absolute top-4 right-4">
                  <button
                    onClick={startVoiceGuidance}
                    className="p-2 bg-cyan-500/20 rounded-lg hover:bg-cyan-500/30 transition"
                  >
                    <svg className="w-5 h-5 text-cyan-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 8.464a5 5 0 010 7.072M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          )}

          {showTutorial && (
            renderTutorial()
          )}
        </AnimatePresence>
      </Card>
    </motion.div>
  );
}