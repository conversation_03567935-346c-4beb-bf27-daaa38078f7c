import React, { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card } from '../common';
import Button from '../common/Button';

// Thermal analysis mock results
const MOCK_ANALYSIS_RESULTS = {
  regions: [
    {
      name: 'Upper Molars (Right)',
      temperature: 36.2,
      status: 'Normal',
      color: 'rgb(34, 197, 94)', // Green
      notes: 'Temperature within normal range'
    },
    {
      name: 'Upper Gumline (Front)',
      temperature: 37.4,
      status: 'Elevated',
      color: 'rgb(249, 115, 22)', // Orange
      notes: 'Slightly higher temperature detected, may indicate mild inflammation'
    },
    {
      name: 'Lower Molars (Left)',
      temperature: 38.1,
      status: 'High',
      color: 'rgb(239, 68, 68)', // Red
      notes: 'Temperature significantly above normal, potential infection or inflammation'
    }
  ],
  overall: {
    assessment: 'Potential issues detected',
    recommendations: [
      'Schedule a dental check-up to examine the left lower molar region',
      'Continue regular brushing and flossing',
      'Monitor for any pain or swelling in the affected areas',
      'Consider over-the-counter anti-inflammatory medication if experiencing discomfort'
    ]
  }
};

export default function ThermalImagingTool({ onBack }) {
  const [scanning, setScanning] = useState(false);
  const [results, setResults] = useState(null);
  const [uploadedImage, setUploadedImage] = useState(null);
  const [previewImage, setPreviewImage] = useState(null);
  const [processing, setProcessing] = useState(false);
  const [processProgress, setProcessProgress] = useState(0);
  const canvasRef = useRef(null);
  const fileInputRef = useRef(null);
  const progressTimerRef = useRef(null);

  // Handle file upload
  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // Only allow images
    if (!file.type.startsWith('image/')) {
      alert('Please upload an image file');
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      setUploadedImage(e.target.result);
      setPreviewImage(e.target.result);
    };
    reader.readAsDataURL(file);
  };

  // Trigger file input click
  const triggerFileUpload = () => {
    fileInputRef.current.click();
  };

  // Start scanning process
  const startScan = () => {
    setScanning(true);
    setProcessProgress(0);
    setResults(null);

    // Simulate progress
    progressTimerRef.current = setInterval(() => {
      setProcessProgress(prev => {
        if (prev >= 100) {
          clearInterval(progressTimerRef.current);
          generateResults();
          return 100;
        }
        return prev + 2;
      });
    }, 100);
  };

  // Generate thermal image and analysis results
  const generateResults = () => {
    setProcessing(true);
    
    setTimeout(() => {
      if (!canvasRef.current || !uploadedImage) return;
      
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');
      
      // Load the original image
      const img = new Image();
      img.onload = () => {
        // Draw the original image
        canvas.width = img.width;
        canvas.height = img.height;
        ctx.drawImage(img, 0, 0);
        
        // Get image data for manipulation
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;
        
        // Apply thermal effect
        for (let i = 0; i < data.length; i += 4) {
          // Random thermal patterns with bias towards certain areas
          const row = Math.floor((i / 4) / canvas.width);
          const col = (i / 4) % canvas.width;
          
          // Create center-biased heat distribution
          const centerX = canvas.width / 2;
          const centerY = canvas.height / 2;
          const distX = Math.abs(col - centerX);
          const distY = Math.abs(row - centerY);
          const distFromCenter = Math.sqrt(distX * distX + distY * distY);
          const maxDist = Math.sqrt(centerX * centerX + centerY * centerY);
          
          // Generate a normalized heat value (0-1) with some random noise
          const normalizedDistance = distFromCenter / maxDist;
          const noise = Math.random() * 0.3;
          let heatValue = 1 - normalizedDistance + noise;
          
          // Add some random hot spots for inflammation simulation
          if (Math.random() > 0.995) {
            // Create a hot spot around this pixel
            const spotIntensity = Math.random() * 0.5 + 0.5; // 0.5-1.0
            const spotRadius = Math.random() * 20 + 10; // 10-30px
            
            for (let y = Math.max(0, row - spotRadius); y < Math.min(canvas.height, row + spotRadius); y++) {
              for (let x = Math.max(0, col - spotRadius); x < Math.min(canvas.width, col + spotRadius); x++) {
                const pixelIndex = (y * canvas.width + x) * 4;
                const dx = x - col;
                const dy = y - row;
                const dist = Math.sqrt(dx * dx + dy * dy);
                
                if (dist < spotRadius) {
                  const factor = (1 - dist / spotRadius) * spotIntensity;
                  // Apply red/yellow thermal effect
                  data[pixelIndex] = Math.min(255, data[pixelIndex] + factor * 200); // Red
                  data[pixelIndex + 1] = Math.min(255, data[pixelIndex + 1] + factor * 100); // Green
                  data[pixelIndex + 2] = Math.min(255, data[pixelIndex + 2] - factor * 100); // Blue
                }
              }
            }
          }
          
          // Apply color transform based on heat value
          if (heatValue > 0.8) {
            // Hot - bright red/yellow
            data[i] = 255; // Red
            data[i + 1] = Math.floor(heatValue * 200); // Green
            data[i + 2] = 0; // Blue
          } else if (heatValue > 0.6) {
            // Warm - orange/yellow
            data[i] = 255; // Red
            data[i + 1] = Math.floor(heatValue * 255); // Green
            data[i + 2] = 0; // Blue
          } else if (heatValue > 0.4) {
            // Medium - green/yellow
            data[i] = Math.floor(heatValue * 255); // Red
            data[i + 1] = 255; // Green
            data[i + 2] = 0; // Blue
          } else if (heatValue > 0.2) {
            // Cool - light blue/green
            data[i] = 0; // Red
            data[i + 1] = Math.floor(heatValue * 255); // Green
            data[i + 2] = 255; // Blue
          } else {
            // Cold - deep blue
            data[i] = 0; // Red
            data[i + 1] = 0; // Green
            data[i + 2] = Math.floor((heatValue + 0.1) * 200); // Blue
          }
          
          // Keep alpha
          data[i + 3] = 255;
        }
        
        // Put thermal image back to canvas
        ctx.putImageData(imageData, 0, 0);
        
        // Generate analysis results
        const mockResults = {
          timestamp: new Date().toISOString(),
          hotSpots: [
            {
              location: "Lower right molar area",
              intensity: Math.random() > 0.5 ? "Moderate" : "Mild",
              potentialIssue: "Possible inflammation or infection",
              recommendation: "Consult with dentist for evaluation of lower right molars"
            },
            {
              location: "Upper gum line",
              intensity: Math.random() > 0.7 ? "Mild" : "Low",
              potentialIssue: "Slight temperature elevation",
              recommendation: "Monitor for any pain or sensitivity in this area"
            }
          ],
          generalFindings: {
            overallTemperature: Math.random() > 0.6 ? "Normal" : "Slightly Elevated",
            temperatureVariation: Math.random() > 0.5 ? "Low" : "Moderate",
            symmetry: Math.random() > 0.7 ? "Good" : "Fair"
          },
          recommendations: [
            "Schedule a dental check-up to evaluate areas of elevated temperature",
            "Maintain regular brushing and flossing to prevent inflammation",
            "Consider using an anti-inflammatory mouthwash if recommended by your dentist",
            "Apply cold compress if experiencing discomfort in identified hot spots"
          ]
        };
        
        // Update thermal image preview
        setPreviewImage(canvas.toDataURL('image/png'));
        setResults(mockResults);
        setProcessing(false);
        setScanning(false);
      };
      
      img.src = uploadedImage;
    }, 1000);
  };

  // Reset and start over
  const handleReset = () => {
    setUploadedImage(null);
    setPreviewImage(null);
    setResults(null);
    setScanning(false);
    setProcessProgress(0);
  };

  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (progressTimerRef.current) {
        clearInterval(progressTimerRef.current);
      }
    };
  }, []);

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="w-full max-w-5xl mx-auto"
    >
      <Card className="p-6 md:p-8 w-full">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl md:text-3xl font-bold text-white">
            Thermal Imaging Analysis
          </h2>
          <button 
            onClick={onBack} 
            className="flex items-center text-white/70 hover:text-white transition"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
            </svg>
            Back to Tools
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Left side - Image upload and preview */}
          <div>
            <div className="bg-gray-800/50 border border-gray-700 rounded-xl overflow-hidden mb-6">
              {!uploadedImage ? (
                <div 
                  className="aspect-video flex flex-col items-center justify-center p-6 cursor-pointer hover:bg-gray-800/80 transition"
                  onClick={triggerFileUpload}
                >
                  <motion.div
                    animate={{ 
                      scale: [1, 1.05, 1],
                      opacity: [0.8, 1, 0.8]
                    }}
                    transition={{ 
                      repeat: Infinity,
                      duration: 3
                    }}
                    className="mb-4"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </motion.div>
                  <h3 className="text-xl font-medium text-white mb-2">Upload an Image</h3>
                  <p className="text-center text-white/60 mb-4">
                    Upload a clear photo of your teeth, gums, or oral cavity for thermal analysis
                  </p>
                  <button 
                    className="px-4 py-2 bg-indigo-500 hover:bg-indigo-600 rounded-lg text-white text-sm transition flex items-center"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                    </svg>
                    Select Image
                  </button>
                  <input 
                    type="file" 
                    ref={fileInputRef} 
                    onChange={handleFileUpload} 
                    accept="image/*" 
                    className="hidden" 
                  />
                </div>
              ) : (
                <div className="relative">
                  <img 
                    src={previewImage} 
                    alt="Preview" 
                    className="w-full aspect-video object-cover"
                  />
                  
                  {scanning && (
                    <div className="absolute inset-0 bg-black/60 flex flex-col items-center justify-center">
                      <div className="space-y-4 text-center">
                        <div className="text-white font-medium">Analyzing thermal patterns...</div>
                        <div className="w-64 h-2 bg-gray-700 rounded-full overflow-hidden">
                          <motion.div 
                            className="h-full bg-gradient-to-r from-blue-500 via-purple-500 to-red-500"
                            initial={{ width: '0%' }}
                            animate={{ width: `${processProgress}%` }}
                          />
                        </div>
                        <div className="text-sm text-white/60">{processProgress}% complete</div>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
            
            {uploadedImage && (
              <div className="space-y-4">
                <canvas ref={canvasRef} className="hidden" />
                
                <div className="flex space-x-3">
                  {!scanning ? (
                    <>
                      <button
                        onClick={results ? handleReset : triggerFileUpload}
                        className="flex-1 px-4 py-3 bg-gray-700 hover:bg-gray-600 rounded-lg text-white font-medium transition flex items-center justify-center"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          {results ? (
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                          ) : (
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                          )}
                        </svg>
                        {results ? "Start Over" : "Change Image"}
                      </button>
                      
                      {!results && (
                        <button
                          onClick={startScan}
                          className="flex-1 px-4 py-3 bg-gradient-to-r from-red-500 to-yellow-500 hover:from-red-600 hover:to-yellow-600 rounded-lg text-white font-medium transition flex items-center justify-center"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                          </svg>
                          Start Thermal Analysis
                        </button>
                      )}
                    </>
                  ) : (
                    <button
                      disabled
                      className="w-full px-4 py-3 bg-gray-700 opacity-70 cursor-not-allowed rounded-lg text-white font-medium flex items-center justify-center"
                    >
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Processing...
                    </button>
                  )}
                </div>
                
                {!results && !scanning && (
                  <div className="bg-indigo-900/30 rounded-lg p-3 text-white/80 text-sm">
                    <div className="flex items-start">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-indigo-400 mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span>For best results, ensure the image is well-lit and clearly shows the area of concern.</span>
                    </div>
                  </div>
                )}
              </div>
            )}
            
            {!uploadedImage && (
              <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-5 border border-gray-700">
                <h3 className="text-lg font-medium text-white mb-3">How Thermal Imaging Works</h3>
                <p className="text-white/70 mb-4 text-sm">
                  Thermal imaging detects temperature variations in your oral cavity, which can indicate:
                </p>
                <ul className="space-y-2 text-sm text-white/80">
                  <li className="flex items-start">
                    <div className="bg-red-500/20 rounded-full p-1 mr-3 mt-0.5">
                      <div className="w-2 h-2 rounded-full bg-red-500"></div>
                    </div>
                    <span>Inflammation or infection in teeth and gums</span>
                  </li>
                  <li className="flex items-start">
                    <div className="bg-yellow-500/20 rounded-full p-1 mr-3 mt-0.5">
                      <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
                    </div>
                    <span>Irregular blood flow patterns to specific areas</span>
                  </li>
                  <li className="flex items-start">
                    <div className="bg-blue-500/20 rounded-full p-1 mr-3 mt-0.5">
                      <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                    </div>
                    <span>Potential nerve issues or abnormal tooth metabolism</span>
                  </li>
                </ul>
              </div>
            )}
          </div>
          
          {/* Right side - Results or information */}
          <div>
            {results ? (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="space-y-6"
              >
                <div className="bg-gray-800/70 rounded-lg p-5 border border-gray-700">
                  <h3 className="text-lg font-medium text-white mb-3 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    Thermal Hotspots Detected
                  </h3>
                  
                  <div className="space-y-4">
                    {results.hotSpots.map((hotspot, index) => (
                      <motion.div 
                        key={index}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className="bg-gray-800/50 rounded-lg p-4 border border-gray-700"
                      >
                        <div className="flex justify-between items-start">
                          <div>
                            <h4 className="font-medium text-white">{hotspot.location}</h4>
                            <p className="text-sm text-white/60 mt-1">{hotspot.potentialIssue}</p>
                          </div>
                          <span className={`text-sm rounded-full px-2 py-0.5 ${
                            hotspot.intensity === 'Moderate' ? 'bg-red-500/20 text-red-400' : 
                            hotspot.intensity === 'Mild' ? 'bg-yellow-500/20 text-yellow-400' : 
                            'bg-green-500/20 text-green-400'
                          }`}>
                            {hotspot.intensity}
                          </span>
                        </div>
                        <div className="mt-3 text-sm text-white/70 flex items-start">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-blue-400 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          {hotspot.recommendation}
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </div>
                
                <div className="bg-gray-800/70 rounded-lg p-5 border border-gray-700">
                  <h3 className="text-lg font-medium text-white mb-3">General Findings</h3>
                  
                  <div className="grid grid-cols-3 gap-4 mb-4">
                    <div className="bg-gray-800/50 rounded-lg p-3 text-center">
                      <div className="text-lg font-medium text-white mb-1">Temperature</div>
                      <div className={`text-sm ${
                        results.generalFindings.overallTemperature === 'Normal' ? 'text-green-400' : 'text-yellow-400'
                      }`}>
                        {results.generalFindings.overallTemperature}
                      </div>
                    </div>
                    
                    <div className="bg-gray-800/50 rounded-lg p-3 text-center">
                      <div className="text-lg font-medium text-white mb-1">Variation</div>
                      <div className={`text-sm ${
                        results.generalFindings.temperatureVariation === 'Low' ? 'text-green-400' : 'text-yellow-400'
                      }`}>
                        {results.generalFindings.temperatureVariation}
                      </div>
                    </div>
                    
                    <div className="bg-gray-800/50 rounded-lg p-3 text-center">
                      <div className="text-lg font-medium text-white mb-1">Symmetry</div>
                      <div className={`text-sm ${
                        results.generalFindings.symmetry === 'Good' ? 'text-green-400' : 'text-yellow-400'
                      }`}>
                        {results.generalFindings.symmetry}
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-white mb-2">Recommendations</h4>
                    <ul className="space-y-2 text-sm text-white/80">
                      {results.recommendations.map((rec, i) => (
                        <motion.li 
                          key={i}
                          initial={{ opacity: 0, x: -5 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.3 + (i * 0.1) }}
                          className="flex items-start"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-indigo-400 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                          </svg>
                          {rec}
                        </motion.li>
                      ))}
                    </ul>
                  </div>
                </div>
                
                <div className="bg-gray-800/70 rounded-lg p-5 border border-gray-700">
                  <div className="flex items-center text-white/60 text-sm">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span>This analysis is for informational purposes only and should not replace professional dental advice.</span>
                  </div>
                </div>
              </motion.div>
            ) : (
              <div className="h-full flex flex-col justify-center">
                <div className="text-center mb-8">
                  <motion.div
                    animate={{ 
                      rotate: [0, 5, -5, 0],
                    }}
                    transition={{ 
                      repeat: Infinity,
                      duration: 8,
                      ease: "easeInOut"
                    }}
                    className="w-40 h-40 mx-auto mb-6"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" className="w-full h-full">
                      <defs>
                        <linearGradient id="thermalGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                          <stop offset="0%" stopColor="#4338ca" />
                          <stop offset="50%" stopColor="#ec4899" />
                          <stop offset="100%" stopColor="#f97316" />
                        </linearGradient>
                      </defs>
                      <path 
                        fill="none" 
                        stroke="url(#thermalGradient)" 
                        strokeWidth="1.5" 
                        strokeLinecap="round" 
                        strokeLinejoin="round"
                        d="M19.5 14c-1.23 0-2.42-.2-3.53-.56-.35-.12-.74-.03-1.01.24l-1.57 1.97c-2.83-1.35-5.48-3.9-6.89-6.83l1.95-1.66c.27-.28.35-.67.24-1.02-.37-1.11-.56-2.3-.56-3.53 0-.54-.45-.99-.99-.99H4.19C3.65 3 3 3.24 3 3.99 3 13.28 10.73 21 20.01 21c.71 0 .99-.63.99-1.18v-3.45c0-.54-.45-.99-.99-.99c-.84 0-1.65-.15-2.41-.44"
                      />
                      <path 
                        fill="none" 
                        stroke="url(#thermalGradient)" 
                        strokeWidth="1.5" 
                        strokeLinecap="round" 
                        strokeLinejoin="round"
                        d="M15 11v-4a3 3 0 116 0v4a3 3 0 11-6 0z"
                      />
                    </svg>
                  </motion.div>
                  
                  <h3 className="text-2xl font-medium text-white mb-2">Thermal Imaging Analysis</h3>
                  <p className="text-white/70 max-w-md mx-auto mb-8">
                    Our AI analyzes thermal patterns in your oral cavity to detect potential issues that might not be visible
                    to the naked eye, such as inflammation, infections, or abnormal blood flow.
                  </p>
                  
                  <div className="bg-gray-800/50 rounded-lg p-5 border border-gray-700 max-w-md mx-auto">
                    <div className="text-left space-y-4">
                      <div className="flex items-start">
                        <div className="bg-indigo-500/20 rounded-full p-2 mr-3">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                          </svg>
                        </div>
                        <div>
                          <h4 className="font-medium text-white text-sm">Early Detection</h4>
                          <p className="text-white/60 text-xs mt-1">Identifies issues before they become visible or symptomatic</p>
                        </div>
                      </div>
                      
                      <div className="flex items-start">
                        <div className="bg-purple-500/20 rounded-full p-2 mr-3">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                          </svg>
                        </div>
                        <div>
                          <h4 className="font-medium text-white text-sm">Non-Invasive</h4>
                          <p className="text-white/60 text-xs mt-1">Painless analysis using only temperature variations</p>
                        </div>
                      </div>
                      
                      <div className="flex items-start">
                        <div className="bg-pink-500/20 rounded-full p-2 mr-3">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-pink-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                          </svg>
                        </div>
                        <div>
                          <h4 className="font-medium text-white text-sm">Personalized Insights</h4>
                          <p className="text-white/60 text-xs mt-1">Get tailored recommendations based on your thermal profile</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </Card>
    </motion.div>
  );
} 