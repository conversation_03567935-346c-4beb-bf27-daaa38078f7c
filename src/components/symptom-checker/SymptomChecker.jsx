import React, { useState } from 'react';
import { useSymptom<PERSON><PERSON><PERSON> } from '../../hooks/useSymptomChecker';
import SymptomForm from './SymptomForm';
import AssessmentResult from './AssessmentResult';
import ErrorMessage from '../ErrorMessage';
import LoadingSpinner from '../common/LoadingSpinner';
import { handleApiError } from '../../lib/utils/errorHandler';
import { motion, AnimatePresence } from 'framer-motion';

export default function SymptomChecker() {
  const { 
    checkSymptoms, 
    loading, 
    error, 
    result, 
    analysisHistory,
    clearError,
    setError 
  } = useSymptomChecker();

  const handleSubmit = async (symptoms, imageData) => {
    try {
      if (clearError) clearError();
      console.log('Starting symptom check with:', { 
        hasSymptoms: !!symptoms, 
        symptomsLength: symptoms?.length,
        hasImageData: !!imageData,
        imageDataType: imageData ? typeof imageData : 'none',
        imageDataStructure: imageData ? Object.keys(imageData) : []
      });

      // Ensure we're passing the correct image data format
      const formattedImageData = typeof imageData === 'string' ? imageData : 
                                (imageData && imageData.data) ? imageData.data : imageData;

      console.log('Formatted image data:', {
        type: typeof formattedImageData,
        isString: typeof formattedImageData === 'string',
        hasDataProperty: formattedImageData && typeof formattedImageData === 'object' && 'data' in formattedImageData
      });

      const response = await checkSymptoms(symptoms, formattedImageData);
      console.log('Symptom check response:', { 
        hasResponse: !!response,
        responseLength: response?.length
      });
      
      if (!response) {
        console.error('No response received from checkSymptoms');
        throw new Error('Failed to get assessment results');
      }
    } catch (err) {
      console.error('Symptom checker error details:', {
        name: err.name,
        message: err.message,
        stack: err.stack
      });
      if (setError) {
        setError(handleApiError(err));
      } else {
        console.error('setError function is not available');
      }
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 15
      }
    }
  };

  return (
    <motion.div 
      className="max-w-2xl mx-auto p-6"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <motion.div variants={itemVariants}>
        <h2 className="text-2xl font-semibold text-white mb-6 flex items-center">
          <span className="mr-2">🦷</span>
          Dental Symptom Checker
        </h2>
      </motion.div>

      <motion.div 
        variants={itemVariants}
        className="bg-gradient-to-r from-blue-500/10 to-indigo-500/10 border border-blue-500/20 rounded-lg p-5 mb-6 shadow-md"
      >
        <p className="text-white/90">
          Describe your dental symptoms or upload an image for AI-powered analysis. 
          Our system will provide a professional assessment and personalized recommendations.
        </p>
      </motion.div>

      <AnimatePresence>
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
          >
            <ErrorMessage 
              message={error}
              onDismiss={clearError}
              className="mb-6"
            />
          </motion.div>
        )}
      </AnimatePresence>

      <motion.div variants={itemVariants}>
        <SymptomForm
          onSubmit={handleSubmit}
          loading={loading}
        />
      </motion.div>

      <AnimatePresence>
        {loading && (
          <motion.div 
            className="mt-6 flex justify-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <LoadingSpinner />
          </motion.div>
        )}
      </AnimatePresence>
      
      <AnimatePresence>
        {result && !loading && (
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -30 }}
            transition={{ type: "spring", stiffness: 100, damping: 15 }}
            className="mt-6"
          >
            <AssessmentResult result={result} />
          </motion.div>
        )}
      </AnimatePresence>

      {analysisHistory.length > 0 && !loading && (
        <motion.div 
          variants={itemVariants}
          className="mt-10"
        >
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
            <span className="mr-2">📋</span>
            Previous Analyses
          </h3>
          <div className="space-y-4">
            {analysisHistory.map((item, index) => (
              <motion.div 
                key={index}
                className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 rounded-lg p-5 border border-white/10 shadow-md hover:shadow-lg transition-all duration-300"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ scale: 1.01 }}
              >
                <div className="text-sm text-blue-300 mb-2 flex items-center">
                  <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  {new Date(item.timestamp).toLocaleString()}
                </div>
                {item.symptoms && (
                  <div className="text-white/90 mb-3 bg-white/5 p-3 rounded-md">
                    <strong className="text-indigo-300">Symptoms:</strong> {item.symptoms}
                  </div>
                )}
                {item.hasImage && (
                  <div className="mt-3">
                    <img 
                      src={item.imageData.data} 
                      alt="Analyzed dental image" 
                      className="max-w-xs rounded-lg shadow-lg mb-2 border border-white/10" 
                    />
                  </div>
                )}
                <div className="mt-4 p-4 bg-white/5 rounded-lg border border-white/5">
                  <div className="prose prose-invert prose-sm max-w-none">
                    {item.response.substring(0, 150)}...
                    <button className="mt-2 text-blue-400 hover:text-blue-300 text-sm">
                      View full analysis
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      )}

      <motion.div 
        variants={itemVariants}
        className="mt-6 p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-lg text-center"
      >
        <p className="text-sm text-yellow-300 flex items-center justify-center">
          <span className="mr-2">⚠️</span>
          This tool provides general information only. Always consult a dental professional for proper diagnosis and treatment.
        </p>
      </motion.div>
    </motion.div>
  );
}