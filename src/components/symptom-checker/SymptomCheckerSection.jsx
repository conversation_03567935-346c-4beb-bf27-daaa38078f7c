import React, { useState } from 'react';
import { useSymptom<PERSON><PERSON><PERSON> } from '../../hooks/useSymptomChecker';
import ErrorMessage from '../ErrorMessage';
import { handleApiError } from '../../lib/utils/errorHandler';
import { motion, AnimatePresence } from 'framer-motion';

export default function SymptomChecker() {
  const {
    checkSymptoms,
    loading,
    error,
    result,
    clearError,
    setError
  } = useSymptomChecker();

  const [selectedRegion, setSelectedRegion] = useState(null);
  const [symptoms, setSymptoms] = useState('');
  const [image, setImage] = useState(null);

  const regionMap = {
    'Upper Left': { id: 'upper-left', label: 'Upper Left' },
    'Upper Right': { id: 'upper-right', label: 'Upper Right' },
    'Lower Left': { id: 'lower-left', label: 'Lower Left' },
    'Lower Right': { id: 'lower-right', label: 'Lower Right' }
  };

  const handleSubmit = async () => {
    try {
      if (clearError) clearError();

      if (!symptoms.trim() && !image) {
        setError('Please describe your symptoms or upload an image');
        return;
      }

      // Include the selected region in the symptoms description if available
      const symptomsWithRegion = selectedRegion
        ? `[${selectedRegion}] ${symptoms}`
        : symptoms;

      console.log('Submitting symptom check:', {
        hasSymptoms: !!symptomsWithRegion.trim(),
        hasImage: !!image,
        imageType: image ? typeof image : 'none',
        imagePreview: image ? image.substring(0, 50) + '...' : 'none'
      });

      try {
        const response = await checkSymptoms(symptomsWithRegion, image);

        if (!response) {
          throw new Error('Failed to get assessment results');
        }

        console.log('Symptom check completed successfully');
      } catch (checkError) {
        console.error('Error during symptom check:', checkError);
        throw checkError;
      }
    } catch (err) {
      console.error('Symptom checker error:', err);
      if (setError) {
        setError(handleApiError(err));
      }
    }
  };

  const handleImageUpload = (file) => {
    if (!file) {
      console.error('No file provided for upload');
      return;
    }

    if (!file.type.startsWith('image/')) {
      console.error('Invalid file type:', file.type);
      setError('Please upload a valid image file (JPEG, PNG, etc.)');
      return;
    }

    console.log('Processing image upload:', {
      fileName: file.name,
      fileType: file.type,
      fileSize: file.size
    });

    const reader = new FileReader();

    reader.onloadend = () => {
      console.log('Image loaded successfully');
      setImage(reader.result);
    };

    reader.onerror = (error) => {
      console.error('Error reading file:', error);
      setError('Failed to process the image. Please try another file.');
    };

    reader.readAsDataURL(file);
  };

  const handleDragOver = (e) => {
    e.preventDefault();
  };

  const handleDrop = (e) => {
    e.preventDefault();
    const file = e.dataTransfer.files[0];
    if (file && file.type.startsWith('image/')) {
      handleImageUpload(file);
    }
  };

  const handleFileSelect = (e) => {
    const file = e.target.files[0];
    if (file) {
      handleImageUpload(file);
    }
  };

  const handleQuadrantClick = (region) => {
    setSelectedRegion(region);
  };

  return (
    <div className="mx-auto px-6 pt-24 pb-12">
      <h1 className="text-6xl font-bold text-blue-300 mb-4 text-center">Symptom Checker</h1>
      <p className="text-xl text-white/80 mb-16 text-center">Get an initial assessment of your dental concerns</p>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-12 max-w-5xl mx-auto">
        <div className="flex flex-col space-y-12 items-center">
          {/* Top buttons */}
          <div className="flex justify-between w-full px-4 gap-8">
            <button
              className={`py-2 px-4 w-[110px] text-sm rounded-full text-white font-medium ${selectedRegion === 'Upper Left' ? 'bg-blue-500' : 'bg-blue-900/50'}`}
              onClick={() => setSelectedRegion('Upper Left')}
            >
              Upper Left
            </button>
            <button
              className={`py-2 px-4 w-[110px] text-sm rounded-full text-white font-medium ${selectedRegion === 'Upper Right' ? 'bg-blue-500' : 'bg-blue-900/50'}`}
              onClick={() => setSelectedRegion('Upper Right')}
            >
              Upper Right
            </button>
          </div>

          {/* Diagram */}
          <div className="w-full max-w-[320px] aspect-square">
            <div className="w-full h-full bg-blue-900/30 relative rounded-lg overflow-hidden">
              {/* Horizontal divider */}
              <div className="absolute left-0 right-0 top-1/2 h-[1px] bg-gray-500/50 z-10"></div>

              {/* Vertical divider */}
              <div className="absolute top-0 bottom-0 left-1/2 w-[1px] bg-gray-500/50 z-10"></div>

              {/* Center dot */}
              <div className="absolute top-1/2 left-1/2 w-8 h-8 bg-white rounded-full transform -translate-x-1/2 -translate-y-1/2 z-20"></div>

              {/* Clickable quadrants */}
              <button
                className={`absolute top-0 left-0 w-1/2 h-1/2 ${selectedRegion === 'Upper Left' ? 'bg-blue-500/20' : 'hover:bg-blue-900/40'} transition-colors z-0`}
                onClick={() => handleQuadrantClick('Upper Left')}
                aria-label="Upper Left Quadrant"
              ></button>
              <button
                className={`absolute top-0 right-0 w-1/2 h-1/2 ${selectedRegion === 'Upper Right' ? 'bg-blue-500/20' : 'hover:bg-blue-900/40'} transition-colors z-0`}
                onClick={() => handleQuadrantClick('Upper Right')}
                aria-label="Upper Right Quadrant"
              ></button>
              <button
                className={`absolute bottom-0 left-0 w-1/2 h-1/2 ${selectedRegion === 'Lower Left' ? 'bg-purple-500/20' : 'hover:bg-blue-900/40'} transition-colors z-0`}
                onClick={() => handleQuadrantClick('Lower Left')}
                aria-label="Lower Left Quadrant"
              ></button>
              <button
                className={`absolute bottom-0 right-0 w-1/2 h-1/2 ${selectedRegion === 'Lower Right' ? 'bg-purple-500/20' : 'hover:bg-blue-900/40'} transition-colors z-0`}
                onClick={() => handleQuadrantClick('Lower Right')}
                aria-label="Lower Right Quadrant"
              ></button>
            </div>
          </div>

          {/* Bottom buttons */}
          <div className="flex justify-between w-full px-4 gap-8">
            <button
              className={`py-2 px-4 w-[110px] text-sm rounded-full text-white font-medium ${selectedRegion === 'Lower Left' ? 'bg-purple-500' : 'bg-blue-900/50'}`}
              onClick={() => setSelectedRegion('Lower Left')}
            >
              Lower Left
            </button>
            <button
              className={`py-2 px-4 w-[110px] text-sm rounded-full text-white font-medium ${selectedRegion === 'Lower Right' ? 'bg-purple-500' : 'bg-blue-900/50'}`}
              onClick={() => setSelectedRegion('Lower Right')}
            >
              Lower Right
            </button>
          </div>
        </div>

        <div className="space-y-6">
          <div className="mb-8">
            <div className="flex items-center mb-3">
              <label className="block text-white">
                {selectedRegion ? (
                  <span className="flex items-center">
                    Describe symptoms in
                    <span className={`ml-2 px-3 py-1 rounded-full text-sm ${selectedRegion.toLowerCase().includes('lower') ? 'bg-purple-500 text-white' : 'bg-blue-500 text-white'}`}>
                      {selectedRegion.toLowerCase()}
                    </span>
                  </span>
                ) : 'Describe your symptoms (optional if uploading an image)'}
              </label>
            </div>
            <textarea
              className="w-full h-36 bg-gray-800/30 text-white rounded-lg border border-gray-700/50 focus:border-blue-500 focus:ring-blue-500 p-4"
              placeholder={selectedRegion
                ? `Describe your symptoms in the ${selectedRegion.toLowerCase()} region...`
                : "Example: Sharp pain when eating cold foods..."}
              value={symptoms}
              onChange={(e) => setSymptoms(e.target.value)}
            ></textarea>
          </div>

          <div className="mb-10">
            <div className="flex justify-between mb-3">
              <label className="block text-white">Upload a dental image (optional)</label>
              <span className="text-sm text-gray-400">Supported: JPEG, PNG (max 5MB)</span>
            </div>

            <div
              className="border-2 border-dashed border-gray-600/50 rounded-lg p-10 text-center cursor-pointer"
              onDragOver={handleDragOver}
              onDrop={handleDrop}
              onClick={() => document.getElementById('file-upload').click()}
      >
              <input
                id="file-upload"
                type="file"
                className="hidden"
                accept="image/jpeg,image/png"
                onChange={handleFileSelect}
              />

              {image ? (
                <div className="space-y-2">
                  <img src={image} alt="Uploaded dental image" className="max-h-32 mx-auto" />
                  <button
                    className="text-red-400 hover:text-red-300 text-sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      setImage(null);
                    }}
                  >
                    Remove Image
                  </button>
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center">
                  <svg className="w-10 h-10 text-gray-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  <p className="text-gray-300 mb-2">
                    Drag & drop a dental image here, or click to select
                  </p>
                  <p className="text-sm text-gray-400">
                    Supported formats: JPEG, PNG (max 5MB)
                  </p>
                </div>
              )}
            </div>
          </div>

          <button
            className="w-full py-4 bg-gradient-to-r from-blue-400 to-indigo-500 text-white rounded-full font-medium flex items-center justify-center"
            onClick={handleSubmit}
            disabled={loading || (!symptoms.trim() && !image)}
          >
            {loading ? (
              <>
                <div className="animate-spin w-5 h-5 mr-3 rounded-full border-2 border-white border-t-transparent"></div>
                Analyzing...
              </>
            ) : (
              <>
                <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
                Analyze
              </>
            )}
          </button>
        </div>
      </div>

      <AnimatePresence>
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="mt-6 max-w-5xl mx-auto"
          >
            <ErrorMessage
              message={error}
              onDismiss={clearError}
            />
          </motion.div>
        )}
      </AnimatePresence>

      <AnimatePresence>
        {result && !loading && (
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -30 }}
            transition={{ type: "spring", stiffness: 100, damping: 15 }}
            className="mt-6 bg-blue-900/30 border border-blue-800 p-6 rounded-lg max-w-5xl mx-auto"
          >
            <h3 className="text-xl font-semibold text-blue-300 mb-4">Assessment Result</h3>
            <div className="prose prose-invert max-w-none">
              {result}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
          </div>
  );
}