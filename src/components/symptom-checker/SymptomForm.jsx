import React, { useState, useCallback, useEffect } from 'react';
import ImageUpload from '../ImageUpload';
import Button from '../common/Button';
import { validateImage } from '../../lib/utils/validation';
import { motion } from 'framer-motion';

export default function SymptomForm({ onSubmit, loading, selectedRegion }) {
  const [symptoms, setSymptoms] = useState('');
  const [image, setImage] = useState(null);
  const [imageError, setImageError] = useState(null);
  const [placeholder, setPlaceholder] = useState(getPlaceholder());
  const [focused, setFocused] = useState(false);

  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Validate inputs
    if (!symptoms.trim() && !image) {
      setImageError('Please describe your symptoms or upload an image');
      return;
    }

    // Clear any previous errors
    setImageError(null);
    
    // Pass the data to the parent component
    // The image is already in the correct format (data URL) from the ImageUpload component
    onSubmit(symptoms, image);
  };

  const handleImageUpload = useCallback((imageDataUrl) => {
    setImageError(null);
    try {
      console.log('Image uploaded:', {
        hasData: !!imageDataUrl,
        dataType: typeof imageDataUrl
      });
      
      // Store the image data URL directly
      setImage(imageDataUrl);
    } catch (error) {
      console.error('Error handling image upload:', error);
      setImageError(error.message || 'Error uploading image');
      setImage(null);
    }
  }, []);

  function getPlaceholder() {
    if (!selectedRegion) return "Example: Sharp pain when eating cold foods...";
    return `Describe your symptoms in the ${selectedRegion} region...`;
  }

  useEffect(() => {
    setPlaceholder(getPlaceholder());
  }, [selectedRegion]);

  // Animation variants
  const formVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 10
      }
    }
  };

  return (
    <motion.form
      onSubmit={handleSubmit}
      className="space-y-8 relative"
      variants={formVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Background decorative element */}
      <div className="absolute -top-8 -right-8 w-64 h-64 bg-gradient-to-br from-blue-500/5 to-purple-500/5 rounded-full blur-xl opacity-70 pointer-events-none"></div>

      <motion.div variants={itemVariants}>
        <label className="block text-sm font-medium text-white/80 mb-3">
          {selectedRegion ? (
            <motion.span 
              key={selectedRegion}
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              className="inline-flex items-center"
            >
              <span className="mr-2">Describe symptoms in</span>
              <span className={`px-2 py-1 rounded-full text-xs font-medium bg-gradient-to-r 
                ${selectedRegion === 'upper-left' ? 'from-blue-500 to-indigo-500' : 
                 selectedRegion === 'upper-right' ? 'from-indigo-500 to-purple-500' :
                 selectedRegion === 'lower-left' ? 'from-purple-500 to-pink-500' :
                 'from-pink-500 to-blue-500'}`}
              >
                {selectedRegion.replace('-', ' ')}
              </span>
            </motion.span>
          ) : (
            'Describe your symptoms (optional if uploading an image)'
          )}
        </label>
        <div className="relative">
          <motion.textarea
            value={symptoms}
            onChange={(e) => setSymptoms(e.target.value)}
            onFocus={() => setFocused(true)}
            onBlur={() => setFocused(false)}
            placeholder={placeholder}
            className="w-full h-36 bg-white/5 text-white rounded-xl border border-white/10 focus:border-blue-500 focus:ring-blue-500 resize-none px-4 py-3 relative z-10"
            animate={{
              boxShadow: focused ? 
                "0 0 15px rgba(59, 130, 246, 0.3)" : 
                "0 0 0px rgba(59, 130, 246, 0)"
            }}
            transition={{ duration: 0.3 }}
          />
          
          {/* Subtle tooth icon in the textarea */}
          <motion.div 
            className="absolute bottom-3 right-3 text-white/10 text-2xl pointer-events-none"
            animate={{ 
              rotate: [0, 5, 0, -5, 0],
            }}
            transition={{ 
              duration: 10, 
              repeat: Infinity,
              ease: "easeInOut" 
            }}
          >
            🦷
          </motion.div>
        </div>
      </motion.div>

      <motion.div variants={itemVariants}>
        <label className="block text-sm font-medium text-white/80 mb-3">
          Upload a dental image (optional)
          <span className="text-white/60 text-xs ml-2">Supported: JPEG, PNG (max 5MB)</span>
        </label>
        
        <div className="relative z-10">
          <ImageUpload onImageUpload={handleImageUpload} />
          {imageError && (
            <motion.p 
              initial={{ opacity: 0, y: 5 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-2 text-red-400 text-sm flex items-center"
            >
              <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
              {imageError}
            </motion.p>
          )}
          {image && (
            <motion.div 
              className="mt-4 overflow-hidden"
              initial={{ opacity: 0, height: 0 }}
              animate={{ 
                opacity: 1, 
                height: 'auto',
                transition: {
                  height: { type: "spring", stiffness: 100, damping: 20 }
                }
              }}
            >
              <motion.div
                className="relative group"
                whileHover={{ scale: 1.02 }}
                transition={{ duration: 0.2 }}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <img 
                  src={image} 
                  alt="Uploaded dental image" 
                  className="max-w-xs rounded-lg shadow-lg mb-2 border border-white/10" 
                />
              </motion.div>
              
              <motion.button
                type="button"
                onClick={() => setImage(null)}
                className="text-red-400 hover:text-red-300 text-sm flex items-center mt-2"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
                Remove Image
              </motion.button>
            </motion.div>
          )}
        </div>
      </motion.div>

      <motion.div variants={itemVariants}>
        <motion.div
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <Button
            type="submit"
            disabled={loading || (!symptoms.trim() && !image)}
            className="w-full flex items-center justify-center gap-2 bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500 hover:from-blue-600 hover:via-indigo-600 hover:to-purple-600 shadow-lg shadow-blue-500/20 py-3"
          >
            {loading ? (
              <>
                <motion.div 
                  className="rounded-full h-5 w-5 border-2 border-white border-t-transparent"
                  animate={{ rotate: 360 }}
                  transition={{ 
                    duration: 1,
                    repeat: Infinity,
                    ease: "linear"
                  }}
                />
                Analyzing...
              </>
            ) : (
              <>
                <motion.svg 
                  className="w-5 h-5" 
                  fill="none" 
                  viewBox="0 0 24 24" 
                  stroke="currentColor"
                  initial={{ rotate: 0 }}
                  animate={{ rotate: [0, 15, 0, -15, 0] }}
                  transition={{ 
                    duration: 2,
                    repeat: Infinity, 
                    repeatDelay: 1,
                    ease: "easeInOut"
                  }}
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </motion.svg>
                Analyze
              </>
            )}
          </Button>
        </motion.div>
      </motion.div>
    </motion.form>
  );
}