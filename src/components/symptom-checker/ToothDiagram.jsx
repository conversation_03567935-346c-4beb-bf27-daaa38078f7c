import React from 'react';
import { motion } from 'framer-motion';

const REGIONS = [
  { 
    id: 'upper-left', 
    label: 'Upper Left', 
    path: 'M20,20 L50,20 L50,50 L20,50 Z',
    textPosition: 'translate-x-[25%] translate-y-[25%] top-0 left-0',
    color: 'from-blue-500 to-indigo-500'
  },
  { 
    id: 'upper-right', 
    label: 'Upper Right', 
    path: 'M50,20 L80,20 L80,50 L50,50 Z',
    textPosition: 'translate-x-[-25%] translate-y-[25%] top-0 right-0',
    color: 'from-indigo-500 to-purple-500'
  },
  { 
    id: 'lower-left', 
    label: 'Lower Left', 
    path: 'M20,50 L50,50 L50,80 L20,80 Z',
    textPosition: 'translate-x-[25%] translate-y-[-25%] bottom-0 left-0',
    color: 'from-purple-500 to-pink-500'
  },
  { 
    id: 'lower-right', 
    label: 'Lower Right', 
    path: 'M50,50 L80,50 L80,80 L50,80 Z',
    textPosition: 'translate-x-[-25%] translate-y-[-25%] bottom-0 right-0',
    color: 'from-pink-500 to-blue-500'
  }
];

export default function ToothDiagram({ onSelectRegion, selectedRegion }) {
  // Animation variants
  const svgVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: { 
        duration: 0.6,
        when: "beforeChildren",
        staggerChildren: 0.1
      }
    }
  };

  const pathVariants = {
    hidden: { opacity: 0, scale: 0.9 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: { 
        duration: 0.4,
        ease: "easeOut"
      }
    }
  };

  const buttonVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { 
        type: "spring",
        stiffness: 200,
        damping: 10
      }
    },
    hover: {
      scale: 1.05,
      backgroundColor: "rgba(96, 165, 250, 0.4)",
      boxShadow: "0 0 15px rgba(96, 165, 250, 0.3)",
      transition: { duration: 0.2 }
    },
    tap: { scale: 0.95 }
  };

  return (
    <div className="relative max-w-md mx-auto aspect-square">
      {/* Decorative floating elements */}
      <motion.div 
        className="absolute -top-4 -left-4 w-24 h-24 bg-gradient-to-br from-blue-500/10 to-indigo-500/10 rounded-full blur-xl"
        animate={{
          y: [0, -8, 0],
          opacity: [0.5, 0.8, 0.5],
        }}
        transition={{
          duration: 5,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
      <motion.div 
        className="absolute -bottom-4 -right-4 w-32 h-32 bg-gradient-to-br from-purple-500/10 to-pink-500/10 rounded-full blur-xl"
        animate={{
          y: [0, 8, 0],
          opacity: [0.5, 0.8, 0.5],
        }}
        transition={{
          duration: 5,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 1
        }}
      />

      {/* SVG Diagram */}
      <motion.svg 
        viewBox="0 0 100 100" 
        className="w-full relative z-10"
        variants={svgVariants}
        initial="hidden"
        animate="visible"
      >
        <defs>
          <linearGradient id="toothGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#FFFFFF" stopOpacity="0.15" />
            <stop offset="100%" stopColor="#FFFFFF" stopOpacity="0.05" />
          </linearGradient>

          {/* Gradient definitions for each quadrant */}
          <linearGradient id="upperLeftGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="rgba(59, 130, 246, 0.6)" />
            <stop offset="100%" stopColor="rgba(99, 102, 241, 0.6)" />
          </linearGradient>
          
          <linearGradient id="upperRightGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="rgba(99, 102, 241, 0.6)" />
            <stop offset="100%" stopColor="rgba(168, 85, 247, 0.6)" />
          </linearGradient>
          
          <linearGradient id="lowerLeftGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="rgba(168, 85, 247, 0.6)" />
            <stop offset="100%" stopColor="rgba(236, 72, 153, 0.6)" />
          </linearGradient>
          
          <linearGradient id="lowerRightGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="rgba(236, 72, 153, 0.6)" />
            <stop offset="100%" stopColor="rgba(59, 130, 246, 0.6)" />
          </linearGradient>
          
          {/* Glow filter */}
          <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
            <feGaussianBlur stdDeviation="3" result="blur" />
            <feComposite in="SourceGraphic" in2="blur" operator="over" />
          </filter>
        </defs>
        
        {/* Center point */}
        <motion.circle 
          cx="50" 
          cy="50" 
          r="3" 
          fill="white"
          animate={{ 
            scale: [1, 1.5, 1],
            opacity: [0.5, 1, 0.5]
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        
        {/* Connector lines */}
        <motion.line 
          x1="50" y1="20" x2="50" y2="80" 
          stroke="white" 
          strokeOpacity="0.3"
          strokeWidth="0.5"
          variants={pathVariants}
        />
        <motion.line 
          x1="20" y1="50" x2="80" y2="50" 
          stroke="white" 
          strokeOpacity="0.3"
          strokeWidth="0.5"
          variants={pathVariants}
        />
        
        {/* Region paths */}
        {REGIONS.map(({ id, path }, index) => (
          <motion.path
            key={id}
            d={path}
            variants={pathVariants}
            whileHover={{ 
              scale: 1.02,
              transition: { duration: 0.2 }
            }}
            className={`
              cursor-pointer transition-all duration-300 hover:opacity-80
            `}
            style={{
              fill: selectedRegion === id 
                ? `url(#${id}Gradient)` 
                : 'rgba(255, 255, 255, 0.07)',
              filter: selectedRegion === id ? 'url(#glow)' : 'none'
            }}
            onClick={() => onSelectRegion?.(id)}
          />
        ))}
      </motion.svg>
        
      {/* Text labels positioned in quadrants */}
      {REGIONS.map(({ id, label, textPosition, color }, index) => (
        <motion.button
          key={id}
          onClick={() => onSelectRegion?.(id)}
          variants={buttonVariants}
          initial="hidden"
          animate="visible"
          whileHover="hover"
          whileTap="tap"
          custom={index}
          className={`
            absolute transform
            px-4 py-2 rounded-full text-sm font-medium
            transition-all duration-300
            ${textPosition}
            ${selectedRegion === id 
              ? `bg-gradient-to-r ${color} text-white shadow-lg shadow-blue-500/25` 
              : 'bg-white/10 backdrop-blur-sm text-white/90 hover:text-white'
            }
            ${id === 'upper-right' || id === 'lower-right' ? 'whitespace-nowrap' : ''}
          `}
        >
          {selectedRegion === id && (
            <motion.span 
              className="absolute inset-0 rounded-full bg-white/10"
              animate={{ 
                scale: [1, 1.2, 1],
                opacity: [0.1, 0.2, 0.1]
              }}
              transition={{ 
                duration: 2, 
                repeat: Infinity,
                ease: "easeInOut" 
              }}
            />
          )}
          {label}
        </motion.button>
      ))}
    </div>
  );
}