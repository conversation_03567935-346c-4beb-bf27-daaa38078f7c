import React from 'react';
import ReactMarkdown from 'react-markdown';
import { motion } from 'framer-motion';

export default function AssessmentResult({ result }) {
  if (!result) return null;

  // Clean up the result text by removing excessive asterisks and formatting
  const cleanResult = result
    .replace(/\*\*([^*]+)\*\*/g, '$1') // Remove ** formatting
    .replace(/\n\d+\.\s+\*\*([^:]+)\*\*:/g, '\n### $1') // Convert numbered points with titles to markdown headings
    .replace(/\n\s*•\s+\*\*([^:]+)\*\*:/g, '\n#### $1') // Convert bullet points with titles to markdown subheadings
    .replace(/\n\s*•\s+\*\*([^*]+)\*\*/g, '\n- $1') // Convert bullet points with bold to regular list items
    .replace(/Recommendations:/g, '### Recommendations'); // Convert "Recommendations:" to a heading

  const disclaimer = `
⚠️ IMPORTANT: This assessment is for informational purposes only and does not constitute medical advice. 
Please consult a dental professional for proper diagnosis and treatment.`;

  return (
    <motion.div 
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="mt-6 p-6 bg-gradient-to-br from-blue-900/30 to-purple-900/30 backdrop-blur-sm rounded-lg border border-white/10 shadow-lg"
    >
      <div className="space-y-6">
        <div className="p-4 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
          <p className="text-yellow-300 text-sm">
            {disclaimer}
          </p>
        </div>
        
        <ReactMarkdown 
          className="prose prose-invert prose-lg max-w-none
            prose-headings:text-blue-300 
            prose-headings:font-semibold 
            prose-headings:mb-3
            prose-h3:text-xl
            prose-h3:text-blue-200
            prose-h3:border-b 
            prose-h3:border-blue-800/50
            prose-h3:pb-2
            prose-h3:mt-6
            prose-h4:text-lg
            prose-h4:text-indigo-300
            prose-p:text-white/90
            prose-li:text-white/90
            prose-li:my-1
            prose-strong:text-blue-200
            prose-strong:font-medium"
        >
          {cleanResult}
        </ReactMarkdown>
      </div>
    </motion.div>
  );
}