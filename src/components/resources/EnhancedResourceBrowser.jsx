import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ENHANCED_RESOURCE_CATEGORIES,
  searchResources,
  filterByTags,
  getRelatedArticles,
  getPopularTags,
  getCategoryStats,
  resourceAnalytics
} from '../../lib/constants/categories';
import {
  USER_ROLES,
  ROLE_PREFERENCES,
  toggleBookmark,
  trackUserProgress,
  addToHistory,
  getPersonalizedRecommendations,
  createLearningPath
} from '../../lib/constants/userPreferences';
import { Chart } from 'react-chartjs-2';

const EnhancedResourceBrowser = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTags, setSelectedTags] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'
  const [sortBy, setSortBy] = useState('newest'); // 'newest', 'popular', 'readingTime'
  const [difficultyFilter, setDifficultyFilter] = useState('all');
  const [categoryStats, setCategoryStats] = useState([]);
  const [popularTags, setPopularTags] = useState([]);
  const [userRole, setUserRole] = useState(USER_ROLES.STUDENT);
  const [bookmarkedArticles, setBookmarkedArticles] = useState([]);
  const [readingProgress, setReadingProgress] = useState({});
  const [showRecommendations, setShowRecommendations] = useState(true);
  const [learningPath, setLearningPath] = useState(null);
  const [chartView, setChartView] = useState('bar'); // 'bar', 'pie', 'radar'

  useEffect(() => {
    setCategoryStats(getCategoryStats(ENHANCED_RESOURCE_CATEGORIES));
    setPopularTags(getPopularTags(ENHANCED_RESOURCE_CATEGORIES));
  }, []);

  useEffect(() => {
    const userId = 'current-user'; // Replace with actual user ID
    const bookmarks = JSON.parse(localStorage.getItem(`bookmarks_${userId}`) || '[]');
    const progress = JSON.parse(localStorage.getItem(`progress_${userId}`) || '{}');
    const recommendations = getPersonalizedRecommendations(userId, userRole);
    
    setBookmarkedArticles(bookmarks);
    setReadingProgress(progress);
    setLearningPath(createLearningPath(userId, [], userRole));
    
    // Set initial filters based on role
    const rolePrefs = ROLE_PREFERENCES[userRole];
    setDifficultyFilter(rolePrefs.defaultDifficulty);
    setSelectedCategory(rolePrefs.recommendedCategories[0]);
  }, [userRole]);

  const filteredArticles = React.useMemo(() => {
    let articles = selectedCategory
      ? ENHANCED_RESOURCE_CATEGORIES.find(c => c.title === selectedCategory)?.articles || []
      : ENHANCED_RESOURCE_CATEGORIES.flatMap(c => c.articles);

    // Apply role-based filtering
    const rolePrefs = ROLE_PREFERENCES[userRole];
    if (showRecommendations) {
      articles = articles.filter(article =>
        article.tags.some(tag => rolePrefs.contentEmphasis.includes(tag))
      );
    }

    if (searchQuery) {
      articles = searchResources(searchQuery, [{ title: '', articles }]);
    }

    if (selectedTags.length > 0) {
      articles = filterByTags(selectedTags, [{ title: '', articles }]);
    }

    if (difficultyFilter !== 'all') {
      articles = articles.filter(a => a.difficultyLevel === difficultyFilter);
    }

    switch (sortBy) {
      case 'newest':
        return articles.sort((a, b) => new Date(b.publishedDate || 0) - new Date(a.publishedDate || 0));
      case 'popular':
        return articles.sort((a, b) => (b.views || 0) - (a.views || 0));
      case 'readingTime':
        return articles.sort((a, b) => a.readingTime - b.readingTime);
      default:
        return articles;
    }
  }, [searchQuery, selectedTags, selectedCategory, sortBy, difficultyFilter, userRole, showRecommendations]);

  const renderArticleCard = (article) => (
    <motion.div
      key={article.title}
      className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow"
      whileHover={{ scale: 1.02 }}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
    >
      <div className="flex justify-between items-start mb-4">
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white">{article.title}</h3>
        <div className="flex gap-2">
          {article.isNew && (
            <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">New</span>
          )}
          <button
            onClick={() => {
              const userId = 'current-user';
              const newBookmarks = toggleBookmark(userId, article.title);
              setBookmarkedArticles(newBookmarks);
            }}
            className={`p-2 rounded-full ${
              bookmarkedArticles.includes(article.title)
                ? 'text-yellow-500 hover:text-yellow-600'
                : 'text-gray-400 hover:text-yellow-500'
            }`}
          >
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path d="M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z" />
            </svg>
          </button>
        </div>
      </div>
      
      <p className="text-gray-600 dark:text-gray-300 mb-4">{article.summary}</p>
      
      <div className="flex flex-wrap gap-2 mb-4">
        {article.tags.map(tag => (
          <span
            key={tag}
            onClick={() => setSelectedTags(prev => [...prev, tag])}
            className="cursor-pointer bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 text-xs font-medium px-2.5 py-0.5 rounded hover:bg-gray-200 dark:hover:bg-gray-600"
          >
            {tag}
          </span>
        ))}
      </div>
      
      <div className="flex justify-between items-center text-sm text-gray-500 dark:text-gray-400">
        <span>{article.readingTime} min read</span>
        <span className="px-2 py-1 rounded bg-gray-100 dark:bg-gray-700">
          {article.difficultyLevel}
        </span>
      </div>
      
      <div className="mt-4 flex justify-between items-center">
        <a
          href={article.link}
          target="_blank"
          rel="noopener noreferrer"
          className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
          onClick={() => resourceAnalytics.trackView(article.title)}
        >
          Read More →
        </a>
        
        <div className="flex gap-2">
          <button
            onClick={() => resourceAnalytics.trackShare(article.title, 'twitter')}
            className="text-gray-600 hover:text-blue-500 dark:text-gray-400"
          >
            Share
          </button>
          {article.hasAttachments && (
            <button
              onClick={() => resourceAnalytics.trackDownload(article.title, 'pdf')}
              className="text-gray-600 hover:text-green-500 dark:text-gray-400"
            >
              Download
            </button>
          )}
        </div>
      </div>

      {/* Progress bar */}
      {readingProgress[article.title] && (
        <div className="mt-4">
          <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
            <div
              className="bg-blue-600 h-2.5 rounded-full"
              style={{ width: `${readingProgress[article.title].lastPosition}%` }}
            ></div>
          </div>
          <p className="text-xs text-gray-500 mt-1">
            {readingProgress[article.title].lastPosition}% completed
          </p>
        </div>
      )}
    </motion.div>
  );

  const renderCategoryStats = () => {
    const data = {
      labels: categoryStats.map(stat => stat.title),
      datasets: [
        {
          label: 'Number of Articles',
          data: categoryStats.map(stat => stat.articleCount),
          backgroundColor: [
            'rgba(54, 162, 235, 0.5)',
            'rgba(255, 99, 132, 0.5)',
            'rgba(255, 206, 86, 0.5)',
            'rgba(75, 192, 192, 0.5)',
            'rgba(153, 102, 255, 0.5)',
          ],
          borderColor: [
            'rgba(54, 162, 235, 1)',
            'rgba(255, 99, 132, 1)',
            'rgba(255, 206, 86, 1)',
            'rgba(75, 192, 192, 1)',
            'rgba(153, 102, 255, 1)',
          ],
          borderWidth: 1,
        },
      ],
    };

    return (
      <div className="mt-12">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Category Statistics</h2>
          <div className="flex gap-2">
            {['bar', 'pie', 'radar'].map(type => (
              <button
                key={type}
                onClick={() => setChartView(type)}
                className={`px-3 py-1 rounded ${
                  chartView === type
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-200 dark:bg-gray-700'
                }`}
              >
                {type.charAt(0).toUpperCase() + type.slice(1)}
              </button>
            ))}
          </div>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
          <Chart type={chartView} data={data} />
        </div>
      </div>
    );
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Role selector */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Select Your Role</h2>
        <div className="flex gap-4">
          {Object.values(USER_ROLES).map(role => (
            <button
              key={role}
              onClick={() => setUserRole(role)}
              className={`px-4 py-2 rounded-lg ${
                userRole === role
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-200 dark:bg-gray-700'
              }`}
            >
              {role.charAt(0).toUpperCase() + role.slice(1)}
            </button>
          ))}
        </div>
      </div>

      {/* Learning path */}
      {learningPath && (
        <div className="mb-8 bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
          <h2 className="text-xl font-semibold mb-4">Your Learning Path</h2>
          <div className="flex flex-wrap gap-4">
            {learningPath.categories.map(category => (
              <div
                key={category}
                className="bg-gray-100 dark:bg-gray-700 rounded-lg p-4"
              >
                <h3 className="font-medium mb-2">{category}</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Recommended for {learningPath.role}s
                </p>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Search and Filters */}
      <div className="mb-8 space-y-4">
        <input
          type="text"
          placeholder="Search resources..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="w-full p-4 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800"
        />
        
        <div className="flex flex-wrap gap-4">
          <select
            value={selectedCategory || ''}
            onChange={(e) => setSelectedCategory(e.target.value || null)}
            className="p-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800"
          >
            <option value="">All Categories</option>
            {ENHANCED_RESOURCE_CATEGORIES.map(category => (
              <option key={category.title} value={category.title}>
                {category.title} ({category.articles.length})
              </option>
            ))}
          </select>
          
          <select
            value={difficultyFilter}
            onChange={(e) => setDifficultyFilter(e.target.value)}
            className="p-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800"
          >
            <option value="all">All Levels</option>
            <option value="Beginner">Beginner</option>
            <option value="Intermediate">Intermediate</option>
            <option value="Advanced">Advanced</option>
          </select>
          
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="p-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800"
          >
            <option value="newest">Newest First</option>
            <option value="popular">Most Popular</option>
            <option value="readingTime">Reading Time</option>
          </select>
          
          <div className="flex gap-2">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded ${viewMode === 'grid' ? 'bg-blue-500 text-white' : 'bg-gray-200 dark:bg-gray-700'}`}
            >
              Grid
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded ${viewMode === 'list' ? 'bg-blue-500 text-white' : 'bg-gray-200 dark:bg-gray-700'}`}
            >
              List
            </button>
          </div>
        </div>
        
        {/* Popular Tags */}
        <div className="flex flex-wrap gap-2">
          {popularTags.slice(0, 10).map(tag => (
            <button
              key={tag}
              onClick={() => setSelectedTags(prev => prev.includes(tag) ? prev.filter(t => t !== tag) : [...prev, tag])}
              className={`px-3 py-1 rounded-full ${
                selectedTags.includes(tag)
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200'
              }`}
            >
              {tag}
            </button>
          ))}
        </div>
      </div>

      {/* Results with role-based recommendations */}
      <div className="mb-8">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">
            {showRecommendations ? 'Recommended for You' : 'All Resources'}
          </h2>
          <button
            onClick={() => setShowRecommendations(!showRecommendations)}
            className="text-blue-500 hover:text-blue-600"
          >
            {showRecommendations ? 'Show All' : 'Show Recommendations'}
          </button>
        </div>
        <div className={`grid gap-6 ${viewMode === 'grid' ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' : 'grid-cols-1'}`}>
          {filteredArticles.map(renderArticleCard)}
        </div>
      </div>

      {/* Enhanced category statistics */}
      {renderCategoryStats()}
    </div>
  );
};

export default EnhancedResourceBrowser; 