import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card } from '../common';
import { DENTAL_OFFICES } from '../../lib/constants/dentalOffices';
import CombinedDentalLocationsMap from './CombinedDentalLocationsMap';

const CustomDentalOfficesSelector = () => {
  const [availableOffices, setAvailableOffices] = useState(DENTAL_OFFICES);
  const [selectedOffices, setSelectedOffices] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  
  // Initialize with all offices selected
  useEffect(() => {
    setSelectedOffices([...availableOffices]);
  }, [availableOffices]);
  
  // Filter offices based on search term
  const filteredOffices = availableOffices.filter(office => 
    office.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    office.address.toLowerCase().includes(searchTerm.toLowerCase()) ||
    office.officeType.toLowerCase().includes(searchTerm.toLowerCase())
  );
  
  // Toggle selected office
  const toggleOfficeSelection = (office) => {
    const isAlreadySelected = selectedOffices.some(o => o.id === office.id);
    
    if (isAlreadySelected) {
      setSelectedOffices(selectedOffices.filter(o => o.id !== office.id));
    } else {
      setSelectedOffices([...selectedOffices, office]);
    }
  };
  
  // Select all offices
  const selectAllOffices = () => {
    setSelectedOffices([...availableOffices]);
  };
  
  // Clear all selected offices
  const clearSelectedOffices = () => {
    setSelectedOffices([]);
  };
  
  return (
    <motion.div
      className="space-y-6"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Personal Journey Section */}
      <Card className="p-6 border-l-4 border-cyan-500">
        <h2 className="text-xl font-bold text-white mb-3">My Dental Mentors</h2>
        
        <p className="text-white/90 leading-relaxed mb-4">
          These three dental offices hold a special place in my heart. Each represents a place where I've shadowed and learned from extraordinary dentists who have shaped my path toward dentistry.
        </p>
        
        <div className="space-y-6 mt-6">
          <div className="border-l-2 border-cyan-400/30 pl-4">
            <h3 className="text-cyan-300 font-semibold">Dr. De Mirza at It's A Smile World</h3>
            <p className="text-white/80 mt-1">
              Dr. De Mirza was my pediatric dentist and graciously invited me to shadow her practice. She attended the University of Miami and shares in my excitement for this journey. Her compassion and skill with children are truly inspiring. She's incredibly humble despite her success and genuinely loves her profession. I aspire to embody her gentle approach and passion for pediatric dentistry.
            </p>
          </div>
          
          <div className="border-l-2 border-indigo-400/30 pl-4">
            <h3 className="text-indigo-300 font-semibold">Dr. Golden - My Mentor</h3>
            <p className="text-white/80 mt-1">
              I met Dr. Golden in January and have nothing but the utmost respect for him. He taught me that "to be on time is to be late, and to be early is to be on time." He emphasizes the importance of excellence and has been instrumental in guiding me toward becoming an amazing dentist. His mentorship continues to shape my professional development and ethical approach to dentistry.
            </p>
          </div>
          
          <div className="border-l-2 border-purple-400/30 pl-4">
            <h3 className="text-purple-300 font-semibold">Dr. Verrett - Community Champion</h3>
            <p className="text-white/80 mt-1">
              Dr. Verrett is a beautiful individual who deeply loves the Black community and strives to provide the best care to low-income patients. She taught me that what matters most is not external appearances but internal character. She takes immense pride in her practice and truly deserves recognition for her commitment to underserved communities. Her dedication to equitable healthcare inspires me daily.
            </p>
          </div>
          
          <p className="text-white/90 italic mt-4">
            I hope to one day give back to these incredible dentists who've shaped my journey, and to share their wisdom and compassion with the world through my own practice.
          </p>
        </div>
      </Card>
      
      <Card className="p-6">
        <h2 className="text-xl font-bold text-white mb-4">Featured Dental Offices</h2>
        
        {/* Search and selection controls */}
        <div className="space-y-4 mb-6">
          <div className="flex flex-col sm:flex-row gap-3">
            <div className="relative flex-grow">
              <input
                type="text"
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                placeholder="Search dental offices..."
                className="w-full px-4 py-2 bg-slate-800 text-white rounded-lg border border-slate-700 focus:border-indigo-500 focus:ring-1 focus:ring-indigo-500 outline-none"
              />
              {searchTerm && (
                <button
                  onClick={() => setSearchTerm('')}
                  className="absolute right-3 top-2.5 text-gray-400 hover:text-white"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </button>
              )}
            </div>
            
            <div className="flex gap-2">
              <button
                onClick={selectAllOffices}
                className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition"
              >
                Select All
              </button>
              <button
                onClick={clearSelectedOffices}
                className="px-4 py-2 bg-slate-700 hover:bg-slate-600 text-white rounded-lg transition"
              >
                Clear
              </button>
            </div>
          </div>
          
          <div className="bg-slate-800/50 p-3 rounded-lg">
            <p className="text-indigo-300 mb-2 font-medium">
              {selectedOffices.length} offices selected
            </p>
            <div className="flex flex-wrap gap-2">
              {selectedOffices.map(office => (
                <span
                  key={office.id}
                  className="inline-flex items-center gap-1 bg-indigo-900/60 text-indigo-200 text-sm px-3 py-1 rounded-full"
                >
                  {office.name}
                  <button
                    onClick={() => toggleOfficeSelection(office)}
                    className="text-indigo-300 hover:text-white ml-1"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </span>
              ))}
              {selectedOffices.length === 0 && (
                <span className="text-slate-400 text-sm">No offices selected. Select offices from the list below.</span>
              )}
            </div>
          </div>
        </div>
        
        {/* Available offices list */}
        <div className="max-h-[300px] overflow-y-auto pr-2 custom-scrollbar">
          <div className="grid gap-3 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
            {filteredOffices.map(office => (
              <motion.div
                key={office.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.2 }}
              >
                <div 
                  className={`p-3 rounded-lg cursor-pointer transition-all duration-200 ${
                    selectedOffices.some(o => o.id === office.id) 
                      ? 'bg-indigo-900/40 border border-indigo-500/50' 
                      : 'bg-slate-800/50 border border-slate-700/50 hover:bg-slate-700/40'
                  }`}
                  onClick={() => toggleOfficeSelection(office)}
                >
                  <div className="flex justify-between items-start">
                    <h3 className="text-white font-medium">{office.name}</h3>
                    <div className="flex items-center">
                      <div className="flex mr-1">
                        {[...Array(Math.floor(office.rating))].map((_, i) => (
                          <svg key={i} className="w-3 h-3 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                          </svg>
                        ))}
                      </div>
                      <span className="text-white/60 text-xs">
                        {office.rating}
                      </span>
                    </div>
                  </div>
                  
                  <p className="text-slate-300 text-sm mt-1">{office.officeType}</p>
                  <p className="text-slate-400 text-xs mt-2 truncate">{office.address}</p>
                  
                  {office.highlights && office.highlights.length > 0 && (
                    <div className="mt-2 flex flex-wrap gap-1">
                      {office.highlights.map((highlight, i) => (
                        <span 
                          key={i} 
                          className="text-xs px-1.5 py-0.5 bg-slate-700 text-slate-300 rounded"
                        >
                          {highlight}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </motion.div>
            ))}
            
            {filteredOffices.length === 0 && (
              <div className="col-span-full p-4 text-center">
                <p className="text-slate-400">No dental offices match your search criteria.</p>
              </div>
            )}
          </div>
        </div>
      </Card>
      
      {/* Map with selected dental offices */}
      <CombinedDentalLocationsMap customOffices={selectedOffices} />
    </motion.div>
  );
};

export default CustomDentalOfficesSelector; 