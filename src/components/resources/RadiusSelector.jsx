import React from 'react';

const RADIUS_OPTIONS = [
  { value: 25, label: '25 miles' },
  { value: 50, label: '50 miles' },
  { value: 100, label: '100 miles' },
  { value: 250, label: '250 miles' },
  { value: Infinity, label: 'All Locations' }
];

export default function RadiusSelector({ value, onChange, disabled }) {
  const handleChange = (e) => {
    const newValue = e.target.value === 'Infinity' ? Infinity : Number(e.target.value);
    onChange(newValue);
  };

  return (
    <div className="flex items-center gap-3">
      <label htmlFor="radius" className="text-white/80 text-sm">
        Search Radius:
      </label>
      <select
        id="radius"
        value={value === Infinity ? 'Infinity' : value}
        onChange={handleChange}
        disabled={disabled}
        className="bg-white/10 border-white/20 rounded-lg text-white focus:ring-blue-500 focus:border-blue-500"
      >
        {RADIUS_OPTIONS.map(({ value, label }) => (
          <option key={label} value={value === Infinity ? 'Infinity' : value}>
            {label}
          </option>
        ))}
      </select>
    </div>
  );
}