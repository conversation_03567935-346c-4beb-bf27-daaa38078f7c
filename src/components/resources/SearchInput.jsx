import React from 'react';
import { motion } from 'framer-motion';

export default function SearchInput({ value, onChange }) {
  return (
    <div className="relative mb-6">
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="relative"
      >
        <input
          type="text"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder="Search articles, topics, or keywords..."
          className="w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl px-5 py-3.5 pl-12 text-white placeholder-white/50 focus:ring-2 focus:ring-blue-500/50 focus:border-blue-400/50 transition-all shadow-md focus:shadow-blue-500/10"
        />
        <div className="absolute left-3.5 top-1/2 -translate-y-1/2 text-white/50">
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>
        
        {value && (
          <button
            onClick={() => onChange('')}
            className="absolute right-3.5 top-1/2 -translate-y-1/2 text-white/50 hover:text-white/80 transition-colors"
          >
            <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}
      </motion.div>
    </div>
  );
}