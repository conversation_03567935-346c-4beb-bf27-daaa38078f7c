import React from 'react';
import { motion } from 'framer-motion';

export default function ArticleCard({ article }) {
  const { title, summary, source, link, tags = [] } = article;
  
  return (
    <motion.div 
      whileHover={{ y: -5, transition: { duration: 0.2 } }}
      className="bg-white/5 backdrop-blur-sm rounded-xl p-6 hover:bg-white/10 transition-all duration-300 border border-white/10 hover:border-white/20 shadow-lg hover:shadow-xl h-full flex flex-col"
    >
      <h3 className="text-xl font-semibold text-white mb-3 line-clamp-2">{title}</h3>
      
      <div className="mb-3">
        <p className="text-blue-100/80 line-clamp-3 text-sm">{summary}</p>
      </div>
      
      {tags && tags.length > 0 && (
        <div className="flex flex-wrap gap-2 mb-4 mt-auto pt-2">
          {tags.slice(0, 3).map((tag, index) => (
            <span
              key={index}
              className="px-2 py-0.5 bg-blue-500/10 text-blue-300 text-xs rounded-full border border-blue-400/20"
            >
              {tag}
            </span>
          ))}
          {tags.length > 3 && (
            <span className="text-xs text-blue-300/70">+{tags.length - 3} more</span>
          )}
        </div>
      )}

      <div className="mt-auto pt-3 border-t border-white/10">
        {source && (
          <div className="text-xs text-white/60 mb-2">
            <span className="text-cyan-400/80">Source:</span> {source}
          </div>
        )}
        {link && (
          <a
            href={link}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center bg-gradient-to-r from-blue-600/80 to-indigo-600/80 text-white text-sm px-3 py-1 rounded-lg hover:from-blue-500 hover:to-indigo-500 transition-all shadow-sm hover:shadow-md"
            onClick={(e) => e.stopPropagation()}
          >
            Read Article
            <svg
              className="w-3.5 h-3.5 ml-1"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
              />
            </svg>
          </a>
        )}
      </div>
    </motion.div>
  );
}