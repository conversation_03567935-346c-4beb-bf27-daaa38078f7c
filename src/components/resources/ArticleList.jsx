import React from 'react';
import ArticleCard from './ArticleCard';
import { motion } from 'framer-motion';

export default function ArticleList({ articles }) {
  if (articles.length === 0) {
    return (
      <motion.div 
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="text-center py-12"
      >
        <div className="bg-white/5 backdrop-blur-sm rounded-xl p-8 border border-white/10">
          <svg className="w-16 h-16 text-blue-300/50 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
          </svg>
          <h3 className="text-xl font-semibold text-white mb-2">No Articles Found</h3>
          <p className="text-blue-100/80 max-w-md mx-auto">
            Try adjusting your search criteria or categories to find relevant dental content.
          </p>
        </div>
      </motion.div>
    );
  }

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 }
  };

  return (
    <motion.div 
      variants={container}
      initial="hidden"
      animate="show"
      className="grid gap-6 md:grid-cols-2 lg:grid-cols-3"
    >
      {articles.map((article, index) => (
        <motion.div key={index} variants={item}>
          <ArticleCard article={article} />
        </motion.div>
      ))}
    </motion.div>
  );
}