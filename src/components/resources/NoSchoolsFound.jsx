import React from 'react';
import { getDefaultSchools } from '../../lib/services/schoolValidationService';
import DentalSchoolCard from './DentalSchoolCard';
import CardGrid from '../common/CardGrid';

export default function NoSchoolsFound() {
  const defaultSchools = getDefaultSchools('FL');

  return (
    <div className="space-y-6">
      <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
        <p className="text-yellow-200">
          No accredited dental schools were found near your location. 
          Here's our list of accredited schools in Florida:
        </p>
      </div>
      
      <CardGrid>
        {defaultSchools.map((school) => (
          <DentalSchoolCard key={school.id} school={school} />
        ))}
      </CardGrid>
    </div>
  );
}