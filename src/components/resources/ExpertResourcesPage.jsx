import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { RESOURCE_CATEGORIES } from '../../lib/constants/categories';
import SearchInput from './SearchInput';
import CategoryFilter from './CategoryFilter';
import TagFilter from './TagFilter';
import ArticleList from './ArticleList';
import NutritionGuidance from './NutritionGuidance';
import CustomDentalOfficesSelector from './CustomDentalOfficesSelector';
import { Link } from 'react-router-dom';

export default function ExpertResourcesPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [selectedTags, setSelectedTags] = useState([]);
  const [activeTab, setActiveTab] = useState('articles');
  const [showSmiloSnap, setShowSmiloSnap] = useState(false);
  const [hideSmiloSnap, setHideSmiloSnap] = useState(true); // Default to hiding the SmiloSnap feature

  const categories = useMemo(() => 
    RESOURCE_CATEGORIES.map(cat => cat.title),
    []
  );

  const allTags = useMemo(() => 
    [...new Set(RESOURCE_CATEGORIES
      .flatMap(cat => cat.articles)
      .flatMap(article => article.tags))],
    []
  );

  const filteredArticles = useMemo(() => {
    let articles = RESOURCE_CATEGORIES
      .filter(cat => !selectedCategory || cat.title === selectedCategory)
      .flatMap(cat => cat.articles);

    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      articles = articles.filter(article =>
        article.title.toLowerCase().includes(term) ||
        article.summary.toLowerCase().includes(term)
      );
    }

    if (selectedTags.length > 0) {
      articles = articles.filter(article =>
        selectedTags.some(tag => article.tags.includes(tag))
      );
    }

    return articles;
  }, [searchTerm, selectedCategory, selectedTags]);

  const handleTagToggle = (tag) => {
    setSelectedTags(prev =>
      prev.includes(tag)
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    );
  };

  const toggleSmiloSnap = () => {
    setShowSmiloSnap(!showSmiloSnap);
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-indigo-900 via-blue-900 to-indigo-800 pb-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-12">
        <div className="text-center mb-12">
          <motion.h1 
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-4xl md:text-5xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-300 to-purple-400"
          >
            Expert Dental Resources
          </motion.h1>
          <motion.p 
            initial={{ opacity: 0, y: -5 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="mt-3 text-xl text-blue-100 max-w-3xl mx-auto"
          >
            Evidence-based articles, research, and dental care locations
          </motion.p>
          
          {/* Admin-only toggle - hidden by default */}
          <div className="mt-2">
            <button 
              onClick={() => setHideSmiloSnap(!hideSmiloSnap)}
              className="text-xs text-blue-300/30 hover:text-blue-300/50"
            >
              {hideSmiloSnap ? "Enable SmiloSnap" : "Disable SmiloSnap"}
            </button>
          </div>
        </div>

        {/* SmiloSnap Toggle Button - Only shown if not hidden */}
        {!hideSmiloSnap && (
          <div className="flex justify-center mb-8">
            <button
              onClick={toggleSmiloSnap}
              className="px-6 py-3 bg-gradient-to-r from-purple-500 to-indigo-600 text-white rounded-lg shadow-lg hover:shadow-xl transition duration-300 flex items-center space-x-2"
            >
              <span className="text-lg font-medium">
                {showSmiloSnap ? 'Close SmiloSnap' : 'Try SmiloSnap: SNAP/EBT Dental Food Planner'}
              </span>
              <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
              </svg>
            </button>
          </div>
        )}

        {/* SmiloSnap Section - Only shown if not hidden and if showSmiloSnap is true */}
        <AnimatePresence>
          {!hideSmiloSnap && showSmiloSnap && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="mb-12 overflow-hidden"
            >
              <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 md:p-8 border border-white/20 shadow-xl">
                <div className="text-center mb-6">
                  <h2 className="text-2xl font-bold text-blue-100">SmiloSnap: EBT Food Planner for Dental Health</h2>
                  <p className="text-blue-200 mt-2">Plan your SNAP/EBT grocery shopping with dental health in mind</p>
                </div>
                <div className="flex justify-center">
                  <Link
                    to="/smilo-bites"
                    className="px-6 py-3 bg-indigo-600 text-white rounded-lg shadow-md hover:bg-indigo-700 transition duration-200"
                  >
                    Launch SmiloSnap - Free Access
                  </Link>
                </div>
                <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-blue-100">
                  <div className="bg-white/5 p-4 rounded-lg">
                    <h3 className="font-medium mb-2">Find Tooth-Friendly Foods</h3>
                    <p>Discover SNAP-eligible foods that promote dental health</p>
                  </div>
                  <div className="bg-white/5 p-4 rounded-lg">
                    <h3 className="font-medium mb-2">Build Smart Shopping Lists</h3>
                    <p>Create grocery lists optimized for your dental health goals</p>
                  </div>
                  <div className="bg-white/5 p-4 rounded-lg">
                    <h3 className="font-medium mb-2">AI-Powered Guidance</h3>
                    <p>Get personalized advice for making tooth-friendly choices</p>
                  </div>
                </div>
                <div className="mt-4 text-center text-blue-200 text-sm">
                  <p>No login required - SmiloSnap is freely accessible to everyone!</p>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        <div className="flex justify-center mb-8">
          <div className="inline-flex rounded-lg p-1 bg-white/5 backdrop-blur-sm">
            <button
              onClick={() => setActiveTab('articles')}
              className={`px-6 py-2.5 rounded-md text-sm font-medium transition-all ${
                activeTab === 'articles'
                  ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg'
                  : 'text-blue-100 hover:text-white'
              }`}
            >
              Research & Articles
            </button>
            <button
              onClick={() => setActiveTab('nutrition')}
              className={`px-6 py-2.5 rounded-md text-sm font-medium transition-all ${
                activeTab === 'nutrition'
                  ? 'bg-gradient-to-r from-emerald-500 to-teal-600 text-white shadow-lg'
                  : 'text-blue-100 hover:text-white'
              }`}
            >
              Nutrition & Oral Health
            </button>
            <button
              onClick={() => setActiveTab('dental-offices')}
              className={`px-6 py-2.5 rounded-md text-sm font-medium transition-all ${
                activeTab === 'dental-offices'
                  ? 'bg-gradient-to-r from-cyan-500 to-blue-600 text-white shadow-lg'
                  : 'text-blue-100 hover:text-white'
              }`}
            >
              Dental Offices
            </button>
          </div>
        </div>

        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
          className="bg-white/10 backdrop-blur-md rounded-2xl p-6 md:p-8 border border-white/20 shadow-xl"
        >
          {activeTab === 'articles' ? (
            <div className="space-y-8">
              <SearchInput
                value={searchTerm}
                onChange={setSearchTerm}
              />

              <div className="grid gap-6 md:grid-cols-2">
                <div>
                  <CategoryFilter
                    categories={categories}
                    selectedCategory={selectedCategory}
                    onSelect={setSelectedCategory}
                  />
                </div>
                <div>
                  <TagFilter
                    tags={allTags}
                    selectedTags={selectedTags}
                    onToggleTag={handleTagToggle}
                  />
                </div>
              </div>

              <AnimatePresence mode="wait">
                <motion.div
                  key={`${selectedCategory}-${selectedTags.join('-')}-${searchTerm}`}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <ArticleList articles={filteredArticles} />
                </motion.div>
              </AnimatePresence>
            </div>
          ) : activeTab === 'nutrition' ? (
            <NutritionGuidance />
          ) : (
            <CustomDentalOfficesSelector />
          )}
        </motion.div>
      </div>
    </div>
  );
}