import React, { useState } from 'react';
import { motion } from 'framer-motion';
import ResourceHeader from './ResourceHeader';
import ResourceFilters from './ResourceFilters';
import ResourceGrid from './ResourceGrid';
import { ENHANCED_RESOURCE_CATEGORIES } from '../../lib/constants/categories';

const ResourceBrowser = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All Categories');
  const [selectedTags, setSelectedTags] = useState([]);
  const [viewMode, setViewMode] = useState('grid');

  // Filter articles based on search, category, and tags
  const filteredArticles = React.useMemo(() => {
    let articles = selectedCategory === 'All Categories'
      ? ENHANCED_RESOURCE_CATEGORIES.flatMap(c => c.articles)
      : ENHANCED_RESOURCE_CATEGORIES.find(c => c.title === selectedCategory)?.articles || [];

    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      articles = articles.filter(article => 
        article.title.toLowerCase().includes(query) ||
        article.summary.toLowerCase().includes(query) ||
        article.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    if (selectedTags.length > 0) {
      articles = articles.filter(article =>
        selectedTags.some(tag => article.tags.includes(tag))
      );
    }

    return articles;
  }, [searchQuery, selectedCategory, selectedTags]);

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <ResourceHeader />
      
      <ResourceFilters
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        selectedCategory={selectedCategory}
        onCategoryChange={setSelectedCategory}
        selectedTags={selectedTags}
        onTagsChange={setSelectedTags}
        viewMode={viewMode}
        onViewModeChange={setViewMode}
        categories={ENHANCED_RESOURCE_CATEGORIES}
      />

      <ResourceGrid
        articles={filteredArticles}
        viewMode={viewMode}
      />
    </div>
  );
};

export default ResourceBrowser; 