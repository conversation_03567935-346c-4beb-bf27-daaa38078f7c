import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

// Define dietary categories
const DIETARY_CATEGORIES = [
  {
    id: 'vegan',
    title: 'Vegan Diet',
    icon: '🌱',
    color: 'from-green-500 to-emerald-500',
    articles: [
      {
        title: 'Vegan Nutrition for Dental Health',
        summary: 'How plant-based diets can support strong teeth and healthy gums with proper planning.',
        imageUrl: 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?ixlib=rb-1.2.1&auto=format&fit=crop&w=600&q=80',
        link: '#vegan-nutrition-dental',
        date: 'March 15, 2023'
      },
      {
        title: 'Calcium Sources for Vegans',
        summary: 'Plant-based calcium sources that help maintain strong teeth and prevent dental issues.',
        imageUrl: 'https://images.unsplash.com/photo-1610348725531-843dff563e2c?ixlib=rb-1.2.1&auto=format&fit=crop&w=600&q=80',
        link: '#vegan-calcium',
        date: 'January 23, 2023'
      },
      {
        title: 'Vitamin D for Vegans: Essential for Dental Health',
        summary: 'How vegans can ensure adequate vitamin D intake for optimal calcium absorption and dental health.',
        imageUrl: 'https://images.unsplash.com/photo-1536304993881-ff6e9eefa2a6?ixlib=rb-1.2.1&auto=format&fit=crop&w=600&q=80',
        link: '#vegan-vitamin-d',
        date: 'February 8, 2023'
      }
    ],
    videos: [
      {
        title: 'Vegan Diet and Oral Health',
        thumbnail: 'https://images.unsplash.com/photo-1505576399279-565b52d4ac71?ixlib=rb-1.2.1&auto=format&fit=crop&w=600&q=80',
        link: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
        duration: '12:45',
        channel: 'Dental Health Today'
      },
      {
        title: 'Plant-Based Calcium for Strong Teeth',
        thumbnail: 'https://images.unsplash.com/photo-1606914707708-5cb33cea43fb?ixlib=rb-1.2.1&auto=format&fit=crop&w=600&q=80',
        link: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
        duration: '8:30',
        channel: 'Vegan Dental Care'
      }
    ]
  },
  {
    id: 'vegetarian',
    title: 'Vegetarian Diet',
    icon: '🥗',
    color: 'from-blue-500 to-cyan-500',
    articles: [
      {
        title: 'Vegetarian Diet Benefits for Oral Health',
        summary: 'How a balanced vegetarian diet can promote good oral health and prevent common dental issues.',
        imageUrl: 'https://images.unsplash.com/photo-1540914124281-342587941389?ixlib=rb-1.2.1&auto=format&fit=crop&w=600&q=80',
        link: '#vegetarian-oral-health',
        date: 'April 2, 2023'
      },
      {
        title: 'Dairy Products and Dental Health',
        summary: 'The role of dairy in a vegetarian diet for maintaining tooth enamel and preventing decay.',
        imageUrl: 'https://images.unsplash.com/photo-**********-b2692b85b150?ixlib=rb-1.2.1&auto=format&fit=crop&w=600&q=80',
        link: '#vegetarian-dairy',
        date: 'March 11, 2023'
      },
      {
        title: "Balanced Vegetarian Diets for Kids' Teeth",
        summary: "Planning nutritionally complete vegetarian meals to support children's dental development.",
        imageUrl: 'https://images.unsplash.com/photo-1490818387583-1baba5e638af?ixlib=rb-1.2.1&auto=format&fit=crop&w=600&q=80',
        link: '#vegetarian-kids',
        date: 'February 23, 2023'
      }
    ],
    videos: [
      {
        title: 'Vegetarian Nutrition for Healthy Teeth',
        thumbnail: 'https://images.unsplash.com/photo-1619566636858-adf3ef46400b?ixlib=rb-1.2.1&auto=format&fit=crop&w=600&q=80',
        link: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
        duration: '10:15',
        channel: 'Dental Health Today'
      },
      {
        title: 'Prevent Tooth Decay with Vegetarian Options',
        thumbnail: 'https://images.unsplash.com/photo-**********-ba9599a7e63c?ixlib=rb-1.2.1&auto=format&fit=crop&w=600&q=80',
        link: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
        duration: '9:30',
        channel: 'Smile Health'
      }
    ]
  },
  {
    id: 'regular',
    title: 'Regular Diet',
    icon: '🍽️',
    color: 'from-amber-500 to-orange-500',
    articles: [
      {
        title: 'Balanced Diet for Optimal Dental Health',
        summary: 'The importance of a varied diet containing all food groups for maintaining healthy teeth and gums.',
        imageUrl: 'https://images.unsplash.com/photo-1494390248081-4e521a5940db?ixlib=rb-1.2.1&auto=format&fit=crop&w=600&q=80',
        link: '#balanced-diet-dental',
        date: 'May 8, 2023'
      },
      {
        title: 'Protein Rich Foods for Gum Health',
        summary: 'How proteins contribute to tissue repair and maintenance for healthier gums.',
        imageUrl: 'https://images.unsplash.com/photo-**********-5315695dadae?ixlib=rb-1.2.1&auto=format&fit=crop&w=600&q=80',
        link: '#protein-gums',
        date: 'April 27, 2023'
      },
      {
        title: 'The Role of Omega-3s in Fighting Gum Disease',
        summary: 'Research on how omega-3 fatty acids from fish and other sources can reduce inflammation in gum tissues.',
        imageUrl: 'https://images.unsplash.com/photo-1537649692853-e0d14a7eccd7?ixlib=rb-1.2.1&auto=format&fit=crop&w=600&q=80',
        link: '#omega3-gum-disease',
        date: 'March 3, 2023'
      }
    ],
    videos: [
      {
        title: 'Foods That Fight Tooth Decay',
        thumbnail: 'https://images.unsplash.com/photo-1505253758473-96b7015fcd40?ixlib=rb-1.2.1&auto=format&fit=crop&w=600&q=80',
        link: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
        duration: '14:20',
        channel: 'Dental Health Today'
      },
      {
        title: 'Meal Planning for Dental Health',
        thumbnail: 'https://images.unsplash.com/photo-1498837167922-ddd27525d352?ixlib=rb-1.2.1&auto=format&fit=crop&w=600&q=80',
        link: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
        duration: '11:45',
        channel: 'Smile Health'
      }
    ]
  }
];

const DietaryNutrition: React.FC = () => {
  const [selectedDiet, setSelectedDiet] = useState(DIETARY_CATEGORIES[0].id);
  const [activeTab, setActiveTab] = useState<'articles' | 'videos'>('articles');
  
  const currentDiet = DIETARY_CATEGORIES.find(diet => diet.id === selectedDiet);

  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-semibold text-white">Dietary Nutrition & Oral Health</h2>
        <div className="flex items-center gap-2">
          <svg className="w-5 h-5 text-blue-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span className="text-sm text-blue-200">Diet-specific resources</span>
        </div>
      </div>
      
      <p className="text-blue-100">
        Browse nutrition resources tailored to different dietary preferences and their impact on your dental health.
      </p>
      
      {/* Diet Navigation */}
      <div className="flex flex-wrap gap-2">
        {DIETARY_CATEGORIES.map(diet => (
          <button
            key={diet.id}
            onClick={() => setSelectedDiet(diet.id)}
            className={`flex items-center px-4 py-2 rounded-lg transition-all ${
              selectedDiet === diet.id
                ? `bg-gradient-to-r ${diet.color} text-white shadow-lg`
                : 'bg-white/10 text-blue-100 hover:bg-white/20'
            }`}
          >
            <span className="mr-2 text-xl">{diet.icon}</span>
            <span>{diet.title}</span>
          </button>
        ))}
      </div>

      {/* Articles/Videos Tab Navigation */}
      <div className="flex justify-center">
        <div className="inline-flex rounded-lg p-1 bg-white/5 backdrop-blur-sm">
          <button
            onClick={() => setActiveTab('articles')}
            className={`px-6 py-2.5 rounded-md text-sm font-medium transition-all ${
              activeTab === 'articles'
                ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg'
                : 'text-blue-100 hover:text-white'
            }`}
          >
            Articles
          </button>
          <button
            onClick={() => setActiveTab('videos')}
            className={`px-6 py-2.5 rounded-md text-sm font-medium transition-all ${
              activeTab === 'videos'
                ? 'bg-gradient-to-r from-purple-500 to-pink-600 text-white shadow-lg'
                : 'text-blue-100 hover:text-white'
            }`}
          >
            Videos
          </button>
        </div>
      </div>

      {/* Content Display */}
      <AnimatePresence mode="wait">
        <motion.div
          key={`${selectedDiet}-${activeTab}`}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3 }}
        >
          {activeTab === 'articles' ? (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {currentDiet?.articles.map(article => (
                <a 
                  href={article.link}
                  key={article.title}
                  className="bg-white/5 backdrop-blur-sm rounded-xl overflow-hidden border border-white/10 hover:border-white/20 transition-all hover:shadow-lg hover:bg-white/10"
                >
                  <div className="relative h-48 overflow-hidden">
                    <img 
                      src={article.imageUrl} 
                      alt={article.title} 
                      className="object-cover w-full h-full"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent flex items-end">
                      <h3 className="text-lg font-semibold text-white p-4">{article.title}</h3>
                    </div>
                  </div>
                  <div className="p-4">
                    <p className="text-blue-100 line-clamp-3 mb-3">{article.summary}</p>
                    <div className="flex justify-between items-center text-sm">
                      <span className="text-blue-300">{article.date}</span>
                      <span className="text-white bg-white/10 px-2 py-1 rounded">Read More</span>
                    </div>
                  </div>
                </a>
              ))}
            </div>
          ) : (
            <div className="grid gap-6 md:grid-cols-2">
              {currentDiet?.videos.map(video => (
                <div 
                  key={video.title}
                  className="bg-white/5 backdrop-blur-sm rounded-xl overflow-hidden border border-white/10"
                >
                  <div className="relative cursor-pointer group">
                    <img 
                      src={video.thumbnail} 
                      alt={video.title} 
                      className="object-cover w-full aspect-video"
                    />
                    <div className="absolute inset-0 flex items-center justify-center bg-black/30 group-hover:bg-black/50 transition-colors">
                      <div className="w-16 h-16 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center">
                        <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M8 5v14l11-7z" />
                        </svg>
                      </div>
                    </div>
                    <div className="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
                      {video.duration}
                    </div>
                  </div>
                  <div className="p-4">
                    <h3 className="text-lg font-semibold text-white mb-1">{video.title}</h3>
                    <p className="text-blue-300 text-sm">{video.channel}</p>
                    <div className="mt-3">
                      <a 
                        href={video.link} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="inline-flex items-center gap-1 text-white bg-white/10 hover:bg-white/20 px-3 py-1.5 rounded transition-colors"
                      >
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10zm-1-7v-6l5 3-5 3z" />
                        </svg>
                        Watch Video
                      </a>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </motion.div>
      </AnimatePresence>
    </div>
  );
};

export default DietaryNutrition; 