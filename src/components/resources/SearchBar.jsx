import React, { useState, useEffect, useRef } from 'react';
import { useUser } from '@/contexts/UserContext';
import { logSearchQuery, getUserSearchHistory } from '@/lib/services/userHistoryService';
import { supabase } from '@/lib/supabase';

export default function SearchBar({ 
  searchTerm, 
  onSearchChange, 
  selectedTags, 
  onTagsChange, 
  availableTags,
  category = 'articles'
}) {
  const { user } = useUser();
  const [showHistory, setShowHistory] = useState(false);
  const [searchHistory, setSearchHistory] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [tablesReady, setTablesReady] = useState(true);
  const searchRef = useRef(null);

  // Check if the search history table exists
  useEffect(() => {
    const checkTable = async () => {
      try {
        const { error } = await supabase
          .from('user_search_history')
          .select('id')
          .limit(1);
          
        if (error && (error.code === '404' || error.status === 404 || error.code === 'PGRST301')) {
          console.info('Search history table not found - history features disabled');
          setTablesReady(false);
        }
      } catch (error) {
        console.info('Error checking search history table:', error);
        setTablesReady(false);
      }
    };
    
    checkTable();
  }, []);

  // Load user search history on mount and when user changes
  useEffect(() => {
    const fetchHistory = async () => {
      if (!user?.id || !tablesReady) {
        setSearchHistory([]);
        return;
      }
      
      setIsLoading(true);
      try {
        const history = await getUserSearchHistory(user.id, category, 5);
        setSearchHistory(history);
      } catch (error) {
        console.error('Error fetching search history:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchHistory();
  }, [user?.id, category, tablesReady]);

  // Handle click outside to close history dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (searchRef.current && !searchRef.current.contains(event.target)) {
        setShowHistory(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Save search when user submits
  const handleSearch = (e) => {
    e.preventDefault();
    
    // Only attempt to save search if user is logged in and tables exist
    if (searchTerm.trim() && user?.id && tablesReady) {
      logSearchQuery(searchTerm, category, user.id)
        .catch(error => console.error('Error saving search:', error));
      
      // Refresh history after adding new search
      getUserSearchHistory(user.id, category, 5)
        .then(history => setSearchHistory(history))
        .catch(error => console.error('Error refreshing history:', error));
    }
    
    setShowHistory(false);
  };

  // Select history item
  const selectHistoryItem = (query) => {
    onSearchChange(query);
    setShowHistory(false);
  };

  return (
    <div className="space-y-4">
      <div className="relative" ref={searchRef}>
        <form onSubmit={handleSearch}>
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
            onFocus={() => user?.id && tablesReady && setShowHistory(true)}
            placeholder="Search articles..."
            className="w-full bg-white/10 border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:ring-2 focus:ring-blue-500"
          />
          <button
            type="submit"
            className="absolute right-4 top-1/2 -translate-y-1/2 w-5 h-5 text-white/50 hover:text-white"
          >
            <svg
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          </button>
        </form>

        {/* Previous searches dropdown - only show if tables exist */}
        {showHistory && user?.id && tablesReady && searchHistory.length > 0 && (
          <div className="absolute z-10 mt-1 w-full bg-gray-900 border border-white/10 rounded-lg shadow-lg overflow-hidden">
            <div className="px-3 py-2 border-b border-white/10">
              <h4 className="text-sm text-white/70">Recent Searches</h4>
            </div>
            <div className="max-h-64 overflow-y-auto">
              {isLoading ? (
                <div className="py-4 text-center text-white/50 text-sm">Loading...</div>
              ) : (
                <ul>
                  {searchHistory.map((item, index) => (
                    <li key={`${item.id || index}`}>
                      <button
                        onClick={() => selectHistoryItem(item.query)}
                        className="w-full px-4 py-2 text-left text-white hover:bg-white/5 flex items-center space-x-2"
                      >
                        <svg className="w-4 h-4 text-white/40" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span>{item.query}</span>
                      </button>
                    </li>
                  ))}
                </ul>
              )}
            </div>
          </div>
        )}
      </div>

      <div className="flex flex-wrap gap-2">
        {availableTags.map((tag) => (
          <button
            key={tag}
            onClick={() => {
              if (selectedTags.includes(tag)) {
                onTagsChange(selectedTags.filter(t => t !== tag));
              } else {
                onTagsChange([...selectedTags, tag]);
              }
            }}
            className={`
              px-3 py-1 rounded-full text-sm font-medium
              transition-colors duration-200
              ${selectedTags.includes(tag)
                ? 'bg-blue-500 text-white'
                : 'bg-white/10 text-white/80 hover:bg-white/20'
              }
            `}
          >
            {tag}
          </button>
        ))}
      </div>
    </div>
  );
}