import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

export default function TagFilter({ tags, selectedTags, onToggleTag }) {
  const [expanded, setExpanded] = useState(false);
  const initialDisplayCount = 12; // Number of tags to show initially
  
  const displayTags = expanded ? tags : tags.slice(0, initialDisplayCount);
  const hasMoreTags = tags.length > initialDisplayCount;
  
  return (
    <div className="mb-6">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-lg font-medium text-white">Popular Tags</h3>
        {hasMoreTags && (
          <button 
            onClick={() => setExpanded(!expanded)}
            className="text-blue-300 hover:text-blue-200 text-sm font-medium flex items-center"
          >
            {expanded ? (
              <>
                <span>Show Less</span>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                </svg>
              </>
            ) : (
              <>
                <span>Show All</span>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </>
            )}
          </button>
        )}
      </div>
      
      <div className="flex flex-wrap gap-2">
        <AnimatePresence>
          {displayTags.map((tag) => (
            <motion.button
              key={tag}
              initial={!expanded ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ duration: 0.15 }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => onToggleTag(tag)}
              className={`px-3 py-1.5 rounded-lg text-xs font-medium transition-all duration-200 
                ${selectedTags.includes(tag)
                  ? 'bg-gradient-to-r from-emerald-500 to-teal-600 text-white shadow-md'
                  : 'bg-white/5 backdrop-blur-sm text-white/80 hover:bg-white/10 border border-white/10'}`}
            >
              {tag}
            </motion.button>
          ))}
        </AnimatePresence>
        
        {!expanded && hasMoreTags && (
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="px-3 py-1.5 rounded-lg text-xs font-medium bg-white/5 backdrop-blur-sm text-blue-300 border border-blue-400/20"
            onClick={() => setExpanded(true)}
          >
            +{tags.length - initialDisplayCount} more
          </motion.button>
        )}
      </div>
    </div>
  );
}