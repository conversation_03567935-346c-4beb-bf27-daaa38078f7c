import React from 'react';

/**
 * PaginatedArticleList component for displaying a paginated list of articles
 * This is a placeholder component created to fix a build error
 */
const PaginatedArticleList = ({ articles = [], currentPage = 1, totalPages = 1, onPageChange = () => {} }) => {
  return (
    <div className="space-y-4">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {articles.map((article) => (
          <div key={article.id} className="bg-white p-4 rounded shadow">
            <h3 className="font-semibold">{article.title}</h3>
            <p className="text-sm text-gray-600">{article.excerpt}</p>
          </div>
        ))}
      </div>
      
      {totalPages > 1 && (
        <div className="flex justify-center mt-6">
          <nav className="flex space-x-2">
            {Array.from({ length: totalPages }, (_, i) => (
              <button
                key={i}
                onClick={() => onPageChange(i + 1)}
                className={`px-3 py-1 rounded ${
                  currentPage === i + 1 ? 'bg-primary-500 text-white' : 'bg-gray-200'
                }`}
              >
                {i + 1}
              </button>
            ))}
          </nav>
        </div>
      )}
    </div>
  );
};

export default PaginatedArticleList; 