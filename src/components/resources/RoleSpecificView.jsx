import React from 'react';
import { motion } from 'framer-motion';
import { USER_ROLES, ROLE_PREFERENCES } from '../../lib/constants/userPreferences';

const RoleSpecificView = ({ role, articles, onArticleClick }) => {
  const rolePrefs = ROLE_PREFERENCES[role];

  const sections = {
    [USER_ROLES.STUDENT]: [
      {
        title: 'Getting Started',
        description: 'Essential resources for dental students',
        filter: article => article.difficultyLevel === 'Beginner'
      },
      {
        title: 'Core Concepts',
        description: 'Fundamental dental knowledge',
        filter: article => article.tags.includes('educational')
      },
      {
        title: 'Practical Skills',
        description: 'Hands-on learning resources',
        filter: article => article.tags.includes('practical')
      }
    ],
    [USER_ROLES.PROFESSIONAL]: [
      {
        title: 'Clinical Updates',
        description: 'Latest clinical research and techniques',
        filter: article => article.tags.includes('clinical')
      },
      {
        title: 'Practice Management',
        description: 'Business and operational resources',
        filter: article => article.tags.includes('business')
      },
      {
        title: 'Advanced Procedures',
        description: 'Specialized treatment techniques',
        filter: article => article.difficultyLevel === 'Advanced'
      }
    ],
    [USER_ROLES.RESEARCHER]: [
      {
        title: 'Research Methods',
        description: 'Research methodologies and tools',
        filter: article => article.tags.includes('research')
      },
      {
        title: 'Data Analysis',
        description: 'Statistical analysis and interpretation',
        filter: article => article.tags.includes('analytical')
      },
      {
        title: 'Latest Studies',
        description: 'Recent research publications',
        filter: article => article.isNew && article.tags.includes('scientific')
      }
    ]
  };

  const renderSection = (section) => {
    const sectionArticles = articles.filter(section.filter);
    
    return (
      <div key={section.title} className="mb-8">
        <h3 className="text-xl font-semibold mb-2">{section.title}</h3>
        <p className="text-gray-600 dark:text-gray-400 mb-4">{section.description}</p>
        
        <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
          {sectionArticles.map(article => (
            <motion.div
              key={article.title}
              className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow hover:shadow-lg transition-shadow cursor-pointer"
              whileHover={{ scale: 1.02 }}
              onClick={() => onArticleClick(article)}
            >
              <h4 className="font-medium mb-2">{article.title}</h4>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                {article.summary}
              </p>
              <div className="flex justify-between items-center text-sm">
                <span>{article.readingTime} min read</span>
                <span className="px-2 py-1 rounded bg-gray-100 dark:bg-gray-700">
                  {article.difficultyLevel}
                </span>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="p-6">
      <div className="mb-8">
        <h2 className="text-2xl font-bold mb-2">
          Resources for {role.charAt(0).toUpperCase() + role.slice(1)}s
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          Curated content matching your professional needs
        </p>
      </div>

      <div className="mb-8">
        <h3 className="text-lg font-semibold mb-4">Recommended Categories</h3>
        <div className="flex flex-wrap gap-4">
          {rolePrefs.recommendedCategories.map(category => (
            <div
              key={category}
              className="bg-blue-50 dark:bg-blue-900 text-blue-700 dark:text-blue-200 px-4 py-2 rounded-lg"
            >
              {category}
            </div>
          ))}
        </div>
      </div>

      {sections[role].map(renderSection)}
    </div>
  );
};

export default RoleSpecificView; 