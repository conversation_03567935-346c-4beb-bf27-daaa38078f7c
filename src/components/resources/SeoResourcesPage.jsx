import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';

// SEO content data with comprehensive dental topics for better search visibility
const SEO_RESOURCES = [
  // Original Articles
  {
    id: 'dental-wipes-secret-weapon',
    title: 'Dental Wipes: The Secret Weapon For Jaw-Dropping First Impressions (And Surprise Hookups)',
    description: 'Discover how pocket-sized dental wipes can save your dating life, provide confidence during surprise intimate encounters, and offer dignity to those without regular bathroom access.',
    category: 'Sexual Health & Oral Conditions',
    route: '/resources/seo/dental-wipes-secret-weapon',
    keywords: 'dental wipes dating, surprise hookup preparation, oral hygiene dating, discreet breath freshening, finger wipes intimate moments'
  },
  {
    id: 'wet-mouth-better-sex',
    title: 'Wet Mouth, Better Sex: The Untold Secret to Mind-Blowing Intimate Encounters',
    description: 'Discover how proper oral hydration dramatically improves intimate experiences, reduces STI risks, and transforms your confidence in the bedroom.',
    category: 'Sexual Health & Oral Conditions',
    route: '/resources/seo/wet-mouth-better-sex',
    keywords: 'oral hydration sex, dry mouth intimacy, XyliMelts sexual benefits, better kissing hydration, oral moisture intimate health'
  },
  {
    id: 'fresh-breath-intimate-moments',
    title: 'Fresh Breath and Intimate Moments: The Unspoken Pillar of Your Love Life',
    description: 'Discover how breath quality directly impacts your dating and sex life, with scientifically-backed strategies for maintaining freshness when it matters most.',
    category: 'Sexual Health & Oral Conditions',
    route: '/resources/seo/fresh-breath-intimate-moments',
    keywords: 'fresh breath intimate moments, kissing breath tips, oral sex breath preparation, confidence sexual encounters, zinc lozenges intimacy'
  },
  {
    id: 'sensitive-gums-ultra-soft-toothbrushes',
    title: 'Sensitive Gums? Why Ultra-Soft Toothbrushes Are The Dental Game-Changer You Need',
    description: 'Discover how ultra-soft toothbrushes can revolutionize your dental care routine, especially if you have sensitive gums or receding gumlines. Science-backed benefits explained.',
    category: 'Dental Technology',
    route: '/resources/seo/sensitive-gums-ultra-soft-toothbrushes',
    keywords: 'sensitive gums, ultra-soft toothbrush, gum recession, gentle brushing, dental care, periodontal health, bristle technology'
  },
  {
    id: 'oral-health-intimate-care',
    title: 'Oral Health and Intimate Care: The Connection You Need to Know',
    description: 'Discover the essential link between oral hygiene and intimate wellness. Learn how tongue scraping and proper oral care can enhance both health and experiences.',
    category: 'Sexual Health & Oral Conditions',
    route: '/resources/seo/oral-health-intimate-care',
    keywords: 'tongue scraper benefits, oral hygiene intimate health, fresh breath confidence, bacterial reduction mouth, oral care sexual health, intimate wellness oral hygiene'
  },
  {
    id: 'unlicensed-veneer-techs',
    title: 'Unlicensed "Veneer Techs": Understanding the Risks of Non-Dental Professionals',
    description: 'Explore the risks associated with unlicensed practitioners claiming to be "veneer techs" or "cosmetic dental specialists" without proper dental education.',
    category: 'Dental Education',
    route: '/resources/seo/unlicensed-veneer-techs',
    keywords: 'unlicensed veneer technicians, illegal dental practice, cosmetic dentistry risks'
  },
  {
    id: 'veneer-techniques',
    title: 'Advanced Dental Veneer Techniques: The Complete Guide',
    description: 'Explore the latest veneer techniques, materials, and innovations that dental professionals are using to create natural-looking, long-lasting results.',
    category: 'Cosmetic Dentistry',
    route: '/resources/seo/veneer-techniques',
    keywords: 'dental veneers, veneer techniques, minimal-prep veneers, digital smile design'
  },
  {
    id: 'prepless-veneers',
    title: 'Prepless Veneers: The Revolutionary No-Prep Smile Transformation',
    description: 'Learn about no-preparation veneers, a significant advancement in cosmetic dentistry that transforms smiles without irreversible tooth reduction.',
    category: 'Cosmetic Dentistry',
    route: '/resources/seo/prepless-veneers',
    keywords: 'prepless veneers, no-prep veneers, conservative cosmetic dentistry'
  },
  {
    id: 'composite-vs-porcelain-veneers',
    title: 'Composite vs. Porcelain Veneers: A Comprehensive Comparison Guide',
    description: 'Compare the differences, advantages, and considerations for both composite and porcelain veneer options for your smile makeover.',
    category: 'Cosmetic Dentistry',
    route: '/resources/seo/composite-vs-porcelain-veneers',
    keywords: 'porcelain veneers, composite veneers, veneer comparison, dental veneer materials'
  },
  {
    id: 'viral-tiktok-dental-trends',
    title: 'Viral Dental Trends on Social Media: What Dental Professionals Need to Know',
    description: 'Analysis of popular dental content on social media, exploring validity, risks, and professional perspectives on trending techniques.',
    category: 'Dental Education',
    route: '/resources/seo/viral-tiktok-dental-trends',
    keywords: 'dental TikTok trends, viral dental content, DIY teeth whitening, social media dentistry'
  },
  {
    id: 'digital-dentistry-revolution',
    title: 'The Digital Dentistry Revolution: Transforming Patient Care Through Technology',
    description: 'Explore how digital technologies from CAD/CAM systems to 3D printing and AI are transforming dental workflow efficiency and patient care.',
    category: 'Dental Technology',
    route: '/resources/seo/digital-dentistry-revolution',
    keywords: 'digital dentistry, CAD/CAM dentistry, 3D printing dental, dental technology'
  },
  {
    id: 'dental-implant-innovations',
    title: 'Dental Implant Innovations: The Latest Advancements in Implant Technology',
    description: 'Discover cutting-edge developments in dental implant technology, including new materials, digital workflows, and minimally invasive techniques.',
    category: 'Dental Technology',
    route: '/resources/seo/dental-implant-innovations',
    keywords: 'dental implant innovations, implant technology advancements, guided implant surgery'
  },
  {
    id: 'clear-aligner-therapy',
    title: 'Clear Aligner Therapy: The Complete Guide to Modern Orthodontic Treatment',
    description: 'Comprehensive overview of clear aligner therapy, including technology, clinical applications, and considerations for this popular orthodontic treatment.',
    category: 'Orthodontics',
    route: '/resources/seo/clear-aligner-therapy',
    keywords: 'clear aligner therapy, Invisalign treatment, transparent orthodontics, digital orthodontics'
  },
  {
    id: 'sleep-apnea-dental-solutions',
    title: 'Sleep Apnea Dental Solutions: The Dentist\'s Role in Managing Sleep-Disordered Breathing',
    description: 'Learn about the essential role dental professionals play in screening, referring, and treating patients with sleep apnea and other breathing disorders.',
    category: 'Dental Medicine',
    route: '/resources/seo/sleep-apnea-dental-solutions',
    keywords: 'dental sleep medicine, oral appliance therapy, sleep apnea dental treatment'
  },
  {
    id: 'dental-anxiety-management',
    title: 'Dental Anxiety Management: Evidence-Based Strategies for Patient Comfort',
    description: 'Explore proven techniques for alleviating dental fear and anxiety, from communication strategies to advanced sedation options.',
    category: 'Patient Care',
    route: '/resources/seo/dental-anxiety-management',
    keywords: 'dental anxiety management, dental phobia treatment, dental fear reduction, sedation dentistry'
  },
  
  // Nutrition & Oral Health Articles
  {
    id: 'nutrition-oral-health-connection',
    title: 'The Nutrition-Oral Health Connection: How Your Diet Affects Your Smile',
    description: 'Comprehensive guide exploring how dietary choices directly influence everything from tooth decay to gum disease, with evidence-based insights on essential nutrients for optimal oral health.',
    category: 'Nutrition & Oral Health',
    route: '/resources/seo/nutrition-oral-health-connection',
    keywords: 'nutrition and oral health, diet and dental health, foods for healthy teeth, sugar and tooth decay'
  },
  {
    id: 'oral-microbiome-diet-influence',
    title: 'The Oral Microbiome Diet: How Food Choices Shape Your Mouth\'s Ecosystem',
    description: 'Explore how dietary choices directly influence the composition and behavior of the oral microbiome, with profound implications for dental caries, periodontal disease, and overall health.',
    category: 'Nutrition & Oral Health',
    route: '/resources/seo/oral-microbiome-diet-influence',
    keywords: 'oral microbiome diet, food and mouth bacteria, diet oral health bacteria, probiotics for teeth'
  },
  {
    id: 'anti-inflammatory-foods-periodontal-health',
    title: 'Anti-Inflammatory Foods for Periodontal Health: Nutrition Strategies to Combat Gum Disease',
    description: 'Evidence-based strategies for leveraging dietary choices to prevent and manage periodontal disease through anti-inflammatory nutrition.',
    category: 'Nutrition & Oral Health',
    route: '/resources/seo/anti-inflammatory-foods-periodontal-health',
    keywords: 'anti-inflammatory diet periodontal disease, foods for gum health, omega-3 gum inflammation'
  },
  {
    id: 'sugar-alternatives-dental-health',
    title: 'Sugar Alternatives for Dental Health: The Complete Guide to Tooth-Friendly Sweeteners',
    description: 'Comprehensive examination of various sugar substitutes, from polyols to artificial sweeteners, providing evidence-based insights into their cariogenic potential, benefits, and practical applications.',
    category: 'Nutrition & Oral Health',
    route: '/resources/seo/sugar-alternatives-dental-health',
    keywords: 'sugar alternatives teeth, xylitol dental benefits, erythritol tooth decay, stevia dental health'
  },
  {
    id: 'superfoods-oral-health-benefits',
    title: 'Superfoods for Oral Health: Evidence-Based Foods That Strengthen Teeth and Gums',
    description: 'Discover the science behind true "superfoods" for oral health, separating evidence-based recommendations from marketing hype.',
    category: 'Nutrition & Oral Health',
    route: '/resources/seo/superfoods-oral-health-benefits',
    keywords: 'superfoods for teeth, best foods for gum health, calcium-rich foods teeth, antioxidants oral health'
  },

  // Sexual Health & Oral Conditions Articles
  {
    id: 'oral-sex-hpv-cancer-risks',
    title: 'Oral Sex and HPV: Understanding the Rising Rates of Oropharyngeal Cancer',
    description: 'Examination of the relationship between sexual behaviors—particularly oral sex—and HPV-related oral cancers, providing evidence-based information for both dental professionals and patients.',
    category: 'Sexual Health & Oral Conditions',
    route: '/resources/seo/oral-sex-hpv-cancer-risks',
    keywords: 'oral sex HPV cancer, HPV oral cancer risk, oropharyngeal cancer causes, HPV-16 oral transmission'
  },
  {
    id: 'std-oral-manifestations-guide',
    title: 'STD Oral Manifestations: A Clinical Guide for Dental Professionals',
    description: 'Comprehensive guide to recognizing, differentiating, and responding to common oral manifestations of sexually transmitted infections in dental practice.',
    category: 'Sexual Health & Oral Conditions',
    route: '/resources/seo/std-oral-manifestations-guide',
    keywords: 'STD oral symptoms, oral STI lesions, syphilis oral manifestations, gonorrhea mouth symptoms'
  },
  {
    id: 'kissing-disease-transmission-oral',
    title: 'The Science of Kissing: Disease Transmission and Oral Health Implications',
    description: 'Scientific analysis of infectious disease transmission through kissing, including pathogens of concern, transmission mechanisms, and preventive strategies for oral health professionals.',
    category: 'Sexual Health & Oral Conditions',
    route: '/resources/seo/kissing-disease-transmission-oral',
    keywords: 'kissing disease transmission, oral herpes transmission, mono kissing disease, saliva pathogen transfer'
  },
  {
    id: 'oral-contraceptives-gum-health',
    title: 'Oral Contraceptives and Gum Health: Understanding the Hormonal Connection',
    description: 'Evidence-based exploration of the relationship between oral contraceptives, hormonal fluctuations, and periodontal health outcomes.',
    category: 'Sexual Health & Oral Conditions',
    route: '/resources/seo/oral-contraceptives-gum-health',
    keywords: 'birth control pills gum disease, oral contraceptives gingivitis, hormonal gingivitis, estrogen gum tissue'
  },
  
  // Trending Oral Health Topics
  {
    id: 'oil-pulling-scientific-evidence',
    title: 'Oil Pulling: Separating Science from Marketing Claims',
    description: 'Comprehensive analysis of the scientific literature on oil pulling, evaluating evidence for oral health benefits against exaggerated marketing claims.',
    category: 'Trending Oral Health Topics',
    route: '/resources/seo/oil-pulling-scientific-evidence',
    keywords: 'oil pulling scientific research, coconut oil pulling evidence, oil pulling gingivitis evidence, oil swishing dental benefits'
  },
  {
    id: 'tongue-scraping-benefits-technique',
    title: 'Tongue Scraping: The Clinical Evidence for This Ancient Practice',
    description: 'Evidence-based examination of tongue scraping for oral malodor management, oral microbiome health, and taste perception enhancement.',
    category: 'Trending Oral Health Topics',
    route: '/resources/seo/tongue-scraping-benefits-technique',
    keywords: 'tongue scraping benefits science, tongue cleaning evidence, tongue scraper halitosis, tongue biofilm removal'
  },
  {
    id: 'water-flossers-traditional-floss-comparison',
    title: 'Water Flossers vs. Traditional Floss: What the Research Actually Shows',
    description: 'Comprehensive, evidence-based comparison of water flossers and traditional dental floss, analyzing the research on effectiveness for different patient populations.',
    category: 'Trending Oral Health Topics',
    route: '/resources/seo/water-flossers-traditional-floss-comparison',
    keywords: 'water flosser vs string floss research, waterpik effectiveness study, flossing comparison clinical trials, interdental cleaning comparison'
  },
  {
    id: 'charcoal-toothpaste-science-safety',
    title: 'Charcoal Toothpaste: Separating Science from Marketing in the Black Dentifrice Trend',
    description: 'Evidence-based examination of charcoal dental products, evaluating their efficacy, safety concerns, and appropriate place in oral hygiene routines.',
    category: 'Trending Oral Health Topics',
    route: '/resources/seo/charcoal-toothpaste-science-safety',
    keywords: 'charcoal toothpaste safety, activated charcoal teeth whitening evidence, black toothpaste abrasivity'
  },
  {
    id: 'teeth-grinding-stress-management',
    title: 'Teeth Grinding and Stress: Breaking the Cycle of Bruxism Through Effective Management',
    description: 'Comprehensive guide examining the stress-bruxism relationship and providing evidence-based strategies for breaking this destructive cycle.',
    category: 'Trending Oral Health Topics',
    route: '/resources/seo/teeth-grinding-stress-management',
    keywords: 'teeth grinding stress, bruxism anxiety management, stress induced teeth clenching'
  }
];

// Define categories for filtering
const CATEGORIES = [
  'All',
  'Cosmetic Dentistry',
  'Dental Technology',
  'Dental Education',
  'Orthodontics',
  'Dental Medicine',
  'Patient Care',
  'Nutrition & Oral Health',
  'Sexual Health & Oral Conditions',
  'Trending Oral Health Topics'
];

// Make the resources available globally for the article view component
if (typeof window !== 'undefined') {
  window.SEO_RESOURCES = SEO_RESOURCES;
}

const SeoResourcesPage = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [filteredResources, setFilteredResources] = useState(SEO_RESOURCES);

  useEffect(() => {
    // Filter resources based on search term and category
    const results = SEO_RESOURCES.filter(resource => {
      const matchesSearch = resource.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           resource.description.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesCategory = selectedCategory === 'All' || resource.category === selectedCategory;
      return matchesSearch && matchesCategory;
    });

    setFilteredResources(results);
  }, [searchTerm, selectedCategory]);

  return (
    <div className="bg-gradient-to-b from-gray-900 to-black min-h-screen py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-blue-400 via-indigo-400 to-purple-400 text-transparent bg-clip-text">
            Dental SEO Resources
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Comprehensive, evidence-based articles on the latest trends and techniques in modern dentistry
          </p>
        </motion.div>

        {/* Search and filter section */}
        <div className="mb-10 max-w-4xl mx-auto">
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-grow">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search resources..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full bg-gray-800/50 border border-gray-700 rounded-lg px-4 py-3 pl-10 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <svg
                  className="absolute left-3 top-3.5 h-5 w-5 text-gray-400"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>
            <div className="flex-shrink-0">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full md:w-auto bg-gray-800/50 border border-gray-700 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none"
                style={{ backgroundImage: "url('data:image/svg+xml;charset=US-ASCII,<svg width=\"24\" height=\"24\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"white\" viewBox=\"0 0 24 24\"><path d=\"M7 10l5 5 5-5z\"/></svg>')", backgroundRepeat: 'no-repeat', backgroundPosition: 'right 10px center' }}
              >
                {CATEGORIES.map(category => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Results counter */}
        <div className="mb-6 max-w-6xl mx-auto">
          <p className="text-gray-400">
            Showing {filteredResources.length} of {SEO_RESOURCES.length} resources
          </p>
        </div>

        {/* Resources grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {filteredResources.length > 0 ? (
            filteredResources.map((resource, index) => (
              <motion.div
                key={resource.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="bg-gradient-to-br from-gray-800/70 to-gray-900/70 backdrop-blur-sm rounded-xl border border-gray-700 overflow-hidden hover:border-blue-500/30 transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/10"
              >
                <div className="p-6">
                  <span className="inline-block px-3 py-1 bg-blue-900/50 text-blue-300 text-xs rounded-full mb-4">
                    {resource.category}
                  </span>
                  <h3 className="text-xl font-semibold text-white mb-3 leading-tight">
                    {resource.title}
                  </h3>
                  <p className="text-gray-300 mb-6 text-sm">
                    {resource.description}
                  </p>
                  <Link
                    to={resource.route}
                    className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg text-white text-sm font-medium transition-transform duration-300 hover:scale-105"
                  >
                    Read Article
                    <svg
                      className="ml-2 h-4 w-4"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                    </svg>
                  </Link>
                </div>
              </motion.div>
            ))
          ) : (
            <div className="col-span-3 text-center py-12">
              <svg
                className="mx-auto h-12 w-12 text-gray-500"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <h3 className="mt-4 text-lg font-medium text-gray-300">No resources found</h3>
              <p className="mt-2 text-gray-400">
                Try adjusting your search or filter to find what you're looking for.
              </p>
              <button
                onClick={() => {
                  setSearchTerm('');
                  setSelectedCategory('All');
                }}
                className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-blue-300 bg-blue-900/30 hover:bg-blue-900/50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Clear filters
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SeoResourcesPage;