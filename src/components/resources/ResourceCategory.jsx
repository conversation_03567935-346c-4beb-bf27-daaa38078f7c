import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import { RESOURCE_CATEGORIES } from '../../lib/constants/categories';
import { motion } from 'framer-motion';
import ArticleCard from './ArticleCard';

const ResourceCategory = ({ category }) => {
  const [articles, setArticles] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchArticles = async () => {
      try {
        // Get static articles from the category
        const staticArticles = category.articles || [];
        
        // Get dynamic articles from Supabase
        const { data: dynamicArticles, error } = await supabase
          .from('dental_articles')
          .select('*')
          .eq('status', 'published')
          .order('pub_date', { ascending: false })
          .limit(50);
          
        if (error) throw error;
        
        // Combine and deduplicate articles
        const combinedArticles = [
          ...staticArticles,
          ...dynamicArticles.map(article => ({
            title: article.title,
            summary: article.summary,
            source: article.source,
            link: article.link,
            tags: article.tags || []
          }))
        ];
        
        // Remove duplicates based on title
        const uniqueArticles = combinedArticles.filter((article, index, self) =>
          index === self.findIndex(a => a.title === article.title)
        );
        
        setArticles(uniqueArticles);
      } catch (error) {
        console.error('Error fetching articles:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchArticles();
  }, [category]);

  return (
    <div className="mb-8">
      <h2 className="text-2xl font-bold mb-4 text-gray-800">{category.title}</h2>
      <p className="text-gray-600 mb-6">{category.description}</p>
      
      {loading ? (
        <div className="flex justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-500"></div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {articles.map((article, index) => (
            <motion.div
              key={article.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300"
            >
              <div className="p-6">
                <h3 className="text-lg font-semibold mb-2 text-gray-800 hover:text-cyan-600 transition-colors">
                  <a href={article.link} target="_blank" rel="noopener noreferrer">
                    {article.title}
                  </a>
                </h3>
                <p className="text-gray-600 mb-4 line-clamp-3">{article.summary}</p>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-500">{article.source}</span>
                  <div className="flex gap-2">
                    {article.tags?.slice(0, 2).map(tag => (
                      <span
                        key={tag}
                        className="px-2 py-1 text-xs bg-cyan-100 text-cyan-800 rounded-full"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ResourceCategory;