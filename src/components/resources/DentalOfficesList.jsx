import React from 'react';
import { motion } from 'framer-motion';
import { Card } from '../common';

const DentalOfficesList = ({ offices, selectedOffice, onOfficeSelect }) => {
  if (!offices || offices.length === 0) {
    return (
      <Card className="p-6 text-center">
        <p className="text-white/80">No dental offices found. Try adjusting your search criteria.</p>
      </Card>
    );
  }

  const renderStarRating = (rating) => {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;
    const stars = [];

    // Add full stars
    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <svg key={`full-${i}`} className="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
        </svg>
      );
    }

    // Add half star if needed
    if (hasHalfStar) {
      stars.push(
        <svg key="half" className="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
          <defs>
            <linearGradient id="halfGradient">
              <stop offset="50%" stopColor="currentColor" />
              <stop offset="50%" stopColor="#4B5563" />
            </linearGradient>
          </defs>
          <path fill="url(#halfGradient)" d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
        </svg>
      );
    }

    // Add empty stars to always have 5 stars
    const emptyStars = 5 - stars.length;
    for (let i = 0; i < emptyStars; i++) {
      stars.push(
        <svg key={`empty-${i}`} className="w-4 h-4 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
        </svg>
      );
    }

    return stars;
  };

  return (
    <motion.div 
      className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ staggerChildren: 0.1 }}
    >
      {offices.map((office) => (
        <motion.div
          key={office.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          whileHover={{ scale: 1.02 }}
          transition={{ duration: 0.2 }}
        >
          <Card 
            className={`h-full cursor-pointer transition-all duration-200 hover:bg-indigo-900/30 hover:border-indigo-500/40 ${
              selectedOffice?.id === office.id ? 'bg-indigo-900/30 border-indigo-500/40' : ''
            }`}
            onClick={() => onOfficeSelect(office)}
          >
            <div className="p-4">
              <h3 className="text-lg font-semibold text-white">{office.name}</h3>
              
              <div className="flex items-center mt-2">
                <div className="flex mr-2">
                  {renderStarRating(office.rating)}
                </div>
                <span className="text-white/60 text-sm">
                  {office.rating} ({office.totalRatings} reviews)
                </span>
              </div>
              
              <p className="text-white/80 mt-2">{office.address}</p>
              
              {office.officeType && (
                <div className="mt-2 text-blue-300 text-sm">
                  {office.officeType}
                </div>
              )}
              
              {office.highlights && office.highlights.length > 0 && (
                <div className="mt-3 flex flex-wrap gap-2">
                  {office.highlights.map((highlight, index) => (
                    <span 
                      key={index} 
                      className="bg-indigo-800/50 text-indigo-200 text-xs px-2 py-1 rounded"
                    >
                      {highlight}
                    </span>
                  ))}
                </div>
              )}
              
              <div className="mt-4 flex justify-between items-center">
                <div className="text-white/60 text-sm">
                  {office.phone}
                </div>
                <button 
                  className="bg-indigo-600 hover:bg-indigo-700 text-white text-sm px-3 py-1 rounded-md"
                  onClick={(e) => {
                    e.stopPropagation();
                    window.open(`tel:${office.phone.replace(/[()\s-]/g, '')}`);
                  }}
                >
                  Call
                </button>
              </div>
            </div>
          </Card>
        </motion.div>
      ))}
    </motion.div>
  );
};

export default DentalOfficesList; 