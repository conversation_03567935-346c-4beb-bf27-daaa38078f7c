import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import SeoArticleAdModifier from '../affiliate/SeoArticleAdModifier';
import remarkGfm from 'remark-gfm';

// Function to fetch article content from markdown files
const fetchArticleContent = async (articleId) => {
  try {
    // Attempt to fetch the markdown file
    const response = await fetch(`/content/seo/${articleId}.md`);

    // Check if the file was found
    if (!response.ok) {
      console.error(`Article not found: ${articleId}`);
      return `# Article Not Found

We couldn't find the article you're looking for. Please check the URL or return to the resources page.`;
    }

    // Return the markdown content
    return await response.text();
  } catch (error) {
    console.error('Error fetching article content:', error);
    throw error;
  }
};

const SeoArticleView = () => {
  const { articleId } = useParams();
  const navigate = useNavigate();
  const [content, setContent] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const loadArticle = async () => {
      try {
        setLoading(true);
        const articleContent = await fetchArticleContent(articleId);
        setContent(articleContent);

        // Find the matching resource to use its title and description
        const matchingResource = window.SEO_RESOURCES?.find(r => r.id === articleId);

        if (matchingResource) {
          // Set document title
          document.title = `${matchingResource.title} | Smilo Dental`;

          // Set canonical URL
          let canonicalLink = document.querySelector('link[rel="canonical"]');
          if (!canonicalLink) {
            canonicalLink = document.createElement('link');
            canonicalLink.rel = 'canonical';
            document.head.appendChild(canonicalLink);
          }
          canonicalLink.href = window.location.origin + matchingResource.route;

          // Set meta description
          let metaDescription = document.querySelector('meta[name="description"]');
          if (!metaDescription) {
            metaDescription = document.createElement('meta');
            metaDescription.name = 'description';
            document.head.appendChild(metaDescription);
          }
          metaDescription.content = matchingResource.description;

          // Set Open Graph tags
          updateMetaTag('og:title', matchingResource.title);
          updateMetaTag('og:description', matchingResource.description);
          updateMetaTag('og:type', 'article');
          updateMetaTag('og:url', window.location.origin + matchingResource.route);

          // Set Twitter Card tags
          updateMetaTag('twitter:card', 'summary_large_image');
          updateMetaTag('twitter:title', matchingResource.title);
          updateMetaTag('twitter:description', matchingResource.description);

          // Add keywords meta tag
          if (matchingResource.keywords) {
            let metaKeywords = document.querySelector('meta[name="keywords"]');
            if (!metaKeywords) {
              metaKeywords = document.createElement('meta');
              metaKeywords.name = 'keywords';
              document.head.appendChild(metaKeywords);
            }
            metaKeywords.content = matchingResource.keywords;
          }

          // Add structured data (JSON-LD)
          let scriptTag = document.querySelector('#article-jsonld');
          if (!scriptTag) {
            scriptTag = document.createElement('script');
            scriptTag.id = 'article-jsonld';
            scriptTag.type = 'application/ld+json';
            document.head.appendChild(scriptTag);
          }

          const structuredData = {
            '@context': 'https://schema.org',
            '@type': 'Article',
            'headline': matchingResource.title,
            'description': matchingResource.description,
            'author': {
              '@type': 'Organization',
              'name': 'Smilo Dental'
            },
            'publisher': {
              '@type': 'Organization',
              'name': 'Smilo Dental',
              'logo': {
                '@type': 'ImageObject',
                'url': window.location.origin + '/images/smilo-logo1.jpg'
              }
            },
            'mainEntityOfPage': {
              '@type': 'WebPage',
              '@id': window.location.origin + matchingResource.route
            },
            'keywords': matchingResource.keywords,
            'datePublished': new Date().toISOString(),
            'dateModified': new Date().toISOString()
          };

          scriptTag.textContent = JSON.stringify(structuredData);
        } else {
          // Fallback if resource not found
          document.title = `${articleId.split('-').map(word =>
            word.charAt(0).toUpperCase() + word.slice(1)).join(' ')} | Smilo Dental`;
        }

        setError(null);
      } catch (err) {
        setError('Failed to load article content');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    // Helper function to update or create meta tags
    const updateMetaTag = (name, content) => {
      let metaTag = document.querySelector(`meta[property="${name}"]`);
      if (!metaTag) {
        metaTag = document.createElement('meta');
        metaTag.setAttribute('property', name);
        document.head.appendChild(metaTag);
      }
      metaTag.content = content;
    };

    loadArticle();

    // Clean up function
    return () => {
      // Reset title on unmount
      document.title = 'Smilo Dental';

      // Remove meta tags we added
      const metaDescription = document.querySelector('meta[name="description"]');
      if (metaDescription) {
        metaDescription.content = 'Smilo Dental Assistant - Your personalized guide to oral health';
      }

      // Remove canonical link
      const canonicalLink = document.querySelector('link[rel="canonical"]');
      if (canonicalLink) {
        canonicalLink.remove();
      }

      // Remove JSON-LD script
      const scriptTag = document.querySelector('#article-jsonld');
      if (scriptTag) {
        scriptTag.remove();
      }

      // Remove Open Graph and Twitter tags
      document.querySelectorAll('meta[property^="og:"], meta[property^="twitter:"]').forEach(tag => {
        tag.remove();
      });
    };
  }, [articleId]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-gray-900 to-black py-12 px-4">
        <div className="max-w-4xl mx-auto flex items-center justify-center py-32">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-gray-900 to-black py-12 px-4">
        <div className="max-w-4xl mx-auto py-16">
          <div className="bg-red-900/30 border border-red-500/30 rounded-lg p-6 text-center">
            <h2 className="text-2xl font-bold text-red-300 mb-4">Error Loading Article</h2>
            <p className="text-red-200 mb-6">{error}</p>
            <button
              onClick={() => navigate('/resources/seo')}
              className="px-4 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded-lg"
            >
              Back to Articles
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-900 to-black py-12 px-4">
      <div className="max-w-4xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-8"
        >
          <button
            onClick={() => navigate('/resources/seo')}
            className="inline-flex items-center text-blue-400 hover:text-blue-300 mb-8"
          >
            <svg
              className="mr-2 h-5 w-5"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Back to Resources
          </button>
        </motion.div>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-xl p-8 shadow-xl"
        >
          <SeoArticleAdModifier content={content} />
        </motion.div>

        <div className="mt-10 flex justify-between items-center border-t border-gray-700 pt-6">
          <p className="text-gray-400 text-sm">
            Published in the Smilo Dental SEO Resources
          </p>
          <button
            onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
            className="text-blue-400 hover:text-blue-300 flex items-center text-sm"
          >
            Back to top
            <svg
              className="ml-2 h-4 w-4"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
};

export default SeoArticleView;