import React, { useState, useEffect } from 'react';
import Button from '../common/Button';
import { useLocationPrompt } from '../../lib/hooks/useLocationPrompt';
import SearchByZip from './SearchByZip';
import { useAuth } from '../../contexts/AuthContext';
import { trackLocationActivity } from '../../lib/services/userActivityService';

export default function LocationSearch({ onLocationFound, loading: externalLoading, onBack }) {
  const [showZipSearch, setShowZipSearch] = useState(false);
  const { requestLocation, loading: locationLoading, error } = useLocationPrompt();
  const { user } = useAuth();
  const [recentSearches, setRecentSearches] = useState([]);

  // Fetch recent location searches if user is logged in
  useEffect(() => {
    const fetchRecentSearches = async () => {
      if (user) {
        try {
          // Fetch recent location searches from localStorage as a simple implementation
          const savedSearches = localStorage.getItem(`location_searches_${user.id}`);
          if (savedSearches) {
            setRecentSearches(JSON.parse(savedSearches).slice(0, 3)); // Show up to 3 recent searches
          }
        } catch (error) {
          console.error('Error fetching recent searches:', error);
        }
      }
    };

    fetchRecentSearches();
  }, [user]);

  // Save search to localStorage and track in user activity
  const saveSearch = (coords, method) => {
    if (user) {
      try {
        // Track this activity
        trackLocationActivity(coords, method).catch(err => 
          console.error('Error tracking location activity:', err)
        );
        
        // Save to localStorage for quick access
        const searchItem = {
          coords,
          method,
          timestamp: new Date().toISOString(),
          label: method === 'zip' ? `ZIP: ${coords.zipCode}` : 'Current Location'
        };
        
        const savedSearches = localStorage.getItem(`location_searches_${user.id}`);
        const searches = savedSearches ? JSON.parse(savedSearches) : [];
        
        // Add new search to the beginning and keep only the last 10
        const updatedSearches = [
          searchItem,
          ...searches.filter(item => 
            !(item.coords.latitude === coords.latitude && 
              item.coords.longitude === coords.longitude)
          )
        ].slice(0, 10);
        
        localStorage.setItem(`location_searches_${user.id}`, JSON.stringify(updatedSearches));
      } catch (error) {
        console.error('Error saving search:', error);
      }
    }
  };

  const handleLocationRequest = async () => {
    console.log('Location request initiated in LocationSearch');
    const coords = await requestLocation();
    if (coords) {
      console.log('Calling onLocationFound with coords:', coords);
      // Save this search
      saveSearch(coords, 'gps');
      
      onLocationFound(coords);
    }
  };

  const handleZipSearch = (coords) => {
    console.log('ZIP search returned coords:', coords);
    // Save this search
    saveSearch(coords, 'zip');
    
    onLocationFound(coords);
    setShowZipSearch(false);
  };

  const handleRecentSearchClick = (search) => {
    onLocationFound(search.coords);
  };

  return (
    <div className="bg-gradient-to-b from-gray-800/50 to-gray-900/50 rounded-xl border border-white/10 shadow-lg p-6 space-y-6">
      <button
        onClick={onBack}
        className="text-white/60 hover:text-white flex items-center gap-2 mb-4 transition-colors"
      >
        <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
        </svg>
        Back to options
      </button>

      <h2 className="text-xl font-bold text-white">Find Dental Schools Near You</h2>
      <p className="text-white/70">Share your location to discover dental schools in your area</p>

      <div className="flex flex-col sm:flex-row gap-4">
        <Button
          onClick={handleLocationRequest}
          disabled={locationLoading || externalLoading}
          className="flex-1 bg-blue-600 hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"
        >
          {locationLoading ? (
            <>
              <div className="animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent"></div>
              <span>Getting Location...</span>
            </>
          ) : (
            <>
              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              <span>Use My Current Location</span>
            </>
          )}
        </Button>
        
        <Button
          variant="secondary"
          onClick={() => setShowZipSearch(true)}
          disabled={externalLoading}
          className="flex-1 border border-white/20 hover:border-white/40 transition-colors flex items-center justify-center gap-2"
        >
          <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
          </svg>
          <span>Enter ZIP Code</span>
        </Button>
      </div>

      {error && (
        <div className="p-4 bg-red-500/10 border border-red-500/20 rounded-lg text-red-400 text-sm flex items-start">
          <svg className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>{error}</span>
        </div>
      )}

      {/* Recent searches section */}
      {user && recentSearches.length > 0 && (
        <div className="mt-6">
          <h3 className="text-sm font-medium text-white/70 mb-2">Recent Searches</h3>
          <div className="space-y-2">
            {recentSearches.map((search, index) => (
              <button
                key={index}
                onClick={() => handleRecentSearchClick(search)}
                className="w-full p-3 bg-white/5 hover:bg-white/10 rounded-lg flex items-center text-left transition-colors"
              >
                <div className="h-8 w-8 rounded-full bg-blue-500/20 flex items-center justify-center mr-3">
                  <svg className="h-4 w-4 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  </svg>
                </div>
                <div>
                  <div className="text-white font-medium">{search.label}</div>
                  <div className="text-white/50 text-xs">
                    {new Date(search.timestamp).toLocaleDateString()}
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>
      )}

      {showZipSearch && (
        <div className="mt-4 bg-gray-800/50 border border-white/10 rounded-lg p-4">
          <SearchByZip 
            onSearch={handleZipSearch}
            onClose={() => setShowZipSearch(false)}
          />
        </div>
      )}
    </div>
  );
}