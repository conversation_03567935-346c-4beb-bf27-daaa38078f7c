import React from 'react';
import { formatDistanceToNow } from 'date-fns';
import { useArticles } from '../../lib/hooks/useArticles';
import ArticleCard from './ArticleCard';
import LoadingSpinner from '../common/LoadingSpinner';

export default function DynamicArticleList() {
  const { articles, loading, error, lastUpdated, refresh } = useArticles();

  if (loading) return <LoadingSpinner />;
  
  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-400">{error}</p>
        <button
          onClick={refresh}
          className="mt-4 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-400"
        >
          Try Again
        </button>
      </div>
    );
  }

  if (!articles.length) {
    return (
      <div className="text-center py-8">
        <p className="text-white/80">No articles available at the moment.</p>
        <button
          onClick={refresh}
          className="mt-4 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-400"
        >
          Refresh Articles
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-white">Latest Dental Research & News</h2>
        {lastUpdated && (
          <div className="flex items-center gap-4">
            <p className="text-sm text-white/60">
              Last updated {formatDistanceToNow(new Date(lastUpdated))} ago
            </p>
            <button
              onClick={refresh}
              className="text-blue-400 hover:text-blue-300"
            >
              <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </button>
          </div>
        )}
      </div>
      
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {articles.map((article, index) => (
          <ArticleCard 
            key={article.link || index} 
            article={article}
          />
        ))}
      </div>
    </div>
  );
}