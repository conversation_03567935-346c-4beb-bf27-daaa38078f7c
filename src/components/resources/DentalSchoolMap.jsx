import React, { useState, useEffect, useRef } from 'react';
import { Card } from '../common';
import { config } from '../../lib/config';
import {
  fetchDentalSchoolReviews,
  getNearbyDentalSchools,
  searchDentalSchools,
  getDentalSchoolPhotos
} from '../../lib/services/googleMapsReviewService';
import { loadGoogleMapsScript } from '../../lib/services/googleMapsService';

const DEFAULT_CENTER = { lat: 39.8283, lng: -98.5795 }; // Center of US
const DEFAULT_ZOOM = 4;
const RADIUS_OPTIONS = [
  { value: 5, label: '5 miles' },
  { value: 10, label: '10 miles' },
  { value: 25, label: '25 miles' },
  { value: 50, label: '50 miles' },
  { value: 100, label: '100 miles' }
];

export default function DentalSchoolMap({ schools: initialSchools, userLocation, onSchoolSelect, onError }) {
  const mapRef = useRef(null);
  const [map, setMap] = useState(null);
  const [markers, setMarkers] = useState([]);
  const [selectedSchool, setSelectedSchool] = useState(null);
  const [schoolReviews, setSchoolReviews] = useState(null);
  const [schoolPhotos, setSchoolPhotos] = useState([]);
  const [mapError, setMapError] = useState(null);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchRadius, setSearchRadius] = useState(25); // Default 25 miles
  const [schools, setSchools] = useState(initialSchools || []);
  const [loadingReviews, setLoadingReviews] = useState(false);
  const [loadingSchools, setLoadingSchools] = useState(false);

  // Initialize map
  useEffect(() => {
    if (!mapRef.current) {
      console.error("Map container reference is null");
      return;
    }

    let mounted = true;

    const initMap = async () => {
      console.log("Initializing map with element:", mapRef.current);

      try {
        // Use the loadGoogleMapsScript utility instead of direct script insertion
        console.log("Loading Google Maps API using API key:", config.google.apiKey ? "Available" : "Missing");
        const google = await loadGoogleMapsScript();

        if (!mounted) return;

        console.log("Google Maps script loaded successfully, creating map instance");

        // Create map with appropriate options
        const mapInstance = new google.maps.Map(mapRef.current, {
          center: userLocation?.latitude && userLocation?.longitude
            ? { lat: userLocation.latitude, lng: userLocation.longitude }
            : DEFAULT_CENTER,
          zoom: userLocation ? 10 : DEFAULT_ZOOM,
          disableDefaultUI: false,
          zoomControl: true,
          mapTypeControl: false,
          scaleControl: true,
          streetViewControl: false,
          rotateControl: false,
          fullscreenControl: true,
          gestureHandling: 'cooperative',
          backgroundColor: '#242f3e',
          styles: [
            {
              featureType: 'all',
              elementType: 'geometry',
              stylers: [{ color: '#242f3e' }]
            },
            {
              featureType: 'all',
              elementType: 'labels.text.stroke',
              stylers: [{ color: '#242f3e' }]
            },
            {
              featureType: 'all',
              elementType: 'labels.text.fill',
              stylers: [{ color: '#746855' }]
            }
          ]
        });

        console.log("Map instance created successfully:", mapInstance);

        // Add search radius circle if user location is available
        if (userLocation?.latitude && userLocation?.longitude) {
          new google.maps.Circle({
            strokeColor: '#4285F4',
            strokeOpacity: 0.5,
            strokeWeight: 1,
            fillColor: '#4285F4',
            fillOpacity: 0.1,
            map: mapInstance,
            center: { lat: userLocation.latitude, lng: userLocation.longitude },
            radius: searchRadius * 1609.34 // Convert miles to meters
          });
        }

        setMap(mapInstance);
        setMapError(null);

        // If no initial schools, fetch nearby schools
        if ((!initialSchools || initialSchools.length === 0) && userLocation) {
          fetchNearbySchools(userLocation, searchRadius);
        }

        setLoading(false);
      } catch (error) {
        console.error('Error initializing map:', error);
        handleMapError(error);
      }
    };

    function handleMapError(error) {
      console.error('Error initializing map:', error);

      // Check if the error is related to the API key
      if (error.message && (error.message.includes('API key') || error.message.includes('apiKey'))) {
        setMapError(`Google Maps API key issue: The API key is missing, invalid, or has restrictions. Please check your configuration.`);
      } else {
        setMapError(`Map view is temporarily unavailable: ${error.message}. Showing list view instead.`);
      }

      setLoading(false);

      // Call the onError prop if provided
      if (onError) {
        onError(error);
      }
    }

    // Small timeout to ensure DOM is fully ready before initializing map
    setTimeout(() => {
      if (mounted) initMap();
    }, 100);

    return () => {
      mounted = false;
      // Clean up markers
      markers.forEach(marker => marker?.setMap(null));
    };
  }, [mapRef, userLocation, searchRadius, initialSchools, onError]);

  // Handle search radius change
  useEffect(() => {
    if (!map || !userLocation) return;

    // Update circle radius
    const circles = map.overlays?.filter(overlay => overlay instanceof google.maps.Circle) || [];
    if (circles.length > 0) {
      circles[0].setRadius(searchRadius * 1609.34); // Convert miles to meters
    } else {
      new google.maps.Circle({
        strokeColor: '#4285F4',
        strokeOpacity: 0.5,
        strokeWeight: 1,
        fillColor: '#4285F4',
        fillOpacity: 0.1,
        map: map,
        center: { lat: userLocation.latitude, lng: userLocation.longitude },
        radius: searchRadius * 1609.34 // Convert miles to meters
      });
    }

    // Fetch schools with new radius
    if (userLocation) {
      fetchNearbySchools(userLocation, searchRadius);
    }
  }, [searchRadius, map, userLocation]);

  // Update markers when schools change
  useEffect(() => {
    if (!map || !schools?.length) return;

    // Clear existing markers
    markers.forEach(marker => marker?.setMap(null));

    const newMarkers = schools.map((school) => {
      if (!school.coordinates?.latitude || !school.coordinates?.longitude) {
        console.warn('Invalid coordinates for school:', school.name);
        return null;
      }

      const marker = new google.maps.Marker({
        position: {
          lat: school.coordinates.latitude,
          lng: school.coordinates.longitude
        },
        map: map,
        title: school.name,
        animation: google.maps.Animation.DROP,
        icon: {
          path: google.maps.SymbolPath.CIRCLE,
          fillColor: '#4285F4',
          fillOpacity: 0.9,
          strokeWeight: 1,
          strokeColor: '#ffffff',
          scale: 10
        }
      });

      // Add info window with basic info
      const infoWindow = new google.maps.InfoWindow({
        content: `
          <div style="padding: 8px;">
            <h3 style="font-weight: bold; margin-bottom: 4px;">${school.name}</h3>
            <p style="margin: 0;">${school.address || ''}</p>
            ${school.rating ? `
              <div style="display: flex; align-items: center; margin-top: 4px;">
                <span style="color: #F5B800; margin-right: 4px;">★</span>
                <span>${school.rating}</span>
                <span style="margin-left: 4px; color: #777;">(${school.totalRatings || 0})</span>
              </div>
            ` : ''}
            ${school.distance ? `<p style="margin: 4px 0 0; color: #4285F4;">${school.distance} miles away</p>` : ''}
          </div>
        `
      });

      marker.addListener('click', () => {
        // Close any open info windows
        markers.forEach(m => {
          if (m && m.infoWindow && m.infoWindow.getMap()) {
            m.infoWindow.close();
          }
        });

        // Open this info window
        infoWindow.open(map, marker);

        // Set selected school and fetch details
        handleSchoolSelect(school);
      });

      // Store the info window with the marker for later access
      marker.infoWindow = infoWindow;

      return marker;
    });

    setMarkers(newMarkers.filter(Boolean));

    // Fit bounds to show all markers
    if (newMarkers.length > 0) {
      const bounds = new google.maps.LatLngBounds();
      newMarkers.forEach(marker => {
        if (marker && marker.getPosition()) {
          bounds.extend(marker.getPosition());
        }
      });
      map.fitBounds(bounds);

      // Don't zoom in too far on small datasets
      const listener = google.maps.event.addListener(map, 'idle', () => {
        if (map.getZoom() > 16) map.setZoom(16);
        google.maps.event.removeListener(listener);
      });
    }

    return () => {
      newMarkers.forEach(marker => marker?.setMap(null));
    };
  }, [map, schools]);

  // Function to fetch nearby schools
  const fetchNearbySchools = async (location, radius) => {
    if (!location) return;

    setLoadingSchools(true);

    try {
      const nearbySchools = await getNearbyDentalSchools(location, radius * 1609.34); // Convert miles to meters

      if (nearbySchools && nearbySchools.length > 0) {
        setSchools(nearbySchools);
      } else {
        console.log('No nearby schools found');
      }
    } catch (error) {
      console.error('Error fetching nearby schools:', error);
      setMapError('Failed to fetch nearby dental schools. Please try again.');
    } finally {
      setLoadingSchools(false);
    }
  };

  // Function to handle school selection
  const handleSchoolSelect = async (school) => {
    setSelectedSchool(school);

    if (onSchoolSelect) {
      onSchoolSelect(school);
    }

    // Fetch reviews and photos
    setLoadingReviews(true);

    try {
      // Fetch reviews
      const reviewsData = await fetchDentalSchoolReviews(
        school.coordinates,
        school.name
      );

      setSchoolReviews(reviewsData);

      // Fetch photos if we have a place ID
      if (school.id) {
        const photos = await getDentalSchoolPhotos(school.id);
        setSchoolPhotos(photos);
      }
    } catch (error) {
      console.error('Error fetching school details:', error);
    } finally {
      setLoadingReviews(false);
    }
  };

  // Function to handle search
  const handleSearch = async (e) => {
    e.preventDefault();

    if (!searchQuery.trim()) return;

    setLoadingSchools(true);

    try {
      const results = await searchDentalSchools(searchQuery);

      if (results && results.length > 0) {
        setSchools(results);
      } else {
        setMapError(`No dental schools found for "${searchQuery}". Please try a different search.`);
      }
    } catch (error) {
      console.error('Error searching dental schools:', error);
      setMapError('Search failed. Please try again.');
    } finally {
      setLoadingSchools(false);
    }
  };

  // Render star rating
  const renderStarRating = (rating, size = 'text-sm') => {
    return (
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <span
            key={star}
            className={`${size} ${
              star <= Math.round(rating)
                ? 'text-yellow-400'
                : 'text-gray-500/30'
            }`}
          >
            ★
          </span>
        ))}
        <span className="ml-2 text-white/70">{rating}</span>
      </div>
    );
  };

  if (loading) {
    return (
      <Card>
        <div className="flex justify-center items-center h-[400px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        </div>
      </Card>
    );
  }

  return (
    <div className="h-full">
      {/* Map container */}
      <div className="relative h-[400px] rounded-t-xl overflow-hidden border border-white/10">
        {loading ? (
          <div className="flex flex-col justify-center items-center h-full bg-indigo-950/50">
            <div className="h-10 w-10 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mb-4"></div>
            <p className="text-white/70">Loading map...</p>
          </div>
        ) : mapError ? (
          <div className="flex flex-col justify-center items-center h-full bg-indigo-950/50 p-6 text-center">
            <div className="text-red-400 mb-4">
              <svg className="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
            </div>
            <h3 className="text-xl font-medium text-white mb-2">Map Error</h3>
            <p className="text-white/70 mb-4">{mapError}</p>
            <div className="space-y-3 max-w-md">
              <p className="text-white/60 text-sm">This might be happening because:</p>
              <ul className="text-sm text-white/60 list-disc list-inside text-left">
                <li>Google Maps service is experiencing issues</li>
                <li>There might be network connectivity problems</li>
                <li>The API key for Google Maps might be missing, restricted, or invalid</li>
                <li>Your browser might be blocking the Google Maps service</li>
              </ul>
              <div className="pt-2">
                <button
                  onClick={() => window.location.reload()}
                  className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors"
                >
                  Refresh Page
                </button>
              </div>
            </div>
          </div>
        ) : (
          // Map container
          <div ref={mapRef} className="h-full w-full" />
        )}
      </div>

      {/* Controls container */}
      <div className="bg-indigo-950/50 p-4 rounded-b-xl border border-white/10 border-t-0">
        {/* Search form */}
        <form onSubmit={handleSearch} className="flex mb-4">
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search for dental schools..."
            className="flex-1 bg-white/10 border border-white/20 text-white rounded-l-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <button
            type="submit"
            className="bg-blue-600 hover:bg-blue-700 text-white rounded-r-md px-4 transition-colors"
          >
            Search
          </button>
        </form>

        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="flex items-center">
            <label className="text-white/70 mr-2 text-sm whitespace-nowrap">Search Radius:</label>
            <select
              value={searchRadius}
              onChange={(e) => setSearchRadius(Number(e.target.value))}
              className="bg-white/10 border border-white/20 rounded-md text-white p-2 focus:outline-none focus:ring-2 focus:ring-blue-500/50"
              disabled={!userLocation}
            >
              {RADIUS_OPTIONS.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {userLocation ? (
            <div className="flex items-center">
              <span className="text-sm text-white/50">
                Showing dental schools within {searchRadius} miles of your location
              </span>
            </div>
          ) : (
            <button
              onClick={() => {
                if (navigator.geolocation) {
                  navigator.geolocation.getCurrentPosition(
                    (position) => {
                      const location = {
                        latitude: position.coords.latitude,
                        longitude: position.coords.longitude
                      };
                      // Use the parent component's handler to set user location
                      if (onSchoolSelect) {
                        onSchoolSelect({ userLocation: location });
                      }
                      // Also set it locally for immediate use
                      fetchNearbySchools(location, searchRadius);
                    },
                    (error) => {
                      console.error('Error getting location:', error);
                      setMapError('Unable to get your location. Please try searching instead.');
                    }
                  );
                }
              }}
              className="inline-flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              <svg className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              Use My Location
            </button>
          )}
        </div>

        {/* School details panel */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* School details */}
          <div className="lg:col-span-2">
            {selectedSchool ? (
              <Card>
                <div className="space-y-4">
                  <h3 className="text-xl font-bold text-white">{selectedSchool.name}</h3>

                  {/* School rating */}
                  {selectedSchool.rating && (
                    <div className="flex items-center">
                      {renderStarRating(selectedSchool.rating)}
                      {selectedSchool.totalRatings && (
                        <span className="ml-2 text-white/50 text-sm">({selectedSchool.totalRatings} ratings)</span>
                      )}
                    </div>
                  )}

                  {/* School address */}
                  <p className="text-white/80">{selectedSchool.address}</p>

                  {/* Distance */}
                  {selectedSchool.distance && (
                    <p className="text-blue-400">
                      {selectedSchool.distance} miles away
                    </p>
                  )}

                  {/* Phone number from reviews */}
                  {schoolReviews?.placeInfo?.phone && (
                    <p className="text-white/80">
                      <span className="text-white/50 mr-2">Phone:</span>
                      <a href={`tel:${schoolReviews.placeInfo.phone}`} className="text-blue-400 hover:underline">
                        {schoolReviews.placeInfo.phone}
                      </a>
                    </p>
                  )}

                  {/* Website */}
                  {(selectedSchool.website || schoolReviews?.placeInfo?.website) && (
                    <div>
                      <a
                        href={selectedSchool.website || schoolReviews?.placeInfo?.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center text-blue-400 hover:underline"
                      >
                        Visit Website
                        <svg className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                        </svg>
                      </a>
                    </div>
                  )}

                  {/* Loading indicator for reviews */}
                  {loadingReviews && (
                    <div className="flex items-center justify-center py-6">
                      <div className="animate-spin rounded-full h-6 w-6 border-2 border-blue-500 border-t-transparent"></div>
                      <span className="ml-2 text-white/70">Loading reviews...</span>
                    </div>
                  )}

                  {/* Photos */}
                  {schoolPhotos.length > 0 && (
                    <div>
                      <h4 className="text-white font-medium mb-2">Photos</h4>
                      <div className="grid grid-cols-3 gap-2">
                        {schoolPhotos.slice(0, 3).map((photo, index) => (
                          <img
                            key={index}
                            src={photo}
                            alt={`${selectedSchool.name} - Photo ${index + 1}`}
                            className="rounded-md w-full h-24 object-cover"
                          />
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Reviews section */}
                  {schoolReviews && schoolReviews.reviews?.length > 0 && (
                    <div className="mt-4">
                      <h4 className="text-white font-medium mb-2">Reviews</h4>
                      <div className="space-y-4 mt-2 max-h-96 overflow-y-auto pr-2">
                        {schoolReviews.reviews.slice(0, 3).map((review) => (
                          <div key={review.id} className="bg-white/5 p-3 rounded-md">
                            <div className="flex items-start mb-2">
                              {review.avatar && (
                                <img
                                  src={review.avatar}
                                  alt={review.author}
                                  className="w-8 h-8 rounded-full mr-2"
                                />
                              )}
                              <div>
                                <div className="text-white font-medium">{review.author}</div>
                                <div className="text-white/50 text-xs">{review.time}</div>
                              </div>
                            </div>
                            {renderStarRating(review.rating, 'text-xs')}
                            <p className="text-white/80 text-sm mt-2">{review.text}</p>
                          </div>
                        ))}
                      </div>

                      {/* View all reviews link */}
                      {schoolReviews.reviews.length > 3 && (
                        <div className="mt-3 text-center">
                          <button
                            onClick={() => {
                              // Could implement a modal or expanded view here
                              window.open(`https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(selectedSchool.name)}&query_place_id=${selectedSchool.id}`, '_blank');
                            }}
                            className="text-blue-400 hover:underline text-sm"
                          >
                            View all {schoolReviews.reviews.length} reviews
                          </button>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </Card>
            ) : (
              <Card>
                <div className="p-4 text-center text-white/70">
                  <svg className="h-12 w-12 mx-auto mb-2 text-white/30" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                  </svg>
                  <p>Select a dental school on the map to view details and reviews</p>
                </div>
              </Card>
            )}
          </div>

          {/* School list (shown when map has an error) */}
          {mapError && (
            <Card>
              <h3 className="text-xl font-bold mb-4 text-white">Dental Schools</h3>
              <div className="space-y-4">
                {schools.map((school) => (
                  <div
                    key={school.id}
                    className="p-4 bg-white/5 rounded-lg hover:bg-white/10 transition-colors cursor-pointer"
                    onClick={() => handleSchoolSelect(school)}
                  >
                    <h3 className="text-lg font-semibold text-white mb-2">
                      {school.name}
                    </h3>
                    <p className="text-white/80">{school.address}</p>
                    {school.rating && (
                      <div className="flex items-center mt-2">
                        {renderStarRating(school.rating)}
                        {school.totalRatings && (
                          <span className="ml-2 text-white/50 text-sm">({school.totalRatings})</span>
                        )}
                      </div>
                    )}
                    {school.distance && (
                      <p className="text-blue-400 mt-1">
                        {school.distance} miles away
                      </p>
                    )}
                  </div>
                ))}
              </div>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}