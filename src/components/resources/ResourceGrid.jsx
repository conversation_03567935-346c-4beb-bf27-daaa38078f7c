import React from 'react';
import { motion } from 'framer-motion';

const ResourceCard = ({ article }) => (
  <motion.div
    className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6"
    whileHover={{ scale: 1.02 }}
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
  >
    <div className="flex justify-between items-start mb-4">
      <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
        {article.title}
      </h3>
      {article.isNew && (
        <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">
          New
        </span>
      )}
    </div>

    <p className="text-gray-600 dark:text-gray-300 mb-4">
      {article.summary}
    </p>

    <div className="flex flex-wrap gap-2 mb-4">
      {article.tags.map(tag => (
        <span
          key={tag}
          className="bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 text-xs font-medium px-2.5 py-0.5 rounded"
        >
          {tag}
        </span>
      ))}
    </div>

    <div className="flex justify-between items-center">
      <span className="text-sm text-gray-500 dark:text-gray-400">
        {article.readingTime} min read
      </span>
      <a
        href={article.link}
        target="_blank"
        rel="noopener noreferrer"
        className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
      >
        Read More →
      </a>
    </div>
  </motion.div>
);

const ResourceList = ({ article }) => (
  <motion.div
    className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 flex items-center gap-4"
    whileHover={{ scale: 1.01 }}
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
  >
    <div className="flex-grow">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-1">
        {article.title}
      </h3>
      <p className="text-gray-600 dark:text-gray-300 text-sm mb-2">
        {article.summary}
      </p>
      <div className="flex gap-2">
        {article.tags.slice(0, 3).map(tag => (
          <span
            key={tag}
            className="bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 text-xs px-2 py-0.5 rounded"
          >
            {tag}
          </span>
        ))}
      </div>
    </div>
    <a
      href={article.link}
      target="_blank"
      rel="noopener noreferrer"
      className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 whitespace-nowrap"
    >
      Read More →
    </a>
  </motion.div>
);

const ResourceGrid = ({ articles, viewMode }) => {
  if (articles.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-600 dark:text-gray-400">
          No resources found matching your criteria.
        </p>
      </div>
    );
  }

  return (
    <div className={
      viewMode === 'grid'
        ? 'grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
        : 'space-y-4'
    }>
      {articles.map(article => (
        viewMode === 'grid'
          ? <ResourceCard key={article.title} article={article} />
          : <ResourceList key={article.title} article={article} />
      ))}
    </div>
  );
};

export default ResourceGrid; 