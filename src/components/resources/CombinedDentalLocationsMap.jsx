import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, TabButton } from '../common';
import DentalSchoolsSection from './DentalSchoolsSection';
import DentalOfficesMap from './DentalOfficesMap';
import { DENTAL_OFFICES } from '../../lib/constants/dentalOffices';

const CombinedDentalLocationsMap = ({ customOffices }) => {
  const [activeTab, setActiveTab] = useState('offices');
  const [selectedOffices, setSelectedOffices] = useState([]);
  
  // Initialize with custom offices or defaults
  useEffect(() => {
    setSelectedOffices(customOffices || DENTAL_OFFICES);
  }, [customOffices]);
  
  // Toggle between dental schools and offices
  const handleTabChange = (tab) => {
    setActiveTab(tab);
  };
  
  return (
    <motion.div 
      className="space-y-6"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Tab Navigation */}
      <Card className="p-4">
        <div className="flex space-x-4">
          <TabButton 
            active={activeTab === 'offices'} 
            onClick={() => handleTabChange('offices')}
          >
            <span className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
              Dental Offices
            </span>
          </TabButton>
          
          <TabButton 
            active={activeTab === 'schools'} 
            onClick={() => handleTabChange('schools')}
          >
            <span className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path d="M12 14l9-5-9-5-9 5 9 5z" />
                <path d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14zm-4 6v-7.5l4-2.222" />
              </svg>
              Dental Schools
            </span>
          </TabButton>
        </div>
      </Card>
      
      {/* Content Area */}
      <div className="min-h-[600px]">
        {activeTab === 'offices' ? (
          <DentalOfficesMap customOffices={selectedOffices} />
        ) : (
          <DentalSchoolsSection />
        )}
      </div>
    </motion.div>
  );
};

export default CombinedDentalLocationsMap; 