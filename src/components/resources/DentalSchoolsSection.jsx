import React, { useState } from 'react';
import { useDentalSchools } from '../../lib/hooks/useDentalSchools';
import LocationOptions from './LocationOptions';
import DentalSchoolList from './DentalSchoolList';
import RadiusSelector from './RadiusSelector';
import MapComponent from '../MapComponent';
import { Card } from '../common';
import { motion } from 'framer-motion';

// Map styles to match the theme
const MAP_STYLES = [
  {
    "featureType": "water",
    "elementType": "geometry",
    "stylers": [
      { "color": "#1e293b" },
      { "lightness": -5 }
    ]
  },
  {
    "featureType": "landscape",
    "elementType": "geometry",
    "stylers": [
      { "color": "#0f172a" }
    ]
  },
  {
    "featureType": "road",
    "elementType": "geometry",
    "stylers": [
      { "color": "#334155" },
      { "lightness": -10 }
    ]
  },
  {
    "featureType": "poi",
    "elementType": "geometry",
    "stylers": [
      { "color": "#1e293b" },
      { "lightness": -15 }
    ]
  },
  {
    "featureType": "administrative",
    "elementType": "geometry.stroke",
    "stylers": [
      { "color": "#6366f1" },
      { "lightness": 15 },
      { "weight": 1.2 }
    ]
  }
];

export default function DentalSchoolsSection() {
  const [searchLocation, setSearchLocation] = useState(null);
  const [selectedSchool, setSelectedSchool] = useState(null);
  const { 
    schools = [], // Provide a default empty array
    loading, 
    error,
    radius,
    setRadius,
    searchSchools
  } = useDentalSchools();

  const handleLocationFound = async (coords) => {
    console.log('Location found:', coords);
    // Convert to both formats needed
    const searchCoords = {
      latitude: parseFloat(coords.lat ?? coords.latitude),
      longitude: parseFloat(coords.lng ?? coords.longitude)
    };
    
    const mapCoords = {
      lat: searchCoords.latitude,
      lng: searchCoords.longitude
    };
    
    if (isNaN(mapCoords.lat) || isNaN(mapCoords.lng)) {
      console.error('Invalid coordinates:', coords);
      return;
    }
    
    console.log('Search coordinates:', searchCoords);
    console.log('Map coordinates:', mapCoords);
    
    setSearchLocation(mapCoords);
    await searchSchools(searchCoords);
  };

  // Convert schools data for map markers, ensuring schools is an array
  const mapSchools = Array.isArray(schools) ? schools.map(school => ({
    ...school,
    lat: parseFloat(school.latitude),
    lng: parseFloat(school.longitude),
    name: school.name,
    address: school.address,
    description: school.clinic_info
  })) : [];

  const handleMarkerClick = (school) => {
    console.log('School selected:', school);
    setSelectedSchool(school);
    // You could add additional logic here like scrolling to the school in the list
  };

  // Only log schools when there are actually schools to show - avoids empty array logs
  if (process.env.NODE_ENV === 'development' && mapSchools.length > 0) {
    console.log('Schools for map:', mapSchools);
  }

  return (
    <motion.div 
      className="space-y-8"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <motion.div 
        className="bg-gradient-to-r from-blue-900/40 to-indigo-900/40 border border-blue-500/20 rounded-xl p-8 shadow-lg"
        initial={{ y: 20 }}
        animate={{ y: 0 }}
        transition={{ type: "spring", stiffness: 50, damping: 10 }}
      >
        <motion.h3 
          className="text-2xl font-semibold text-white mb-4 relative inline-block"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.1, duration: 0.5 }}
        >
          Find Affordable Dental Care Near You
          <motion.div 
            className="absolute -bottom-1 left-0 h-1 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full"
            initial={{ width: 0 }}
            animate={{ width: '100%' }}
            transition={{ delay: 0.5, duration: 0.8 }}
          />
        </motion.h3>
        <motion.p 
          className="text-white/80 mb-6"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3, duration: 0.5 }}
        >
          Search for accredited dental schools offering reduced-cost care through supervised student clinics.
        </motion.p>
        <LocationOptions 
          onLocationFound={handleLocationFound}
          loading={loading}
        />
      </motion.div>

      {searchLocation && !loading && (
        <motion.div 
          className="flex justify-between items-center"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <p className="text-white/80">
            {radius === Infinity 
              ? 'Showing all dental schools'
              : `Showing dental schools within ${radius} miles`}
          </p>
          <RadiusSelector
            value={radius}
            onChange={setRadius}
            disabled={loading}
          />
        </motion.div>
      )}

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2, duration: 0.5 }}
      >
        <Card className="overflow-hidden backdrop-blur-sm">
          <div className="relative">
            {loading ? (
              <div className="flex justify-center items-center h-[400px]">
                <motion.div 
                  className="rounded-full h-10 w-10 border-3 border-blue-500 border-t-transparent"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                />
              </div>
            ) : !searchLocation ? (
              <div className="flex flex-col justify-center items-center h-[400px] relative overflow-hidden">
                {/* Background elements */}
                <motion.div 
                  className="absolute top-1/3 left-1/4 w-32 h-32 bg-blue-500/10 rounded-full blur-xl"
                  animate={{ 
                    scale: [1, 1.2, 1],
                    opacity: [0.3, 0.5, 0.3]
                  }}
                  transition={{ 
                    duration: 8, 
                    repeat: Infinity,
                    ease: "easeInOut" 
                  }}
                />
                <motion.div 
                  className="absolute bottom-1/3 right-1/4 w-40 h-40 bg-indigo-500/10 rounded-full blur-xl"
                  animate={{ 
                    scale: [1, 1.3, 1],
                    opacity: [0.3, 0.5, 0.3]
                  }}
                  transition={{ 
                    duration: 10, 
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 2
                  }}
                />
                
                {/* Animated particles */}
                <div className="absolute inset-0 pointer-events-none">
                  {[...Array(20)].map((_, i) => (
                    <motion.div
                      key={i}
                      className="absolute rounded-full bg-blue-400/30"
                      style={{
                        width: Math.random() * 4 + 2,
                        height: Math.random() * 4 + 2,
                        left: `${Math.random() * 100}%`,
                        top: `${Math.random() * 100}%`,
                      }}
                      animate={{
                        y: [0, -(20 + Math.random() * 30)],
                        x: [0, (Math.random() - 0.5) * 20],
                        opacity: [0, 0.7, 0],
                        scale: [0, 1, 0],
                      }}
                      transition={{
                        duration: 3 + Math.random() * 3,
                        repeat: Infinity,
                        delay: Math.random() * 5,
                        ease: "easeInOut",
                      }}
                    />
                  ))}
                </div>
                
                {/* Icon */}
                <motion.div 
                  className="text-4xl mb-6 text-blue-400/80"
                  animate={{ 
                    y: [0, -5, 0],
                    scale: [1, 1.05, 1]
                  }}
                  transition={{ 
                    duration: 3, 
                    repeat: Infinity,
                    ease: "easeInOut" 
                  }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                </motion.div>
                
                {/* Text */}
                <motion.p 
                  className="text-lg text-white/70 text-center max-w-md"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.5, duration: 0.8 }}
                >
                  Enter a location to view dental schools on the map
                </motion.p>
                
                {/* Pulsing indicator to draw attention to action */}
                <motion.div 
                  className="mt-8 bg-gradient-to-r from-blue-500/30 to-indigo-500/30 rounded-full px-6 py-3 text-white/90"
                  animate={{ 
                    boxShadow: [
                      "0 0 0 0 rgba(59, 130, 246, 0)",
                      "0 0 0 15px rgba(59, 130, 246, 0.3)",
                      "0 0 0 0 rgba(59, 130, 246, 0)"
                    ]
                  }}
                  transition={{ 
                    duration: 3, 
                    repeat: Infinity,
                    ease: "easeInOut" 
                  }}
                >
                  <motion.span 
                    className="flex items-center gap-2"
                    whileHover={{ scale: 1.05 }}
                    transition={{ duration: 0.2 }}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                    </svg>
                    Click the options above to search
                  </motion.span>
                </motion.div>
              </div>
            ) : (
              <div className="h-[500px]">
                <MapComponent 
                  locations={mapSchools}
                  center={searchLocation}
                  zoom={10}
                  height="500px"
                  onMarkerClick={handleMarkerClick}
                  customStyles={MAP_STYLES}
                />
              </div>
            )}
          </div>
        </Card>
      </motion.div>

      {searchLocation && !loading && schools.length > 0 && (
        <DentalSchoolList 
          schools={schools} 
          selectedSchool={selectedSchool}
          onSchoolSelect={setSelectedSchool}
        />
      )}

      {searchLocation && !loading && schools.length === 0 && (
        <motion.div
          className="bg-blue-900/20 border border-blue-500/20 rounded-xl p-6 text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.5 }}
        >
          <p className="text-white/80">No dental schools found within {radius} miles of your location.</p>
          <p className="text-white/60 mt-2">Try increasing the search radius or searching in a different location.</p>
        </motion.div>
      )}
    </motion.div>
  );
}