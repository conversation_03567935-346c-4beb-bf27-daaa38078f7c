import React, { useState } from 'react';
import { useLocationPrompt } from '../../lib/hooks/useLocationPrompt';
import { motion, AnimatePresence } from 'framer-motion';

export default function LocationOptions({ onLocationFound, loading }) {
  const [showZipInput, setShowZipInput] = useState(false);
  const [zipCode, setZipCode] = useState('');
  const { requestLocation, loading: locationLoading, error } = useLocationPrompt();

  const handleLocationRequest = async () => {
    const coords = await requestLocation();
    if (coords) {
      onLocationFound(coords);
    }
  };

  const handleZipSubmit = async (e) => {
    e.preventDefault();
    if (!zipCode.trim() || loading) return;

    try {
      const response = await fetch(
        `https://maps.googleapis.com/maps/api/geocode/json?address=${zipCode}&key=${import.meta.env.VITE_GOOGLE_MAPS_API_KEY}`
      );
      const data = await response.json();

      if (data.results && data.results[0]) {
        const { lat, lng } = data.results[0].geometry.location;
        onLocationFound({ latitude: lat, longitude: lng });
        setZipCode('');
        setShowZipInput(false);
      }
    } catch (err) {
      console.error('Error geocoding ZIP code:', err);
    }
  };

  return (
    <div className="space-y-6">
      <div className="relative bg-gradient-to-br from-blue-900/20 to-purple-900/20 rounded-xl p-6 backdrop-blur-sm border border-white/10 shadow-xl">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5 rounded-xl"></div>
        
        <div className="relative space-y-6">
          {/* Location Options */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <motion.button
              onClick={handleLocationRequest}
              disabled={locationLoading || loading}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className={`
                group relative overflow-hidden rounded-xl p-4
                transition-all duration-300 ease-out
                ${locationLoading || loading
                  ? 'bg-blue-500/30 cursor-not-allowed'
                  : 'bg-gradient-to-br from-blue-500 to-blue-600 hover:from-blue-400 hover:to-blue-500'
                }
              `}
            >
              <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity"></div>
              <div className="flex items-center gap-4">
                <div className="p-3 bg-white/10 rounded-lg">
                  <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                </div>
                <div className="flex-1">
                  <div className="font-medium text-lg">
                    {locationLoading ? 'Getting Location...' : 'Use My Location'}
                  </div>
                  <div className="text-sm text-white/60">
                    Quick and accurate location detection
                  </div>
                </div>
                {!locationLoading && (
                  <div className="hidden sm:block px-3 py-1 rounded-full bg-white/10 text-sm">
                    Recommended
                  </div>
                )}
              </div>
            </motion.button>

            <motion.button
              onClick={() => setShowZipInput(true)}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className={`
                group relative overflow-hidden rounded-xl p-4
                transition-all duration-300 ease-out
                ${loading
                  ? 'bg-white/5 cursor-not-allowed'
                  : 'bg-white/10 hover:bg-white/15'
                }
              `}
            >
              <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity"></div>
              <div className="flex items-center gap-4">
                <div className="p-3 bg-white/10 rounded-lg">
                  <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </div>
                <div className="flex-1">
                  <div className="font-medium text-lg">Enter ZIP Code</div>
                  <div className="text-sm text-white/60">
                    Search by postal code
                  </div>
                </div>
              </div>
            </motion.button>
          </div>

          {/* Error Message */}
          <AnimatePresence>
            {error && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="p-4 bg-red-500/10 border border-red-500/20 rounded-lg"
              >
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-red-500/20 rounded-lg">
                    <svg className="w-5 h-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div className="flex-1 text-red-400 text-sm">{error}</div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* ZIP Input */}
          <AnimatePresence>
            {showZipInput && (
              <motion.form
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                onSubmit={handleZipSubmit}
                className="overflow-hidden"
              >
                <div className="flex gap-3">
                  <div className="relative flex-1">
                    <input
                      type="text"
                      value={zipCode}
                      onChange={(e) => setZipCode(e.target.value.replace(/\D/g, '').slice(0, 5))}
                      placeholder="Enter ZIP code"
                      className="
                        w-full px-4 py-3 rounded-lg
                        bg-white/10 border border-white/20
                        text-white placeholder-white/40
                        focus:outline-none focus:ring-2 focus:ring-blue-500/50
                        transition-all duration-200
                      "
                      disabled={loading}
                    />
                    {zipCode && (
                      <motion.button
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        type="button"
                        onClick={() => setZipCode('')}
                        className="absolute right-3 top-1/2 -translate-y-1/2 p-1 hover:bg-white/10 rounded-full transition-colors"
                      >
                        <svg className="w-5 h-5 text-white/60" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </motion.button>
                    )}
                  </div>
                  <motion.button
                    type="submit"
                    disabled={!zipCode.trim() || loading}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className={`
                      px-6 py-3 rounded-lg font-medium
                      transition-all duration-200
                      ${!zipCode.trim() || loading
                        ? 'bg-blue-500/30 cursor-not-allowed'
                        : 'bg-blue-500 hover:bg-blue-400'
                      }
                    `}
                  >
                    Search
                  </motion.button>
                  <motion.button
                    type="button"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => {
                      setShowZipInput(false);
                      setZipCode('');
                    }}
                    className="px-4 py-3 rounded-lg bg-white/10 hover:bg-white/15 transition-colors"
                  >
                    Cancel
                  </motion.button>
                </div>
              </motion.form>
            )}
          </AnimatePresence>
        </div>
      </div>

      <p className="text-center text-sm text-white/60">
        Choose how you'd like to find dental schools in your area
      </p>
    </div>
  );
}