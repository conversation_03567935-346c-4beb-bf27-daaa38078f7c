import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

export default function CategoryFilter({ categories, selectedCategory, onSelect }) {
  const [expanded, setExpanded] = useState(false);
  const initialDisplayCount = 5; // Number of categories to show initially
  
  // Always show "All Categories" button and then the first few categories
  const displayCategories = expanded ? categories : categories.slice(0, initialDisplayCount);
  const hasMoreCategories = categories.length > initialDisplayCount;
  
  return (
    <div className="mb-6">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-lg font-medium text-white">Categories</h3>
        {hasMoreCategories && (
          <button 
            onClick={() => setExpanded(!expanded)}
            className="text-blue-300 hover:text-blue-200 text-sm font-medium flex items-center"
          >
            {expanded ? (
              <>
                <span>Show Less</span>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                </svg>
              </>
            ) : (
              <>
                <span>Show All</span>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </>
            )}
          </button>
        )}
      </div>
      
      <div className="flex flex-wrap gap-2">
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => onSelect(null)}
          className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200
            ${!selectedCategory 
              ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-md' 
              : 'bg-white/5 backdrop-blur-sm text-white/80 hover:bg-white/10 border border-white/10'}`}
        >
          All Categories
        </motion.button>
        
        <AnimatePresence>
          {displayCategories.map((category) => (
            <motion.button
              key={category}
              initial={!expanded ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ duration: 0.2 }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => onSelect(category)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200
                ${selectedCategory === category
                  ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-md'
                  : 'bg-white/5 backdrop-blur-sm text-white/80 hover:bg-white/10 border border-white/10'}`}
            >
              {category}
            </motion.button>
          ))}
        </AnimatePresence>
      </div>
    </div>
  );
}