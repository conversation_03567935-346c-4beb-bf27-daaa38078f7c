import React, { useState } from 'react';
import Button from '../common/Button';
import { getCoordinatesFromZip } from '../../lib/services/geocodingService';

export default function SearchByZip({ onSearch, onClose }) {
  const [zipCode, setZipCode] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    
    if (!zipCode.match(/^\d{5}$/)) {
      setError('Please enter a valid 5-digit ZIP code');
      return;
    }

    setLoading(true);
    try {
      const coords = await getCoordinatesFromZip(zipCode);
      console.log('ZIP search returned coords:', coords);
      coords.zipCode = zipCode;
      onSearch(coords);
    } catch (err) {
      console.error('ZIP search error:', err);
      setError('Could not find location for this ZIP code');
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label htmlFor="zipCode" className="block text-sm font-medium text-white/80 mb-2">
          Enter ZIP Code
        </label>
        <div className="relative">
          <input
            type="text"
            id="zipCode"
            value={zipCode}
            onChange={(e) => {
              const value = e.target.value.replace(/\D/g, '');
              setZipCode(value.slice(0, 5));
            }}
            placeholder="12345"
            maxLength={5}
            pattern="\d{5}"
            className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50"
          />
          {zipCode.length > 0 && (
            <button
              type="button"
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/40 hover:text-white/70"
              onClick={() => setZipCode('')}
            >
              <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )}
        </div>
        {error && (
          <p className="mt-2 text-sm text-red-400 flex items-center">
            <svg className="h-4 w-4 mr-1 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            {error}
          </p>
        )}
      </div>

      <div className="flex justify-end space-x-4">
        <Button
          type="button"
          variant="secondary"
          onClick={onClose}
          disabled={loading}
          className="border border-white/20 hover:border-white/40"
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={loading || !zipCode.match(/^\d{5}$/)}
          className="bg-blue-600 hover:bg-blue-700 flex items-center gap-2"
        >
          {loading ? (
            <>
              <div className="animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent"></div>
              <span>Searching...</span>
            </>
          ) : (
            <>
              <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
              <span>Search</span>
            </>
          )}
        </Button>
      </div>
    </form>
  );
}