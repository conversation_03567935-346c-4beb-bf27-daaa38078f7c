import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import DietaryNutrition from './DietaryNutrition';

// Define nutrition categories with evidence-based information
const NUTRITION_CATEGORIES = [
  {
    id: 'beneficial',
    title: 'Beneficial Foods',
    icon: (
      <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    ),
    color: 'from-emerald-500 to-teal-500',
    items: [
      {
        name: 'Dairy Products',
        description: 'Cheese, milk, and yogurt are high in calcium and phosphorus, which help remineralize tooth enamel. Casein proteins form a protective film on enamel. Yogurt with live cultures may reduce harmful bacteria.',
        examples: ['Cheese', 'Plain yogurt', 'Milk'],
        source: 'Journal of the American Dental Association, 2018',
        imageSrc: 'https://images.unsplash.com/photo-1628689469838-524a4a973b8e?ixlib=rb-1.2.1&auto=format&fit=crop&w=600&q=80'
      },
      {
        name: 'Fibrous Fruits & Vegetables',
        description: 'Crunchy fruits and vegetables with high water content stimulate saliva production, washing away food particles and acids. They also provide a natural cleaning mechanism for teeth.',
        examples: ['Apples', 'Carrots', 'Celery'],
        source: 'British Dental Journal, 2020',
        imageSrc: 'https://images.unsplash.com/photo-1610832958506-aa56368176cf?ixlib=rb-1.2.1&auto=format&fit=crop&w=600&q=80'
      },
      {
        name: 'Foods Rich in Vitamin C',
        description: 'Vitamin C is essential for gum health, helping prevent gingivitis and periodontal disease by promoting collagen production for tissue repair and fighting inflammation.',
        examples: ['Citrus fruits', 'Bell peppers', 'Broccoli'],
        source: 'Journal of Clinical Periodontology, 2019',
        imageSrc: 'https://images.unsplash.com/photo-1597714026720-8f74c62310ba?ixlib=rb-1.2.1&auto=format&fit=crop&w=600&q=80'
      },
      {
        name: 'Green & Black Teas',
        description: 'Teas contain polyphenols and catechins that suppress bacteria, reducing plaque formation and acid production. Black tea contains fluoride which helps strengthen enamel.',
        examples: ['Green tea', 'Black tea (unsweetened)'],
        source: 'European Journal of Nutrition, 2021',
        imageSrc: 'https://images.unsplash.com/photo-1564890369878-4f65486afd97?ixlib=rb-1.2.1&auto=format&fit=crop&w=600&q=80'
      },
      {
        name: 'Foods With Healthy Fats',
        description: 'Omega-3 fatty acids reduce inflammation, potentially helping prevent or treat periodontitis and supporting overall gum health.',
        examples: ['Fatty fish', 'Avocados', 'Nuts and seeds'],
        source: 'Nutrients Journal, 2020',
        imageSrc: 'https://images.unsplash.com/photo-1580400588930-2efd927bf088?ixlib=rb-1.2.1&auto=format&fit=crop&w=600&q=80'
      }
    ]
  },
  {
    id: 'harmful',
    title: 'Foods to Limit',
    icon: (
      <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    ),
    color: 'from-rose-500 to-pink-500',
    items: [
      {
        name: 'Sugary Foods & Drinks',
        description: 'Oral bacteria feed on sugars, producing acids that demineralize tooth enamel, leading to cavities. The longer sugary substances remain in contact with teeth, the more damage occurs.',
        examples: ['Candy', 'Soda', 'Pastries', 'Sports drinks'],
        source: 'Journal of Dental Research, 2019',
        imageSrc: 'https://images.unsplash.com/photo-1621939514649-280e2ee25f60?ixlib=rb-1.2.1&auto=format&fit=crop&w=600&q=80'
      },
      {
        name: 'Acidic Foods & Beverages',
        description: 'Acids directly erode tooth enamel, leading to sensitivity and increased risk of decay. Repeated exposure can cause significant enamel loss over time.',
        examples: ['Citrus juices', 'Vinegar-based foods', 'Carbonated drinks'],
        source: 'Caries Research Journal, 2020',
        imageSrc: 'https://images.unsplash.com/photo-1596803244618-8dea4aeafe35?ixlib=rb-1.2.1&auto=format&fit=crop&w=600&q=80'
      },
      {
        name: 'Sticky Foods',
        description: 'Sticky foods cling to teeth for longer periods, prolonging acid attacks and increasing cavity risk. They can also pull out fillings or damage dental work.',
        examples: ['Dried fruits', 'Caramel', 'Gummies'],
        source: 'American Journal of Dentistry, 2018',
        imageSrc: 'https://images.unsplash.com/photo-1575224300306-1b8da36134ec?ixlib=rb-1.2.1&auto=format&fit=crop&w=600&q=80'
      },
      {
        name: 'Starchy Refined Carbohydrates',
        description: 'Refined starches quickly break down to simple sugars in the mouth. They can get trapped between teeth and feed bacteria, similar to sugars.',
        examples: ['White bread', 'Potato chips', 'Crackers'],
        source: 'Journal of Dental Research, 2021',
        imageSrc: 'https://images.unsplash.com/photo-1514517521153-1be72277b32f?ixlib=rb-1.2.1&auto=format&fit=crop&w=600&q=80'
      },
      {
        name: 'Alcohol',
        description: 'Alcohol causes dehydration and reduces saliva flow, which is essential for removing plaque and bacteria. It is also often acidic and may contain added sugars.',
        examples: ['Wine', 'Mixed drinks', 'Beer'],
        source: 'Journal of Periodontology, 2020',
        imageSrc: 'https://images.unsplash.com/photo-1470337458703-46ad1756a187?ixlib=rb-1.2.1&auto=format&fit=crop&w=600&q=80'
      }
    ]
  },
  {
    id: 'habits',
    title: 'Eating Habits',
    icon: (
      <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    ),
    color: 'from-blue-500 to-indigo-500',
    items: [
      {
        name: 'Timing of Meals and Snacks',
        description: 'Constant snacking exposes teeth to repeated acid attacks. Eating during meals is less harmful as increased saliva production helps neutralize acids and cleanse the mouth.',
        examples: ['Limit snacking frequency', 'Eat treats with meals'],
        source: 'Caries Research, 2019',
        imageSrc: 'https://images.unsplash.com/photo-1573497620053-ea5300f94f21?ixlib=rb-1.2.1&auto=format&fit=crop&w=600&q=80'
      },
      {
        name: 'Hydration Habits',
        description: 'Water helps wash away food particles, dilute acids, and maintain saliva production. Drinking water after consuming acidic or sugary foods helps minimize their harmful effects.',
        examples: ['Drink water after meals', 'Choose water over sugary beverages'],
        source: 'International Journal of Dental Hygiene, 2020',
        imageSrc: 'https://images.unsplash.com/photo-1559839914-17aae19cec71?ixlib=rb-1.2.1&auto=format&fit=crop&w=600&q=80'
      },
      {
        name: 'Post-Consumption Care',
        description: 'After consuming acidic foods or drinks, waiting at least 30 minutes before brushing prevents brushing softened enamel away. Rinsing with water is recommended immediately after.',
        examples: ['Rinse with water after acidic foods', 'Wait 30+ minutes to brush'],
        source: 'Journal of Dentistry, 2018',
        imageSrc: 'https://images.unsplash.com/photo-1559930139-f004d7d9d216?ixlib=rb-1.2.1&auto=format&fit=crop&w=600&q=80'
      }
    ]
  },
  {
    id: 'dietary',
    title: 'Dietary Guides',
    icon: (
      <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
      </svg>
    ),
    color: 'from-purple-500 to-violet-500'
  }
];

const NutritionGuidance: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState(NUTRITION_CATEGORIES[0].id);
  const [expandedItem, setExpandedItem] = useState<string | null>(null);
  
  const currentCategory = NUTRITION_CATEGORIES.find(cat => cat.id === selectedCategory);

  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-semibold text-white">Nutrition & Oral Health</h2>
        <div className="flex items-center gap-2">
          <svg className="w-5 h-5 text-blue-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span className="text-sm text-blue-200">Evidence-based guidance</span>
        </div>
      </div>
      
      <p className="text-blue-100">
        Your food choices significantly impact your oral health. Below is science-backed information on foods that benefit and harm your teeth and gums.
      </p>
      
      {/* Category Navigation */}
      <div className="flex flex-wrap gap-2">
        {NUTRITION_CATEGORIES.map(category => (
          <button
            key={category.id}
            onClick={() => setSelectedCategory(category.id)}
            className={`flex items-center px-4 py-2 rounded-lg transition-all ${
              selectedCategory === category.id
                ? `bg-gradient-to-r ${category.color} text-white shadow-lg`
                : 'bg-white/10 text-blue-100 hover:bg-white/20'
            }`}
          >
            <span className="mr-2">{category.icon}</span>
            <span>{category.title}</span>
          </button>
        ))}
      </div>

      {/* Content Based on Selected Category */}
      <AnimatePresence mode="wait">
        <motion.div
          key={selectedCategory}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3 }}
        >
          {selectedCategory === 'dietary' ? (
            <DietaryNutrition />
          ) : (
            <>
              {/* Items Grid */}
              <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
                {currentCategory?.items?.map(item => (
                  <motion.div
                    key={item.name}
                    layout
                    className={`bg-white/5 backdrop-blur-sm rounded-xl overflow-hidden border border-white/10 ${
                      expandedItem === item.name
                        ? 'col-span-full'
                        : ''
                    } hover:border-white/20 transition-colors`}
                  >
                    <div 
                      className="cursor-pointer"
                      onClick={() => setExpandedItem(expandedItem === item.name ? null : item.name)}
                    >
                      <div className="relative h-48 overflow-hidden">
                        <img 
                          src={item.imageSrc} 
                          alt={item.name} 
                          className="object-cover w-full h-full"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex items-end">
                          <h3 className="text-xl font-semibold text-white p-4">{item.name}</h3>
                        </div>
                      </div>
                      
                      <div className="p-4">
                        <p className="text-blue-100 line-clamp-3">
                          {item.description}
                        </p>
                      </div>
                    </div>
                    
                    <AnimatePresence>
                      {expandedItem === item.name && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                          className="px-4 pb-4"
                        >
                          <div className="bg-white/10 rounded-lg p-4 space-y-3">
                            <div>
                              <h4 className="text-white font-medium">Examples:</h4>
                              <div className="flex flex-wrap gap-2 mt-2">
                                {item.examples.map(example => (
                                  <span 
                                    key={example} 
                                    className="px-2 py-1 bg-white/10 rounded-full text-sm text-blue-200"
                                  >
                                    {example}
                                  </span>
                                ))}
                              </div>
                            </div>
                            
                            {item.source && (
                              <div className="text-sm text-blue-300 flex items-center gap-1">
                                <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                                </svg>
                                <span>Source: {item.source}</span>
                              </div>
                            )}
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                    
                    <div className="px-4 pb-4 text-sm text-blue-200 flex justify-between items-center">
                      {expandedItem === item.name ? (
                        <button 
                          onClick={() => setExpandedItem(null)}
                          className="text-blue-300 hover:text-blue-200 transition-colors"
                        >
                          Show Less
                        </button>
                      ) : (
                        <button 
                          onClick={() => setExpandedItem(item.name)}
                          className="text-blue-300 hover:text-blue-200 transition-colors"
                        >
                          Read More
                        </button>
                      )}
                    </div>
                  </motion.div>
                ))}
              </div>
              
              {/* Practical Tips */}
              {selectedCategory !== 'dietary' && (
                <div className="bg-white/5 backdrop-blur-sm rounded-xl p-6 mt-8">
                  <h3 className="text-xl font-semibold text-white mb-4">Practical Tips for Better Dental Nutrition</h3>
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="flex gap-3">
                      <div className="bg-blue-900/30 rounded-full p-2 h-min">
                        <svg className="w-5 h-5 text-blue-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <div>
                        <h4 className="text-white font-medium mb-1">Timing Matters</h4>
                        <p className="text-blue-100 text-sm">
                          Consume acidic or sweet foods as part of meals rather than as separate snacks to minimize acid exposure.
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex gap-3">
                      <div className="bg-blue-900/30 rounded-full p-2 h-min">
                        <svg className="w-5 h-5 text-blue-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <div>
                        <h4 className="text-white font-medium mb-1">Water After Eating</h4>
                        <p className="text-blue-100 text-sm">
                          Rinse with water after eating sugary or acidic foods to help neutralize acids and wash away particles.
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex gap-3">
                      <div className="bg-blue-900/30 rounded-full p-2 h-min">
                        <svg className="w-5 h-5 text-blue-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <div>
                        <h4 className="text-white font-medium mb-1">Chewing Gum</h4>
                        <p className="text-blue-100 text-sm">
                          Chew sugar-free gum containing xylitol after meals when brushing isn't possible to stimulate saliva flow.
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex gap-3">
                      <div className="bg-blue-900/30 rounded-full p-2 h-min">
                        <svg className="w-5 h-5 text-blue-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <div>
                        <h4 className="text-white font-medium mb-1">Calcium-Rich Finish</h4>
                        <p className="text-blue-100 text-sm">
                          End meals with a piece of cheese or calcium-rich food to help neutralize acids and strengthen teeth.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
        </motion.div>
      </AnimatePresence>
    </div>
  );
};

export default NutritionGuidance; 