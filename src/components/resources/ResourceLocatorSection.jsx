import React from 'react';
import { motion } from 'framer-motion';
import ContentSection from '../sections/ContentSection';
import DentalSchoolsSection from './DentalSchoolsSection';

export default function ResourceLocatorSection() {
  // Title animation variants
  const titleVariants = {
    hidden: { 
      opacity: 0, 
      y: -20,
      scale: 0.9
    },
    visible: { 
      opacity: 1, 
      y: 0,
      scale: 1,
      transition: { 
        duration: 0.8,
        ease: "easeOut" 
      }
    }
  };

  return (
    <ContentSection
      id="resources"
      title={
        <motion.div 
          className="relative"
          variants={titleVariants}
          initial="hidden"
          animate="visible"
        >
          {/* Glowing background effect for title */}
          <div className="absolute -inset-1 rounded-lg bg-gradient-to-r from-blue-500/20 via-indigo-500/20 to-violet-500/20 blur-xl opacity-70"></div>
          
          {/* Main title with increased size */}
          <motion.h2 
            className="text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold bg-gradient-to-r from-blue-400 via-sky-400 to-indigo-400 text-transparent bg-clip-text relative z-10 tracking-tight leading-tight"
            animate={{ 
              textShadow: [
                "0 0 8px rgba(59, 130, 246, 0.4)",
                "0 0 20px rgba(99, 102, 241, 0.6)",
                "0 0 8px rgba(59, 130, 246, 0.4)"
              ]
            }}
            transition={{ 
              duration: 4, 
              repeat: Infinity, 
              ease: "easeInOut" 
            }}
          >
            Find Affordable Care
          </motion.h2>
        </motion.div>
      }
      subtitle={
        <motion.p 
          className="text-xl md:text-2xl text-white/70 mt-4 max-w-3xl mx-auto"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.6 }}
        >
          Locate dental schools and affordable care programs in your area
        </motion.p>
      }
      className="bg-gradient-to-b from-indigo-950 to-blue-950/90 border-t-4 border-indigo-800/30 pt-20 mt-16"
    >
      {/* Section decorations */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Background decorative elements */}
        <div className="absolute -top-20 -right-20 w-80 h-80 bg-blue-500/10 rounded-full blur-[100px]"></div>
        <div className="absolute -bottom-40 -left-20 w-80 h-80 bg-indigo-500/10 rounded-full blur-[100px]"></div>
        <div className="absolute top-1/2 left-1/4 w-40 h-40 bg-violet-500/10 rounded-full blur-[60px]" style={{ animationDelay: '2s' }}></div>
        
        {/* Subtle particle effects */}
        <motion.div 
          className="absolute top-40 right-1/4 w-2 h-2 bg-blue-400 rounded-full opacity-60"
          animate={{ y: [0, -15, 0] }}
          transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
        ></motion.div>
        <motion.div 
          className="absolute bottom-60 right-1/3 w-1 h-1 bg-indigo-400 rounded-full opacity-60"
          animate={{ y: [0, -10, 0] }}
          transition={{ duration: 5, repeat: Infinity, ease: "easeInOut", delay: 0.5 }}
        ></motion.div>
        <motion.div 
          className="absolute top-1/3 left-1/5 w-1.5 h-1.5 bg-violet-400 rounded-full opacity-60"
          animate={{ y: [0, -12, 0] }}
          transition={{ duration: 4.5, repeat: Infinity, ease: "easeInOut", delay: 1 }}
        ></motion.div>
      </div>
      
      <DentalSchoolsSection />
    </ContentSection>
  );
}