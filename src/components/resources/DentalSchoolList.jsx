import React from 'react';
import DentalSchoolCard from './DentalSchoolCard';
import CardGrid from '../common/CardGrid';

export default function DentalSchoolList({ schools, searchLocation, error }) {
  if (error) {
    return (
      <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
        <p className="text-red-400">{error}</p>
      </div>
    );
  }

  if (!schools.length) {
    return (
      <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
        <p className="text-yellow-200">
          {searchLocation 
            ? 'No dental schools found within the selected radius. Try increasing the search radius.'
            : 'Please share your location or enter a ZIP code to find nearby dental schools.'}
        </p>
      </div>
    );
  }

  return (
    <CardGrid>
      {schools.map((school) => (
        <DentalSchoolCard key={school.id} school={school} />
      ))}
    </CardGrid>
  );
}