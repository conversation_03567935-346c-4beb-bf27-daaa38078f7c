import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import ResponsiveDentalMap from './ResponsiveDentalMap';
import { useDentalSchools } from '../../lib/hooks/useDentalSchools';
import LocationOptions from './LocationOptions';
import RadiusSelector from './RadiusSelector';
import DentalSchoolList from './DentalSchoolList';
import { isMobileDevice, isIPad } from '../../utils/ensureResponsiveness';

export default function AffordableDentalLocator() {
  const [activeTab, setActiveTab] = useState('map');
  const [searchLocation, setSearchLocation] = useState(null);
  const [mapError, setMapError] = useState(null);
  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);

  // Detect device type on mount
  useEffect(() => {
    setIsMobile(isMobileDevice());
    setIsTablet(isIPad());

    // Re-check on resize
    const handleResize = () => {
      setIsMobile(isMobileDevice());
      setIsTablet(isIPad());
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  const {
    schools = [],
    loading,
    error,
    radius,
    setRadius,
    searchSchools
  } = useDentalSchools();

  const handleLocationFound = async (coords) => {
    console.log('Location found:', coords);
    // Convert to both formats needed
    const searchCoords = {
      latitude: parseFloat(coords.lat ?? coords.latitude),
      longitude: parseFloat(coords.lng ?? coords.longitude)
    };

    const mapCoords = {
      lat: searchCoords.latitude,
      lng: searchCoords.longitude
    };

    if (isNaN(mapCoords.lat) || isNaN(mapCoords.lng)) {
      console.error('Invalid coordinates:', coords);
      return;
    }

    console.log('Search coordinates:', searchCoords);
    console.log('Map coordinates:', mapCoords);

    setSearchLocation(mapCoords);
    await searchSchools(searchCoords);
  };

  // Convert schools data for map markers
  const mapSchools = Array.isArray(schools) ? schools.map(school => ({
    ...school,
    coordinates: {
      latitude: parseFloat(school.latitude),
      longitude: parseFloat(school.longitude)
    },
    lat: parseFloat(school.latitude),
    lng: parseFloat(school.longitude),
    name: school.name,
    address: school.address,
    clinic_info: school.clinic_info
  })) : [];

  const handleMapError = (error) => {
    console.error('Map error:', error);
    setMapError(error.message || 'Failed to load the map. Please try again later.');
  };

  const handleSchoolSelect = (school) => {
    console.log('School selected:', school);
    // Handle additional actions if needed when a school is selected
  };

  return (
    <div className="bg-indigo-950 min-h-screen">
      {/* Header area with title and subtitle - matching the SMILO Dental Assistant style */}
      <div className="pt-16 pb-12 px-4 sm:px-6 text-center">
        <motion.h1
          className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold text-blue-300 mb-4"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          Find Affordable Dental Care
        </motion.h1>
        <motion.p
          className="text-base sm:text-lg md:text-xl text-white/80 max-w-3xl mx-auto"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          Discover dental schools, community health centers, and other programs offering
          quality dental care at reduced costs.
        </motion.p>
      </div>

      {/* Main tab navigation */}
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 mb-8">
        <div className="bg-indigo-900/50 rounded-lg p-2 inline-flex mb-8 overflow-x-auto max-w-full">
          <button
            className={`px-4 sm:px-6 py-3 rounded-md font-medium transition-colors whitespace-nowrap ${
              activeTab === 'map'
              ? 'bg-blue-600 text-white'
              : 'text-white/70 hover:text-white'
            }`}
            onClick={() => setActiveTab('map')}
          >
            Map
          </button>
          <button
            className={`px-4 sm:px-6 py-3 rounded-md font-medium transition-colors whitespace-nowrap ${
              activeTab === 'reviews'
              ? 'bg-blue-600 text-white'
              : 'text-white/70 hover:text-white'
            }`}
            onClick={() => setActiveTab('reviews')}
          >
            Reviews & Details
          </button>
          <button
            className={`px-4 sm:px-6 py-3 rounded-md font-medium transition-colors whitespace-nowrap ${
              activeTab === 'programs'
              ? 'bg-blue-600 text-white'
              : 'text-white/70 hover:text-white'
            }`}
            onClick={() => setActiveTab('programs')}
          >
            Programs & Resources
          </button>
        </div>

        {/* Dental School Locator section */}
        <div className="bg-indigo-900/30 rounded-xl p-4 sm:p-6 md:p-8 mb-8">
          <h2 className="text-2xl font-bold text-white mb-2">Dental School Locator</h2>
          <p className="text-white/80 mb-6">
            Find accredited dental schools offering reduced-cost care through supervised student clinics.
          </p>

          {/* Secondary tab navigation for the locator */}
          <div className="mb-6">
            <div className="bg-indigo-900/50 rounded-lg p-1 inline-flex overflow-x-auto max-w-full">
              <button
                className={`px-5 py-2 rounded-md font-medium text-sm transition-colors ${
                  activeTab === 'map'
                  ? 'bg-blue-600 text-white'
                  : 'text-white/70 hover:text-white'
                }`}
                onClick={() => setActiveTab('map')}
              >
                Map
              </button>
              <button
                className={`px-5 py-2 rounded-md font-medium text-sm transition-colors ${
                  activeTab === 'reviews'
                  ? 'bg-blue-600 text-white'
                  : 'text-white/70 hover:text-white'
                }`}
                onClick={() => setActiveTab('reviews')}
              >
                Reviews & Details
              </button>
              <button
                className={`px-5 py-2 rounded-md font-medium text-sm transition-colors ${
                  activeTab === 'programs'
                  ? 'bg-blue-600 text-white'
                  : 'text-white/70 hover:text-white'
                }`}
                onClick={() => setActiveTab('programs')}
              >
                Programs & Resources
              </button>
            </div>
          </div>

          {/* Map and details container */}
          <div className="flex flex-col lg:flex-row gap-4 sm:gap-6">
            {/* Map section - left side */}
            <div className="lg:w-2/3 bg-indigo-950 rounded-xl overflow-hidden">
              {loading ? (
                <div className="flex justify-center items-center h-[400px]">
                  <div className="h-10 w-10 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                </div>
              ) : !searchLocation ? (
                <div className="flex flex-col justify-center items-center h-[300px] sm:h-[350px] md:h-[400px] text-center p-4 sm:p-8">
                  <div className="animate-pulse mb-6">
                    <svg className="h-12 w-12 text-blue-400 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </div>
                  <LocationOptions
                    onLocationFound={handleLocationFound}
                    loading={loading}
                  />
                </div>
              ) : mapError ? (
                <div className="flex flex-col justify-center items-center h-[300px] sm:h-[350px] md:h-[400px] text-center p-4 sm:p-8">
                  <div className="text-red-400 mb-4">
                    <svg className="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                  </div>
                  <h3 className="text-white/90 text-lg font-medium mb-2">Map Error</h3>
                  <p className="text-white/70 mb-4">{mapError}</p>
                  <p className="text-white/50 text-sm">Please try refreshing the page or viewing the dental schools in list format below.</p>
                </div>
              ) : (
                <ResponsiveDentalMap
                  schools={mapSchools}
                  userLocation={searchLocation}
                  onSchoolSelect={handleSchoolSelect}
                  onError={handleMapError}
                />
              )}
            </div>

            {/* Details section - right side */}
            <div className="lg:w-1/3">
              <div className="bg-indigo-950 rounded-xl p-6 h-full flex flex-col justify-center items-center text-center">
                <svg className="h-16 w-16 text-blue-400/50 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
                <p className="text-blue-300 text-lg mb-2">
                  Select a dental school on the map
                </p>
                <p className="text-white/60">
                  to view details
                </p>

                {searchLocation && (
                  <div className="mt-8">
                    <div className="bg-blue-900/20 rounded-lg p-4 text-left">
                      <div className="flex items-center mb-2">
                        <svg className="h-5 w-5 text-blue-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <h3 className="font-medium text-white">Tips for Dental Schools</h3>
                      </div>
                      <ul className="text-sm text-white/70 space-y-2">
                        <li>• Call ahead to schedule an appointment</li>
                        <li>• Procedures may take longer as students are learning</li>
                        <li>• All work is supervised by experienced faculty</li>
                        <li>• Bring your ID and insurance information</li>
                      </ul>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* School list when schools are found */}
        {!loading && searchLocation && schools.length > 0 && (
          <DentalSchoolList
            schools={schools}
            searchLocation={searchLocation}
            error={error}
          />
        )}
      </div>
    </div>
  );
}