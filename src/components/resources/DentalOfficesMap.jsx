import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card } from '../common';
import MapComponent from '../MapComponent';
import { DENTAL_OFFICES } from '../../lib/constants/dentalOffices';
import DentalOfficesList from './DentalOfficesList';

// Map styles to match the theme
const MAP_STYLES = [
  {
    "featureType": "water",
    "elementType": "geometry",
    "stylers": [
      { "color": "#1e293b" },
      { "lightness": -5 }
    ]
  },
  {
    "featureType": "landscape",
    "elementType": "geometry",
    "stylers": [
      { "color": "#0f172a" }
    ]
  },
  {
    "featureType": "road",
    "elementType": "geometry",
    "stylers": [
      { "color": "#334155" },
      { "lightness": -10 }
    ]
  },
  {
    "featureType": "poi",
    "elementType": "geometry",
    "stylers": [
      { "color": "#1e293b" },
      { "lightness": -15 }
    ]
  },
  {
    "featureType": "administrative",
    "elementType": "geometry.stroke",
    "stylers": [
      { "color": "#6366f1" },
      { "lightness": 15 },
      { "weight": 1.2 }
    ]
  }
];

const DentalOfficesMap = ({ customOffices }) => {
  const [selectedOffice, setSelectedOffice] = useState(null);
  const [mapOffices, setMapOffices] = useState([]);
  const [searchLocation, setSearchLocation] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  // Use either custom offices passed as props or default DENTAL_OFFICES
  useEffect(() => {
    setMapOffices(customOffices || DENTAL_OFFICES);
  }, [customOffices]);
  
  // Format offices data for map component
  const formattedOffices = mapOffices.map(office => ({
    id: office.id,
    name: office.name,
    address: office.address,
    lat: office.coordinates.latitude,
    lng: office.coordinates.longitude,
    description: office.officeType,
    icon: '🦷', // Dental icon
    rating: office.rating,
    totalRatings: office.totalRatings
  }));
  
  // Calculate center of the map based on offices locations
  const calculateMapCenter = () => {
    if (selectedOffice) {
      return { 
        lat: selectedOffice.coordinates.latitude, 
        lng: selectedOffice.coordinates.longitude 
      };
    }
    
    if (mapOffices.length === 0) {
      return { lat: 26.1224, lng: -80.1373 }; // Default to Florida
    }
    
    // Calculate the average lat/lng of all offices
    const sumCoords = mapOffices.reduce(
      (acc, office) => {
        return {
          lat: acc.lat + office.coordinates.latitude,
          lng: acc.lng + office.coordinates.longitude
        };
      },
      { lat: 0, lng: 0 }
    );
    
    return {
      lat: sumCoords.lat / mapOffices.length,
      lng: sumCoords.lng / mapOffices.length
    };
  };
  
  // Handle marker click on map
  const handleMarkerClick = (location) => {
    const office = mapOffices.find(
      o => o.coordinates.latitude === location.lat && o.coordinates.longitude === location.lng
    );
    
    if (office) {
      setSelectedOffice(office);
    }
  };

  // Render star rating helper
  const renderStarRating = (rating) => {
    const fullStars = Math.floor(rating);
    const stars = [];

    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <svg key={`star-${i}`} className="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
        </svg>
      );
    }

    return stars;
  };
  
  return (
    <motion.div
      className="space-y-6"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Map Card */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Card>
          <div className="p-4">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold text-white">Dental Offices</h2>
              <div className="text-indigo-300 text-sm">
                {mapOffices.length} offices available
              </div>
            </div>
            
            <div className="h-[500px] rounded-lg overflow-hidden border border-white/10">
              {loading ? (
                <div className="flex flex-col justify-center items-center h-full bg-indigo-950/50">
                  <div className="h-10 w-10 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mb-4"></div>
                  <p className="text-white/70">Loading map...</p>
                </div>
              ) : error ? (
                <div className="flex flex-col justify-center items-center h-full bg-indigo-950/50 p-6 text-center">
                  <div className="text-red-400 mb-4">
                    <svg className="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-medium text-white mb-2">Map Error</h3>
                  <p className="text-white/70 mb-4">{error}</p>
                </div>
              ) : (
                <MapComponent 
                  locations={formattedOffices}
                  center={calculateMapCenter()}
                  zoom={10}
                  height="500px"
                  onMarkerClick={handleMarkerClick}
                  customStyles={MAP_STYLES}
                />
              )}
            </div>
          </div>
        </Card>
      </motion.div>
      
      {/* Selected Dentist Profile Card */}
      {selectedOffice && selectedOffice.dentist && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card className="p-6 border-l-4 border-indigo-500">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h3 className="text-xl font-bold text-white">{selectedOffice.dentist.name}</h3>
                <p className="text-indigo-300 mt-1">{selectedOffice.name}</p>
              </div>
              <div className="flex items-center">
                <div className="flex">{renderStarRating(selectedOffice.rating)}</div>
                <span className="text-white/60 text-sm ml-2">
                  ({selectedOffice.totalRatings} reviews)
                </span>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <div className="bg-slate-800/50 rounded-lg p-4">
                  <h4 className="text-indigo-300 font-medium mb-2">About</h4>
                  <p className="text-white/80 text-sm leading-relaxed">
                    {selectedOffice.dentist.bio}
                  </p>
                </div>
                
                <div className="mt-4">
                  <h4 className="text-indigo-300 font-medium mb-2">Education & Specialties</h4>
                  <p className="text-white/80 text-sm mb-2">
                    <span className="text-white/60">Education:</span> {selectedOffice.dentist.education}
                  </p>
                  
                  {selectedOffice.dentist.specialties && (
                    <div className="flex flex-wrap gap-2 mt-2">
                      {selectedOffice.dentist.specialties.map((specialty, index) => (
                        <span 
                          key={index} 
                          className="bg-indigo-900/40 text-indigo-200 text-xs px-2 py-1 rounded"
                        >
                          {specialty}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </div>
              
              <div>
                <div className="bg-slate-800/50 rounded-lg p-4">
                  <h4 className="text-indigo-300 font-medium mb-2">Personal Experience</h4>
                  <p className="text-white/80 text-sm leading-relaxed italic">
                    "{selectedOffice.dentist.personalNote}"
                  </p>
                </div>
                
                <div className="mt-4">
                  <h4 className="text-indigo-300 font-medium mb-2">Contact Information</h4>
                  <p className="text-white/80 text-sm mb-1">
                    <span className="text-white/60">Address:</span> {selectedOffice.address}
                  </p>
                  <p className="text-white/80 text-sm mb-1">
                    <span className="text-white/60">Phone:</span> {selectedOffice.phone}
                  </p>
                  {selectedOffice.website && (
                    <p className="text-white/80 text-sm">
                      <span className="text-white/60">Website:</span>{' '}
                      <a 
                        href={selectedOffice.website} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-blue-400 hover:text-blue-300 transition-colors"
                      >
                        {selectedOffice.website}
                      </a>
                    </p>
                  )}
                </div>
              </div>
            </div>
            
            <div className="mt-6 flex justify-end">
              <button 
                className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md transition-colors"
                onClick={() => window.open(`tel:${selectedOffice.phone.replace(/[()\s-]/g, '')}`)}
              >
                Call Office
              </button>
            </div>
          </Card>
        </motion.div>
      )}
      
      {/* Dental Offices List */}
      <DentalOfficesList 
        offices={mapOffices}
        selectedOffice={selectedOffice}
        onOfficeSelect={setSelectedOffice}
      />
    </motion.div>
  );
};

export default DentalOfficesMap; 