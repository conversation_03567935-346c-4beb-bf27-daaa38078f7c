import React from 'react';
import Card from '../common/Card';

export default function DentalSchoolCard({ school }) {
  const { 
    name, 
    address,
    city,
    state,
    zip,
    distance,
    website,
    phone,
    clinic_info,
    student_clinic_hours,
    payment_info,
    programs = [],
    services = []
  } = school;
  
  return (
    <Card className="h-full flex flex-col justify-between">
      <div className="space-y-4">
        <h3 className="text-xl font-semibold text-white">{name}</h3>
        <p className="text-blue-100/80">{`${address}, ${city}, ${state} ${zip}`}</p>
        
        {distance && (
          <p className="text-blue-400">
            {distance} miles away
          </p>
        )}

        {clinic_info && (
          <p className="text-white/80 text-sm">
            {clinic_info}
          </p>
        )}

        {student_clinic_hours && (
          <div className="mt-2">
            <h4 className="text-sm font-medium text-white/90 mb-1">Clinic Hours</h4>
            <p className="text-white/80 text-sm">{student_clinic_hours}</p>
          </div>
        )}

        {programs?.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {programs.map(program => (
              <span
                key={program}
                className="px-2 py-1 bg-blue-500/10 text-blue-400 text-xs rounded-full"
              >
                {program}
              </span>
            ))}
          </div>
        )}

        {services?.length > 0 && (
          <div className="mt-4">
            <h4 className="text-sm font-medium text-white/90 mb-2">Available Services</h4>
            <div className="space-y-2">
              {services.slice(0, 3).map(service => (
                <div key={service.service_name} className="text-sm">
                  <span className="text-white/90">{service.service_name}</span>
                  {service.price_range && (
                    <span className="text-blue-400 ml-2">{service.price_range}</span>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      <div className="mt-6 space-y-3">
        {payment_info && (
          <p className="text-white/80 text-sm">
            {payment_info}
          </p>
        )}
        
        {phone && (
          <p className="text-white/80 text-sm">
            Phone: <a 
              href={`tel:${phone.replace(/[^\d]/g, '')}`} 
              className="text-blue-400 hover:text-blue-300 transition-colors"
            >
              {phone}
            </a>
          </p>
        )}
        
        {website && (
          <a
            href={website.startsWith('http') ? website : `https://${website}`}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center text-blue-400 hover:text-blue-300 transition-colors"
          >
            Visit Website
            <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
            </svg>
          </a>
        )}
      </div>
    </Card>
  );
}