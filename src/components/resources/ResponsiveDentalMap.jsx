import React, { useState, useEffect, useRef } from 'react';
import MapComponent from '../MapComponent';
import { loadGoogleMapsScript } from '../../lib/services/googleMapsService';
import { isMobileDevice, isIPad } from '../../utils/ensureResponsiveness';

// Map styles to match the theme
const MAP_STYLES = [
  {
    "featureType": "water",
    "elementType": "geometry",
    "stylers": [
      { "color": "#1e293b" },
      { "lightness": -5 }
    ]
  },
  {
    "featureType": "landscape",
    "elementType": "geometry",
    "stylers": [
      { "color": "#0f172a" }
    ]
  },
  {
    "featureType": "road",
    "elementType": "geometry",
    "stylers": [
      { "color": "#334155" },
      { "lightness": -10 }
    ]
  },
  {
    "featureType": "poi",
    "elementType": "geometry",
    "stylers": [
      { "color": "#1e293b" },
      { "lightness": -15 }
    ]
  },
  {
    "featureType": "administrative",
    "elementType": "geometry.stroke",
    "stylers": [
      { "color": "#6366f1" },
      { "lightness": 15 },
      { "weight": 1.2 }
    ]
  }
];

const DEFAULT_CENTER = { lat: 39.8283, lng: -98.5795 }; // Center of US
const DEFAULT_ZOOM = 4;

export default function ResponsiveDentalMap({ 
  schools = [], 
  userLocation, 
  onSchoolSelect,
  onError
}) {
  const [mapHeight, setMapHeight] = useState('500px');
  const [selectedSchool, setSelectedSchool] = useState(null);
  const containerRef = useRef(null);

  // Determine if we're on mobile or iPad
  const isMobile = isMobileDevice();
  const isTablet = isIPad();

  // Convert schools data for map markers
  const mapSchools = Array.isArray(schools) ? schools.map(school => ({
    ...school,
    lat: parseFloat(school.latitude),
    lng: parseFloat(school.longitude),
    name: school.name,
    address: school.address,
    description: school.clinic_info
  })) : [];

  // Handle marker click
  const handleMarkerClick = (school) => {
    setSelectedSchool(school);
    if (onSchoolSelect) {
      onSchoolSelect(school);
    }
  };

  // Adjust map height based on screen size
  useEffect(() => {
    const updateMapHeight = () => {
      if (!containerRef.current) return;
      
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;
      
      // Responsive height adjustments
      if (viewportWidth <= 640) { // Small mobile
        setMapHeight('350px');
      } else if (viewportWidth <= 768) { // Mobile
        setMapHeight('400px');
      } else if (viewportWidth <= 1024) { // Tablet
        setMapHeight('450px');
      } else { // Desktop
        setMapHeight('500px');
      }
      
      // Adjust for landscape orientation on mobile
      if (isMobile && viewportWidth > viewportHeight) {
        setMapHeight('300px');
      }
    };
    
    // Set initial height
    updateMapHeight();
    
    // Update on resize
    window.addEventListener('resize', updateMapHeight);
    window.addEventListener('orientationchange', updateMapHeight);
    
    return () => {
      window.removeEventListener('resize', updateMapHeight);
      window.removeEventListener('orientationchange', updateMapHeight);
    };
  }, [isMobile]);

  // Handle map error
  const handleMapError = (error) => {
    console.error('Map error:', error);
    if (onError) {
      onError(error);
    }
  };

  // Determine center and zoom
  const mapCenter = userLocation ? {
    lat: parseFloat(userLocation.lat ?? userLocation.latitude),
    lng: parseFloat(userLocation.lng ?? userLocation.longitude)
  } : DEFAULT_CENTER;
  
  const mapZoom = userLocation ? (isMobile ? 9 : 10) : DEFAULT_ZOOM;

  return (
    <div 
      ref={containerRef} 
      className="w-full rounded-lg overflow-hidden relative"
    >
      <MapComponent 
        locations={mapSchools}
        center={mapCenter}
        zoom={mapZoom}
        height={mapHeight}
        onMarkerClick={handleMarkerClick}
        customStyles={MAP_STYLES}
      />
      
      {/* Optional: Selected school info overlay */}
      {selectedSchool && (
        <div className="absolute bottom-0 left-0 right-0 bg-indigo-900/80 backdrop-blur-sm p-4 border-t border-indigo-500/30">
          <h3 className="text-lg font-medium text-white">{selectedSchool.name}</h3>
          {selectedSchool.address && (
            <p className="text-white/80 text-sm mt-1">{selectedSchool.address}</p>
          )}
          <button 
            className="mt-2 text-blue-300 text-sm hover:text-blue-200 transition-colors"
            onClick={() => setSelectedSchool(null)}
          >
            Close
          </button>
        </div>
      )}
    </div>
  );
}
