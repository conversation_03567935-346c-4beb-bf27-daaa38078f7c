import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import RecordVisit from './components/RecordVisit';
import TranscriptViewer from './components/TranscriptViewer';
import BillingComparison from './components/BillingComparison';
import VisitHistory from './components/VisitHistory';
import SecureHeader from './components/SecureHeader';
import SecureDashboard from './components/SecureDashboard';
import AdminSettings from './components/AdminSettings';
import UserProfile from './components/UserProfile';
import Help from './components/Help';
import BreathCheck from './components/BreathCheck';
import { SecureProvider } from './SecureContext';

const SmiloSecureTool = ({ onBack }) => {
  const [activeView, setActiveView] = useState('dashboard');
  const [selectedVisitId, setSelectedVisitId] = useState(null);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.5 } },
    exit: { opacity: 0, transition: { duration: 0.3 } }
  };

  // Functions to handle navigation between different views
  const goToDashboard = () => {
    setActiveView('dashboard');
    setSelectedVisitId(null);
  };

  const goToRecordVisit = () => {
    setActiveView('record');
  };

  const goToHistory = () => {
    setActiveView('history');
  };

  const viewVisitDetails = (visitId) => {
    setSelectedVisitId(visitId);
    setActiveView('transcript');
  };

  const goToBillingComparison = (visitId) => {
    setSelectedVisitId(visitId || selectedVisitId);
    setActiveView('comparison');
  };
  
  const goToUserProfile = () => {
    setActiveView('profile');
  };
  
  const goToHelp = () => {
    setActiveView('help');
  };
  
  const goToAdminSettings = () => {
    setActiveView('admin');
  };
  
  const goToBreathCheck = () => {
    setActiveView('breathcheck');
  };

  // Render the correct component based on activeView
  const renderActiveView = () => {
    switch (activeView) {
      case 'dashboard':
        return (
          <SecureDashboard 
            onRecordVisit={goToRecordVisit} 
            onViewHistory={goToHistory} 
            onProfile={goToUserProfile}
            onHelp={goToHelp}
            onAdmin={goToAdminSettings}
            onBreathCheck={goToBreathCheck}
          />
        );
      case 'record':
        return <RecordVisit onComplete={viewVisitDetails} onCancel={goToDashboard} />;
      case 'history':
        return <VisitHistory onViewVisit={viewVisitDetails} onBack={goToDashboard} />;
      case 'transcript':
        return (
          <TranscriptViewer 
            visitId={selectedVisitId} 
            onBack={goToHistory}
            onCompare={goToBillingComparison} 
          />
        );
      case 'comparison':
        return (
          <BillingComparison 
            visitId={selectedVisitId} 
            onBack={() => setActiveView('transcript')} 
          />
        );
      case 'profile':
        return <UserProfile onBack={goToDashboard} />;
      case 'help':
        return <Help onBack={goToDashboard} />;
      case 'admin':
        return <AdminSettings onBack={goToDashboard} />;
      case 'breathcheck':
        return <BreathCheck onBack={goToDashboard} />;
      default:
        return <SecureDashboard onRecordVisit={goToRecordVisit} onViewHistory={goToHistory} />;
    }
  };

  return (
    <SecureProvider>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        exit="exit"
        className="bg-white/5 backdrop-blur-sm p-6 rounded-2xl border border-white/10 shadow-xl"
      >
        <SecureHeader onBack={onBack} activeView={activeView} onDashboard={goToDashboard} />
        
        <AnimatePresence mode="wait">
          <motion.div
            key={activeView}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
            className="mt-6"
          >
            {renderActiveView()}
          </motion.div>
        </AnimatePresence>
        
        {/* Footer Navigation */}
        <div className="mt-8 pt-6 border-t border-white/10">
          <div className="flex justify-between items-center">
            <div className="flex space-x-3">
              <button 
                onClick={goToDashboard}
                className={`p-2 rounded-lg transition-colors ${activeView === 'dashboard' ? 'bg-emerald-600 text-white' : 'text-white/60 hover:text-white hover:bg-white/5'}`}
              >
                <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
              </button>
              
              <button 
                onClick={goToRecordVisit}
                className={`p-2 rounded-lg transition-colors ${activeView === 'record' ? 'bg-emerald-600 text-white' : 'text-white/60 hover:text-white hover:bg-white/5'}`}
              >
                <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                </svg>
              </button>
              
              <button 
                onClick={goToHistory}
                className={`p-2 rounded-lg transition-colors ${activeView === 'history' ? 'bg-emerald-600 text-white' : 'text-white/60 hover:text-white hover:bg-white/5'}`}
              >
                <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </button>
              
              <button 
                onClick={goToBreathCheck}
                className={`p-2 rounded-lg transition-colors ${activeView === 'breathcheck' ? 'bg-emerald-600 text-white' : 'text-white/60 hover:text-white hover:bg-white/5'}`}
                title="BreathCheck™ Premium"
              >
                <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
                </svg>
              </button>
            </div>
            
            <div className="flex space-x-3">
              <button 
                onClick={goToHelp}
                className={`p-2 rounded-lg transition-colors ${activeView === 'help' ? 'bg-emerald-600 text-white' : 'text-white/60 hover:text-white hover:bg-white/5'}`}
              >
                <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </button>
              
              <button 
                onClick={goToUserProfile}
                className={`p-2 rounded-lg transition-colors ${activeView === 'profile' ? 'bg-emerald-600 text-white' : 'text-white/60 hover:text-white hover:bg-white/5'}`}
              >
                <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </button>
              
              <button 
                onClick={goToAdminSettings}
                className={`p-2 rounded-lg transition-colors ${activeView === 'admin' ? 'bg-emerald-600 text-white' : 'text-white/60 hover:text-white hover:bg-white/5'}`}
              >
                <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </button>
            </div>
          </div>
          
          {/* Powered by Jamevo */}
          <div className="mt-4 flex justify-end">
            <span className="text-white/40 text-xs font-medium">Powered by <span className="text-emerald-400">Jamevo</span></span>
          </div>
        </div>
      </motion.div>
    </SecureProvider>
  );
};

export default SmiloSecureTool; 