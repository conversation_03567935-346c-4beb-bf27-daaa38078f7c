import React, { createContext, useContext, useState, useEffect } from 'react';

// Create context
const SecureContext = createContext();

// Sample mock data
const mockVisits = [
  {
    id: '1',
    date: '2023-09-15',
    dentistName: '<PERSON>. <PERSON>',
    clinic: 'Bright Smile Dental',
    duration: 35, // minutes
    status: 'processed',
    audioUrl: 'https://example.com/recordings/visit-1.mp3',
    transcript: {
      segments: [
        { startTime: 0, endTime: 15, text: "Hello Mr. <PERSON>, how are you feeling today?", speaker: "Dentist" },
        { startTime: 15, endTime: 22, text: "I'm doing well, thanks. Just a bit of pain in my back molar.", speaker: "Patient" },
        { startTime: 22, endTime: 45, text: "Let's take a look at that. I see some decay on tooth #18. We'll need to do a filling today.", speaker: "Dentist" },
        { startTime: 45, endTime: 55, text: "Will my insurance cover that?", speaker: "Patient" },
        { startTime: 55, endTime: 85, text: "Yes, this is a standard procedure. We'll be doing a composite resin filling which is covered by your plan.", speaker: "Dentist" }
      ],
      identifiedProcedures: [
        { 
          code: 'D2391', 
          name: 'Resin-based composite filling, one surface, posterior',
          description: 'Tooth-colored filling on one surface of a back tooth',
          confidence: 0.95,
          timestamp: 30
        }
      ]
    },
    billingAnalysis: {
      statement: {
        provider: 'Delta Dental',
        date: '2023-09-15',
        totalAmount: '210.00',
        items: [
          {
            procedureCode: 'D2391',
            description: 'Resin-based composite filling, one surface, posterior',
            amount: '210.00',
            quantity: 1
          }
        ]
      },
      comparisonResult: {
        status: 'no_issues',
        summary: 'Billing statement matches the procedures discussed during your visit.'
      }
    }
  },
  {
    id: '2',
    date: '2023-08-02',
    dentistName: 'Dr. Michael Chen',
    clinic: 'City Dental Group',
    duration: 55, // minutes
    status: 'processed',
    audioUrl: 'https://example.com/recordings/visit-2.mp3',
    transcript: {
      segments: [
        { startTime: 0, endTime: 12, text: "Welcome back, Mrs. Johnson. How have you been?", speaker: "Dentist" },
        { startTime: 12, endTime: 25, text: "I've been having some sensitivity to cold on the left side.", speaker: "Patient" },
        { startTime: 25, endTime: 45, text: "Let me check that out. Looks like you have some recession on tooth #14 exposing the root. We should do a deep cleaning today and I'd recommend a crown for better protection.", speaker: "Dentist" },
        { startTime: 45, endTime: 60, text: "Is that expensive? I'm not sure what my insurance covers.", speaker: "Patient" },
        { startTime: 60, endTime: 95, text: "The deep cleaning is typically covered. For the crown, your insurance will cover about 50%. We can do the cleaning today and schedule you for the crown next month if you'd like.", speaker: "Dentist" }
      ],
      identifiedProcedures: [
        { 
          code: 'D4355', 
          name: 'Full mouth debridement',
          description: 'Deep cleaning of the entire mouth',
          confidence: 0.88,
          timestamp: 35
        },
        { 
          code: 'D2740', 
          name: 'Crown - porcelain/ceramic',
          description: 'Full ceramic crown restoration',
          confidence: 0.92,
          timestamp: 40
        }
      ]
    },
    billingAnalysis: {
      statement: {
        provider: 'Aetna Dental',
        date: '2023-08-02',
        totalAmount: '420.00',
        items: [
          {
            procedureCode: 'D4355',
            description: 'Full mouth debridement',
            amount: '220.00',
            quantity: 1
          },
          {
            procedureCode: 'D2950',
            description: 'Core buildup, including any pins when required',
            amount: '200.00',
            quantity: 1
          }
        ]
      },
      comparisonResult: {
        status: 'has_issues',
        summary: 'Potential discrepancies detected between your visit discussion and billing statement.',
        flaggedItems: [
          {
            procedureCode: 'D2950',
            description: 'Core buildup, including any pins when required',
            severity: 'high',
            issue: 'This procedure was not clearly discussed during your visit but appears on your bill.',
            recommendedAction: 'Contact your dental office for clarification about this charge.'
          }
        ],
        missingProcedures: [
          {
            code: 'D2740',
            name: 'Crown - porcelain/ceramic'
          }
        ]
      }
    }
  },
  {
    id: '3',
    date: '2023-10-05',
    dentistName: 'Dr. Sarah Johnson',
    clinic: 'Bright Smile Dental',
    duration: 15, // minutes
    status: 'processed',
    audioUrl: 'https://example.com/recordings/visit-3.mp3',
    transcript: {
      segments: [
        { startTime: 0, endTime: 10, text: "Good morning, just here for your regular check-up today?", speaker: "Dentist" },
        { startTime: 10, endTime: 15, text: "Yes, and I want to ask about whitening options.", speaker: "Patient" },
        { startTime: 15, endTime: 35, text: "We'll do your cleaning and exam first, then discuss whitening. We offer both in-office and take-home options.", speaker: "Dentist" },
        { startTime: 35, endTime: 60, text: "Everything looks good! No cavities. For whitening, the in-office treatment is $350 and the take-home kit is $200. Both are quite effective, but the in-office is faster.", speaker: "Dentist" },
        { startTime: 60, endTime: 70, text: "I think I'll go with the take-home kit. Will my insurance cover any of that?", speaker: "Patient" },
        { startTime: 70, endTime: 85, text: "No, whitening is considered cosmetic so it's not covered. But we can process the cleaning and exam through your insurance today.", speaker: "Dentist" }
      ],
      identifiedProcedures: [
        { 
          code: 'D1110', 
          name: 'Prophylaxis - adult',
          description: 'Regular dental cleaning',
          confidence: 0.97,
          timestamp: 20
        },
        { 
          code: 'D0120', 
          name: 'Periodic oral evaluation',
          description: 'Regular dental examination',
          confidence: 0.95,
          timestamp: 25
        },
        { 
          code: 'D9972', 
          name: 'External bleaching - per arch',
          description: 'Take-home teeth whitening kit',
          confidence: 0.85,
          timestamp: 65
        }
      ]
    },
    billingAnalysis: {
      statement: {
        provider: 'Delta Dental',
        date: '2023-10-05',
        totalAmount: '290.00',
        items: [
          {
            procedureCode: 'D1110',
            description: 'Prophylaxis - adult',
            amount: '90.00',
            quantity: 1
          },
          {
            procedureCode: 'D0120',
            description: 'Periodic oral evaluation',
            amount: '0.00',
            quantity: 1
          },
          {
            procedureCode: 'D9972',
            description: 'External bleaching - per arch - take home',
            amount: '200.00',
            quantity: 1
          }
        ]
      },
      comparisonResult: {
        status: 'has_issues',
        summary: 'Minor discrepancy in billing detected.',
        flaggedItems: [
          {
            procedureCode: 'D9972',
            description: 'External bleaching - per arch',
            severity: 'low',
            issue: 'Cosmetic procedure not covered by insurance, as mentioned during your visit.',
            recommendedAction: 'This is expected as the dentist mentioned this would not be covered.'
          }
        ]
      }
    }
  }
];

// Mock system settings
const mockSystemSettings = {
  apiKeys: {
    openai: 'sk-sample-key-openai',
    googleCloud: 'AIza-sample-google-key',
    azure: 'azure-sample-key'
  },
  serviceSettings: {
    transcriptionProvider: 'openai',
    storageProvider: 'local',
    maxRecordingLength: 60,
    maxStorageMB: 5000
  },
  userPermissions: {
    enableAutoSharing: false,
    enableExport: true,
    requireEncryption: true
  },
  billingAnalysis: {
    sensitivityLevel: 'medium',
    enableAutomaticFlagging: true,
    cptCodeDatabase: 'ada-2023'
  }
};

// Context provider component
export const SecureProvider = ({ children }) => {
  const [visits, setVisits] = useState([]);
  const [loadingState, setLoadingState] = useState({
    loading: false,
    error: null
  });
  const [currentRecording, setCurrentRecording] = useState(null);
  const [systemSettings, setSystemSettings] = useState(null);
  
  // Fetch visits (simulating API call)
  const fetchVisits = async () => {
    setLoadingState({ loading: true, error: null });
    
    try {
      // Simulate network request
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setVisits(mockVisits);
      setLoadingState({ loading: false, error: null });
      return mockVisits;
    } catch (error) {
      setLoadingState({ loading: false, error: 'Failed to fetch visits' });
      throw error;
    }
  };
  
  // Start a new recording
  const startRecording = () => {
    const newRecording = {
      id: `temp-${Date.now()}`,
      status: 'recording',
      startTime: new Date(),
      metadata: {}
    };
    
    setCurrentRecording(newRecording);
    return newRecording;
  };
  
  // Stop the current recording
  const stopRecording = (audioBlob, durationInSeconds) => {
    if (!currentRecording) return null;
    
    const updatedRecording = {
      ...currentRecording,
      status: 'ready_to_process',
      endTime: new Date(),
      duration: durationInSeconds,
      audioBlob
    };
    
    setCurrentRecording(updatedRecording);
    return updatedRecording;
  };
  
  // Get a visit by ID
  const getVisitById = async (id) => {
    setLoadingState({ loading: true, error: null });
    
    try {
      // Simulate network request
      await new Promise(resolve => setTimeout(resolve, 800));
      
      const visit = mockVisits.find(v => v.id === id);
      
      if (!visit) {
        throw new Error('Visit not found');
      }
      
      setLoadingState({ loading: false, error: null });
      return visit;
    } catch (error) {
      setLoadingState({ loading: false, error: 'Failed to fetch visit' });
      throw error;
    }
  };
  
  // Process a recording to create a visit
  const processRecording = async (recordingId) => {
    if (!currentRecording || currentRecording.id !== recordingId) {
      throw new Error('Recording not found');
    }
    
    setLoadingState({ loading: true, error: null });
    
    try {
      // Simulate processing time
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Create a new visit from the recording
      const newVisit = {
        id: `visit-${Date.now()}`,
        date: new Date().toISOString().split('T')[0],
        dentistName: 'Dr. Sample Doctor',
        clinic: 'Sample Clinic',
        duration: Math.floor(currentRecording.duration / 60),
        status: 'processed',
        audioUrl: URL.createObjectURL(currentRecording.audioBlob),
        transcript: {
          segments: [
            { startTime: 0, endTime: 10, text: "This is a simulated transcript for your new recording.", speaker: "Dentist" },
            { startTime: 10, endTime: 20, text: "It would normally be generated from your actual audio.", speaker: "Patient" }
          ],
          identifiedProcedures: [
            { 
              code: 'D0120', 
              name: 'Periodic oral evaluation',
              description: 'Regular dental check-up',
              confidence: 0.92,
              timestamp: 5
            }
          ]
        }
      };
      
      // Add the new visit to the list
      setVisits(prev => [newVisit, ...prev]);
      setCurrentRecording(null);
      setLoadingState({ loading: false, error: null });
      
      return newVisit.id;
    } catch (error) {
      setLoadingState({ loading: false, error: 'Failed to process recording' });
      throw error;
    }
  };
  
  // Update billing information for a visit
  const updateBillingInfo = async (visitId, billingStatement) => {
    setLoadingState({ loading: true, error: null });
    
    try {
      // Simulate network request
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Find the visit to update
      const visitIndex = visits.findIndex(v => v.id === visitId);
      
      if (visitIndex === -1) {
        throw new Error('Visit not found');
      }
      
      // Generate a simulated comparison result
      const hasMismatches = Math.random() > 0.5;
      const comparisonResult = hasMismatches ? {
        status: 'has_issues',
        summary: 'Potential discrepancies detected between your visit discussion and billing statement.',
        flaggedItems: [
          {
            procedureCode: billingStatement.items[0]?.procedureCode || 'UNKNOWN',
            description: billingStatement.items[0]?.description || 'Unknown procedure',
            severity: 'medium',
            issue: 'This procedure was not clearly discussed during your visit or was billed at a higher rate than mentioned.',
            recommendedAction: 'Contact your dental office for clarification about this charge.'
          }
        ],
        missingProcedures: []
      } : {
        status: 'no_issues',
        summary: 'Billing statement appears to match the procedures discussed during your visit.'
      };
      
      // Update the visit with billing info
      const updatedVisit = {
        ...visits[visitIndex],
        billingAnalysis: {
          statement: billingStatement,
          comparisonResult
        }
      };
      
      // Update the visits array
      const updatedVisits = [...visits];
      updatedVisits[visitIndex] = updatedVisit;
      setVisits(updatedVisits);
      
      setLoadingState({ loading: false, error: null });
      return updatedVisit.billingAnalysis;
    } catch (error) {
      setLoadingState({ loading: false, error: 'Failed to update billing information' });
      throw error;
    }
  };
  
  // Fetch system settings
  const fetchSystemSettings = async () => {
    setLoadingState({ loading: true, error: null });
    
    try {
      // Simulate network request
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setSystemSettings(mockSystemSettings);
      setLoadingState({ loading: false, error: null });
      return mockSystemSettings;
    } catch (error) {
      setLoadingState({ loading: false, error: 'Failed to fetch system settings' });
      throw error;
    }
  };
  
  // Update system settings
  const updateSystemSettings = async (newSettings) => {
    setLoadingState({ loading: true, error: null });
    
    try {
      // Simulate network request
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      setSystemSettings(newSettings);
      setLoadingState({ loading: false, error: null });
      return true;
    } catch (error) {
      setLoadingState({ loading: false, error: 'Failed to update system settings' });
      throw error;
    }
  };
  
  // Initial data fetch
  useEffect(() => {
    fetchVisits().catch(console.error);
  }, []);
  
  // Context value
  const value = {
    visits,
    loadingState,
    currentRecording,
    systemSettings,
    fetchVisits,
    startRecording,
    stopRecording,
    getVisitById,
    processRecording,
    updateBillingInfo,
    fetchSystemSettings,
    updateSystemSettings
  };
  
  return (
    <SecureContext.Provider value={value}>
      {children}
    </SecureContext.Provider>
  );
};

// Custom hook for using the context
export const useSecure = () => {
  const context = useContext(SecureContext);
  
  if (!context) {
    throw new Error('useSecure must be used within a SecureProvider');
  }
  
  return context;
};

export default SecureContext; 