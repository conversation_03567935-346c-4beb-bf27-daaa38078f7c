import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useSecure } from '../SecureContext';

const UserProfile = () => {
  const { systemSettings } = useSecure();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  
  const [profile, setProfile] = useState({
    personal: {
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '(*************'
    },
    preferences: {
      enableNotifications: true,
      notificationMethods: ['email', 'app'],
      theme: 'dark',
      language: 'english',
      autoShareWithDentist: false
    },
    insurance: {
      provider: 'Delta Dental',
      memberId: 'DD123456789',
      groupNumber: 'GRP987654',
      planType: 'PPO',
      coverageLevel: '80/20'
    },
    security: {
      twoFactorEnabled: false,
      lastPasswordChange: '2023-08-15',
      sessionTimeout: 30,
      dataExportEnabled: true
    }
  });
  
  // Load profile data
  useEffect(() => {
    const loadProfile = async () => {
      setLoading(true);
      try {
        // In a real app, you would fetch this from an API
        await new Promise(resolve => setTimeout(resolve, 800));
        
        // Profile would be fetched from API, using mock data for now
        setLoading(false);
      } catch (error) {
        console.error('Error loading profile:', error);
        setErrorMessage('Failed to load profile. Please try again later.');
        setLoading(false);
      }
    };
    
    loadProfile();
  }, []);
  
  // Handle form input changes
  const handleInputChange = (section, field, value) => {
    setProfile(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
    
    // Clear any messages
    setSuccessMessage('');
    setErrorMessage('');
  };
  
  // Handle notification method toggle
  const handleNotificationMethodToggle = (method) => {
    setProfile(prev => {
      const currentMethods = prev.preferences.notificationMethods;
      const newMethods = currentMethods.includes(method)
        ? currentMethods.filter(m => m !== method)
        : [...currentMethods, method];
      
      return {
        ...prev,
        preferences: {
          ...prev.preferences,
          notificationMethods: newMethods
        }
      };
    });
  };
  
  // Save profile changes
  const handleSaveProfile = async () => {
    setSaving(true);
    setSuccessMessage('');
    setErrorMessage('');
    
    try {
      // In a real app, you would send this to an API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setSuccessMessage('Profile updated successfully!');
      
      // Auto-hide success message after 3 seconds
      setTimeout(() => {
        setSuccessMessage('');
      }, 3000);
    } catch (error) {
      console.error('Error saving profile:', error);
      setErrorMessage('Failed to update profile. Please try again.');
    } finally {
      setSaving(false);
    }
  };
  
  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <svg className="animate-spin h-8 w-8 text-emerald-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      </div>
    );
  }
  
  return (
    <div className="space-y-6">
      <div className="bg-white/5 rounded-xl border border-white/10 p-5">
        <h2 className="text-xl font-semibold text-white mb-1">User Profile</h2>
        <p className="text-white/60 text-sm mb-6">
          Manage your personal information and preferences.
        </p>
        
        {/* Status Messages */}
        {successMessage && (
          <motion.div 
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-emerald-900/20 border border-emerald-500/30 text-emerald-200 p-4 rounded-lg mb-6"
          >
            <div className="flex">
              <svg className="w-5 h-5 text-emerald-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span>{successMessage}</span>
            </div>
          </motion.div>
        )}
        
        {errorMessage && (
          <motion.div 
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-red-900/20 border border-red-500/30 text-red-300 p-4 rounded-lg mb-6"
          >
            <div className="flex">
              <svg className="w-5 h-5 text-red-500 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
              <span>{errorMessage}</span>
            </div>
          </motion.div>
        )}
        
        {/* Personal Information Section */}
        <div className="mb-8">
          <h3 className="text-lg font-medium text-white mb-4">Personal Information</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-white/80 mb-2 text-sm font-medium">
                Full Name
              </label>
              <input 
                type="text"
                value={profile.personal.name}
                onChange={(e) => handleInputChange('personal', 'name', e.target.value)}
                className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
              />
            </div>
            
            <div>
              <label className="block text-white/80 mb-2 text-sm font-medium">
                Email Address
              </label>
              <input 
                type="email"
                value={profile.personal.email}
                onChange={(e) => handleInputChange('personal', 'email', e.target.value)}
                className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
              />
            </div>
            
            <div>
              <label className="block text-white/80 mb-2 text-sm font-medium">
                Phone Number
              </label>
              <input 
                type="tel"
                value={profile.personal.phone}
                onChange={(e) => handleInputChange('personal', 'phone', e.target.value)}
                className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>
        
        {/* Preferences Section */}
        <div className="mb-8">
          <h3 className="text-lg font-medium text-white mb-4">Preferences</h3>
          
          <div className="space-y-4 mb-4">
            <div className="flex items-center">
              <input
                id="enableNotifications"
                type="checkbox"
                checked={profile.preferences.enableNotifications}
                onChange={(e) => handleInputChange('preferences', 'enableNotifications', e.target.checked)}
                className="h-4 w-4 rounded border-white/30 bg-white/5 text-emerald-600 focus:ring-emerald-500"
              />
              <label htmlFor="enableNotifications" className="ml-2 text-white">
                Enable notifications
              </label>
            </div>
            
            {profile.preferences.enableNotifications && (
              <div className="ml-6 space-y-3">
                <p className="text-white/70 text-sm">Notification methods:</p>
                <div className="space-x-3">
                  <button
                    onClick={() => handleNotificationMethodToggle('email')}
                    className={`px-3 py-1 text-sm rounded-full ${
                      profile.preferences.notificationMethods.includes('email')
                        ? 'bg-emerald-600 text-white'
                        : 'bg-white/10 text-white/70 hover:bg-white/20'
                    }`}
                  >
                    Email
                  </button>
                  <button
                    onClick={() => handleNotificationMethodToggle('sms')}
                    className={`px-3 py-1 text-sm rounded-full ${
                      profile.preferences.notificationMethods.includes('sms')
                        ? 'bg-emerald-600 text-white'
                        : 'bg-white/10 text-white/70 hover:bg-white/20'
                    }`}
                  >
                    SMS
                  </button>
                  <button
                    onClick={() => handleNotificationMethodToggle('app')}
                    className={`px-3 py-1 text-sm rounded-full ${
                      profile.preferences.notificationMethods.includes('app')
                        ? 'bg-emerald-600 text-white'
                        : 'bg-white/10 text-white/70 hover:bg-white/20'
                    }`}
                  >
                    App Push
                  </button>
                </div>
              </div>
            )}
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-white/80 mb-2 text-sm font-medium">
                Theme
              </label>
              <select
                value={profile.preferences.theme}
                onChange={(e) => handleInputChange('preferences', 'theme', e.target.value)}
                className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
              >
                <option value="dark">Dark</option>
                <option value="light">Light</option>
                <option value="system">Match System</option>
              </select>
            </div>
            
            <div>
              <label className="block text-white/80 mb-2 text-sm font-medium">
                Language
              </label>
              <select
                value={profile.preferences.language}
                onChange={(e) => handleInputChange('preferences', 'language', e.target.value)}
                className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
              >
                <option value="english">English</option>
                <option value="spanish">Spanish</option>
                <option value="french">French</option>
                <option value="german">German</option>
              </select>
            </div>
          </div>
          
          <div className="mt-4">
            <div className="flex items-center">
              <input
                id="autoShareWithDentist"
                type="checkbox"
                checked={profile.preferences.autoShareWithDentist}
                onChange={(e) => handleInputChange('preferences', 'autoShareWithDentist', e.target.checked)}
                disabled={systemSettings?.userPermissions?.enableAutoSharing === false}
                className={`h-4 w-4 rounded border-white/30 bg-white/5 text-emerald-600 focus:ring-emerald-500 ${
                  systemSettings?.userPermissions?.enableAutoSharing === false ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              />
              <label 
                htmlFor="autoShareWithDentist" 
                className={`ml-2 text-white ${
                  systemSettings?.userPermissions?.enableAutoSharing === false ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                Automatically share recordings with my dental provider
              </label>
            </div>
            {systemSettings?.userPermissions?.enableAutoSharing === false && (
              <p className="text-amber-400/80 text-xs mt-1 ml-6">
                This feature has been disabled by your administrator.
              </p>
            )}
          </div>
        </div>
        
        {/* Insurance Information Section */}
        <div className="mb-8">
          <h3 className="text-lg font-medium text-white mb-4">Insurance Information</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-white/80 mb-2 text-sm font-medium">
                Insurance Provider
              </label>
              <input 
                type="text"
                value={profile.insurance.provider}
                onChange={(e) => handleInputChange('insurance', 'provider', e.target.value)}
                className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
              />
            </div>
            
            <div>
              <label className="block text-white/80 mb-2 text-sm font-medium">
                Member ID
              </label>
              <input 
                type="text"
                value={profile.insurance.memberId}
                onChange={(e) => handleInputChange('insurance', 'memberId', e.target.value)}
                className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
              />
            </div>
            
            <div>
              <label className="block text-white/80 mb-2 text-sm font-medium">
                Group Number
              </label>
              <input 
                type="text"
                value={profile.insurance.groupNumber}
                onChange={(e) => handleInputChange('insurance', 'groupNumber', e.target.value)}
                className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
              />
            </div>
            
            <div>
              <label className="block text-white/80 mb-2 text-sm font-medium">
                Plan Type
              </label>
              <select
                value={profile.insurance.planType}
                onChange={(e) => handleInputChange('insurance', 'planType', e.target.value)}
                className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
              >
                <option value="PPO">PPO</option>
                <option value="HMO">HMO</option>
                <option value="EPO">EPO</option>
                <option value="DHMO">DHMO</option>
                <option value="Indemnity">Indemnity</option>
              </select>
            </div>
          </div>
          
          <div className="mt-2 text-white/50 text-xs">
            Your insurance information is used only for comparing benefits and coverage during billing analysis.
          </div>
        </div>
        
        {/* Security Settings */}
        <div className="mb-8">
          <h3 className="text-lg font-medium text-white mb-4">Security Settings</h3>
          
          <div className="space-y-4 mb-4">
            <div className="flex items-center">
              <input
                id="twoFactorEnabled"
                type="checkbox"
                checked={profile.security.twoFactorEnabled}
                onChange={(e) => handleInputChange('security', 'twoFactorEnabled', e.target.checked)}
                className="h-4 w-4 rounded border-white/30 bg-white/5 text-emerald-600 focus:ring-emerald-500"
              />
              <label htmlFor="twoFactorEnabled" className="ml-2 text-white">
                Enable two-factor authentication
              </label>
            </div>
            
            <div className="flex items-center">
              <input
                id="dataExportEnabled"
                type="checkbox"
                checked={profile.security.dataExportEnabled}
                onChange={(e) => handleInputChange('security', 'dataExportEnabled', e.target.checked)}
                disabled={systemSettings?.userPermissions?.enableExport === false}
                className={`h-4 w-4 rounded border-white/30 bg-white/5 text-emerald-600 focus:ring-emerald-500 ${
                  systemSettings?.userPermissions?.enableExport === false ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              />
              <label 
                htmlFor="dataExportEnabled" 
                className={`ml-2 text-white ${
                  systemSettings?.userPermissions?.enableExport === false ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                Allow exporting my data (recordings, transcripts)
              </label>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-white/80 mb-2 text-sm font-medium">
                Session Timeout (minutes)
              </label>
              <select
                value={profile.security.sessionTimeout}
                onChange={(e) => handleInputChange('security', 'sessionTimeout', Number(e.target.value))}
                className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
              >
                <option value={15}>15 minutes</option>
                <option value={30}>30 minutes</option>
                <option value={60}>1 hour</option>
                <option value={120}>2 hours</option>
              </select>
            </div>
            
            <div>
              <label className="block text-white/80 mb-2 text-sm font-medium">
                Password
              </label>
              <button
                type="button"
                className="w-full bg-white/5 hover:bg-white/10 border border-white/10 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent text-left"
              >
                Change Password
                <span className="text-white/50 text-sm ml-2">
                  (Last changed: {new Date(profile.security.lastPasswordChange).toLocaleDateString()})
                </span>
              </button>
            </div>
          </div>
        </div>
        
        {/* Action Buttons */}
        <div className="flex justify-end space-x-4">
          <button
            onClick={() => window.history.back()}
            className="px-4 py-2 text-white/70 hover:text-white bg-transparent hover:bg-white/10 rounded-lg transition-colors"
          >
            Cancel
          </button>
          
          <button
            onClick={handleSaveProfile}
            disabled={saving}
            className={`px-4 py-2 rounded-lg transition-colors flex items-center ${
              saving
                ? 'bg-white/20 text-white/50 cursor-not-allowed'
                : 'bg-emerald-600 hover:bg-emerald-500 text-white'
            }`}
          >
            {saving ? (
              <>
                <svg className="animate-spin h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Saving...
              </>
            ) : (
              'Save Profile'
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default UserProfile; 