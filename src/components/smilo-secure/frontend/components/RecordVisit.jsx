import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { useSecure } from '../SecureContext';

const RecordVisit = ({ onComplete, onCancel }) => {
  const { startRecording, stopRecording, processRecording, currentRecording } = useSecure();
  
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [audioChunks, setAudioChunks] = useState([]);
  const [recordingStep, setRecordingStep] = useState('prepare'); // prepare, recording, review, processing
  const [recordingError, setRecordingError] = useState(null);
  const [visitInfo, setVisitInfo] = useState({
    dentistName: '',
    clinicName: '',
    date: new Date().toISOString().split('T')[0]
  });
  
  const mediaRecorderRef = useRef(null);
  const timerRef = useRef(null);
  const audioRef = useRef(null);
  const canvasRef = useRef(null);
  const streamRef = useRef(null);
  const analyzerRef = useRef(null);
  const animationFrameRef = useRef(null);
  
  // Format seconds to MM:SS
  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };
  
  // Initialize audio visualization
  const setupAudioVisualization = (stream) => {
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const source = audioContext.createMediaStreamSource(stream);
    const analyzer = audioContext.createAnalyser();
    
    analyzer.fftSize = 256;
    source.connect(analyzer);
    analyzerRef.current = analyzer;
    
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    const bufferLength = analyzer.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);
    
    const draw = () => {
      if (!isRecording) return;
      
      animationFrameRef.current = requestAnimationFrame(draw);
      analyzer.getByteFrequencyData(dataArray);
      
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      // Draw visualization
      const barWidth = (canvas.width / bufferLength) * 2.5;
      let x = 0;
      
      for(let i = 0; i < bufferLength; i++) {
        const barHeight = dataArray[i] / 2;
        
        // Create gradient based on volume
        const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
        gradient.addColorStop(0, '#10B981'); // Emerald-500
        gradient.addColorStop(1, '#059669'); // Emerald-600
        
        ctx.fillStyle = gradient;
        ctx.fillRect(x, canvas.height - barHeight, barWidth, barHeight);
        
        x += barWidth + 1;
      }
    };
    
    draw();
  };
  
  // Start recording
  const handleStartRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      streamRef.current = stream;
      
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      
      const chunks = [];
      mediaRecorder.ondataavailable = (e) => {
        if (e.data.size > 0) {
          chunks.push(e.data);
        }
      };
      
      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(chunks, { type: 'audio/webm' });
        const audioUrl = URL.createObjectURL(audioBlob);
        
        if (audioRef.current) {
          audioRef.current.src = audioUrl;
        }
        
        setAudioChunks(chunks);
        setRecordingStep('review');
      };
      
      // Start timer
      setRecordingTime(0);
      timerRef.current = setInterval(() => {
        setRecordingTime(prevTime => prevTime + 1);
      }, 1000);
      
      // Start recording
      mediaRecorder.start();
      setIsRecording(true);
      setRecordingStep('recording');
      
      // Initialize audio visualization
      setupAudioVisualization(stream);
      
      startRecording();
      
    } catch (error) {
      console.error('Error starting recording:', error);
      setRecordingError('Unable to access microphone. Please ensure you have granted permission and have a working microphone connected.');
    }
  };
  
  // Stop recording
  const handleStopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
      
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      
      // Stop all tracks on the stream
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
      }
      
      setIsRecording(false);
      stopRecording(new Blob(audioChunks), recordingTime);
    }
  };
  
  // Process the recording (send for transcription)
  const handleProcessRecording = async () => {
    setRecordingStep('processing');
    
    try {
      // This would normally send the audio to the server for processing
      const visitId = await processRecording(currentRecording?.id);
      
      // Navigate to the transcript view
      if (visitId) {
        onComplete(visitId);
      }
    } catch (error) {
      console.error('Error processing recording:', error);
      setRecordingError('Unable to process the recording. Please try again.');
      setRecordingStep('review');
    }
  };
  
  // Handle user input for visit info
  const handleVisitInfoChange = (e) => {
    const { name, value } = e.target;
    setVisitInfo(prev => ({ ...prev, [name]: value }));
  };
  
  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
      
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
      }
    };
  }, []);
  
  return (
    <div>
      {recordingError && (
        <div className="bg-red-900/20 border border-red-500/30 text-red-300 p-4 rounded-lg mb-6">
          <div className="flex items-start">
            <svg className="w-5 h-5 text-red-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            <div>
              <p className="font-medium mb-1">Recording Error</p>
              <p className="text-sm">{recordingError}</p>
            </div>
          </div>
        </div>
      )}
      
      {/* Preparation Step */}
      {recordingStep === 'prepare' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6"
        >
          <div className="bg-white/5 p-6 rounded-xl border border-white/10">
            <h3 className="text-xl font-semibold text-white mb-4">Visit Information</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-white/80 mb-2 text-sm font-medium">
                  Dentist Name
                </label>
                <input 
                  type="text"
                  name="dentistName"
                  value={visitInfo.dentistName}
                  onChange={handleVisitInfoChange}
                  placeholder="Dr. John Smith"
                  className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                />
              </div>
              
              <div>
                <label className="block text-white/80 mb-2 text-sm font-medium">
                  Dental Clinic
                </label>
                <input 
                  type="text"
                  name="clinicName"
                  value={visitInfo.clinicName}
                  onChange={handleVisitInfoChange}
                  placeholder="Bright Smile Dental"
                  className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                />
              </div>
              
              <div>
                <label className="block text-white/80 mb-2 text-sm font-medium">
                  Visit Date
                </label>
                <input 
                  type="date"
                  name="date"
                  value={visitInfo.date}
                  onChange={handleVisitInfoChange}
                  className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>
          
          <div className="bg-white/5 p-6 rounded-xl border border-white/10">
            <h3 className="text-xl font-semibold text-white mb-4">Recording Guidelines</h3>
            
            <ul className="space-y-3 text-white/80">
              <li className="flex items-start">
                <svg className="w-5 h-5 text-emerald-400 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                Position your microphone close to the conversation but out of the way
              </li>
              <li className="flex items-start">
                <svg className="w-5 h-5 text-emerald-400 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                Record the entire appointment including discussion of procedures and billing
              </li>
              <li className="flex items-start">
                <svg className="w-5 h-5 text-emerald-400 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                Ensure all parties are aware you are recording for your personal records
              </li>
              <li className="flex items-start">
                <svg className="w-5 h-5 text-emerald-400 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                Try to reduce background noise when possible
              </li>
            </ul>
          </div>
          
          <div className="flex space-x-4">
            <button 
              onClick={onCancel}
              className="px-4 py-2 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-colors"
            >
              Cancel
            </button>
            <button 
              onClick={handleStartRecording}
              className="px-4 py-2 bg-emerald-600 hover:bg-emerald-500 text-white rounded-lg transition-colors flex items-center"
              disabled={!visitInfo.dentistName || !visitInfo.clinicName}
            >
              <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
              </svg>
              Start Recording
            </button>
          </div>
        </motion.div>
      )}
      
      {/* Recording Step */}
      {recordingStep === 'recording' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6"
        >
          <div className="bg-white/5 p-6 rounded-xl border border-white/10 text-center">
            <div className="mb-4">
              <div className="inline-flex justify-center items-center w-16 h-16 bg-red-500/20 rounded-full mb-4">
                <div className="w-4 h-4 bg-red-500 rounded-full animate-pulse"></div>
              </div>
              <h3 className="text-2xl font-bold text-white">{formatTime(recordingTime)}</h3>
              <p className="text-white/60 mt-1">Recording in progress</p>
            </div>
            
            <div className="w-full h-24 mb-6">
              <canvas ref={canvasRef} className="w-full h-full"></canvas>
            </div>
            
            <div className="flex justify-center">
              <button 
                onClick={handleStopRecording}
                className="px-6 py-3 bg-red-600 hover:bg-red-500 text-white rounded-full transition-colors flex items-center"
              >
                <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 10a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 01-1-1v-4z" />
                </svg>
                Stop Recording
              </button>
            </div>
          </div>
          
          <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4 text-blue-200">
            <div className="flex">
              <svg className="w-5 h-5 text-blue-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p className="text-sm">
                Your recording is securely encrypted and will be processed locally on your device when possible.
                For best results, record the entire visit including discussions about procedures and billing.
              </p>
            </div>
          </div>
        </motion.div>
      )}
      
      {/* Review Step */}
      {recordingStep === 'review' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6"
        >
          <div className="bg-white/5 p-6 rounded-xl border border-white/10">
            <h3 className="text-xl font-semibold text-white mb-4">Review Recording</h3>
            
            <div className="mb-6">
              <p className="text-white/70 mb-2">Total Duration: <span className="text-white font-medium">{formatTime(recordingTime)}</span></p>
              
              <div className="w-full bg-white/5 rounded-lg p-4 flex items-center justify-center">
                <audio ref={audioRef} controls className="w-full"></audio>
              </div>
            </div>
            
            <div className="bg-emerald-900/20 border border-emerald-500/30 rounded-lg p-4 text-emerald-200 mb-6">
              <div className="flex">
                <svg className="w-5 h-5 text-emerald-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div>
                  <p className="text-sm mb-1 font-medium">Ready for Transcription</p>
                  <p className="text-sm">
                    Your recording will be processed to create a transcript and extract dental procedures mentioned.
                    This will be used to compare against your billing statement later.
                  </p>
                </div>
              </div>
            </div>
            
            <div className="flex space-x-4">
              <button 
                onClick={onCancel}
                className="px-4 py-2 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-colors"
              >
                Discard
              </button>
              <button 
                onClick={handleProcessRecording}
                className="flex-1 px-4 py-2 bg-emerald-600 hover:bg-emerald-500 text-white rounded-lg transition-colors flex items-center justify-center"
              >
                <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </svg>
                Process Recording
              </button>
            </div>
          </div>
        </motion.div>
      )}
      
      {/* Processing Step */}
      {recordingStep === 'processing' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center py-12"
        >
          <div className="inline-block mb-8">
            <svg className="animate-spin h-16 w-16 text-emerald-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </div>
          
          <h3 className="text-2xl font-bold text-white mb-2">Processing Your Recording</h3>
          <p className="text-white/70 max-w-md mx-auto">
            Your audio is being transcribed and analyzed. This process may take a minute or two depending on the
            length of the recording.
          </p>
        </motion.div>
      )}
    </div>
  );
};

export default RecordVisit; 