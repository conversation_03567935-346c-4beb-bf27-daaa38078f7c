import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useSecure } from '../SecureContext';

const VisitHistory = ({ onSelectVisit }) => {
  const { visits, fetchVisits } = useSecure();
  const [loading, setLoading] = useState(true);
  const [sortBy, setSortBy] = useState('date');
  const [sortOrder, setSortOrder] = useState('desc');
  const [filteredVisits, setFilteredVisits] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filter, setFilter] = useState('all'); // all, flagged, complete
  
  // Load visits data
  useEffect(() => {
    const loadVisits = async () => {
      setLoading(true);
      try {
        await fetchVisits();
      } catch (error) {
        console.error('Error loading visits:', error);
      } finally {
        setLoading(false);
      }
    };
    
    loadVisits();
  }, [fetchVisits]);
  
  // Filter and sort visits
  useEffect(() => {
    if (!visits) return;
    
    let result = [...visits];
    
    // Apply filter
    if (filter === 'flagged') {
      result = result.filter(visit => 
        visit.billingAnalysis && 
        visit.billingAnalysis.flaggedItems && 
        visit.billingAnalysis.flaggedItems.length > 0
      );
    } else if (filter === 'complete') {
      result = result.filter(visit => 
        visit.status === 'processed' && 
        visit.transcript && 
        visit.billingAnalysis
      );
    }
    
    // Apply search
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      result = result.filter(visit => 
        visit.dentistName.toLowerCase().includes(term) ||
        visit.clinic.toLowerCase().includes(term) ||
        (visit.transcript && visit.transcript.toLowerCase().includes(term))
      );
    }
    
    // Apply sort
    result.sort((a, b) => {
      let valueA, valueB;
      
      switch (sortBy) {
        case 'date':
          valueA = new Date(a.date);
          valueB = new Date(b.date);
          break;
        case 'dentist':
          valueA = a.dentistName;
          valueB = b.dentistName;
          break;
        case 'clinic':
          valueA = a.clinic;
          valueB = b.clinic;
          break;
        case 'status':
          valueA = a.status;
          valueB = b.status;
          break;
        default:
          valueA = new Date(a.date);
          valueB = new Date(b.date);
      }
      
      if (sortOrder === 'asc') {
        return valueA > valueB ? 1 : -1;
      } else {
        return valueA < valueB ? 1 : -1;
      }
    });
    
    setFilteredVisits(result);
  }, [visits, sortBy, sortOrder, searchTerm, filter]);
  
  // Toggle sort order and update sort field
  const handleSort = (field) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('desc');
    }
  };
  
  // Format date for display
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };
  
  // Get status badge style based on status
  const getStatusBadge = (status) => {
    switch (status) {
      case 'recording':
        return 'bg-blue-900/50 text-blue-300';
      case 'processing':
        return 'bg-amber-900/50 text-amber-300';
      case 'processed':
        return 'bg-emerald-900/50 text-emerald-300';
      case 'error':
        return 'bg-red-900/50 text-red-300';
      default:
        return 'bg-gray-900/50 text-gray-300';
    }
  };
  
  // Get formatted status label
  const getStatusLabel = (status) => {
    switch (status) {
      case 'recording':
        return 'Recording';
      case 'processing':
        return 'Processing';
      case 'processed':
        return 'Complete';
      case 'error':
        return 'Error';
      default:
        return 'Unknown';
    }
  };
  
  // Render flag icon if visit has billing issues
  const renderFlaggedStatus = (visit) => {
    if (
      visit.billingAnalysis && 
      visit.billingAnalysis.flaggedItems && 
      visit.billingAnalysis.flaggedItems.length > 0
    ) {
      return (
        <div className="rounded-full bg-red-900/30 p-1 w-6 h-6 flex items-center justify-center" title="Potential billing issues detected">
          <svg className="w-4 h-4 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 21v-4m0 0V5a2 2 0 012-2h6.5l1 1H21l-3 6 3 6h-8.5l-1-1H5a2 2 0 00-2 2zm9-13.5V9" />
          </svg>
        </div>
      );
    }
    return null;
  };
  
  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <svg className="animate-spin h-8 w-8 text-emerald-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      </div>
    );
  }
  
  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <input
            type="text"
            placeholder="Search visits..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full bg-white/5 border border-white/10 rounded-lg pl-10 py-2 text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
          />
          <svg 
            className="absolute left-3 top-2.5 w-5 h-5 text-white/50" 
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
        
        <div className="flex">
          <button
            onClick={() => setFilter('all')}
            className={`px-4 py-2 rounded-l-lg border-r border-white/5 ${
              filter === 'all' 
                ? 'bg-emerald-600 hover:bg-emerald-500 text-white' 
                : 'bg-white/5 hover:bg-white/10 text-white/70 hover:text-white'
            }`}
          >
            All
          </button>
          <button
            onClick={() => setFilter('complete')}
            className={`px-4 py-2 border-r border-white/5 ${
              filter === 'complete' 
                ? 'bg-emerald-600 hover:bg-emerald-500 text-white' 
                : 'bg-white/5 hover:bg-white/10 text-white/70 hover:text-white'
            }`}
          >
            Complete
          </button>
          <button
            onClick={() => setFilter('flagged')}
            className={`px-4 py-2 rounded-r-lg flex items-center ${
              filter === 'flagged' 
                ? 'bg-emerald-600 hover:bg-emerald-500 text-white' 
                : 'bg-white/5 hover:bg-white/10 text-white/70 hover:text-white'
            }`}
          >
            <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 21v-4m0 0V5a2 2 0 012-2h6.5l1 1H21l-3 6 3 6h-8.5l-1-1H5a2 2 0 00-2 2zm9-13.5V9" />
            </svg>
            Flagged
          </button>
        </div>
      </div>
      
      {/* Visit List */}
      {filteredVisits.length === 0 ? (
        <div className="bg-white/5 rounded-xl border border-white/10 p-8 text-center">
          <div className="inline-flex justify-center items-center w-16 h-16 bg-white/5 rounded-full mb-4">
            <svg className="w-8 h-8 text-white/40" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-xl font-semibold text-white mb-2">No visits found</h3>
          <p className="text-white/60 max-w-md mx-auto">
            {searchTerm || filter !== 'all' 
              ? 'Try adjusting your search or filters to see more visits.' 
              : 'You have not recorded any dental visits yet. Start by recording your next visit.'}
          </p>
        </div>
      ) : (
        <div className="bg-white/5 rounded-xl border border-white/10 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-white/10 text-white/70 text-left text-sm">
                  <th className="py-3 px-4">
                    <button 
                      onClick={() => handleSort('date')}
                      className="flex items-center focus:outline-none"
                    >
                      Date
                      {sortBy === 'date' && (
                        <svg 
                          className={`ml-1 w-4 h-4 transition-transform ${sortOrder === 'desc' ? '' : 'transform rotate-180'}`} 
                          fill="none" 
                          viewBox="0 0 24 24" 
                          stroke="currentColor"
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      )}
                    </button>
                  </th>
                  <th className="py-3 px-4">
                    <button 
                      onClick={() => handleSort('dentist')}
                      className="flex items-center focus:outline-none"
                    >
                      Dentist
                      {sortBy === 'dentist' && (
                        <svg 
                          className={`ml-1 w-4 h-4 transition-transform ${sortOrder === 'desc' ? '' : 'transform rotate-180'}`} 
                          fill="none" 
                          viewBox="0 0 24 24" 
                          stroke="currentColor"
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      )}
                    </button>
                  </th>
                  <th className="py-3 px-4">
                    <button 
                      onClick={() => handleSort('clinic')}
                      className="flex items-center focus:outline-none"
                    >
                      Clinic
                      {sortBy === 'clinic' && (
                        <svg 
                          className={`ml-1 w-4 h-4 transition-transform ${sortOrder === 'desc' ? '' : 'transform rotate-180'}`} 
                          fill="none" 
                          viewBox="0 0 24 24" 
                          stroke="currentColor"
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      )}
                    </button>
                  </th>
                  <th className="py-3 px-4">
                    <button 
                      onClick={() => handleSort('status')}
                      className="flex items-center focus:outline-none"
                    >
                      Status
                      {sortBy === 'status' && (
                        <svg 
                          className={`ml-1 w-4 h-4 transition-transform ${sortOrder === 'desc' ? '' : 'transform rotate-180'}`} 
                          fill="none" 
                          viewBox="0 0 24 24" 
                          stroke="currentColor"
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      )}
                    </button>
                  </th>
                  <th className="py-3 px-4 text-center">Issues</th>
                  <th className="py-3 px-4 sr-only">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredVisits.map((visit) => (
                  <motion.tr 
                    key={visit.id}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    whileHover={{ backgroundColor: 'rgba(255, 255, 255, 0.05)' }}
                    className="border-b border-white/10 text-white cursor-pointer"
                    onClick={() => onSelectVisit(visit.id)}
                  >
                    <td className="py-4 px-4">{formatDate(visit.date)}</td>
                    <td className="py-4 px-4">{visit.dentistName}</td>
                    <td className="py-4 px-4">{visit.clinic}</td>
                    <td className="py-4 px-4">
                      <span className={`px-2 py-1 text-xs rounded-full ${getStatusBadge(visit.status)}`}>
                        {getStatusLabel(visit.status)}
                      </span>
                    </td>
                    <td className="py-4 px-4 text-center">
                      {renderFlaggedStatus(visit)}
                    </td>
                    <td className="py-4 px-4 text-right">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          onSelectVisit(visit.id);
                        }}
                        className="text-emerald-400 hover:text-emerald-300 p-1"
                        aria-label="View visit details"
                      >
                        <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                      </button>
                    </td>
                  </motion.tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
};

export default VisitHistory; 