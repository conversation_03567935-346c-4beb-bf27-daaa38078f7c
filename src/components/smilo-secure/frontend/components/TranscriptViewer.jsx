import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { useSecure } from '../SecureContext';

const TranscriptViewer = ({ visitId, onBack }) => {
  const { getVisitById } = useSecure();
  const [visit, setVisit] = useState(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [playbackSpeed, setPlaybackSpeed] = useState(1);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [activeSegmentIndex, setActiveSegmentIndex] = useState(-1);
  const [highlightedSegments, setHighlightedSegments] = useState([]);
  
  const audioRef = useRef(null);
  const transcriptContainerRef = useRef(null);
  const segmentRefs = useRef([]);
  
  // Load visit data
  useEffect(() => {
    const loadVisit = async () => {
      setLoading(true);
      try {
        const visitData = await getVisitById(visitId);
        setVisit(visitData);
        
        // Pre-process transcript segments if they exist
        if (visitData.transcript && visitData.transcript.segments) {
          // Create refs for each segment
          segmentRefs.current = visitData.transcript.segments.map(() => React.createRef());
        }
      } catch (error) {
        console.error('Error loading visit:', error);
      } finally {
        setLoading(false);
      }
    };
    
    loadVisit();
  }, [visitId, getVisitById]);
  
  // Format time (seconds) to MM:SS format
  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };
  
  // Handle audio playback
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;
    
    const handleTimeUpdate = () => {
      setCurrentTime(audio.currentTime);
      
      // Find the currently active segment
      if (visit?.transcript?.segments) {
        const activeIndex = visit.transcript.segments.findIndex(
          (segment, index, segments) => {
            const nextSegment = segments[index + 1];
            const startTime = segment.startTime;
            const endTime = nextSegment ? nextSegment.startTime : segment.endTime || audio.duration;
            
            return audio.currentTime >= startTime && audio.currentTime < endTime;
          }
        );
        
        if (activeIndex !== -1 && activeIndex !== activeSegmentIndex) {
          setActiveSegmentIndex(activeIndex);
          
          // Scroll to active segment
          if (segmentRefs.current[activeIndex]?.current && transcriptContainerRef.current) {
            const container = transcriptContainerRef.current;
            const element = segmentRefs.current[activeIndex].current;
            
            container.scrollTop = element.offsetTop - container.offsetTop - (container.clientHeight / 2) + (element.clientHeight / 2);
          }
        }
      }
    };
    
    const handlePlayPause = () => {
      setIsPlaying(!audio.paused);
    };
    
    const handleEnded = () => {
      setIsPlaying(false);
      setActiveSegmentIndex(-1);
    };
    
    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('play', handlePlayPause);
    audio.addEventListener('pause', handlePlayPause);
    audio.addEventListener('ended', handleEnded);
    
    // Set playback speed
    audio.playbackRate = playbackSpeed;
    
    return () => {
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('play', handlePlayPause);
      audio.removeEventListener('pause', handlePlayPause);
      audio.removeEventListener('ended', handleEnded);
    };
  }, [visit, playbackSpeed, activeSegmentIndex]);
  
  // Search in transcript
  useEffect(() => {
    if (!searchTerm || !visit?.transcript?.segments) {
      setHighlightedSegments([]);
      return;
    }
    
    const term = searchTerm.toLowerCase();
    const matches = visit.transcript.segments
      .map((segment, index) => {
        if (segment.text.toLowerCase().includes(term)) {
          return index;
        }
        return -1;
      })
      .filter(index => index !== -1);
    
    setHighlightedSegments(matches);
  }, [searchTerm, visit]);
  
  // Play/pause audio
  const togglePlayPause = () => {
    const audio = audioRef.current;
    if (!audio) return;
    
    if (audio.paused) {
      audio.play();
    } else {
      audio.pause();
    }
  };
  
  // Jump to specific segment
  const jumpToSegment = (index) => {
    const audio = audioRef.current;
    if (!audio || !visit?.transcript?.segments) return;
    
    const segment = visit.transcript.segments[index];
    if (segment) {
      audio.currentTime = segment.startTime;
      
      if (audio.paused) {
        audio.play();
      }
    }
  };
  
  // Handle playback speed change
  const handleSpeedChange = (speed) => {
    setPlaybackSpeed(speed);
    
    if (audioRef.current) {
      audioRef.current.playbackRate = speed;
    }
  };
  
  // Jump to next search result
  const jumpToNextResult = () => {
    if (highlightedSegments.length === 0) return;
    
    // Find the next highlight after current segment
    const nextIndex = highlightedSegments.find(index => index > activeSegmentIndex);
    
    if (nextIndex !== undefined) {
      jumpToSegment(nextIndex);
    } else if (highlightedSegments.length > 0) {
      // Loop back to first result
      jumpToSegment(highlightedSegments[0]);
    }
  };
  
  // Jump to previous search result
  const jumpToPrevResult = () => {
    if (highlightedSegments.length === 0) return;
    
    // Find segments before current one, take the last one
    const prevResults = highlightedSegments.filter(index => index < activeSegmentIndex);
    
    if (prevResults.length > 0) {
      jumpToSegment(prevResults[prevResults.length - 1]);
    } else if (highlightedSegments.length > 0) {
      // Loop to last result
      jumpToSegment(highlightedSegments[highlightedSegments.length - 1]);
    }
  };
  
  // Loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <svg className="animate-spin h-8 w-8 text-emerald-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      </div>
    );
  }
  
  // Visit not found
  if (!visit) {
    return (
      <div className="text-center py-8">
        <p className="text-red-400">Visit not found or an error occurred.</p>
        <button 
          onClick={onBack}
          className="mt-4 px-4 py-2 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-colors"
        >
          Go Back
        </button>
      </div>
    );
  }
  
  // Transcript not available
  if (!visit.transcript || !visit.transcript.segments || visit.transcript.segments.length === 0) {
    return (
      <div className="bg-white/5 rounded-xl border border-white/10 p-8 text-center">
        <div className="inline-flex justify-center items-center w-16 h-16 bg-white/5 rounded-full mb-4">
          <svg className="w-8 h-8 text-white/40" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <h3 className="text-xl font-semibold text-white mb-2">Transcript not available</h3>
        <p className="text-white/60 max-w-md mx-auto mb-6">
          {visit.status === 'processing' 
            ? 'The visit recording is still being processed. Please check back soon.'
            : 'The transcript for this visit could not be generated. This might be due to an error or insufficient audio quality.'}
        </p>
        <button 
          onClick={onBack}
          className="px-4 py-2 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-colors"
        >
          Go Back
        </button>
      </div>
    );
  }
  
  return (
    <div className="space-y-6">
      {/* Visit Info Card */}
      <div className="bg-white/5 rounded-xl border border-white/10 p-5">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <p className="text-white/60 text-sm">Dentist</p>
            <p className="text-white font-medium">{visit.dentistName}</p>
          </div>
          <div>
            <p className="text-white/60 text-sm">Clinic</p>
            <p className="text-white font-medium">{visit.clinic}</p>
          </div>
          <div>
            <p className="text-white/60 text-sm">Date</p>
            <p className="text-white font-medium">{new Date(visit.date).toLocaleDateString()}</p>
          </div>
        </div>
      </div>
      
      {/* Audio Player */}
      <div className="bg-white/5 rounded-xl border border-white/10 overflow-hidden">
        <div className="p-4 border-b border-white/10">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <button
                onClick={togglePlayPause}
                className="w-10 h-10 flex items-center justify-center bg-emerald-600 hover:bg-emerald-500 text-white rounded-full transition-colors"
                aria-label={isPlaying ? 'Pause' : 'Play'}
              >
                {isPlaying ? (
                  <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                ) : (
                  <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                )}
              </button>
              
              <div className="text-white/70 font-medium">
                {formatTime(currentTime)} / {formatTime(audioRef.current?.duration || 0)}
              </div>
            </div>
            
            <div className="flex items-center">
              <div className="mr-3 text-white/70 text-sm">Speed:</div>
              <div className="flex space-x-1">
                {[0.5, 1, 1.25, 1.5, 2].map((speed) => (
                  <button
                    key={speed}
                    onClick={() => handleSpeedChange(speed)}
                    className={`px-2 py-1 text-xs rounded ${
                      playbackSpeed === speed
                        ? 'bg-emerald-600 text-white'
                        : 'bg-white/10 text-white/70 hover:bg-white/20 hover:text-white'
                    }`}
                  >
                    {speed}x
                  </button>
                ))}
              </div>
            </div>
          </div>
          
          {/* Audio element */}
          <audio 
            ref={audioRef}
            src={visit.audioUrl}
            className="hidden"
            preload="auto"
          />
        </div>
        
        {/* Transcript search */}
        <div className="p-4 border-b border-white/10">
          <div className="relative">
            <input
              type="text"
              placeholder="Search in transcript..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full bg-white/5 border border-white/10 rounded-lg pl-10 pr-24 py-2 text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
            />
            <svg 
              className="absolute left-3 top-2.5 w-5 h-5 text-white/50" 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            
            {searchTerm && (
              <div className="absolute right-2 top-1.5 flex space-x-1">
                <button
                  onClick={jumpToPrevResult}
                  className="p-1 text-white/70 hover:text-white"
                  title="Previous result"
                  disabled={highlightedSegments.length === 0}
                >
                  <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </button>
                <button
                  onClick={jumpToNextResult}
                  className="p-1 text-white/70 hover:text-white"
                  title="Next result"
                  disabled={highlightedSegments.length === 0}
                >
                  <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </button>
                <span className="px-2 bg-white/10 rounded text-xs flex items-center text-white/70 mr-1">
                  {highlightedSegments.length} {highlightedSegments.length === 1 ? 'match' : 'matches'}
                </span>
              </div>
            )}
          </div>
        </div>
        
        {/* Transcript content */}
        <div 
          className="h-96 overflow-y-auto p-4 space-y-4"
          ref={transcriptContainerRef}
        >
          {visit.transcript.segments.map((segment, index) => {
            const isActive = index === activeSegmentIndex;
            const isHighlighted = highlightedSegments.includes(index);
            const speaker = segment.speaker || 'Unknown';
            
            // Helper to highlight search term
            const highlightText = (text, term) => {
              if (!term) return text;
              
              const parts = text.split(new RegExp(`(${term})`, 'gi'));
              return parts.map((part, i) => 
                part.toLowerCase() === term.toLowerCase() 
                  ? <mark key={i} className="bg-emerald-900/30 text-emerald-200 px-0.5 rounded">{part}</mark>
                  : part
              );
            };
            
            return (
              <motion.div
                key={index}
                ref={segmentRefs.current[index]}
                initial={false}
                animate={{
                  backgroundColor: isActive 
                    ? 'rgba(16, 185, 129, 0.1)' 
                    : isHighlighted 
                      ? 'rgba(16, 185, 129, 0.05)'
                      : 'rgba(255, 255, 255, 0)',
                  borderColor: isActive 
                    ? 'rgba(16, 185, 129, 0.3)' 
                    : isHighlighted 
                      ? 'rgba(16, 185, 129, 0.15)'
                      : 'rgba(255, 255, 255, 0.05)'
                }}
                className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                  isActive 
                    ? 'bg-emerald-900/10 border-emerald-500/30' 
                    : isHighlighted 
                      ? 'bg-emerald-900/5 border-emerald-500/15'
                      : 'bg-white/5 border-white/5 hover:bg-white/10'
                }`}
                onClick={() => jumpToSegment(index)}
              >
                <div className="flex justify-between items-center mb-1">
                  <div className="flex items-center">
                    <span className={`px-2 py-0.5 text-xs rounded font-medium ${
                      speaker.toLowerCase().includes('dentist') 
                        ? 'bg-blue-900/50 text-blue-300' 
                        : speaker.toLowerCase().includes('patient')
                          ? 'bg-purple-900/50 text-purple-300'
                          : 'bg-gray-900/50 text-gray-300'
                    }`}>
                      {speaker}
                    </span>
                  </div>
                  <span className="text-white/40 text-xs">{formatTime(segment.startTime)}</span>
                </div>
                <p className="text-white/90">
                  {highlightText(segment.text, searchTerm)}
                </p>
              </motion.div>
            );
          })}
        </div>
      </div>
      
      {/* Identified Procedures */}
      {visit.transcript.identifiedProcedures && visit.transcript.identifiedProcedures.length > 0 && (
        <div className="bg-white/5 rounded-xl border border-white/10 p-5">
          <h3 className="text-lg font-semibold text-white mb-4">Identified Dental Procedures</h3>
          
          <div className="space-y-3">
            {visit.transcript.identifiedProcedures.map((procedure, index) => (
              <div key={index} className="bg-white/5 rounded-lg border border-white/10 p-3">
                <div className="flex justify-between items-start">
                  <div>
                    <div className="flex items-center">
                      <span className="text-emerald-400 font-medium">
                        {procedure.code && `${procedure.code} - `}{procedure.name}
                      </span>
                      {procedure.confidence && (
                        <span className={`ml-2 text-xs px-2 py-0.5 rounded-full ${
                          procedure.confidence >= 0.8 
                            ? 'bg-emerald-900/50 text-emerald-300' 
                            : procedure.confidence >= 0.5 
                              ? 'bg-amber-900/50 text-amber-300'
                              : 'bg-red-900/50 text-red-300'
                        }`}>
                          {Math.round(procedure.confidence * 100)}% confidence
                        </span>
                      )}
                    </div>
                    {procedure.description && (
                      <p className="text-white/70 text-sm mt-1">{procedure.description}</p>
                    )}
                  </div>
                  {procedure.timestamp && (
                    <button 
                      onClick={() => {
                        const audio = audioRef.current;
                        if (audio) {
                          audio.currentTime = procedure.timestamp;
                          audio.play();
                        }
                      }}
                      className="ml-2 p-1 text-emerald-400 hover:text-emerald-300 rounded-full"
                      title="Jump to mention"
                    >
                      <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15.536a5 5 0 01-.707-7.072m-2.121 9.9a9 9 0 010-12.728M6 18h.01M6 12h.01M6 6h.01" />
                      </svg>
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default TranscriptViewer;