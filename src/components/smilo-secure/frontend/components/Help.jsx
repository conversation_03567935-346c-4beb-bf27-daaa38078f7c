import React, { useState } from 'react';
import { motion } from 'framer-motion';

const Help = () => {
  const [activeCategory, setActiveCategory] = useState('general');
  const [expandedQuestion, setExpandedQuestion] = useState(null);
  
  const categories = [
    { id: 'general', name: 'General' },
    { id: 'recording', name: 'Recording Visits' },
    { id: 'billing', name: 'Billing Analysis' },
    { id: 'privacy', name: 'Privacy & Security' },
    { id: 'troubleshooting', name: 'Troubleshooting' }
  ];
  
  const faqs = {
    general: [
      {
        id: 'what-is-smilo-secure',
        question: 'What is Smi<PERSON> Secure?',
        answer: 'Smilo Secure is a feature designed to protect dental patients from billing fraud and errors. It allows you to record dental visits, automatically transcribe conversations, analyze procedures discussed, and compare them with billing statements to identify potential discrepancies.'
      },
      {
        id: 'how-does-it-work',
        question: 'How does Smilo Secure work?',
        answer: 'During your dental appointment, you can use the Record Visit feature to capture audio. Our AI-powered system transcribes the conversation, identifies dental procedures discussed, and stores this information securely. When you receive a bill, you can upload it for comparison with what was discussed during your visit. The system will flag any discrepancies or potential issues.'
      },
      {
        id: 'is-it-legal',
        question: 'Is it legal to record my dental visits?',
        answer: 'Recording laws vary by location. Many states in the US allow recording if one party (you) consents, but some require all parties to consent. We recommend informing your dental provider that you\'re recording the visit. Most dental professionals understand and support transparency in billing and procedures.'
      }
    ],
    recording: [
      {
        id: 'how-to-record',
        question: 'How do I record a dental visit?',
        answer: 'To record a visit, navigate to the Record Visit section, provide basic details about your appointment, then click the "Start Recording" button when you\'re ready. Make sure your device\'s microphone is enabled and place it where it can clearly capture the conversation. When the visit is complete, click "Stop Recording" and follow the prompts to process the recording.'
      },
      {
        id: 'recording-quality',
        question: 'How can I ensure good recording quality?',
        answer: 'For best results, place your device (phone or tablet) in an open area near where the conversation will take place. Avoid covering the microphone or placing it in a pocket or bag. Before your important visit, you can test the recording in a quiet environment to ensure it captures audio clearly. Consider using an external microphone for improved quality.'
      },
      {
        id: 'recording-time-limit',
        question: 'Is there a time limit for recordings?',
        answer: 'Yes, recordings are limited to a maximum duration set by your system administrator (typically 60 minutes). For longer appointments, you may need to stop and start a new recording. You\'ll receive a notification when you\'re approaching the time limit.'
      }
    ],
    billing: [
      {
        id: 'how-to-compare-bills',
        question: 'How do I compare my dental bill with what was discussed?',
        answer: 'After receiving your dental bill, go to the Visit History section and select the relevant visit. Click on "Billing Analysis" and enter the details from your bill, including procedure codes, descriptions, and amounts. The system will automatically compare this information with the procedures identified in your visit recording and highlight any discrepancies.'
      },
      {
        id: 'what-are-flags',
        question: 'What do the different flag colors mean?',
        answer: 'Red flags indicate high-severity issues where a billed procedure was not discussed or there\'s a significant price discrepancy. Yellow flags indicate medium-severity issues that merit attention but may have explanations. Green items match the discussion from your visit. We recommend following up with your dental provider on any red or yellow items.'
      },
      {
        id: 'insurance-coverage',
        question: 'Can Smilo Secure tell me if a procedure is covered by my insurance?',
        answer: 'Smilo Secure is primarily designed to compare bills with what was discussed during visits, not to verify insurance coverage. However, if you enter your insurance information in your profile, the system may provide general coverage estimates for common procedures based on your plan type. For accurate coverage information, contact your insurance provider directly.'
      }
    ],
    privacy: [
      {
        id: 'data-security',
        question: 'How is my data protected?',
        answer: 'All recordings and transcripts are encrypted end-to-end, meaning only you can access them with your account credentials. We use industry-standard encryption protocols to secure data both in transit and at rest. Your data is never sold or shared with third parties unless you explicitly choose to share specific visits with your dental provider.'
      },
      {
        id: 'data-retention',
        question: 'How long is my data stored?',
        answer: 'By default, visit recordings and associated data are stored for 3 years, which covers the typical statute of limitations for billing disputes. You can delete recordings manually at any time. System backups may retain deleted data for up to 30 days for disaster recovery purposes.'
      },
      {
        id: 'sharing-data',
        question: 'Can I share my recordings with my dentist or insurance company?',
        answer: 'Yes, you can share specific recordings with your dental provider or insurance company directly from the Visit History page. When you share, you can choose to include just the transcript, the audio, or both. The recipient will receive a secure link to access the shared information, which expires after a set period of time.'
      }
    ],
    troubleshooting: [
      {
        id: 'mic-issues',
        question: 'My microphone isn\'t working during recording',
        answer: 'First, ensure you\'ve granted microphone permissions to your browser or app. Check that no other applications are using the microphone. Try refreshing the page or restarting the app. If problems persist, try using a different device or an external microphone. Make sure your device\'s operating system is up-to-date, as outdated systems may have compatibility issues.'
      },
      {
        id: 'transcription-errors',
        question: 'The transcription has errors or missing parts',
        answer: 'Transcription quality depends on audio clarity and background noise. If there are errors, you can manually edit the transcript from the Visit History page. For future recordings, try to reduce background noise, ensure the microphone is positioned closer to the conversation, and speak clearly. Our AI continuously improves with more data, so transcription quality should improve over time.'
      },
      {
        id: 'cant-upload-bill',
        question: 'I can\'t upload or enter my bill information',
        answer: 'If you\'re having trouble entering bill information, first ensure all fields are in the correct format. For procedure codes, don\'t include spaces (e.g., use "D2740" not "D 2740"). If you\'re uploading a PDF bill, make sure it\'s not password-protected. Try using a different browser or device. If problems persist, you can contact support with details about the error you\'re experiencing.'
      }
    ]
  };
  
  const videoTutorials = [
    {
      id: 'getting-started',
      title: 'Getting Started with Smilo Secure',
      duration: '3:45',
      thumbnail: '/assets/videos/getting-started-thumb.jpg',
      url: 'https://example.com/tutorials/getting-started'
    },
    {
      id: 'recording-visit',
      title: 'How to Record a Dental Visit',
      duration: '2:30',
      thumbnail: '/assets/videos/recording-visit-thumb.jpg',
      url: 'https://example.com/tutorials/recording-visit'
    },
    {
      id: 'analyzing-bills',
      title: 'Understanding Billing Analysis',
      duration: '4:15',
      thumbnail: '/assets/videos/analyzing-bills-thumb.jpg',
      url: 'https://example.com/tutorials/analyzing-bills'
    }
  ];
  
  const toggleQuestion = (id) => {
    if (expandedQuestion === id) {
      setExpandedQuestion(null);
    } else {
      setExpandedQuestion(id);
    }
  };
  
  return (
    <div className="space-y-6">
      <div className="bg-white/5 rounded-xl border border-white/10 p-5">
        <h2 className="text-xl font-semibold text-white mb-1">Help & Resources</h2>
        <p className="text-white/60 text-sm mb-6">
          Find answers, learn about features, and get support for Smilo Secure.
        </p>
        
        {/* Category Tabs */}
        <div className="border-b border-white/10 mb-6">
          <div className="flex flex-wrap -mb-px">
            {categories.map(category => (
              <button
                key={category.id}
                onClick={() => setActiveCategory(category.id)}
                className={`mr-4 py-2 px-1 border-b-2 font-medium text-sm ${
                  activeCategory === category.id
                    ? 'border-emerald-500 text-emerald-500'
                    : 'border-transparent text-white/60 hover:text-white/80 hover:border-white/20'
                }`}
              >
                {category.name}
              </button>
            ))}
          </div>
        </div>
        
        {/* FAQs */}
        <div className="mb-10">
          <h3 className="text-lg font-medium text-white mb-4">Frequently Asked Questions</h3>
          
          <div className="space-y-4">
            {faqs[activeCategory] && faqs[activeCategory].map(faq => (
              <div 
                key={faq.id}
                className="border border-white/10 rounded-lg overflow-hidden"
              >
                <button
                  onClick={() => toggleQuestion(faq.id)}
                  className="w-full text-left px-6 py-4 flex justify-between items-center hover:bg-white/5 transition-colors"
                >
                  <span className="font-medium text-white">{faq.question}</span>
                  <svg 
                    className={`w-5 h-5 text-white/60 transform transition-transform ${expandedQuestion === faq.id ? 'rotate-180' : ''}`}
                    fill="none" 
                    viewBox="0 0 24 24" 
                    stroke="currentColor"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
                
                {expandedQuestion === faq.id && (
                  <motion.div 
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                    className="px-6 py-4 bg-white/5 border-t border-white/10"
                  >
                    <p className="text-white/80">{faq.answer}</p>
                  </motion.div>
                )}
              </div>
            ))}
          </div>
        </div>
        
        {/* Video Tutorials */}
        <div className="mb-10">
          <h3 className="text-lg font-medium text-white mb-4">Video Tutorials</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {videoTutorials.map(tutorial => (
              <div 
                key={tutorial.id}
                className="bg-white/5 border border-white/10 rounded-lg overflow-hidden hover:bg-white/10 transition-colors"
              >
                <div className="relative pb-[56.25%]">
                  <div className="absolute inset-0 bg-black/40 flex items-center justify-center">
                    <div className="w-16 h-16 rounded-full bg-emerald-500/90 flex items-center justify-center">
                      <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M8 5v14l11-7z" />
                      </svg>
                    </div>
                  </div>
                  <img 
                    src={tutorial.thumbnail || 'https://via.placeholder.com/640x360'} 
                    alt={tutorial.title}
                    className="absolute inset-0 w-full h-full object-cover"
                  />
                </div>
                <div className="p-4">
                  <h4 className="font-medium text-white mb-1">{tutorial.title}</h4>
                  <p className="text-white/60 text-sm">{tutorial.duration}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
        
        {/* Contact Support */}
        <div>
          <h3 className="text-lg font-medium text-white mb-4">Need More Help?</h3>
          
          <div className="bg-white/5 border border-white/10 rounded-lg p-5">
            <p className="text-white/80 mb-4">
              If you couldn't find an answer to your question, our support team is here to help you.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4">
              <a 
                href="mailto:<EMAIL>" 
                className="flex items-center justify-center px-4 py-2 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-colors"
              >
                <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                Email Support
              </a>
              
              <a 
                href="#" 
                className="flex items-center justify-center px-4 py-2 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-colors"
              >
                <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                </svg>
                Live Chat
              </a>
              
              <a 
                href="tel:+18005551234" 
                className="flex items-center justify-center px-4 py-2 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-colors"
              >
                <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                </svg>
                Call Support
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Help; 