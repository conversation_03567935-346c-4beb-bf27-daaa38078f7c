import React from 'react';
import { motion } from 'framer-motion';
import { useSecure } from '../SecureContext';

const SecureHeader = ({ onBack, activeView, onDashboard }) => {
  const { loadingState } = useSecure();
  
  // Map of view names to display titles
  const viewTitles = {
    dashboard: 'Smilo Secure',
    record: 'Record Dental Visit',
    history: 'Visit History',
    transcript: 'Visit Transcript',
    comparison: 'Billing Comparison',
    profile: 'User Profile',
    help: 'Help & Resources',
    admin: 'Admin Settings',
    breathcheck: 'BreathCheck™ Premium'
  };
  
  return (
    <div className="flex justify-between items-center">
      <div className="flex items-center">
        <button 
          onClick={onBack}
          className="mr-4 p-2 rounded-full hover:bg-white/10 transition-colors"
        >
          <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
        </button>
        
        <div>
          <h2 className="text-2xl font-bold text-white flex items-center">
            {activeView !== 'dashboard' && (
              <button 
                onClick={onDashboard}
                className="mr-2 text-emerald-400 hover:text-emerald-300 transition-colors"
              >
                Smilo Secure
              </button>
            )}
            
            {activeView !== 'dashboard' && (
              <span className="mx-2 text-white/40">/</span>
            )}
            
            <span>{viewTitles[activeView] || 'Smilo Secure'}</span>
            
            {activeView === 'breathcheck' && (
              <span className="ml-2 text-xs bg-emerald-900/20 text-emerald-400 py-0.5 px-2 rounded-full">Premium</span>
            )}
          </h2>
          
          <p className="text-white/60 text-sm mt-1">
            {activeView === 'dashboard' ? 'Dental fraud detection through conversation analysis' : 
             activeView === 'help' ? 'Find answers and learn how to use Smilo Secure' :
             activeView === 'profile' ? 'Manage your personal settings and preferences' :
             activeView === 'admin' ? 'Configure system settings and user permissions' :
             activeView === 'breathcheck' ? 'Advanced breath analysis for comprehensive oral health monitoring' :
             'Protect yourself from dental billing errors and fraud'}
          </p>
        </div>
      </div>
      
      {/* Loading indicator */}
      {loadingState.loading && (
        <motion.div 
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="flex items-center bg-emerald-900/30 text-emerald-300 px-3 py-2 rounded-lg"
        >
          <svg className="animate-spin -ml-1 mr-3 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span>{loadingState.message || 'Loading...'}</span>
        </motion.div>
      )}
      
      {/* Security badge */}
      {!loadingState.loading && (
        <div className="flex items-center bg-emerald-900/20 text-emerald-300 px-3 py-2 rounded-lg">
          <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
          </svg>
          <span className="font-medium">End-to-End Encrypted</span>
        </div>
      )}
    </div>
  );
};

export default SecureHeader; 