import React, { useState } from 'react';
import { motion } from 'framer-motion';

const DeviceSetup = ({ onComplete }) => {
  const [isScanning, setIsScanning] = useState(false);
  const [foundDevices, setFoundDevices] = useState([]);
  const [selectedDevice, setSelectedDevice] = useState(null);
  const [setupStep, setSetupStep] = useState('intro'); // intro, scanning, pairing, complete
  
  const handleStartScan = () => {
    setIsScanning(true);
    setSetupStep('scanning');
    
    // Simulate finding devices
    setTimeout(() => {
      const mockDevices = [
        { id: 'BC-1234', name: 'BreathCheck Pro', type: 'Pro', batteryLevel: 85, signal: 'strong' },
        { id: 'BC-5678', name: 'BreathCheck Lite', type: 'Lite', batteryLevel: 72, signal: 'medium' }
      ];
      
      setFoundDevices(mockDevices);
      setIsScanning(false);
    }, 3000);
  };
  
  const handleSelectDevice = (device) => {
    setSelectedDevice(device);
    setSetupStep('pairing');
    
    // Simulate pairing process
    setTimeout(() => {
      setSetupStep('complete');
    }, 2000);
  };
  
  const handleCompletePairing = () => {
    // Simulate storing device info in localStorage
    localStorage.setItem('breathcheck_device', JSON.stringify(selectedDevice));
    onComplete();
  };
  
  const renderSetupStep = () => {
    switch (setupStep) {
      case 'intro':
        return (
          <div className="text-center py-6">
            <div className="mb-6 bg-emerald-900/30 p-6 rounded-xl inline-block">
              <svg className="w-24 h-24 text-emerald-400 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
              </svg>
            </div>
            
            <h3 className="text-2xl font-semibold text-white mb-4">Set Up Your BreathCheck™ Device</h3>
            
            <p className="text-white/70 mb-6 max-w-md mx-auto">
              Connect your BreathCheck device to begin real-time breath analysis and track your oral health with precision.
            </p>
            
            <div className="space-y-4">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleStartScan}
                className="px-6 py-3 bg-gradient-to-r from-emerald-600 to-emerald-500 hover:from-emerald-500 hover:to-emerald-400 text-white rounded-lg transition-colors shadow-lg w-full sm:w-auto"
              >
                Search for Devices
              </motion.button>
              
              <div>
                <a
                  href="#"
                  className="text-emerald-400 hover:text-emerald-300 text-sm"
                >
                  Don't have a device? Purchase now
                </a>
              </div>
            </div>
          </div>
        );
        
      case 'scanning':
        return (
          <div className="text-center py-6">
            <div className="mb-6">
              <div className="inline-block p-4 rounded-full bg-emerald-900/30">
                <svg className="animate-spin h-12 w-12 text-emerald-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </div>
            </div>
            
            <h3 className="text-xl font-semibold text-white mb-2">Searching for BreathCheck Devices</h3>
            
            <p className="text-white/70 mb-4">
              Make sure your device is turned on and within range.
            </p>
            
            <div className="flex flex-col items-center justify-center space-y-2 text-white/60 text-sm">
              <span>Ensuring Bluetooth is enabled</span>
              <span>Scanning for nearby devices...</span>
            </div>
          </div>
        );
        
      case 'pairing':
        return (
          <div className="text-center py-6">
            <div className="mb-6">
              <div className="inline-block p-4 rounded-full bg-emerald-900/30">
                <svg className="h-12 w-12 text-emerald-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
            </div>
            
            <h3 className="text-xl font-semibold text-white mb-2">Pairing with {selectedDevice?.name}</h3>
            
            <p className="text-white/70 mb-4">
              Please wait while we securely connect to your device.
            </p>
            
            <div className="w-48 mx-auto bg-white/10 rounded-full h-2 overflow-hidden">
              <motion.div 
                className="bg-emerald-500 h-2 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: '100%' }}
                transition={{ duration: 2 }}
              ></motion.div>
            </div>
          </div>
        );
        
      case 'complete':
        return (
          <div className="text-center py-6">
            <div className="mb-6">
              <div className="inline-block p-4 rounded-full bg-emerald-900/30">
                <svg className="h-12 w-12 text-emerald-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
            </div>
            
            <h3 className="text-xl font-semibold text-white mb-2">Device Successfully Paired!</h3>
            
            <p className="text-white/70 mb-6">
              Your {selectedDevice?.name} is now connected and ready to use.
            </p>
            
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={handleCompletePairing}
              className="px-6 py-3 bg-gradient-to-r from-emerald-600 to-emerald-500 hover:from-emerald-500 hover:to-emerald-400 text-white rounded-lg transition-colors shadow-lg"
            >
              Continue to BreathCheck
            </motion.button>
          </div>
        );
        
      default:
        return null;
    }
  };
  
  const renderDeviceList = () => {
    if (setupStep !== 'scanning' || isScanning || foundDevices.length === 0) return null;
    
    return (
      <div className="mt-6">
        <h4 className="text-white font-medium mb-3">Available Devices</h4>
        
        <div className="space-y-3">
          {foundDevices.map(device => (
            <motion.button
              key={device.id}
              onClick={() => handleSelectDevice(device)}
              className="w-full bg-white/5 hover:bg-white/10 border border-white/10 rounded-lg p-4 flex items-center transition-colors"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="p-2 bg-emerald-900/30 rounded-lg mr-4">
                <svg className="w-8 h-8 text-emerald-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                </svg>
              </div>
              
              <div className="flex-1 text-left">
                <div className="flex items-center justify-between">
                  <span className="text-white font-medium">{device.name}</span>
                  <span className="text-emerald-400 text-sm">{device.signal} signal</span>
                </div>
                
                <div className="text-white/60 text-sm">ID: {device.id}</div>
                
                <div className="flex items-center mt-1">
                  <span className="text-white/60 text-xs mr-2">Battery:</span>
                  <div className="w-16 h-2 bg-white/10 rounded-full">
                    <div 
                      className={`h-2 rounded-full ${
                        device.batteryLevel > 50 ? 'bg-emerald-500' : 
                        device.batteryLevel > 20 ? 'bg-amber-500' : 'bg-red-500'
                      }`}
                      style={{ width: `${device.batteryLevel}%` }}
                    ></div>
                  </div>
                  <span className="text-white/60 text-xs ml-2">{device.batteryLevel}%</span>
                </div>
              </div>
              
              <svg className="h-5 w-5 text-white/40" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </motion.button>
          ))}
        </div>
      </div>
    );
  };
  
  return (
    <div className="max-w-2xl mx-auto">
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="bg-gradient-to-br from-emerald-900/40 to-emerald-800/20 rounded-xl overflow-hidden border border-emerald-500/20"
      >
        <div className="p-6">
          <div className="text-center mb-6">
            <div className="inline-block py-1 px-3 rounded-full bg-emerald-900/20 text-emerald-400 text-xs font-medium">
              PREMIUM FEATURE
            </div>
            <h2 className="text-2xl font-bold text-white mb-1">BreathCheck™ by Smilo</h2>
            <p className="text-white/60 text-sm">
              Advanced breath analysis for comprehensive oral health monitoring
            </p>
          </div>
          
          <div className="mt-8">
            {renderSetupStep()}
            {renderDeviceList()}
          </div>
        </div>
      </motion.div>
      
      {/* Premium feature benefits */}
      {setupStep === 'intro' && (
        <div className="mt-10">
          <h3 className="text-lg font-medium text-white mb-4">Premium Benefits</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white/5 p-4 rounded-lg">
              <div className="bg-purple-900/30 w-10 h-10 rounded-full flex items-center justify-center mb-3">
                <svg className="w-5 h-5 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <h4 className="text-white font-medium mb-1">Early Detection</h4>
              <p className="text-white/70 text-sm">
                Identify potential oral health issues before they become serious problems.
              </p>
            </div>
            
            <div className="bg-white/5 p-4 rounded-lg">
              <div className="bg-blue-900/30 w-10 h-10 rounded-full flex items-center justify-center mb-3">
                <svg className="w-5 h-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <h4 className="text-white font-medium mb-1">Track Progress</h4>
              <p className="text-white/70 text-sm">
                Monitor how your oral care routine impacts breath quality over time.
              </p>
            </div>
            
            <div className="bg-white/5 p-4 rounded-lg">
              <div className="bg-emerald-900/30 w-10 h-10 rounded-full flex items-center justify-center mb-3">
                <svg className="w-5 h-5 text-emerald-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
              </div>
              <h4 className="text-white font-medium mb-1">Smart Insights</h4>
              <p className="text-white/70 text-sm">
                Get personalized recommendations based on your breath analysis.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DeviceSetup; 