import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';

const BreathHistory = () => {
  const [historyData, setHistoryData] = useState([]);
  const [timeRange, setTimeRange] = useState('month'); // week, month, year, all
  const [loading, setLoading] = useState(true);
  const chartRef = useRef(null);
  
  // Load history data from localStorage
  useEffect(() => {
    const loadHistoryData = () => {
      setLoading(true);
      
      // Get data from localStorage or generate mock data if none exists
      const historyString = localStorage.getItem('breathcheck_history');
      let history = [];
      
      if (historyString) {
        history = JSON.parse(historyString);
      } else {
        // Generate mock data for demo
        history = generateMockData();
        localStorage.setItem('breathcheck_history', JSON.stringify(history));
      }
      
      // Sort by timestamp
      history.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
      
      setHistoryData(history);
      setLoading(false);
    };
    
    loadHistoryData();
  }, []);
  
  // Draw chart when data or timeRange changes
  useEffect(() => {
    if (historyData.length > 0 && chartRef.current) {
      const filteredData = getFilteredData();
      drawChart(filteredData);
    }
  }, [historyData, timeRange]);
  
  // Generate 30 days of mock data
  const generateMockData = () => {
    const mockData = [];
    const now = new Date();
    
    // Generate data for the past 30 days
    for (let i = 30; i >= 0; i--) {
      const date = new Date();
      date.setDate(now.getDate() - i);
      
      // Generate random score between 60-100
      // Trend upward over time for demo purposes
      const baseScore = 65 + (i * 0.5);
      const randomVariation = Math.floor(Math.random() * 10) - 5;
      const score = Math.min(100, Math.max(60, baseScore + randomVariation));
      
      mockData.push({
        timestamp: date.toISOString(),
        overallScore: Math.round(score),
        measurements: {
          hydrogenSulfide: Math.floor(Math.random() * 150) + 50,
          methylMercaptan: Math.floor(Math.random() * 100) + 30,
          dimethylSulfide: Math.floor(Math.random() * 60) + 10,
        }
      });
    }
    
    return mockData;
  };
  
  // Filter data based on selected time range
  const getFilteredData = () => {
    if (historyData.length === 0) return [];
    
    const now = new Date();
    let cutoffDate = new Date();
    
    switch (timeRange) {
      case 'week':
        cutoffDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        cutoffDate.setMonth(now.getMonth() - 1);
        break;
      case 'year':
        cutoffDate.setFullYear(now.getFullYear() - 1);
        break;
      case 'all':
      default:
        return [...historyData];
    }
    
    return historyData.filter(item => new Date(item.timestamp) >= cutoffDate);
  };
  
  // Calculate averages and trend
  const calculateAverages = (data) => {
    if (!data || data.length === 0) return { average: 0, trend: 'stable' };
    
    const totalScore = data.reduce((sum, item) => sum + item.overallScore, 0);
    const average = Math.round(totalScore / data.length);
    
    // Split data in half to determine trend
    const midpoint = Math.floor(data.length / 2);
    const firstHalf = data.slice(0, midpoint);
    const secondHalf = data.slice(midpoint);
    
    if (firstHalf.length === 0 || secondHalf.length === 0) {
      return { average, trend: 'stable' };
    }
    
    const firstHalfAvg = firstHalf.reduce((sum, item) => sum + item.overallScore, 0) / firstHalf.length;
    const secondHalfAvg = secondHalf.reduce((sum, item) => sum + item.overallScore, 0) / secondHalf.length;
    
    const difference = secondHalfAvg - firstHalfAvg;
    
    let trend = 'stable';
    if (difference > 2) trend = 'improving';
    if (difference < -2) trend = 'worsening';
    
    return { average, trend };
  };
  
  // Format date for display
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(undefined, { 
      month: 'short', 
      day: 'numeric',
    });
  };
  
  // Draw chart using canvas
  const drawChart = (data) => {
    if (!chartRef.current || data.length === 0) return;
    
    const canvas = chartRef.current;
    const ctx = canvas.getContext('2d');
    const width = canvas.width;
    const height = canvas.height;
    
    // Clear canvas
    ctx.clearRect(0, 0, width, height);
    
    // Set up chart dimensions
    const padding = { top: 20, right: 20, bottom: 30, left: 40 };
    const chartWidth = width - padding.left - padding.right;
    const chartHeight = height - padding.top - padding.bottom;
    
    // Find max and min values
    const scores = data.map(item => item.overallScore);
    const maxScore = Math.max(...scores, 100);
    const minScore = Math.min(...scores, 60);
    
    // Draw grid
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
    ctx.lineWidth = 1;
    
    // Horizontal grid lines
    for (let i = 0; i <= 4; i++) {
      const y = padding.top + (chartHeight / 4) * i;
      ctx.beginPath();
      ctx.moveTo(padding.left, y);
      ctx.lineTo(width - padding.right, y);
      ctx.stroke();
    }
    
    // Draw axes
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
    ctx.lineWidth = 1;
    
    // X-axis
    ctx.beginPath();
    ctx.moveTo(padding.left, height - padding.bottom);
    ctx.lineTo(width - padding.right, height - padding.bottom);
    ctx.stroke();
    
    // Y-axis
    ctx.beginPath();
    ctx.moveTo(padding.left, padding.top);
    ctx.lineTo(padding.left, height - padding.bottom);
    ctx.stroke();
    
    // Plot data points
    const xStep = chartWidth / (data.length - 1 || 1);
    
    // Draw line
    ctx.strokeStyle = 'rgba(16, 185, 129, 0.8)'; // emerald-500
    ctx.lineWidth = 2;
    ctx.beginPath();
    
    data.forEach((item, index) => {
      const x = padding.left + (xStep * index);
      const normalizedScore = (item.overallScore - minScore) / (maxScore - minScore);
      const y = padding.top + chartHeight - (normalizedScore * chartHeight);
      
      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });
    
    ctx.stroke();
    
    // Draw points
    data.forEach((item, index) => {
      const x = padding.left + (xStep * index);
      const normalizedScore = (item.overallScore - minScore) / (maxScore - minScore);
      const y = padding.top + chartHeight - (normalizedScore * chartHeight);
      
      // Draw point
      ctx.fillStyle = getScoreColor(item.overallScore);
      ctx.beginPath();
      ctx.arc(x, y, 4, 0, Math.PI * 2);
      ctx.fill();
      
      // Draw outer circle
      ctx.strokeStyle = 'rgba(0, 0, 0, 0.3)';
      ctx.lineWidth = 1;
      ctx.beginPath();
      ctx.arc(x, y, 4, 0, Math.PI * 2);
      ctx.stroke();
    });
    
    // Add x-axis labels (dates) for first, middle and last
    ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
    ctx.font = '10px sans-serif';
    ctx.textAlign = 'center';
    
    // Show fewer labels to avoid crowding
    const labelsToShow = data.length <= 10 ? data.length : 5;
    const labelStep = Math.floor(data.length / (labelsToShow - 1)) || 1;
    
    for (let i = 0; i < data.length; i += labelStep) {
      if (i >= data.length) continue;
      
      const x = padding.left + (xStep * i);
      const date = new Date(data[i].timestamp);
      const label = formatDate(data[i].timestamp);
      
      ctx.fillText(label, x, height - padding.bottom + 15);
    }
    
    // Add last date if not already added
    if ((data.length - 1) % labelStep !== 0) {
      const x = padding.left + (xStep * (data.length - 1));
      const label = formatDate(data[data.length - 1].timestamp);
      ctx.fillText(label, x, height - padding.bottom + 15);
    }
  };
  
  // Get color based on score
  const getScoreColor = (score) => {
    if (score >= 90) return 'rgba(16, 185, 129, 1)'; // emerald-500
    if (score >= 80) return 'rgba(101, 163, 13, 1)'; // lime-600
    if (score >= 70) return 'rgba(234, 179, 8, 1)'; // yellow-500
    if (score >= 60) return 'rgba(217, 119, 6, 1)'; // amber-600
    return 'rgba(220, 38, 38, 1)'; // red-600
  };
  
  // Display badge for score
  const ScoreBadge = ({ score }) => {
    let color = 'emerald';
    let label = 'Excellent';
    
    if (score < 90) {
      color = 'green';
      label = 'Good';
    }
    if (score < 80) {
      color = 'yellow';
      label = 'Fair';
    }
    if (score < 70) {
      color = 'amber';
      label = 'Poor';
    }
    if (score < 60) {
      color = 'red';
      label = 'Bad';
    }
    
    return (
      <div className={`bg-${color}-900/30 text-${color}-400 px-2 py-1 rounded-md inline-flex items-center`}>
        <span className="text-sm font-medium">{label} ({score})</span>
      </div>
    );
  };
  
  // Trend indicator component
  const TrendIndicator = ({ trend }) => {
    if (trend === 'improving') {
      return (
        <div className="flex items-center text-emerald-400">
          <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
          </svg>
          <span className="text-sm">Improving</span>
        </div>
      );
    }
    
    if (trend === 'worsening') {
      return (
        <div className="flex items-center text-red-400">
          <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
          </svg>
          <span className="text-sm">Worsening</span>
        </div>
      );
    }
    
    return (
      <div className="flex items-center text-blue-400">
        <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h14" />
        </svg>
        <span className="text-sm">Stable</span>
      </div>
    );
  };
  
  // Get filtered data and statistics
  const filteredData = getFilteredData();
  const { average, trend } = calculateAverages(filteredData);
  
  // Find best score
  const bestScore = filteredData.length > 0 
    ? Math.max(...filteredData.map(item => item.overallScore))
    : 0;
  
  return (
    <div className="space-y-6 pb-6">
      {loading ? (
        <div className="flex justify-center py-12">
          <svg className="animate-spin h-8 w-8 text-emerald-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        </div>
      ) : (
        <>
          {/* Time range selector */}
          <div className="flex justify-between items-center">
            <h3 className="text-xl font-semibold text-white">Breath Health History</h3>
            
            <div className="flex space-x-1 bg-white/5 p-1 rounded-lg">
              <button 
                className={`px-3 py-1 rounded-md text-sm ${
                  timeRange === 'week' 
                    ? 'bg-emerald-700 text-white' 
                    : 'text-white/70 hover:bg-white/10'
                }`}
                onClick={() => setTimeRange('week')}
              >
                Week
              </button>
              <button 
                className={`px-3 py-1 rounded-md text-sm ${
                  timeRange === 'month' 
                    ? 'bg-emerald-700 text-white' 
                    : 'text-white/70 hover:bg-white/10'
                }`}
                onClick={() => setTimeRange('month')}
              >
                Month
              </button>
              <button 
                className={`px-3 py-1 rounded-md text-sm ${
                  timeRange === 'year' 
                    ? 'bg-emerald-700 text-white' 
                    : 'text-white/70 hover:bg-white/10'
                }`}
                onClick={() => setTimeRange('year')}
              >
                Year
              </button>
              <button 
                className={`px-3 py-1 rounded-md text-sm ${
                  timeRange === 'all' 
                    ? 'bg-emerald-700 text-white' 
                    : 'text-white/70 hover:bg-white/10'
                }`}
                onClick={() => setTimeRange('all')}
              >
                All
              </button>
            </div>
          </div>
          
          {/* Summary stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white/5 rounded-lg p-4">
              <div className="text-white/60 text-sm mb-2">Average Score</div>
              <div className="flex items-center justify-between">
                <div className="text-2xl font-semibold text-white">{average}</div>
                <TrendIndicator trend={trend} />
              </div>
            </div>
            
            <div className="bg-white/5 rounded-lg p-4">
              <div className="text-white/60 text-sm mb-2">Measurements</div>
              <div className="flex items-center justify-between">
                <div className="text-2xl font-semibold text-white">{filteredData.length}</div>
                <div className="text-white/60 text-sm">
                  {timeRange === 'week' ? 'this week' :
                   timeRange === 'month' ? 'this month' :
                   timeRange === 'year' ? 'this year' : 'total'}
                </div>
              </div>
            </div>
            
            <div className="bg-white/5 rounded-lg p-4">
              <div className="text-white/60 text-sm mb-2">Best Score</div>
              <div className="flex items-center justify-between">
                <div className="text-2xl font-semibold text-white">{bestScore}</div>
                <ScoreBadge score={bestScore} />
              </div>
            </div>
          </div>
          
          {/* Chart */}
          <div className="bg-white/5 rounded-lg p-4">
            <h4 className="text-white font-medium mb-4">Breath Health Trend</h4>
            
            {filteredData.length === 0 ? (
              <div className="py-8 text-center text-white/60">
                No data available for the selected time range
              </div>
            ) : (
              <canvas 
                ref={chartRef} 
                width={800} 
                height={300} 
                className="w-full h-[300px]"
              ></canvas>
            )}
          </div>
          
          {/* Recent measurements */}
          <div className="bg-white/5 rounded-lg p-4">
            <h4 className="text-white font-medium mb-4">Recent Measurements</h4>
            
            {filteredData.length === 0 ? (
              <div className="py-8 text-center text-white/60">
                No measurements available for the selected time range
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="text-left text-white/60 text-sm">
                      <th className="pb-2">Date</th>
                      <th className="pb-2">Score</th>
                      <th className="pb-2">H₂S</th>
                      <th className="pb-2">CH₃SH</th>
                      <th className="pb-2">(CH₃)₂S</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredData.slice(-5).reverse().map((item, index) => (
                      <tr key={index} className="border-t border-white/10">
                        <td className="py-3 text-white">
                          {new Date(item.timestamp).toLocaleDateString(undefined, {
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric',
                          })}
                        </td>
                        <td className="py-3">
                          <ScoreBadge score={item.overallScore} />
                        </td>
                        <td className="py-3 text-white/70">
                          {item.measurements.hydrogenSulfide} ppb
                        </td>
                        <td className="py-3 text-white/70">
                          {item.measurements.methylMercaptan} ppb
                        </td>
                        <td className="py-3 text-white/70">
                          {item.measurements.dimethylSulfide} ppb
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default BreathHistory; 