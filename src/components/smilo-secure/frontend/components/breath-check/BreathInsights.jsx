import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

const BreathInsights = () => {
  const [loading, setLoading] = useState(true);
  const [insights, setInsights] = useState(null);
  const [products, setProducts] = useState([]);

  useEffect(() => {
    // Load insights data
    const loadInsights = async () => {
      setLoading(true);
      
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        // Get breath history from localStorage
        const historyString = localStorage.getItem('breathcheck_history');
        
        if (historyString) {
          const history = JSON.parse(historyString);
          
          if (history.length > 0) {
            // Generate insights based on history
            const generatedInsights = generateInsights(history);
            setInsights(generatedInsights);
            
            // Get product recommendations
            const recommendedProducts = getRecommendedProducts(generatedInsights);
            setProducts(recommendedProducts);
          } else {
            // Default insights if no history
            const defaultInsights = generateDefaultInsights();
            setInsights(defaultInsights);
            
            // Default product recommendations
            const defaultProducts = getRecommendedProducts(defaultInsights);
            setProducts(defaultProducts);
          }
        } else {
          // Default insights if no history
          const defaultInsights = generateDefaultInsights();
          setInsights(defaultInsights);
          
          // Default product recommendations
          const defaultProducts = getRecommendedProducts(defaultInsights);
          setProducts(defaultProducts);
        }
      } catch (error) {
        console.error('Error loading insights:', error);
        // Set default insights on error
        const defaultInsights = generateDefaultInsights();
        setInsights(defaultInsights);
        setProducts(getRecommendedProducts(defaultInsights));
      } finally {
        setLoading(false);
      }
    };
    
    loadInsights();
  }, []);
  
  // Generate insights based on breath history
  const generateInsights = (history) => {
    // Sort history by timestamp (descending)
    const sortedHistory = [...history].sort((a, b) => 
      new Date(b.timestamp) - new Date(a.timestamp)
    );
    
    // Get latest test result
    const latestTest = sortedHistory[0];
    
    // Calculate average score
    const totalScore = sortedHistory.reduce((sum, test) => sum + test.overallScore, 0);
    const avgScore = Math.round(totalScore / sortedHistory.length);
    
    // Determine trend
    let trend = 'stable';
    if (sortedHistory.length > 3) {
      const recentTests = sortedHistory.slice(0, 3);
      const olderTests = sortedHistory.slice(3, 6).length > 0 
        ? sortedHistory.slice(3, 6) 
        : sortedHistory.slice(Math.floor(sortedHistory.length / 2));
      
      const recentAvg = recentTests.reduce((sum, test) => sum + test.overallScore, 0) / recentTests.length;
      const olderAvg = olderTests.reduce((sum, test) => sum + test.overallScore, 0) / olderTests.length;
      
      if (recentAvg > olderAvg + 3) trend = 'improving';
      if (recentAvg < olderAvg - 3) trend = 'worsening';
    }
    
    // Analyze VSC levels
    const vscAnalysis = analyzeVSCLevels(latestTest.measurements);
    
    // Generate recommendations based on latest score and VSC levels
    const recommendations = generateRecommendations(latestTest.overallScore, vscAnalysis, trend);
    
    // Detect patterns
    const correlations = generateCorrelations(sortedHistory);
    
    return {
      latestScore: latestTest.overallScore,
      averageScore: avgScore,
      trend,
      vscAnalysis,
      recommendations,
      correlations
    };
  };
  
  // Generate default insights
  const generateDefaultInsights = () => {
    return {
      latestScore: 75,
      averageScore: 72,
      trend: 'stable',
      vscAnalysis: {
        hydrogenSulfide: { level: 'moderate', source: 'gum disease' },
        methylMercaptan: { level: 'low', source: null },
        dimethylSulfide: { level: 'low', source: null }
      },
      recommendations: [
        'Schedule a dental checkup in the next 30 days',
        'Brush teeth twice daily with fluoride toothpaste',
        'Floss daily to remove plaque between teeth',
        'Use alcohol-free antimicrobial mouthwash',
        'Clean your tongue daily with a tongue scraper'
      ],
      correlations: [
        'Your breath quality tends to be better in the morning',
        'Hydration appears to positively impact your breath freshness'
      ]
    };
  };
  
  // Analyze VSC levels from test measurements
  const analyzeVSCLevels = (measurements) => {
    const analysis = {};
    
    // Hydrogen Sulfide (H₂S)
    if (measurements.hydrogenSulfide > 150) {
      analysis.hydrogenSulfide = { level: 'high', source: 'gum disease or tooth decay' };
    } else if (measurements.hydrogenSulfide > 100) {
      analysis.hydrogenSulfide = { level: 'moderate', source: 'gum disease' };
    } else {
      analysis.hydrogenSulfide = { level: 'low', source: null };
    }
    
    // Methyl Mercaptan (CH₃SH)
    if (measurements.methylMercaptan > 80) {
      analysis.methylMercaptan = { level: 'high', source: 'periodontal issues' };
    } else if (measurements.methylMercaptan > 50) {
      analysis.methylMercaptan = { level: 'moderate', source: 'food debris' };
    } else {
      analysis.methylMercaptan = { level: 'low', source: null };
    }
    
    // Dimethyl Sulfide ((CH₃)₂S)
    if (measurements.dimethylSulfide > 40) {
      analysis.dimethylSulfide = { level: 'high', source: 'systemic or metabolic issues' };
    } else if (measurements.dimethylSulfide > 20) {
      analysis.dimethylSulfide = { level: 'moderate', source: 'diet' };
    } else {
      analysis.dimethylSulfide = { level: 'low', source: null };
    }
    
    return analysis;
  };
  
  // Generate personalized recommendations
  const generateRecommendations = (score, vscAnalysis, trend) => {
    const recommendations = [];
    
    // Base recommendations based on score
    if (score >= 90) {
      recommendations.push('Continue your current oral hygiene routine');
      recommendations.push('Maintain your regular dental check-up schedule');
    } else if (score >= 80) {
      recommendations.push('Brush teeth twice daily with fluoride toothpaste');
      recommendations.push('Floss daily to remove plaque between teeth');
    } else if (score >= 70) {
      recommendations.push('Schedule a dental checkup in the next 30 days');
      recommendations.push('Use alcohol-free antimicrobial mouthwash');
      recommendations.push('Brush after meals when possible');
    } else {
      recommendations.push('Schedule a dental appointment within 2 weeks');
      recommendations.push('Use a prescription-strength antimicrobial mouthwash');
      recommendations.push('Consider professional cleaning');
    }
    
    // Add recommendations based on VSC analysis
    if (vscAnalysis.hydrogenSulfide.level === 'high' || vscAnalysis.hydrogenSulfide.level === 'moderate') {
      recommendations.push('Clean your tongue daily with a tongue scraper');
    }
    
    if (vscAnalysis.methylMercaptan.level === 'high' || vscAnalysis.methylMercaptan.level === 'moderate') {
      recommendations.push('Use an interdental brush or water flosser to remove trapped food particles');
    }
    
    if (vscAnalysis.dimethylSulfide.level === 'high') {
      recommendations.push('Consider discussing your diet with a nutrition professional');
      recommendations.push('Stay well-hydrated throughout the day');
    }
    
    // Add recommendations based on trend
    if (trend === 'worsening') {
      recommendations.push('Review recent changes in your oral care routine or diet');
    }
    
    // Return unique recommendations
    return [...new Set(recommendations)];
  };
  
  // Generate correlations based on history
  const generateCorrelations = (history) => {
    // This would be more sophisticated in a real app
    // For demo purposes, we'll return some example correlations
    return [
      'Your breath quality tends to be better in the morning',
      'Hydration appears to positively impact your breath freshness',
      'Your breath scores have improved since starting to use a tongue scraper'
    ];
  };
  
  // Get recommended products based on insights
  const getRecommendedProducts = (insights) => {
    const vscAnalysis = insights.vscAnalysis;
    const score = insights.latestScore;
    
    const products = [];
    
    // Recommend mouthwash
    if (score < 80 || vscAnalysis.hydrogenSulfide.level !== 'low') {
      products.push({
        name: 'Smilo Fresh Breath Mouthwash',
        type: 'mouthwash',
        description: 'Alcohol-free formula with zinc and CPC to neutralize VSCs',
        price: 12.99,
        image: 'https://via.placeholder.com/100x100?text=Mouthwash'
      });
    }
    
    // Recommend water flosser
    if (vscAnalysis.methylMercaptan.level !== 'low') {
      products.push({
        name: 'Smilo Water Flosser Pro',
        type: 'flosser',
        description: 'High-pressure water flosser to remove food particles and debris',
        price: 49.99,
        image: 'https://via.placeholder.com/100x100?text=WaterFlosser'
      });
    }
    
    // Recommend tongue scraper
    if (vscAnalysis.hydrogenSulfide.level !== 'low') {
      products.push({
        name: 'Smilo Tongue Cleaner',
        type: 'tongue-cleaner',
        description: 'Stainless steel tongue scraper to remove bacteria and debris',
        price: 8.99,
        image: 'https://via.placeholder.com/100x100?text=TongueScraper'
      });
    }
    
    // Recommend toothpaste
    if (score < 85) {
      products.push({
        name: 'Smilo Total Care Toothpaste',
        type: 'toothpaste',
        description: 'Specially formulated to fight bad breath and improve oral health',
        price: 6.99,
        image: 'https://via.placeholder.com/100x100?text=Toothpaste'
      });
    }
    
    return products;
  };
  
  if (loading) {
    return (
      <div className="flex justify-center py-12">
        <svg className="animate-spin h-8 w-8 text-emerald-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      </div>
    );
  }
  
  if (!insights) {
    return (
      <div className="text-center py-12">
        <h3 className="text-xl font-semibold text-white mb-2">No Insights Available</h3>
        <p className="text-white/70 mb-6">
          Take a breath test to get personalized insights and recommendations.
        </p>
        <button className="px-4 py-2 bg-emerald-600 hover:bg-emerald-500 text-white rounded-lg transition-colors">
          Take a Test
        </button>
      </div>
    );
  }
  
  // Get badge color based on score
  const getScoreColor = (score) => {
    if (score >= 90) return 'emerald';
    if (score >= 80) return 'green';
    if (score >= 70) return 'yellow';
    if (score >= 60) return 'amber';
    return 'red';
  };
  
  // Get score label based on score
  const getScoreLabel = (score) => {
    if (score >= 90) return 'Excellent';
    if (score >= 80) return 'Good';
    if (score >= 70) return 'Fair';
    if (score >= 60) return 'Poor';
    return 'Bad';
  };
  
  return (
    <div className="space-y-6 pb-6">
      {/* AI Summary */}
      <div className="bg-gradient-to-br from-purple-900/40 to-purple-800/20 rounded-xl overflow-hidden border border-purple-500/30 p-6">
        <div className="flex items-center mb-4">
          <div className="p-2 bg-purple-900/40 rounded-lg mr-3">
            <svg className="w-6 h-6 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          </div>
          <h3 className="text-xl font-semibold text-white">AI-Powered Insights</h3>
        </div>
        
        <div className="flex flex-wrap gap-4 mb-4">
          <div className="bg-white/5 rounded-lg p-3 flex items-center">
            <span className="text-white/60 mr-2">Latest Score:</span>
            <span className={`bg-${getScoreColor(insights.latestScore)}-900/30 text-${getScoreColor(insights.latestScore)}-400 px-2 py-0.5 rounded text-sm font-medium`}>
              {insights.latestScore} ({getScoreLabel(insights.latestScore)})
            </span>
          </div>
          
          <div className="bg-white/5 rounded-lg p-3 flex items-center">
            <span className="text-white/60 mr-2">Average:</span>
            <span className={`bg-${getScoreColor(insights.averageScore)}-900/30 text-${getScoreColor(insights.averageScore)}-400 px-2 py-0.5 rounded text-sm font-medium`}>
              {insights.averageScore}
            </span>
          </div>
          
          <div className="bg-white/5 rounded-lg p-3 flex items-center">
            <span className="text-white/60 mr-2">Trend:</span>
            <span className={`
              ${insights.trend === 'improving' ? 'bg-emerald-900/30 text-emerald-400' : 
                insights.trend === 'worsening' ? 'bg-red-900/30 text-red-400' : 
                'bg-blue-900/30 text-blue-400'} 
              px-2 py-0.5 rounded text-sm font-medium`}
            >
              {insights.trend === 'improving' ? 'Improving' : 
               insights.trend === 'worsening' ? 'Worsening' : 
               'Stable'}
            </span>
          </div>
        </div>
        
        <p className="text-white/70">
          Based on your breath analysis, our AI has identified specific patterns and recommendations 
          to help improve your oral health. Your recent tests show 
          {insights.trend === 'improving' ? ' improvement in' : 
           insights.trend === 'worsening' ? ' declining' : 
           ' stable'} breath quality. The analysis below provides detailed insights and personalized recommendations.
        </p>
      </div>
      
      {/* VSC Analysis */}
      <div className="bg-white/5 rounded-xl p-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <svg className="w-5 h-5 mr-2 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
          Volatile Sulfur Compound Analysis
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-white/5 p-4 rounded-lg">
            <div className="flex justify-between items-center mb-2">
              <span className="text-white font-medium">Hydrogen Sulfide (H₂S)</span>
              <span className={`
                ${insights.vscAnalysis.hydrogenSulfide.level === 'low' ? 'bg-emerald-900/30 text-emerald-400' : 
                  insights.vscAnalysis.hydrogenSulfide.level === 'moderate' ? 'bg-yellow-900/30 text-yellow-400' : 
                  'bg-red-900/30 text-red-400'} 
                text-xs px-2 py-0.5 rounded`}
              >
                {insights.vscAnalysis.hydrogenSulfide.level}
              </span>
            </div>
            <p className="text-white/70 text-sm">
              {insights.vscAnalysis.hydrogenSulfide.level === 'low' 
                ? 'Healthy level. No concern needed.' 
                : `May indicate ${insights.vscAnalysis.hydrogenSulfide.source}.`}
            </p>
          </div>
          
          <div className="bg-white/5 p-4 rounded-lg">
            <div className="flex justify-between items-center mb-2">
              <span className="text-white font-medium">Methyl Mercaptan (CH₃SH)</span>
              <span className={`
                ${insights.vscAnalysis.methylMercaptan.level === 'low' ? 'bg-emerald-900/30 text-emerald-400' : 
                  insights.vscAnalysis.methylMercaptan.level === 'moderate' ? 'bg-yellow-900/30 text-yellow-400' : 
                  'bg-red-900/30 text-red-400'} 
                text-xs px-2 py-0.5 rounded`}
              >
                {insights.vscAnalysis.methylMercaptan.level}
              </span>
            </div>
            <p className="text-white/70 text-sm">
              {insights.vscAnalysis.methylMercaptan.level === 'low' 
                ? 'Healthy level. No concern needed.' 
                : `May indicate ${insights.vscAnalysis.methylMercaptan.source}.`}
            </p>
          </div>
          
          <div className="bg-white/5 p-4 rounded-lg">
            <div className="flex justify-between items-center mb-2">
              <span className="text-white font-medium">Dimethyl Sulfide ((CH₃)₂S)</span>
              <span className={`
                ${insights.vscAnalysis.dimethylSulfide.level === 'low' ? 'bg-emerald-900/30 text-emerald-400' : 
                  insights.vscAnalysis.dimethylSulfide.level === 'moderate' ? 'bg-yellow-900/30 text-yellow-400' : 
                  'bg-red-900/30 text-red-400'} 
                text-xs px-2 py-0.5 rounded`}
              >
                {insights.vscAnalysis.dimethylSulfide.level}
              </span>
            </div>
            <p className="text-white/70 text-sm">
              {insights.vscAnalysis.dimethylSulfide.level === 'low' 
                ? 'Healthy level. No concern needed.' 
                : `May indicate ${insights.vscAnalysis.dimethylSulfide.source}.`}
            </p>
          </div>
        </div>
      </div>
      
      {/* Recommendations */}
      <div className="bg-white/5 rounded-xl p-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <svg className="w-5 h-5 mr-2 text-emerald-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
          </svg>
          Personalized Recommendations
        </h3>
        
        <ul className="space-y-2">
          {insights.recommendations.map((recommendation, index) => (
            <motion.li 
              key={index}
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1, duration: 0.3 }}
              className="bg-white/5 p-3 rounded-lg flex items-start"
            >
              <svg className="w-5 h-5 mr-3 text-emerald-400 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <span className="text-white/80">{recommendation}</span>
            </motion.li>
          ))}
        </ul>
      </div>
      
      {/* Detected Patterns */}
      <div className="bg-white/5 rounded-xl p-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <svg className="w-5 h-5 mr-2 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
          </svg>
          Detected Patterns
        </h3>
        
        <ul className="space-y-2">
          {insights.correlations.map((correlation, index) => (
            <li key={index} className="bg-white/5 p-3 rounded-lg flex items-start">
              <svg className="w-5 h-5 mr-3 text-blue-400 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="text-white/80">{correlation}</span>
            </li>
          ))}
        </ul>
      </div>
      
      {/* Product Recommendations */}
      {products.length > 0 && (
        <div className="bg-white/5 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
            <svg className="w-5 h-5 mr-2 text-amber-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
            </svg>
            Recommended Products
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {products.map((product, index) => (
              <motion.div 
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1, duration: 0.3 }}
                className="bg-white/5 rounded-lg overflow-hidden"
              >
                <div className="h-32 bg-white/10 flex items-center justify-center">
                  <img 
                    src={product.image} 
                    alt={product.name} 
                    className="max-h-full max-w-full object-contain"
                  />
                </div>
                <div className="p-4">
                  <h4 className="text-white font-medium mb-1">{product.name}</h4>
                  <p className="text-white/60 text-sm mb-3">{product.description}</p>
                  <div className="flex justify-between items-center">
                    <span className="text-amber-400 font-medium">${product.price}</span>
                    <button className="text-xs bg-emerald-700 hover:bg-emerald-600 text-white px-2 py-1 rounded transition-colors">
                      View Product
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default BreathInsights; 