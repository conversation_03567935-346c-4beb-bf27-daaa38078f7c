import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

const BreathStats = ({ deviceInfo }) => {
  const [breathScore, setBreathScore] = useState(null);
  const [isScanning, setIsScanning] = useState(false);
  const [lastScan, setLastScan] = useState(null);
  const [batteryLevel, setBatteryLevel] = useState(deviceInfo?.batteryLevel || 85);
  
  // Simulated VSC compounds (volatile sulfur compounds)
  const [vscLevels, setVscLevels] = useState({
    hydrogen_sulfide: 0,
    methyl_mercaptan: 0,
    dimethyl_sulfide: 0
  });
  
  useEffect(() => {
    // Check if we have a recent scan (within last 30 minutes)
    const lastScanData = localStorage.getItem('breathcheck_last_scan');
    if (lastScanData) {
      const scanData = JSON.parse(lastScanData);
      const scanTime = new Date(scanData.timestamp);
      const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);
      
      if (scanTime > thirtyMinutesAgo) {
        setBreathScore(scanData.score);
        setVscLevels(scanData.vscLevels);
        setLastScan(scanData.timestamp);
      }
    }
  }, []);
  
  const handleStartScan = () => {
    setIsScanning(true);
    
    // Simulate a breath scan that takes a few seconds
    setTimeout(() => {
      // Generate random VSC levels
      const hydrogen_sulfide = Math.floor(Math.random() * 150);
      const methyl_mercaptan = Math.floor(Math.random() * 100);
      const dimethyl_sulfide = Math.floor(Math.random() * 80);
      
      // Calculate breath score (lower is better, 0-100)
      // In a real app this would use actual formulas based on VSC levels
      const score = Math.min(
        Math.round((hydrogen_sulfide * 0.3 + methyl_mercaptan * 0.5 + dimethyl_sulfide * 0.2) / 3),
        100
      );
      
      const newVscLevels = { hydrogen_sulfide, methyl_mercaptan, dimethyl_sulfide };
      const timestamp = new Date().toISOString();
      
      // Update state
      setBreathScore(score);
      setVscLevels(newVscLevels);
      setLastScan(timestamp);
      setIsScanning(false);
      
      // Save to localStorage for persistence
      const scanData = {
        score,
        vscLevels: newVscLevels,
        timestamp
      };
      localStorage.setItem('breathcheck_last_scan', JSON.stringify(scanData));
      
      // Simulate battery usage
      setBatteryLevel(prev => Math.max(prev - 3, 0));
      
      // Save result to history
      const historyData = localStorage.getItem('breathcheck_history') 
        ? JSON.parse(localStorage.getItem('breathcheck_history')) 
        : [];
      
      historyData.push(scanData);
      localStorage.setItem('breathcheck_history', JSON.stringify(historyData));
    }, 3000);
  };
  
  const getScoreColor = (score) => {
    if (score <= 30) return 'text-emerald-400';
    if (score <= 60) return 'text-amber-400';
    return 'text-red-400';
  };
  
  const getScoreLabel = (score) => {
    if (score <= 30) return 'Excellent';
    if (score <= 60) return 'Moderate';
    return 'Poor';
  };
  
  const getBatteryColor = (level) => {
    if (level > 50) return 'text-emerald-400';
    if (level > 20) return 'text-amber-400';
    return 'text-red-400';
  };
  
  return (
    <div className="space-y-8">
      {/* Breath score gauge */}
      <div className="bg-white/5 rounded-xl p-6 relative overflow-hidden">
        <div className={`absolute top-0 left-0 h-full ${breathScore !== null ? 'bg-gradient-to-t from-white/0 to-emerald-900/20' : 'bg-white/5'} w-full`}></div>
        
        <div className="relative z-10">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-white text-xl font-semibold">Breath Score</h3>
            <div className="flex items-center text-sm">
              <span className={`mr-1 ${getBatteryColor(batteryLevel)}`}>
                {batteryLevel}%
              </span>
              <svg className="w-4 h-4 text-white/60" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 10.5h-3.33a5 5 0 10-10.35 0H4.5a2 2 0 00-2 2v4a2 2 0 002 2h16a2 2 0 002-2v-4a2 2 0 00-2-2zM15 10.5V10a3 3 0 10-6 0v.5" />
              </svg>
            </div>
          </div>
          
          {breathScore !== null ? (
            <div className="flex items-center justify-center py-10">
              <div className="text-center">
                <div className={`text-7xl font-bold mb-2 ${getScoreColor(breathScore)}`}>
                  {breathScore}
                </div>
                <div className={`text-lg font-medium ${getScoreColor(breathScore)}`}>
                  {getScoreLabel(breathScore)}
                </div>
                <div className="text-white/50 text-sm mt-2">
                  Last scan: {lastScan ? new Date(lastScan).toLocaleTimeString() : 'Never'}
                </div>
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center py-10">
              <div className="text-center text-white/60">
                No recent breath analysis data
              </div>
            </div>
          )}
          
          <div className="mt-4">
            <button
              onClick={handleStartScan}
              disabled={isScanning}
              className={`w-full py-3 rounded-lg font-medium flex items-center justify-center ${
                isScanning
                  ? 'bg-white/20 text-white/60 cursor-not-allowed'
                  : 'bg-emerald-600 hover:bg-emerald-500 text-white'
              }`}
            >
              {isScanning ? (
                <>
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Analyzing...
                </>
              ) : (
                <>
                  <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                  </svg>
                  Start New Scan
                </>
              )}
            </button>
          </div>
        </div>
      </div>
      
      {/* VSC Levels */}
      {breathScore !== null && (
        <div className="bg-white/5 rounded-xl p-6">
          <h3 className="text-white text-xl font-semibold mb-4">VSC Compound Levels (ppb)</h3>
          
          <div className="space-y-4">
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-white/80 text-sm">Hydrogen Sulfide</span>
                <span className="text-white/80 text-sm">{vscLevels.hydrogen_sulfide} ppb</span>
              </div>
              <div className="w-full bg-white/10 rounded-full h-2.5">
                <div 
                  className="bg-emerald-500 h-2.5 rounded-full" 
                  style={{ width: `${Math.min((vscLevels.hydrogen_sulfide / 200) * 100, 100)}%` }}
                ></div>
              </div>
              <div className="text-white/50 text-xs mt-1">Normal range: 0-80 ppb</div>
            </div>
            
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-white/80 text-sm">Methyl Mercaptan</span>
                <span className="text-white/80 text-sm">{vscLevels.methyl_mercaptan} ppb</span>
              </div>
              <div className="w-full bg-white/10 rounded-full h-2.5">
                <div 
                  className="bg-blue-500 h-2.5 rounded-full" 
                  style={{ width: `${Math.min((vscLevels.methyl_mercaptan / 150) * 100, 100)}%` }}
                ></div>
              </div>
              <div className="text-white/50 text-xs mt-1">Normal range: 0-50 ppb</div>
            </div>
            
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-white/80 text-sm">Dimethyl Sulfide</span>
                <span className="text-white/80 text-sm">{vscLevels.dimethyl_sulfide} ppb</span>
              </div>
              <div className="w-full bg-white/10 rounded-full h-2.5">
                <div 
                  className="bg-purple-500 h-2.5 rounded-full" 
                  style={{ width: `${Math.min((vscLevels.dimethyl_sulfide / 100) * 100, 100)}%` }}
                ></div>
              </div>
              <div className="text-white/50 text-xs mt-1">Normal range: 0-30 ppb</div>
            </div>
          </div>
          
          <div className="mt-6 p-4 bg-white/5 rounded-lg">
            <h4 className="text-white font-medium mb-2">What These Results Mean</h4>
            <p className="text-white/70 text-sm">
              Volatile Sulfur Compounds (VSCs) are the primary cause of bad breath. Higher levels may indicate gum disease, poor oral hygiene, or digestive issues. Regular monitoring helps track your oral health progress.
            </p>
          </div>
        </div>
      )}
      
      {/* Quick Tips based on score */}
      {breathScore !== null && (
        <div className="bg-white/5 rounded-xl p-6">
          <h3 className="text-white text-xl font-semibold mb-4">Quick Recommendations</h3>
          
          <div className="space-y-4">
            {breathScore > 60 && (
              <div className="flex p-3 bg-red-900/20 rounded-lg">
                <svg className="w-6 h-6 text-red-400 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                <div>
                  <h4 className="text-white font-medium">Schedule a Cleaning Soon</h4>
                  <p className="text-white/70 text-sm">Your high VSC levels may indicate gum disease or excessive plaque buildup.</p>
                </div>
              </div>
            )}
            
            {breathScore > 30 && (
              <div className="flex p-3 bg-amber-900/20 rounded-lg">
                <svg className="w-6 h-6 text-amber-400 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div>
                  <h4 className="text-white font-medium">Try an Antimicrobial Rinse</h4>
                  <p className="text-white/70 text-sm">Using an alcohol-free mouthwash can help reduce VSC-producing bacteria.</p>
                </div>
              </div>
            )}
            
            <div className="flex p-3 bg-blue-900/20 rounded-lg">
              <svg className="w-6 h-6 text-blue-400 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div>
                <h4 className="text-white font-medium">Regular Testing</h4>
                <p className="text-white/70 text-sm">For best results, test at the same time each day, preferably before brushing in the morning.</p>
              </div>
            </div>
            
            <div className="flex p-3 bg-emerald-900/20 rounded-lg">
              <svg className="w-6 h-6 text-emerald-400 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div>
                <h4 className="text-white font-medium">Stay Hydrated</h4>
                <p className="text-white/70 text-sm">Drinking more water helps wash away bacteria and food particles that cause bad breath.</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BreathStats; 