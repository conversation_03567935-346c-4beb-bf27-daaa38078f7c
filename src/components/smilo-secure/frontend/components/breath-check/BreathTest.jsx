import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';

const BreathTest = () => {
  // State for test progress and results
  const [testState, setTestState] = useState('ready'); // ready, connecting, measuring, processing, complete
  const [progress, setProgress] = useState(0);
  const [deviceConnected, setDeviceConnected] = useState(false);
  const [deviceBattery, setDeviceBattery] = useState(85);
  const [testResults, setTestResults] = useState(null);
  const progressInterval = useRef(null);
  
  // Clean up interval on unmount
  useEffect(() => {
    return () => {
      if (progressInterval.current) {
        clearInterval(progressInterval.current);
      }
    };
  }, []);
  
  // Connect to device and start test
  const startTest = () => {
    setTestState('connecting');
    setProgress(0);
    
    // Simulate connecting to device
    setTimeout(() => {
      setDeviceConnected(true);
      setTestState('measuring');
      
      // Simulate progress
      progressInterval.current = setInterval(() => {
        setProgress(prev => {
          if (prev >= 100) {
            clearInterval(progressInterval.current);
            setTestState('processing');
            processResults();
            return 100;
          }
          return prev + 5;
        });
      }, 300);
    }, 1500);
  };
  
  // Process test results
  const processResults = () => {
    // Simulate processing time
    setTimeout(() => {
      // Generate mock test results
      const mockResults = {
        timestamp: new Date().toISOString(),
        overallScore: Math.floor(Math.random() * 30) + 70, // 70-100
        measurements: {
          hydrogenSulfide: Math.floor(Math.random() * 150) + 50, // 50-200 ppb
          methylMercaptan: Math.floor(Math.random() * 100) + 30, // 30-130 ppb
          dimethylSulfide: Math.floor(Math.random() * 60) + 10, // 10-70 ppb
        },
        interpretation: {
          level: 'good', // bad, moderate, good
          mainCause: 'normal',
          recommendations: [
            'Continue your regular oral hygiene routine',
            'Ensure you clean your tongue as part of daily brushing',
            'Stay hydrated throughout the day'
          ]
        }
      };
      
      // Adjust interpretation based on score
      if (mockResults.overallScore < 80) {
        mockResults.interpretation.level = 'moderate';
        mockResults.interpretation.mainCause = 'possible gingivitis';
        mockResults.interpretation.recommendations = [
          'Increase brushing to twice daily with fluoride toothpaste',
          'Use an antibacterial mouthwash before bed',
          'Floss daily to remove food particles between teeth',
          'Schedule a dental check-up in the next 2 weeks'
        ];
      } 
      else if (mockResults.overallScore < 75) {
        mockResults.interpretation.level = 'bad';
        mockResults.interpretation.mainCause = 'possible periodontal disease';
        mockResults.interpretation.recommendations = [
          'Schedule a dental appointment immediately',
          'Use prescription-strength mouthwash if available',
          'Brush after every meal with antibacterial toothpaste',
          'Increase water intake and avoid foods that cause dry mouth'
        ];
      }
      
      // Save result to history in localStorage
      const historyString = localStorage.getItem('breathcheck_history');
      let history = historyString ? JSON.parse(historyString) : [];
      history.push(mockResults);
      localStorage.setItem('breathcheck_history', JSON.stringify(history));
      
      // Update state with results
      setTestResults(mockResults);
      setTestState('complete');
    }, 2000);
  };
  
  // Reset test to start over
  const resetTest = () => {
    setTestState('ready');
    setProgress(0);
    setTestResults(null);
  };
  
  // Get color based on score
  const getScoreColor = (score) => {
    if (score >= 90) return 'emerald';
    if (score >= 80) return 'green';
    if (score >= 70) return 'yellow';
    if (score >= 60) return 'amber';
    return 'red';
  };
  
  // Get label based on score
  const getScoreLabel = (score) => {
    if (score >= 90) return 'Excellent';
    if (score >= 80) return 'Good';
    if (score >= 70) return 'Fair';
    if (score >= 60) return 'Poor';
    return 'Bad';
  };
  
  // Render instructions based on test state
  const renderInstructions = () => {
    switch (testState) {
      case 'ready':
        return (
          <div className="text-center">
            <h3 className="text-xl font-bold text-white mb-4">Ready to Check Your Breath Health?</h3>
            <p className="text-white/70 mb-6">
              Make sure your BreathCheck device is charged and nearby. When ready, click the button below 
              to connect and begin the test.
            </p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={startTest}
              className="px-6 py-3 bg-gradient-to-r from-emerald-600 to-emerald-500 hover:from-emerald-500 hover:to-emerald-400 text-white rounded-lg shadow-lg transition-colors font-medium"
            >
              Start Test
            </motion.button>
          </div>
        );
        
      case 'connecting':
        return (
          <div className="text-center">
            <h3 className="text-xl font-bold text-white mb-4">Connecting to Device</h3>
            <p className="text-white/70 mb-6">
              Please wait while we connect to your BreathCheck device...
            </p>
            <div className="flex justify-center">
              <div className="animate-pulse w-12 h-12 rounded-full border-4 border-emerald-500 border-t-transparent animate-spin"></div>
            </div>
          </div>
        );
        
      case 'measuring':
        return (
          <div className="text-center">
            <h3 className="text-xl font-bold text-white mb-4">Measuring in Progress</h3>
            <p className="text-white/70 mb-6">
              Please breathe normally into the device until the progress bar completes.
              Keep the device about 1-2 inches from your mouth.
            </p>
            <div className="relative h-4 bg-white/10 rounded-full overflow-hidden mb-2">
              <div 
                className="absolute top-0 left-0 h-full bg-gradient-to-r from-emerald-600 to-emerald-400 rounded-full transition-all"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
            <p className="text-sm text-white/50">{progress}% complete</p>
          </div>
        );
        
      case 'processing':
        return (
          <div className="text-center">
            <h3 className="text-xl font-bold text-white mb-4">Analyzing Results</h3>
            <p className="text-white/70 mb-6">
              Please wait while we process your breath sample and analyze the compounds...
            </p>
            <div className="flex justify-center">
              <div className="animate-pulse w-12 h-12 rounded-full border-4 border-emerald-500 border-t-transparent animate-spin"></div>
            </div>
          </div>
        );
        
      case 'complete':
        if (!testResults) return null;
        
        const scoreColor = getScoreColor(testResults.overallScore);
        const scoreLabel = getScoreLabel(testResults.overallScore);
        
        return (
          <div>
            <div className="text-center mb-8">
              <h3 className="text-xl font-bold text-white mb-2">Test Complete</h3>
              <p className="text-white/70">
                Your breath health analysis is complete. Here are your results:
              </p>
            </div>
            
            {/* Score display */}
            <div className="flex justify-center mb-10">
              <div className="relative">
                <svg className="w-48 h-48" viewBox="0 0 100 100">
                  <circle 
                    cx="50" 
                    cy="50" 
                    r="45" 
                    fill="none" 
                    stroke="#1f2937" 
                    strokeWidth="10"
                  />
                  <circle 
                    cx="50" 
                    cy="50" 
                    r="45" 
                    fill="none" 
                    stroke={`var(--tw-color-${scoreColor}-500)`}
                    strokeWidth="10" 
                    strokeDasharray="283" 
                    strokeDashoffset={283 - (283 * testResults.overallScore / 100)} 
                    transform="rotate(-90 50 50)"
                  />
                </svg>
                <div className="absolute inset-0 flex flex-col items-center justify-center">
                  <span className={`text-4xl font-bold text-${scoreColor}-400`}>{testResults.overallScore}</span>
                  <span className={`text-sm font-medium text-${scoreColor}-400`}>{scoreLabel}</span>
                </div>
              </div>
            </div>
            
            {/* VSC measurements */}
            <div className="bg-white/5 rounded-lg p-5 mb-6">
              <h4 className="text-lg font-semibold text-white mb-4">Volatile Sulfur Compounds</h4>
              
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="text-white/70 text-sm">Hydrogen Sulfide</span>
                    <span className="text-white/70 text-sm">{testResults.measurements.hydrogenSulfide} ppb</span>
                  </div>
                  <div className="h-2 bg-white/10 rounded-full overflow-hidden">
                    <div 
                      className="h-full bg-blue-500 rounded-full"
                      style={{ width: `${Math.min(100, testResults.measurements.hydrogenSulfide / 2)}%` }}
                    ></div>
                  </div>
                </div>
                
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="text-white/70 text-sm">Methyl Mercaptan</span>
                    <span className="text-white/70 text-sm">{testResults.measurements.methylMercaptan} ppb</span>
                  </div>
                  <div className="h-2 bg-white/10 rounded-full overflow-hidden">
                    <div 
                      className="h-full bg-purple-500 rounded-full"
                      style={{ width: `${Math.min(100, testResults.measurements.methylMercaptan / 2)}%` }}
                    ></div>
                  </div>
                </div>
                
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="text-white/70 text-sm">Dimethyl Sulfide</span>
                    <span className="text-white/70 text-sm">{testResults.measurements.dimethylSulfide} ppb</span>
                  </div>
                  <div className="h-2 bg-white/10 rounded-full overflow-hidden">
                    <div 
                      className="h-full bg-emerald-500 rounded-full"
                      style={{ width: `${Math.min(100, testResults.measurements.dimethylSulfide * 2)}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Interpretation and recommendations */}
            <div className="bg-white/5 rounded-lg p-5 mb-6">
              <h4 className="text-lg font-semibold text-white mb-2">Analysis</h4>
              <p className="text-white/70 mb-4">
                {testResults.interpretation.level === 'good' ? (
                  'Your breath health is good. No significant issues detected.'
                ) : testResults.interpretation.level === 'moderate' ? (
                  'Your breath health shows some concerns that should be addressed.'
                ) : (
                  'Your breath health indicates possible issues that require attention.'
                )}
                {testResults.interpretation.mainCause !== 'normal' && (
                  ` Likely cause: ${testResults.interpretation.mainCause}.`
                )}
              </p>
              
              <h5 className="font-medium text-white mt-4 mb-2">Recommendations:</h5>
              <ul className="list-disc pl-5 space-y-1 text-white/70">
                {testResults.interpretation.recommendations.map((rec, index) => (
                  <li key={index}>{rec}</li>
                ))}
              </ul>
            </div>
            
            {/* Action buttons */}
            <div className="flex justify-center space-x-4">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={resetTest}
                className="px-5 py-2 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-colors"
              >
                New Test
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-5 py-2 bg-blue-600 hover:bg-blue-500 text-white rounded-lg transition-colors"
              >
                View Insights
              </motion.button>
            </div>
          </div>
        );
      
      default:
        return null;
    }
  };
  
  // Render device status information
  const renderDeviceStatus = () => {
    if (testState === 'ready' || !deviceConnected) return null;
    
    return (
      <div className="mb-6 bg-white/5 rounded-lg p-3 flex items-center justify-between">
        <div className="flex items-center">
          <div className="w-2 h-2 rounded-full bg-emerald-400 mr-2"></div>
          <span className="text-sm text-white/70">BreathCheck Device Connected</span>
        </div>
        <div className="flex items-center">
          <svg className="w-4 h-4 text-white/70 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
          <span className="text-sm text-white/70">{deviceBattery}%</span>
        </div>
      </div>
    );
  };
  
  return (
    <div className="max-w-2xl mx-auto">
      {renderDeviceStatus()}
      
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="bg-gradient-to-br from-emerald-900/40 to-emerald-800/20 rounded-xl overflow-hidden border border-emerald-500/20"
      >
        <div className="p-6">
          {renderInstructions()}
        </div>
      </motion.div>
      
      {/* Info cards */}
      {testState === 'ready' && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-8">
          <div className="bg-white/5 p-4 rounded-lg">
            <h4 className="font-medium text-white mb-2">What BreathCheck Measures</h4>
            <p className="text-sm text-white/70">
              BreathCheck analyzes volatile sulfur compounds (VSCs) in your breath, which are primary 
              indicators of oral health issues and bad breath. These include hydrogen sulfide, methyl mercaptan, 
              and dimethyl sulfide.
            </p>
          </div>
          
          <div className="bg-white/5 p-4 rounded-lg">
            <h4 className="font-medium text-white mb-2">Tips for Accurate Results</h4>
            <ul className="list-disc pl-5 text-sm text-white/70 space-y-1">
              <li>Wait at least 3 hours after eating or drinking</li>
              <li>Don't use mouthwash before testing</li>
              <li>Avoid smoking for at least 12 hours prior</li>
              <li>Test at the same time each day for consistency</li>
            </ul>
          </div>
        </div>
      )}
    </div>
  );
};

export default BreathTest; 