import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useSecure } from '../SecureContext';

const AdminSettings = () => {
  const { fetchSystemSettings, updateSystemSettings } = useSecure();
  const [loading, setLoading] = useState(true);
  const [savingChanges, setSavingChanges] = useState(false);
  const [settings, setSettings] = useState({
    apiKeys: {
      openai: '',
      googleCloud: '',
      azure: ''
    },
    serviceSettings: {
      transcriptionProvider: 'openai',
      storageProvider: 'local',
      maxRecordingLength: 60,
      maxStorageMB: 5000
    },
    userPermissions: {
      enableAutoSharing: false,
      enableExport: true,
      requireEncryption: true
    },
    billingAnalysis: {
      sensitivityLevel: 'medium',
      enableAutomaticFlagging: true,
      cptCodeDatabase: 'ada-2023'
    }
  });
  
  const [showAPIKeys, setShowAPIKeys] = useState(false);
  const [settingsChanged, setSettingsChanged] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  
  // Load settings
  useEffect(() => {
    const loadSettings = async () => {
      setLoading(true);
      try {
        const systemSettings = await fetchSystemSettings();
        if (systemSettings) {
          setSettings(systemSettings);
        }
      } catch (error) {
        console.error('Error loading settings:', error);
        setErrorMessage('Failed to load system settings. Please try again later.');
      } finally {
        setLoading(false);
      }
    };
    
    loadSettings();
  }, [fetchSystemSettings]);
  
  // Handle form changes
  const handleInputChange = (section, field, value) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
    
    setSettingsChanged(true);
    
    // Clear any displayed messages
    setSuccessMessage('');
    setErrorMessage('');
  };
  
  // Save settings
  const handleSaveSettings = async () => {
    setSavingChanges(true);
    setSuccessMessage('');
    setErrorMessage('');
    
    try {
      await updateSystemSettings(settings);
      setSuccessMessage('Settings updated successfully!');
      setSettingsChanged(false);
      
      // Auto-hide success message after 3 seconds
      setTimeout(() => {
        setSuccessMessage('');
      }, 3000);
    } catch (error) {
      console.error('Error saving settings:', error);
      setErrorMessage('Failed to update settings. Please try again.');
    } finally {
      setSavingChanges(false);
    }
  };
  
  // Reset settings to defaults
  const handleResetDefaults = () => {
    if (window.confirm('Are you sure you want to reset all settings to defaults? This cannot be undone.')) {
      setSettings({
        apiKeys: {
          openai: '',
          googleCloud: '',
          azure: ''
        },
        serviceSettings: {
          transcriptionProvider: 'openai',
          storageProvider: 'local',
          maxRecordingLength: 60,
          maxStorageMB: 5000
        },
        userPermissions: {
          enableAutoSharing: false,
          enableExport: true,
          requireEncryption: true
        },
        billingAnalysis: {
          sensitivityLevel: 'medium',
          enableAutomaticFlagging: true,
          cptCodeDatabase: 'ada-2023'
        }
      });
      
      setSettingsChanged(true);
    }
  };
  
  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <svg className="animate-spin h-8 w-8 text-emerald-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      </div>
    );
  }
  
  return (
    <div className="space-y-6">
      <div className="bg-white/5 rounded-xl border border-white/10 p-5">
        <h2 className="text-xl font-semibold text-white mb-1">Admin Settings</h2>
        <p className="text-white/60 text-sm mb-6">
          Configure your Smilo Secure system settings. Changes will apply to all users.
        </p>
        
        {/* Status Messages */}
        {successMessage && (
          <motion.div 
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-emerald-900/20 border border-emerald-500/30 text-emerald-200 p-4 rounded-lg mb-6"
          >
            <div className="flex">
              <svg className="w-5 h-5 text-emerald-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span>{successMessage}</span>
            </div>
          </motion.div>
        )}
        
        {errorMessage && (
          <motion.div 
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-red-900/20 border border-red-500/30 text-red-300 p-4 rounded-lg mb-6"
          >
            <div className="flex">
              <svg className="w-5 h-5 text-red-500 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
              <span>{errorMessage}</span>
            </div>
          </motion.div>
        )}
        
        {/* API Keys Section */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-white">API Keys</h3>
            <button
              onClick={() => setShowAPIKeys(!showAPIKeys)}
              className="text-sm px-3 py-1 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-colors flex items-center"
            >
              {showAPIKeys ? (
                <>
                  <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                  Hide Keys
                </>
              ) : (
                <>
                  <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                  </svg>
                  Show Keys
                </>
              )}
            </button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-white/80 mb-2 text-sm font-medium">
                OpenAI API Key
              </label>
              <input 
                type={showAPIKeys ? "text" : "password"}
                value={settings.apiKeys.openai}
                onChange={(e) => handleInputChange('apiKeys', 'openai', e.target.value)}
                placeholder="sk-..."
                className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
              />
            </div>
            
            <div>
              <label className="block text-white/80 mb-2 text-sm font-medium">
                Google Cloud API Key
              </label>
              <input 
                type={showAPIKeys ? "text" : "password"}
                value={settings.apiKeys.googleCloud}
                onChange={(e) => handleInputChange('apiKeys', 'googleCloud', e.target.value)}
                placeholder="AIza..."
                className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
              />
            </div>
            
            <div>
              <label className="block text-white/80 mb-2 text-sm font-medium">
                Azure API Key
              </label>
              <input 
                type={showAPIKeys ? "text" : "password"}
                value={settings.apiKeys.azure}
                onChange={(e) => handleInputChange('apiKeys', 'azure', e.target.value)}
                placeholder="Azure key..."
                className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
              />
            </div>
          </div>
          
          <div className="mt-2 text-white/50 text-xs">
            API keys are stored encrypted and are only used to access external services.
          </div>
        </div>
        
        {/* Service Settings */}
        <div className="mb-8">
          <h3 className="text-lg font-medium text-white mb-4">Service Settings</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-white/80 mb-2 text-sm font-medium">
                Transcription Provider
              </label>
              <select
                value={settings.serviceSettings.transcriptionProvider}
                onChange={(e) => handleInputChange('serviceSettings', 'transcriptionProvider', e.target.value)}
                className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
              >
                <option value="openai">OpenAI Whisper</option>
                <option value="google">Google Speech-to-Text</option>
                <option value="azure">Azure Speech Service</option>
                <option value="local">Local Processing (Limited)</option>
              </select>
            </div>
            
            <div>
              <label className="block text-white/80 mb-2 text-sm font-medium">
                Storage Provider
              </label>
              <select
                value={settings.serviceSettings.storageProvider}
                onChange={(e) => handleInputChange('serviceSettings', 'storageProvider', e.target.value)}
                className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
              >
                <option value="local">Local Storage</option>
                <option value="s3">Amazon S3</option>
                <option value="azure">Azure Blob Storage</option>
                <option value="google">Google Cloud Storage</option>
              </select>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-white/80 mb-2 text-sm font-medium">
                Max Recording Length (minutes)
              </label>
              <input 
                type="number"
                min="1"
                max="240"
                value={settings.serviceSettings.maxRecordingLength}
                onChange={(e) => handleInputChange('serviceSettings', 'maxRecordingLength', Number(e.target.value))}
                className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
              />
            </div>
            
            <div>
              <label className="block text-white/80 mb-2 text-sm font-medium">
                Max Storage (MB)
              </label>
              <input 
                type="number"
                min="100"
                step="100"
                value={settings.serviceSettings.maxStorageMB}
                onChange={(e) => handleInputChange('serviceSettings', 'maxStorageMB', Number(e.target.value))}
                className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>
        
        {/* User Permissions */}
        <div className="mb-8">
          <h3 className="text-lg font-medium text-white mb-4">User Permissions</h3>
          
          <div className="space-y-4">
            <div className="flex items-center">
              <input
                id="enableAutoSharing"
                type="checkbox"
                checked={settings.userPermissions.enableAutoSharing}
                onChange={(e) => handleInputChange('userPermissions', 'enableAutoSharing', e.target.checked)}
                className="h-4 w-4 rounded border-white/30 bg-white/5 text-emerald-600 focus:ring-emerald-500"
              />
              <label htmlFor="enableAutoSharing" className="ml-2 text-white">
                Enable automatic sharing with dental providers
              </label>
            </div>
            
            <div className="flex items-center">
              <input
                id="enableExport"
                type="checkbox"
                checked={settings.userPermissions.enableExport}
                onChange={(e) => handleInputChange('userPermissions', 'enableExport', e.target.checked)}
                className="h-4 w-4 rounded border-white/30 bg-white/5 text-emerald-600 focus:ring-emerald-500"
              />
              <label htmlFor="enableExport" className="ml-2 text-white">
                Allow users to export recordings and transcripts
              </label>
            </div>
            
            <div className="flex items-center">
              <input
                id="requireEncryption"
                type="checkbox"
                checked={settings.userPermissions.requireEncryption}
                onChange={(e) => handleInputChange('userPermissions', 'requireEncryption', e.target.checked)}
                className="h-4 w-4 rounded border-white/30 bg-white/5 text-emerald-600 focus:ring-emerald-500"
              />
              <label htmlFor="requireEncryption" className="ml-2 text-white">
                Require end-to-end encryption for all recordings
              </label>
            </div>
          </div>
        </div>
        
        {/* Billing Analysis Settings */}
        <div className="mb-8">
          <h3 className="text-lg font-medium text-white mb-4">Billing Analysis Settings</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-white/80 mb-2 text-sm font-medium">
                Sensitivity Level
              </label>
              <select
                value={settings.billingAnalysis.sensitivityLevel}
                onChange={(e) => handleInputChange('billingAnalysis', 'sensitivityLevel', e.target.value)}
                className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
              >
                <option value="low">Low - Only flag major issues</option>
                <option value="medium">Medium - Balance between sensitivity and specificity</option>
                <option value="high">High - Flag all potential issues</option>
              </select>
            </div>
            
            <div>
              <label className="block text-white/80 mb-2 text-sm font-medium">
                CPT Code Database
              </label>
              <select
                value={settings.billingAnalysis.cptCodeDatabase}
                onChange={(e) => handleInputChange('billingAnalysis', 'cptCodeDatabase', e.target.value)}
                className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
              >
                <option value="ada-2023">ADA 2023</option>
                <option value="ada-2022">ADA 2022</option>
                <option value="medicare-2023">Medicare 2023</option>
                <option value="custom">Custom Database</option>
              </select>
            </div>
          </div>
          
          <div className="flex items-center">
            <input
              id="enableAutomaticFlagging"
              type="checkbox"
              checked={settings.billingAnalysis.enableAutomaticFlagging}
              onChange={(e) => handleInputChange('billingAnalysis', 'enableAutomaticFlagging', e.target.checked)}
              className="h-4 w-4 rounded border-white/30 bg-white/5 text-emerald-600 focus:ring-emerald-500"
            />
            <label htmlFor="enableAutomaticFlagging" className="ml-2 text-white">
              Enable automatic flagging of potential issues
            </label>
          </div>
        </div>
        
        {/* Action Buttons */}
        <div className="flex justify-end space-x-4">
          <button
            onClick={handleResetDefaults}
            className="px-4 py-2 text-white/70 hover:text-white bg-transparent hover:bg-white/10 rounded-lg transition-colors"
          >
            Reset to Defaults
          </button>
          
          <button
            onClick={handleSaveSettings}
            disabled={!settingsChanged || savingChanges}
            className={`px-4 py-2 rounded-lg transition-colors flex items-center ${
              !settingsChanged || savingChanges
                ? 'bg-white/20 text-white/50 cursor-not-allowed'
                : 'bg-emerald-600 hover:bg-emerald-500 text-white'
            }`}
          >
            {savingChanges ? (
              <>
                <svg className="animate-spin h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Saving...
              </>
            ) : (
              'Save Changes'
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default AdminSettings; 