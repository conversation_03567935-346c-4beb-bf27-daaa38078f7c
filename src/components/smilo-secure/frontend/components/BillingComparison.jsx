import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useSecure } from '../SecureContext';

const BillingComparison = ({ visitId, onBack }) => {
  const { getVisitById, updateBillingInfo } = useSecure();
  const [visit, setVisit] = useState(null);
  const [loading, setLoading] = useState(true);
  const [analysisLoading, setAnalysisLoading] = useState(false);
  const [billingStatement, setBillingStatement] = useState({
    provider: '',
    date: '',
    totalAmount: '',
    items: []
  });
  const [tempItem, setTempItem] = useState({
    procedureCode: '',
    description: '',
    amount: '',
    quantity: 1
  });
  const [showAddItemForm, setShowAddItemForm] = useState(false);
  
  // Load visit data
  useEffect(() => {
    const loadVisit = async () => {
      setLoading(true);
      try {
        const visitData = await getVisitById(visitId);
        setVisit(visitData);
        
        // If the visit has billing analysis data, use it to pre-populate
        if (visitData.billingAnalysis && visitData.billingAnalysis.statement) {
          setBillingStatement(visitData.billingAnalysis.statement);
        }
      } catch (error) {
        console.error('Error loading visit:', error);
      } finally {
        setLoading(false);
      }
    };
    
    loadVisit();
  }, [visitId, getVisitById]);
  
  // Format currency value
  const formatCurrency = (value) => {
    if (!value) return '$0.00';
    
    // Convert string to number if needed
    const numValue = typeof value === 'string' ? parseFloat(value) : value;
    
    // Format as currency
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(numValue);
  };
  
  // Handle billing info update
  const handleUpdateBillingInfo = async () => {
    setAnalysisLoading(true);
    
    try {
      // Calculate total if not set
      if (!billingStatement.totalAmount || billingStatement.totalAmount === '') {
        const total = billingStatement.items.reduce((sum, item) => {
          return sum + (parseFloat(item.amount) * (item.quantity || 1));
        }, 0);
        
        billingStatement.totalAmount = total.toString();
      }
      
      await updateBillingInfo(visitId, billingStatement);
      
      // Refetch the visit to get updated analysis
      const updatedVisit = await getVisitById(visitId);
      setVisit(updatedVisit);
    } catch (error) {
      console.error('Error updating billing info:', error);
    } finally {
      setAnalysisLoading(false);
    }
  };
  
  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setBillingStatement(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  // Handle adding a new item
  const handleAddItem = () => {
    if (!tempItem.procedureCode || !tempItem.description || !tempItem.amount) {
      return;
    }
    
    setBillingStatement(prev => ({
      ...prev,
      items: [...prev.items, { ...tempItem }]
    }));
    
    setTempItem({
      procedureCode: '',
      description: '',
      amount: '',
      quantity: 1
    });
    
    setShowAddItemForm(false);
  };
  
  // Handle temp item changes
  const handleTempItemChange = (e) => {
    const { name, value } = e.target;
    setTempItem(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  // Handle removing an item
  const handleRemoveItem = (index) => {
    setBillingStatement(prev => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index)
    }));
  };
  
  // Render billing comparison result
  const renderComparisonResult = () => {
    if (!visit?.billingAnalysis?.comparisonResult) {
      return (
        <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4 text-blue-200">
          <div className="flex">
            <svg className="w-5 h-5 text-blue-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p className="text-sm">
              Submit your billing information to analyze and compare with the procedures detected in your visit recording.
            </p>
          </div>
        </div>
      );
    }
    
    const { comparisonResult } = visit.billingAnalysis;
    
    return (
      <div className="space-y-4">
        <div className={`rounded-lg p-5 ${
          comparisonResult.status === 'no_issues'
            ? 'bg-emerald-900/20 border border-emerald-500/30 text-emerald-200'
            : 'bg-amber-900/20 border border-amber-500/30 text-amber-200'
        }`}>
          <div className="flex items-start">
            <svg 
              className={`w-6 h-6 mr-3 mt-0.5 flex-shrink-0 ${
                comparisonResult.status === 'no_issues' ? 'text-emerald-400' : 'text-amber-400'
              }`} 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              {comparisonResult.status === 'no_issues' ? (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              ) : (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              )}
            </svg>
            <div>
              <h4 className="text-lg font-medium mb-1">
                {comparisonResult.status === 'no_issues' 
                  ? 'Billing looks accurate' 
                  : 'Potential billing discrepancies detected'}
              </h4>
              <p>{comparisonResult.summary}</p>
            </div>
          </div>
        </div>
        
        {comparisonResult.flaggedItems && comparisonResult.flaggedItems.length > 0 && (
          <div className="bg-white/5 rounded-lg border border-white/10 overflow-hidden">
            <div className="px-5 py-3 border-b border-white/10">
              <h4 className="font-medium text-white">Items requiring attention</h4>
            </div>
            <div className="divide-y divide-white/10">
              {comparisonResult.flaggedItems.map((item, index) => (
                <div key={index} className="px-5 py-4">
                  <div className="flex justify-between items-start mb-1">
                    <div className="flex items-center">
                      <span className={`w-2 h-2 rounded-full mr-2 ${
                        item.severity === 'high' ? 'bg-red-500' : 
                        item.severity === 'medium' ? 'bg-amber-500' : 'bg-blue-500'
                      }`}></span>
                      <h5 className="font-medium text-white">{item.procedureCode} - {item.description}</h5>
                    </div>
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      item.severity === 'high' ? 'bg-red-900/50 text-red-300' : 
                      item.severity === 'medium' ? 'bg-amber-900/50 text-amber-300' : 'bg-blue-900/50 text-blue-300'
                    }`}>
                      {item.severity === 'high' ? 'Critical' : 
                       item.severity === 'medium' ? 'Warning' : 'Info'}
                    </span>
                  </div>
                  <p className="text-white/70 text-sm mb-2">{item.issue}</p>
                  <div className="text-xs text-white/50">
                    Recommended action: {item.recommendedAction}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
        
        {comparisonResult.missingProcedures && comparisonResult.missingProcedures.length > 0 && (
          <div className="bg-white/5 rounded-lg border border-white/10 p-5">
            <h4 className="font-medium text-white mb-3">Procedures mentioned but not billed</h4>
            <ul className="space-y-2">
              {comparisonResult.missingProcedures.map((proc, index) => (
                <li key={index} className="flex items-center text-white/70">
                  <svg className="w-4 h-4 text-blue-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  {proc.name} {proc.code && `(${proc.code})`}
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    );
  };
  
  // Loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <svg className="animate-spin h-8 w-8 text-emerald-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      </div>
    );
  }
  
  // Visit not found
  if (!visit) {
    return (
      <div className="text-center py-8">
        <p className="text-red-400">Visit not found or an error occurred.</p>
        <button 
          onClick={onBack}
          className="mt-4 px-4 py-2 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-colors"
        >
          Go Back
        </button>
      </div>
    );
  }
  
  return (
    <div className="space-y-6">
      {/* Visit Info Card */}
      <div className="bg-white/5 rounded-xl border border-white/10 p-5">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <p className="text-white/60 text-sm">Dentist</p>
            <p className="text-white font-medium">{visit.dentistName}</p>
          </div>
          <div>
            <p className="text-white/60 text-sm">Clinic</p>
            <p className="text-white font-medium">{visit.clinic}</p>
          </div>
          <div>
            <p className="text-white/60 text-sm">Date</p>
            <p className="text-white font-medium">{new Date(visit.date).toLocaleDateString()}</p>
          </div>
        </div>
      </div>
      
      {/* Billing Details Form */}
      <div className="bg-white/5 rounded-xl border border-white/10 p-5">
        <h3 className="text-xl font-semibold text-white mb-4">Billing Information</h3>
        
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-6">
          <div>
            <label className="block text-white/80 mb-2 text-sm font-medium">
              Provider/Insurance
            </label>
            <input 
              type="text"
              name="provider"
              value={billingStatement.provider}
              onChange={handleInputChange}
              placeholder="e.g. Delta Dental"
              className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
            />
          </div>
          
          <div>
            <label className="block text-white/80 mb-2 text-sm font-medium">
              Statement Date
            </label>
            <input 
              type="date"
              name="date"
              value={billingStatement.date}
              onChange={handleInputChange}
              className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
            />
          </div>
          
          <div>
            <label className="block text-white/80 mb-2 text-sm font-medium">
              Total Amount
            </label>
            <input 
              type="text"
              name="totalAmount"
              value={billingStatement.totalAmount}
              onChange={handleInputChange}
              placeholder="e.g. 250.00"
              className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
            />
          </div>
        </div>
        
        {/* Billing Items Table */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-medium text-white">Billed Procedures</h4>
            <button 
              onClick={() => setShowAddItemForm(true)}
              className="text-sm px-3 py-1 bg-emerald-600 hover:bg-emerald-500 text-white rounded transition-colors flex items-center"
              disabled={showAddItemForm}
            >
              <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Add Item
            </button>
          </div>
          
          {billingStatement.items.length === 0 ? (
            <div className="text-center py-6 text-white/60 bg-white/5 rounded-lg">
              <p>No billing items added yet.</p>
              <p className="text-sm">Click "Add Item" to enter your dental procedures.</p>
            </div>
          ) : (
            <div className="bg-white/5 rounded-lg overflow-hidden">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-white/10 text-white/70 text-left text-sm">
                    <th className="py-3 px-4">Code</th>
                    <th className="py-3 px-4">Description</th>
                    <th className="py-3 px-4">Qty</th>
                    <th className="py-3 px-4">Amount</th>
                    <th className="py-3 px-4 text-right">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {billingStatement.items.map((item, index) => (
                    <tr key={index} className="border-b border-white/10 text-white">
                      <td className="py-3 px-4">{item.procedureCode}</td>
                      <td className="py-3 px-4">{item.description}</td>
                      <td className="py-3 px-4">{item.quantity || 1}</td>
                      <td className="py-3 px-4">{formatCurrency(item.amount)}</td>
                      <td className="py-3 px-4 text-right">
                        <button
                          onClick={() => handleRemoveItem(index)}
                          className="text-red-400 hover:text-red-300 p-1"
                          title="Remove item"
                        >
                          <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
          
          {/* Add Item Form */}
          {showAddItemForm && (
            <motion.div 
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-4 bg-white/10 rounded-lg p-4 border border-white/20"
            >
              <h5 className="font-medium text-white mb-3">Add Billing Item</h5>
              <div className="grid grid-cols-1 sm:grid-cols-4 gap-3 mb-4">
                <div>
                  <label className="block text-white/80 mb-1 text-sm">
                    Procedure Code
                  </label>
                  <input 
                    type="text"
                    name="procedureCode"
                    value={tempItem.procedureCode}
                    onChange={handleTempItemChange}
                    placeholder="e.g. D2140"
                    className="w-full bg-white/5 border border-white/10 rounded-lg px-3 py-1.5 text-white text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                  />
                </div>
                
                <div className="sm:col-span-2">
                  <label className="block text-white/80 mb-1 text-sm">
                    Description
                  </label>
                  <input 
                    type="text"
                    name="description"
                    value={tempItem.description}
                    onChange={handleTempItemChange}
                    placeholder="e.g. Amalgam Filling"
                    className="w-full bg-white/5 border border-white/10 rounded-lg px-3 py-1.5 text-white text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                  />
                </div>
                
                <div>
                  <label className="block text-white/80 mb-1 text-sm">
                    Amount
                  </label>
                  <input 
                    type="text"
                    name="amount"
                    value={tempItem.amount}
                    onChange={handleTempItemChange}
                    placeholder="e.g. 150.00"
                    className="w-full bg-white/5 border border-white/10 rounded-lg px-3 py-1.5 text-white text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                  />
                </div>
              </div>
              
              <div className="flex justify-end space-x-3">
                <button 
                  onClick={() => {
                    setShowAddItemForm(false);
                    setTempItem({
                      procedureCode: '',
                      description: '',
                      amount: '',
                      quantity: 1
                    });
                  }}
                  className="px-3 py-1 bg-white/10 hover:bg-white/20 text-white rounded transition-colors text-sm"
                >
                  Cancel
                </button>
                <button 
                  onClick={handleAddItem}
                  className="px-3 py-1 bg-emerald-600 hover:bg-emerald-500 text-white rounded transition-colors text-sm"
                  disabled={!tempItem.procedureCode || !tempItem.description || !tempItem.amount}
                >
                  Add Item
                </button>
              </div>
            </motion.div>
          )}
        </div>
        
        <div className="flex justify-end">
          <button 
            onClick={handleUpdateBillingInfo}
            disabled={billingStatement.items.length === 0 || analysisLoading}
            className={`px-4 py-2 rounded-lg transition-colors flex items-center ${
              billingStatement.items.length === 0 || analysisLoading
                ? 'bg-white/20 text-white/50 cursor-not-allowed'
                : 'bg-emerald-600 hover:bg-emerald-500 text-white'
            }`}
          >
            {analysisLoading ? (
              <>
                <svg className="animate-spin h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Analyzing...
              </>
            ) : (
              <>
                <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                </svg>
                Analyze Billing
              </>
            )}
          </button>
        </div>
      </div>
      
      {/* Comparison Results */}
      <div className="bg-white/5 rounded-xl border border-white/10 p-5">
        <h3 className="text-xl font-semibold text-white mb-4">Analysis Results</h3>
        {renderComparisonResult()}
      </div>
    </div>
  );
};

export default BillingComparison; 