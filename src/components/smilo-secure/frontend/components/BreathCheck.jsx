import React, { useState } from 'react';
import { motion } from 'framer-motion';

// Import sub-components
import DeviceSetup from './breath-check/DeviceSetup';
import BreathTest from './breath-check/BreathTest';
import BreathHistory from './breath-check/BreathHistory';
import BreathInsights from './breath-check/BreathInsights';

const BreathCheck = () => {
  // Manage active tab
  const [activeTab, setActiveTab] = useState('test');
  // Check if device is set up
  const [deviceSetup, setDeviceSetup] = useState(() => {
    const setup = localStorage.getItem('breathcheck_device_setup');
    return setup === 'true';
  });

  // Handle device setup completion
  const handleDeviceSetupComplete = () => {
    localStorage.setItem('breathcheck_device_setup', 'true');
    setDeviceSetup(true);
    setActiveTab('test');
  };

  // Render the appropriate view based on device setup status and active tab
  const renderContent = () => {
    if (!deviceSetup) {
      return <DeviceSetup onComplete={handleDeviceSetupComplete} />;
    }

    switch (activeTab) {
      case 'test':
        return <BreathTest />;
      case 'history':
        return <BreathHistory />;
      case 'insights':
        return <BreathInsights />;
      default:
        return <BreathTest />;
    }
  };

  // Render the tab navigation
  const renderTabs = () => {
    if (!deviceSetup) return null;

    return (
      <div className="flex space-x-1 bg-white/5 p-1 rounded-lg mb-6">
        <TabButton 
          label="Breath Test" 
          active={activeTab === 'test'} 
          onClick={() => setActiveTab('test')}
          icon={
            <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
            </svg>
          }
        />
        <TabButton 
          label="History" 
          active={activeTab === 'history'} 
          onClick={() => setActiveTab('history')}
          icon={
            <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          }
        />
        <TabButton 
          label="Insights" 
          active={activeTab === 'insights'} 
          onClick={() => setActiveTab('insights')}
          icon={
            <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          }
        />
      </div>
    );
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="container mx-auto px-4"
    >
      {renderTabs()}
      {renderContent()}
      
      {/* Powered by Jamevo */}
      <div className="mt-8 flex justify-end">
        <span className="text-white/40 text-xs font-medium">Powered by <span className="text-emerald-400">Jamevo</span></span>
      </div>
    </motion.div>
  );
};

// Tab button component
const TabButton = ({ label, active, onClick, icon }) => (
  <motion.button
    whileTap={{ scale: 0.95 }}
    onClick={onClick}
    className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
      active 
        ? 'bg-emerald-700 text-white' 
        : 'text-white/70 hover:bg-white/10'
    }`}
  >
    {icon && <span className="mr-2">{icon}</span>}
    {label}
  </motion.button>
);

export default BreathCheck; 