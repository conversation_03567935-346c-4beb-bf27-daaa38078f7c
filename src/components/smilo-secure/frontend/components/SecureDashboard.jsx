import React from 'react';
import { motion } from 'framer-motion';
import { useSecure } from '../SecureContext';

const StatCard = ({ title, value, icon, color, subtext }) => (
  <div className={`bg-white/5 p-6 rounded-xl border border-${color}-500/20 hover:bg-white/10 transition-colors`}>
    <div className="flex items-start justify-between">
      <div>
        <h3 className="text-sm font-medium text-white/70">{title}</h3>
        <p className={`text-3xl font-bold mt-2 text-${color}-400`}>{value}</p>
        {subtext && <p className="text-sm text-white/50 mt-1">{subtext}</p>}
      </div>
      <div className={`p-3 rounded-lg bg-${color}-900/30`}>
        {icon}
      </div>
    </div>
  </div>
);

const ActionCard = ({ title, description, buttonText, icon, onClick, color }) => (
  <motion.div 
    className={`bg-gradient-to-br from-${color}-900/40 to-${color}-800/20 rounded-xl overflow-hidden cursor-pointer`}
    whileHover={{ scale: 1.02 }}
    whileTap={{ scale: 0.98 }}
    onClick={onClick}
  >
    <div className="p-6 h-full flex flex-col">
      <div className="flex items-center mb-4">
        <div className={`mr-4 p-3 rounded-lg bg-${color}-500/20`}>
          {icon}
        </div>
        <h3 className="text-xl font-semibold text-white">{title}</h3>
      </div>
      
      <p className="mb-6 text-white/70">{description}</p>
      
      <div className="mt-auto">
        <button className={`px-4 py-2 rounded-lg bg-${color}-600 hover:bg-${color}-500 text-white transition-colors inline-flex items-center`}>
          {buttonText}
          <svg className="ml-2 w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
          </svg>
        </button>
      </div>
    </div>
  </motion.div>
);

const FraudAlertCard = ({ visit }) => (
  <motion.div 
    className="bg-white/5 rounded-xl overflow-hidden border-l-4 border-l-red-500 mb-4 last:mb-0"
    whileHover={{ scale: 1.02 }}
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.3 }}
  >
    <div className="p-4">
      <div className="flex items-center justify-between mb-2">
        <h3 className="font-semibold text-white">{visit.dentistName} - {visit.clinic}</h3>
        <span className="text-sm text-white/50">{visit.date}</span>
      </div>
      
      <div className="flex items-center mb-3">
        <svg className="w-5 h-5 text-red-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
        </svg>
        <p className="text-red-300 font-medium">Possible billing discrepancy detected</p>
      </div>
      
      <p className="text-white/70 mb-3">
        {visit.comparisonResults.summary}
      </p>
      
      <div className="flex space-x-2">
        <button className="px-3 py-1.5 bg-white/10 hover:bg-white/20 rounded-lg text-sm text-white transition-colors">
          View Details
        </button>
        <button className="px-3 py-1.5 bg-red-900/30 hover:bg-red-900/50 rounded-lg text-sm text-red-300 transition-colors">
          Generate Report
        </button>
      </div>
    </div>
  </motion.div>
);

const SecureDashboard = ({ onRecordVisit, onViewHistory, onProfile, onHelp, onAdmin, onBreathCheck }) => {
  const { visits } = useSecure();
  
  // Calculate stats
  const totalVisits = visits.length;
  const analyzedVisits = visits.filter(v => v.status === 'analyzed').length;
  const potentialIssues = visits.filter(v => 
    v.comparisonResults && v.comparisonResults.possibleIssues && v.comparisonResults.possibleIssues.length > 0
  ).length;
  
  // Filter visits with potential fraud
  const flaggedVisits = visits.filter(v => 
    v.comparisonResults && 
    v.comparisonResults.possibleIssues && 
    v.comparisonResults.possibleIssues.length > 0
  );
  
  return (
    <div className="space-y-8">
      {/* Stats section */}
      <section className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <StatCard 
          title="Total Recorded Visits" 
          value={totalVisits}
          icon={
            <svg className="w-6 h-6 text-emerald-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
          }
          color="emerald"
        />
        
        <StatCard 
          title="Analyzed Visits" 
          value={analyzedVisits}
          subtext={totalVisits > 0 ? `${Math.round((analyzedVisits / totalVisits) * 100)}% of total` : 'No visits recorded'}
          icon={
            <svg className="w-6 h-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          }
          color="blue"
        />
        
        <StatCard 
          title="Potential Issues" 
          value={potentialIssues}
          subtext={analyzedVisits > 0 ? `${Math.round((potentialIssues / analyzedVisits) * 100)}% of analyzed visits` : 'No analyzed visits'}
          icon={
            <svg className="w-6 h-6 text-amber-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          }
          color="amber"
        />
      </section>
      
      {/* BreathCheck Premium Feature Promotion */}
      <section className="relative overflow-hidden bg-gradient-to-br from-purple-900/40 to-purple-800/20 rounded-xl border border-purple-500/30">
        <div className="absolute top-0 right-0 w-64 h-64 bg-purple-500/10 rounded-full -mr-32 -mt-32 blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-64 h-64 bg-emerald-500/10 rounded-full -ml-32 -mb-32 blur-3xl"></div>
        
        <div className="relative p-6">
          <div className="flex items-center mb-1">
            <span className="bg-emerald-900/30 text-emerald-400 text-xs font-medium py-1 px-2 rounded-full mr-2">
              PREMIUM
            </span>
            <h3 className="text-xl font-bold text-white">BreathCheck™ by Smilo</h3>
          </div>
          
          <p className="text-white/70 mb-6 max-w-3xl">
            Take your oral health monitoring to the next level with advanced breath analysis. 
            Detect potential issues early, track improvements over time, and get personalized recommendations.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div className="bg-white/5 p-4 rounded-lg flex items-start">
              <div className="p-2 mr-3 rounded-lg bg-emerald-900/30">
                <svg className="w-5 h-5 text-emerald-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <div>
                <h4 className="text-white font-medium">Early Detection</h4>
                <p className="text-white/60 text-sm">Identify gum disease, infection, and other issues before they become serious</p>
              </div>
            </div>
            
            <div className="bg-white/5 p-4 rounded-lg flex items-start">
              <div className="p-2 mr-3 rounded-lg bg-purple-900/30">
                <svg className="w-5 h-5 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <div>
                <h4 className="text-white font-medium">VSC Analysis</h4>
                <p className="text-white/60 text-sm">Measure volatile sulfur compounds with clinical precision</p>
              </div>
            </div>
            
            <div className="bg-white/5 p-4 rounded-lg flex items-start">
              <div className="p-2 mr-3 rounded-lg bg-blue-900/30">
                <svg className="w-5 h-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
              </div>
              <div>
                <h4 className="text-white font-medium">Personalized Insights</h4>
                <p className="text-white/60 text-sm">Get AI-powered recommendations based on your unique oral profile</p>
              </div>
            </div>
          </div>
          
          <div className="flex justify-center">
            <button
              onClick={onBreathCheck}
              className="px-5 py-3 bg-gradient-to-r from-emerald-600 to-emerald-500 hover:from-emerald-500 hover:to-emerald-400 text-white rounded-lg transition-colors shadow-lg flex items-center font-medium"
            >
              Explore BreathCheck™
              <svg className="ml-2 w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
              </svg>
            </button>
          </div>
        </div>
      </section>
      
      {/* Actions section */}
      <section className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <ActionCard 
          title="Record New Visit"
          description="Use your microphone to record a dental visit conversation. The audio will be transcribed and analyzed for fraud detection."
          buttonText="Start Recording"
          icon={
            <svg className="w-6 h-6 text-emerald-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
            </svg>
          }
          onClick={onRecordVisit}
          color="emerald"
        />
        
        <ActionCard 
          title="View Visit History"
          description="Browse your recorded dental visits, review transcripts, and check for possible billing discrepancies."
          buttonText="View History"
          icon={
            <svg className="w-6 h-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          }
          onClick={onViewHistory}
          color="blue"
        />
      </section>
      
      {/* Additional Actions */}
      <section className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <motion.div
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={onProfile}
          className="bg-white/5 p-4 rounded-lg cursor-pointer flex items-center hover:bg-white/10 transition-colors"
        >
          <div className="p-2 mr-3 rounded-lg bg-purple-500/20">
            <svg className="w-5 h-5 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </div>
          <div>
            <h3 className="font-medium text-white">Your Profile</h3>
            <p className="text-sm text-white/60">Manage personal and insurance information</p>
          </div>
        </motion.div>
        
        <motion.div
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={onHelp}
          className="bg-white/5 p-4 rounded-lg cursor-pointer flex items-center hover:bg-white/10 transition-colors"
        >
          <div className="p-2 mr-3 rounded-lg bg-blue-500/20">
            <svg className="w-5 h-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div>
            <h3 className="font-medium text-white">Help & Resources</h3>
            <p className="text-sm text-white/60">Learn how to use Smilo Secure</p>
          </div>
        </motion.div>
        
        <motion.div
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={onAdmin}
          className="bg-white/5 p-4 rounded-lg cursor-pointer flex items-center hover:bg-white/10 transition-colors"
        >
          <div className="p-2 mr-3 rounded-lg bg-amber-500/20">
            <svg className="w-5 h-5 text-amber-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          </div>
          <div>
            <h3 className="font-medium text-white">Admin Settings</h3>
            <p className="text-sm text-white/60">Configure system settings</p>
          </div>
        </motion.div>
      </section>
      
      {/* Alerts section */}
      {flaggedVisits.length > 0 && (
        <section>
          <h2 className="text-xl font-bold text-white mb-4 flex items-center">
            <svg className="w-5 h-5 mr-2 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            Potential Fraud Alerts
          </h2>
          
          <div>
            {flaggedVisits.map(visit => (
              <FraudAlertCard key={visit.id} visit={visit} />
            ))}
          </div>
        </section>
      )}
      
      {/* How it works section */}
      <section className="mt-10 pt-6 border-t border-white/10">
        <h2 className="text-xl font-bold text-white mb-4">How Smilo Secure Works</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white/5 p-5 rounded-lg">
            <div className="bg-emerald-900/30 w-10 h-10 rounded-full flex items-center justify-center mb-4">
              <span className="text-emerald-400 font-bold">1</span>
            </div>
            <h3 className="text-lg font-medium text-white mb-2">Record Your Visit</h3>
            <p className="text-white/70">
              Record your dental appointment conversation using your microphone. All data is encrypted and securely stored.
            </p>
          </div>
          
          <div className="bg-white/5 p-5 rounded-lg">
            <div className="bg-blue-900/30 w-10 h-10 rounded-full flex items-center justify-center mb-4">
              <span className="text-blue-400 font-bold">2</span>
            </div>
            <h3 className="text-lg font-medium text-white mb-2">Add Billing Information</h3>
            <p className="text-white/70">
              Upload or manually enter your dental billing information, including procedure codes and charges.
            </p>
          </div>
          
          <div className="bg-white/5 p-5 rounded-lg">
            <div className="bg-purple-900/30 w-10 h-10 rounded-full flex items-center justify-center mb-4">
              <span className="text-purple-400 font-bold">3</span>
            </div>
            <h3 className="text-lg font-medium text-white mb-2">Analyze for Discrepancies</h3>
            <p className="text-white/70">
              Our AI compares the transcript with billing data to identify possible inconsistencies or fraudulent charges.
            </p>
          </div>
        </div>
      </section>
      
      {/* Privacy notice */}
      <section className="bg-white/5 p-4 rounded-lg">
        <div className="flex">
          <svg className="w-6 h-6 text-blue-400 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <p className="text-sm text-white/70">
            <span className="font-medium text-white">Privacy Notice:</span> All recordings are end-to-end encrypted and processed locally when possible. 
            Your data is never shared with third parties. You can delete your recordings at any time.
            <button onClick={onHelp} className="text-blue-400 ml-1 hover:underline">Learn more</button>
          </p>
        </div>
      </section>
    </div>
  );
};

export default SecureDashboard; 