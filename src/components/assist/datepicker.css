/* Custom styling for the DatePicker component */

.smilo-datepicker.react-date-picker {
  width: 100%;
  font-family: inherit;
  position: relative;
}

.smilo-datepicker .react-date-picker__wrapper {
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  padding: 0.5rem 0.75rem;
  height: 42px;
  background-color: white;
  display: flex;
  align-items: center;
  transition: all 0.2s ease;
}

.smilo-datepicker .react-date-picker__inputGroup {
  padding-right: 10px;
  color: #4b5563;
  min-width: 0; /* Prevent input group from growing beyond container */
  flex: 1; /* Allow input group to take available space */
}

/* Fix inputs to prevent overflow */
.smilo-datepicker .react-date-picker__inputGroup__input {
  min-width: 0.8rem !important; /* Narrow inputs */
  max-width: 1.5rem !important; /* Limit max width */
  padding: 0 2px !important; /* Reduce padding */
  height: 24px !important; /* Consistent height */
  font-weight: 500;
}

/* Year field should be wider */
.smilo-datepicker .react-date-picker__inputGroup__year {
  min-width: 2rem !important;
  max-width: 3rem !important;
}

/* Fix for separator spacing */
.smilo-datepicker .react-date-picker__inputGroup__divider {
  padding: 0 2px;
}

.smilo-datepicker .react-date-picker__button {
  padding: 0 4px;
  margin-left: 2px;
  cursor: pointer;
}

.smilo-datepicker .react-date-picker__button:enabled:hover .react-date-picker__button__icon,
.smilo-datepicker .react-date-picker__button:enabled:focus .react-date-picker__button__icon {
  stroke: #4f46e5;
}

/* Dark theme adjustments for SmiloAssistPage */
:root.dark .smilo-datepicker .react-date-picker__wrapper,
.dark-theme .smilo-datepicker .react-date-picker__wrapper {
  background-color: #1f2937 !important; /* bg-gray-800 */
  border-color: #374151 !important; /* border-gray-700 */
  color: #ffffff !important;
  transition: all 0.2s ease;
}

:root.dark .smilo-datepicker .react-date-picker__inputGroup,
.dark-theme .smilo-datepicker .react-date-picker__inputGroup {
  color: #e5e7eb !important; /* text-gray-200 */
}

:root.dark .smilo-datepicker .react-date-picker__inputGroup__input,
.dark-theme .smilo-datepicker .react-date-picker__inputGroup__input {
  color: #ffffff !important;
  background-color: transparent !important;
  font-weight: 500;
}

:root.dark .smilo-datepicker .react-date-picker__button svg,
.dark-theme .smilo-datepicker .react-date-picker__button svg {
  stroke: #818cf8 !important; /* indigo-400 */
  transition: all 0.2s ease;
}

/* Calendar styling */
.react-calendar {
  width: 350px;
  border-radius: 1rem;
  border: none;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.2), 0 10px 10px -5px rgba(0, 0, 0, 0.1);
  font-family: inherit;
  overflow: hidden;
  animation: scaleUp 0.2s ease forwards;
  transform-origin: top center;
  margin-top: 4px;
  z-index: 100;
}

@keyframes scaleUp {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Dark theme calendar */
:root.dark .react-calendar,
.dark-theme .react-calendar {
  background-color: #1e293b;
  color: #e5e7eb;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.5), 0 10px 10px -5px rgba(0, 0, 0, 0.4);
}

/* Navigation bar styling */
.react-calendar__navigation {
  display: flex;
  height: 48px;
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  margin-bottom: 0;
}

.react-calendar__navigation button {
  color: white;
  min-width: 44px;
  background: none;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  border-radius: 0;
}

.react-calendar__navigation button:disabled {
  background-color: rgba(0, 0, 0, 0.1);
  opacity: 0.5;
}

.react-calendar__navigation button:enabled:hover,
.react-calendar__navigation button:enabled:focus {
  background-color: rgba(255, 255, 255, 0.15);
}

/* Month header styling */
.react-calendar__month-view__weekdays {
  text-align: center;
  text-transform: uppercase;
  font-weight: 700;
  font-size: 0.7em;
  padding: 12px 0 4px;
  background-color: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
  letter-spacing: 0.05em;
}

:root.dark .react-calendar__month-view__weekdays,
.dark-theme .react-calendar__month-view__weekdays {
  background-color: #0f172a;
  border-bottom: 1px solid #1e293b;
  color: #64748b;
}

.react-calendar__month-view__weekdays__weekday {
  padding: 0.5em;
}

.react-calendar__month-view__weekdays__weekday abbr {
  text-decoration: none;
  cursor: default;
}

/* Days styling */
.react-calendar__month-view__days__day {
  padding: 10px 0;
  border-radius: 0;
  font-weight: 500;
}

.react-calendar__tile {
  text-align: center;
  padding: 10px;
  border-radius: 0;
  transition: all 0.15s ease;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  margin: 2px;
  aspect-ratio: 1 / 1;
}

.react-calendar__tile:enabled:hover,
.react-calendar__tile:enabled:focus {
  background-color: rgba(79, 70, 229, 0.1);
  color: #4f46e5;
  border-radius: 9999px;
}

:root.dark .react-calendar__tile:enabled:hover,
:root.dark .react-calendar__tile:enabled:focus,
.dark-theme .react-calendar__tile:enabled:hover,
.dark-theme .react-calendar__tile:enabled:focus {
  background-color: rgba(99, 102, 241, 0.2);
  color: #818cf8;
}

.react-calendar__tile--now {
  background: rgba(79, 70, 229, 0.1);
  color: #4f46e5;
  border-radius: 9999px;
  font-weight: 700;
}

:root.dark .react-calendar__tile--now,
.dark-theme .react-calendar__tile--now {
  background: rgba(99, 102, 241, 0.2);
  color: #818cf8;
}

.react-calendar__tile--active {
  background: linear-gradient(135deg, #4f46e5, #7c3aed) !important;
  color: white !important;
  border-radius: 9999px !important;
  box-shadow: 0 4px 6px -1px rgba(79, 70, 229, 0.2), 0 2px 4px -1px rgba(79, 70, 229, 0.1);
  font-weight: 700;
}

:root.dark .react-calendar__tile--active,
.dark-theme .react-calendar__tile--active {
  box-shadow: 0 4px 6px -1px rgba(99, 102, 241, 0.4), 0 2px 4px -1px rgba(99, 102, 241, 0.2);
}

.react-calendar__month-view__days__day--weekend {
  color: #f43f5e;
}

:root.dark .react-calendar__month-view__days__day--weekend,
.dark-theme .react-calendar__month-view__days__day--weekend {
  color: #fb7185;
}

.react-calendar__month-view__days__day--neighboringMonth {
  color: #9ca3af;
  opacity: 0.4;
}

:root.dark .react-calendar__month-view__days__day--neighboringMonth,
.dark-theme .react-calendar__month-view__days__day--neighboringMonth {
  color: #64748b;
  opacity: 0.4;
}

/* Fix for disabled state */
.smilo-datepicker.react-date-picker--disabled .react-date-picker__wrapper {
  background-color: #f3f4f6;
  opacity: 0.7;
}

/* When focused */
.smilo-datepicker .react-date-picker__wrapper:focus-within {
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.25);
  outline: none;
}

/* Calendar container should have auto width on mobile */
@media (max-width: 640px) {
  .react-calendar {
    width: calc(100vw - 40px);
    max-width: 350px;
  }
} 