import React from 'react';
import ReactDatePicker from 'react-date-picker';
import 'react-calendar/dist/Calendar.css';
import 'react-date-picker/dist/DatePicker.css';
import './datepicker.css'; // Custom styling

const DatePicker = ({ date, onDatesChange, className, disabled, minDate, darkMode }) => {
  // Format function to avoid date inputs from overflowing
  const formatShortDate = (locale, date) => {
    // Only display day number without leading zeros
    return date.getDate().toString();
  };

  // Format function for month
  const formatShortMonth = (locale, date) => {
    // Display month without leading zeros
    return (date.getMonth() + 1).toString();
  };

  return (
    <div className={`${className || ''} ${darkMode ? 'dark-theme' : ''}`}>
      <ReactDatePicker
        value={date}
        onChange={onDatesChange}
        minDate={minDate}
        disabled={disabled}
        clearIcon={null}
        className="smilo-datepicker w-full border border-gray-700 rounded-lg"
        calendarIcon={
          <svg 
            className="h-5 w-5 text-indigo-400" 
            xmlns="http://www.w3.org/2000/svg" 
            viewBox="0 0 20 20" 
            fill="currentColor"
          >
            <path 
              fillRule="evenodd" 
              d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" 
              clipRule="evenodd" 
            />
          </svg>
        }
        formatDay={formatShortDate}
        formatMonth={formatShortMonth}
        dayPlaceholder="d"
        monthPlaceholder="m"
        yearPlaceholder="yyyy"
        showLeadingZeros={false}
        format="d / M / y" // Simplified format with spaces
        isOpen={false} // Ensures calendar opens on click and not by default
        calendarType="US" // Use US calendar type (Sunday as first day)
      />
    </div>
  );
};

export default DatePicker; 