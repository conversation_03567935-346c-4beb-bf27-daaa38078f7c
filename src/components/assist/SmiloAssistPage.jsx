import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FiCalendar, FiMessageSquare, FiHelpCircle, FiLink, FiX, FiDollarSign, FiCheckCircle, FiMessageCircle, FiBarChart2, FiClipboard, FiUsers } from 'react-icons/fi';
import { SmiloAssistScheduler } from './index';
import WaitlistSignup from '../waitlist/WaitlistSignup';

export default function SmiloAssistPage() {
  const [activeTab, setActiveTab] = useState('overview');
  const [showDemo, setShowDemo] = useState(false);
  const [demoType, setDemoType] = useState('patient');
  const [showChatDemo, setShowChatDemo] = useState(false);
  const [showWaitlist, setShowWaitlist] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState('');
  const [chatMessages, setChatMessages] = useState([
    { sender: 'ai', text: 'Hi there! I\'m your Smilo Dental Assistant. How can I help you today?' }
  ]);
  const [newMessage, setNewMessage] = useState('');
  const [comfortTab, setComfortTab] = useState(0);
  const [show3DDemo, setShow3DDemo] = useState(false);
  const [answers, setAnswers] = useState({});
  const [personalizedPlan, setPersonalizedPlan] = useState(null);

  const anxietyQuestions = [
    {
      question: "What makes you most nervous about dental visits?",
      options: ["Pain", "Needles", "Embarrassment", "Uncertainty"]
    },
    {
      question: "How would you rate your anxiety level?",
      options: ["Very High", "High", "Moderate", "Low"]
    },
    {
      question: "What helps you feel more comfortable?",
      options: ["Music", "TV", "Breathing", "Distraction"]
    }
  ];

  const matchedProviders = [
    {
      id: 1,
      name: "Dr. Sarah Johnson",
      specialty: "Gentle Dentistry",
      matchScore: 95,
      amenities: ["TVs", "Weighted Blankets", "Nitrous Oxide"]
    },
    {
      id: 2,
      name: "Dr. Michael Chen",
      specialty: "Sedation Dentistry",
      matchScore: 88,
      amenities: ["IV Sedation", "Aromatherapy", "Music"]
    }
  ];

  const openDemo = (type) => {
    setDemoType(type);
    setShowDemo(true);
  };

  const openWaitlist = (plan) => {
    setSelectedPlan(plan);
    setShowWaitlist(true);
  };

  const handleSendMessage = () => {
    if (!newMessage.trim()) return;
    
    // Add user message
    setChatMessages(prev => [...prev, { sender: 'user', text: newMessage }]);
    
    // Clear input
    setNewMessage('');
    
    // Simulate AI response after delay
    setTimeout(() => {
      let response = '';
      const userMsg = newMessage.toLowerCase();
      
      if (userMsg.includes('appointment') || userMsg.includes('schedule')) {
        response = 'I can help you schedule an appointment! You can use our online scheduler or I can connect you with our front desk. Would you like me to help you find available times?';
      } else if (userMsg.includes('tooth') && (userMsg.includes('pain') || userMsg.includes('hurt'))) {
        response = 'I\'m sorry to hear you\'re experiencing tooth pain. This could be due to several reasons such as decay, infection, or sensitivity. I recommend scheduling an urgent appointment. Would you like me to help with that?';
      } else if (userMsg.includes('insurance') || userMsg.includes('coverage')) {
        response = 'We accept most major dental insurance plans. Our billing team can help verify your coverage before your appointment. Would you like information about specific providers we work with?';
      } else if (userMsg.includes('cost') || userMsg.includes('price') || userMsg.includes('fee')) {
        response = 'Our treatment costs vary depending on the procedure and your specific needs. We offer transparent pricing and financing options. Would you like to see our general fee schedule?';
      } else {
        response = 'Thanks for your question. I\'d be happy to help with that. Would you like me to find more specific information from our dental team?';
      }
      
      setChatMessages(prev => [...prev, { sender: 'ai', text: response }]);
    }, 1000);
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleAnswer = (questionIdx, answerIdx) => {
    setAnswers(prev => ({
      ...prev,
      [questionIdx]: answerIdx
    }));
  };

  const generateAnxietyReport = () => {
    // Generate personalized plan based on answers
    const plan = [
      "4-7-8 breathing exercise before appointment",
      "Request noise-canceling headphones at check-in",
      "Bring a stress ball or fidget toy",
      "Schedule morning appointments when possible"
    ];
    setPersonalizedPlan(plan);
  };

  return (
    <div className="min-h-screen relative">
      {/* Background elements to match App.jsx style */}
      <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/10 via-gray-900/0 to-purple-500/10 pointer-events-none" />
      <div className="absolute top-0 right-0 w-1/2 h-1/2 bg-[radial-gradient(circle,rgba(129,140,248,0.1),transparent_50%)] pointer-events-none" />
      <div className="absolute bottom-0 left-0 w-1/2 h-1/2 bg-[radial-gradient(circle,rgba(168,85,247,0.1),transparent_50%)] pointer-events-none" />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 relative z-10">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-extrabold text-white sm:text-5xl sm:tracking-tight lg:text-6xl mb-4">
            Smilo <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-indigo-300 to-purple-400">Assist</span>
          </h1>
          <p className="max-w-xl mx-auto text-xl text-gray-300">
            AI-powered scheduling that saves time for you and your staff
          </p>
        </div>

        {/* Navigation Tabs */}
        <div className="bg-gray-800/60 backdrop-blur-sm shadow-xl border border-gray-700/50 rounded-xl overflow-hidden mb-8">
          <div className="flex border-b border-gray-700/50 overflow-x-auto">
            <button
              onClick={() => setActiveTab('overview')}
              className={`flex items-center px-6 py-4 text-sm font-medium ${
                activeTab === 'overview'
                  ? 'border-b-2 border-indigo-500 text-indigo-400'
                  : 'text-gray-400 hover:text-gray-300'
              }`}
            >
              <FiHelpCircle className="mr-2" />
              Overview
            </button>
            <button
              onClick={() => setActiveTab('how-it-works')}
              className={`flex items-center px-6 py-4 text-sm font-medium ${
                activeTab === 'how-it-works'
                  ? 'border-b-2 border-indigo-500 text-indigo-400'
                  : 'text-gray-400 hover:text-gray-300'
              }`}
            >
              <FiMessageSquare className="mr-2" />
              How It Works
            </button>
            <button
              onClick={() => setActiveTab('pricing')}
              className={`flex items-center px-6 py-4 text-sm font-medium ${
                activeTab === 'pricing'
                  ? 'border-b-2 border-indigo-500 text-indigo-400'
                  : 'text-gray-400 hover:text-gray-300'
              }`}
            >
              <FiDollarSign className="mr-2" />
              Pricing
            </button>
            <button
              onClick={() => setActiveTab('ai-assistant')}
              className={`flex items-center px-6 py-4 text-sm font-medium ${
                activeTab === 'ai-assistant'
                  ? 'border-b-2 border-indigo-500 text-indigo-400'
                  : 'text-gray-400 hover:text-gray-300'
              }`}
            >
              <FiMessageCircle className="mr-2" />
              AI Assistant
            </button>
            <button
              onClick={() => setActiveTab('scheduler')}
              className={`flex items-center px-6 py-4 text-sm font-medium ${
                activeTab === 'scheduler'
                  ? 'border-b-2 border-indigo-500 text-indigo-400'
                  : 'text-gray-400 hover:text-gray-300'
              }`}
            >
              <FiCalendar className="mr-2" />
              Scheduler
            </button>
            <button
              onClick={() => setActiveTab('comfort')}
              className={`flex items-center px-6 py-4 text-sm font-medium ${
                activeTab === 'comfort'
                  ? 'border-b-2 border-indigo-500 text-indigo-400'
                  : 'text-gray-400 hover:text-gray-300'
              }`}
            >
              <FiHelpCircle className="mr-2" />
              Comfort & Empowerment
            </button>
            <button
              onClick={() => setActiveTab('premium')}
              className={`flex items-center px-6 py-4 text-sm font-medium ${
                activeTab === 'premium'
                  ? 'border-b-2 border-indigo-500 text-indigo-400'
                  : 'text-gray-400 hover:text-gray-300'
              }`}
            >
              <FiDollarSign className="mr-2" />
              Premium Features
            </button>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {/* Overview Tab */}
            {activeTab === 'overview' && (
              <div>
                <h2 className="text-2xl font-bold text-white mb-4">Smart Dental Scheduling</h2>
                <p className="text-gray-300 mb-6">
                  Smilo Assist revolutionizes dental practice management with AI-powered scheduling that saves time, 
                  reduces administrative burden, and improves patient experience. Our system analyzes patient symptoms 
                  to recommend appropriate treatments and prioritizes urgent cases automatically.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
                  <div className="bg-gray-800/40 border border-gray-700/50 shadow-lg rounded-lg p-6 hover:bg-gray-800/60 transition duration-300">
                    <div className="flex items-center justify-center h-12 w-12 rounded-md bg-gradient-to-br from-blue-500 to-indigo-600 text-white mb-4">
                      <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-medium text-white mb-2">Save Time</h3>
                    <p className="text-gray-300">
                      AI helps prioritize appointments and suggests optimal time slots, reducing administrative overhead.
                    </p>
                  </div>
                    
                  <div className="bg-gray-800/40 border border-gray-700/50 shadow-lg rounded-lg p-6 hover:bg-gray-800/60 transition duration-300">
                    <div className="flex items-center justify-center h-12 w-12 rounded-md bg-gradient-to-br from-indigo-500 to-purple-600 text-white mb-4">
                      <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-medium text-white mb-2">Staff Approval</h3>
                    <p className="text-gray-300">
                      AI makes recommendations, but your staff maintains control to approve or adjust appointments.
                    </p>
                  </div>
                    
                  <div className="bg-gray-800/40 border border-gray-700/50 shadow-lg rounded-lg p-6 hover:bg-gray-800/60 transition duration-300">
                    <div className="flex items-center justify-center h-12 w-12 rounded-md bg-gradient-to-br from-purple-500 to-blue-600 text-white mb-4">
                      <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-medium text-white mb-2">Instant Updates</h3>
                    <p className="text-gray-300">
                      Patients receive real-time updates on their appointment status and AI-powered reminders.
                    </p>
                  </div>
                </div>

                <div className="mt-8 flex justify-center">
                  <button 
                    onClick={() => setActiveTab('scheduler')}
                    className="px-6 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-md hover:from-indigo-500 hover:to-purple-500 transition-all duration-300 shadow-lg shadow-indigo-500/20"
                  >
                    Try Smilo Assist Now
                  </button>
                  <button 
                    onClick={() => openDemo('patient')} 
                    className="ml-4 px-6 py-3 border border-indigo-500/30 text-indigo-400 rounded-md hover:bg-indigo-900/30 transition-colors duration-300"
                  >
                    Watch Demo
                  </button>
                </div>
              </div>
            )}

            {/* How It Works Tab */}
            {activeTab === 'how-it-works' && (
              <div>
                <h2 className="text-2xl font-bold text-white mb-4">How Smilo Assist Works</h2>
                <p className="text-gray-300 mb-8">
                  Our AI-powered system transforms your scheduling process with intelligent automation and predictive analytics.
                </p>

                <div className="space-y-8">
                  <div className="flex flex-col md:flex-row gap-6 items-start">
                    <div className="flex-shrink-0 bg-gradient-to-br from-blue-500/20 to-indigo-500/20 border border-indigo-500/30 rounded-full p-4">
                      <span className="text-2xl font-bold text-indigo-400">1</span>
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-white mb-2">Symptom Analysis</h3>
                      <p className="text-gray-300">
                        Patients describe their symptoms or needs through our intelligent intake form. Our AI analyzes this information to understand severity, urgency, and recommended treatment types.
                      </p>
                    </div>
                  </div>

                  <div className="flex flex-col md:flex-row gap-6 items-start">
                    <div className="flex-shrink-0 bg-gradient-to-br from-indigo-500/20 to-purple-500/20 border border-purple-500/30 rounded-full p-4">
                      <span className="text-2xl font-bold text-indigo-400">2</span>
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-white mb-2">Intelligent Scheduling</h3>
                      <p className="text-gray-300">
                        The system automatically suggests optimal appointment durations based on the required treatment and prioritizes urgent cases. This ensures that your calendar is optimized for both efficiency and patient care.
                      </p>
                    </div>
                  </div>

                  <div className="flex flex-col md:flex-row gap-6 items-start">
                    <div className="flex-shrink-0 bg-gradient-to-br from-purple-500/20 to-blue-500/20 border border-blue-500/30 rounded-full p-4">
                      <span className="text-2xl font-bold text-indigo-400">3</span>
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-white mb-2">Staff Oversight</h3>
                      <p className="text-gray-300">
                        While our AI makes smart recommendations, your staff maintains complete control. They can review, approve, or adjust appointments as needed, ensuring the human touch is never lost.
                      </p>
                    </div>
                  </div>

                  <div className="flex flex-col md:flex-row gap-6 items-start">
                    <div className="flex-shrink-0 bg-gradient-to-br from-blue-500/20 to-purple-500/20 border border-indigo-500/30 rounded-full p-4">
                      <span className="text-2xl font-bold text-indigo-400">4</span>
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-white mb-2">Automated Communication</h3>
                      <p className="text-gray-300">
                        Patients receive automated confirmations, reminders, and updates about their appointments. This reduces no-shows and keeps your schedule running smoothly.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* AI Assistant Tab */}
            {activeTab === 'ai-assistant' && (
              <div>
                <h2 className="text-2xl font-bold text-white mb-4">Smart AI Dental Assistant</h2>
                <p className="text-gray-300 mb-6">
                  Our AI Assistant is designed to provide instant help to patients browsing your website. It can answer common questions, 
                  guide users to the right information, and help schedule appointments - all while integrating seamlessly with your existing 
                  systems and knowledge base.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div>
                    <div className="space-y-6">
                      <div className="bg-gray-800/40 border border-gray-700/50 shadow-lg rounded-lg p-6">
                        <div className="flex items-center justify-center h-12 w-12 rounded-md bg-gradient-to-br from-indigo-500 to-purple-600 text-white mb-4">
                          <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                          </svg>
                        </div>
                        <h3 className="text-lg font-medium text-white mb-2">24/7 Patient Support</h3>
                        <p className="text-gray-300">
                          Provide instant answers to patient questions at any time, day or night. Our AI can handle frequently asked questions about services, hours, insurance, and more.
                        </p>
                      </div>

                      <div className="bg-gray-800/40 border border-gray-700/50 shadow-lg rounded-lg p-6">
                        <div className="flex items-center justify-center h-12 w-12 rounded-md bg-gradient-to-br from-blue-500 to-indigo-600 text-white mb-4">
                          <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                          </svg>
                        </div>
                        <h3 className="text-lg font-medium text-white mb-2">Flexible Knowledge Base</h3>
                        <p className="text-gray-300">
                          Connect to your existing documentation or let us build a custom knowledge base about your practice, procedures, and policies. The AI continuously learns and improves.
                        </p>
                      </div>

                      <div className="bg-gray-800/40 border border-gray-700/50 shadow-lg rounded-lg p-6">
                        <div className="flex items-center justify-center h-12 w-12 rounded-md bg-gradient-to-br from-purple-500 to-pink-600 text-white mb-4">
                          <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122" />
                          </svg>
                        </div>
                        <h3 className="text-lg font-medium text-white mb-2">Seamless Integration</h3>
                        <p className="text-gray-300">
                          The assistant integrates with your scheduling system, patient records, and website. It can help patients book appointments and find the information they need without leaving your site.
                        </p>
                      </div>
                    </div>

                    <div className="mt-8">
                      <button 
                        onClick={() => setShowChatDemo(true)}
                        className="w-full px-6 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-md hover:from-indigo-500 hover:to-purple-500 transition-all duration-300 shadow-lg shadow-indigo-500/20 flex items-center justify-center"
                      >
                        <FiMessageCircle className="mr-2" />
                        Try AI Assistant Demo
                      </button>
                    </div>
                  </div>

                  <div className="bg-gray-800/40 border border-gray-700/50 rounded-lg p-6 flex flex-col">
                    <h3 className="text-lg font-medium text-white mb-4 flex items-center">
                      <span className="inline-flex items-center justify-center h-6 w-6 rounded-full bg-indigo-500 text-white text-xs mr-2">AI</span>
                      Key Benefits
                    </h3>
                    
                    <ul className="space-y-4 text-gray-300 mb-6 flex-grow">
                      <li className="flex">
                        <FiCheckCircle className="text-indigo-400 mt-1 mr-2 flex-shrink-0" />
                        <span>Reduce phone calls by answering common questions automatically</span>
                      </li>
                      <li className="flex">
                        <FiCheckCircle className="text-indigo-400 mt-1 mr-2 flex-shrink-0" />
                        <span>Capture more appointment bookings with 24/7 scheduling support</span>
                      </li>
                      <li className="flex">
                        <FiCheckCircle className="text-indigo-400 mt-1 mr-2 flex-shrink-0" />
                        <span>Improve patient experience with instant answers to their questions</span>
                      </li>
                      <li className="flex">
                        <FiCheckCircle className="text-indigo-400 mt-1 mr-2 flex-shrink-0" />
                        <span>Free up staff time to focus on in-office patient care</span>
                      </li>
                      <li className="flex">
                        <FiCheckCircle className="text-indigo-400 mt-1 mr-2 flex-shrink-0" />
                        <span>Guide patients to relevant educational content on your website</span>
                      </li>
                    </ul>

                    <div className="bg-indigo-900/30 rounded-lg p-4 border border-indigo-500/30">
                      <h4 className="font-medium text-indigo-300 mb-2">Integration Options</h4>
                      <p className="text-gray-300 text-sm">
                        The AI Assistant can be configured to use our pre-built dental knowledge base or connect to your existing documentation. 
                        Custom training is available to ensure the assistant accurately represents your practice's specific services and policies.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Pricing Tab */}
            {activeTab === 'pricing' && (
              <div>
                <h2 className="text-2xl font-bold text-white mb-4">Pricing Plans</h2>
                <p className="text-gray-300 mb-8">
                  Choose the plan that best fits your practice size and needs. All plans include our core AI scheduling technology.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                  {/* Basic Plan */}
                  <div className="bg-gray-800/40 border border-gray-700/50 rounded-lg overflow-hidden hover:shadow-lg hover:shadow-indigo-500/10 transition-all duration-300">
                    <div className="p-6 border-b border-gray-700/50">
                      <h3 className="text-xl font-bold text-white">Basic</h3>
                      <div className="mt-4 flex items-baseline">
                        <span className="text-3xl font-extrabold text-white">$99</span>
                        <span className="ml-1 text-gray-400">/month</span>
                      </div>
                      <p className="mt-2 text-sm text-gray-400">For small practices with up to 2 providers</p>
                    </div>
                    <div className="p-6">
                      <ul className="space-y-3">
                        <li className="flex items-start">
                          <FiCheckCircle className="flex-shrink-0 w-5 h-5 text-indigo-400 mt-0.5" />
                          <span className="ml-3 text-gray-300">Up to 150 appointments/month</span>
                        </li>
                        <li className="flex items-start">
                          <FiCheckCircle className="flex-shrink-0 w-5 h-5 text-indigo-400 mt-0.5" />
                          <span className="ml-3 text-gray-300">AI symptom analysis</span>
                        </li>
                        <li className="flex items-start">
                          <FiCheckCircle className="flex-shrink-0 w-5 h-5 text-indigo-400 mt-0.5" />
                          <span className="ml-3 text-gray-300">Automated reminders</span>
                        </li>
                        <li className="flex items-start">
                          <FiCheckCircle className="flex-shrink-0 w-5 h-5 text-indigo-400 mt-0.5" />
                          <span className="ml-3 text-gray-300">Email support</span>
                        </li>
                      </ul>
                      <button 
                        className="mt-6 w-full px-4 py-2 border border-indigo-500 text-indigo-400 rounded-md hover:bg-indigo-900/30 transition-colors duration-300"
                        onClick={() => openWaitlist('Basic')}
                      >
                        Select Plan
                      </button>
                    </div>
                  </div>

                  {/* Professional Plan */}
                  <div className="bg-gray-800/60 border-2 border-indigo-500/50 rounded-lg overflow-hidden shadow-lg shadow-indigo-500/10 relative">
                    <div className="absolute top-0 right-0 bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-3 py-1 text-xs font-medium uppercase">Popular</div>
                    <div className="p-6 border-b border-gray-700/50">
                      <h3 className="text-xl font-bold text-white">Professional</h3>
                      <div className="mt-4 flex items-baseline">
                        <span className="text-3xl font-extrabold text-white">$249</span>
                        <span className="ml-1 text-gray-400">/month</span>
                      </div>
                      <p className="mt-2 text-sm text-gray-400">For growing practices with 3-5 providers</p>
                    </div>
                    <div className="p-6">
                      <ul className="space-y-3">
                        <li className="flex items-start">
                          <FiCheckCircle className="flex-shrink-0 w-5 h-5 text-indigo-400 mt-0.5" />
                          <span className="ml-3 text-gray-300">Up to 500 appointments/month</span>
                        </li>
                        <li className="flex items-start">
                          <FiCheckCircle className="flex-shrink-0 w-5 h-5 text-indigo-400 mt-0.5" />
                          <span className="ml-3 text-gray-300">Everything in Basic</span>
                        </li>
                        <li className="flex items-start">
                          <FiCheckCircle className="flex-shrink-0 w-5 h-5 text-indigo-400 mt-0.5" />
                          <span className="ml-3 text-gray-300">Advanced analytics dashboard</span>
                        </li>
                        <li className="flex items-start">
                          <FiCheckCircle className="flex-shrink-0 w-5 h-5 text-indigo-400 mt-0.5" />
                          <span className="ml-3 text-gray-300">Priority phone support</span>
                        </li>
                        <li className="flex items-start">
                          <FiCheckCircle className="flex-shrink-0 w-5 h-5 text-indigo-400 mt-0.5" />
                          <span className="ml-3 text-gray-300">SMS notifications</span>
                        </li>
                      </ul>
                      <button 
                        className="mt-6 w-full px-4 py-2 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-md hover:from-indigo-500 hover:to-purple-500 transition-all duration-300"
                        onClick={() => openWaitlist('Professional')}
                      >
                        Select Plan
                      </button>
                    </div>
                  </div>

                  {/* Enterprise Plan */}
                  <div className="bg-gray-800/40 border border-gray-700/50 rounded-lg overflow-hidden hover:shadow-lg hover:shadow-indigo-500/10 transition-all duration-300">
                    <div className="p-6 border-b border-gray-700/50">
                      <h3 className="text-xl font-bold text-white">Enterprise</h3>
                      <div className="mt-4 flex items-baseline">
                        <span className="text-3xl font-extrabold text-white">Custom</span>
                        <span className="ml-1 text-gray-400">pricing</span>
                      </div>
                      <p className="mt-2 text-sm text-gray-400">For large practices with 6+ providers</p>
                    </div>
                    <div className="p-6">
                      <ul className="space-y-3">
                        <li className="flex items-start">
                          <FiCheckCircle className="flex-shrink-0 w-5 h-5 text-indigo-400 mt-0.5" />
                          <span className="ml-3 text-gray-300">Unlimited appointments</span>
                        </li>
                        <li className="flex items-start">
                          <FiCheckCircle className="flex-shrink-0 w-5 h-5 text-indigo-400 mt-0.5" />
                          <span className="ml-3 text-gray-300">Everything in Professional</span>
                        </li>
                        <li className="flex items-start">
                          <FiCheckCircle className="flex-shrink-0 w-5 h-5 text-indigo-400 mt-0.5" />
                          <span className="ml-3 text-gray-300">Dedicated account manager</span>
                        </li>
                        <li className="flex items-start">
                          <FiCheckCircle className="flex-shrink-0 w-5 h-5 text-indigo-400 mt-0.5" />
                          <span className="ml-3 text-gray-300">Custom integrations</span>
                        </li>
                        <li className="flex items-start">
                          <FiCheckCircle className="flex-shrink-0 w-5 h-5 text-indigo-400 mt-0.5" />
                          <span className="ml-3 text-gray-300">Staff training sessions</span>
                        </li>
                      </ul>
                      <button 
                        className="mt-6 w-full px-4 py-2 border border-indigo-500 text-indigo-400 rounded-md hover:bg-indigo-900/30 transition-colors duration-300"
                        onClick={() => openWaitlist('Enterprise')}
                      >
                        Contact Sales
                      </button>
                    </div>
                  </div>
                </div>

                <div className="mt-10 bg-gray-800/30 border border-gray-700/50 backdrop-blur-sm rounded-lg p-6">
                  <h3 className="text-lg font-medium text-indigo-400 mb-2">Why Choose Smilo Assist?</h3>
                  <p className="text-gray-300 mb-4">
                    Investing in Smilo Assist pays for itself by reducing no-shows, optimizing provider schedules, and freeing up staff time from administrative tasks.
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="bg-gray-800/50 border border-gray-700/50 p-4 rounded-md shadow-sm">
                      <h4 className="font-medium text-white mb-2">Increased Efficiency</h4>
                      <p className="text-sm text-gray-300">Save 15-20 hours of staff time per week on scheduling tasks</p>
                    </div>
                    <div className="bg-gray-800/50 border border-gray-700/50 p-4 rounded-md shadow-sm">
                      <h4 className="font-medium text-white mb-2">Reduced No-Shows</h4>
                      <p className="text-sm text-gray-300">Lower no-show rates by up to 30% with our smart reminder system</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* New: Smilo Comfort & Empowerment Features Section */}
            {activeTab === 'comfort' && (
              <div>
                <h2 className="text-2xl font-bold text-white mb-4">Comfort & Empowerment Features</h2>
                <p className="text-gray-300 mb-6">Transform patient experience and reduce anxiety with our AI-powered comfort features. See how these tools can increase patient satisfaction and retention.</p>
                
                <div className="mb-6">
                  <div className="flex border-b border-gray-700/50 overflow-x-auto">
                    {['Virtual Comfort Experience', 'AI-Driven Anxiety Questionnaire', 'Personalized Anxiety Reduction', 'Patient Empowerment Dashboard', 'Smart Matching to Gentle Providers'].map((tab, idx) => (
                      <button
                        key={tab}
                        onClick={() => setComfortTab(idx)}
                        className={`flex items-center px-6 py-4 text-sm font-medium ${comfortTab === idx ? 'border-b-2 border-indigo-500 text-indigo-400' : 'text-gray-400 hover:text-gray-300'}`}
                      >
                        {tab}
                      </button>
                    ))}
                  </div>
                </div>

                <div className="bg-gray-800/40 border border-gray-700/50 rounded-lg p-8 shadow-lg transition-all duration-300">
                  {comfortTab === 0 && (
                    <div>
                      <h3 className="text-xl font-bold text-indigo-300 mb-2">Virtual Comfort Experience</h3>
                      <p className="text-gray-300 mb-4">Walk through your upcoming appointment with interactive 3D visualizations, soothing voiceovers, and personalized explanations. Smilo makes every step clear and gentle, so you know exactly what to expect.</p>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div className="bg-indigo-900/30 rounded-lg p-4 border border-indigo-500/30">
                          <h4 className="text-indigo-200 font-medium mb-2">Try the Interactive Demo</h4>
                          <div className="aspect-video bg-gray-900 rounded-lg mb-4 flex items-center justify-center">
                            <button 
                              onClick={() => setShow3DDemo(true)}
                              className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-500 transition-colors"
                            >
                              Launch 3D Experience
                            </button>
                          </div>
                          <p className="text-sm text-gray-300">See how patients experience their visit before they arrive</p>
                        </div>
                        
                        <div className="bg-indigo-900/30 rounded-lg p-4 border border-indigo-500/30">
                          <h4 className="text-indigo-200 font-medium mb-2">Practice Benefits</h4>
                          <ul className="space-y-2 text-gray-200">
                            <li className="flex items-start">
                              <FiCheckCircle className="text-indigo-400 mt-1 mr-2 flex-shrink-0" />
                              <span>Reduce no-shows by 40%</span>
                            </li>
                            <li className="flex items-start">
                              <FiCheckCircle className="text-indigo-400 mt-1 mr-2 flex-shrink-0" />
                              <span>Increase patient satisfaction scores</span>
                            </li>
                            <li className="flex items-start">
                              <FiCheckCircle className="text-indigo-400 mt-1 mr-2 flex-shrink-0" />
                              <span>Save staff time on phone explanations</span>
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  )}

                  {comfortTab === 1 && (
                    <div>
                      <h3 className="text-xl font-bold text-indigo-300 mb-2">AI-Driven Anxiety Questionnaire</h3>
                      <p className="text-gray-300 mb-4">Take a short, friendly quiz before your visit. Smilo learns your specific fears and creates custom coping strategies.</p>
                      
                      <div className="bg-indigo-900/30 rounded-lg p-6 border border-indigo-500/30 mb-6">
                        <h4 className="text-indigo-200 font-medium mb-4">Try the Questionnaire</h4>
                        <div className="space-y-4">
                          {anxietyQuestions.map((q, idx) => (
                            <div key={idx} className="bg-gray-800/50 rounded-lg p-4">
                              <p className="text-gray-200 mb-2">{q.question}</p>
                              <div className="flex flex-wrap gap-2">
                                {q.options.map((opt, optIdx) => (
                                  <button
                                    key={optIdx}
                                    onClick={() => handleAnswer(idx, optIdx)}
                                    className={`px-3 py-1 rounded-full text-sm ${
                                      answers[idx] === optIdx
                                        ? 'bg-indigo-600 text-white'
                                        : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                                    }`}
                                  >
                                    {opt}
                                  </button>
                                ))}
                              </div>
                            </div>
                          ))}
                        </div>
                        <button
                          onClick={generateAnxietyReport}
                          className="mt-4 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-500 transition-colors"
                        >
                          Generate Personalized Report
                        </button>
                      </div>
                    </div>
                  )}

                  {comfortTab === 2 && (
                    <div>
                      <h3 className="text-xl font-bold text-indigo-300 mb-2">Personalized Anxiety Reduction</h3>
                      <p className="text-gray-300 mb-4">Based on your quiz, Smilo offers personalized tips and recommendations.</p>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="bg-indigo-900/30 rounded-lg p-4 border border-indigo-500/30">
                          <h4 className="text-indigo-200 font-medium mb-2">Your Personalized Plan</h4>
                          {personalizedPlan ? (
                            <div className="space-y-3">
                              {personalizedPlan.map((item, idx) => (
                                <div key={idx} className="flex items-start">
                                  <FiCheckCircle className="text-indigo-400 mt-1 mr-2 flex-shrink-0" />
                                  <span className="text-gray-200">{item}</span>
                                </div>
                              ))}
                            </div>
                          ) : (
                            <p className="text-gray-400">Complete the anxiety questionnaire to generate your plan</p>
                          )}
                        </div>
                        
                        <div className="bg-indigo-900/30 rounded-lg p-4 border border-indigo-500/30">
                          <h4 className="text-indigo-200 font-medium mb-2">Practice Integration</h4>
                          <ul className="space-y-2 text-gray-200">
                            <li className="flex items-start">
                              <FiCheckCircle className="text-indigo-400 mt-1 mr-2 flex-shrink-0" />
                              <span>Automatically syncs with patient records</span>
                            </li>
                            <li className="flex items-start">
                              <FiCheckCircle className="text-indigo-400 mt-1 mr-2 flex-shrink-0" />
                              <span>Staff receives anxiety alerts before appointments</span>
                            </li>
                            <li className="flex items-start">
                              <FiCheckCircle className="text-indigo-400 mt-1 mr-2 flex-shrink-0" />
                              <span>Track anxiety reduction over time</span>
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  )}

                  {comfortTab === 3 && (
                    <div>
                      <h3 className="text-xl font-bold text-indigo-300 mb-2">Patient Empowerment Dashboard</h3>
                      <p className="text-gray-300 mb-4">Get your "Visit Game Plan" with real-time updates and support.</p>
                      
                      <div className="bg-indigo-900/30 rounded-lg p-6 border border-indigo-500/30">
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                          <div className="bg-gray-800/50 rounded-lg p-4">
                            <h4 className="text-indigo-200 font-medium mb-2">Today's Visit</h4>
                            <p className="text-gray-300">Cleaning & Checkup</p>
                            <p className="text-sm text-gray-400">Estimated time: 45 minutes</p>
                          </div>
                          <div className="bg-gray-800/50 rounded-lg p-4">
                            <h4 className="text-indigo-200 font-medium mb-2">Comfort Options</h4>
                            <ul className="space-y-1 text-gray-300">
                              <li>Noise-canceling headphones</li>
                              <li>Weighted blanket</li>
                              <li>TV entertainment</li>
                            </ul>
                          </div>
                          <div className="bg-gray-800/50 rounded-lg p-4">
                            <h4 className="text-indigo-200 font-medium mb-2">Quick Actions</h4>
                            <button className="w-full px-3 py-1 bg-indigo-600 text-white rounded-md text-sm hover:bg-indigo-500">
                              Request Comfort Item
                            </button>
                            <button className="w-full mt-2 px-3 py-1 border border-indigo-500 text-indigo-400 rounded-md text-sm hover:bg-indigo-900/30">
                              Signal for Break
                            </button>
                          </div>
                        </div>
                        
                        <div className="bg-gray-800/50 rounded-lg p-4">
                          <h4 className="text-indigo-200 font-medium mb-2">Practice Benefits</h4>
                          <ul className="space-y-2 text-gray-200">
                            <li className="flex items-start">
                              <FiCheckCircle className="text-indigo-400 mt-1 mr-2 flex-shrink-0" />
                              <span>Reduce procedure interruptions by 60%</span>
                            </li>
                            <li className="flex items-start">
                              <FiCheckCircle className="text-indigo-400 mt-1 mr-2 flex-shrink-0" />
                              <span>Improve patient communication</span>
                            </li>
                            <li className="flex items-start">
                              <FiCheckCircle className="text-indigo-400 mt-1 mr-2 flex-shrink-0" />
                              <span>Streamline comfort requests</span>
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  )}

                  {comfortTab === 4 && (
                    <div>
                      <h3 className="text-xl font-bold text-indigo-300 mb-2">Smart Matching to Gentle Providers</h3>
                      <p className="text-gray-300 mb-4">Get listed as a Certified Gentle Provider on the Smilo platform. When patients search for anxiety-friendly dentists in your area, your practice will be prominently featured with your comfort amenities and gentle care certification.</p>
                      
                      <div className="bg-indigo-900/30 rounded-lg p-6 border border-indigo-500/30">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <h4 className="text-indigo-200 font-medium mb-4">Search Filters</h4>
                            <div className="space-y-4">
                              <div>
                                <label className="block text-gray-300 mb-2">Comfort Level</label>
                                <select className="w-full bg-gray-800 border border-gray-700 rounded-md px-3 py-2 text-gray-300">
                                  <option>Very Gentle</option>
                                  <option>Gentle</option>
                                  <option>Standard</option>
                                </select>
                              </div>
                              <div>
                                <label className="block text-gray-300 mb-2">Sedation Options</label>
                                <div className="space-y-2">
                                  {['Nitrous Oxide', 'Oral Sedation', 'IV Sedation'].map((opt) => (
                                    <label key={opt} className="flex items-center">
                                      <input type="checkbox" className="mr-2" />
                                      <span className="text-gray-300">{opt}</span>
                                    </label>
                                  ))}
                                </div>
                              </div>
                              <div>
                                <label className="block text-gray-300 mb-2">Comfort Amenities</label>
                                <div className="space-y-2">
                                  {['TVs', 'Weighted Blankets', 'Aromatherapy', 'Music'].map((opt) => (
                                    <label key={opt} className="flex items-center">
                                      <input type="checkbox" className="mr-2" />
                                      <span className="text-gray-300">{opt}</span>
                                    </label>
                                  ))}
                                </div>
                              </div>
                            </div>
                          </div>
                          
                          <div>
                            <h4 className="text-indigo-200 font-medium mb-4">Smilo Website Integration</h4>
                            <div className="space-y-4">
                              {matchedProviders.map((provider) => (
                                <div key={provider.id} className="bg-gray-800/50 rounded-lg p-4">
                                  <div className="flex justify-between items-start">
                                    <div>
                                      <h5 className="text-white font-medium">{provider.name}</h5>
                                      <p className="text-sm text-gray-400">{provider.specialty}</p>
                                    </div>
                                    <div className="flex flex-col items-end">
                                      <span className="px-2 py-1 bg-indigo-600 text-white text-xs rounded-full mb-1">
                                        {provider.matchScore}% Match
                                      </span>
                                      <span className="px-2 py-1 bg-green-600 text-white text-xs rounded-full">
                                        Certified Gentle Provider
                                      </span>
                                    </div>
                                  </div>
                                  <div className="mt-2 flex flex-wrap gap-2">
                                    {provider.amenities.map((amenity) => (
                                      <span key={amenity} className="px-2 py-1 bg-gray-700 text-gray-300 text-xs rounded-full">
                                        {amenity}
                                      </span>
                                    ))}
                                  </div>
                                  <div className="mt-3 text-sm text-gray-400">
                                    <span className="flex items-center">
                                      <FiCheckCircle className="text-green-400 mr-1" />
                                      Listed on Smilo.com
                                    </span>
                                    <span className="flex items-center">
                                      <FiCheckCircle className="text-green-400 mr-1" />
                                      Featured in local searches
                                    </span>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>

                        <div className="mt-6 bg-gray-800/50 rounded-lg p-4">
                          <h4 className="text-indigo-200 font-medium mb-2">Practice Benefits</h4>
                          <ul className="space-y-2 text-gray-200">
                            <li className="flex items-start">
                              <FiCheckCircle className="text-indigo-400 mt-1 mr-2 flex-shrink-0" />
                              <span>Get listed as a Certified Gentle Provider on Smilo.com</span>
                            </li>
                            <li className="flex items-start">
                              <FiCheckCircle className="text-indigo-400 mt-1 mr-2 flex-shrink-0" />
                              <span>Appear in local searches for anxiety-friendly dentists</span>
                            </li>
                            <li className="flex items-start">
                              <FiCheckCircle className="text-indigo-400 mt-1 mr-2 flex-shrink-0" />
                              <span>Receive qualified patient referrals from Smilo</span>
                            </li>
                            <li className="flex items-start">
                              <FiCheckCircle className="text-indigo-400 mt-1 mr-2 flex-shrink-0" />
                              <span>Showcase your comfort amenities and gentle care approach</span>
                            </li>
                          </ul>
                        </div>

                        <div className="mt-6 bg-gray-800/50 rounded-lg p-4">
                          <h4 className="text-indigo-200 font-medium mb-2">Certification Requirements</h4>
                          <div className="space-y-4">
                            <div className="bg-indigo-900/30 rounded-lg p-4 border border-indigo-500/30">
                              <h5 className="text-white font-medium mb-2">Patient Reviews & Testimonials</h5>
                              <ul className="space-y-2 text-gray-200">
                                <li className="flex items-start">
                                  <FiCheckCircle className="text-indigo-400 mt-1 mr-2 flex-shrink-0" />
                                  <span>Minimum 25 verified patient reviews with 4.5+ star rating</span>
                                </li>
                                <li className="flex items-start">
                                  <FiCheckCircle className="text-indigo-400 mt-1 mr-2 flex-shrink-0" />
                                  <span>At least 10 video testimonials from anxious patients</span>
                                </li>
                                <li className="flex items-start">
                                  <FiCheckCircle className="text-indigo-400 mt-1 mr-2 flex-shrink-0" />
                                  <span>Reviews must specifically mention gentle care and anxiety management</span>
                                </li>
                              </ul>
                            </div>
                            
                            <div className="bg-indigo-900/30 rounded-lg p-4 border border-indigo-500/30">
                              <h5 className="text-white font-medium mb-2">Practice Standards</h5>
                              <ul className="space-y-2 text-gray-200">
                                <li className="flex items-start">
                                  <FiCheckCircle className="text-indigo-400 mt-1 mr-2 flex-shrink-0" />
                                  <span>Staff trained in anxiety management techniques</span>
                                </li>
                                <li className="flex items-start">
                                  <FiCheckCircle className="text-indigo-400 mt-1 mr-2 flex-shrink-0" />
                                  <span>Minimum 3 comfort amenities available</span>
                                </li>
                                <li className="flex items-start">
                                  <FiCheckCircle className="text-indigo-400 mt-1 mr-2 flex-shrink-0" />
                                  <span>Regular patient satisfaction surveys</span>
                                </li>
                              </ul>
                            </div>

                            <div className="bg-indigo-900/30 rounded-lg p-4 border border-indigo-500/30">
                              <h5 className="text-white font-medium mb-2">Certification Process</h5>
                              <ol className="space-y-2 text-gray-200 list-decimal pl-5">
                                <li>Submit initial application with practice details</li>
                                <li>Collect and submit required patient reviews and video testimonials</li>
                                <li>Complete Smilo's gentle care training program</li>
                                <li>Pass on-site assessment of comfort amenities and staff training</li>
                                <li>Maintain certification through quarterly reviews and patient feedback</li>
                              </ol>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Scheduler Tab */}
            {activeTab === 'scheduler' && (
              <div>
                <h2 className="text-2xl font-bold text-white mb-4">Book an Appointment</h2>
                <p className="text-gray-300 mb-6">
                  Experience our AI-powered scheduling system. Describe your symptoms and our system will recommend
                  the appropriate treatment and appointment duration.
                </p>
                <div className="bg-gray-800/40 border border-gray-700/50 shadow-xl rounded-xl overflow-hidden">
                  <SmiloAssistScheduler darkMode={true} />
                </div>
              </div>
            )}

            {/* New: Premium Features Section */}
            {activeTab === 'premium' && (
              <div>
                <h2 className="text-2xl font-bold text-white mb-4">Premium Practice Features</h2>
                <p className="text-gray-300 mb-6">Transform your practice with AI-powered tools that increase efficiency, boost revenue, and improve patient satisfaction.</p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* AI Scheduling Optimization */}
                  <div className="bg-gray-800/40 border border-gray-700/50 rounded-lg p-6">
                    <div className="flex items-center mb-4">
                      <div className="w-12 h-12 bg-indigo-600 rounded-lg flex items-center justify-center mr-4">
                        <FiCalendar className="w-6 h-6 text-white" />
                      </div>
                      <h3 className="text-xl font-bold text-white">AI Scheduling Optimization</h3>
                    </div>
                    <ul className="space-y-3 text-gray-300 mb-4">
                      <li className="flex items-start">
                        <FiCheckCircle className="text-indigo-400 mt-1 mr-2 flex-shrink-0" />
                        <span>Smart appointment sequencing to maximize chair utilization</span>
                      </li>
                      <li className="flex items-start">
                        <FiCheckCircle className="text-indigo-400 mt-1 mr-2 flex-shrink-0" />
                        <span>Predictive scheduling based on treatment duration patterns</span>
                      </li>
                      <li className="flex items-start">
                        <FiCheckCircle className="text-indigo-400 mt-1 mr-2 flex-shrink-0" />
                        <span>Automated buffer time calculation for complex procedures</span>
                      </li>
                    </ul>
                    <div className="bg-indigo-900/30 rounded-lg p-4 border border-indigo-500/30">
                      <p className="text-sm text-gray-300">Average practice sees 15-20% increase in daily chair utilization</p>
                    </div>
                  </div>

                  {/* Automated Patient Follow-up */}
                  <div className="bg-gray-800/40 border border-gray-700/50 rounded-lg p-6">
                    <div className="flex items-center mb-4">
                      <div className="w-12 h-12 bg-indigo-600 rounded-lg flex items-center justify-center mr-4">
                        <FiMessageSquare className="w-6 h-6 text-white" />
                      </div>
                      <h3 className="text-xl font-bold text-white">Automated Patient Follow-up</h3>
                    </div>
                    <ul className="space-y-3 text-gray-300 mb-4">
                      <li className="flex items-start">
                        <FiCheckCircle className="text-indigo-400 mt-1 mr-2 flex-shrink-0" />
                        <span>AI-powered post-treatment check-ins and satisfaction surveys</span>
                      </li>
                      <li className="flex items-start">
                        <FiCheckCircle className="text-indigo-400 mt-1 mr-2 flex-shrink-0" />
                        <span>Automated recall system with personalized messaging</span>
                      </li>
                      <li className="flex items-start">
                        <FiCheckCircle className="text-indigo-400 mt-1 mr-2 flex-shrink-0" />
                        <span>Smart treatment plan follow-up based on patient history</span>
                      </li>
                    </ul>
                    <div className="bg-indigo-900/30 rounded-lg p-4 border border-indigo-500/30">
                      <p className="text-sm text-gray-300">Practices report 30% increase in treatment plan acceptance</p>
                    </div>
                  </div>

                  {/* Practice Analytics Dashboard */}
                  <div className="bg-gray-800/40 border border-gray-700/50 rounded-lg p-6">
                    <div className="flex items-center mb-4">
                      <div className="w-12 h-12 bg-indigo-600 rounded-lg flex items-center justify-center mr-4">
                        <FiBarChart2 className="w-6 h-6 text-white" />
                      </div>
                      <h3 className="text-xl font-bold text-white">Practice Analytics Dashboard</h3>
                    </div>
                    <ul className="space-y-3 text-gray-300 mb-4">
                      <li className="flex items-start">
                        <FiCheckCircle className="text-indigo-400 mt-1 mr-2 flex-shrink-0" />
                        <span>Real-time revenue tracking and forecasting</span>
                      </li>
                      <li className="flex items-start">
                        <FiCheckCircle className="text-indigo-400 mt-1 mr-2 flex-shrink-0" />
                        <span>Patient satisfaction metrics and trends</span>
                      </li>
                      <li className="flex items-start">
                        <FiCheckCircle className="text-indigo-400 mt-1 mr-2 flex-shrink-0" />
                        <span>Staff performance and efficiency analytics</span>
                      </li>
                    </ul>
                    <div className="bg-indigo-900/30 rounded-lg p-4 border border-indigo-500/30">
                      <p className="text-sm text-gray-300">Data-driven insights lead to 25% average revenue growth</p>
                    </div>
                  </div>

                  {/* Smart Treatment Planning */}
                  <div className="bg-gray-800/40 border border-gray-700/50 rounded-lg p-6">
                    <div className="flex items-center mb-4">
                      <div className="w-12 h-12 bg-indigo-600 rounded-lg flex items-center justify-center mr-4">
                        <FiClipboard className="w-6 h-6 text-white" />
                      </div>
                      <h3 className="text-xl font-bold text-white">Smart Treatment Planning</h3>
                    </div>
                    <ul className="space-y-3 text-gray-300 mb-4">
                      <li className="flex items-start">
                        <FiCheckCircle className="text-indigo-400 mt-1 mr-2 flex-shrink-0" />
                        <span>AI-powered treatment recommendations based on patient history</span>
                      </li>
                      <li className="flex items-start">
                        <FiCheckCircle className="text-indigo-400 mt-1 mr-2 flex-shrink-0" />
                        <span>Automated insurance coverage estimation</span>
                      </li>
                      <li className="flex items-start">
                        <FiCheckCircle className="text-indigo-400 mt-1 mr-2 flex-shrink-0" />
                        <span>Personalized treatment presentation tools</span>
                      </li>
                    </ul>
                    <div className="bg-indigo-900/30 rounded-lg p-4 border border-indigo-500/30">
                      <p className="text-sm text-gray-300">40% increase in treatment plan acceptance rates</p>
                    </div>
                  </div>

                  {/* Staff Training & Support */}
                  <div className="bg-gray-800/40 border border-gray-700/50 rounded-lg p-6">
                    <div className="flex items-center mb-4">
                      <div className="w-12 h-12 bg-indigo-600 rounded-lg flex items-center justify-center mr-4">
                        <FiUsers className="w-6 h-6 text-white" />
                      </div>
                      <h3 className="text-xl font-bold text-white">Staff Training & Support</h3>
                    </div>
                    <ul className="space-y-3 text-gray-300 mb-4">
                      <li className="flex items-start">
                        <FiCheckCircle className="text-indigo-400 mt-1 mr-2 flex-shrink-0" />
                        <span>AI-powered staff training modules</span>
                      </li>
                      <li className="flex items-start">
                        <FiCheckCircle className="text-indigo-400 mt-1 mr-2 flex-shrink-0" />
                        <span>Real-time guidance during patient interactions</span>
                      </li>
                      <li className="flex items-start">
                        <FiCheckCircle className="text-indigo-400 mt-1 mr-2 flex-shrink-0" />
                        <span>Performance tracking and improvement suggestions</span>
                      </li>
                    </ul>
                    <div className="bg-indigo-900/30 rounded-lg p-4 border border-indigo-500/30">
                      <p className="text-sm text-gray-300">50% reduction in staff training time</p>
                    </div>
                  </div>

                  {/* Revenue Optimization */}
                  <div className="bg-gray-800/40 border border-gray-700/50 rounded-lg p-6">
                    <div className="flex items-center mb-4">
                      <div className="w-12 h-12 bg-indigo-600 rounded-lg flex items-center justify-center mr-4">
                        <FiDollarSign className="w-6 h-6 text-white" />
                      </div>
                      <h3 className="text-xl font-bold text-white">Revenue Optimization</h3>
                    </div>
                    <ul className="space-y-3 text-gray-300 mb-4">
                      <li className="flex items-start">
                        <FiCheckCircle className="text-indigo-400 mt-1 mr-2 flex-shrink-0" />
                        <span>Smart pricing recommendations based on market data</span>
                      </li>
                      <li className="flex items-start">
                        <FiCheckCircle className="text-indigo-400 mt-1 mr-2 flex-shrink-0" />
                        <span>Automated insurance verification and claims processing</span>
                      </li>
                      <li className="flex items-start">
                        <FiCheckCircle className="text-indigo-400 mt-1 mr-2 flex-shrink-0" />
                        <span>Patient financing optimization and approval prediction</span>
                      </li>
                    </ul>
                    <div className="bg-indigo-900/30 rounded-lg p-4 border border-indigo-500/30">
                      <p className="text-sm text-gray-300">35% increase in collections and reduced claim denials</p>
                    </div>
                  </div>
                </div>

                <div className="mt-8 bg-gray-800/50 rounded-lg p-6">
                  <h3 className="text-xl font-bold text-white mb-4">ROI Calculator</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-indigo-900/30 rounded-lg p-4 border border-indigo-500/30">
                      <h4 className="text-indigo-200 font-medium mb-2">Monthly Benefits</h4>
                      <ul className="space-y-2 text-gray-200">
                        <li>+$15,000 in increased collections</li>
                        <li>+20% treatment acceptance</li>
                        <li>-30% no-shows</li>
                        <li>-25% staff time on admin</li>
                      </ul>
                    </div>
                    <div className="bg-indigo-900/30 rounded-lg p-4 border border-indigo-500/30">
                      <h4 className="text-indigo-200 font-medium mb-2">Annual Impact</h4>
                      <ul className="space-y-2 text-gray-200">
                        <li>+$180,000 in revenue</li>
                        <li>+240 new patients</li>
                        <li>-360 missed appointments</li>
                        <li>+40% patient satisfaction</li>
                      </ul>
                    </div>
                    <div className="bg-indigo-900/30 rounded-lg p-4 border border-indigo-500/30">
                      <h4 className="text-indigo-200 font-medium mb-2">Investment</h4>
                      <ul className="space-y-2 text-gray-200">
                        <li>Starting at $499/month</li>
                        <li>No long-term contracts</li>
                        <li>30-day money-back guarantee</li>
                        <li>Dedicated support team</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* AI Chat Demo */}
      <AnimatePresence>
        {showChatDemo && (
          <motion.div 
            initial={{ opacity: 0, y: 20, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 20, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="fixed bottom-4 right-4 w-80 sm:w-96 bg-gray-800 border border-gray-700 rounded-lg shadow-xl overflow-hidden z-50"
          >
            <div className="bg-gradient-to-r from-indigo-600 to-purple-600 p-4 flex items-center justify-between">
              <div className="flex items-center">
                <div className="h-8 w-8 rounded-full bg-white/20 flex items-center justify-center mr-3">
                  <FiMessageCircle className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="font-medium text-white">Smilo Assistant</h3>
                  <p className="text-xs text-indigo-200">AI Dental Support</p>
                </div>
              </div>
              <button onClick={() => setShowChatDemo(false)} className="text-white hover:bg-white/10 rounded-full p-1">
                <FiX className="h-5 w-5" />
              </button>
            </div>
            
            <div className="h-80 overflow-y-auto p-4 bg-gray-900/50" style={{ scrollBehavior: 'smooth' }}>
              {chatMessages.map((msg, idx) => (
                <div key={idx} className={`mb-4 flex ${msg.sender === 'user' ? 'justify-end' : 'justify-start'}`}>
                  <div className={`max-w-[80%] rounded-lg p-3 ${
                    msg.sender === 'user' 
                      ? 'bg-indigo-600 text-white' 
                      : 'bg-gray-700 text-gray-200'
                  }`}>
                    {msg.text}
                  </div>
                </div>
              ))}
            </div>
            
            <div className="p-3 border-t border-gray-700 bg-gray-800 flex items-end">
              <textarea 
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Ask me anything about dental care..."
                className="flex-grow bg-gray-700 border-0 rounded-lg px-3 py-2 text-gray-200 placeholder-gray-400 focus:ring-2 focus:ring-indigo-500 resize-none"
                rows={1}
              />
              <button 
                onClick={handleSendMessage}
                disabled={!newMessage.trim()}
                className={`ml-2 p-2 rounded-full ${
                  newMessage.trim() 
                    ? 'bg-indigo-600 text-white hover:bg-indigo-700 cursor-pointer' 
                    : 'bg-gray-700 text-gray-400 cursor-not-allowed'
                }`}
              >
                <svg className="h-5 w-5 transform rotate-90" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                </svg>
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Video Demo Modal */}
      <AnimatePresence>
        {showDemo && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/80 backdrop-blur-sm"
            onClick={() => setShowDemo(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-gray-900 rounded-xl overflow-hidden border border-indigo-800/30 shadow-2xl max-w-5xl w-full max-h-[90vh] relative"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="bg-gradient-to-r from-indigo-800/30 to-purple-800/30 p-4 flex justify-between items-center">
                <h3 className="text-xl font-bold text-white">
                  {demoType === 'patient' ? 'Patient Experience Demo' : 'Practice Dashboard Demo'}
                </h3>
                <button 
                  onClick={() => setShowDemo(false)}
                  className="text-gray-400 hover:text-white transition-colors"
                >
                  <FiX className="w-6 h-6" />
                </button>
              </div>
              <div className="p-0 h-[80vh] overflow-hidden">
                <iframe
                  title={`Smilo Assist ${demoType} Demo`}
                  src={demoType === 'patient' 
                    ? "https://www.youtube.com/embed/dQw4w9WgXcQ?autoplay=1" 
                    : "https://www.youtube.com/embed/dQw4w9WgXcQ?autoplay=1"}
                  className="w-full h-full"
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                  allowFullScreen
                ></iframe>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Waitlist Signup Modal */}
      <WaitlistSignup 
        selectedPlan={selectedPlan}
        isOpen={showWaitlist}
        onClose={() => setShowWaitlist(false)}
      />
    </div>
  );
}

// Feature Card Component
const FeatureCard = ({ icon, title, description, color }) => {
  return (
    <div className={`bg-gray-800/50 rounded-xl p-6 border border-gray-700 hover:border-indigo-600/50 transition group relative overflow-hidden`}>
      {/* Gradient background */}
      <div className={`absolute inset-0 bg-gradient-to-br ${color} opacity-0 group-hover:opacity-100 transition-opacity duration-300`} />
      
      <div className="relative">
        <div className="w-12 h-12 bg-indigo-900/40 rounded-full flex items-center justify-center mb-4 text-indigo-400">
          {icon}
        </div>
        <h3 className="text-lg font-semibold text-white mb-2">{title}</h3>
        <p className="text-gray-300">{description}</p>
      </div>
    </div>
  );
}; 