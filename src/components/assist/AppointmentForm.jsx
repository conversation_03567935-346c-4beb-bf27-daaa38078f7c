import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import DatePicker from './DatePicker';

const AppointmentForm = ({ onSubmit, serviceTypes }) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    serviceType: '',
    date: new Date(),
    preferredTime: '',
    notes: '',
    symptoms: '',
    urgencyLevel: 'normal',
    depositAmount: 0,
    depositPaid: false,
  });

  const [availableTimes, setAvailableTimes] = useState([
    '9:00 AM', '10:00 AM', '11:00 AM',
    '1:00 PM', '2:00 PM', '3:00 PM', '4:00 PM'
  ]);

  const [aiSuggestions, setAiSuggestions] = useState(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [showAiHelper, setShowAiHelper] = useState(false);
  const [isAnalyzingSymptoms, setIsAnalyzingSymptoms] = useState(false);
  const [aiConfidence, setAiConfidence] = useState(0);

  // Animation variants for the pulse effect
  const pulseVariants = {
    pulse: {
      scale: [1, 1.05, 1],
      opacity: [0.7, 1, 0.7],
      transition: {
        duration: 1.5,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Reset AI suggestions when service type changes
    if (name === 'serviceType') {
      setAiSuggestions(null);
    }
  };

  const handleDateChange = (date) => {
    setFormData(prev => ({
      ...prev,
      date
    }));
    
    // In a real implementation, you would fetch available times based on the selected date
    // For demo, we're simulating AI-assisted time recommendations
    simulateAiTimeRecommendations(date);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Include AI recommendations and deposit information in the submitted data
    onSubmit({
      ...formData,
      aiRecommendations: aiSuggestions,
      depositRequired: formData.depositAmount > 0,
      depositAmount: formData.depositAmount
    });
  };

  // Enhanced symptom analysis with visual indicators
  const analyzeSymptoms = () => {
    if (!formData.symptoms.trim()) return;
    
    setIsAnalyzing(true);
    setIsAnalyzingSymptoms(true);
    setAiConfidence(0);
    
    // Simulate AI confidence growing over time
    const confidenceInterval = setInterval(() => {
      setAiConfidence(prev => {
        const newValue = prev + Math.random() * 10;
        return newValue > 100 ? 100 : newValue;
      });
    }, 300);
    
    // Simulate API call to AI service
    setTimeout(() => {
      const symptomText = formData.symptoms.toLowerCase();
      
      // Simple keyword-based analysis for demo purposes
      // In a real implementation, this would be a call to an actual AI service
      let suggestedService = '';
      let urgency = 'normal';
      let additionalNotes = '';
      let confidence = Math.floor(Math.random() * 30) + 70; // Random confidence between 70-99%
      
      if (symptomText.includes('pain') || symptomText.includes('hurt') || symptomText.includes('ache')) {
        if (symptomText.includes('severe') || symptomText.includes('extreme') || symptomText.includes('unbearable')) {
          suggestedService = 'emergency';
          urgency = 'high';
          additionalNotes = 'Based on the description, this appears to be a high-priority case that may require immediate attention.';
        } else {
          suggestedService = 'root-canal';
          additionalNotes = 'The symptoms suggest potential nerve involvement. A comprehensive examination is recommended.';
        }
      } else if (symptomText.includes('broken') || symptomText.includes('chipped') || symptomText.includes('cracked')) {
        suggestedService = 'crown';
        additionalNotes = 'Dental restoration may be needed based on the reported damage.';
      } else if (symptomText.includes('yellow') || symptomText.includes('stain') || symptomText.includes('discolor')) {
        suggestedService = 'whitening';
        additionalNotes = 'Cosmetic treatment appears to be the primary concern.';
      } else if (symptomText.includes('clean') || symptomText.includes('check') || symptomText.includes('routine')) {
        suggestedService = 'cleaning';
        additionalNotes = 'A standard cleaning and checkup should address the patient\'s needs.';
      } else if (symptomText.includes('bleed') || symptomText.includes('gum') || symptomText.includes('sensitive')) {
        suggestedService = 'cleaning';
        urgency = 'medium';
        additionalNotes = 'The symptoms may indicate potential gum issues that should be examined soon.';
      }
      
      clearInterval(confidenceInterval);
      setAiConfidence(confidence);
      
      setAiSuggestions({
        suggestedService,
        urgency,
        additionalNotes,
        recommendedTimeSlots: getRecommendedTimeSlots(suggestedService, urgency),
        confidence: confidence
      });
      
      // Update form data with AI suggestions if a service was identified
      if (suggestedService) {
        setFormData(prev => ({
          ...prev,
          serviceType: suggestedService,
          urgencyLevel: urgency
        }));
      }
      
      setIsAnalyzing(false);
      
      // Keep the analysis state for a moment before hiding the indicator
      setTimeout(() => {
        setIsAnalyzingSymptoms(false);
      }, 1000);
    }, 2500); // Increased simulation time for better UX
  };

  // Get AI-recommended time slots based on service type and urgency
  const getRecommendedTimeSlots = (serviceType, urgency) => {
    // In a real implementation, this would consider current schedule,
    // provider availability, and treatment duration
    if (urgency === 'high') {
      return ['9:00 AM', '1:00 PM']; // First available slots of each session
    } else if (serviceType === 'cleaning' || serviceType === 'whitening') {
      return ['10:00 AM', '2:00 PM']; // Mid-morning/afternoon for routine procedures
    } else if (serviceType === 'root-canal' || serviceType === 'crown') {
      return ['11:00 AM', '3:00 PM']; // Longer slots for complex procedures
    }
    return ['10:00 AM', '2:00 PM', '4:00 PM']; // Default recommendations
  };

  // Simulate AI-based time recommendations
  const simulateAiTimeRecommendations = (selectedDate) => {
    if (!formData.serviceType) return;
    
    // In a real implementation, this would analyze schedule density,
    // provider availability, and patient preferences
    const day = selectedDate.getDay();
    
    // Simulate different availability for different days
    if (day === 1 || day === 3) { // Monday/Wednesday
      setAvailableTimes(['9:00 AM', '10:00 AM', '11:00 AM', '1:00 PM', '2:00 PM']);
    } else if (day === 2 || day === 4) { // Tuesday/Thursday
      setAvailableTimes(['10:00 AM', '11:00 AM', '1:00 PM', '3:00 PM', '4:00 PM']);
    } else if (day === 5) { // Friday
      setAvailableTimes(['9:00 AM', '10:00 AM', '11:00 AM']);
    } else { // Weekend
      setAvailableTimes(['10:00 AM', '11:00 AM']);
    }
  };

  return (
    <div>
      <div className="mb-6">
        <h3 className="text-xl font-semibold text-white mb-2">Request an Appointment</h3>
        <p className="text-sm text-gray-400">
          Fill out the form below to request an appointment. Our AI-assisted system will help find the best options for you.
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-5">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-1">
              Full Name
            </label>
            <input
              id="name"
              name="name"
              type="text"
              required
              value={formData.name}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-700 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-gray-800 text-white placeholder-gray-400"
              placeholder="John Doe"
            />
          </div>
          
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-1">
              Email Address
            </label>
            <input
              id="email"
              name="email"
              type="email"
              required
              value={formData.email}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-700 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-gray-800 text-white placeholder-gray-400"
              placeholder="<EMAIL>"
            />
          </div>
        </div>

        <div>
          <label htmlFor="phone" className="block text-sm font-medium text-gray-300 mb-1">
            Phone Number
          </label>
          <input
            id="phone"
            name="phone"
            type="tel"
            required
            value={formData.phone}
            onChange={handleChange}
            className="w-full px-3 py-2 border border-gray-700 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-gray-800 text-white placeholder-gray-400"
            placeholder="(*************"
          />
        </div>

        <div className="relative">
          <div className="flex justify-between items-center mb-2">
            <label htmlFor="symptoms" className="block text-sm font-medium text-gray-300">
              Describe Your Dental Concern
            </label>
            <button
              type="button"
              onClick={() => setShowAiHelper(!showAiHelper)}
              className="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <svg 
                className="w-4 h-4 mr-1" 
                xmlns="http://www.w3.org/2000/svg" 
                fill="none" 
                viewBox="0 0 24 24" 
                stroke="currentColor"
              >
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  strokeWidth={2} 
                  d="M13 10V3L4 14h7v7l9-11h-7z" 
                />
              </svg>
              AI Assist
            </button>
          </div>

          <div className="relative">
            <textarea
              id="symptoms"
              name="symptoms"
              rows="3"
              value={formData.symptoms}
              onChange={handleChange}
              className={`w-full px-3 py-2 border ${isAnalyzingSymptoms ? 'border-blue-400 ring-2 ring-blue-200' : 'border-gray-700'} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-gray-800 text-white placeholder-gray-400`}
              placeholder="Please describe any symptoms or concerns (e.g., 'I have pain in my upper right molar' or 'I need a routine cleaning')"
            />
            
            {isAnalyzingSymptoms && (
              <motion.div 
                variants={pulseVariants}
                animate="pulse"
                className="absolute inset-0 pointer-events-none"
              >
                <div className="absolute top-2 right-2 flex items-center space-x-2 bg-blue-100 rounded-full px-3 py-1">
                  <svg className="animate-spin h-4 w-4 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span className="text-xs font-medium text-blue-800">AI analyzing...</span>
                </div>
                
                {aiConfidence > 0 && (
                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-gray-200">
                    <div 
                      className={`h-full bg-blue-600`}
                      style={{ width: `${aiConfidence}%` }}
                    ></div>
                  </div>
                )}
              </motion.div>
            )}
          </div>
          
          <div className="mt-2 flex justify-end">
            <button
              type="button"
              onClick={analyzeSymptoms}
              disabled={isAnalyzing || !formData.symptoms.trim()}
              className={`text-sm px-4 py-2 rounded ${
                isAnalyzing || !formData.symptoms.trim()
                  ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                  : 'bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-500 hover:to-purple-500 text-white'
              } flex items-center shadow-sm`}
            >
              {isAnalyzing ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Analyzing...
                </>
              ) : (
                <>
                  <svg className="w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                  </svg>
                  Analyze with AI
                </>
              )}
            </button>
          </div>

          {/* AI Helper Panel */}
          {showAiHelper && (
            <div className="mt-3 p-4 bg-gray-800/60 border border-gray-700/50 rounded-lg">
              <h4 className="text-sm font-medium text-indigo-300 mb-2 flex items-center">
                <svg className="w-4 h-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                AI Appointment Assistant
              </h4>
              <p className="text-xs text-gray-300 mb-2">
                Our AI system can help recommend the appropriate service and appointment time based on your description.
              </p>
              <ul className="text-xs text-gray-400 list-disc pl-5 space-y-1">
                <li>Describe your symptoms or dental needs in detail</li>
                <li>Click "Analyze with AI" for personalized recommendations</li>
                <li>AI will suggest the appropriate service type and timing</li>
                <li>All suggestions are reviewed by our dental staff</li>
              </ul>
            </div>
          )}
        </div>

        {/* Enhanced AI Suggestions Panel */}
        {aiSuggestions && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="border rounded-lg overflow-hidden shadow-sm"
          >
            <div className={`p-3 ${
              aiSuggestions.urgency === 'high'
                ? 'bg-red-500' 
                : aiSuggestions.urgency === 'medium'
                ? 'bg-yellow-500'
                : 'bg-green-500'
            }`}>
              <div className="flex items-center justify-between">
                <h3 className="text-white font-medium flex items-center">
                  <svg className="w-5 h-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                  AI Analysis Results
                </h3>
                <span className="bg-white text-sm px-2 py-0.5 rounded-full font-medium flex items-center">
                  <span className={`${
                    aiSuggestions.urgency === 'high'
                      ? 'text-red-600'
                      : aiSuggestions.urgency === 'medium'
                      ? 'text-yellow-600'
                      : 'text-green-600'
                  }`}>
                    {aiSuggestions.urgency === 'high'
                      ? 'High Priority'
                      : aiSuggestions.urgency === 'medium'
                      ? 'Medium Priority'
                      : 'Standard Priority'
                    }
                  </span>
                </span>
              </div>
            </div>
            
            <div className="p-4 bg-gray-800">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div className="p-3 bg-gray-700 rounded-lg">
                  <h4 className="text-xs font-medium text-gray-300 uppercase tracking-wider mb-1">Recommended Service</h4>
                  <p className="font-medium text-white">
                    {serviceTypes.find(s => s.value === aiSuggestions.suggestedService)?.label || 'General Consultation'}
                  </p>
                </div>
                
                <div className="p-3 bg-gray-700 rounded-lg">
                  <h4 className="text-xs font-medium text-gray-300 uppercase tracking-wider mb-1">Urgency Level</h4>
                  <p className={`font-medium ${
                    aiSuggestions.urgency === 'high'
                      ? 'text-red-400'
                      : aiSuggestions.urgency === 'medium'
                      ? 'text-yellow-400'
                      : 'text-green-400'
                  }`}>
                    {aiSuggestions.urgency === 'high'
                      ? 'Schedule ASAP'
                      : aiSuggestions.urgency === 'medium'
                      ? 'Schedule Within Week'
                      : 'Regular Scheduling'
                    }
                  </p>
                </div>
                
                <div className="p-3 bg-gray-700 rounded-lg">
                  <h4 className="text-xs font-medium text-gray-300 uppercase tracking-wider mb-1">AI Confidence</h4>
                  <div className="flex items-center">
                    <div className="w-full bg-gray-600 rounded-full h-2.5 mr-2">
                      <div 
                        className={`h-2.5 rounded-full ${
                          aiSuggestions.confidence > 90
                            ? 'bg-green-500'
                            : aiSuggestions.confidence > 75
                            ? 'bg-blue-500'
                            : 'bg-yellow-500'
                        }`}
                        style={{ width: `${aiSuggestions.confidence}%` }}
                      ></div>
                    </div>
                    <span className="text-xs font-medium text-white">{aiSuggestions.confidence}%</span>
                  </div>
                </div>
              </div>
              
              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-300 mb-2">AI Insights:</h4>
                <p className="text-sm text-gray-300 bg-gray-700 p-3 rounded-lg border border-gray-600">
                  {aiSuggestions.additionalNotes}
                </p>
              </div>
              
              {aiSuggestions.recommendedTimeSlots.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-gray-300 mb-2">Recommended Appointment Times:</h4>
                  <div className="flex flex-wrap gap-2">
                    {aiSuggestions.recommendedTimeSlots.map((slot, index) => (
                      <button
                        key={index}
                        type="button"
                        onClick={() => setFormData(prev => ({ ...prev, preferredTime: slot }))}
                        className={`text-sm px-3 py-1.5 rounded-md transition-colors ${
                          formData.preferredTime === slot
                            ? aiSuggestions.urgency === 'high'
                              ? 'bg-red-900/60 text-red-200 border-2 border-red-700'
                              : aiSuggestions.urgency === 'medium'
                              ? 'bg-yellow-900/60 text-yellow-200 border-2 border-yellow-700'
                              : 'bg-green-900/60 text-green-200 border-2 border-green-700'
                            : 'bg-gray-700 text-gray-300 hover:bg-gray-600 border-2 border-transparent'
                        }`}
                      >
                        {slot}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        )}

        <div>
          <label htmlFor="serviceType" className="block text-sm font-medium text-gray-300 mb-1">
            Service Type
          </label>
          <select
            id="serviceType"
            name="serviceType"
            required
            value={formData.serviceType}
            onChange={handleChange}
            className="w-full px-3 py-2 border border-gray-700 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-gray-800 text-white"
          >
            <option value="">Select a service</option>
            {serviceTypes.map((service, index) => (
              <option key={index} value={service.value}>
                {service.label}
              </option>
            ))}
          </select>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Preferred Date
            </label>
            <div className="relative">
              <DatePicker 
                date={formData.date} 
                onDatesChange={handleDateChange}
                minDate={new Date()}
                className="bg-gray-800 text-white border-gray-700"
                darkMode={true}
              />
              <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <div className="w-2 h-2 rounded-full bg-indigo-400 animate-pulse"></div>
              </div>
            </div>
          </div>
          
          <div>
            <label htmlFor="preferredTime" className="block text-sm font-medium text-gray-300 mb-1">
              Preferred Time
            </label>
            <select
              id="preferredTime"
              name="preferredTime"
              required
              value={formData.preferredTime}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-700 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-gray-800 text-white"
            >
              <option value="">Select a time</option>
              {availableTimes.map((time, index) => (
                <option key={index} value={time}>
                  {time}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div>
          <label htmlFor="notes" className="block text-sm font-medium text-gray-300 mb-1">
            Additional Notes
          </label>
          <textarea
            id="notes"
            name="notes"
            rows="3"
            value={formData.notes}
            onChange={handleChange}
            className="w-full px-3 py-2 border border-gray-700 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-gray-800 text-white placeholder-gray-400"
            placeholder="Please share any additional information that might be relevant for your appointment."
          />
        </div>

        <div className="border border-indigo-500/30 rounded-lg overflow-hidden">
          <div className="bg-indigo-900/30 px-4 py-3">
            <h3 className="font-medium text-indigo-300 flex items-center">
              <svg className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Appointment Deposit
            </h3>
          </div>
          
          <div className="p-4 bg-gray-800/60">
            <p className="text-sm text-gray-300 mb-4">
              To secure your appointment and reduce no-shows, we require a small refundable deposit. 
              This amount will be applied to your treatment cost or refunded if you provide at least 24 hours notice of cancellation.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div 
                className={`border rounded-lg p-3 cursor-pointer transition-all ${
                  formData.depositAmount === 25 
                    ? 'border-indigo-500 bg-indigo-900/40' 
                    : 'border-gray-700 bg-gray-700/30 hover:bg-gray-700/50'
                }`}
                onClick={() => setFormData(prev => ({ ...prev, depositAmount: 25 }))}
              >
                <div className="flex justify-between items-center mb-1">
                  <span className="text-gray-300 font-medium">Basic</span>
                  {formData.depositAmount === 25 && (
                    <svg className="h-5 w-5 text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  )}
                </div>
                <div className="text-xl font-bold text-white mb-1">$25</div>
                <div className="text-xs text-gray-400">Standard appointments</div>
              </div>
              
              <div 
                className={`border rounded-lg p-3 cursor-pointer transition-all ${
                  formData.depositAmount === 50 
                    ? 'border-indigo-500 bg-indigo-900/40' 
                    : 'border-gray-700 bg-gray-700/30 hover:bg-gray-700/50'
                }`}
                onClick={() => setFormData(prev => ({ ...prev, depositAmount: 50 }))}
              >
                <div className="flex justify-between items-center mb-1">
                  <span className="text-gray-300 font-medium">Premium</span>
                  {formData.depositAmount === 50 && (
                    <svg className="h-5 w-5 text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  )}
                </div>
                <div className="text-xl font-bold text-white mb-1">$50</div>
                <div className="text-xs text-gray-400">Complex procedures</div>
              </div>
              
              <div 
                className={`border rounded-lg p-3 cursor-pointer transition-all ${
                  formData.depositAmount === 0 
                    ? 'border-indigo-500 bg-indigo-900/40' 
                    : 'border-gray-700 bg-gray-700/30 hover:bg-gray-700/50'
                }`}
                onClick={() => setFormData(prev => ({ ...prev, depositAmount: 0 }))}
              >
                <div className="flex justify-between items-center mb-1">
                  <span className="text-gray-300 font-medium">No Deposit</span>
                  {formData.depositAmount === 0 && (
                    <svg className="h-5 w-5 text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  )}
                </div>
                <div className="text-xl font-bold text-white mb-1">$0</div>
                <div className="text-xs text-gray-400">Pay at appointment</div>
              </div>
            </div>
            
            {formData.depositAmount > 0 && (
              <div className="mt-4 bg-indigo-900/20 border border-indigo-500/20 rounded-lg p-3">
                <h4 className="text-sm font-medium text-indigo-300 mb-2">Deposit Benefits:</h4>
                <ul className="text-sm text-gray-300 space-y-1 pl-5 list-disc">
                  <li>Priority appointment scheduling</li>
                  <li>Faster confirmation of your booking</li>
                  <li>Full amount applied to your treatment</li>
                  <li>Fully refundable with 24-hour cancellation notice</li>
                </ul>
                <div className="mt-3 text-sm text-gray-400">
                  Your deposit will be processed securely after submitting this form.
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="bg-gray-800/60 rounded-lg p-4 border border-gray-700/50">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-indigo-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-gray-300">
                Your appointment request will be reviewed by our staff with AI-assisted prioritization. 
                {formData.depositAmount > 0 
                  ? ` After paying the $${formData.depositAmount} deposit, you'll receive a confirmation email with appointment details.` 
                  : " You'll receive a confirmation email when approved."}
              </p>
            </div>
          </div>
        </div>

        <div className="pt-4">
          <button
            type="submit"
            className="w-full bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-500 hover:to-purple-500 text-white font-medium py-2.5 px-4 rounded-lg transition-colors"
          >
            {formData.depositAmount > 0 
              ? `Submit & Pay $${formData.depositAmount} Deposit` 
              : 'Submit Appointment Request'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default AppointmentForm; 