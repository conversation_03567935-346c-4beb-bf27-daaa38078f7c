import React from 'react';
import { motion } from 'framer-motion';

const AppointmentConfirmation = ({ onNewRequest, onViewRequests, darkMode = true, depositPaid = false, depositAmount = 0 }) => {
  const bgColor = darkMode ? 'bg-gray-800/60' : 'bg-white';
  const borderColor = darkMode ? 'border-gray-700/50' : 'border-gray-200';
  const textColor = darkMode ? 'text-white' : 'text-gray-900';
  const textColorSecondary = darkMode ? 'text-gray-300' : 'text-gray-600';
  
  return (
    <div className="text-center">
      <div className={`mx-auto flex items-center justify-center h-16 w-16 rounded-full ${depositPaid ? 'bg-gradient-to-r from-green-500 to-emerald-500' : 'bg-gradient-to-r from-indigo-500 to-purple-500'} mb-5`}>
        <svg 
          className="h-10 w-10 text-white" 
          xmlns="http://www.w3.org/2000/svg" 
          fill="none" 
          viewBox="0 0 24 24" 
          stroke="currentColor"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d={depositPaid ? "M5 13l4 4L19 7" : "M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"}
          />
        </svg>
      </div>
      
      <h3 className={`text-2xl font-bold ${textColor} mb-3`}>
        {depositPaid 
          ? "Appointment Confirmed" 
          : "Appointment Request Submitted"}
      </h3>
      <p className={`${textColorSecondary} mb-6 max-w-md mx-auto`}>
        {depositPaid 
          ? `Thank you for your $${depositAmount} deposit. Your appointment has been confirmed and is now secured in our system.` 
          : "Your appointment request has been successfully submitted. Our AI system will help prioritize your request and our staff will review it shortly."}
      </p>
      
      <div className={`p-5 ${darkMode ? 'bg-indigo-900/20 border-indigo-500/30' : 'bg-blue-50 border-blue-100'} border rounded-lg mb-8 inline-block text-left`}>
        <div className="flex">
          <svg 
            className={`h-6 w-6 ${darkMode ? 'text-indigo-400' : 'text-blue-500'} flex-shrink-0 mr-3 mt-0.5`}
            xmlns="http://www.w3.org/2000/svg" 
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" 
            />
          </svg>
          <div>
            <h4 className={`text-md font-semibold ${darkMode ? 'text-indigo-300' : 'text-blue-800'} mb-1`}>What happens next?</h4>
            {depositPaid ? (
              <ul className={`text-sm ${darkMode ? 'text-gray-300' : 'text-blue-700'} space-y-1 list-disc pl-5`}>
                <li>Your appointment is now confirmed and secured</li>
                <li>Your deposit will be applied to your treatment cost</li>
                <li>You'll receive a reminder 24 hours before your appointment</li>
                <li>Our AI system has prioritized your case based on your symptoms</li>
              </ul>
            ) : (
              <ul className={`text-sm ${darkMode ? 'text-gray-300' : 'text-blue-700'} space-y-1 list-disc pl-5`}>
                <li>Our AI system analyzes your request for urgency and appropriate care</li>
                <li>Staff reviews your request with AI-assisted insights within 24 hours</li>
                <li>You'll receive an email notification of approval</li>
                <li>If unavailable, we'll suggest alternative times based on AI recommendations</li>
              </ul>
            )}
          </div>
        </div>
      </div>
      
      <div className={`mb-8 p-4 ${darkMode ? 'bg-gray-800/40 border-gray-700/50' : 'bg-indigo-50 border-indigo-100'} border rounded-lg max-w-md mx-auto`}>
        <div className="flex items-start">
          <div className={`flex-shrink-0 ${darkMode ? 'bg-indigo-900/50' : 'bg-indigo-100'} rounded-full p-1`}>
            <svg
              className={`h-5 w-5 ${darkMode ? 'text-indigo-400' : 'text-indigo-600'}`}
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"
              />
            </svg>
          </div>
          <div className="ml-3">
            <h5 className={`text-sm font-medium ${darkMode ? 'text-indigo-300' : 'text-indigo-800'}`}>Smilo AI Assistant</h5>
            <p className={`mt-1 text-xs ${darkMode ? 'text-gray-400' : 'text-indigo-700'}`}>
              Our AI system helps dentists provide better care by prioritizing urgent cases and 
              optimizing appointment scheduling based on your specific needs and symptoms.
            </p>
          </div>
        </div>
      </div>
      
      {depositPaid && (
        <motion.div 
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className={`mb-8 p-4 bg-gradient-to-r from-green-500/20 to-emerald-500/20 border border-green-500/30 rounded-lg max-w-md mx-auto`}
        >
          <div className="flex items-center justify-center mb-3">
            <div className="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center mr-2">
              <svg className="h-5 w-5 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
              </svg>
            </div>
            <h5 className="text-lg font-semibold text-green-500">Payment Successful</h5>
          </div>
          <p className="text-sm text-gray-300">
            Your deposit of <span className="font-bold text-green-400">${depositAmount}.00</span> has been 
            processed successfully. This amount will be applied to your final treatment cost.
          </p>
          <div className="mt-3 text-xs text-gray-400 flex items-center justify-center">
            <svg className="h-4 w-4 mr-1 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
            </svg>
            <span>Secure payment processing via Stripe</span>
          </div>
        </motion.div>
      )}
      
      <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4 justify-center">
        <button
          onClick={onViewRequests}
          className="inline-flex items-center justify-center px-5 py-2.5 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-500 hover:to-purple-500 transition-all duration-300"
        >
          View My {depositPaid ? 'Appointments' : 'Requests'}
        </button>
        
        <button
          onClick={onNewRequest}
          className={`inline-flex items-center justify-center px-5 py-2.5 border text-sm font-medium rounded-md shadow-sm ${
            darkMode 
              ? 'border-gray-700 text-gray-300 bg-gray-800 hover:bg-gray-700'
              : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50'
          } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500`}
        >
          Submit New Request
        </button>
      </div>
    </div>
  );
};

export default AppointmentConfirmation; 