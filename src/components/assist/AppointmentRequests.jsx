import React, { useState } from 'react';
import { motion } from 'framer-motion';

// Mock data - In a real app, this would come from an API
const mockAppointments = [
  {
    id: '1',
    serviceType: 'Dental Cleaning',
    date: '2023-12-20T10:00:00',
    dentist: 'Dr. <PERSON>',
    status: 'pending',
    notes: 'Regular checkup and cleaning.',
    symptoms: 'I need my 6-month cleaning and checkup.',
    aiAnalysis: {
      urgency: 'normal',
      confidence: 0.92,
      servicePrediction: 'cleaning',
      timeEstimate: '30 minutes',
      patientHistory: 'Regular checkups, no major issues',
      suggestedAction: 'Routine cleaning appointment'
    }
  },
  {
    id: '2',
    serviceType: 'Teeth Whitening',
    date: '2023-12-25T14:30:00',
    dentist: 'Dr. <PERSON>',
    status: 'confirmed',
    notes: 'Follow-up whitening session.',
    symptoms: 'I want to whiten my teeth for my upcoming wedding.',
    aiAnalysis: {
      urgency: 'normal',
      confidence: 0.88,
      servicePrediction: 'whitening',
      timeEstimate: '45 minutes',
      patientHistory: 'Previous whitening treatment 1 year ago',
      suggestedAction: 'Whitening procedure'
    }
  },
  {
    id: '3',
    serviceType: 'Root Canal',
    date: '2023-12-18T09:15:00',
    dentist: 'Dr. <PERSON>',
    status: 'rejected',
    rejectionReason: 'Doctor unavailable on requested date. Please reschedule.',
    notes: '',
    symptoms: 'Severe pain in my lower right molar for the past 3 days. Can\'t sleep or eat properly.',
    aiAnalysis: {
      urgency: 'high',
      confidence: 0.95,
      servicePrediction: 'root-canal',
      timeEstimate: '90 minutes',
      patientHistory: 'Previous cavity in same area',
      suggestedAction: 'Urgent treatment needed, potential infection'
    }
  },
  {
    id: '4',
    serviceType: 'Emergency Dental Care',
    date: '2023-12-15T10:30:00',
    dentist: 'Dr. Sarah Johnson',
    status: 'pending',
    notes: 'Chipped front tooth while playing sports.',
    symptoms: 'I chipped my front tooth during basketball today. There\'s no pain but it looks bad.',
    aiAnalysis: {
      urgency: 'medium',
      confidence: 0.87,
      servicePrediction: 'crown',
      timeEstimate: '60 minutes',
      patientHistory: 'No previous dental trauma',
      suggestedAction: 'Cosmetic restoration, evaluate for nerve damage'
    }
  }
];

const AppointmentRequests = ({ onBack }) => {
  const [expandedId, setExpandedId] = useState(null);
  const [showAiInsights, setShowAiInsights] = useState(true);
  const [staffView, setStaffView] = useState(false); // Toggle between patient and staff view

  // Format date for display
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Format time for display
  const formatTime = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit'
    });
  };

  // Handle appointment selection
  const handleAppointmentSelect = (id) => {
    setExpandedId(id === expandedId ? null : id);
  };

  // Handle view toggle
  const toggleStaffView = () => {
    setStaffView(!staffView);
  };

  // Status badges with appropriate colors
  const StatusBadge = ({ status }) => {
    let bgColor, textColor, label;
    
    switch (status) {
      case 'pending':
        bgColor = 'bg-yellow-100';
        textColor = 'text-yellow-800';
        label = 'Pending';
        break;
      case 'confirmed':
        bgColor = 'bg-green-100';
        textColor = 'text-green-800';
        label = 'Confirmed';
        break;
      case 'rejected':
        bgColor = 'bg-red-100';
        textColor = 'text-red-800';
        label = 'Declined';
        break;
      default:
        bgColor = 'bg-gray-100';
        textColor = 'text-gray-800';
        label = status;
    }

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${bgColor} ${textColor}`}>
        {label}
      </span>
    );
  };

  // Urgency badges with appropriate colors
  const UrgencyBadge = ({ urgency }) => {
    let bgColor, textColor, label, icon;
    
    switch (urgency) {
      case 'high':
        bgColor = 'bg-red-100';
        textColor = 'text-red-800';
        label = 'High Priority';
        icon = (
          <svg className="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
        );
        break;
      case 'medium':
        bgColor = 'bg-yellow-100';
        textColor = 'text-yellow-800';
        label = 'Medium Priority';
        icon = (
          <svg className="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
        break;
      case 'normal':
        bgColor = 'bg-green-100';
        textColor = 'text-green-800';
        label = 'Normal Priority';
        icon = (
          <svg className="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
        break;
      default:
        bgColor = 'bg-gray-100';
        textColor = 'text-gray-800';
        label = urgency;
        icon = null;
    }

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${bgColor} ${textColor}`}>
        {icon}
        {label}
      </span>
    );
  };

  // Sort appointments by urgency and status
  const sortedAppointments = [...mockAppointments].sort((a, b) => {
    // First sort by status - pending first
    if (a.status === 'pending' && b.status !== 'pending') return -1;
    if (a.status !== 'pending' && b.status === 'pending') return 1;
    
    // Then sort by urgency
    const urgencyOrder = { high: 0, medium: 1, normal: 2 };
    const aUrgency = a.aiAnalysis?.urgency || 'normal';
    const bUrgency = b.aiAnalysis?.urgency || 'normal';
    return urgencyOrder[aUrgency] - urgencyOrder[bUrgency];
  });
  
  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-semibold text-gray-800">
          {staffView ? 'Staff View: Appointment Requests' : 'Your Appointment Requests'}
        </h3>
        <div className="flex items-center space-x-4">
          {/* Staff/Patient view toggle */}
          {staffView && (
            <button
              onClick={() => setShowAiInsights(!showAiInsights)}
              className={`text-xs ${showAiInsights ? 'text-indigo-600' : 'text-gray-500'} flex items-center`}
            >
              <svg className="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2z" 
                  stroke="currentColor" strokeWidth={1.5} strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              {showAiInsights ? 'Hide AI Insights' : 'Show AI Insights'}
            </button>
          )}
          <button
            onClick={toggleStaffView}
            className="text-sm bg-indigo-50 hover:bg-indigo-100 text-indigo-700 px-3 py-1 rounded-md flex items-center"
          >
            <svg className="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" 
                stroke="currentColor" strokeWidth={1.5} strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            {staffView ? 'Switch to Patient View' : 'Switch to Staff View'}
          </button>
          <button
            onClick={onBack}
            className="text-sm text-blue-600 hover:text-blue-800 flex items-center"
          >
            <svg 
              className="h-4 w-4 mr-1" 
              xmlns="http://www.w3.org/2000/svg" 
              viewBox="0 0 20 20" 
              fill="currentColor"
            >
              <path 
                fillRule="evenodd" 
                d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z" 
                clipRule="evenodd" 
              />
            </svg>
            Back to Form
          </button>
        </div>
      </div>

      {staffView && showAiInsights && (
        <div className="mb-6 p-4 bg-indigo-50 border border-indigo-100 rounded-lg">
          <div className="flex items-start">
            <div className="flex-shrink-0 bg-indigo-100 p-2 rounded-full">
              <svg className="h-5 w-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h4 className="text-sm font-medium text-indigo-800">AI-Powered Appointment Management</h4>
              <p className="mt-1 text-xs text-indigo-700">
                Appointments are automatically prioritized based on AI analysis of patient symptoms, urgency, and history.
                The system suggests optimal treatment types and duration to help with scheduling.
              </p>
            </div>
          </div>
        </div>
      )}

      {sortedAppointments.length === 0 ? (
        <div className="bg-white border border-gray-200 rounded-lg p-6 text-center">
          <svg 
            className="h-12 w-12 text-gray-400 mx-auto mb-4" 
            xmlns="http://www.w3.org/2000/svg" 
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={1.5} 
              d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" 
            />
          </svg>
          <h4 className="text-lg font-medium text-gray-800 mb-2">No appointment requests</h4>
          <p className="text-gray-600 mb-4">
            {staffView ? 'There are no appointment requests to review at this time.' : 'You haven\'t submitted any appointment requests yet.'}
          </p>
          <button
            onClick={onBack}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
          >
            {staffView ? 'Return to Dashboard' : 'Request an Appointment'}
          </button>
        </div>
      ) : (
        <div className="space-y-4">
          {sortedAppointments.map((appointment) => (
            <div key={appointment.id} className="bg-white border border-gray-200 rounded-lg overflow-hidden">
              <div 
                onClick={() => handleAppointmentSelect(appointment.id)}
                className="p-4 flex justify-between items-center cursor-pointer hover:bg-gray-50"
              >
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0">
                    <div className="h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center">
                      <svg 
                        className="h-6 w-6 text-blue-600" 
                        xmlns="http://www.w3.org/2000/svg" 
                        fill="none" 
                        viewBox="0 0 24 24" 
                        stroke="currentColor"
                      >
                        <path 
                          strokeLinecap="round" 
                          strokeLinejoin="round" 
                          strokeWidth={2} 
                          d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" 
                        />
                      </svg>
                    </div>
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">
                      {appointment.serviceType}
                    </div>
                    <div className="text-sm text-gray-500">
                      {formatDate(appointment.date)} at {formatTime(appointment.date)}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <StatusBadge status={appointment.status} />
                  {staffView && appointment.aiAnalysis && showAiInsights && (
                    <UrgencyBadge urgency={appointment.aiAnalysis.urgency} />
                  )}
                  <svg 
                    className={`h-5 w-5 text-gray-400 transition-transform duration-200 ${expandedId === appointment.id ? 'transform rotate-180' : ''}`} 
                    xmlns="http://www.w3.org/2000/svg" 
                    viewBox="0 0 20 20" 
                    fill="currentColor" 
                    aria-hidden="true"
                  >
                    <path 
                      fillRule="evenodd" 
                      d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
                      clipRule="evenodd" 
                    />
                  </svg>
                </div>
              </div>
              
              {expandedId === appointment.id && (
                <motion.div
                  initial={{ height: 0 }}
                  animate={{ height: 'auto' }}
                  exit={{ height: 0 }}
                  className="border-t border-gray-200 px-4 py-4 bg-gray-50"
                >
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <p className="text-xs text-gray-500">Service</p>
                      <p className="text-sm font-medium">{appointment.serviceType}</p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500">Dentist</p>
                      <p className="text-sm font-medium">{appointment.dentist}</p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500">Date & Time</p>
                      <p className="text-sm font-medium">
                        {formatDate(appointment.date)} at {formatTime(appointment.date)}
                      </p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500">Status</p>
                      <StatusBadge status={appointment.status} />
                    </div>
                  </div>
                  
                  {appointment.symptoms && (
                    <div className="mb-4">
                      <p className="text-xs text-gray-500">Patient Described Symptoms/Needs</p>
                      <p className="text-sm">{appointment.symptoms}</p>
                    </div>
                  )}
                  
                  {appointment.notes && (
                    <div className="mb-4">
                      <p className="text-xs text-gray-500">Notes</p>
                      <p className="text-sm">{appointment.notes}</p>
                    </div>
                  )}
                  
                  {/* AI Analysis Section - Only visible in staff view */}
                  {staffView && showAiInsights && appointment.aiAnalysis && (
                    <div className="mb-4 p-4 bg-indigo-50 border border-indigo-100 rounded-lg">
                      <div className="flex items-start">
                        <div className="flex-shrink-0">
                          <svg className="h-5 w-5 text-indigo-600" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z" />
                            <path d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z" />
                          </svg>
                        </div>
                        <div className="ml-3">
                          <h5 className="text-sm font-medium text-indigo-800">AI Analysis</h5>
                          <div className="mt-2 grid grid-cols-1 md:grid-cols-2 gap-2">
                            <div>
                              <p className="text-xs text-indigo-700">Urgency:</p>
                              <UrgencyBadge urgency={appointment.aiAnalysis.urgency} />
                            </div>
                            <div>
                              <p className="text-xs text-indigo-700">Confidence:</p>
                              <p className="text-sm text-indigo-900">{(appointment.aiAnalysis.confidence * 100).toFixed(0)}%</p>
                            </div>
                            <div>
                              <p className="text-xs text-indigo-700">Service Prediction:</p>
                              <p className="text-sm text-indigo-900">{appointment.aiAnalysis.servicePrediction}</p>
                            </div>
                            <div>
                              <p className="text-xs text-indigo-700">Estimated Duration:</p>
                              <p className="text-sm text-indigo-900">{appointment.aiAnalysis.timeEstimate}</p>
                            </div>
                            <div className="md:col-span-2">
                              <p className="text-xs text-indigo-700">Patient History:</p>
                              <p className="text-sm text-indigo-900">{appointment.aiAnalysis.patientHistory}</p>
                            </div>
                            <div className="md:col-span-2">
                              <p className="text-xs text-indigo-700">Suggested Action:</p>
                              <p className="text-sm text-indigo-900">{appointment.aiAnalysis.suggestedAction}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                  
                  {appointment.rejectionReason && (
                    <div className="mb-4 p-3 bg-red-50 border border-red-100 rounded-md">
                      <p className="text-xs text-red-800 font-medium">Reason for Declining</p>
                      <p className="text-sm text-red-700">{appointment.rejectionReason}</p>
                    </div>
                  )}
                  
                  <div className="flex justify-end space-x-2 mt-4">
                    {staffView ? (
                      // Staff action buttons
                      <>
                        {appointment.status === 'pending' && (
                          <>
                            <button className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded shadow-sm text-white bg-green-600 hover:bg-green-700">
                              Approve
                            </button>
                            <button className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded shadow-sm text-white bg-blue-600 hover:bg-blue-700">
                              Reschedule
                            </button>
                            <button className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded shadow-sm text-gray-700 bg-white hover:bg-gray-50">
                              Decline
                            </button>
                          </>
                        )}
                        {appointment.status === 'confirmed' && (
                          <button className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded shadow-sm text-gray-700 bg-white hover:bg-gray-50">
                            Send Reminder
                          </button>
                        )}
                      </>
                    ) : (
                      // Patient action buttons
                      <>
                        {appointment.status === 'confirmed' && (
                          <button className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded shadow-sm text-white bg-indigo-600 hover:bg-indigo-700">
                            Add to Calendar
                          </button>
                        )}
                        
                        {appointment.status === 'pending' && (
                          <button className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded shadow-sm text-gray-700 bg-white hover:bg-gray-50">
                            Cancel Request
                          </button>
                        )}
                        
                        {appointment.status === 'rejected' && (
                          <button className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded shadow-sm text-white bg-blue-600 hover:bg-blue-700">
                            Request New Time
                          </button>
                        )}
                      </>
                    )}
                  </div>
                </motion.div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default AppointmentRequests; 