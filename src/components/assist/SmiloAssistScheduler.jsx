import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import AppointmentForm from './AppointmentForm';
import AppointmentRequests from './AppointmentRequests';
import AppointmentConfirmation from './AppointmentConfirmation';
import { ServiceTypes } from './constants';

const SmiloAssistScheduler = ({ darkMode = false }) => {
  const [activeView, setActiveView] = useState('request'); // request, requests, confirmation
  const [selectedAppointment, setSelectedAppointment] = useState(null);
  const [aiInsightsActive, setAiInsightsActive] = useState(true);
  const [appointmentData, setAppointmentData] = useState(null);
  const [depositPaid, setDepositPaid] = useState(false);

  const handleSubmitRequest = (data) => {
    // In a real implementation, this would send the data to an API
    console.log('Appointment requested:', data);
    setAppointmentData(data);
    
    // If deposit amount is greater than 0, simulate payment success
    if (data.depositAmount > 0) {
      // In a real app, this is where you'd redirect to a payment processor
      // For demo purposes, we're simulating successful payment
      setDepositPaid(true);
    } else {
      setDepositPaid(false);
    }
    
    setActiveView('confirmation');
  };

  const handleViewRequests = () => {
    setActiveView('requests');
  };

  const handleBackToForm = () => {
    setActiveView('request');
  };

  const containerClasses = darkMode 
    ? "bg-gray-800/40 rounded-xl shadow-lg overflow-hidden border border-gray-700/50 dark-theme"
    : "bg-white rounded-xl shadow-lg overflow-hidden border border-indigo-100";

  const headerClasses = darkMode
    ? "bg-gradient-to-r from-indigo-800 to-purple-800 py-6 px-6 relative overflow-hidden"
    : "bg-gradient-to-r from-indigo-600 to-blue-500 py-6 px-6 relative overflow-hidden";

  const tabClasses = (isActive) => {
    return `pb-3 px-4 font-medium text-sm ${
      isActive
        ? (darkMode ? 'text-indigo-400 border-b-2 border-indigo-500' : 'text-indigo-600 border-b-2 border-indigo-600')
        : (darkMode ? 'text-gray-400 hover:text-gray-300' : 'text-gray-500 hover:text-gray-700')
    }`;
  };

  const borderClasses = darkMode
    ? "border-b border-gray-700/50 mb-6"
    : "border-b border-gray-200 mb-6";

  return (
    <div className={containerClasses}>
      <div className={headerClasses}>
        {/* AI pattern decoration */}
        <div className="absolute top-0 right-0 w-64 h-full opacity-10">
          <svg viewBox="0 0 100 100" className="w-full h-full">
            <pattern id="ai-grid" patternUnits="userSpaceOnUse" width="10" height="10" 
              patternTransform="rotate(45)">
              <rect width="6" height="6" fill="white" />
            </pattern>
            <rect width="100" height="100" fill="url(#ai-grid)" />
          </svg>
        </div>
        
        <div className="flex items-center">
          <div className="flex-shrink-0 mr-3">
            <div className="bg-white bg-opacity-20 p-2 rounded-full">
              <svg className="w-6 h-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            </div>
          </div>
          <div>
            <h2 className="text-white text-2xl font-bold">AI-Powered Scheduling</h2>
            <p className="text-blue-100 mt-1">Smart dental appointment system with symptom analysis</p>
          </div>
        </div>
        
        {/* AI insight toggle */}
        <div className="absolute top-3 right-3">
          <button 
            onClick={() => setAiInsightsActive(!aiInsightsActive)}
            className={`flex items-center space-x-1 text-xs px-2 py-1 rounded-full ${
              aiInsightsActive ? 'bg-white bg-opacity-20 text-white' : 'bg-white bg-opacity-10 text-blue-100'
            }`}
          >
            <svg className="w-3 h-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            <span>AI Insights {aiInsightsActive ? 'On' : 'Off'}</span>
          </button>
        </div>
      </div>

      {aiInsightsActive && (
        <div className={darkMode ? "bg-gray-800/60 px-6 py-3 border-b border-gray-700/50" : "bg-indigo-50 px-6 py-3 border-b border-indigo-100"}>
          <div className="flex items-start">
            <div className="flex-shrink-0 mt-0.5">
              <svg className={`h-5 w-5 ${darkMode ? "text-indigo-400" : "text-indigo-600"}`} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
            </div>
            <div className="ml-3">
              <h4 className={`text-sm font-medium ${darkMode ? "text-indigo-300" : "text-indigo-800"}`}>Smart Scheduling Active</h4>
              <p className={`mt-1 text-xs ${darkMode ? "text-gray-300" : "text-indigo-600"}`}>
                Our AI is analyzing patient symptoms to prioritize urgent cases and optimize appointment scheduling.
              </p>
            </div>
          </div>
        </div>
      )}

      <div className="p-6">
        <div className={borderClasses}>
          <button
            onClick={() => setActiveView('request')}
            className={tabClasses(activeView === 'request')}
          >
            Request Appointment
          </button>
          <button
            onClick={() => setActiveView('requests')}
            className={tabClasses(activeView === 'requests')}
          >
            My Requests
          </button>
        </div>

        <AnimatePresence mode="wait">
          {activeView === 'request' && (
            <motion.div
              key="request-form"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.3 }}
            >
              <AppointmentForm 
                onSubmit={handleSubmitRequest} 
                serviceTypes={ServiceTypes}
                darkMode={darkMode}
              />
            </motion.div>
          )}

          {activeView === 'requests' && (
            <motion.div
              key="requests-list"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.3 }}
            >
              <AppointmentRequests onBack={handleBackToForm} darkMode={darkMode} />
            </motion.div>
          )}

          {activeView === 'confirmation' && (
            <motion.div
              key="confirmation"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.3 }}
            >
              <AppointmentConfirmation 
                onNewRequest={handleBackToForm} 
                onViewRequests={handleViewRequests} 
                darkMode={darkMode}
                depositPaid={depositPaid}
                depositAmount={appointmentData?.depositAmount || 0}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </div>
      
      {/* AI features explanation */}
      {activeView === 'request' && (
        <div className={darkMode ? "bg-gray-800/30 px-6 py-4 border-t border-gray-700/50" : "bg-gray-50 px-6 py-4 border-t border-gray-100"}>
          <h4 className={`text-sm font-medium mb-2 flex items-center ${darkMode ? "text-gray-300" : "text-gray-800"}`}>
            <svg className={`w-4 h-4 mr-1 ${darkMode ? "text-indigo-400" : "text-indigo-600"}`} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            How Our AI Helps You
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mt-2">
            <div className={darkMode ? "bg-gray-800/60 p-3 rounded-md border border-gray-700/50" : "bg-white p-3 rounded-md border border-gray-200"}>
              <div className={darkMode ? "text-indigo-400 mb-1" : "text-indigo-600 mb-1"}>
                <svg className="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <h5 className={`text-xs font-medium ${darkMode ? "text-gray-300" : "text-gray-800"}`}>Symptom Analysis</h5>
              <p className={`text-xs ${darkMode ? "text-gray-400" : "text-gray-600"} mt-1`}>Analyzes your description to recommend appropriate treatment types</p>
            </div>
            <div className={darkMode ? "bg-gray-800/60 p-3 rounded-md border border-gray-700/50" : "bg-white p-3 rounded-md border border-gray-200"}>
              <div className={darkMode ? "text-indigo-400 mb-1" : "text-indigo-600 mb-1"}>
                <svg className="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h5 className={`text-xs font-medium ${darkMode ? "text-gray-300" : "text-gray-800"}`}>Priority Scheduling</h5>
              <p className={`text-xs ${darkMode ? "text-gray-400" : "text-gray-600"} mt-1`}>Automatically prioritizes urgent cases based on severity assessment</p>
            </div>
            <div className={darkMode ? "bg-gray-800/60 p-3 rounded-md border border-gray-700/50" : "bg-white p-3 rounded-md border border-gray-200"}>
              <div className={darkMode ? "text-indigo-400 mb-1" : "text-indigo-600 mb-1"}>
                <svg className="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                </svg>
              </div>
              <h5 className={`text-xs font-medium ${darkMode ? "text-gray-300" : "text-gray-800"}`}>Secure Scheduling</h5>
              <p className={`text-xs ${darkMode ? "text-gray-400" : "text-gray-600"} mt-1`}>Optional deposit system reduces no-shows and guarantees your appointment</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SmiloAssistScheduler; 