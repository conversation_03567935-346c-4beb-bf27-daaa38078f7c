import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Card } from '../common';
import MentorBiographyCard from './MentorBiographyCard';
import { MENTOR_BIOGRAPHIES } from '../../lib/constants/mentorBiographies';

const MentorBiographiesSection = () => {
  const [activeTab, setActiveTab] = useState('all');
  
  // Filter mentors based on active tab
  const filteredMentors = 
    activeTab === 'all' 
      ? MENTOR_BIOGRAPHIES 
      : MENTOR_BIOGRAPHIES.filter(mentor => mentor.id === activeTab);
  
  return (
    <div className="space-y-6">
      {/* Section Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-white">My Dental Mentors</h2>
          <p className="text-indigo-200 mt-1">
            The dentists who shaped my journey and inspired my passion
          </p>
        </div>
        
        {/* Tabs Navigation */}
        <div className="flex flex-wrap gap-2">
          <TabButton 
            isActive={activeTab === 'all'} 
            onClick={() => setActiveTab('all')}
          >
            All Mentors
          </TabButton>
          <TabButton 
            isActive={activeTab === 'benita-de-mirza'} 
            onClick={() => setActiveTab('benita-de-mirza')}
          >
            Dr. De Mirza
          </TabButton>
          <TabButton 
            isActive={activeTab === 'joshua-golden'} 
            onClick={() => setActiveTab('joshua-golden')}
          >
            Dr. Golden
          </TabButton>
          <TabButton 
            isActive={activeTab === 'hajar-hasan-verrett'} 
            onClick={() => setActiveTab('hajar-hasan-verrett')}
          >
            Dr. Verrett
          </TabButton>
        </div>
      </div>
      
      {/* Mentor Cards Grid */}
      <motion.div 
        className="grid gap-6 md:grid-cols-3"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.4 }}
      >
        {filteredMentors.map(mentor => (
          <motion.div
            key={mentor.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.4 }}
            layout
          >
            <MentorBiographyCard mentor={mentor} />
          </motion.div>
        ))}
      </motion.div>
      
      {/* Empty State */}
      {filteredMentors.length === 0 && (
        <Card className="p-8 text-center">
          <p className="text-white/70">No mentors found in the selected category.</p>
        </Card>
      )}
    </div>
  );
};

// Tab Button Component
const TabButton = ({ isActive, onClick, children }) => (
  <button
    className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-colors ${
      isActive 
        ? 'bg-indigo-600 text-white shadow-md shadow-indigo-600/20' 
        : 'bg-indigo-900/30 text-indigo-200 hover:bg-indigo-900/50'
    }`}
    onClick={onClick}
  >
    {children}
  </button>
);

export default MentorBiographiesSection; 