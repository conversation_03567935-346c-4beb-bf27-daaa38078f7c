import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Card } from '../common';

const MentorBiographyCard = ({ mentor }) => {
  const navigate = useNavigate();
  
  const navigateToMentorDetail = () => {
    navigate(`/mentor/${mentor.id}`);
  };

  // Helper function to get the profile image path
  const getProfileImage = (mentorId) => {
    if (mentorId === 'benita-de-mirza') {
      return '/images/dr-demirza.jpg';
    } else if (mentorId === 'hajar-hasan-verrett') {
      return '/images/dr-verrett.jpg';
    }
    // Default or placeholder image for other mentors
    return null;
  };

  // Use portrait image if available, otherwise use profile image
  const displayImage = mentor.portraitImage || mentor.profileImage;
  
  // Get object position and scale based on mentor ID to focus on faces
  const getImageStyle = (mentorId) => {
    switch (mentorId) {
      case 'benita-de-mirza':
        return 'object-contain scale-100';
      case 'joshua-golden':
        return 'object-[center_20%] scale-[0.85]';
      case 'hajar-hasan-verrett':
        return 'object-[center_25%] scale-[0.9]';
      default:
        return 'object-center';
    }
  };
  
  return (
    <div className="h-full">
      <Card 
        className="h-full transition-all duration-300 overflow-hidden relative hover:shadow-md hover:shadow-indigo-500/30 hover:border-cyan-500/40 cursor-pointer"
        onClick={navigateToMentorDetail}
      >
        {/* Profile Image (if available) */}
        {displayImage && (
          <div className="w-full h-56 overflow-hidden">
            <img 
              src={displayImage} 
              alt={`Dr. ${mentor.name}`}
              className={`w-full h-full object-cover ${getImageStyle(mentor.id)} hover:scale-100 transition-transform duration-500`}
            />
          </div>
        )}
        
        {/* Mentor Card Header */}
        <div className="p-5">
          <div className="flex justify-between items-start">
            <div>
              <h3 className="text-lg font-semibold text-white">{mentor.name}</h3>
              <p className="text-cyan-300 text-sm mt-1">{mentor.title}</p>
            </div>
            <button 
              className="w-8 h-8 flex items-center justify-center rounded-full bg-indigo-700/50 text-white transition-transform duration-300"
              aria-label="View mentor details"
              onClick={(e) => {
                e.stopPropagation();
                navigateToMentorDetail();
              }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
          
          {/* Mentor Quick Info */}
          <div className="mt-3 flex flex-wrap text-xs text-white/70 gap-x-3 gap-y-1">
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              <span>{mentor.practice}</span>
            </div>
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              <span>{mentor.location}</span>
            </div>
          </div>
          
          {/* Preview Text */}
          <p className="mt-3 text-white/80 text-sm line-clamp-2">{mentor.preview}</p>
          
          {/* Tags/Highlights */}
          <div className="mt-3 flex flex-wrap gap-2">
            {mentor.specialties.map((specialty, index) => (
              <span 
                key={index} 
                className="text-xs px-2 py-0.5 bg-indigo-900/50 text-indigo-200 rounded-full"
              >
                {specialty}
              </span>
            ))}
          </div>
          
          {/* View Details Button */}
          <div className="mt-4 pt-3 border-t border-indigo-800/30">
            <button 
              onClick={(e) => {
                e.stopPropagation();
                navigateToMentorDetail();
              }}
              className="w-full py-2 bg-indigo-700/40 hover:bg-indigo-700/60 text-indigo-100 text-sm rounded transition-colors flex items-center justify-center space-x-2"
            >
              <span>View Full Biography</span>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
              </svg>
            </button>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default MentorBiographyCard; 