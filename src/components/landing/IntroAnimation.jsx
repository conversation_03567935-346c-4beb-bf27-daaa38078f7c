import React, { useEffect, useState, useRef } from 'react';
import GeometricToothIcon from '../common/GeometricToothIcon';

// This component contains the ONLY animated logo on the site
// The logos in the navigation bars (<PERSON><PERSON> and FloatingNav) are NOT animated
export default function IntroAnimation() {
  const [isVisible, setIsVisible] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const containerRef = useRef(null);

  useEffect(() => {
    // Trigger animation after component mounts
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 100);
    
    return () => clearTimeout(timer);
  }, []);

  // 3D tilt effect based on mouse position
  useEffect(() => {
    if (!containerRef.current || !isVisible) return;
    
    const container = containerRef.current;
    
    const handleMouseMove = (e) => {
      if (!isHovered) return;
      
      const rect = container.getBoundingClientRect();
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;
      
      // Calculate distance from center (normalized)
      const distX = (e.clientX - centerX) / (rect.width / 2);
      const distY = (e.clientY - centerY) / (rect.height / 2);
      
      // Apply transform with constraints
      container.style.transform = `
        perspective(800px) 
        rotateY(${distX * 5}deg) 
        rotateX(${-distY * 5}deg)
        translateY(${isVisible ? '0' : '10px'})
        scale(${isHovered ? 1.05 : 1})
      `;
    };
    
    document.addEventListener('mousemove', handleMouseMove);
    return () => document.removeEventListener('mousemove', handleMouseMove);
  }, [isVisible, isHovered]);

  return (
    <div 
      ref={containerRef}
      className={`
        transition-all duration-1000
        ${isVisible ? 'opacity-100' : 'opacity-0 translate-y-10'}
      `}
      style={{
        transformStyle: 'preserve-3d',
        transform: `
          perspective(800px)
          translateY(${isVisible ? '0' : '10px'})
        `
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => {
        setIsHovered(false);
        // Reset transform when not hovered
        if (containerRef.current) {
          containerRef.current.style.transform = `
            perspective(800px)
            rotateY(0deg)
            rotateX(0deg)
            translateY(0)
            scale(1)
          `;
        }
      }}
    >
      <div className="relative w-36 h-36 mx-auto mb-8">
        {/* Outer pulsing rings */}
        <div className="absolute inset-0 -m-8 bg-blue-400/5 rounded-full animate-ping-slow opacity-0 group-hover:opacity-100" />
        <div className="absolute inset-0 -m-4 bg-blue-400/10 rounded-full animate-ping-slow [animation-delay:300ms] opacity-0 group-hover:opacity-100" />
        
        {/* Enhanced background glow effect */}
        <div className="absolute inset-0 -m-2 bg-gradient-to-r from-blue-500/20 via-indigo-500/20 to-blue-400/20 rounded-full blur-3xl animate-pulse-slow" />
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/30 to-cyan-400/30 rounded-full blur-2xl animate-pulse-slow [animation-delay:250ms]" />
        
        {/* Main tooth icon */}
        <GeometricToothIcon className="z-10" />
      </div>
      
      {/* Add CSS for enhanced animations */}
      <style jsx>{`
        @keyframes ping-slow {
          0% {
            transform: scale(0.95);
            opacity: 0;
          }
          50% {
            opacity: 0.5;
          }
          100% {
            transform: scale(1.3);
            opacity: 0;
          }
        }
        
        @keyframes pulse-slow {
          0% {
            opacity: 0.2;
          }
          50% {
            opacity: 0.5;
          }
          100% {
            opacity: 0.2;
          }
        }
      `}</style>
    </div>
  );
}