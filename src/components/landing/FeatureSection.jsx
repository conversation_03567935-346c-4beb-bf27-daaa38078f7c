import React, { useEffect, useRef, useState, memo } from 'react';
import Card from '../common/Card';
import CardGrid from '../common/CardGrid';
import ContentSection from '../sections/ContentSection';
import { useNavigate } from 'react-router-dom';
import { motion, useReducedMotion, AnimatePresence } from 'framer-motion';
import { useAnimations } from '../../contexts/AnimationContext';

// Import Lucide icons for a more modern and consistent look
import { 
  Brain, 
  Search, 
  MessageSquare, 
  Map, 
  BookOpen, 
  Stethoscope,
  GraduationCap, 
  Smartphone,
  ChevronRight
} from 'lucide-react';

// Modern feature data with Lucide icons instead of emojis
const features = [
  {
    title: 'AI-Powered Insights',
    description: 'Get instant, intelligent responses to your dental health questions',
    icon: Brain,
    href: '/chat',
    color: 'from-blue-500/20 via-indigo-500/20 to-blue-600/20',
    hoverGlow: 'shadow-blue-500/30'
  },
  {
    title: 'Image Analysis',
    description: 'Upload dental images for AI-assisted visual analysis',
    icon: Search,
    href: '/chat',
    color: 'from-purple-500/20 via-fuchsia-500/20 to-pink-600/20',
    hoverGlow: 'shadow-purple-500/30'
  },
  {
    title: 'Smilo Assist',
    description: 'Virtual assistant for enhanced patient-practice communication',
    icon: MessageSquare,
    href: '/assist',
    color: 'from-cyan-500/20 via-teal-500/20 to-emerald-600/20',
    hoverGlow: 'shadow-teal-500/30',
    badge: 'New'
  },
  {
    title: 'Affordable Care Locator',
    description: 'Find nearby dental schools and affordable care programs with reviews and ratings',
    icon: Map,
    href: '/affordable-care',
    color: 'from-sky-500/20 via-blue-500/20 to-indigo-600/20',
    hoverGlow: 'shadow-blue-500/30',
    badge: 'New'
  },
  {
    title: 'Expert Resources',
    description: 'Access curated dental health information and guidelines',
    icon: BookOpen,
    href: '/expert-resources',
    color: 'from-blue-500/20 via-indigo-500/20 to-violet-600/20',
    hoverGlow: 'shadow-indigo-500/30'
  },
  /* Temporarily hidden until ready
  {
    title: 'Pre-Dental Journey',
    description: 'Track your pre-dental progress, calculate GPA, and access success stories',
    icon: GraduationCap,
    href: '/predental',
    color: 'from-cyan-500/20 via-sky-500/20 to-blue-600/20'
    hoverGlow: 'shadow-sky-500/30'
  },
  */
  /* Temporarily hidden until ready
  {
    title: 'Sensors & DenTech',
    description: 'Explore innovative dental technology and sensor solutions for oral health monitoring',
    icon: Smartphone,
    href: '/dentech',
    color: 'from-indigo-500/20 via-blue-500/20 to-sky-600/20',
    hoverGlow: 'shadow-indigo-500/30'
  },
  */
  {
    title: 'For Dentists',
    description: 'Register your practice or join our network of professionals supporting accessible dental care',
    icon: Stethoscope,
    href: '/dentists',
    color: 'from-indigo-500/20 via-blue-500/20 to-purple-600/20',
    hoverGlow: 'shadow-indigo-500/30',
    badge: 'Join Us'
  }
];

// Memoize individual feature cards for performance
const FeatureCard = memo(({ feature, onClick, shouldReduceAnimations }) => {
  const Icon = feature.icon;
  
  // Simplified hover variants for icons
  const iconVariants = {
    initial: { scale: 1 },
    hover: { 
      scale: 1.05,
      transition: {
        duration: 0.2
      }
    }
  };
  
  return (
    <motion.div
      whileHover={{ 
        y: shouldReduceAnimations ? 0 : -3,
        transition: { duration: 0.2 }
      }}
      className="flex"
    >
      {/* Simplified Glassmorphism card design */}
      <div 
        className={`
          w-full group cursor-pointer rounded-xl overflow-hidden relative 
          h-[220px] flex flex-col p-6
          bg-white/10 border border-white/15
          hover:border-white/30 hover:${feature.hoverGlow} hover:shadow-md
        `}
        onClick={() => onClick(feature.href)}
        style={{ transform: 'translateZ(0)' }} // Force GPU acceleration
      >
        {/* Simple gradient background on hover - with improved performance */}
        <div 
          className={`absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-0`}
          style={{
            background: `linear-gradient(to bottom right, var(--${feature.color.split('-')[1]}), var(--${feature.color.split('-')[3]}))`,
            opacity: 0,
            transform: 'translateZ(0)' // Force GPU acceleration
          }}
        />
        
        <div className="relative flex flex-col h-full z-10">
          {/* Simplified icon container */}
          <motion.div 
            className="mb-4 relative"
            variants={iconVariants}
            initial="initial"
            whileHover={shouldReduceAnimations ? {} : "hover"}
          >
            <div className="w-12 h-12 flex items-center justify-center rounded-lg bg-gradient-to-br from-white/15 to-white/5 border border-white/15">
              <Icon size={20} className="text-white" strokeWidth={1.5} />
            </div>
          </motion.div>
          
          {/* Title and badge */}
          <div className="flex items-center mb-2">
            <h3 className="text-xl font-medium text-white">
              {feature.title}
            </h3>
            
            {feature.badge && (
              <span className="ml-3 px-2 py-0.5 text-xs font-medium bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-full">
                {feature.badge}
              </span>
            )}
          </div>
          
          {/* Description */}
          <p className="text-blue-100/80 text-sm leading-relaxed font-light">{feature.description}</p>
          
          {/* Static arrow instead of animated */}
          <div className="mt-auto pt-3 flex justify-end">
            <div className="w-6 h-6 rounded-full flex items-center justify-center bg-white/15 opacity-50 group-hover:opacity-100 transition-opacity duration-200 group-hover:bg-gradient-to-r from-blue-500 to-indigo-600">
              <ChevronRight size={14} className="text-white" />
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
});

// Prevent unnecessary re-renders
FeatureCard.displayName = 'FeatureCard';

export default function FeatureSection() {
  const navigate = useNavigate();
  const containerRef = useRef(null);
  // Check if user prefers reduced motion (accessibility)
  const prefersReducedMotion = useReducedMotion();
  
  // Use the animation context
  const { animationsEnabled, isHighPerformanceDevice } = useAnimations();
  const [renderKey, setRenderKey] = useState(0);

  // Combined animation state (either user preference or reduced)
  const shouldReduceAnimations = !animationsEnabled || prefersReducedMotion || !isHighPerformanceDevice;

  // Cross-browser rehydration fix - ensure component stays mounted
  useEffect(() => {
    // Create a stable unique key for each render
    setRenderKey(Date.now());
    
    // Fix for Safari animation issues - force rerender after 100ms
    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
    if (isSafari) {
      const timer = setTimeout(() => {
        setRenderKey(prev => prev + 1);
      }, 100);
      return () => clearTimeout(timer);
    }
  }, []);

  const handleCardClick = (href) => {
    navigate(href);
  };

  // Animation variants for container - simplified for better performance
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: shouldReduceAnimations ? 0 : 0.03, // Even faster staggering
        delayChildren: 0.1,
      }
    }
  };

  // Animation variants for cards - simplified and optimized
  const cardVariants = {
    hidden: { 
      y: shouldReduceAnimations ? 0 : 10, 
      opacity: 0
    },
    visible: { 
      y: 0, 
      opacity: 1,
      transition: {
        type: "tween", // Always use tween for better cross-browser compatibility
        duration: 0.3,
        ease: "easeOut"
      }
    }
  };

  return (
    <div className="relative">
      {/* Top blending gradient - fades from the previous section */}
      <div 
        className="absolute top-0 left-0 right-0 h-24 z-10 pointer-events-none"
        style={{
          background: 'linear-gradient(to bottom, #1a1a37, rgba(26, 26, 55, 0))',
          transform: 'translateZ(0)' // Force GPU rendering
        }}
      ></div>

      {/* Main feature section with expanded background */}
      <div className="relative py-24">
        {/* Full width background container with gradients - brighter and more vibrant */}
        <div 
          className="absolute inset-0 -z-10"
          style={{
            background: 'linear-gradient(to bottom, rgba(26, 26, 55, 0.97), rgba(34, 40, 87, 0.95) 50%, rgba(42, 47, 100, 0.93))',
            boxShadow: 'inset 0px 0px 100px rgba(120, 150, 255, 0.1)',
            transform: 'translateZ(0)' // Force GPU rendering
          }}
        ></div>
        
        {/* Brand color radial gradients - brighter colors matching the Smilo logo */}
        <div 
          className="absolute inset-0 -z-10 opacity-60 overflow-hidden"
          style={{ transform: 'translateZ(0)' }} // Force GPU rendering
        >
          {/* Bright blue gradient from Smilo logo */}
          <div 
            className="absolute top-0 right-0 w-full md:w-2/3 h-full md:h-2/3 pointer-events-none"
            style={{
              background: 'radial-gradient(circle at top right, rgba(120, 170, 255, 0.25), rgba(100, 150, 255, 0.15), transparent 60%)',
              transform: 'translateZ(0)' // Force GPU rendering
            }}
          ></div>
          
          {/* Purple gradient matching Smilo logo */}
          <div 
            className="absolute bottom-0 left-0 w-full md:w-2/3 h-full md:h-2/3 pointer-events-none"
            style={{
              background: 'radial-gradient(circle at bottom left, rgba(140, 120, 255, 0.25), rgba(120, 100, 255, 0.15), transparent 60%)',
              transform: 'translateZ(0)' // Force GPU rendering
            }}
          ></div>
          
          {/* Center highlight matching brighter tones */}
          <div 
            className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full h-full pointer-events-none"
            style={{
              background: 'radial-gradient(circle at center, rgba(160, 180, 255, 0.1), transparent 50%)',
              transform: 'translateZ(0)' // Force GPU rendering
            }}
          ></div>
        </div>
        
        {/* Subtle digital pattern overlay to match the tooth logo's circuit board aesthetic */}
        <div 
          className="absolute inset-0 -z-10 opacity-5" 
          style={{
            backgroundImage: 'url("data:image/svg+xml,%3Csvg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="%23FFFFFF" fill-opacity="0.3"%3E%3Cpath d="M0 20 h40 M20 0 v40 M10 0 v40 M30 0 v40 M0 10 h40 M0 30 h40"%3E%3C/path%3E%3C/g%3E%3C/svg%3E")',
            backgroundSize: '40px 40px',
            transform: 'translateZ(0)' // Force GPU rendering
          }}
        ></div>
        
        {/* Glowing orbs that match the vibrant blues and purples from the Smilo logo */}
        {!shouldReduceAnimations && (
          <>
            <div 
              className="absolute top-1/4 right-10 w-72 h-72 rounded-full opacity-30 pointer-events-none"
              style={{
                background: 'radial-gradient(circle, rgba(120, 170, 255, 0.5), rgba(100, 150, 255, 0.2) 50%, transparent 70%)',
                filter: 'blur(50px)',
                transform: 'translateZ(0)' // Force GPU rendering
              }}
            ></div>
            
            <div 
              className="absolute bottom-1/4 left-10 w-72 h-72 rounded-full opacity-30 pointer-events-none"
              style={{
                background: 'radial-gradient(circle, rgba(140, 120, 255, 0.5), rgba(120, 100, 255, 0.2) 50%, transparent 70%)',
                filter: 'blur(50px)',
                transform: 'translateZ(0)' // Force GPU rendering
              }}
            ></div>
            
            {/* Additional smaller glowing elements to create more excitement */}
            <div 
              className="absolute top-1/3 left-1/4 w-32 h-32 rounded-full opacity-20 pointer-events-none"
              style={{
                background: 'radial-gradient(circle, rgba(160, 180, 255, 0.5), transparent 70%)',
                filter: 'blur(30px)',
                transform: 'translateZ(0)' // Force GPU rendering
              }}
            ></div>
            
            <div 
              className="absolute bottom-1/3 right-1/4 w-24 h-24 rounded-full opacity-20 pointer-events-none"
              style={{
                background: 'radial-gradient(circle, rgba(130, 140, 255, 0.5), transparent 70%)',
                filter: 'blur(25px)',
                transform: 'translateZ(0)' // Force GPU rendering
              }}
            ></div>
          </>
        )}
        
        {/* Main content with ContentSection */}
        <ContentSection
          id="features"
          title={
            <div className="relative mb-8">
              {/* Enhanced background for title with brighter glow */}
              <div 
                className="absolute -inset-8 rounded-xl opacity-80"
                style={{
                  background: 'radial-gradient(ellipse, rgba(120, 170, 255, 0.15), rgba(140, 120, 255, 0.15), rgba(160, 140, 255, 0.1))',
                  filter: 'blur(20px)',
                  transform: 'translateZ(0)' // Force GPU rendering
                }}
              ></div>
              
              {/* Subtitle with improved typography and brighter color */}
              <p className="text-lg md:text-xl font-light text-blue-200 mb-3 tracking-wide relative z-10">
                Experience the future of dental care
              </p>
              
              {/* Main title - brighter gradient matching Smilo logo */}
              <h2 className="font-heading text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold bg-gradient-to-r from-blue-300 via-indigo-200 to-purple-300 text-transparent bg-clip-text relative z-10 tracking-tight leading-tight">
                Intelligent Dental Care Assistant
              </h2>
      </div>
          }
          className="relative py-8 overflow-hidden"
        >
      <motion.div
            key={renderKey} // Force clean re-render on browser issues
        ref={containerRef}
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.1 }}
            className="relative z-10"
      >
            <CardGrid className="gap-6 lg:gap-8">
              <AnimatePresence mode="wait">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              variants={cardVariants}
                    custom={index}
                    layout // Add layout animation for smoother transitions
                  >
                    <FeatureCard 
                      feature={feature} 
                      onClick={handleCardClick} 
                      shouldReduceAnimations={shouldReduceAnimations}
                    />
            </motion.div>
          ))}
              </AnimatePresence>
        </CardGrid>
      </motion.div>

          {/* Enhanced separator with brighter colors and glow */}
          <div className="w-full flex justify-center mt-16">
            <div 
              className="h-0.5 w-40 rounded-full"
              style={{
                background: 'linear-gradient(to right, transparent, rgba(120, 170, 255, 0.5), rgba(140, 120, 255, 0.5), transparent)',
                boxShadow: '0 0 12px rgba(120, 170, 255, 0.4), 0 0 20px rgba(140, 120, 255, 0.3)',
                transform: 'translateZ(0)' // Force GPU rendering
              }}
            ></div>
          </div>
        </ContentSection>
      </div>
      
      {/* Bottom blending gradient - fades into the next section */}
      <div 
        className="absolute bottom-0 left-0 right-0 h-24 z-10 pointer-events-none"
        style={{
          background: 'linear-gradient(to top, #1a1a37, rgba(26, 26, 55, 0))',
          transform: 'translateZ(0)' // Force GPU rendering
        }}
      ></div>
    </div>
  );
}