import React, { useState, useEffect, useRef, useMemo } from 'react';
import { BRANDING } from '../../lib/constants/branding';
import Logo from '../common/Logo';
import Button from '../common/Button';
import ScrollPrompt from '../common/ScrollPrompt';
import EnhancedAuthModal from '../auth/EnhancedAuthModal';
import { useParallax } from '../../lib/hooks/useParallax';
import { motion, AnimatePresence, useMotionValue } from 'framer-motion';
import { useAnimations } from '../../contexts/AnimationContext';

export default function HeroSection() {
  const [showAuth, setShowAuth] = useState(false);
  const [authMode, setAuthMode] = useState('register');
  const titleRef = useRef(null);
  const mouseEnterTimeoutRef = useRef(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isMouseOverTitle, setIsMouseOverTitle] = useState(false);
  const timeoutRef = useRef(null);

  // Use the animation context with device performance detection
  const { animationsEnabled, isHighPerformanceDevice } = useAnimations();
  const disableHeavyAnimations = !animationsEnabled;

  // Only use parallax if heavy animations are enabled AND on high-performance devices
  const shouldUseParallax = animationsEnabled && isHighPerformanceDevice;
  
  // Detect Safari - use memo to avoid recalculating
  const isSafari = useMemo(() => /^((?!chrome|android).)*safari/i.test(navigator.userAgent), []);
  
  // Always call the hook, but we'll add a condition inside the hook's effect
  // to only activate the parallax when needed
  useParallax(shouldUseParallax && !isSafari);

  // Create cached motion values for performance
  const titleRotateX = useMotionValue(0);
  const titleRotateY = useMotionValue(0);

  // Modified effect to only handle DOM operations, not hook calls
  useEffect(() => {
    if (shouldUseParallax) {
      // Any additional DOM setup for parallax can go here
      // But do NOT call React hooks here
    }
  }, [shouldUseParallax]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      if (mouseEnterTimeoutRef.current) {
        clearTimeout(mouseEnterTimeoutRef.current);
      }
    };
  }, []);

  // More efficient mouse move handler with debouncing and RAF
  useEffect(() => {
    let rafId = null;
    let lastUpdateTime = 0;
    const THROTTLE_DELAY = 16; // Only update at ~60fps

    const handleMouseMove = (e) => {
      // Skip if animations are disabled or mouse isn't over title
      if (disableHeavyAnimations || !isMouseOverTitle || !titleRef.current) return;

      const now = performance.now();
      if (now - lastUpdateTime < THROTTLE_DELAY) return;

      // Cancel any pending animation frame
      if (rafId) {
        cancelAnimationFrame(rafId);
      }

      // Use RAF for smoother animation
      rafId = requestAnimationFrame(() => {
        if (!titleRef.current) return;

        const { left, top, width, height } = titleRef.current.getBoundingClientRect();
        const x = (e.clientX - left) / width - 0.5;
        const y = (e.clientY - top) / height - 0.5;

        // Limit the rotation angle to avoid excessive movement
        const maxAngle = 5;
        const rotateY = x * maxAngle;
        const rotateX = -y * maxAngle;

        // Update motion values for smoother animation
        titleRotateX.set(rotateX);
        titleRotateY.set(rotateY);

        titleRef.current.style.transform = `
          perspective(1200px)
          rotateY(${rotateY}deg)
          rotateX(${rotateX}deg)
          translateZ(20px)
        `;

        lastUpdateTime = now;
        rafId = null;
      });
    };

    // Event listeners with passive option for better performance
    if (!disableHeavyAnimations) {
      window.addEventListener('mousemove', handleMouseMove, { passive: true });
    }

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      if (rafId) {
        cancelAnimationFrame(rafId);
      }
    };
  }, [disableHeavyAnimations, isMouseOverTitle, titleRotateX, titleRotateY]);

  const handleTitleMouseEnter = () => {
    if (disableHeavyAnimations) return;

    // Add small delay to prevent jitter on quick mouse movements
    mouseEnterTimeoutRef.current = setTimeout(() => {
      setIsMouseOverTitle(true);
    }, 50);
  };

  const handleTitleMouseLeave = () => {
    if (mouseEnterTimeoutRef.current) {
      clearTimeout(mouseEnterTimeoutRef.current);
    }

    setIsMouseOverTitle(false);

    // Reset title rotation smoothly
    if (titleRef.current) {
      titleRef.current.style.transition = 'transform 0.5s ease-out';
      titleRef.current.style.transform = 'perspective(1200px) rotateY(0deg) rotateX(0deg) translateZ(0px)';

      // Clear the transition after it's complete
      setTimeout(() => {
        if (titleRef.current) {
          titleRef.current.style.transition = '';
        }
      }, 500);
    }
  };

  const handleAuthClick = (mode) => {
    setAuthMode(mode);
    setShowAuth(true);
    // Prevent body scroll when modal is open
    document.body.style.overflow = 'hidden';
  };

  const handleModalClose = () => {
    setShowAuth(false);
    // Restore body scroll when modal is closed
    document.body.style.overflow = 'unset';
  };

  // Highly optimized variants for staggered animations
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: disableHeavyAnimations ? 0 : 0.05, // Further reduced stagger
        delayChildren: disableHeavyAnimations ? 0 : 0.05,   // Further reduced delay
      }
    }
  };

  const itemVariants = {
    hidden: { y: disableHeavyAnimations ? 0 : 10, opacity: 0 }, // Reduced movement distance
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "tween", // Always use tween for better performance
        duration: 0.3,  // Fixed duration
        ease: "easeOut" // Simple easing
      }
    }
  };

  // Letters animation for DentAl Assistant text - simplified with fewer animations
  const subtitleLetters = "DentAl Assistant".split("");

  const handleScrollToFeatures = () => {
    // Try to find the FeatureSection by ID immediately without delay
    const featuresSection = document.getElementById('features');
    if (featuresSection) {
      featuresSection.scrollIntoView({ behavior: 'smooth' });
    } else {
      // Fallback: scroll down by viewport height
      window.scrollBy({
        top: window.innerHeight,
        behavior: 'smooth'
      });
    }
  };

  // Generate fewer orbs, especially on Safari
  const orbCount = disableHeavyAnimations ? 0 : (isSafari ? 2 : 4);

  // Create orbs only once with useMemo to avoid recreating animations
  const orbParams = useMemo(() => (
    [...Array(orbCount)].map(() => ({
      top: Math.random() * 100,
      left: Math.random() * 100,
      y: Math.random() * -30 - 10, // Reduced movement range
      x: (Math.random() - 0.5) * 20, // Reduced movement range
      opacity: [0, 0.3, 0], // Reduced opacity
      scale: [0, 1, 0],
      duration: 8 + Math.random() * 4, // Shorter duration
      delay: Math.random() * 2,
    }))
  ), [disableHeavyAnimations, orbCount]);

  return (
    <div className="relative min-h-[92vh] flex items-center overflow-hidden isolate">
      {/* Enhanced circular logo placement - always visible with improved styling */}
      <div className="absolute top-8 left-1/2 transform -translate-x-1/2 z-10">
        <div className="relative">
          {/* Enhanced background glow with compatibility fix for Safari - reduced brightness */}
          <div
            className={`absolute -inset-10 rounded-full transition-opacity duration-300 ${!disableHeavyAnimations ? 'opacity-50' : 'opacity-30'}`}
            style={{
              background: 'radial-gradient(circle, rgba(59,130,246,0.15) 0%, rgba(99,102,241,0.1) 50%, rgba(147,51,234,0.15) 100%)',
              filter: 'blur(10px)',
              transform: 'translateZ(0)' // Force GPU rendering to avoid Safari blur issues
            }}
          ></div>

          {/* Logo with animation based on global preference */}
          <Logo
            size="large"
            animated={!disableHeavyAnimations}
            showText={false}
          />
        </div>
      </div>

      {/* Simplified background for better mobile performance */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Base gradient layer - static on mobile */}
        <div
          className="absolute inset-0 bg-gradient-to-br from-blue-900/20 via-gray-900 to-purple-900/20"
        />

        {/* Primary radial glow - static on mobile, only animated on desktop */}
        <div
          className="absolute inset-0"
          style={{
            background: 'radial-gradient(circle at center, rgba(99,102,241,0.1) 0%, rgba(79,70,229,0.05) 40%, transparent 70%)',
            transform: 'translateZ(0)' // Force GPU rendering
          }}
        />

        {/* Additional directional glows for depth - reduced opacity */}
        <div
          className="absolute inset-0"
          style={{
            background: 'radial-gradient(circle at top right, rgba(59,130,246,0.15), transparent 70%)'
          }}
          data-parallax={shouldUseParallax ? "medium" : "none"}
        />

        <div
          className="absolute inset-0"
          style={{
            background: 'radial-gradient(circle at bottom left, rgba(147,51,234,0.15), transparent 70%)'
          }}
          data-parallax={shouldUseParallax ? "fast" : "none"}
        />

        {/* Edge-to-edge horizontal gradient overlay - more subtle */}
        <div
          className="absolute inset-0"
          style={{
            background: 'linear-gradient(90deg, rgba(59,130,246,0.08) 0%, rgba(99,102,241,0.05) 50%, rgba(147,51,234,0.08) 100%)',
            transform: 'translateZ(0)' // Force GPU rendering
          }}
        />

        {/* Animated elements only when enabled and not on mobile */}
        {!disableHeavyAnimations && typeof window !== 'undefined' && window.innerWidth > 768 && (
          <>
            {/* Apply GPU acceleration for Safari compatibility */}
            {/* Pulsing glow overlay - slower animation for better performance */}
            <motion.div
              className="absolute inset-0"
              style={{ transform: 'translateZ(0)' }}
              animate={{
                opacity: [0.05, 0.1, 0.05]
              }}
              transition={{
                duration: 8, // Slower animation
                repeat: Infinity,
                repeatType: "mirror",
                ease: "easeInOut"
              }}
            />

            {/* Additional subtle animated gradient overlay - even more subtle */}
            <motion.div
              className="absolute inset-0"
              style={{
                background: 'linear-gradient(135deg, rgba(59,130,246,0.05), rgba(99,102,241,0.03), rgba(147,51,234,0.05))',
                backgroundSize: '400% 400%',
                transform: 'translateZ(0)'
              }}
              animate={{
                backgroundPosition: ['0% 0%', '100% 100%', '0% 0%']
              }}
              transition={{
                duration: 15,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />

            {/* Reduced number of floating orbs with simpler animations */}
            {orbParams.map((params, i) => (
              <motion.div
                key={i}
                className="absolute w-3 h-3 rounded-full"
                style={{
                  top: `${params.top}%`,
                  left: `${params.left}%`,
                  backgroundColor: 'rgba(255, 255, 255, 0.6)',
                  filter: 'blur(1px)',
                  transform: 'translateZ(0)' // Force GPU rendering
                }}
                animate={{
                  y: [0, params.y],
                  x: [0, params.x],
                  opacity: params.opacity,
                  scale: params.scale
                }}
                transition={{
                  duration: params.duration,
                  repeat: 1,
                  delay: params.delay,
                  ease: "easeInOut"
                }}
              />
            ))}
          </>
        )}
      </div>

      {/* Main content with animations */}
      {/* Main content container with its own subtle glow effect */}
      <div className="relative w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 pb-32">
        {/* Additional content glow that follows the content area - more subtle */}
        <div
          className="absolute inset-0 rounded-[50%/20%] opacity-40"
          style={{
            background: 'radial-gradient(ellipse at center, rgba(99,102,241,0.15) 0%, rgba(79,70,229,0.08) 50%, transparent 80%)',
            filter: 'blur(30px)',
            transform: 'translateZ(0)' // Force GPU rendering
          }}
        ></div>
        <motion.div
          className="text-center space-y-10"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {/* Simplified title effect for mobile */}
          <motion.div
            ref={titleRef}
            onMouseEnter={handleTitleMouseEnter}
            onMouseLeave={handleTitleMouseLeave}
            className="transition-transform duration-200 ease-out relative"
            style={{
              willChange: isMouseOverTitle && !disableHeavyAnimations && typeof window !== 'undefined' && window.innerWidth > 768 ? 'transform' : 'auto',
              transform: 'translateZ(0)' // Force GPU acceleration for Safari
            }}
            variants={itemVariants}
          >
            {/* Enhanced glow effect behind text - more subtle with compatibility fix */}
            <div
              className="absolute -inset-16 rounded-full opacity-50"
              style={{
                background: 'radial-gradient(circle, rgba(59,130,246,0.2) 0%, rgba(99,102,241,0.15) 50%, rgba(147,51,234,0.2) 100%)',
                filter: 'blur(30px)',
                transform: 'translateZ(0)' // Force GPU rendering
              }}
            ></div>

            <h1 className="font-heading text-[8rem] md:text-[10rem] lg:text-[12rem] font-bold tracking-tight mb-2 bg-gradient-to-r from-blue-400 via-indigo-300 to-purple-400 text-transparent bg-clip-text">
              SMILO
            </h1>

            <div className="relative mt-2">
              {/* Dental Assistant text - static or animated */}
              <div className="flex justify-center">
                {subtitleLetters.map((letter, index) => (
                  <motion.span
                    key={index}
                    className="text-3xl md:text-4xl lg:text-5xl text-blue-200/90 font-light tracking-wide inline-block"
                    initial={{ opacity: 0, y: disableHeavyAnimations ? 0 : 10 }}
                    animate={{
                      opacity: 1,
                      y: 0
                    }}
                    transition={{
                      delay: disableHeavyAnimations ? 0.1 : 0.2 + index * 0.02, // Faster animation for better performance
                      duration: 0.3
                    }}
                  >
                    {letter === ' ' ? '\u00A0' : letter}
                  </motion.span>
                ))}
              </div>
            </div>
          </motion.div>

          {/* Tagline */}
          <motion.h2
            className="text-2xl md:text-3xl lg:text-4xl font-light text-blue-100/90 mx-auto max-w-3xl"
            variants={itemVariants}
          >
            Your Personalized Guide to Oral Health
          </motion.h2>

          {/* Description */}
          <motion.div
            className="relative mx-auto max-w-2xl"
            variants={itemVariants}
          >
            <motion.p className="text-lg text-white/70 leading-relaxed">
              Empowering healthier smiles through AI-driven insights and resources.
            </motion.p>
          </motion.div>

          {/* CTA Buttons */}
          <motion.div
            className="flex flex-wrap justify-center gap-4 mt-8 mb-16"
            variants={itemVariants}
          >
            <motion.div
              whileHover={!disableHeavyAnimations ? { scale: 1.03 } : undefined}
              whileTap={!disableHeavyAnimations ? { scale: 0.98 } : undefined}
            >
              <Button
                onClick={() => handleAuthClick('register')}
                className="text-white bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 shadow-lg shadow-indigo-500/30 px-8 py-3 text-lg"
                rounded="full"
              >
                Create Free Account
              </Button>
            </motion.div>
            <motion.div
              whileHover={!disableHeavyAnimations ? { scale: 1.03 } : undefined}
              whileTap={!disableHeavyAnimations ? { scale: 0.98 } : undefined}
            >
              <Button
                className="text-white/90 bg-white/10 hover:bg-white/15 backdrop-blur-sm shadow-md border border-white/10 px-8 py-3 text-lg relative"
                rounded="full"
                onClick={handleScrollToFeatures}
              >
                Learn More
              </Button>
            </motion.div>
          </motion.div>
        </motion.div>

        {/* Scroll prompt */}
        <div className="w-full absolute bottom-8 left-0 right-0 z-10">
          <div
            className="mx-auto w-max cursor-pointer"
            onClick={handleScrollToFeatures}
          >
            <ScrollPrompt />
          </div>
        </div>
      </div>

      {/* Auth modal */}
      <AnimatePresence>
        {showAuth && (
          <EnhancedAuthModal
            isOpen={showAuth}
            onClose={handleModalClose}
            initialMode={authMode}
          />
        )}
      </AnimatePresence>
    </div>
  );
}