import React from 'react';
import Button from '../common/Button';
import { buttonPress, pulseOnHover } from '../../lib/utils/animations';

export default function CallToAction({ onAssessment, onLearnMore }) {
  return (
    <div className="flex justify-center space-x-4 animate-[fadeInUp_0.5s_ease-out]">
      <Button
        onClick={onAssessment}
        className={`
          bg-gradient-to-r from-blue-600 to-blue-500
          hover:from-blue-500 hover:to-blue-400
          transform transition-all duration-200
          ${buttonPress} ${pulseOnHover}
        `}
      >
        Start Your Free Assessment
      </Button>
      
      <Button
        variant="secondary"
        onClick={onLearnMore}
        className={`${buttonPress} ${pulseOnHover}`}
      >
        Learn More
      </Button>
    </div>
  );
}