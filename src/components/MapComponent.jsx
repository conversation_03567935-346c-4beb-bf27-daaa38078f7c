import React, { useRef, useEffect, useState } from "react";
import { loadGoogleMapsScript } from "../lib/services/googleMapsService";

// Add debugging output to verify the component is being used
console.log("MapComponent loaded");

const MapComponent = ({ 
  locations = [], 
  center = { lat: 39.8283, lng: -98.5795 }, // Default US center
  zoom = 4,
  height = "500px",
  onMarkerClick,
  customStyles
}) => {
  const mapRef = useRef(null);
  const [map, setMap] = useState(null);
  const [markers, setMarkers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeInfoWindow, setActiveInfoWindow] = useState(null);
  const googleRef = useRef(null);

  // Debug logging when component mounts
  useEffect(() => {
    console.log("MapComponent mounted", { 
      mapRefExists: !!mapRef.current,
      locationsCount: locations.length,
      center,
      zoom
    });
    
    // Check for API key
    if (!import.meta.env.VITE_GOOGLE_MAPS_API_KEY) {
      console.error("Google Maps API key is missing in environment variables!");
      setError(new Error("Google Maps API key is missing"));
      setLoading(false);
    }
    
    return () => {
      console.log("MapComponent unmounting");
    };
  }, []);

  // Initialize map
  useEffect(() => {
    let isMounted = true;
    
    const initMap = async () => {
      if (!mapRef.current) {
        console.error("Map container reference is null");
        return;
      }

      try {
        console.log("Initializing map...");
        setLoading(true);
        
        // Load Google Maps script
        console.log("Loading Google Maps script...");
        const google = await loadGoogleMapsScript();
        
        if (!isMounted) {
          console.log("Component unmounted during script load");
          return;
        }
        
        console.log("Google Maps script loaded successfully");
        googleRef.current = google;
        
        // Create the map instance
        console.log("Creating map instance with options:", { center, zoom });
        const mapInstance = new google.maps.Map(mapRef.current, {
          center,
          zoom,
          mapTypeControl: false,
          streetViewControl: false,
          fullscreenControl: true,
          zoomControl: true,
          styles: customStyles || undefined,
        });

        if (isMounted) {
          console.log("Map instance created successfully");
          setMap(mapInstance);
          setLoading(false);
        }
      } catch (err) {
        console.error("Error initializing map:", err);
        if (isMounted) {
          setError(err);
          setLoading(false);
        }
      }
    };

    initMap();

    return () => {
      isMounted = false;
      // Clean up markers when component unmounts
      if (markers.length > 0) {
        console.log("Cleaning up markers");
        clearMarkers();
      }
    };
  }, [center, zoom, customStyles]);

  // Create markers when map is ready and locations change
  useEffect(() => {
    if (!map || !googleRef.current) {
      console.log("Map or Google not yet available for markers");
      return;
    }
    
    if (!locations.length) {
      console.log("No locations provided for markers");
      return;
    }
    
    console.log("Creating markers for", locations.length, "locations");

    // Remove existing markers
    clearMarkers();
    
    // Create new markers
    const newMarkers = locations.map(location => {
      // Validate location coordinates
      const lat = parseFloat(location.lat || location.latitude);
      const lng = parseFloat(location.lng || location.longitude);
      
      if (isNaN(lat) || isNaN(lng)) {
        console.error("Invalid coordinates for location:", location);
        return null;
      }
      
      console.log("Creating marker for location:", location.name || "Unnamed");
      
      // Create info window content
      const infoContent = document.createElement('div');
      infoContent.className = 'p-3 max-w-sm';
      infoContent.innerHTML = `
        <h3 class="font-semibold text-lg">${location.name || 'Location'}</h3>
        ${location.address ? `<p class="text-sm text-gray-600 mt-1">${location.address}</p>` : ''}
        ${location.description ? `<p class="mt-2 text-sm">${location.description}</p>` : ''}
      `;
      
      // Create info window
      const infoWindow = new googleRef.current.maps.InfoWindow({
        content: infoContent,
        maxWidth: 300
      });
      
      // Create marker position
      const position = { lat, lng };
      
      // Use Advanced Marker Element if available (to address the deprecation warning)
      let marker;
      
      try {
        if (googleRef.current.maps.marker?.AdvancedMarkerElement) {
          console.log("Using AdvancedMarkerElement");
          // Create marker element
          const markerElement = document.createElement('div');
          markerElement.className = 'marker-pin';
          markerElement.innerHTML = `
            <div class="w-10 h-10 bg-primary rounded-full flex items-center justify-center text-white shadow-lg transform -translate-x-1/2 -translate-y-full">
              ${location.icon || '📍'}
            </div>
          `;
          
          // Create advanced marker
          marker = new googleRef.current.maps.marker.AdvancedMarkerElement({
            map,
            position,
            content: markerElement,
            title: location.name || 'Location'
          });
          
          // Add click listener
          marker.addListener('click', () => {
            // Close any open info windows
            if (activeInfoWindow) {
              activeInfoWindow.close();
            }
            
            // Open this info window
            infoWindow.open({
              anchor: marker,
              map
            });
            
            setActiveInfoWindow(infoWindow);
            
            // Call the callback if provided
            if (onMarkerClick) {
              onMarkerClick(location);
            }
          });
        } else {
          console.log("Using legacy Marker");
          // Fallback to standard Marker
          marker = new googleRef.current.maps.Marker({
            map,
            position,
            title: location.name || 'Location',
            animation: googleRef.current.maps.Animation.DROP
          });
          
          // Add click listener
          marker.addListener('click', () => {
            // Close any open info windows
            if (activeInfoWindow) {
              activeInfoWindow.close();
            }
            
            // Open this info window
            infoWindow.open(map, marker);
            
            setActiveInfoWindow(infoWindow);
            
            // Call the callback if provided
            if (onMarkerClick) {
              onMarkerClick(location);
            }
          });
        }
      } catch (err) {
        console.error("Error creating marker:", err);
        return null;
      }
      
      return marker;
    }).filter(Boolean); // Remove null markers
    
    console.log("Created", newMarkers.length, "markers");
    setMarkers(newMarkers);
    
    // Fit bounds if more than one location
    if (newMarkers.length > 1) {
      console.log("Fitting map to markers");
      fitMapToMarkers(locations);
    }
    
  }, [map, locations, onMarkerClick]);

  // Helper functions
  const clearMarkers = () => {
    markers.forEach(marker => {
      if (marker) {
        try {
          if (marker.setMap) {
            marker.setMap(null);
          } else if (marker.map) {
            marker.map = null;
          }
        } catch (err) {
          console.error("Error clearing marker:", err);
        }
      }
    });
    setMarkers([]);
  };

  const fitMapToMarkers = (locations) => {
    if (!map || !googleRef.current || !locations.length) return;
    
    try {
      const bounds = new googleRef.current.maps.LatLngBounds();
      
      locations.forEach(location => {
        const lat = parseFloat(location.lat || location.latitude);
        const lng = parseFloat(location.lng || location.longitude);
        
        if (!isNaN(lat) && !isNaN(lng)) {
          bounds.extend({ lat, lng });
        }
      });
      
      map.fitBounds(bounds);
      
      // Don't zoom in too far
      if (map.getZoom() > 16) {
        map.setZoom(16);
      }
    } catch (err) {
      console.error("Error fitting bounds:", err);
    }
  };

  // Handle map resize
  useEffect(() => {
    if (map && googleRef.current) {
      const handleResize = () => {
        try {
          googleRef.current.maps.event.trigger(map, "resize");
          
          // Re-center the map
          if (center) {
            map.setCenter(center);
          }
        } catch (err) {
          console.error("Error handling resize:", err);
        }
      };

      window.addEventListener("resize", handleResize);
      // Initial resize trigger
      setTimeout(handleResize, 100);
      
      return () => window.removeEventListener("resize", handleResize);
    }
  }, [map, center]);

  return (
    <div className="relative w-full">
      {/* Map Container */}
      <div 
        ref={mapRef} 
        className="w-full rounded-lg shadow-sm relative z-10"
        style={{ height }}
      />

      {/* Loading Overlay */}
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white/50 backdrop-blur-sm rounded-lg z-20">
          <div className="flex flex-col items-center gap-2">
            <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin" />
            <span className="text-sm text-gray-600">Loading map...</span>
          </div>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-red-50 rounded-lg z-20">
          <div className="text-center p-4">
            <p className="text-red-500 font-medium">Error loading map</p>
            <p className="text-sm text-red-400 mt-1">{error.message}</p>
            <button 
              onClick={() => window.location.reload()} 
              className="mt-4 px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors"
            >
              Retry
            </button>
          </div>
        </div>
      )}
      
      {/* Debug Info (only in development) */}
      {import.meta.env.DEV && (
        <div className="mt-2 text-xs text-gray-500">
          <p>Map Status: {loading ? 'Loading' : (error ? 'Error' : (map ? 'Ready' : 'Not Initialized'))}</p>
          <p>Locations: {locations.length}</p>
          <p>Markers: {markers.length}</p>
          <p>API Key: {import.meta.env.VITE_GOOGLE_MAPS_API_KEY ? '✅ Present' : '❌ Missing'}</p>
        </div>
      )}
    </div>
  );
};

export default MapComponent; 