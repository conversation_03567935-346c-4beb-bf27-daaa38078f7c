import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { DENTECH_TOOLS, PREMIUM_DENTECH_TOOLS } from '../../lib/constants/navigation';
import WebcamOralScanner from '../ai-tools/WebcamOralScanner';
import VoiceAnalysisTool from '../ai-tools/VoiceAnalysisTool';
import ThermalImagingTool from '../ai-tools/ThermalImagingTool';
import BreathAnalysisTool from '../ai-tools/BreathAnalysisTool';
import MirrorModeTool from '../ai-tools/MirrorModeTool';
import SmiloBrushTool from '../ai-tools/SmiloBrushTool';
import SmiloSecureTool from '../smilo-secure/frontend/SmiloSecureTool';
import { Card } from '../common';

// Tool icons
const ToolIcons = {
  camera: (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
    </svg>
  ),
  microphone: (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
    </svg>
  ),
  fire: (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 18.657A8 8 0 016.343 7.343S7 9 9 10c0-2 .5-5 2.986-7C14 5 16.09 5.777 17.656 7.343A7.975 7.975 0 0120 13a7.975 7.975 0 01-2.343 5.657z" />
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.879 16.121A3 3 0 1012.015 11L11 14H9c0 .768.293 1.536.879 2.121z" />
    </svg>
  ),
  wind: (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
    </svg>
  ),
  mirror: (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
    </svg>
  ),
  bluetooth: (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18.75a6 6 0 006-6v-1.5m-6 7.5a6 6 0 01-6-6v-1.5m6 7.5v3.75m-3.75 0h7.5M12 15.75a3 3 0 01-3-3V4.5a3 3 0 116 0v8.25a3 3 0 01-3 3z" />
    </svg>
  ),
  shield: (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
    </svg>
  )
};

export default function DenTechPage() {
  const [selectedTool, setSelectedTool] = useState(null);
  const [toolData, setToolData] = useState(null);

  const handleSelectTool = (toolId) => {
    // Check in both regular and premium tools
    const tool = [...DENTECH_TOOLS, ...PREMIUM_DENTECH_TOOLS].find(t => t.id === toolId);
    setToolData(tool);
    setSelectedTool(toolId);
  };

  const handleBackToTools = () => {
    setSelectedTool(null);
    setToolData(null);
  };

  const renderTool = () => {
    switch (selectedTool) {
      case 'webcam':
        return <WebcamOralScanner onBack={handleBackToTools} />;
      case 'voice':
        return <VoiceAnalysisTool onBack={handleBackToTools} />;
      case 'thermal':
        return <ThermalImagingTool onBack={handleBackToTools} />;
      case 'breath':
        return <BreathAnalysisTool onBack={handleBackToTools} />;
      case 'mirror':
        return <MirrorModeTool onBack={handleBackToTools} />;
      case 'smilobrush':
        return <SmiloBrushTool onBack={handleBackToTools} />;
      case 'smilosecure':
        return <SmiloSecureTool onBack={handleBackToTools} />;
      default:
        return null;
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 10
      }
    }
  };

  const pageTransition = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: "easeInOut"
      }
    },
    exit: {
      opacity: 0,
      y: -20,
      transition: {
        duration: 0.3
      }
    }
  };

  return (
    <div className="container mx-auto px-4 py-16 max-w-7xl relative min-h-[80vh]">
      {/* Background elements */}
      <div className="absolute top-1/4 right-1/4 w-64 h-64 bg-blue-500/10 rounded-full blur-[100px] -z-10"></div>
      <div className="absolute bottom-1/4 left-1/4 w-64 h-64 bg-purple-500/10 rounded-full blur-[100px] -z-10"></div>
      <div className="absolute top-1/3 left-1/3 w-32 h-32 bg-cyan-500/10 rounded-full blur-[80px] -z-10"></div>

      <AnimatePresence mode="sync">
        {!selectedTool ? (
          <motion.div
            key="tools-grid"
            initial="hidden"
            animate="visible"
            exit="exit"
            variants={pageTransition}
          >
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="text-center mb-16"
            >
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
                <span className="bg-gradient-to-r from-blue-400 via-indigo-400 to-purple-400 text-transparent bg-clip-text">
                  DenTech AI Tools
                </span>
              </h1>
              <p className="text-xl text-white/70 max-w-3xl mx-auto">
                Cutting-edge AI-powered dental technology tools to monitor and improve your oral health,
                all accessible through your device.
              </p>
            </motion.div>

            {/* Standard Tools Section */}
            <motion.div className="mb-16">
              <h2 className="text-2xl md:text-3xl font-bold mb-8 text-white">Standard Tools</h2>
              <motion.div
                className="grid grid-cols-1 md:grid-cols-2 gap-8"
                variants={containerVariants}
              >
                {DENTECH_TOOLS.map((tool, index) => (
                  <motion.div
                    key={tool.id}
                    variants={itemVariants}
                    whileHover={{
                      scale: 1.03,
                      transition: { duration: 0.2 }
                    }}
                    whileTap={{ scale: 0.97 }}
                  >
                    <Card
                      className="cursor-pointer h-full hover:shadow-xl hover:shadow-blue-500/10 transition-all duration-300 overflow-hidden"
                    >
                      <div className="p-8 flex flex-col h-full relative">
                        <div className="mb-6">
                          <div className={`inline-flex p-4 rounded-xl bg-gradient-to-br ${tool.color} mb-4`}>
                            {ToolIcons[tool.icon]}
                          </div>
                          <h3 className="text-2xl font-bold text-white mb-2">{tool.name}</h3>
                          <p className="text-white/70">{tool.description}</p>
                        </div>

                        <div className="mt-auto">
                          <button
                            onClick={() => handleSelectTool(tool.id)}
                            className={`inline-flex items-center py-2 px-4 rounded-lg bg-gradient-to-r ${tool.color} text-white hover:shadow-lg hover:shadow-${tool.color.split(' ')[0]}/20 transition-all duration-300`}
                          >
                            <span>Launch Tool</span>
                            <svg className="ml-2 w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                            </svg>
                          </button>
                        </div>

                        {/* Dynamic particle effects */}
                        <div className="absolute bottom-4 right-4 opacity-20">
                          <motion.div
                            className="h-20 w-20"
                            animate={{
                              scale: [1, 1.1, 1],
                              opacity: [0.1, 0.3, 0.1]
                            }}
                            transition={{
                              duration: 4,
                              repeat: Infinity,
                              delay: index * 0.5
                            }}
                          >
                            {ToolIcons[tool.icon]}
                          </motion.div>
                        </div>
                      </div>
                    </Card>
                  </motion.div>
                ))}
              </motion.div>
            </motion.div>

            {/* Premium Tools Section */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.6 }}
            >
              <div className="flex items-center mb-8">
                <h2 className="text-2xl md:text-3xl font-bold text-white mr-3">Premium Tools</h2>
                <span className="px-3 py-1 bg-gradient-to-r from-amber-400 to-yellow-500 text-black font-medium text-sm rounded-full">
                  Hardware Required
                </span>
              </div>

              <p className="text-white/70 mb-8 max-w-3xl">
                These advanced tools require specific hardware components to function properly. Each tool comes with
                recommendations for compatible hardware that can be purchased separately.
              </p>

              <motion.div
                className="grid grid-cols-1 lg:grid-cols-3 gap-8"
                variants={containerVariants}
              >
                {PREMIUM_DENTECH_TOOLS.map((tool, index) => (
                  <motion.div
                    key={tool.id}
                    variants={itemVariants}
                    whileHover={{
                      scale: 1.03,
                      transition: { duration: 0.2 }
                    }}
                    whileTap={{ scale: 0.97 }}
                  >
                    <Card
                      className="cursor-pointer h-full hover:shadow-xl hover:shadow-blue-500/10 transition-all duration-300 overflow-hidden border border-white/10"
                    >
                      <div className="p-8 flex flex-col h-full relative">
                        <div className="mb-6">
                          <div className={`inline-flex p-4 rounded-xl bg-gradient-to-br ${tool.color} mb-4`}>
                            {ToolIcons[tool.icon]}
                          </div>
                          <h3 className="text-2xl font-bold text-white mb-2">{tool.name}</h3>
                          <p className="text-white/70 mb-4">{tool.description}</p>

                          <div className="bg-white/5 rounded-lg p-3 mb-4">
                            <p className="text-sm text-white/80 mb-1"><strong>Required Hardware:</strong></p>
                            <div className="flex items-center justify-between">
                              <p className="text-sm text-white/70">{tool.requiredHardware}</p>
                              <span className="text-sm font-semibold text-amber-400">{tool.hardwarePrice}</span>
                            </div>
                          </div>
                        </div>

                        <div className="mt-auto flex space-x-2">
                          <button
                            onClick={() => handleSelectTool(tool.id)}
                            className={`flex-1 inline-flex items-center justify-center py-2 px-4 rounded-lg bg-gradient-to-r ${tool.color} text-white hover:shadow-lg hover:shadow-${tool.color.split(' ')[0]}/20 transition-all duration-300`}
                          >
                            <span>Launch Tool</span>
                            <svg className="ml-2 w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                            </svg>
                          </button>

                          <button
                            className="inline-flex items-center justify-center py-2 px-4 rounded-lg border border-white/20 hover:bg-white/5 text-white transition-all duration-300"
                          >
                            <span>Shop</span>
                            <svg className="ml-2 w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                            </svg>
                          </button>
                        </div>

                        {/* Premium badge */}
                        <div className="absolute top-2 right-2">
                          <span className="px-2 py-1 bg-amber-500/20 text-amber-300 text-xs rounded-full border border-amber-500/30">
                            Premium
                          </span>
                        </div>
                      </div>
                    </Card>
                  </motion.div>
                ))}
              </motion.div>
            </motion.div>
          </motion.div>
        ) : (
          <motion.div
            key="selected-tool"
            initial="hidden"
            animate="visible"
            exit="exit"
            variants={pageTransition}
            className="tool-container"
          >
            {toolData && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className={`py-3 px-4 rounded-lg mb-6 inline-flex items-center bg-gradient-to-r ${toolData.color} bg-opacity-10`}
              >
                <div className="mr-3">
                  {ToolIcons[toolData.icon]}
                </div>
                <div>
                  <h2 className="text-xl font-bold text-white">{toolData.name}</h2>
                </div>
              </motion.div>
            )}
            {renderTool()}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}