import React from 'react';
import { Card } from './common';
import { formatDistanceToNow } from 'date-fns';

export default function STLFileList({ files, onSelect, onDelete, loading }) {
  if (loading) {
    return (
      <div className="flex justify-center items-center h-32">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (!files?.length) {
    return (
      <div className="text-center py-8 text-white/60">
        No STL files uploaded yet
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {files.map((file) => (
        <Card key={file.id} className="hover:bg-white/10 transition-colors cursor-pointer">
          <div className="flex items-center justify-between">
            <div 
              className="flex-1 p-4"
              onClick={() => onSelect(file.public_url)}
            >
              <h4 className="font-medium text-white mb-1">
                {file.original_name}
              </h4>
              <div className="text-sm text-white/60">
                <span>{(file.file_size / 1024 / 1024).toFixed(1)} MB</span>
                <span className="mx-2">•</span>
                <span>
                  {formatDistanceToNow(new Date(file.created_at), { addSuffix: true })}
                </span>
              </div>
            </div>
            
            <button
              onClick={() => onDelete(file.id)}
              className="p-2 text-red-400 hover:text-red-300 transition-colors"
            >
              <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </button>
          </div>
        </Card>
      ))}
    </div>
  );
}