import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { pageTransitionVariants } from '../../lib/utils/animations';
import { useAnimations } from '../../contexts/AnimationContext';

export default function PageTransition({ children, className = "" }) {
  const { animationsEnabled, isHighPerformanceDevice } = useAnimations();
  const [reduceMotion, setReduceMotion] = useState(false);
  const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
  const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);

  useEffect(() => {
    // Check if animations should be reduced
    setReduceMotion(!animationsEnabled || !isHighPerformanceDevice || isSafari || isIOS);

    // Re-check on window resize
    const handleResize = () => {
      setReduceMotion(!animationsEnabled || !isHighPerformanceDevice || isSafari || isIOS);
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [animationsEnabled, isHighPerformanceDevice]);

  // Use simplified transitions for mobile/reduced motion/Safari
  const transitionProps = reduceMotion ? {
    type: "tween",
    duration: 0.2,
    ease: "easeOut"
  } : {
    type: "tween", // Use tween instead of spring for better performance
    duration: 0.3,
    ease: "easeOut"
  };

  // Simplified variants for reduced motion
  const variants = reduceMotion ? {
    initial: { opacity: 0 },
    enter: { opacity: 1 },
    exit: { opacity: 0 }
  } : {
    initial: { opacity: 0, y: 10 }, // Reduced movement distance
    enter: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -10 }
  };

  return (
    <motion.div
      key={location.pathname}
      initial="initial"
      animate="enter"
      exit="exit"
      variants={variants}
      className={`w-full overflow-hidden relative z-10 ${className}`}
      transition={transitionProps}
      layout={false} // Disable layout animations completely
      style={{
        willChange: 'transform, opacity',
        transform: 'translateZ(0)',
        WebkitTransform: 'translateZ(0)',
        backfaceVisibility: 'hidden',
        WebkitBackfaceVisibility: 'hidden',
        perspective: '1000px',
        WebkitPerspective: '1000px'
      }}
    >
      {children}
    </motion.div>
  );
}