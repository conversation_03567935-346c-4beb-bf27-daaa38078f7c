import React from 'react';
import { Link } from 'react-router-dom';

export default function FooterSection({ title, items = [] }) {
  // Handle case where items might be undefined or not an array
  if (!items || !Array.isArray(items) || items.length === 0) {
    return (
      <div>
        <h3 className="text-white font-medium text-lg mb-4">{title}</h3>
        <p className="text-white/60">No items available</p>
      </div>
    );
  }

  return (
    <div>
      <h3 className="text-white font-medium text-lg mb-4">{title}</h3>
      <ul className="space-y-3">
        {items.map((item) => {
          const label = item.label || item.name || '';
          const href = item.href || '#';
          const isExternal = href.startsWith('http');
          
          return (
            <li key={label + href}>
              {isExternal ? (
                <a
                  href={href}
                  className="text-white/70 hover:text-white transition-colors"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  {label}
                </a>
              ) : (
                <Link
                  to={href}
                  className="text-white/70 hover:text-white transition-colors"
                >
                  {label}
                </Link>
              )}
            </li>
          );
        })}
      </ul>
    </div>
  );
}