import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { scrollToElement } from '../../lib/utils/transitions';

export default function ScrollLink({ to, children, className }) {
  const navigate = useNavigate();
  const location = useLocation();

  const handleClick = (e) => {
    e.preventDefault();
    
    if (location.pathname === '/') {
      scrollToElement(to.replace('/', ''));
    } else {
      navigate('/');
      setTimeout(() => {
        scrollToElement(to.replace('/', ''));
      }, 500);
    }
  };

  return (
    <a href={to} onClick={handleClick} className={className}>
      {children}
    </a>
  );
}