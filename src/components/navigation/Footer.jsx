import React, { useState, lazy, Suspense, useEffect, useRef } from 'react';
import { FOOTER_LINKS } from '../../lib/constants/navigation';
import { BRANDING } from '../../lib/constants/branding';
import { useUser } from '../../contexts/UserContext';
import { useAdminAuth } from '../../lib/hooks/useAdminAuth';
import Logo from '../common/Logo';
import { Link } from 'react-router-dom';
import SocialLinks from './SocialLinks';
import KofiButton from '../common/KofiButton';
import { motion, AnimatePresence } from 'framer-motion';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { supabase } from '../../lib/supabase';
import { Sparkles, Zap } from 'lucide-react';
import { useAnimations } from '../../contexts/AnimationContext';

// Lazy load the admin component to isolate it from the main site
const AdminAuth = lazy(() => import('../admin/AdminAuth'));

export default function Footer() {
  const { user } = useUser();
  const { logout, checkAdminSession } = useAdminAuth();
  const [showAdminModal, setShowAdminModal] = useState(false);
  const [showLogoutConfirm, setShowLogoutConfirm] = useState(false);
  const [showDisclaimer, setShowDisclaimer] = useState(true);
  const isAdmin = checkAdminSession();
  const animationsDisabled = useRef(false);

  // Use the animation context instead of local state
  const { animationsEnabled, toggleAnimations } = useAnimations();

  // Performance optimization - detect if the page has been open too long
  // and disable unnecessary animations
  useEffect(() => {
    // Check if we should disable animations based on page load time
    const checkAnimationStatus = () => {
      if (window._pageLoadTime) {
        const timeOnPage = Date.now() - window._pageLoadTime;
        // If page has been open for more than 90 seconds, disable footer animations
        if (timeOnPage > 90000) {
          animationsDisabled.current = true;
        }
      }
    };

    // Run check once on mount
    checkAnimationStatus();

    // Set up visibility change listener to check again when tab becomes active
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        checkAnimationStatus();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  // Check localStorage on mount to see if disclaimer has been accepted
  useEffect(() => {
    const hasAcceptedDisclaimer = localStorage.getItem('disclaimerAccepted');
    if (hasAcceptedDisclaimer) {
      setShowDisclaimer(false);
    }
  }, []);

  // Function to handle disclaimer acceptance
  const handleDisclaimerAccept = async () => {
    try {
      // Log the user acceptance
      const userIP = await fetch('https://api.ipify.org?format=json')
        .then(response => response.json())
        .then(data => data.ip)
        .catch(() => 'unknown');

      // Log to database if available
      const userData = {
        user_id: user?.id || 'anonymous',
        ip_address: userIP,
        disclaimer_type: 'medical_disclaimer',
        accepted_at: new Date().toISOString(),
        user_agent: navigator.userAgent || 'unknown'
      };

      // Log to console
      console.log('Disclaimer accepted:', userData);

      // If supabase is available, log to database
      if (supabase) {
        try {
          const { error } = await supabase
            .from('disclaimer_acceptances')
            .insert([userData]);

          if (error) {
            console.error('Failed to log disclaimer acceptance:', error);
          }
        } catch (err) {
          console.error('Error logging disclaimer acceptance:', err);
        }
      }

      // Save to localStorage to remember acceptance
      localStorage.setItem('disclaimerAccepted', 'true');
      localStorage.setItem('disclaimerAcceptedAt', new Date().toISOString());

      // Hide the disclaimer
      setShowDisclaimer(false);
    } catch (error) {
      console.error('Error processing disclaimer acceptance:', error);
      // Still hide the disclaimer even if logging fails
      setShowDisclaimer(false);
    }
  };

  // Function to scroll to top when links are clicked
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  // Find the Connect section from FOOTER_LINKS for social media links
  const connectSection = FOOTER_LINKS.find(section => section.section === 'Connect');
  const socialLinks = connectSection ? connectSection.links.map(link => ({
    label: link.name,
    href: link.href,
    icon: getIconFromUrl(link.href) // Extract icon from URL
  })) : [];

  // Get icon name from URL
  function getIconFromUrl(url = '') {
    if (url.includes('twitter')) return 'twitter';
    if (url.includes('linkedin')) return 'linkedin';
    if (url.includes('instagram') || url.includes('facebook')) return 'instagram'; // Use instagram icon for facebook too
    return null;
  }

  // Create a handler for admin button click
  const handleAdminClick = () => {
    setShowAdminModal(true);
  };

  // Create a handler for admin modal close
  const handleAdminModalClose = () => {
    setShowAdminModal(false);
  };

  // Handle opening the logout confirmation dialog
  const handleShowLogoutConfirm = () => {
    setShowLogoutConfirm(true);
  };

  // Handle closing the logout confirmation dialog
  const closeLogoutConfirm = () => {
    setShowLogoutConfirm(false);
  };

  // Handle admin logout
  const handleAdminLogout = () => {
    // Close the confirmation dialog
    setShowLogoutConfirm(false);

    try {
      // Clear all localStorage item by item
      localStorage.removeItem('admin_session');
      localStorage.removeItem('smilo_admin_session');
      localStorage.removeItem('supabase.auth.token');

      // Force clear all admin and session related items
      const keysToRemove = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && (key.includes('admin') || key.includes('session') || key.includes('supabase') || key.includes('token'))) {
          keysToRemove.push(key);
        }
      }

      // Remove identified keys
      keysToRemove.forEach(key => {
        localStorage.removeItem(key);
      });

      // Reset any admin state in window object
      if (window && window.smiloAdmin) {
        window.smiloAdmin.isLoggedIn = false;
      }

      // Also clear sessionStorage
      sessionStorage.clear();

      // Only after clearing everything, call the hook's logout (which might add its own cleanup)
      logout();
    } catch (error) {
      console.error('Error during admin logout:', error);
    }

    // Force navigation to home page with cache-busting parameter
    const timestamp = new Date().getTime();
    console.log('Admin logout from footer - redirecting to home');
    window.location.href = `/?logout=${timestamp}`;

    // Fallback in case the above doesn't work
    setTimeout(() => {
      console.log('Fallback redirect triggered from footer');
      try {
        window.location.replace('/');
      } catch (e) {
        console.error('Redirect failed:', e);
      }

      // Third attempt with different approach
      setTimeout(() => {
        try {
          document.location.href = '/';
        } catch (e) {
          console.error('All redirects failed:', e);
          // Last resort - try to open in new tab/window
          window.open('/', '_self');
        }
      }, 100);
    }, 100);
  };

  return (
    <footer className="relative w-full bg-gradient-to-b from-gray-900 to-gray-950 pt-10 pb-8 overflow-hidden">
      {/* Medical/Dental Disclaimer Section - Now dismissible */}
      <AnimatePresence>
        {showDisclaimer && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
            className="max-w-6xl mx-auto px-6 lg:px-8 mb-10"
          >
            <div className="bg-red-900/30 border border-red-500/30 rounded-lg p-4 shadow-lg backdrop-blur-sm">
              <div className="flex justify-between items-start">
                <h3 className="text-red-300 font-semibold text-sm uppercase tracking-wider mb-2">IMPORTANT MEDICAL DISCLAIMER</h3>
                <button
                  onClick={handleDisclaimerAccept}
                  className="px-3 py-1 bg-red-700 hover:bg-red-600 text-white text-xs font-medium rounded transition-colors duration-200 ml-4"
                >
                  I Understand
                </button>
              </div>
              <p className="text-white/80 text-sm">
                Smilo is not a licensed dentist, doctor, or healthcare provider. This platform does not provide medical or dental advice, diagnosis, or treatment.
                All information, content, and material provided on this website is for informational and educational purposes only and is not a substitute for professional advice,
                diagnosis, or treatment from a qualified healthcare provider. The AI-generated recommendations and responses should not be relied upon as professional healthcare advice.
                Always seek the advice of your dentist, physician, or other qualified healthcare provider with any questions you may have regarding a medical or dental condition.
              </p>
              <div className="mt-2 flex justify-end">
                <Link to="/terms" className="text-blue-300 hover:text-blue-200 text-xs transition-colors duration-200 mr-4">
                  Terms of Service
                </Link>
                <Link to="/privacy" className="text-blue-300 hover:text-blue-200 text-xs transition-colors duration-200">
                  Privacy Policy
                </Link>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Animation toggle moved to bottom section */}

      {/* Decorative elements - simplified when animations are disabled */}
      <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500"></div>

      {/* Conditionally render decorative elements only if animations aren't disabled */}
      {!animationsDisabled.current && (
        <>
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-500/10 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-20 -left-20 w-60 h-60 bg-purple-500/10 rounded-full blur-3xl"></div>
        </>
      )}

      <div className="max-w-6xl mx-auto px-6 lg:px-8 relative z-10">
        {/* "Created by" section at the top - ENHANCED VERSION with conditional animations */}
        <div className="mb-8">
          <motion.div
            className="max-w-md mx-auto"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4 }}
          >
            <div className="bg-gradient-to-r from-blue-600/20 via-indigo-600/20 to-purple-600/20 p-4 rounded-xl border border-white/10 shadow-lg backdrop-blur-sm flex items-center justify-center relative group overflow-hidden">
              {/* Background effect - only applied when animations aren't disabled */}
              {!animationsDisabled.current && (
                <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 via-indigo-600/5 to-purple-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
              )}

              {/* Subtle pulsing glow */}
              <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-xl blur-sm group-hover:blur-md transition-all duration-700 opacity-0 group-hover:opacity-100"></div>

              {/* Avatar circle with gradient border */}
              <div className="mr-4 p-0.5 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full shadow-lg relative z-10 group-hover:scale-105 transition-transform duration-300">
                <div className="bg-gray-900/90 p-2.5 rounded-full">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                </div>
              </div>

              {/* Text content */}
              <div className="flex flex-col relative z-10">
                <p className="text-white text-sm font-medium">
                  Created and founded by
                </p>
                <h3 className="text-xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-indigo-400 to-purple-400 relative" title="Powered by Jamevo">
                  Michael Coffie
                  <span className="absolute -bottom-1 left-0 h-0.5 w-0 bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500 group-hover:w-full transition-all duration-500"></span>
                </h3>
              </div>

              {/* Right decorative line with animation */}
              <div className="ml-4 w-16 h-0.5 rounded-full bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500 relative z-10 group-hover:w-24 transition-all duration-500"></div>
            </div>
          </motion.div>
        </div>

        {/* Improved separator line below the "Created by" section */}
        <div className="w-full h-0.5 bg-gradient-to-r from-transparent via-white/20 to-transparent mb-10 relative">
          <div className="absolute left-1/2 -translate-x-1/2 top-1/2 -translate-y-1/2 h-1.5 w-1.5 rounded-full bg-blue-400/70 animate-pulse"></div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-x-10 gap-y-10">
          {/* Brand Section */}
          <div className="col-span-1">
            <motion.div
              className="flex items-start"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Logo size="default" className="transform hover:scale-105 transition-transform duration-200" />
            </motion.div>
            <motion.p
              className="text-white/70 text-sm mt-4 leading-relaxed"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              Your Personalized Guide to Oral Health
            </motion.p>

            <motion.div
              className="mt-4 pt-4 border-t border-white/5"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <p className="text-white/60 text-sm italic">
                <span className="text-blue-300/80">Smile</span> with confidence.
                <span className="text-purple-300/80"> Smile</span> with AI.
              </p>
            </motion.div>
          </div>

          {/* Quick Links Section - Now with two columns */}
          <motion.div
            className="col-span-1"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <h3 className="text-white font-medium text-lg mb-5 relative">
              Quick Links
              <span className="absolute -bottom-2 left-0 w-10 h-0.5 bg-gradient-to-r from-blue-500 to-indigo-500"></span>
            </h3>
            <div className="grid grid-cols-2 gap-x-4">
              <div>
                <ul className="space-y-3.5">
                  <li><Link to="/" onClick={scrollToTop} className="text-white/70 hover:text-white transition-all duration-300 hover:translate-x-1 inline-block">Home</Link></li>
                  <li><Link to="/chat" onClick={scrollToTop} className="text-white/70 hover:text-white transition-all duration-300 hover:translate-x-1 inline-block">Chat</Link></li>
                  <li><Link to="/options" onClick={scrollToTop} className="text-white/70 hover:text-white transition-all duration-300 hover:translate-x-1 inline-block">Options</Link></li>
                  {/* Temporarily hidden until ready */}
                  {/* <li><Link to="/dentech" onClick={scrollToTop} className="text-white/70 hover:text-white transition-all duration-300 hover:translate-x-1 inline-block">DenTech</Link></li> */}
                </ul>
              </div>
              <div>
                <ul className="space-y-3.5">
                  {/* Temporarily hidden until ready */}
                  {/* <li><Link to="/predental" onClick={scrollToTop} className="text-white/70 hover:text-white transition-all duration-300 hover:translate-x-1 inline-block">Pre-Dental</Link></li> */}
                  <li><Link to="/expert-resources" onClick={scrollToTop} className="text-white/70 hover:text-white transition-all duration-300 hover:translate-x-1 inline-block">Resources</Link></li>
                  <li><Link to="/about" onClick={scrollToTop} className="text-white/70 hover:text-white transition-all duration-300 hover:translate-x-1 inline-block">About</Link></li>
                  <li><Link to="/providers" onClick={scrollToTop} className="text-white/70 hover:text-white transition-all duration-300 hover:translate-x-1 inline-block">Providers</Link></li>
                </ul>
              </div>
            </div>
          </motion.div>

          {/* Legal Section */}
          <motion.div
            className="col-span-1"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <h3 className="text-white font-medium text-lg mb-5 relative">
              Legal
              <span className="absolute -bottom-2 left-0 w-10 h-0.5 bg-gradient-to-r from-indigo-500 to-purple-500"></span>
            </h3>
            <ul className="space-y-3.5">
              <li><Link to="/privacy" onClick={scrollToTop} className="text-white/70 hover:text-white transition-all duration-300 hover:translate-x-1 inline-block">Privacy Policy</Link></li>
              <li><Link to="/terms" onClick={scrollToTop} className="text-white/70 hover:text-white transition-all duration-300 hover:translate-x-1 inline-block">Terms of Service</Link></li>
              {/* Hidden Partnerships link as requested */}
              {/* <li><Link to="/partnerships" onClick={scrollToTop} className="text-white/70 hover:text-white transition-all duration-300 hover:translate-x-1 inline-block">Partnerships</Link></li> */}
            </ul>

            {/* Admin Links Section */}
            <div className="mt-6 pt-4 border-t border-white/10">
              {isAdmin ? (
                <div className="space-y-3.5">
                  <div className="flex space-x-4">
                    <Link
                      to="/admin"
                      onClick={scrollToTop}
                      className="text-blue-400 hover:text-blue-300 text-sm transition-colors duration-300 flex items-center"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                      Admin Portal
                    </Link>

                    <Link to="/resources/seo" onClick={scrollToTop} className="text-blue-400 hover:text-blue-300 text-sm transition-colors duration-300 flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
                      </svg>
                      SEO Articles
                      <span className="ml-1.5 px-1.5 py-0.5 text-[10px] bg-blue-900/50 text-blue-300 rounded-full">New</span>
                    </Link>
                  </div>

                  <motion.button
                    onClick={handleShowLogoutConfirm}
                    className="text-red-400 hover:text-red-300 text-sm transition-colors duration-300 flex items-center group"
                    whileHover={{ scale: 1.05 }}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                    </svg>
                    <span className="relative">
                      Logout Admin
                      <span className="absolute -bottom-0.5 left-0 h-0.5 w-0 bg-red-500 group-hover:w-full transition-all duration-300"></span>
                    </span>
                  </motion.button>
                </div>
              ) : (
                <div className="flex space-x-4">
                  <motion.button
                    onClick={handleAdminClick}
                    className="opacity-30 hover:opacity-100 transition-opacity duration-300 text-white/50 hover:text-white/80 text-xs flex items-center group"
                    whileHover={{ scale: 1.05 }}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                    Admin
                  </motion.button>

                  <Link to="/resources/seo" onClick={scrollToTop} className="opacity-30 hover:opacity-100 transition-opacity duration-300 text-white/50 hover:text-white/80 text-xs flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
                    </svg>
                    SEO Articles
                    <span className="ml-1.5 px-1.5 py-0.5 text-[10px] bg-blue-900/50 text-blue-300 rounded-full">New</span>
                  </Link>
                </div>
              )}
            </div>
          </motion.div>

          {/* Connect With Us */}
          <motion.div
            className="col-span-1"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <h3 className="text-white font-medium text-lg mb-5 relative">
              Connect With Us
              <span className="absolute -bottom-2 left-0 w-10 h-0.5 bg-gradient-to-r from-purple-500 to-pink-500"></span>
            </h3>
            <div className="flex space-x-4">
              <SocialLinks links={socialLinks} />
            </div>

            <div className="mt-8 pt-6 border-t border-white/10">
              <p className="text-white/60 text-sm">
                Have questions? Reach out!
              </p>
              <a
                href="mailto:<EMAIL>"
                className="mt-2 inline-flex items-center text-white/70 hover:text-white group transition-colors duration-300"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-blue-400 group-hover:text-blue-300 transition-colors duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                <EMAIL>
              </a>
            </div>
          </motion.div>
        </div>

        {/* Copyright with Animation Toggle and Ko-fi Button */}
        <div className="mt-12 pt-6 border-t border-white/10 text-center relative px-4 sm:px-6">
          <motion.div
            className="flex flex-col md:flex-row justify-between items-center gap-4 md:gap-2"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.6 }}
          >
            {/* Copyright text - left aligned */}
            <div className="md:flex-1 text-left order-1 md:order-1">
              <p className="text-white/60 text-sm">
                © {new Date().getFullYear()} {BRANDING.fullName}. All rights reserved.
              </p>
            </div>

            {/* Animation toggle - center aligned */}
            <div className="order-3 md:order-2 my-4 md:my-0 md:flex-1 flex justify-center">
              <button
                onClick={toggleAnimations}
                className={`
                  flex items-center gap-1.5 px-3 py-1.5 rounded-full transition-all duration-300
                  border border-white/10 backdrop-blur-sm text-xs sm:text-sm
                  ${animationsEnabled
                    ? 'bg-gradient-to-r from-blue-600/20 to-indigo-600/20 hover:from-blue-600/30 hover:to-indigo-600/30 text-blue-300 shadow-sm shadow-blue-500/10'
                    : 'bg-white/5 hover:bg-white/10 text-white/70 hover:text-white/90'}
                `}
              >
                {animationsEnabled ? (
                  <>
                    <Sparkles size={14} className="animate-pulse text-blue-300" />
                    <span className="font-medium">Animations On</span>
                  </>
                ) : (
                  <>
                    <Zap size={14} />
                    <span className="font-medium">Enable Animations</span>
                  </>
                )}
              </button>
            </div>

            {/* Ko-fi Button - right aligned */}
            <div className="md:flex-1 text-right order-2 md:order-3 mt-4 md:mt-0 relative z-10">
              <KofiButton />
            </div>
          </motion.div>
        </div>
      </div>

      {/* Admin Authentication Modal */}
      <Suspense fallback={<div>Loading...</div>}>
        <AdminAuth isOpen={showAdminModal} onClose={handleAdminModalClose} />
      </Suspense>

      {/* Logout Confirmation Dialog */}
      <AnimatePresence>
        {showLogoutConfirm && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50"
            onClick={(e) => {
              // Close dialog when clicking the backdrop
              if (e.target === e.currentTarget) {
                closeLogoutConfirm();
              }
            }}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-gray-800 border border-gray-700 rounded-lg shadow-xl p-6 max-w-md w-full mx-4"
            >
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-white">Confirm Logout</h3>
                <button
                  onClick={closeLogoutConfirm}
                  className="text-gray-400 hover:text-white"
                >
                  <XMarkIcon className="w-5 h-5" />
                </button>
              </div>

              <p className="text-gray-300 mb-6">Are you sure you want to log out of the admin panel and return to the main site?</p>

              <div className="flex justify-end space-x-3">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={closeLogoutConfirm}
                  className="px-4 py-2 bg-gray-700 text-gray-300 rounded-lg hover:bg-gray-600"
                >
                  No
                </motion.button>

                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={handleAdminLogout}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-500"
                >
                  Yes, Log Out
                </motion.button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </footer>
  );
}