import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { NAV_ITEMS } from '../../lib/constants/navigation';

const menuVariants = {
  closed: {
    opacity: 0,
    y: -20,
    height: 0,
    transition: {
      duration: 0.2,
      height: { duration: 0.3 }
    }
  },
  open: {
    opacity: 1,
    y: 0,
    height: 'auto',
    transition: {
      duration: 0.3,
      staggerChildren: 0.07,
      delayChildren: 0.1,
      height: { duration: 0.4 }
    }
  }
};

const itemVariants = {
  closed: { opacity: 0, x: -20 },
  open: { opacity: 1, x: 0 }
};

export default function MobileNav({ isOpen, onClose }) {
  // Handle scroll prevention when menu is open
  React.useEffect(() => {
    const body = document.body;
    
    if (isOpen) {
      // Just add a class - the CSS will handle the overflow
      body.classList.add('mobile-nav-open');
      
      // For iOS specifically, prevent background content from scrolling
      if (/iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream) {
        // Remember the current scroll position
        const scrollY = window.scrollY;
        body.style.top = `-${scrollY}px`;
      }
    } else {
      if (body.classList.contains('mobile-nav-open')) {
        // If we're on iOS and have saved the scroll position
        if (/iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream && body.style.top) {
          const scrollY = parseInt(body.style.top || '0', 10) * -1;
          body.style.top = '';
          // Restore scroll position
          window.scrollTo(0, scrollY);
        }
        
        // Remove the class
        body.classList.remove('mobile-nav-open');
      }
    }
    
    return () => {
      // Cleanup if component unmounts while menu is open
      if (body.classList.contains('mobile-nav-open')) {
        // For iOS, restore scroll position if needed
        if (/iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream && body.style.top) {
          const scrollY = parseInt(body.style.top || '0', 10) * -1;
          body.style.top = '';
          window.scrollTo(0, scrollY);
        }
        
        body.classList.remove('mobile-nav-open');
      }
    };
  }, [isOpen]);

  return (
    <motion.div
      className={`
        fixed inset-x-0 top-16 bg-gray-900/95 backdrop-blur-lg
        border-b border-white/10 shadow-xl z-50
        max-h-[calc(100vh-4rem)] overflow-y-auto scroll-ios 
        ${isOpen ? 'pointer-events-auto' : 'pointer-events-none'}
      `}
      initial="closed"
      animate={isOpen ? "open" : "closed"}
      variants={menuVariants}
    >
      <nav className="max-w-7xl mx-auto px-4 py-6 space-y-4 safe-inset-padding">
        {NAV_ITEMS.map(({ name, href }) => (
          <motion.div
            key={name}
            variants={itemVariants}
            className="touch-target-mobile"
          >
            <Link
              to={href}
              className="block py-3 px-4 text-lg text-white/80 hover:text-white hover:bg-white/5 rounded-lg transition-colors"
              onClick={onClose}
            >
              {name}
            </Link>
          </motion.div>
        ))}
      </nav>
    </motion.div>
  );
}