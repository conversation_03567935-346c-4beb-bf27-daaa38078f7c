import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { NAV_ITEMS } from '../../lib/constants/navigation';

export default function NavLinks({ mobile = false, compact = false, onItemClick, className = '' }) {
  const location = useLocation();
  
  // With HashRouter, the path will be preceded by #
  const currentPath = location.pathname;
  
  return (
    <div className={`flex ${mobile ? 'flex-col space-y-4' : 'items-center justify-center space-x-8'} ${className}`}>
      {NAV_ITEMS.map(({ name, href }) => {
        // Check if the current path matches this nav item
        const isActive = currentPath === href;
        
        return (
          <Link
            key={name}
            to={href}
            onClick={(e) => {
              // Call the onItemClick callback if provided
              if (onItemClick) onItemClick();
              
              // If the link is already active, prevent navigation altogether
              if (isActive) {
                e.preventDefault();
                return;
              }
              
              // Smooth scroll to top on navigation
              setTimeout(() => {
                window.scrollTo({
                  top: 0,
                  behavior: 'smooth'
                });
              }, 100);
            }}
            className={`
              relative
              group
              ${compact ? 'text-xs py-1' : 'text-sm py-2'}
              font-medium
              transition-all
              duration-300
              ${isActive 
                ? 'text-white font-semibold' 
                : 'text-white/70 hover:text-white'
              }
            `}
          >
            {name}
            
            {/* Animated hover underline with gradient */}
            <span 
              className={`
                absolute 
                bottom-0 
                left-0 
                w-0 
                h-0.5 
                bg-gradient-to-r 
                from-blue-400 
                via-indigo-400 
                to-purple-400 
                group-hover:w-full 
                transition-all 
                duration-300
                ${isActive ? 'w-full opacity-100' : 'opacity-0 group-hover:opacity-100'}
              `}
            />
            
            {/* Subtle glow effect on hover */}
            <span className="absolute inset-0 -z-10 rounded-md group-hover:bg-white/5 group-hover:blur-sm transition-all duration-300 opacity-0 group-hover:opacity-100"></span>
          </Link>
        );
      })}
    </div>
  );
}