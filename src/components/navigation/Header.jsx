import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import Logo from '../common/Logo';
import NavLinks from './NavLinks';
import NewAuthButton from '../auth/NewAuthButton';
import FloatingNav from './FloatingNav';
import MobileNav from './MobileNav';
import { useUser } from '../../contexts/UserContext';

export default function Header() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { user } = useUser();
  const lastScrollY = useRef(0);
  const ticking = useRef(false);
  const [isMobile, setIsMobile] = useState(false);

  // Check if device is mobile
  useEffect(() => {
    const checkMobile = () => {
      const mobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || 
                   window.innerWidth <= 768;
      setIsMobile(mobile);
    };
    
    checkMobile();
    
    // Update on resize
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Simplified scroll handler for debugging
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      console.log('Scroll position:', currentScrollY);

      if (currentScrollY > 100) {
        console.log('Setting isScrolled to TRUE');
        setIsScrolled(true);
      } else {
        console.log('Setting isScrolled to FALSE');
        setIsScrolled(false);
      }
    };

    // Add scroll event listener
    window.addEventListener('scroll', handleScroll, { passive: true });

    // Check initial scroll position
    handleScroll();

    // Cleanup
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const handleAuthChange = (userInfo) => {
    console.log('Auth state changed:', userInfo);
    if (isMenuOpen) {
      setIsMenuOpen(false);
    }
  };

  return (
    <>
      <header className={`
        fixed top-0 left-0 right-0 z-50
        transition-all duration-300 ease-out transform
        safe-top fixed-ios
        ${isScrolled ? '-translate-y-full opacity-0 pointer-events-none' : 'translate-y-0 opacity-100'}
        ${isMobile ? 'h-16' : ''}
      `}>
        {/* Simplified background with fewer animations for better performance */}
        <div className="absolute inset-0 -z-10">
          {/* Base gradient */}
          <div className="absolute inset-0 bg-gradient-to-r from-indigo-900/80 via-purple-900/20 to-indigo-900/30 backdrop-blur-sm"></div>
          
          {/* Simple glowing orbs - reduced quantity */}
          <div className="absolute -top-20 -right-20 w-40 h-40 bg-blue-500/20 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-20 -left-20 w-40 h-40 bg-purple-500/20 rounded-full blur-3xl"></div>

          {/* Top border gradient line */}
          <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-blue-500/0 via-indigo-500/50 to-purple-500/0"></div>
        </div>

        <nav className="max-w-7xl mx-auto px-4">
          <div className="flex h-16 items-center">
            {/* Logo (left aligned) */}
            <div className="flex-shrink-0">
              <Link to="/">
                <div className="flex items-center h-full">
                  <Logo
                    size="default"
                    className="transition-transform duration-300 ease-out"
                    animated={false}
                  />
                </div>
              </Link>
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden ml-auto">
              <button
                onClick={toggleMenu}
                className="p-2 rounded-lg hover:bg-white/10 transition-colors h-full flex items-center touch-target-mobile"
                aria-label="Toggle menu"
                aria-expanded={isMenuOpen}
              >
                <svg
                  className="w-6 h-6 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  {isMenuOpen ? (
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  ) : (
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 6h16M4 12h16M4 18h16"
                    />
                  )}
                </svg>
              </button>
            </div>

            {/* Desktop navigation - centered with flex */}
            <div className="hidden md:flex flex-1 justify-center mx-8">
              <NavLinks className="" />
            </div>

            {/* Auth button (right aligned) */}
            <div className="hidden md:block flex-shrink-0">
              <NewAuthButton
                onAuthChange={handleAuthChange}
                className="bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500 hover:from-blue-600 hover:via-indigo-600 hover:to-purple-600 shadow-lg shadow-indigo-500/20"
              />
            </div>
          </div>
        </nav>
      </header>
      
      {/* Mobile Navigation Menu - extracted to its own component */}
      <MobileNav isOpen={isMenuOpen} onClose={() => setIsMenuOpen(false)} />
      
      {/* Always render FloatingNav but control visibility with isVisible prop */}
      <FloatingNav isVisible={isScrolled} />
      
      {/* Spacer for fixed header - adjusted for safe areas */}
      <div className="h-16 safe-area-pt" /> 
    </>
  );
}