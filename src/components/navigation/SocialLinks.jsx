import React from 'react';
import { motion } from 'framer-motion';
import { SocialIcon } from './SocialIcon';

export default function SocialLinks({ links = [] }) {
  if (!links || links.length === 0) return null;

  return (
    <div className="flex items-center space-x-4">
      {links.map((link, index) => {
        const { name, href, icon, label } = link;
        const displayName = name || label || '';

        return (
          <motion.a
            key={displayName || href}
            href={href}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.98 }}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{
              duration: 0.5,
              delay: index * 0.1,
              type: "spring",
              stiffness: 100,
              damping: 15
            }}
            className="relative group"
            target="_blank"
            rel="noopener noreferrer"
            aria-label={displayName}
          >
            {/* Animated glow effect - smooth, subtle glow with longer duration */}
            <div className="absolute inset-0 bg-gradient-to-tr from-blue-500 via-indigo-500 to-purple-500 rounded-full opacity-0 group-hover:opacity-40 blur-md transition-all duration-500 ease-in-out"></div>

            {/* Icon container */}
            <div className="bg-gradient-to-tr from-blue-500/20 via-indigo-500/20 to-purple-500/20 hover:from-blue-500/30 hover:via-indigo-500/30 hover:to-purple-500/30 p-3 rounded-full relative z-10 backdrop-blur-sm border border-white/10 shadow-lg transition-all duration-500 ease-in-out">
              <div className="text-white/90 group-hover:text-white transition-colors duration-500 ease-in-out">
                {icon ? <SocialIcon icon={icon} href={href} /> : displayName}
              </div>
            </div>
          </motion.a>
        );
      })}
    </div>
  );
}