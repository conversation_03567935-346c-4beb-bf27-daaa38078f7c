import React, { memo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { NAV_ITEMS } from '../../lib/constants/navigation';
import Logo from '../common/Logo';
import AuthButton from '../auth/AuthButton';
import { Link } from 'react-router-dom';

// Memoize the component to prevent unnecessary re-renders
const FloatingNav = memo(({ isVisible = false }) => {
  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ y: -100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: -100, opacity: 0 }}
          transition={{ 
            duration: 0.3, // Reduced duration for better performance
            type: "spring", 
            stiffness: 200, // Lower stiffness for better performance
            damping: 20 
          }}
          className="fixed top-4 left-0 right-0 z-50 w-full px-4"
          style={{ willChange: 'transform, opacity' }} // Optimize for animations
        >
          <div 
            className="
              relative
              max-w-5xl
              mx-auto
              rounded-full
              p-[1px]
              overflow-hidden
              before:absolute
              before:inset-0
              before:bg-gradient-to-r
              before:from-indigo-500/50
              before:via-purple-500/50
              before:to-blue-500/50
              before:blur-sm
            "
            // Removed animate-pulse-soft for better performance
          >
            <div 
              className="
                relative
                rounded-full
                bg-gradient-to-r 
                from-gray-900/90 
                via-indigo-900/30 
                to-purple-900/30
                backdrop-blur-md
                shadow-lg
                flex
                items-center
                justify-between
                px-6
                py-2
                z-10
              "
            >
              {/* Removed floating particles for better performance */}
              
              {/* Logo section - left side */}
              <div className="flex-shrink-0 flex items-center">
                <Logo size="small" className="" animated={false} />
              </div>
              
              {/* Navigation links - centered, only visible on md and up */}
              <div className="hidden md:flex justify-center flex-1 mx-8">
                <div className="flex space-x-8 items-center">
                  {NAV_ITEMS.map(({ name, href }) => (
                    <Link
                      key={name}
                      to={href}
                      className="
                        relative
                        text-sm
                        text-white/80
                        hover:text-white
                        transition-colors
                        group
                      "
                    >
                      {name}
                      
                      {/* Simplified hover effect */}
                      <span 
                        className="
                          absolute 
                          bottom-0 
                          left-0 
                          w-0 
                          h-0.5 
                          bg-blue-400
                          group-hover:w-full 
                          transition-all 
                          duration-200
                        "
                      />
                    </Link>
                  ))}
                </div>
              </div>
              
              {/* Auth button - right side, always visible even on mobile */}
              <div className="flex-shrink-0 ml-auto">
                <AuthButton compact={true} />
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
});

// Add displayName for better debugging
FloatingNav.displayName = 'FloatingNav';

export default FloatingNav;