import React, { memo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { NAV_ITEMS } from '../../lib/constants/navigation';
import Logo from '../common/Logo';
import AuthButton from '../auth/AuthButton';
import { Link } from 'react-router-dom';

// Memoize the component to prevent unnecessary re-renders
const FloatingNav = memo(({ isVisible = false }) => {
  // Only show when isVisible is true, but stay fixed to viewport when shown
  if (!isVisible) {
    return null;
  }

  return (
    <div
      className="fixed top-4 left-0 right-0 w-full px-4 z-50"
      style={{
        zIndex: 9999
      }}
    >
      <div className="relative max-w-4xl mx-auto">
        {/* Floating navigation bar that follows scroll */}
        <div
          className="
            relative
            rounded-full
            bg-gray-900/95
            border border-white/20
            shadow-lg
            flex
            items-center
            justify-between
            px-6
            py-3
            backdrop-blur-sm
          "
          style={{
            background: 'rgba(15, 23, 42, 0.95)',
            backdropFilter: 'blur(8px)',
            WebkitBackdropFilter: 'blur(8px)'
          }}
        >
          {/* Logo section - left side */}
          <div className="flex-shrink-0 flex items-center">
            <Logo size="small" className="" animated={false} />
          </div>

          {/* Navigation links - centered, only visible on md and up */}
          <div className="hidden md:flex justify-center flex-1 mx-8">
            <div className="flex space-x-6 items-center">
              {NAV_ITEMS.map(({ name, href }) => (
                <Link
                  key={name}
                  to={href}
                  className="
                    relative
                    text-sm
                    text-white/80
                    hover:text-white
                    transition-colors
                    duration-200
                    group
                  "
                >
                  {name}

                  {/* Hover effect */}
                  <span
                    className="
                      absolute
                      bottom-0
                      left-0
                      w-0
                      h-0.5
                      bg-blue-400
                      group-hover:w-full
                      transition-all
                      duration-200
                    "
                  />
                </Link>
              ))}
            </div>
          </div>

          {/* Auth button - right side */}
          <div className="flex-shrink-0">
            <AuthButton compact={true} />
          </div>
        </div>
      </div>
    </div>
  );


});

// Add displayName for better debugging
FloatingNav.displayName = 'FloatingNav';

export default FloatingNav;