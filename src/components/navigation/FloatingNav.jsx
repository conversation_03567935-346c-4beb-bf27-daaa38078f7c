import React, { memo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { NAV_ITEMS } from '../../lib/constants/navigation';
import Logo from '../common/Logo';
import AuthButton from '../auth/AuthButton';
import { Link } from 'react-router-dom';

// Memoize the component to prevent unnecessary re-renders
const FloatingNav = memo(({ isVisible = false }) => {
  // Always render for debugging - use inline styles to bypass any CSS conflicts
  return (
    <div
      style={{
        position: 'fixed',
        top: '20px',
        left: '50%',
        transform: 'translateX(-50%)',
        zIndex: 99999,
        backgroundColor: isVisible ? '#ef4444' : '#3b82f6',
        color: 'white',
        padding: '12px 24px',
        borderRadius: '25px',
        fontSize: '14px',
        fontWeight: 'bold',
        boxShadow: '0 4px 12px rgba(0,0,0,0.3)',
        display: 'block',
        width: 'auto',
        minWidth: '200px',
        textAlign: 'center'
      }}
    >
      FLOATING NAV - {isVisible ? 'VISIBLE' : 'HIDDEN'}
    </div>
  );


});

// Add displayName for better debugging
FloatingNav.displayName = 'FloatingNav';

export default FloatingNav;