import React, { memo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { NAV_ITEMS } from '../../lib/constants/navigation';
import Logo from '../common/Logo';
import AuthButton from '../auth/AuthButton';
import { Link } from 'react-router-dom';

// Memoize the component to prevent unnecessary re-renders
const FloatingNav = memo(({ isVisible = false }) => {
  console.log('FloatingNav render - isVisible:', isVisible);

  // Always render for debugging, but with conditional styling
  return (
    <div
      className="fixed top-4 left-0 right-0 z-[100] w-full px-4"
      style={{
        backgroundColor: isVisible ? 'red' : 'blue',
        height: '60px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: 'white',
        fontSize: '18px',
        fontWeight: 'bold',
        opacity: isVisible ? 1 : 0.3
      }}
    >
      FLOATING NAV - isVisible: {isVisible.toString()}
    </div>
  );
});

// Add displayName for better debugging
FloatingNav.displayName = 'FloatingNav';

export default FloatingNav;