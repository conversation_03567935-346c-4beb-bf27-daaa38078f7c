import React, { memo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { NAV_ITEMS } from '../../lib/constants/navigation';
import Logo from '../common/Logo';
import AuthButton from '../auth/AuthButton';
import { Link } from 'react-router-dom';

// Memoize the component to prevent unnecessary re-renders
const FloatingNav = memo(({ isVisible = false }) => {
  // Always render a visible test bar to debug
  return (
    <div
      className="fixed top-4 left-0 right-0 w-full px-4"
      style={{
        zIndex: 9999,
        backgroundColor: isVisible ? 'red' : 'blue',
        height: '60px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: 'white',
        fontSize: '16px',
        fontWeight: 'bold'
      }}
    >
      FLOATING NAV TEST - isVisible: {isVisible ? 'TRUE' : 'FALSE'}
    </div>
  );


});

// Add displayName for better debugging
FloatingNav.displayName = 'FloatingNav';

export default FloatingNav;