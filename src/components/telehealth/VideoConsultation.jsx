import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { Card } from '../common';

export default function VideoConsultation({ providerName, onEnd }) {
  const [isConnecting, setIsConnecting] = useState(true);
  const [isMuted, setIsMuted] = useState(false);
  const [isCameraOff, setIsCameraOff] = useState(false);
  const [isScreenSharing, setIsScreenSharing] = useState(false);
  const [chatOpen, setChatOpen] = useState(false);
  const [chatMessages, setChatMessages] = useState([]);
  const [messageInput, setMessageInput] = useState('');
  const [callDuration, setCallDuration] = useState(0);
  
  // Refs for video elements
  const localVideoRef = useRef(null);
  const remoteVideoRef = useRef(null);
  
  // Timer for call duration
  useEffect(() => {
    const timer = setInterval(() => {
      setCallDuration(prev => prev + 1);
    }, 1000);
    
    // In a real implementation, this would establish the WebRTC connection
    const connectionTimer = setTimeout(() => {
      setIsConnecting(false);
    }, 3000);
    
    return () => {
      clearInterval(timer);
      clearTimeout(connectionTimer);
    };
  }, []);
  
  // Initialize webcam
  useEffect(() => {
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
      navigator.mediaDevices.getUserMedia({ video: true, audio: true })
        .then(stream => {
          if (localVideoRef.current) {
            localVideoRef.current.srcObject = stream;
          }
        })
        .catch(err => {
          console.error("Error accessing media devices:", err);
        });
    }
    
    // Cleanup function to stop all streams when component unmounts
    return () => {
      if (localVideoRef.current && localVideoRef.current.srcObject) {
        const tracks = localVideoRef.current.srcObject.getTracks();
        tracks.forEach(track => track.stop());
      }
    };
  }, []);
  
  // Format seconds into mm:ss
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };
  
  const handleSendMessage = (e) => {
    e.preventDefault();
    if (messageInput.trim()) {
      const newMessage = {
        id: Date.now(),
        sender: 'You',
        text: messageInput,
        time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
      };
      setChatMessages([...chatMessages, newMessage]);
      setMessageInput('');
      
      // In a real implementation, this would send the message via WebRTC data channel or API
    }
  };
  
  const toggleMute = () => {
    setIsMuted(!isMuted);
    // In a real implementation, this would mute the actual audio track
  };
  
  const toggleCamera = () => {
    setIsCameraOff(!isCameraOff);
    // In a real implementation, this would toggle the video track
  };
  
  const toggleScreenShare = () => {
    setIsScreenSharing(!isScreenSharing);
    // In a real implementation, this would start/stop screen sharing
  };
  
  const endCall = () => {
    // Stop any active media streams
    if (localVideoRef.current && localVideoRef.current.srcObject) {
      const tracks = localVideoRef.current.srcObject.getTracks();
      tracks.forEach(track => track.stop());
    }
    
    // Call the onEnd callback
    if (onEnd) onEnd();
  };
  
  return (
    <div className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4">
      <motion.div 
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="w-full max-w-6xl flex flex-col h-full max-h-[90vh]"
      >
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center">
            <div className="bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full h-3 w-3 mr-2"></div>
            <h2 className="text-white text-lg font-semibold">
              {isConnecting ? 'Connecting...' : `Consultation with ${providerName || 'Provider'}`}
            </h2>
          </div>
          <div className="text-white bg-gray-800 px-3 py-1 rounded-full text-sm">
            {formatTime(callDuration)}
          </div>
        </div>
        
        <div className="flex-1 grid grid-cols-1 lg:grid-cols-3 gap-4 h-full">
          {/* Main video area */}
          <div className="lg:col-span-2 flex flex-col">
            <div className="relative flex-1 bg-gray-900 rounded-xl overflow-hidden">
              {isConnecting ? (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500"></div>
                </div>
              ) : (
                <>
                  {/* This would be the remote video feed in a real implementation */}
                  <video 
                    ref={remoteVideoRef}
                    className="w-full h-full object-cover" 
                    autoPlay
                    playsInline
                    muted
                  >
                    Your browser does not support the video tag.
                  </video>
                  
                  {/* Local video (picture-in-picture) */}
                  <div className="absolute bottom-4 right-4 w-48 h-36 bg-black rounded-lg overflow-hidden border-2 border-gray-700 shadow-lg">
                    <video 
                      ref={localVideoRef}
                      className={`w-full h-full object-cover ${isCameraOff ? 'invisible' : ''}`}
                      autoPlay
                      playsInline
                      muted
                    >
                      Your browser does not support the video tag.
                    </video>
                    {isCameraOff && (
                      <div className="absolute inset-0 flex items-center justify-center">
                        <svg className="w-12 h-12 text-white/50" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                      </div>
                    )}
                  </div>
                </>
              )}
            </div>
            
            {/* Controls */}
            <div className="flex justify-center mt-4 space-x-4">
              <button 
                onClick={toggleMute}
                className={`p-3 rounded-full ${isMuted ? 'bg-red-500' : 'bg-gray-700'} hover:bg-opacity-80 transition-colors`}
              >
                <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  {isMuted ? (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                  ) : (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                  )}
                </svg>
              </button>
              
              <button 
                onClick={toggleCamera}
                className={`p-3 rounded-full ${isCameraOff ? 'bg-red-500' : 'bg-gray-700'} hover:bg-opacity-80 transition-colors`}
              >
                <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
              </button>
              
              <button 
                onClick={toggleScreenShare}
                className={`p-3 rounded-full ${isScreenSharing ? 'bg-green-500' : 'bg-gray-700'} hover:bg-opacity-80 transition-colors`}
              >
                <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </button>
              
              <button 
                onClick={() => setChatOpen(!chatOpen)}
                className={`p-3 rounded-full ${chatOpen ? 'bg-blue-500' : 'bg-gray-700'} hover:bg-opacity-80 transition-colors`}
              >
                <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
              </button>
              
              <button 
                onClick={endCall}
                className="p-3 rounded-full bg-red-600 hover:bg-red-700 transition-colors"
              >
                <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 8l2-2m0 0l2-2m-2 2l-2-2m-2 2l2 2M5 3a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 00-1-1.73l-1.66-.83-.16 1.67a2 2 0 01-2 1.89H8.5a2 2 0 01-2-2V7.17a2 2 0 012-1.97H12" />
                </svg>
              </button>
            </div>
          </div>
          
          {/* Chat and info panel */}
          <div className={`lg:col-span-1 ${chatOpen ? 'block' : 'hidden lg:block'}`}>
            <Card className="h-full flex flex-col">
              <div className="p-4 border-b border-gray-700">
                <h3 className="text-lg font-semibold text-white mb-1">Chat</h3>
                <p className="text-sm text-gray-400">Send messages during your consultation</p>
              </div>
              
              <div className="flex-1 overflow-y-auto p-4 space-y-4">
                {chatMessages.length > 0 ? (
                  chatMessages.map(message => (
                    <div 
                      key={message.id}
                      className={`flex ${message.sender === 'You' ? 'justify-end' : 'justify-start'}`}
                    >
                      <div className={`max-w-[80%] rounded-lg p-3 ${
                        message.sender === 'You' 
                          ? 'bg-blue-500 text-white' 
                          : 'bg-gray-700 text-white'
                      }`}>
                        <div className="flex justify-between items-center mb-1">
                          <span className="font-medium text-sm">{message.sender}</span>
                          <span className="text-xs opacity-70">{message.time}</span>
                        </div>
                        <p>{message.text}</p>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center text-gray-500 py-4">
                    No messages yet. Start the conversation!
                  </div>
                )}
              </div>
              
              <div className="p-4 border-t border-gray-700">
                <form onSubmit={handleSendMessage} className="flex">
                  <input
                    type="text"
                    value={messageInput}
                    onChange={(e) => setMessageInput(e.target.value)}
                    placeholder="Type a message..."
                    className="flex-1 bg-gray-800 border border-gray-700 rounded-l-lg py-2 px-3 text-white"
                  />
                  <button 
                    type="submit"
                    className="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-r-lg"
                  >
                    <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                    </svg>
                  </button>
                </form>
              </div>
            </Card>
          </div>
        </div>
      </motion.div>
    </div>
  );
} 