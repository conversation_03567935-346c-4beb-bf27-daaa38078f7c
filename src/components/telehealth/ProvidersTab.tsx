import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

// Mock data
const mockProviders = [
  {
    id: 1,
    name: 'Dr. <PERSON>',
    specialization: 'Orthodontist',
    experience: 8,
    rating: 4.9,
    reviewCount: 127,
    image: 'https://randomuser.me/api/portraits/women/44.jpg',
    availability: ['Mon', 'Wed', 'Fri'],
    nextAvailable: '2023-08-15T10:00:00',
    languages: ['English', 'Spanish'],
    consultationFee: 85,
    acceptingNewPatients: true,
  },
  {
    id: 2,
    name: 'Dr. <PERSON>',
    specialization: 'Periodontics',
    experience: 12,
    rating: 4.8,
    reviewCount: 186,
    image: 'https://randomuser.me/api/portraits/men/45.jpg',
    availability: ['Tue', 'Thu', 'Sat'],
    nextAvailable: '2023-08-16T14:00:00',
    languages: ['English', 'Mandarin'],
    consultationFee: 95,
    acceptingNewPatients: true,
  },
  {
    id: 3,
    name: 'Dr. <PERSON>',
    specialization: 'General Dentistry',
    experience: 5,
    rating: 4.7,
    reviewCount: 93,
    image: 'https://randomuser.me/api/portraits/women/46.jpg',
    availability: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri'],
    nextAvailable: '2023-08-14T09:00:00',
    languages: ['English'],
    consultationFee: 75,
    acceptingNewPatients: true,
  }
];

interface Provider {
  id: number;
  name: string;
  specialization: string;
  experience: number;
  rating: number;
  reviewCount: number;
  image: string;
  availability: string[];
  nextAvailable: string;
  languages: string[];
  consultationFee: number;
  acceptingNewPatients: boolean;
}

const ProvidersTab: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSpecialty, setSelectedSpecialty] = useState('all');
  const [selectedProvider, setSelectedProvider] = useState<Provider | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [showAIMatching, setShowAIMatching] = useState(false);
  
  const filteredProviders = mockProviders.filter(provider => {
    // Filter by specialty
    if (selectedSpecialty !== 'all' && provider.specialization !== selectedSpecialty) {
      return false;
    }
    
    // Filter by search query
    if (searchQuery.trim() !== '') {
      const query = searchQuery.toLowerCase();
      return (
        provider.name.toLowerCase().includes(query) ||
        provider.specialization.toLowerCase().includes(query)
      );
    }
    
    return true;
  });
  
  return (
    <div>
      {/* Header with search and filters */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-8 gap-4">
        <h2 className="text-2xl font-bold text-white">Available Dental Providers</h2>
        
        <div className="flex flex-wrap gap-3">
          <div className="relative">
            <label htmlFor="provider-search" className="sr-only">Search providers</label>
            <input
              id="provider-search"
              name="provider-search"
              type="text"
              placeholder="Search providers..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 pr-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white w-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <svg className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          
          <button
            onClick={() => setShowFilters(true)}
            className="flex items-center px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white hover:bg-white/20 transition-colors"
          >
            <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
            </svg>
            Filters
          </button>
          
          <button
            onClick={() => setShowAIMatching(true)}
            className="flex items-center px-4 py-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg text-white hover:shadow-lg hover:shadow-purple-500/20 transition-all"
          >
            <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
            </svg>
            AI Matching
          </button>
        </div>
      </div>
      
      {/* Specialty quick filters */}
      <div className="flex items-center mb-6 overflow-x-auto py-2 gap-2">
        <button
          onClick={() => setSelectedSpecialty('all')}
          className={`px-4 py-1.5 rounded-full text-sm whitespace-nowrap ${
            selectedSpecialty === 'all'
              ? 'bg-blue-500 text-white'
              : 'bg-white/10 text-blue-100 hover:bg-white/20'
          } transition-colors`}
        >
          All Specialties
        </button>
        <button
          onClick={() => setSelectedSpecialty('General Dentistry')}
          className={`px-4 py-1.5 rounded-full text-sm whitespace-nowrap ${
            selectedSpecialty === 'General Dentistry'
              ? 'bg-blue-500 text-white'
              : 'bg-white/10 text-blue-100 hover:bg-white/20'
          } transition-colors`}
        >
          General Dentistry
        </button>
        <button
          onClick={() => setSelectedSpecialty('Orthodontist')}
          className={`px-4 py-1.5 rounded-full text-sm whitespace-nowrap ${
            selectedSpecialty === 'Orthodontist'
              ? 'bg-blue-500 text-white'
              : 'bg-white/10 text-blue-100 hover:bg-white/20'
          } transition-colors`}
        >
          Orthodontist
        </button>
        <button
          onClick={() => setSelectedSpecialty('Periodontics')}
          className={`px-4 py-1.5 rounded-full text-sm whitespace-nowrap ${
            selectedSpecialty === 'Periodontics'
              ? 'bg-blue-500 text-white'
              : 'bg-white/10 text-blue-100 hover:bg-white/20'
          } transition-colors`}
        >
          Periodontics
        </button>
        <button
          onClick={() => setSelectedSpecialty('Endodontics')}
          className={`px-4 py-1.5 rounded-full text-sm whitespace-nowrap ${
            selectedSpecialty === 'Endodontics'
              ? 'bg-blue-500 text-white'
              : 'bg-white/10 text-blue-100 hover:bg-white/20'
          } transition-colors`}
        >
          Endodontics
        </button>
      </div>
      
      {/* Provider cards */}
      {filteredProviders.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredProviders.map(provider => (
            <motion.div
              key={provider.id}
              whileHover={{ y: -5, boxShadow: '0 10px 25px -5px rgba(59, 130, 246, 0.2)' }}
              className="bg-white/10 backdrop-blur-md rounded-xl overflow-hidden border border-white/10 hover:border-blue-400/30 transition-all cursor-pointer"
              onClick={() => setSelectedProvider(provider)}
            >
              <div className="p-6">
                <div className="flex items-center">
                  <img 
                    src={provider.image} 
                    alt={provider.name} 
                    className="w-16 h-16 rounded-full object-cover border-2 border-white/20"
                  />
                  <div className="ml-4">
                    <h4 className="font-semibold text-white">{provider.name}</h4>
                    <p className="text-blue-200 text-sm">{provider.specialization}</p>
                    <div className="flex items-center mt-1">
                      <div className="flex">
                        {[1, 2, 3, 4, 5].map(star => (
                          <svg 
                            key={star} 
                            className={`w-4 h-4 ${
                              star <= Math.floor(provider.rating) 
                                ? 'text-yellow-400' 
                                : star <= provider.rating 
                                  ? 'text-yellow-400/50' 
                                  : 'text-gray-400'
                            }`}
                            fill="currentColor" 
                            viewBox="0 0 20 20"
                          >
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                          </svg>
                        ))}
                      </div>
                      <span className="ml-1 text-xs text-blue-200">({provider.reviewCount})</span>
                    </div>
                  </div>
                </div>
                
                <div className="mt-4 grid grid-cols-2 gap-3 text-sm">
                  <div className="flex items-center text-blue-200">
                    <svg className="w-4 h-4 mr-1 text-blue-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    {new Date(provider.nextAvailable).toLocaleDateString()}
                  </div>
                  <div className="flex items-center text-blue-200">
                    <svg className="w-4 h-4 mr-1 text-blue-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    {new Date(provider.nextAvailable).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </div>
                  <div className="flex items-center text-blue-200">
                    <svg className="w-4 h-4 mr-1 text-blue-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
                    </svg>
                    {provider.languages.join(', ')}
                  </div>
                  <div className="flex items-center text-blue-200">
                    <svg className="w-4 h-4 mr-1 text-blue-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    ${provider.consultationFee}
                  </div>
                </div>
              </div>
              
              <div className="bg-gradient-to-r from-blue-500/20 to-indigo-500/20 py-3 px-4 flex justify-between items-center">
                <span className="text-sm text-blue-100">
                  {provider.experience} Years Experience
                </span>
                <button className="px-3 py-1 bg-blue-500 hover:bg-blue-600 text-white text-sm rounded-lg transition-colors">
                  Book Now
                </button>
              </div>
            </motion.div>
          ))}
        </div>
      ) : (
        <div className="bg-white/5 border border-white/10 rounded-xl p-8 text-center">
          <div className="mx-auto w-16 h-16 bg-blue-900/30 rounded-full flex items-center justify-center mb-4">
            <svg className="w-8 h-8 text-blue-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
          </div>
          <h4 className="text-xl font-semibold text-white mb-2">No providers found</h4>
          <p className="text-blue-200 mb-4 max-w-md mx-auto">
            No dental providers match your current search criteria. Try adjusting your filters or try our AI matching.
          </p>
          <button 
            onClick={() => setShowAIMatching(true)}
            className="px-5 py-2.5 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-lg hover:shadow-lg transition-all"
          >
            Try AI Matching
          </button>
        </div>
      )}
      
      {/* Overlays for Filters, AI Matching, and Provider Details will go here */}
      
    </div>
  );
};

export default ProvidersTab; 