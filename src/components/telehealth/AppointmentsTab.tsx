import React, { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

// Mock data for upcoming appointments
const mockUpcomingAppointments = [
  {
    id: 1,
    doctorName: 'Dr. <PERSON>',
    specialization: 'Orthodontist',
    date: '2023-08-15',
    time: '10:00 AM',
    duration: 30,
    image: 'https://randomuser.me/api/portraits/women/44.jpg',
    virtualRoom: 'dental-ortho-455',
    preConsultationRequired: false
  },
  // Empty for now to show the empty state
];

interface Appointment {
  id: number;
  doctorName: string;
  specialization: string;
  date: string;
  time: string;
  duration: number;
  image: string;
  virtualRoom: string;
  preConsultationRequired: boolean;
}

const AppointmentsTab: React.FC = () => {
  const [showScheduleForm, setShowScheduleForm] = useState(false);
  const [showAIRecommendation, setShowAIRecommendation] = useState(false);
  const [showARPreview, setShowARPreview] = useState(false);
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<string | null>(null);
  const [selectedAppointment, setSelectedAppointment] = useState<Appointment | null>(null);
  const [showSymptomScanner, setShowSymptomScanner] = useState(false);
  
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const handleSchedule = () => {
    setShowScheduleForm(true);
    // Close other overlays
    setShowARPreview(false);
    setShowAIRecommendation(false);
  };
  
  const handleAIRecommend = () => {
    setShowAIRecommendation(true);
    setShowARPreview(false);
  };
  
  const handleARPreview = () => {
    setShowARPreview(true);
    setShowAIRecommendation(false);
  };
  
  const handleFileUpload = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };
  
  return (
    <div>
      {/* Header with actions */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
        <h2 className="text-2xl font-bold text-white">My Appointments</h2>
        
        <div className="flex flex-wrap gap-3">
          <button 
            onClick={handleSchedule}
            className="flex items-center px-4 py-2 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-lg text-white hover:shadow-lg hover:shadow-blue-500/20 transition-all"
          >
            <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Schedule Consultation
          </button>
          
          <button
            onClick={handleAIRecommend}
            className="flex items-center px-4 py-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg text-white hover:shadow-lg hover:shadow-purple-500/20 transition-all"
          >
            <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            AI Recommendation
          </button>
          
          <button
            onClick={handleARPreview}
            className="flex items-center px-4 py-2 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-lg text-white hover:shadow-lg hover:shadow-emerald-500/20 transition-all"
          >
            <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            AR Preview
          </button>
        </div>
      </div>
      
      {/* Upload input (hidden) */}
      <label htmlFor="symptom-scan-upload" className="sr-only">Upload symptom scan image</label>
      <input 
        id="symptom-scan-upload"
        name="symptom-scan-upload"
        type="file" 
        ref={fileInputRef} 
        className="hidden" 
        accept="image/*" 
        onChange={() => setShowSymptomScanner(true)} 
      />
      
      {/* Main content area */}
      <div className="space-y-8">
        {/* Upcoming appointments section */}
        <div>
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-xl font-semibold text-white">Upcoming Consultations</h3>
            <div>
              <button 
                onClick={handleFileUpload}
                className="flex items-center text-sm px-3 py-1.5 bg-white/10 hover:bg-white/20 rounded-lg text-blue-100 transition-colors"
              >
                <svg className="w-4 h-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                Scan Symptoms
              </button>
            </div>
          </div>
          
          {mockUpcomingAppointments.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {mockUpcomingAppointments.map(appointment => (
                <motion.div
                  key={appointment.id}
                  whileHover={{ scale: 1.02 }}
                  className="bg-white/10 backdrop-blur-md rounded-xl overflow-hidden border border-white/10 hover:border-blue-400/50 transition-colors cursor-pointer"
                  onClick={() => setSelectedAppointment(appointment)}
                >
                  <div className="flex items-start p-4">
                    <img 
                      src={appointment.image} 
                      alt={appointment.doctorName} 
                      className="w-16 h-16 rounded-full object-cover border-2 border-white/20"
                    />
                    <div className="ml-4 flex-1">
                      <h4 className="font-semibold text-white">{appointment.doctorName}</h4>
                      <p className="text-blue-200 text-sm">{appointment.specialization}</p>
                      <div className="flex items-center mt-3 text-sm">
                        <div className="flex items-center text-emerald-300 mr-4">
                          <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                          {new Date(appointment.date).toLocaleDateString()}
                        </div>
                        <div className="flex items-center text-emerald-300">
                          <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          {appointment.time}
                        </div>
                      </div>
                    </div>
                    <div>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Ready
                      </span>
                    </div>
                  </div>
                  <div className="bg-gradient-to-r from-blue-500/20 to-indigo-500/20 py-3 px-4 flex justify-between items-center">
                    <span className="text-sm text-blue-100">Virtual Room: {appointment.virtualRoom}</span>
                    <button className="px-3 py-1 bg-blue-500 hover:bg-blue-600 text-white text-sm rounded-lg transition-colors">
                      Join Now
                    </button>
                  </div>
                </motion.div>
              ))}
            </div>
          ) : (
            <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-8">
              <div className="text-center">
                <div className="mx-auto w-16 h-16 bg-blue-900/30 rounded-full flex items-center justify-center mb-4">
                  <svg className="w-8 h-8 text-blue-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
                <h4 className="text-xl font-semibold text-white mb-2">No upcoming consultations</h4>
                <p className="text-blue-200 mb-6 max-w-md mx-auto">
                  You don't have any dental consultations scheduled. Book a virtual appointment with a dental professional.
                </p>
                <button 
                  onClick={handleSchedule}
                  className="px-5 py-2.5 bg-gradient-to-r from-blue-500 to-indigo-500 text-white rounded-lg hover:shadow-lg hover:shadow-blue-500/20 transition-all"
                >
                  Schedule Your First Consultation
                </button>
              </div>
            </div>
          )}
        </div>
        
        {/* Reminder about pre-consultation form */}
        <div className="bg-gradient-to-r from-amber-500/20 to-yellow-500/20 rounded-xl p-4 flex items-start">
          <div className="bg-amber-400/20 rounded-full p-2 mr-4">
            <svg className="w-6 h-6 text-amber-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          </div>
          <div>
            <h4 className="font-medium text-white mb-1">Pre-Consultation Form</h4>
            <p className="text-blue-100 text-sm">
              For the best experience, please fill out the pre-consultation questionnaire at least 24 hours before your appointment.
              This helps your dental provider prepare for your specific needs.
            </p>
          </div>
        </div>
      </div>
      
      {/* Schedule Consultation Form (Overlay) */}
      <AnimatePresence>
        {showScheduleForm && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-gray-900 border border-gray-800 rounded-2xl p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto"
            >
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-2xl font-bold text-white">Schedule New Consultation</h3>
                <button 
                  onClick={() => setShowScheduleForm(false)}
                  className="text-gray-400 hover:text-white"
                >
                  <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              
              <form className="space-y-6">
                {/* Form content will go here */}
                <p>Interactive scheduling form will be implemented here</p>
                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => setShowScheduleForm(false)}
                    className="px-4 py-2 text-gray-300 hover:text-white"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-gradient-to-r from-blue-500 to-indigo-500 text-white rounded-lg hover:shadow-lg transition-all"
                  >
                    Schedule Appointment
                  </button>
                </div>
              </form>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* AI Recommendation (Overlay) */}
      <AnimatePresence>
        {showAIRecommendation && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-gray-900 border border-gray-800 rounded-2xl p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto"
            >
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-2xl font-bold text-white">AI Consultation Recommendation</h3>
                <button 
                  onClick={() => setShowAIRecommendation(false)}
                  className="text-gray-400 hover:text-white"
                >
                  <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              
              <div className="space-y-6">
                {/* AI recommendation content */}
                <p className="text-blue-200">
                  Based on your dental history and recent interactions, our AI recommends:
                </p>
                <div className="p-4 bg-gradient-to-r from-purple-900/30 to-pink-900/30 rounded-xl border border-purple-500/20">
                  <p>AI recommendation content will be displayed here</p>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* AR Preview (Overlay) */}
      <AnimatePresence>
        {showARPreview && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-gray-900 border border-gray-800 rounded-2xl p-6 max-w-4xl w-full max-h-[90vh] overflow-y-auto"
            >
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-2xl font-bold text-white">AR Teeth Visualization</h3>
                <button 
                  onClick={() => setShowARPreview(false)}
                  className="text-gray-400 hover:text-white"
                >
                  <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              
              <div className="space-y-6">
                {/* AR preview content */}
                <p className="text-blue-200">
                  Explore a 3D model of your teeth and potential treatment outcomes.
                </p>
                <div className="aspect-video bg-black/50 rounded-xl flex items-center justify-center">
                  <p className="text-gray-400">3D Visualization would appear here</p>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* Symptom Scanner (Overlay) */}
      <AnimatePresence>
        {showSymptomScanner && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-gray-900 border border-gray-800 rounded-2xl p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto"
            >
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-2xl font-bold text-white">Symptom Scanner</h3>
                <button 
                  onClick={() => setShowSymptomScanner(false)}
                  className="text-gray-400 hover:text-white"
                >
                  <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              
              <div className="space-y-6">
                <p className="text-blue-200">
                  Our AI will analyze your dental image to identify potential issues.
                </p>
                <div className="p-4 bg-black/50 rounded-xl border border-gray-700">
                  <p>Symptom scanner content would appear here</p>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default AppointmentsTab; 