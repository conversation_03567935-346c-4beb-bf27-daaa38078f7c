import React, { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';

// Import our tab components
import AppointmentsTab from './AppointmentsTab';
import ProvidersTab from './ProvidersTab';

// Component imports would go here in a real implementation
// import ConsultationAI from './ConsultationAI';
// import ARTeethScanner from './ARTeethScanner';

const EnhancedVirtualConsultations: React.FC = () => {
  // State for active tab
  const [activeTab, setActiveTab] = useState<string>('appointments');
  
  // State for 3D teeth model
  const [showTeethModel, setShowTeethModel] = useState<boolean>(false);
  
  // State for AI assistant
  const [aiAssistantActive, setAiAssistantActive] = useState<boolean>(false);
  
  // State for appointment selection
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<string | null>(null);
  
  // Function to render tab content based on active tab
  const renderTabContent = () => {
    switch (activeTab) {
      case 'appointments':
        return <AppointmentsTab />;
      case 'providers':
        return <ProvidersTab />;
      case 'history':
        return <HistoryTab />;
      case 'settings':
        return <SettingsTab />;
      default:
        return <AppointmentsTab />;
    }
  };
  
  return (
    <div className="min-h-screen bg-gradient-to-b from-indigo-900 via-blue-900 to-indigo-800 text-white">
      <header className="py-12 px-6 md:px-12">
        <motion.h1 
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-5xl md:text-6xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-300 to-purple-400 text-center"
        >
          Virtual Dental Consultations
        </motion.h1>
        <motion.p 
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="text-center text-blue-200 max-w-3xl mx-auto mt-4 text-lg"
        >
          Connect with dental professionals from the comfort of your home. Get expert
          advice, follow-ups, and preliminary diagnoses through secure video
          consultations.
        </motion.p>
      </header>
      
      <main className="px-6 md:px-12 pb-20">
        {/* Main container with glass-morphism effect */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="max-w-7xl mx-auto rounded-2xl overflow-hidden backdrop-blur-lg bg-white/10 shadow-2xl border border-white/20"
        >
          {/* Floating AI assistant toggle */}
          <button
            onClick={() => setAiAssistantActive(!aiAssistantActive)}
            className="fixed bottom-6 right-6 z-50 flex items-center justify-center w-14 h-14 rounded-full bg-gradient-to-r from-indigo-600 to-blue-500 shadow-lg hover:shadow-blue-500/30 transition-all duration-300 group"
          >
            <svg 
              className="w-7 h-7 text-white" 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={1.5} 
                d="M9.75 3.104v5.714a2.25 2.25 0 01-.659 1.591L5 14.5M9.75 3.104c-.251.023-.501.05-.75.082m.75-.082a24.301 24.301 0 014.5 0m0 0v5.714a2.25 2.25 0 01-.659 1.591L9.5 14.5m3.75-11.396c.251.023.501.05.75.082m-1.524 16.457A8.949 8.949 0 0112 15.75v-1.643A4.5 4.5 0 10 7.5a4.5 4.5 0 004.5 4.5H15a7 7 0 01-1.5-0.859" 
              />
            </svg>
            <span className="absolute right-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap bg-gray-900 text-white text-sm py-1 px-2 rounded">
              AI Dental Assistant
            </span>
          </button>
          
          {/* Navigation Tabs */}
          <div className="flex overflow-x-auto scrollbar-hide border-b border-white/20">
            <TabButton 
              label="My Appointments"
              icon="calendar"
              isActive={activeTab === 'appointments'}
              onClick={() => setActiveTab('appointments')}
            />
            <TabButton 
              label="Providers"
              icon="users"
              isActive={activeTab === 'providers'}
              onClick={() => setActiveTab('providers')}
            />
            <TabButton 
              label="History"
              icon="clock"
              isActive={activeTab === 'history'}
              onClick={() => setActiveTab('history')}
            />
            <TabButton 
              label="Settings"
              icon="settings"
              isActive={activeTab === 'settings'}
              onClick={() => setActiveTab('settings')}
            />
          </div>
          
          {/* Tab Content Area */}
          <div className="p-6 md:p-8">
            {renderTabContent()}
          </div>
        </motion.div>
      </main>
    </div>
  );
};

// TabButton component for navigation
interface TabButtonProps {
  label: string;
  icon: string;
  isActive: boolean;
  onClick: () => void;
}

const TabButton: React.FC<TabButtonProps> = ({ label, icon, isActive, onClick }) => {
  const renderIcon = () => {
    switch (icon) {
      case 'calendar':
        return (
          <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
        );
      case 'users':
        return (
          <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
        );
      case 'clock':
        return (
          <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      case 'settings':
        return (
          <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        );
      default:
        return null;
    }
  };
  
  return (
    <button
      onClick={onClick}
      className={`flex items-center px-6 py-4 focus:outline-none transition-all ${
        isActive 
          ? 'bg-white/20 text-white border-b-2 border-cyan-400' 
          : 'text-blue-100 hover:bg-white/10 border-b-2 border-transparent'
      }`}
    >
      <span className="mr-2">{renderIcon()}</span>
      <span className="font-medium">{label}</span>
      {isActive && (
        <motion.span
          layoutId="activeTabIndicator"
          className="absolute bottom-0 left-0 right-0 h-0.5 bg-cyan-400"
        />
      )}
    </button>
  );
};

// Simple placeholder components for other tabs
const HistoryTab: React.FC = () => (
  <div className="bg-white/5 border border-white/10 rounded-xl p-8 text-center">
    <div className="mx-auto w-16 h-16 bg-blue-900/30 rounded-full flex items-center justify-center mb-4">
      <svg className="w-8 h-8 text-blue-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    </div>
    <h4 className="text-xl font-semibold text-white mb-2">Consultation History</h4>
    <p className="text-blue-200 mb-4 max-w-md mx-auto">
      Your past consultations and dental visit records will appear here.
    </p>
  </div>
);

const SettingsTab: React.FC = () => (
  <div className="bg-white/5 border border-white/10 rounded-xl p-8 text-center">
    <div className="mx-auto w-16 h-16 bg-blue-900/30 rounded-full flex items-center justify-center mb-4">
      <svg className="w-8 h-8 text-blue-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
      </svg>
    </div>
    <h4 className="text-xl font-semibold text-white mb-2">Telehealth Settings</h4>
    <p className="text-blue-200 mb-4 max-w-md mx-auto">
      Configure your notification preferences, appointment defaults, and video settings.
    </p>
  </div>
);

export default EnhancedVirtualConsultations; 