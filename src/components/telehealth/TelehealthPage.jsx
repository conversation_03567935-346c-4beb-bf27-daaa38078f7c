import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Card } from '../common';

function AppointmentsSection() {
  const [showScheduleForm, setShowScheduleForm] = useState(false);
  const upcomingAppointments = [];
  
  return (
    <div>
      <div className="flex justify-between items-center mb-8">
        <h2 className="text-2xl font-bold text-white">My Appointments</h2>
        <button 
          onClick={() => setShowScheduleForm(!showScheduleForm)}
          className="bg-gradient-to-r from-blue-500 to-indigo-500 text-white py-2 px-4 rounded-lg hover:shadow-lg transition-all"
        >
          {showScheduleForm ? 'Cancel' : 'Schedule Consultation'}
        </button>
      </div>
      
      {showScheduleForm ? (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="mb-8"
        >
          <Card className="p-6 mb-8 border border-gray-700">
            <h3 className="text-xl font-bold mb-4 text-white">Schedule New Consultation</h3>
            <form className="space-y-4">
              <div>
                <label htmlFor="consultation-type" className="block text-gray-300 mb-2">Consultation Type</label>
                <select id="consultation-type" name="consultation-type" className="w-full p-3 bg-gray-800 border border-gray-700 rounded-lg text-white">
                  <option>General Dental Consultation</option>
                  <option>Orthodontic Consultation</option>
                  <option>Cosmetic Dentistry Consultation</option>
                  <option>Emergency Dental Advice</option>
                </select>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="preferred-date" className="block text-gray-300 mb-2">Preferred Date</label>
                  <input id="preferred-date" name="preferred-date" type="date" className="w-full p-3 bg-gray-800 border border-gray-700 rounded-lg text-white" />
                </div>
                <div>
                  <label htmlFor="preferred-time" className="block text-gray-300 mb-2">Preferred Time</label>
                  <select id="preferred-time" name="preferred-time" className="w-full p-3 bg-gray-800 border border-gray-700 rounded-lg text-white">
                    <option>Morning (9AM - 12PM)</option>
                    <option>Afternoon (12PM - 5PM)</option>
                    <option>Evening (5PM - 8PM)</option>
                  </select>
                </div>
              </div>
              
              <div>
                <label htmlFor="concern-description" className="block text-gray-300 mb-2">Brief Description of Your Concern</label>
                <textarea 
                  id="concern-description" 
                  name="concern-description" 
                  rows="3" 
                  className="w-full p-3 bg-gray-800 border border-gray-700 rounded-lg text-white" 
                  placeholder="Please describe your dental concern or reason for consultation"></textarea>
              </div>
              
              <div className="flex justify-end mt-6">
                <button 
                  type="submit"
                  className="bg-gradient-to-r from-blue-500 to-indigo-500 text-white py-2 px-6 rounded-lg hover:shadow-lg transition-all"
                >
                  Request Appointment
                </button>
              </div>
            </form>
          </Card>
        </motion.div>
      ) : null}
      
      <h3 className="text-xl font-semibold text-white mb-4">Upcoming Consultations</h3>
      
      {upcomingAppointments.length > 0 ? (
        <div className="space-y-4">
          {/* Appointments will be mapped here from real data */}
        </div>
      ) : (
        <Card className="p-6 text-center border border-gray-700">
          <p className="text-gray-400 mb-4">You have no upcoming consultations scheduled.</p>
          <button 
            onClick={() => setShowScheduleForm(true)}
            className="bg-gradient-to-r from-blue-500 to-indigo-500 text-white py-2 px-4 rounded-lg hover:shadow-lg transition-all"
          >
            Schedule Your First Consultation
          </button>
        </Card>
      )}
    </div>
  );
}

function ProvidersSection() {
  const providers = [];
  
  return (
    <div>
      <div className="flex justify-between items-center mb-8">
        <h2 className="text-2xl font-bold text-white">Available Dental Providers</h2>
        <div className="relative">
          <input 
            id="provider-search"
            name="provider-search"
            type="text" 
            placeholder="Search providers..." 
            className="p-2 pl-8 bg-gray-800 border border-gray-700 rounded-lg text-white w-48"
          />
          <svg className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
      </div>
      
      {providers.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Providers will be mapped here from real data */}
        </div>
      ) : (
        <Card className="p-6 text-center border border-gray-700">
          <p className="text-gray-400">No providers are currently available. Please check back later.</p>
        </Card>
      )}
    </div>
  );
}

function HistorySection() {
  const pastConsultations = [];
  
  return (
    <div>
      <h2 className="text-2xl font-bold text-white mb-8">Consultation History</h2>
      
      {pastConsultations.length > 0 ? (
        <div className="space-y-4">
          {/* Past consultations will be mapped here from real data */}
        </div>
      ) : (
        <Card className="p-6 text-center border border-gray-700">
          <p className="text-gray-400">You have no past consultations on record.</p>
        </Card>
      )}
    </div>
  );
}

function SettingsSection() {
  return (
    <div>
      <h2 className="text-2xl font-bold text-white mb-8">Telehealth Settings</h2>
      
      <div className="space-y-8">
        <Card className="p-4 border border-gray-700">
          <h3 className="text-lg font-semibold text-white mb-4">Notification Preferences</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <label htmlFor="email-reminders" className="text-gray-300">Email reminders</label>
              <div className="relative inline-block w-12 h-6 rounded-full bg-gray-700">
                <input id="email-reminders" name="email-reminders" type="checkbox" className="peer sr-only" defaultChecked />
                <span className="absolute cursor-pointer inset-0 rounded-full transition duration-300 peer-checked:bg-blue-500"></span>
                <span className="absolute cursor-pointer left-1 top-1 w-4 h-4 rounded-full bg-white transition-all duration-300 peer-checked:left-7"></span>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <label htmlFor="sms-notifications" className="text-gray-300">SMS notifications</label>
              <div className="relative inline-block w-12 h-6 rounded-full bg-gray-700">
                <input id="sms-notifications" name="sms-notifications" type="checkbox" className="peer sr-only" defaultChecked />
                <span className="absolute cursor-pointer inset-0 rounded-full transition duration-300 peer-checked:bg-blue-500"></span>
                <span className="absolute cursor-pointer left-1 top-1 w-4 h-4 rounded-full bg-white transition-all duration-300 peer-checked:left-7"></span>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <label htmlFor="calendar-integration" className="text-gray-300">Appointment calendar integration</label>
              <div className="relative inline-block w-12 h-6 rounded-full bg-gray-700">
                <input id="calendar-integration" name="calendar-integration" type="checkbox" className="peer sr-only" />
                <span className="absolute cursor-pointer inset-0 rounded-full transition duration-300 peer-checked:bg-blue-500"></span>
                <span className="absolute cursor-pointer left-1 top-1 w-4 h-4 rounded-full bg-white transition-all duration-300 peer-checked:left-7"></span>
              </div>
            </div>
          </div>
        </Card>
        
        <Card className="p-4 border border-gray-700">
          <h3 className="text-lg font-semibold text-white mb-4">Appointment Defaults</h3>
          <div className="space-y-4">
            <div>
              <label htmlFor="consultation-length" className="block text-gray-300 mb-2">Preferred Consultation Length</label>
              <select id="consultation-length" name="consultation-length" className="w-full p-3 bg-gray-800 border border-gray-700 rounded-lg text-white">
                <option>15 minutes</option>
                <option selected>30 minutes</option>
                <option>45 minutes</option>
                <option>60 minutes</option>
              </select>
            </div>
            
            <div>
              <label htmlFor="reminder-time" className="block text-gray-300 mb-2">Default Reminder Time</label>
              <select id="reminder-time" name="reminder-time" className="w-full p-3 bg-gray-800 border border-gray-700 rounded-lg text-white">
                <option>1 hour before</option>
                <option selected>24 hours before</option>
                <option>48 hours before</option>
              </select>
            </div>
          </div>
        </Card>
        
        <Card className="p-4 border border-gray-700">
          <h3 className="text-lg font-semibold text-white mb-4">Video Consultation Settings</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <label htmlFor="camera-on" className="text-gray-300">Auto-join with camera on</label>
              <div className="relative inline-block w-12 h-6 rounded-full bg-gray-700">
                <input id="camera-on" name="camera-on" type="checkbox" className="peer sr-only" defaultChecked />
                <span className="absolute cursor-pointer inset-0 rounded-full transition duration-300 peer-checked:bg-blue-500"></span>
                <span className="absolute cursor-pointer left-1 top-1 w-4 h-4 rounded-full bg-white transition-all duration-300 peer-checked:left-7"></span>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <label htmlFor="microphone-on" className="text-gray-300">Auto-join with microphone on</label>
              <div className="relative inline-block w-12 h-6 rounded-full bg-gray-700">
                <input id="microphone-on" name="microphone-on" type="checkbox" className="peer sr-only" defaultChecked />
                <span className="absolute cursor-pointer inset-0 rounded-full transition duration-300 peer-checked:bg-blue-500"></span>
                <span className="absolute cursor-pointer left-1 top-1 w-4 h-4 rounded-full bg-white transition-all duration-300 peer-checked:left-7"></span>
              </div>
            </div>
            
            <div>
              <label htmlFor="video-quality" className="block text-gray-300 mb-2">Preferred Video Quality</label>
              <select id="video-quality" name="video-quality" className="w-full p-3 bg-gray-800 border border-gray-700 rounded-lg text-white">
                <option>Low (save data)</option>
                <option selected>Medium</option>
                <option>High (better quality)</option>
              </select>
            </div>
          </div>
        </Card>
        
        <div className="flex justify-end">
          <button className="bg-gradient-to-r from-blue-500 to-indigo-500 text-white py-2 px-6 rounded-lg hover:shadow-lg transition-all">
            Save Settings
          </button>
        </div>
      </div>
    </div>
  );
}

export default function TelehealthPage() {
  const [activeTab, setActiveTab] = useState('appointments');
  
  return (
    <div className="container mx-auto px-4 py-16 max-w-7xl">
      {/* Hero Section */}
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-center mb-16"
      >
        <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
          <span className="bg-gradient-to-r from-blue-400 via-indigo-400 to-purple-400 text-transparent bg-clip-text">
            Virtual Dental Consultations
          </span>
        </h1>
        <p className="text-xl text-white/70 max-w-3xl mx-auto">
          Connect with dental professionals from the comfort of your home. 
          Get expert advice, follow-ups, and preliminary diagnoses through secure video consultations.
        </p>
      </motion.div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Navigation Sidebar */}
        <div className="lg:col-span-1">
          <Card className="sticky top-24 overflow-hidden">
            <nav className="p-6">
              <h3 className="text-2xl font-bold mb-6 text-white">Telehealth Services</h3>
              <ul className="space-y-2">
                {['appointments', 'providers', 'history', 'settings'].map((tab) => (
                  <li key={tab}>
                    <button
                      onClick={() => setActiveTab(tab)}
                      className={`w-full text-left py-3 px-4 rounded-lg transition-all ${
                        activeTab === tab
                          ? 'bg-gradient-to-r from-blue-500 to-indigo-500 text-white'
                          : 'text-gray-300 hover:bg-gray-800'
                      }`}
                    >
                      <span className="capitalize">{tab}</span>
                    </button>
                  </li>
                ))}
              </ul>
            </nav>
          </Card>
        </div>
        
        {/* Content Area */}
        <div className="lg:col-span-2">
          <Card className="p-6">
            {activeTab === 'appointments' && <AppointmentsSection />}
            {activeTab === 'providers' && <ProvidersSection />}
            {activeTab === 'history' && <HistorySection />}
            {activeTab === 'settings' && <SettingsSection />}
          </Card>
        </div>
      </div>
    </div>
  );
} 