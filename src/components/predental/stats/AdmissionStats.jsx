import React, { useState } from 'react';
import { Card } from '../../common';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON><PERSON>, <PERSON>, Cell } from 'recharts';

const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];

const averageStats = {
  overall: {
    gpa: 3.57,
    scienceGpa: 3.53,
    datAA: 20.4,
    datPAT: 20.8,
    totalApplicants: 12500,
    acceptanceRate: "55%"
  },
  datBreakdown: [
    { section: 'Biology', score: 20.3 },
    { section: 'General Chemistry', score: 20.5 },
    { section: 'Organic Chemistry', score: 20.2 },
    { section: 'PAT', score: 20.8 },
    { section: 'Reading', score: 20.6 },
    { section: 'QR', score: 20.1 }
  ],
  experienceHours: [
    { name: 'Shadowing', hours: 100, recommended: 150 },
    { name: 'Volunteering', hours: 150, recommended: 200 },
    { name: 'Research', hours: 50, recommended: 100 },
    { name: 'Leadership', hours: 75, recommended: 100 }
  ],
  gpaRanges: [
    { range: '3.8-4.0', percentage: 35 },
    { range: '3.6-3.79', percentage: 30 },
    { range: '3.4-3.59', percentage: 20 },
    { range: '3.2-3.39', percentage: 10 },
    { range: '< 3.2', percentage: 5 }
  ],
  datRanges: [
    { range: '23-30', percentage: 15 },
    { range: '21-22', percentage: 30 },
    { range: '19-20', percentage: 35 },
    { range: '17-18', percentage: 15 },
    { range: '< 17', percentage: 5 }
  ]
};

const successStories = [
  {
    name: "Dr. James Wilson",
    school: "University of Michigan School of Dentistry",
    stats: { gpa: 3.2, dat: 19 },
    video: "https://www.youtube.com/watch?v=example4",
    quote: "There's no testimony without a test. I failed my first DAT attempt with a 16 AA, but I didn't give up. I studied harder, improved my application, and eventually got accepted.",
    background: "Failed first DAT attempt, worked full-time while retaking prerequisites to improve GPA",
    tips: [
      "Don't let initial failures define you",
      "Focus on showing improvement over time",
      "Use setbacks as motivation"
    ],
    journey: "After getting a 2.8 GPA in my first two years, I retook key science courses while working full-time. It took me 5 years to finish undergrad, but I showed consistent improvement."
  },
  {
    name: "Dr. Maria Gonzalez",
    school: "Rutgers School of Dental Medicine",
    stats: { gpa: 3.3, dat: 18 },
    video: "https://www.youtube.com/watch?v=example5",
    quote: "Your stats don't define your potential. I made up for lower numbers with extensive community service and a compelling personal story.",
    background: "ESL student, worked as dental assistant for 3 years before acceptance",
    tips: [
      "Get hands-on dental experience",
      "Build strong relationships with dentists for letters",
      "Highlight your unique perspective"
    ],
    journey: "English is my second language, which affected my DAT reading scores. I focused on gaining real-world experience and showing my passion for serving underserved communities."
  },
  {
    name: "Dr. David Park",
    school: "University of Illinois Chicago College of Dentistry",
    stats: { gpa: 3.4, dat: 17 },
    video: "https://www.youtube.com/watch?v=example6",
    quote: "I was rejected from 8 schools before getting accepted. Each interview made me stronger, and I learned to better tell my story.",
    background: "Career changer from finance, completed post-bacc program",
    tips: [
      "Be honest about your journey",
      "Show growth from challenges",
      "Practice interviewing extensively"
    ],
    journey: "After 3 years in finance, I realized dentistry was my true calling. My non-traditional path and life experience helped me stand out despite lower stats."
  },
  {
    name: "Dr. Sarah Chen",
    school: "Harvard School of Dental Medicine",
    stats: { gpa: 3.85, dat: 23 },
    video: "https://www.youtube.com/watch?v=example1",
    quote: "Focus on consistency and don't let setbacks discourage you. Every hour of study counts!",
    background: "First-generation college student, worked part-time throughout undergrad",
    tips: [
      "Start DAT prep early",
      "Focus on understanding concepts, not memorization",
      "Get involved in research early"
    ],
    journey: "As a first-generation student, I had to figure out a lot on my own. I worked 20 hours per week while maintaining my studies and still achieved strong results."
  },
  {
    name: "Dr. Michael Rodriguez",
    school: "UCLA School of Dentistry",
    stats: { gpa: 3.6, dat: 21 },
    video: "https://www.youtube.com/watch?v=example2",
    quote: "It's not just about the numbers. Show your passion for dentistry through experiences.",
    background: "Non-traditional student, switched from business to pre-dental",
    tips: [
      "Quality of shadowing matters more than quantity",
      "Stay organized with application materials",
      "Practice interview skills early"
    ],
    journey: "I spent two years in business before realizing my true passion was dentistry. The transition was challenging, but my business background actually helped me stand out."
  },
  {
    name: "Dr. Emily Patel",
    school: "UPenn School of Dental Medicine",
    stats: { gpa: 3.7, dat: 22 },
    video: "https://www.youtube.com/watch?v=example3",
    quote: "Balance is key. Make time for both academics and experiences that showcase your personality.",
    background: "Division I athlete, dental mission trips to South America",
    tips: [
      "Find unique ways to stand out",
      "Build relationships with professors",
      "Start personal statement early"
    ],
    journey: "Balancing Division I athletics with pre-dental coursework taught me incredible time management. I used my athletic discipline to excel in both areas."
  }
];

const schoolTypeBreakdown = [
  { name: 'Public Schools', value: 40 },
  { name: 'Private Schools', value: 35 },
  { name: 'State Contract', value: 25 }
];

const testimonialVideos = [
  {
    id: 'low-gpa-success',
    title: 'From 2.8 to Dental School: My Journey',
    url: 'https://www.youtube.com/watch?v=YxjEjZqYNis',
    author: 'Dr. James Wilson',
    stats: { initialGpa: 2.8, finalGpa: 3.2, datScore: 19 },
    description: 'How I overcame a low GPA and multiple DAT attempts to get accepted'
  },
  {
    id: 'non-trad-path',
    title: 'Career Changer Success Story',
    url: 'https://www.youtube.com/watch?v=8JWXuJ8Yv_0',
    author: 'Dr. Sarah Chen',
    stats: { age: 28, gpa: 3.4, datScore: 20 },
    description: 'Switching from finance to dentistry at 28 - my non-traditional journey'
  },
  {
    id: 'multiple-attempts',
    title: 'Never Give Up: Third Time\'s the Charm',
    url: 'https://www.youtube.com/watch?v=QZEKj8QnmZQ',
    author: 'Dr. Michael Rodriguez',
    stats: { attempts: 3, finalGpa: 3.3, datScore: 18 },
    description: 'How persistence and improvement led to acceptance after multiple application cycles'
  }
];

export default function AdmissionStats() {
  const [selectedStory, setSelectedStory] = useState(null);

  return (
    <div className="space-y-8">
      <Card>
        <h3 className="text-xl font-semibold text-white mb-6">
          Average Dental School Admission Statistics (2023-2024)
        </h3>
        
        <div className="grid md:grid-cols-3 gap-6">
          <div className="space-y-4">
            <div>
              <p className="text-white/80 mb-1">Overall GPA</p>
              <p className="text-3xl font-bold text-white">{averageStats.overall.gpa}</p>
            </div>
            <div>
              <p className="text-white/80 mb-1">Science GPA</p>
              <p className="text-3xl font-bold text-white">{averageStats.overall.scienceGpa}</p>
            </div>
          </div>
          
          <div className="space-y-4">
            <div>
              <p className="text-white/80 mb-1">DAT Academic Average</p>
              <p className="text-3xl font-bold text-white">{averageStats.overall.datAA}</p>
            </div>
            <div>
              <p className="text-white/80 mb-1">DAT PAT</p>
              <p className="text-3xl font-bold text-white">{averageStats.overall.datPAT}</p>
            </div>
          </div>
          
          <div className="space-y-4">
            <div>
              <p className="text-white/80 mb-1">Total Applicants</p>
              <p className="text-3xl font-bold text-white">{averageStats.overall.totalApplicants}</p>
            </div>
            <div>
              <p className="text-white/80 mb-1">Acceptance Rate</p>
              <p className="text-3xl font-bold text-white">{averageStats.overall.acceptanceRate}</p>
            </div>
          </div>
        </div>

        <div className="mt-8">
          <h4 className="text-lg font-medium text-white mb-4">DAT Section Averages</h4>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={averageStats.datBreakdown}>
                <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                <XAxis dataKey="section" stroke="#9CA3AF" />
                <YAxis domain={[15, 30]} stroke="#9CA3AF" />
                <Tooltip
                  contentStyle={{
                    backgroundColor: '#1F2937',
                    border: '1px solid #374151',
                    borderRadius: '0.5rem'
                  }}
                />
                <Bar dataKey="score" fill="#3B82F6" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </Card>

      <div className="grid md:grid-cols-2 gap-6">
        <Card>
          <h3 className="text-lg font-semibold text-white mb-4">GPA Distribution</h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={averageStats.gpaRanges}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="percentage"
                  label={({ name, value }) => `${name}: ${value}%`}
                >
                  {averageStats.gpaRanges.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </Card>

        <Card>
          <h3 className="text-lg font-semibold text-white mb-4">DAT Score Distribution</h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={averageStats.datRanges}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="percentage"
                  label={({ name, value }) => `${name}: ${value}%`}
                >
                  {averageStats.datRanges.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </Card>
      </div>

      <Card>
        <h3 className="text-lg font-semibold text-white mb-4">Experience Hours Comparison</h3>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={averageStats.experienceHours}>
              <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
              <XAxis dataKey="name" stroke="#9CA3AF" />
              <YAxis stroke="#9CA3AF" />
              <Tooltip
                contentStyle={{
                  backgroundColor: '#1F2937',
                  border: '1px solid #374151',
                  borderRadius: '0.5rem'
                }}
              />
              <Bar dataKey="hours" name="Average Hours" fill="#3B82F6" />
              <Bar dataKey="recommended" name="Recommended Hours" fill="#10B981" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </Card>

      <Card>
        <h3 className="text-xl font-semibold text-white mb-6">
          Success Stories: There's No Testimony Without a Test
        </h3>
        
        <div className="mb-6 p-4 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg border border-blue-500/20">
          <p className="text-lg text-white/90 italic">
            "Your journey is unique, and numbers don't tell the whole story. Below are real stories of dental students who overcame challenges and proved that determination matters more than perfect stats."
          </p>
        </div>
        
        <div className="grid md:grid-cols-3 gap-6">
          {successStories.map((story, index) => (
            <div 
              key={index} 
              className="bg-white/5 rounded-lg p-6 cursor-pointer hover:bg-white/10 transition-colors"
              onClick={() => setSelectedStory(story)}
            >
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h4 className="text-lg font-medium text-white">{story.name}</h4>
                  <p className="text-blue-400">{story.school}</p>
                </div>
                <div className="text-right">
                  <p className="text-white/80">GPA: {story.stats.gpa}</p>
                  <p className="text-white/80">DAT: {story.stats.dat}</p>
                </div>
              </div>
              
              <p className="text-white/60 text-sm mb-4">{story.background}</p>
              
              <blockquote className="text-white/80 italic mb-4">
                "{story.quote}"
              </blockquote>
              
              <a
                href={story.video}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center text-blue-400 hover:text-blue-300"
                onClick={(e) => e.stopPropagation()}
              >
                Watch Their Story
                <svg className="w-4 h-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
              </a>
            </div>
          ))}
        </div>

        {selectedStory && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
            <div className="bg-gray-900 rounded-lg p-8 max-w-2xl w-full border border-white/10">
              <div className="flex justify-between items-start mb-6">
                <div>
                  <h3 className="text-2xl font-semibold text-white">{selectedStory.name}</h3>
                  <p className="text-blue-400">{selectedStory.school}</p>
                </div>
                <button
                  onClick={() => setSelectedStory(null)}
                  className="text-white/60 hover:text-white"
                >
                  <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
                {selectedStory.journey && (
                  <div className="mt-4">
                    <h4 className="text-lg font-medium text-white mb-2">My Journey</h4>
                    <p className="text-white/80">{selectedStory.journey}</p>
                  </div>
                )}
              </div>

              <div className="space-y-6">
                <div>
                  <h4 className="text-lg font-medium text-white mb-2">Background</h4>
                  <p className="text-white/80">{selectedStory.background}</p>
                </div>

                <div>
                  <h4 className="text-lg font-medium text-white mb-2">Stats</h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-white/5 rounded-lg p-4">
                      <p className="text-white/60 text-sm">GPA</p>
                      <p className="text-2xl font-semibold text-white">{selectedStory.stats.gpa}</p>
                    </div>
                    <div className="bg-white/5 rounded-lg p-4">
                      <p className="text-white/60 text-sm">DAT</p>
                      <p className="text-2xl font-semibold text-white">{selectedStory.stats.dat}</p>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="text-lg font-medium text-white mb-2">Tips for Success</h4>
                  <ul className="space-y-2">
                    {selectedStory.tips.map((tip, index) => (
                      <li key={index} className="flex items-center text-white/80">
                        <span className="w-2 h-2 bg-blue-400 rounded-full mr-2" />
                        {tip}
                      </li>
                    ))}
                  </ul>
                </div>

                <blockquote className="text-lg text-white/80 italic border-l-4 border-blue-500 pl-4">
                  "{selectedStory.quote}"
                </blockquote>

                <div className="flex justify-end">
                  <a
                    href={selectedStory.video}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-400 transition-colors"
                  >
                    Watch Full Story
                    <svg className="w-4 h-4 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                  </a>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="mt-8 p-6 bg-blue-500/10 rounded-lg border border-blue-500/20">
          <h4 className="text-lg font-medium text-white mb-4">Key Takeaways:</h4>
          <ul className="space-y-2 text-white/80">
            <li className="flex items-center">
              <span className="w-2 h-2 bg-blue-400 rounded-full mr-2" />
              Lower stats don't mean your dream is impossible - focus on showing growth and commitment
            </li>
            <li className="flex items-center">
              <span className="w-2 h-2 bg-blue-400 rounded-full mr-2" />
              Use setbacks as opportunities to demonstrate resilience and determination
            </li>
            <li className="flex items-center">
              <span className="w-2 h-2 bg-blue-400 rounded-full mr-2" />
              Build a compelling narrative that explains your journey and challenges
            </li>
            <li className="flex items-center">
              <span className="w-2 h-2 bg-blue-400 rounded-full mr-2" />
              Consider post-bacc programs or retaking courses to show academic improvement
            </li>
            <li className="flex items-center">
              <span className="w-2 h-2 bg-blue-400 rounded-full mr-2" />
              Get extensive hands-on experience to compensate for lower academic metrics
            </li>
            <li className="flex items-center">
              <span className="w-2 h-2 bg-blue-400 rounded-full mr-2" />
              Don't be afraid to apply multiple cycles - use each attempt to strengthen your application
            </li>
            <li className="flex items-center">
              <span className="w-2 h-2 bg-blue-400 rounded-full mr-2" />
              Remember: "There's no testimony without a test" - your struggles can become your strength
            </li>
          </ul>
        </div>
        
        <div className="mt-6 p-6 bg-gradient-to-r from-purple-500/10 to-blue-500/10 rounded-lg border border-purple-500/20">
          <h4 className="text-xl font-semibold text-white mb-4">Words of Encouragement</h4>
          <blockquote className="text-lg text-white/90 italic">
            "Many successful dentists faced rejection, low scores, and seemingly impossible odds. What sets them apart isn't their initial stats - it's their refusal to give up. Your past scores don't define your future success. Keep pushing forward, learn from each setback, and remember that persistence often matters more than perfection."
          </blockquote>
        </div>
      </Card>

      <div className="mt-8 p-6 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg">
        <h3 className="text-xl font-semibold text-white mb-4">Success Stories & Testimonials</h3>
        <div className="grid md:grid-cols-3 gap-6">
          {testimonialVideos.map(video => (
            <div key={video.id} className="bg-white/5 rounded-lg p-6">
              <div className="aspect-video mb-4 bg-gray-900 rounded-lg relative group cursor-pointer">
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-16 h-16 bg-blue-500/90 rounded-full flex items-center justify-center group-hover:bg-blue-400 transition-colors">
                    <svg className="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>
              </div>
              <h4 className="text-lg font-medium text-white mb-2">{video.title}</h4>
              <p className="text-white/60 text-sm mb-4">{video.author}</p>
              <div className="space-y-2 mb-4">
                {Object.entries(video.stats).map(([key, value]) => (
                  <div key={key} className="flex justify-between">
                    <span className="text-white/80">{key.replace(/([A-Z])/g, ' $1').toLowerCase()}:</span>
                    <span className="text-blue-400">{value}</span>
                  </div>
                ))}
              </div>
              <p className="text-white/80 text-sm">{video.description}</p>
              <a
                href={video.url}
                target="_blank"
                rel="noopener noreferrer"
                className="mt-4 inline-flex items-center text-blue-400 hover:text-blue-300"
              >
                Watch Video
                <svg className="w-4 h-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
              </a>
            </div>
          ))}
        </div>
      </div>

      <div className="mt-8 p-6 bg-gradient-to-r from-purple-500/10 to-blue-500/10 rounded-lg">
        <h3 className="text-xl font-semibold text-white mb-4">Key Requirements for Dental School</h3>
        <div className="grid md:grid-cols-2 gap-8">
          <div>
            <h4 className="text-lg font-medium text-white mb-3">Academic Requirements</h4>
            <ul className="space-y-2 text-white/80">
              <li>• Bachelor's degree (any major)</li>
              <li>• Pre-dental prerequisite courses:
                <ul className="ml-4 mt-2 space-y-1">
                  <li>- Biology with lab (8-12 credits)</li>
                  <li>- General Chemistry with lab (8 credits)</li>
                  <li>- Organic Chemistry with lab (8 credits)</li>
                  <li>- Physics with lab (8 credits)</li>
                  <li>- Biochemistry (3-4 credits)</li>
                  <li>- English (6 credits)</li>
                </ul>
              </li>
              <li>• Competitive GPA (3.0+ minimum, 3.5+ preferred)</li>
              <li>• DAT score (17+ minimum, 20+ preferred)</li>
            </ul>
          </div>
          <div>
            <h4 className="text-lg font-medium text-white mb-3">Experience & Extracurriculars</h4>
            <ul className="space-y-2 text-white/80">
              <li>• Dental shadowing (100+ hours recommended)</li>
              <li>• Community service/volunteering</li>
              <li>• Research experience (preferred)</li>
              <li>• Leadership positions</li>
              <li>• Dental-related experience:
                <ul className="ml-4 mt-2 space-y-1">
                  <li>- Dental assistant</li>
                  <li>- Dental lab technician</li>
                  <li>- Dental office experience</li>
                </ul>
              </li>
            </ul>
          </div>
        </div>
      </div>

      <div className="mt-8 p-6 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg">
        <h3 className="text-xl font-semibold text-white mb-4">Words of Encouragement</h3>
        <blockquote className="text-lg text-white/90 italic mb-6">
          "There's no testimony without a test. Your journey to dental school is unique, and every challenge you face is preparing you for success. Don't let statistics discourage you - they're guidelines, not guarantees."
        </blockquote>
        <div className="space-y-4 text-white/80">
          <p>Remember:</p>
          <ul className="space-y-2">
            <li>• Many successful dentists were once "below average" applicants</li>
            <li>• Schools look at your entire application, not just numbers</li>
            <li>• Growth and improvement can outweigh initial setbacks</li>
            <li>• Your unique background and experiences matter</li>
            <li>• Persistence often matters more than perfection</li>
          </ul>
        </div>
      </div>
    </div>
  );
}