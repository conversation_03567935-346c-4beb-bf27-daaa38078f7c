import React from 'react';
import { Card, CardGrid } from '../common';

const resources = [
  {
    category: 'DAT Preparation',
    items: [
      {
        title: 'DAT Bootcamp',
        description: 'Comprehensive DAT study platform with practice tests',
        link: 'https://datbootcamp.com'
      },
      {
        title: 'Chad\'s Videos',
        description: 'In-depth science content review',
        link: 'https://www.chadsvideos.com'
      }
    ]
  },
  {
    category: 'Application Resources',
    items: [
      {
        title: 'ADEA AADSAS',
        description: 'Official dental school application service',
        link: 'https://www.adea.org/aadsas'
      },
      {
        title: 'Student Doctor Network',
        description: 'Forums and resources for pre-dental students',
        link: 'https://forums.studentdoctor.net/categories/pre-dental'
      }
    ]
  },
  {
    category: 'Study Materials',
    items: [
      {
        title: 'Organic Chemistry as a Second Language',
        description: 'Essential organic chemistry concepts',
        link: '#'
      },
      {
        title: 'ACS Study Guides',
        description: 'Chemistry preparation materials',
        link: '#'
      }
    ]
  }
];

export default function ResourceLibrary() {
  return (
    <div className="space-y-8">
      {resources.map((section) => (
        <div key={section.category}>
          <h3 className="text-xl font-semibold text-white mb-4">{section.category}</h3>
          <CardGrid>
            {section.items.map((item) => (
              <Card key={item.title}>
                <h4 className="text-lg font-medium text-white mb-2">{item.title}</h4>
                <p className="text-white/80 mb-4">{item.description}</p>
                <a
                  href={item.link}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-400 hover:text-blue-300 inline-flex items-center"
                >
                  Learn More
                  <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                </a>
              </Card>
            ))}
          </CardGrid>
        </div>
      ))}
    </div>
  );
}