import React from 'react';
import { Card } from '../common';

const testimonials = [
  {
    name: '<PERSON>',
    school: 'Harvard School of Dental Medicine',
    year: 'D2',
    quote: 'Focus on maintaining a balanced approach. While academics are crucial, dental schools also value well-rounded individuals with diverse experiences.',
    tips: [
      'Start shadowing early',
      'Join study groups for science courses',
      'Take practice DATs regularly'
    ]
  },
  {
    name: '<PERSON>',
    school: 'University of Michigan School of Dentistry',
    year: 'D1',
    quote: 'The journey is challenging but incredibly rewarding. Building strong time management skills during your pre-dental years is essential for success.',
    tips: [
      'Create a detailed study schedule',
      'Get involved in research if possible',
      'Network with current dental students'
    ]
  },
  {
    name: '<PERSON><PERSON>',
    school: 'NYU College of Dentistry',
    year: 'D3',
    quote: "Don't be afraid to reach out to mentors and ask questions. The dental community is very supportive of aspiring dentists.",
    tips: [
      'Attend dental school info sessions',
      'Participate in pre-dental organizations',
      'Seek diverse shadowing experiences'
    ]
  }
];

export default function TestimonialsSection() {
  return (
    <div className="space-y-8">
      {testimonials.map((testimonial) => (
        <Card key={testimonial.name}>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-xl font-semibold text-white">{testimonial.name}</h3>
                <p className="text-blue-400">{testimonial.school}</p>
                <p className="text-white/60 text-sm">{testimonial.year} Student</p>
              </div>
              <div className="text-5xl text-blue-500/20">"</div>
            </div>
            
            <blockquote className="text-white/80 italic">
              "{testimonial.quote}"
            </blockquote>
            
            <div>
              <h4 className="text-white font-medium mb-2">Top Tips:</h4>
              <ul className="space-y-2">
                {testimonial.tips.map((tip, index) => (
                  <li key={index} className="flex items-center text-white/70">
                    <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2" />
                    {tip}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
}