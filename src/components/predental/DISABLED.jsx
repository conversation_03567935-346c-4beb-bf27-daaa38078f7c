import React from 'react';

// This component is a placeholder for disabled PreDental features
export default function DisabledPreDentalComponent() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-900 via-blue-900/20 to-purple-900/20 py-24">
      <div className="max-w-7xl mx-auto px-4 text-center">
        <h1 className="text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-purple-400 to-blue-400 mb-4">
          Pre-Dental Features Coming Soon
        </h1>
        <p className="text-xl text-blue-100/80 mb-4">
          This feature is currently being prepared for a future release.
        </p>
        <div className="mt-8 p-6 bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 max-w-2xl mx-auto">
          <p className="text-white/80">
            We're working on building comprehensive tools to help you track your pre-dental journey. 
            Check back soon for course tracking, application timelines, and more.
          </p>
        </div>
      </div>
    </div>
  );
}

// Export empty hooks to prevent errors
export const usePreDentalProgress = () => ({
  courses: [],
  gpaStats: { overall: 0, science: 0, totalCredits: 0, scienceCredits: 0 },
  loading: false,
  error: null,
  addCourse: () => Promise.resolve(null),
  updateCourse: () => Promise.resolve(null),
  deleteCourse: () => Promise.resolve(),
  refresh: () => {}
});

// Export empty components
export const PreDentalTracker = DisabledPreDentalComponent;
export const ProgressDashboard = () => <></>;
export const CourseTracker = () => <></>;
export const RequirementsList = () => <></>;
export const TimelineTracker = () => <></>;
export const ResourceLibrary = () => <></>;
export const ApplicationCountdown = () => <></>;
export const AdmissionStats = () => <></>;
export const TestimonialsSection = () => <></>;
export const SchoolSelector = () => <></>;
export const SchoolComparison = () => <></>;
export const FinancialResources = () => <></>;
export const OfficialResources = () => <></>;
export const FloatingAssistant = () => <></>;
export const ThreeDMouthModel = () => <></>;

// This is a mapping of any hooks we need to disable
export const preDentalHooks = {
  usePreDentalProgress
}; 