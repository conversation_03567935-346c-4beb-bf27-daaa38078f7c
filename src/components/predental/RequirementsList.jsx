import React from 'react';
import { Card } from '../common';

const requirements = [
  {
    category: 'Required Courses',
    items: [
      { name: 'General Biology I & II with Lab', credits: 8 },
      { name: 'General Chemistry I & II with Lab', credits: 8 },
      { name: 'Organic Chemistry I & II with Lab', credits: 8 },
      { name: 'Physics I & II with Lab', credits: 8 },
      { name: 'Biochemistry', credits: 3 }
    ]
  },
  {
    category: 'DAT Preparation',
    items: [
      { name: 'Biology', target: '20+' },
      { name: 'General Chemistry', target: '20+' },
      { name: 'Organic Chemistry', target: '20+' },
      { name: 'Perceptual Ability', target: '20+' },
      { name: 'Reading Comprehension', target: '20+' },
      { name: 'Quantitative Reasoning', target: '20+' }
    ]
  },
  {
    category: 'Experience',
    items: [
      { name: 'Shadowing Hours', target: '100+ hours' },
      { name: 'Volunteer Service', target: '150+ hours' },
      { name: 'Research Experience', target: 'Recommended' },
      { name: 'Leadership Roles', target: '2-3 positions' }
    ]
  }
];

export default function RequirementsList() {
  return (
    <div className="space-y-8">
      {requirements.map((section) => (
        <Card key={section.category}>
          <h3 className="text-xl font-semibold text-white mb-4">{section.category}</h3>
          <div className="space-y-3">
            {section.items.map((item) => (
              <div 
                key={item.name}
                className="flex justify-between items-center p-3 bg-white/5 rounded-lg"
              >
                <span className="text-white/90">{item.name}</span>
                <span className="text-blue-400">
                  {item.credits ? `${item.credits} credits` : item.target}
                </span>
              </div>
            ))}
          </div>
        </Card>
      ))}
    </div>
  );
}