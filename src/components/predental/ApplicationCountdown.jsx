import React, { useState, useEffect } from 'react';
import { Card } from '../common';

// AADSAS typically opens in early June
const APPLICATION_OPEN_DATE = new Date(2024, 5, 1, 0, 0, 0); // June 1st, 2024

export default function ApplicationCountdown() {
  const [timeLeft, setTimeLeft] = useState(calculateTimeLeft());

  function calculateTimeLeft() {
    const difference = APPLICATION_OPEN_DATE - new Date();
    
    if (difference < 0) {
      return {
        days: 0,
        hours: 0,
        minutes: 0,
        seconds: 0,
        isOpen: true
      };
    }

    return {
      days: Math.floor(difference / (1000 * 60 * 60 * 24)),
      hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
      minutes: Math.floor((difference / 1000 / 60) % 60),
      seconds: Math.floor((difference / 1000) % 60),
      isOpen: false
    };
  }

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft(calculateTimeLeft());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  return (
    <Card className="relative overflow-hidden">
      {/* Background gradient animation */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-blue-500/10 animate-gradient"></div>
      
      <div className="relative">
        <div className="text-center mb-6">
          <h3 className="text-2xl font-bold text-white mb-2">
            {timeLeft.isOpen ? 'AADSAS is Open!' : 'AADSAS Opening Countdown'}
          </h3>
          <p className="text-blue-200/80">
            {timeLeft.isOpen 
              ? 'Start your application now'
              : 'Time until the 2024-2025 AADSAS application cycle opens'}
          </p>
        </div>

        {timeLeft.isOpen ? (
          <div className="text-center">
            <a
              href="https://www.adea.org/aadsas"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-block px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-400 transition-colors"
            >
              Begin Application
            </a>
          </div>
        ) : (
          <div className="grid grid-cols-4 gap-4">
            <div className="text-center">
              <div className="bg-white/5 rounded-lg p-4">
                <div className="text-3xl font-bold text-white mb-1">{timeLeft.days}</div>
                <div className="text-sm text-white/60">Days</div>
              </div>
            </div>
            <div className="text-center">
              <div className="bg-white/5 rounded-lg p-4">
                <div className="text-3xl font-bold text-white mb-1">{timeLeft.hours}</div>
                <div className="text-sm text-white/60">Hours</div>
              </div>
            </div>
            <div className="text-center">
              <div className="bg-white/5 rounded-lg p-4">
                <div className="text-3xl font-bold text-white mb-1">{timeLeft.minutes}</div>
                <div className="text-sm text-white/60">Minutes</div>
              </div>
            </div>
            <div className="text-center">
              <div className="bg-white/5 rounded-lg p-4">
                <div className="text-3xl font-bold text-white mb-1">{timeLeft.seconds}</div>
                <div className="text-sm text-white/60">Seconds</div>
              </div>
            </div>
          </div>
        )}

        <div className="mt-6 text-center">
          <div className="inline-flex items-center space-x-2 text-white/80 text-sm">
            <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>Applications typically open in early June each year</span>
          </div>
        </div>
      </div>
    </Card>
  );
}