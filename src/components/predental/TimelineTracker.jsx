import React from 'react';
import { Card } from '../common';

const timeline = [
  {
    year: 'Freshman Year',
    tasks: [
      'Begin required science courses (Biology, Chemistry)',
      'Join pre-dental club or society',
      'Start volunteering in community service',
      'Research dental schools of interest'
    ]
  },
  {
    year: 'Sophomore Year',
    tasks: [
      'Continue science prerequisites',
      'Begin shadowing dentists',
      'Maintain strong GPA',
      'Consider research opportunities'
    ]
  },
  {
    year: 'Junior Year',
    tasks: [
      'Complete remaining prerequisites',
      'Study for and take the DAT',
      'Gather letters of recommendation',
      'Begin AADSAS application'
    ]
  },
  {
    year: 'Senior Year',
    tasks: [
      'Submit dental school applications',
      'Prepare for interviews',
      'Continue shadowing and volunteering',
      'Make final school decision'
    ]
  }
];

export default function TimelineTracker() {
  return (
    <div className="space-y-8">
      {timeline.map((period) => (
        <Card key={period.year}>
          <h3 className="text-xl font-semibold text-white mb-4">{period.year}</h3>
          <div className="space-y-3">
            {period.tasks.map((task, index) => (
              <div 
                key={index}
                className="flex items-center p-3 bg-white/5 rounded-lg"
              >
                <div className="w-2 h-2 bg-blue-500 rounded-full mr-3" />
                <span className="text-white/90">{task}</span>
              </div>
            ))}
          </div>
        </Card>
      ))}
    </div>
  );
}