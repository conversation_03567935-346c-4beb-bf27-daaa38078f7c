import React, { useState } from 'react';

export default function ThreeDMouthModel() {
  const [selectedPart, setSelectedPart] = useState('overview');

  const annotations = {
    overview: "Complete dental anatomy model with upper and lower teeth",
    upperJaw: "Upper jaw (maxillary) teeth including incisors, canines, premolars, and molars",
    lowerJaw: "Lower jaw (mandibular) teeth and anatomical structures",
    tongue: "Tongue anatomy and relationship to oral cavity"
  };

  const handlePartSelect = (part) => {
    setSelectedPart(part);
    // You could add specific camera positions for each part in the future
  };

  return (
    <div className="space-y-4">
      <div className="relative" style={{ height: '500px', width: '100%' }}>
        <iframe
          title="Dental Model Viewer"
          frameBorder="0"
          allowFullScreen
          mozallowfullscreen="true"
          webkitallowfullscreen="true"
          allow="autoplay; fullscreen; xr-spatial-tracking"
          className="w-full h-full rounded-lg"
          src="https://sketchfab.com/models/5b6a5b6b74384bc5a25c612db00903c5/embed?autostart=1&ui_controls=1&ui_infos=1&ui_inspector=1&ui_stop=1&ui_watermark=1&ui_watermark_link=1"
        />
        <div className="absolute bottom-4 left-4 text-white/70 text-sm bg-black/50 px-3 py-1 rounded-full">
          Interactive 3D Model • Use mouse/touch to explore
        </div>
      </div>

      {/* Educational Controls */}
      <div className="bg-white/5 backdrop-blur-sm rounded-lg p-4 border border-white/10">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mb-4">
          {Object.entries(annotations).map(([key, _]) => (
            <button
              key={key}
              onClick={() => handlePartSelect(key)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200
                ${selectedPart === key 
                  ? 'bg-blue-500 text-white' 
                  : 'bg-white/10 text-white/80 hover:bg-white/20'}`}
            >
              {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
            </button>
          ))}
        </div>
        
        <div className="text-white/80 p-4 bg-white/5 rounded-lg">
          {annotations[selectedPart]}
        </div>

        <div className="mt-4 text-white/60 text-sm">
          <h4 className="font-medium mb-2">Key Features:</h4>
          <ul className="list-disc list-inside space-y-1">
            <li>High-detail anatomical model</li>
            <li>Accurate tooth positioning and structure</li>
            <li>Interactive 360° viewing</li>
            <li>Zoom capability for detailed examination</li>
          </ul>
        </div>
      </div>
    </div>
  );
} 