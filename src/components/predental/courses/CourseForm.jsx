import React, { useState } from 'react';

const COURSE_TYPES = [
  'Biology',
  'Chemistry',
  'Organic Chemistry',
  'Physics',
  'Biochemistry',
  'Mathematics',
  'Other Science'
];

const GRADE_OPTIONS = ['A', 'A-', 'B+', 'B', 'B-', 'C+', 'C', 'C-', 'D+', 'D', 'F', 'IP'];

export default function CourseForm({ onSubmit, onCancel, initialData }) {
  const [formData, setFormData] = useState(initialData || {
    name: '',
    type: '',
    credits: '',
    grade: '',
    semester: '',
    year: new Date().getFullYear(),
    notes: ''
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4 bg-white/5 rounded-lg p-6 mb-6">
      <div className="grid md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-white/80 mb-1">
            Course Name
          </label>
          <input
            type="text"
            value={formData.name}
            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            className="w-full bg-white/10 border-white/20 rounded-lg text-white"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-white/80 mb-1">
            Course Type
          </label>
          <select
            value={formData.type}
            onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value }))}
            className="w-full bg-white/10 border-white/20 rounded-lg text-white"
            required
          >
            <option value="">Select Type</option>
            {COURSE_TYPES.map(type => (
              <option key={type} value={type}>{type}</option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-white/80 mb-1">
            Credits
          </label>
          <input
            type="number"
            min="0"
            max="6"
            step="0.5"
            value={formData.credits}
            onChange={(e) => setFormData(prev => ({ ...prev, credits: e.target.value }))}
            className="w-full bg-white/10 border-white/20 rounded-lg text-white"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-white/80 mb-1">
            Grade
          </label>
          <select
            value={formData.grade}
            onChange={(e) => setFormData(prev => ({ ...prev, grade: e.target.value }))}
            className="w-full bg-white/10 border-white/20 rounded-lg text-white"
            required
          >
            <option value="">Select Grade</option>
            {GRADE_OPTIONS.map(grade => (
              <option key={grade} value={grade}>{grade}</option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-white/80 mb-1">
            Semester
          </label>
          <select
            value={formData.semester}
            onChange={(e) => setFormData(prev => ({ ...prev, semester: e.target.value }))}
            className="w-full bg-white/10 border-white/20 rounded-lg text-white"
            required
          >
            <option value="">Select Semester</option>
            <option value="Fall">Fall</option>
            <option value="Spring">Spring</option>
            <option value="Summer">Summer</option>
            <option value="Winter">Winter</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-white/80 mb-1">
            Year
          </label>
          <input
            type="number"
            min="2000"
            max="2030"
            value={formData.year}
            onChange={(e) => setFormData(prev => ({ ...prev, year: e.target.value }))}
            className="w-full bg-white/10 border-white/20 rounded-lg text-white"
            required
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-white/80 mb-1">
          Notes
        </label>
        <textarea
          value={formData.notes}
          onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
          className="w-full bg-white/10 border-white/20 rounded-lg text-white h-24"
          placeholder="Any additional notes..."
        />
      </div>

      <div className="flex justify-end space-x-4">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 text-white/80 hover:text-white"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-400 transition-colors"
        >
          {initialData ? 'Update Course' : 'Add Course'}
        </button>
      </div>
    </form>
  );
}