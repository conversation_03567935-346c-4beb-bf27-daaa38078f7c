import React, { useState, useEffect } from 'react';
import { Card } from '../../common';
import CourseCalculator from './CourseCalculator';
import CourseForm from './CourseForm';
import CourseList from './CourseList';
import { useCourses } from '../../../lib/hooks/useCourses';
import { useUser } from '../../../contexts/UserContext';
import AuthPrompt from '../../auth/AuthPrompt';

export default function CourseTracker() {
  const { user } = useUser();
  const { courses, loading, error, addCourse, updateCourse, deleteCourse } = useCourses();
  const [showForm, setShowForm] = useState(false);

  const handleAddCourse = async (course) => {
    await addCourse(course);
    setShowForm(false);
  };

  const handleDeleteCourse = async (courseId) => {
    await deleteCourse(courseId);
  };

  const handleEditCourse = async (courseId, updatedCourse) => {
    await updateCourse(courseId, updatedCourse);
  };

  if (!user) {
    return (
      <AuthPrompt message="Sign in to track your pre-dental courses" />
    );
  }

  if (loading) {
    return (
      <Card>
        <div className="flex justify-center items-center h-32">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <div className="text-red-400 text-center py-4">
          {error}
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-8">
      <CourseCalculator />
      
      <Card>
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-xl font-semibold text-white">Course Tracker</h3>
          <button
            onClick={() => setShowForm(true)}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-400 transition-colors"
          >
            Add Course
          </button>
        </div>

        {showForm && (
          <CourseForm
            onSubmit={handleAddCourse}
            onCancel={() => setShowForm(false)}
          />
        )}

        <CourseList
          courses={courses}
          onDelete={handleDeleteCourse}
          onEdit={handleEditCourse}
        />
      </Card>
    </div>
  );
}