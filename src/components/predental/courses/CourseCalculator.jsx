import React, { useState } from 'react';
import { Card } from '../../common';
import Select from 'react-select';

const GRADE_POINTS = {
  'A+': 4.0, 'A': 4.0, 'A-': 3.7,
  'B+': 3.3, 'B': 3.0, 'B-': 2.7,
  'C+': 2.3, 'C': 2.0, 'C-': 1.7,
  'D+': 1.3, 'D': 1.0, 'D-': 0.7,
  'F': 0.0
};

const COURSE_TYPES = {
  'BIOLOGY': [
    { value: 'BIO101', label: 'General Biology I', credits: 4 },
    { value: 'BIO102', label: 'General Biology II', credits: 4 },
    { value: 'BIO201', label: 'Cell Biology', credits: 3 },
    { value: 'BIO202', label: 'Genetics', credits: 3 },
    { value: 'BIO301', label: 'Anatomy & Physiology I', credits: 4 },
    { value: 'BIO302', label: 'Anatomy & Physiology II', credits: 4 },
    { value: 'BIO303', label: 'Microbiology', credits: 4 }
  ],
  'CHEMISTRY': [
    { value: 'CHEM101', label: 'General Chemistry I', credits: 4 },
    { value: 'CHEM102', label: 'General Chemistry II', credits: 4 },
    { value: 'CHEM201', label: 'Organic Chemistry I', credits: 4 },
    { value: 'CHEM202', label: 'Organic Chemistry II', credits: 4 },
    { value: 'CHEM301', label: 'Biochemistry', credits: 3 }
  ],
  'PHYSICS': [
    { value: 'PHYS101', label: 'Physics I', credits: 4 },
    { value: 'PHYS102', label: 'Physics II', credits: 4 }
  ],
  'MATH': [
    { value: 'MATH101', label: 'Calculus I', credits: 4 },
    { value: 'MATH102', label: 'Calculus II', credits: 4 },
    { value: 'MATH201', label: 'Statistics', credits: 3 }
  ]
};

const gradeOptions = Object.keys(GRADE_POINTS).map(grade => ({
  value: grade,
  label: grade
}));

export default function CourseCalculator() {
  const [courses, setCourses] = useState([]);
  const [selectedType, setSelectedType] = useState(null);
  const [selectedCourse, setSelectedCourse] = useState(null);
  const [selectedGrade, setSelectedGrade] = useState(null);
  const [customCredits, setCustomCredits] = useState('');
  const [isCustomCourse, setIsCustomCourse] = useState(false);
  const [customCourseName, setCustomCourseName] = useState('');

  const addCourse = () => {
    if (!selectedGrade) return;

    const newCourse = {
      id: Date.now(),
      name: isCustomCourse ? customCourseName : selectedCourse.label,
      credits: isCustomCourse ? Number(customCredits) : selectedCourse.credits,
      grade: selectedGrade.value,
      gradePoints: GRADE_POINTS[selectedGrade.value]
    };

    setCourses([...courses, newCourse]);
    resetForm();
  };

  const resetForm = () => {
    setSelectedType(null);
    setSelectedCourse(null);
    setSelectedGrade(null);
    setCustomCredits('');
    setIsCustomCourse(false);
    setCustomCourseName('');
  };

  const removeCourse = (courseId) => {
    setCourses(courses.filter(course => course.id !== courseId));
  };

  const calculateGPA = () => {
    if (courses.length === 0) return 0;
    
    const totalPoints = courses.reduce((sum, course) => 
      sum + (course.credits * course.gradePoints), 0);
    const totalCredits = courses.reduce((sum, course) => 
      sum + course.credits, 0);
    
    return (totalPoints / totalCredits).toFixed(2);
  };

  const calculateScienceGPA = () => {
    const scienceCourses = courses.filter(course => 
      course.name.toLowerCase().includes('bio') || 
      course.name.toLowerCase().includes('chem') || 
      course.name.toLowerCase().includes('phys')
    );

    if (scienceCourses.length === 0) return 0;

    const totalPoints = scienceCourses.reduce((sum, course) => 
      sum + (course.credits * course.gradePoints), 0);
    const totalCredits = scienceCourses.reduce((sum, course) => 
      sum + course.credits, 0);
    
    return (totalPoints / totalCredits).toFixed(2);
  };

  return (
    <div className="space-y-6">
      <Card>
        <h3 className="text-xl font-semibold text-white mb-6">GPA Calculator</h3>

        <div className="space-y-4">
          <div className="flex items-center gap-4 mb-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={isCustomCourse}
                onChange={(e) => setIsCustomCourse(e.target.checked)}
                className="mr-2"
              />
              <span className="text-white/80">Add Custom Course</span>
            </label>
          </div>

          {isCustomCourse ? (
            <div className="grid md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-white/80 mb-1">
                  Course Name
                </label>
                <input
                  type="text"
                  value={customCourseName}
                  onChange={(e) => setCustomCourseName(e.target.value)}
                  className="w-full bg-white/5 border-white/10 rounded-lg text-white"
                  placeholder="Enter course name"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-white/80 mb-1">
                  Credits
                </label>
                <input
                  type="number"
                  value={customCredits}
                  onChange={(e) => setCustomCredits(e.target.value)}
                  min="0"
                  max="6"
                  step="0.5"
                  className="w-full bg-white/5 border-white/10 rounded-lg text-white"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-white/80 mb-1">
                  Grade
                </label>
                <Select
                  value={selectedGrade}
                  onChange={setSelectedGrade}
                  options={gradeOptions}
                  className="text-gray-900"
                  placeholder="Select grade"
                />
              </div>
            </div>
          ) : (
            <div className="grid md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-white/80 mb-1">
                  Course Type
                </label>
                <Select
                  value={selectedType}
                  onChange={setSelectedType}
                  options={Object.keys(COURSE_TYPES).map(type => ({
                    value: type,
                    label: type
                  }))}
                  className="text-gray-900"
                  placeholder="Select type"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-white/80 mb-1">
                  Course
                </label>
                <Select
                  value={selectedCourse}
                  onChange={setSelectedCourse}
                  options={selectedType ? COURSE_TYPES[selectedType.value] : []}
                  className="text-gray-900"
                  isDisabled={!selectedType}
                  placeholder="Select course"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-white/80 mb-1">
                  Grade
                </label>
                <Select
                  value={selectedGrade}
                  onChange={setSelectedGrade}
                  options={gradeOptions}
                  className="text-gray-900"
                  placeholder="Select grade"
                />
              </div>
            </div>
          )}

          <div className="flex justify-end">
            <button
              onClick={addCourse}
              disabled={(!isCustomCourse && (!selectedCourse || !selectedGrade)) || 
                       (isCustomCourse && (!customCourseName || !customCredits || !selectedGrade))}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-400 transition-colors disabled:opacity-50"
            >
              Add Course
            </button>
          </div>
        </div>

        {courses.length > 0 && (
          <div className="mt-8">
            <h4 className="text-lg font-medium text-white mb-4">Course List</h4>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="text-left border-b border-white/10">
                    <th className="py-2 px-4 text-white/80">Course</th>
                    <th className="py-2 px-4 text-white/80">Credits</th>
                    <th className="py-2 px-4 text-white/80">Grade</th>
                    <th className="py-2 px-4 text-white/80">Grade Points</th>
                    <th className="py-2 px-4"></th>
                  </tr>
                </thead>
                <tbody>
                  {courses.map(course => (
                    <tr key={course.id} className="border-b border-white/5">
                      <td className="py-2 px-4 text-white">{course.name}</td>
                      <td className="py-2 px-4 text-white">{course.credits}</td>
                      <td className="py-2 px-4 text-white">{course.grade}</td>
                      <td className="py-2 px-4 text-white">{course.gradePoints}</td>
                      <td className="py-2 px-4">
                        <button
                          onClick={() => removeCourse(course.id)}
                          className="text-red-400 hover:text-red-300"
                        >
                          Remove
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <div className="mt-6 grid md:grid-cols-2 gap-6">
              <div className="bg-white/5 rounded-lg p-4">
                <h5 className="text-lg font-medium text-white mb-2">Overall GPA</h5>
                <p className="text-3xl font-bold text-blue-400">{calculateGPA()}</p>
              </div>
              <div className="bg-white/5 rounded-lg p-4">
                <h5 className="text-lg font-medium text-white mb-2">Science GPA</h5>
                <p className="text-3xl font-bold text-green-400">{calculateScienceGPA()}</p>
              </div>
            </div>
          </div>
        )}
      </Card>
    </div>
  );
}