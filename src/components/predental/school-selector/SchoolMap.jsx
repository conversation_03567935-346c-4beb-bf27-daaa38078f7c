import React, { useEffect, useRef, useState } from 'react';
import { loadGoogleMapsScript } from '../../../lib/services/googleMapsService';
import { Card } from '../../common';
import LoadingSpinner from '../../common/LoadingSpinner';

const DEFAULT_CENTER = { lat: 39.8283, lng: -98.5795 }; // Center of US
const DEFAULT_ZOOM = 4;

export default function SchoolMap({ schools, selectedSchool, onSchoolSelect }) {
  const mapContainerRef = useRef(null);
  const [map, setMap] = useState(null);
  const [markers, setMarkers] = useState([]);
  const [infoWindow, setInfoWindow] = useState(null);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(true);

  // Initialize map
  useEffect(() => {
    let mounted = true;
    let mapInstance = null;

    const initMap = async () => {
      // Add a small delay to ensure DOM is ready
      if (!mapContainerRef.current) {
        if (mounted) {
          setTimeout(initMap, 100);
        }
        return;
      }

      try {
        const google = await loadGoogleMapsScript();
        if (!mounted) return;

        mapInstance = new google.maps.Map(mapContainerRef.current, {
          center: DEFAULT_CENTER,
          zoom: DEFAULT_ZOOM,
          styles: [
            {
              featureType: 'all',
              elementType: 'geometry',
              stylers: [{ color: '#242f3e' }]
            },
            {
              featureType: 'all',
              elementType: 'labels.text.stroke',
              stylers: [{ color: '#242f3e' }]
            },
            {
              featureType: 'all',
              elementType: 'labels.text.fill',
              stylers: [{ color: '#746855' }]
            }
          ],
          mapTypeControl: false,
          streetViewControl: false,
          fullscreenControl: true,
          gestureHandling: 'cooperative'
        });

        const infoWindowInstance = new google.maps.InfoWindow();
        
        setMap(mapInstance);
        setInfoWindow(infoWindowInstance);
        setLoading(false);
      } catch (err) {
        console.error("Error loading Google Maps:", err);
        if (mounted) {
          setError(err);
        }
      }
    };

    initMap();
    return () => { mounted = false; };
  }, []);

  // Handle markers
  useEffect(() => {
    if (!map || !schools?.length || !infoWindow) return;

    // Clear existing markers
    markers.forEach(marker => marker.setMap(null));

    // Create new markers
    const newMarkers = schools.map(school => {
      if (!school.latitude || !school.longitude) {
        console.warn('Missing coordinates for school:', school.name);
        return null;
      }

      const marker = new google.maps.Marker({
        position: {
          lat: parseFloat(school.latitude),
          lng: parseFloat(school.longitude)
        },
        map,
        title: school.name,
        animation: google.maps.Animation.DROP
      });

      marker.addListener('click', () => {
        infoWindow.setContent(`
          <div style="padding: 1rem;">
            <h3 style="font-weight: 600; font-size: 1.125rem; margin-bottom: 0.5rem;">${school.name}</h3>
            <p style="font-size: 0.875rem; margin-bottom: 0.5rem;">${school.address}</p>
            <p style="font-size: 0.875rem; color: #60A5FA;">${school.clinic_info || ''}</p>
          </div>
        `);
        infoWindow.open(map, marker);
        onSchoolSelect?.(school.id);
      });

      return marker;
    }).filter(Boolean);

    setMarkers(newMarkers);

    // Fit bounds to show all markers
    if (newMarkers.length > 0) {
      const bounds = new google.maps.LatLngBounds();
      newMarkers.forEach(marker => bounds.extend(marker.getPosition()));
      map.fitBounds(bounds);
    }
  }, [map, schools, infoWindow, onSchoolSelect]);

  // Update selected school marker
  useEffect(() => {
    if (!markers.length || !schools.length || !selectedSchool) return;

    markers.forEach((marker, index) => {
      const school = schools[index];
      if (!school) return;
      
      marker.setIcon(school.id === selectedSchool.id ? {
        path: google.maps.SymbolPath.CIRCLE,
        scale: 10,
        fillColor: '#3B82F6',
        fillOpacity: 1,
        strokeColor: '#ffffff',
        strokeWeight: 2
      } : null);
    });
  }, [selectedSchool, markers, schools]);

  if (loading) {
    return (
      <Card>
        <div className="flex justify-center items-center h-[400px]">
          <LoadingSpinner />
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <div className="p-4 text-red-400 text-center">
          {error.message}
        </div>
      </Card>
    );
  }

  return (
    <Card>
      <div 
        ref={mapContainerRef}
        className="w-full h-[500px] bg-gray-100 rounded-lg"
      />
    </Card>
  );
}