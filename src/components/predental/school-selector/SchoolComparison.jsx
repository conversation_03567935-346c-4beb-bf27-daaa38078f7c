import React, { useState, useEffect } from 'react';
import { Card } from '../../common';
import { supabase } from '../../../lib/supabase';

export default function SchoolComparison({ selectedSchools, onSchoolSelect }) {
  const [schools, setSchools] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (selectedSchools?.length > 0) {
      loadSchoolDetails();
    }
  }, [selectedSchools]);

  const loadSchoolDetails = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('dental_schools')
        .select(`
          *,
          school_programs (
            program_name,
            degree_type,
            description
          ),
          clinic_services (
            service_name,
            description,
            price_range
          )
        `)
        .in('id', selectedSchools);

      if (error) throw error;
      setSchools(data);
    } catch (err) {
      console.error('Error loading school details:', err);
      setError('Failed to load school details');
    } finally {
      setLoading(false);
    }
  };

  if (!selectedSchools?.length) {
    return null;
  }

  if (loading) {
    return (
      <Card>
        <div className="flex justify-center items-center h-32">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <div className="text-red-400 text-center p-4">
          {error}
        </div>
      </Card>
    );
  }

  return (
    <Card>
      <h3 className="text-xl font-semibold text-white mb-6">School Comparison</h3>
      <div className="overflow-x-auto">
        <table className="w-full text-left">
          <thead>
            <tr className="border-b border-white/10">
              <th className="py-3 px-4">School</th>
              <th className="py-3 px-4">Location</th>
              <th className="py-3 px-4">Programs</th>
              <th className="py-3 px-4">Clinic Info</th>
            </tr>
          </thead>
          <tbody className="text-white/80">
            {schools.map(school => (
              <tr key={school.id} className="border-b border-white/10">
                <td className="py-3 px-4">
                  <div>
                    <div className="font-medium text-white">{school.name}</div>
                    <a 
                      href={school.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-sm text-blue-400 hover:text-blue-300"
                    >
                      Visit Website
                    </a>
                  </div>
                </td>
                <td className="py-3 px-4">{`${school.city}, ${school.state}`}</td>
                <td className="py-3 px-4">
                  <div className="flex flex-wrap gap-2">
                    {school.school_programs?.map(program => (
                      <span 
                        key={program.program_name}
                        className="px-2 py-1 bg-blue-500/10 text-blue-400 text-xs rounded-full"
                      >
                        {program.program_name}
                      </span>
                    ))}
                  </div>
                </td>
                <td className="py-3 px-4">
                  <div className="space-y-1 text-sm">
                    {school.clinic_info && (
                      <p className="text-white/80">{school.clinic_info}</p>
                    )}
                    {school.student_clinic_hours && (
                      <p className="text-white/60">Hours: {school.student_clinic_hours}</p>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </Card>
  );
}