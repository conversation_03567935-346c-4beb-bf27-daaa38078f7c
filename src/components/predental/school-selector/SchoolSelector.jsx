import React, { useState, useEffect } from 'react';
import Select from 'react-select';
import { supabase } from '../../../lib/supabase';

export default function SchoolSelector({ selectedSchools, onSchoolSelect }) {
  const [schools, setSchools] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadSchools();
  }, []);

  const loadSchools = async () => {
    try {
      setLoading(true);
      setError(null);
      const { data, error } = await supabase
        .from('dental_schools')
        .select('id, name, state')
        .order('state', { ascending: true });

      if (error) throw error;
      if (!data?.length) {
        throw new Error('No dental schools found');
      }

      // Group schools by region/state
      const groupedSchools = data.reduce((acc, school) => {
        const state = school.state;
        if (!acc[state]) {
          acc[state] = [];
        }
        acc[state].push({
          value: school.id,
          label: school.name
        });
        return acc;
      }, {});

      // Convert to format needed by react-select
      const options = Object.entries(groupedSchools).map(([state, schools]) => ({
        label: state,
        options: schools
      }));

      setSchools(options);
    } catch (err) {
      console.error('Error loading schools:', err);
      setError(err.message || 'Failed to load dental schools');
    } finally {
      setLoading(false);
    }
  };

  const customStyles = {
    control: (base) => ({
      ...base,
      background: 'rgba(255, 255, 255, 0.1)',
      borderColor: 'rgba(255, 255, 255, 0.2)',
      '&:hover': {
        borderColor: 'rgba(255, 255, 255, 0.3)'
      }
    }),
    menu: (base) => ({
      ...base,
      background: 'rgba(31, 41, 55, 0.95)',
      backdropFilter: 'blur(8px)'
    }),
    option: (base, { isFocused, isSelected }) => ({
      ...base,
      background: isSelected 
        ? 'rgba(59, 130, 246, 0.5)'
        : isFocused 
          ? 'rgba(59, 130, 246, 0.2)'
          : 'transparent',
      color: 'white',
      '&:hover': {
        background: 'rgba(59, 130, 246, 0.3)'
      }
    }),
    singleValue: (base) => ({
      ...base,
      color: 'white'
    }),
    input: (base) => ({
      ...base,
      color: 'white'
    }),
    placeholder: (base) => ({
      ...base,
      color: 'rgba(255, 255, 255, 0.5)'
    }),
    group: (base) => ({
      ...base,
      padding: '8px 0'
    }),
    groupHeading: (base) => ({
      ...base,
      color: 'rgba(255, 255, 255, 0.7)',
      fontWeight: '600',
      fontSize: '0.9rem'
    })
  };

  if (error) {
    return (
      <div className="text-red-400 text-center p-4">
        {error}
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto">
      <Select
        isLoading={loading}
        options={schools}
        value={selectedSchools?.map(id => {
          const group = schools.find(g => 
            g.options.some(s => s.value === id)
          );
          return group?.options.find(s => s.value === id);
        })}
        onChange={(options) => onSchoolSelect(options?.map(opt => opt.value) || [])}
        styles={customStyles}
        isMulti
        closeMenuOnSelect={false}
        placeholder="Select a dental school to track requirements..."
        isClearable
      />
    </div>
  );
}