import React, { useState } from 'react';
import CourseForm from './CourseForm';

export default function CourseList({ courses, onDelete, onEdit }) {
  const [editingCourse, setEditingCourse] = useState(null);

  const handleEdit = (course) => {
    setEditingCourse(course);
  };

  const handleUpdate = (updatedCourse) => {
    onEdit(editingCourse.id, updatedCourse);
    setEditingCourse(null);
  };

  if (editingCourse) {
    return (
      <CourseForm
        initialData={editingCourse}
        onSubmit={handleUpdate}
        onCancel={() => setEditingCourse(null)}
      />
    );
  }

  if (!courses.length) {
    return (
      <div className="text-center py-8 text-white/60">
        No courses added yet. Click "Add Course" to get started.
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {courses.map(course => (
        <div
          key={course.id}
          className="bg-white/5 rounded-lg p-4 flex justify-between items-start"
        >
          <div className="space-y-1">
            <div className="flex items-center gap-3">
              <h4 className="font-medium text-white">{course.name}</h4>
              <span className="px-2 py-1 bg-blue-500/10 text-blue-400 text-xs rounded-full">
                {course.type.replace('_', ' ')}
              </span>
              <span className="text-white/60">
                {course.semester} {course.year}
              </span>
            </div>
            <div className="text-sm text-white/80">
              <span className="mr-4">Credits: {course.credits}</span>
              <span className="mr-4">Grade: {course.grade}</span>
            </div>
            {course.notes && (
              <p className="text-sm text-white/60 mt-2">{course.notes}</p>
            )}
          </div>
          
          <div className="flex gap-2">
            <button
              onClick={() => handleEdit(course)}
              className="p-2 text-white/60 hover:text-white transition-colors"
            >
              <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            </button>
            <button
              onClick={() => onDelete(course.id)}
              className="p-2 text-red-400 hover:text-red-300 transition-colors"
            >
              <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </button>
          </div>
        </div>
      ))}
    </div>
  );
}