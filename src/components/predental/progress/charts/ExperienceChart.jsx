import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveC<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from 'recharts';
import { useExperience } from '../../../../lib/hooks/useExperience';

export default function ExperienceChart({ selectedSchool }) {
  const { experience } = useExperience();

  const data = [
    { name: 'Shadowing', value: 100, required: 100 },
    { name: 'Volunteering', value: 150, required: 150 },
    { name: 'Research', value: 50, required: 0 },
    { name: 'Leadership', value: 75, required: 50 }
  ];

  const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#8B5CF6'];

  return (
    <div className="h-[300px]">
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            innerRadius={60}
            outerRadius={80}
            fill="#8884d8"
            paddingAngle={5}
            dataKey="value"
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
          </Pie>
          <Tooltip
            contentStyle={{
              backgroundColor: '#1F2937',
              border: '1px solid #374151',
              borderRadius: '0.5rem'
            }}
            labelStyle={{ color: '#F3F4F6' }}
          />
          <Legend />
        </PieChart>
      </ResponsiveContainer>
    </div>
  );
}