import React from 'react';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON><PERSON><PERSON>s, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { useRequirements } from '../../../../lib/hooks/useRequirements';

export default function RequirementsChart({ selectedSchool }) {
  const { requirements } = useRequirements(selectedSchool?.id);

  const data = [
    { name: 'Biology', completed: 6, required: 8 },
    { name: 'Chemistry', completed: 8, required: 8 },
    { name: 'O-Chem', completed: 4, required: 8 },
    { name: 'Physics', completed: 4, required: 8 },
    { name: 'Biochem', completed: 0, required: 3 }
  ];

  return (
    <div className="h-[300px]">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={data}>
          <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
          <XAxis dataKey="name" stroke="#9CA3AF" />
          <YAxis stroke="#9CA3AF" />
          <Tooltip
            contentStyle={{
              backgroundColor: '#1F2937',
              border: '1px solid #374151',
              borderRadius: '0.5rem'
            }}
            labelStyle={{ color: '#F3F4F6' }}
          />
          <Bar dataKey="completed" fill="#3B82F6" name="Completed Credits" />
          <Bar dataKey="required" fill="#6B7280" name="Required Credits" />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
}