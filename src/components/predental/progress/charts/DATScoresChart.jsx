import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, XAxis, <PERSON><PERSON><PERSON>s, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { useDATScores } from '../../../../lib/hooks/useDATScores';

export default function DATScoresChart({ selectedSchool }) {
  const { scores } = useDATScores();

  const data = [
    { name: 'Practice 1', Biology: 19, Chemistry: 18, PAT: 20, Reading: 20, QR: 19 },
    { name: 'Practice 2', Biology: 20, Chemistry: 19, PAT: 21, Reading: 21, QR: 20 },
    { name: 'Practice 3', Biology: 21, Chemistry: 20, PAT: 22, Reading: 22, QR: 21 }
  ];

  return (
    <div className="h-[300px]">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={data}>
          <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
          <XAxis dataKey="name" stroke="#9CA3AF" />
          <YAxis domain={[15, 30]} stroke="#9CA3AF" />
          <Tooltip
            contentStyle={{
              backgroundColor: '#1F2937',
              border: '1px solid #374151',
              borderRadius: '0.5rem'
            }}
            labelStyle={{ color: '#F3F4F6' }}
          />
          <Line type="monotone" dataKey="Biology" stroke="#3B82F6" />
          <Line type="monotone" dataKey="Chemistry" stroke="#10B981" />
          <Line type="monotone" dataKey="PAT" stroke="#F59E0B" />
          <Line type="monotone" dataKey="Reading" stroke="#EC4899" />
          <Line type="monotone" dataKey="QR" stroke="#8B5CF6" />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
}