import React from 'react';
import { Card } from '../../common';

export default function GPAStats({ stats }) {
  return (
    <Card>
      <h3 className="text-xl font-semibold text-white mb-6">GPA Overview</h3>
      
      <div className="grid md:grid-cols-2 gap-6">
        <div className="bg-white/5 rounded-lg p-6">
          <div className="flex justify-between items-center mb-4">
            <h4 className="text-lg font-medium text-white">Overall GPA</h4>
            <span className="text-3xl font-bold text-blue-400">
              {stats.overall.toFixed(2)}
            </span>
          </div>
          <p className="text-white/60">
            Total Credits: {stats.totalCredits}
          </p>
        </div>

        <div className="bg-white/5 rounded-lg p-6">
          <div className="flex justify-between items-center mb-4">
            <h4 className="text-lg font-medium text-white">Science GPA</h4>
            <span className="text-3xl font-bold text-green-400">
              {stats.science.toFixed(2)}
            </span>
          </div>
          <p className="text-white/60">
            Science Credits: {stats.scienceCredits}
          </p>
        </div>
      </div>

      <div className="mt-6 p-4 bg-blue-500/10 rounded-lg">
        <div className="flex items-start gap-3">
          <div className="text-2xl">💡</div>
          <div>
            <p className="text-white/80">
              Most dental schools require a minimum GPA of 3.0, but competitive applicants typically have:
            </p>
            <ul className="mt-2 space-y-1 text-white/70">
              <li>• Overall GPA: 3.5+</li>
              <li>• Science GPA: 3.4+</li>
            </ul>
          </div>
        </div>
      </div>
    </Card>
  );
}