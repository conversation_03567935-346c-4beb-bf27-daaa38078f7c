import React from 'react';
import { Card } from '../../common';

const REQUIREMENTS = {
  BIOLOGY: {
    name: 'Biology',
    required: 8,
    description: 'General Biology with lab'
  },
  CHEMISTRY: {
    name: 'Chemistry',
    required: 8,
    description: 'General Chemistry with lab'
  },
  ORGANIC_CHEMISTRY: {
    name: 'Organic Chemistry',
    required: 8,
    description: 'Organic Chemistry with lab'
  },
  PHYSICS: {
    name: 'Physics',
    required: 8,
    description: 'Physics with lab'
  },
  BIOCHEMISTRY: {
    name: 'Biochemistry',
    required: 3,
    description: 'Biochemistry lecture'
  }
};

export default function RequirementsProgress({ courses }) {
  const getCompletedCredits = (type) => {
    return courses
      .filter(course => course.type === type)
      .reduce((sum, course) => sum + course.credits, 0);
  };

  const calculateProgress = (type) => {
    const completed = getCompletedCredits(type);
    const required = REQUIREMENTS[type].required;
    return Math.min((completed / required) * 100, 100);
  };

  return (
    <Card>
      <h3 className="text-xl font-semibold text-white mb-6">
        Course Requirements Progress
      </h3>

      <div className="space-y-6">
        {Object.entries(REQUIREMENTS).map(([type, info]) => {
          const completed = getCompletedCredits(type);
          const progress = calculateProgress(type);
          
          return (
            <div key={type} className="space-y-2">
              <div className="flex justify-between items-center">
                <div>
                  <h4 className="font-medium text-white">{info.name}</h4>
                  <p className="text-sm text-white/60">{info.description}</p>
                </div>
                <div className="text-right">
                  <span className="text-white">
                    {completed}/{info.required} credits
                  </span>
                </div>
              </div>
              
              <div className="h-2 bg-white/10 rounded-full overflow-hidden">
                <div 
                  className="h-full bg-blue-500 rounded-full transition-all duration-500"
                  style={{ width: `${progress}%` }}
                />
              </div>
            </div>
          );
        })}
      </div>

      <div className="mt-8 p-4 bg-blue-500/10 rounded-lg">
        <h4 className="font-medium text-white mb-2">Additional Requirements</h4>
        <ul className="space-y-2 text-white/80">
          <li>• English Composition (6 credits)</li>
          <li>• Mathematics/Statistics (3 credits)</li>
          <li>• Social Sciences/Humanities (varies by school)</li>
        </ul>
      </div>
    </Card>
  );
}