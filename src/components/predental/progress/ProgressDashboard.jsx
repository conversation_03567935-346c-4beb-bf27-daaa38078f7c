import React from 'react';
import { Card, CardGrid } from '../../common';
import {
  BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer,
  <PERSON><PERSON><PERSON>, Pie, Cell
} from 'recharts';

const COLORS = ['#60A5FA', '#34D399', '#F87171', '#FBBF24'];

export default function ProgressDashboard({ selectedSchools }) {
  const primarySchool = selectedSchools?.[0];
  
  // Example data - in a real app, this would come from your backend
  const courseProgress = [
    { name: 'Biology', required: 8, completed: 6 },
    { name: 'Chemistry', required: 8, completed: 8 },
    { name: 'Organic Chem', required: 8, completed: 4 },
    { name: 'Physics', required: 8, completed: 2 }
  ];

  const datScores = [
    { name: 'Biology', score: 20 },
    { name: 'Gen Chem', score: 21 },
    { name: 'Org Chem', score: 19 },
    { name: 'PAT', score: 22 },
    { name: 'QR', score: 20 },
    { name: '<PERSON>', score: 21 }
  ];

  const experienceData = [
    { name: 'Shadowing', value: 75 }, // percentage of target
    { name: 'Volunteering', value: 60 },
    { name: 'Research', value: 30 },
    { name: 'Leadership', value: 85 }
  ];

  return (
    <div className="space-y-8">
      <div className="grid gap-6 md:grid-cols-3">
        <Card>
          <h3 className="text-lg font-semibold text-white mb-4">
            {primarySchool ? `GPA Requirements for ${ACCREDITED_SCHOOLS.find(s => s.id === primarySchool)?.name}` : 'GPA Overview'}
          </h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-white/80">Overall GPA</span>
              <span className="text-2xl font-bold text-white">3.75</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-white/80">Science GPA</span>
              <span className="text-2xl font-bold text-white">3.82</span>
            </div>
          </div>
        </Card>

        <Card>
          <h3 className="text-lg font-semibold text-white mb-4">
            {primarySchool ? `DAT Requirements for ${ACCREDITED_SCHOOLS.find(s => s.id === primarySchool)?.name}` : 'DAT Score'}
          </h3>
          <div className="flex justify-between items-center">
            <span className="text-white/80">Academic Average</span>
            <span className="text-2xl font-bold text-white">20</span>
          </div>
        </Card>

        <Card>
          <h3 className="text-lg font-semibold text-white mb-4">
            {primarySchool ? `Experience Requirements for ${ACCREDITED_SCHOOLS.find(s => s.id === primarySchool)?.name}` : 'Experience Hours'}
          </h3>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-white/80">Shadowing</span>
              <span className="text-xl font-bold text-white">75/100 hrs</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-white/80">Volunteering</span>
              <span className="text-xl font-bold text-white">90/150 hrs</span>
            </div>
          </div>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <h3 className="text-lg font-semibold text-white mb-4">Course Progress</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={courseProgress}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="completed" fill="#60A5FA" name="Completed Credits" />
                <Bar dataKey="required" fill="#34D399" name="Required Credits" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </Card>

        <Card>
          <h3 className="text-lg font-semibold text-white mb-4">DAT Section Scores</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={datScores}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis domain={[1, 30]} />
                <Tooltip />
                <Bar dataKey="score" fill="#60A5FA" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </Card>
      </div>

      <Card>
        <h3 className="text-lg font-semibold text-white mb-4">Experience Progress</h3>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={experienceData}
                cx="50%"
                cy="50%"
                labelLine={false}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
                label={({ name, value }) => `${name}: ${value}%`}
              >
                {experienceData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </Card>
    </div>
  );
}