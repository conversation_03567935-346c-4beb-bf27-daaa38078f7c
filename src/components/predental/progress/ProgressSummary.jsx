import React from 'react';
import { Card } from '../../common';

export default function ProgressSummary({ selectedSchool }) {
  const getCompletionPercentage = () => {
    return selectedSchool ? '75%' : '0%';
  };

  return (
    <Card>
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h3 className="text-xl font-semibold text-white">
            {selectedSchool ? `Progress for ${selectedSchool.name}` : 'Select a School to Track Progress'}
          </h3>
          <span className="text-2xl font-bold text-blue-400">{getCompletionPercentage()}</span>
        </div>

        {selectedSchool && (
          <div className="grid gap-4 md:grid-cols-3">
            <div className="bg-white/5 rounded-lg p-4">
              <h4 className="text-sm font-medium text-white/80 mb-1">Course Requirements</h4>
              <p className="text-2xl font-semibold text-white">22/32 Credits</p>
            </div>
            <div className="bg-white/5 rounded-lg p-4">
              <h4 className="text-sm font-medium text-white/80 mb-1">DAT Score (Latest)</h4>
              <p className="text-2xl font-semibold text-white">20 AA</p>
            </div>
            <div className="bg-white/5 rounded-lg p-4">
              <h4 className="text-sm font-medium text-white/80 mb-1">Experience Hours</h4>
              <p className="text-2xl font-semibold text-white">375 hrs</p>
            </div>
          </div>
        )}
      </div>
    </Card>
  );
}