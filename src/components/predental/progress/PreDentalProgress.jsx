import React, { useState } from 'react';
import { useUser } from '../../../contexts/UserContext';
import { usePreDentalProgress } from '../../../lib/hooks/usePreDentalProgress';
import { Card } from '../../common';
import CourseForm from './CourseForm';
import CourseList from './CourseList';
import GPAStats from './GPAStats';
import RequirementsProgress from './RequirementsProgress';
import AuthPrompt from '../../auth/AuthPrompt';

export default function PreDentalProgress() {
  const { user } = useUser();
  const { 
    courses, 
    gpaStats, 
    loading, 
    error,
    addCourse,
    updateCourse,
    deleteCourse
  } = usePreDentalProgress(user?.id);
  const [showForm, setShowForm] = useState(false);

  const hasData = courses.length > 0;

  if (!user) {
    return <AuthPrompt message="Sign in to track your pre-dental progress" />;
  }

  if (loading) {
    return (
      <Card>
        <div className="flex justify-center items-center h-32">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <div className="text-red-400 text-center py-4">
          {error}
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-8">
      {!hasData && (
        <Card>
          <div className="text-center py-8">
            <h3 className="text-xl font-semibold text-white mb-4">
              Welcome to Pre-Dental Progress Tracking!
            </h3>
            <p className="text-white/80 mb-6">
              Start by adding your courses to track your GPA and requirements progress.
            </p>
            <button
              onClick={() => setShowForm(true)}
              className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-400 transition-colors"
            >
              Add Your First Course
            </button>
          </div>
        </Card>
      )}

      <GPAStats stats={gpaStats} />
      
      <Card>
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-xl font-semibold text-white">Course Tracker</h3>
          <button
            onClick={() => setShowForm(true)}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-400 transition-colors"
          >
            Add Course
          </button>
        </div>

        {showForm && (
          <CourseForm
            onSubmit={async (data) => {
              await addCourse(data);
              setShowForm(false);
            }}
            onCancel={() => setShowForm(false)}
          />
        )}

        <CourseList
          courses={courses}
          onDelete={deleteCourse}
          onEdit={updateCourse}
        />
      </Card>

      <RequirementsProgress 
        courses={courses}
        gpaStats={gpaStats}
      />
    </div>
  );
}