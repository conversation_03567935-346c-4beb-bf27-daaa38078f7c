import React, { useState } from 'react';
import { Card } from '../common';
import RequirementsList from './RequirementsList';
import TimelineTracker from './TimelineTracker';
import ResourceLibrary from './ResourceLibrary';
import ApplicationCountdown from './ApplicationCountdown';
import AdmissionStats from './stats/AdmissionStats';
import TestimonialsSection from './TestimonialsSection';
import ProgressDashboard from './progress/ProgressDashboard';
import SchoolSelector from './school-selector/SchoolSelector';
import SchoolComparison from './school-selector/SchoolComparison';
import CourseTracker from './courses/CourseTracker';
import FinancialResources from './resources/FinancialResources';
import OfficialResources from './resources/OfficialResources';
import FloatingAssistant from './FloatingAssistant';
import FileUpload from '../FileUpload';
import STLViewer from '../STLViewer';
import ThreeDMouthModel from './ThreeDMouthModel';

export default function PreDentalTracker() {
  const [activeTab, setActiveTab] = useState('courses');
  const [selectedSchools, setSelectedSchools] = useState([]);
  const [stlFile, setStlFile] = useState(null);
  const [viewerError, setViewerError] = useState(null);
  const [viewerKey, setViewerKey] = useState(0);

  const handleFileUpload = (url) => {
    setStlFile(url);
    setViewerKey(prev => prev + 1); // Force viewer remount
    setViewerError(null);
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-900 via-blue-900/20 to-purple-900/20 py-24">
      {/* Animated background elements */}
      <div className="fixed inset-0 pointer-events-none">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-900/20 via-gray-900 to-purple-900/20" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_top_right,rgba(59,130,246,0.1),transparent_50%)]" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_bottom_left,rgba(147,51,234,0.1),transparent_50%)]" />
      </div>
      <div className="relative">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-purple-400 to-blue-400 mb-4">
            Pre-Dental Journey Tracker
          </h1>
          <p className="text-xl text-blue-100/80 mb-4">Track your progress, access resources, and connect with future dentists</p>
          <ApplicationCountdown />
          <div className="max-w-3xl mx-auto p-4 bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-blue-500/10 rounded-lg border border-blue-500/20 hover:border-blue-400/30 transition-all duration-300">
            <p className="text-white/90">
              "There's no testimony without a test" - Your journey is unique, and every challenge is an opportunity to grow. 
              Use this tracker to monitor your progress and stay motivated!
            </p>
          </div>
        </div>

        <div className="mb-8">
          <SchoolSelector
            selectedSchools={selectedSchools} 
            onSchoolSelect={setSelectedSchools}
          />
          <SchoolComparison 
            selectedSchools={selectedSchools}
            onSchoolSelect={(id) => setSelectedSchools([id])}
          />

          <div className="mt-8 p-6 bg-white/5 backdrop-blur-sm rounded-lg border border-white/10">
            <h3 className="text-xl font-semibold text-white mb-4">Dental Scan Viewer</h3>
            <p className="text-white/80 mb-6">
              Upload your dental scan files (STL format) to view them in 3D. This can help you better understand your dental anatomy and treatment plans.
            </p>
            
            <FileUpload onUpload={handleFileUpload} />
            
            {stlFile && (
              <div className="mt-6">
                <div className="bg-gray-900 rounded-lg overflow-hidden" style={{ height: '400px' }}>
                  <STLViewer 
                    key={viewerKey}
                    modelUrl={stlFile} 
                    onError={setViewerError}
                  />
                </div>
                {viewerError && (
                  <div className="mt-2 text-red-400 text-sm">
                    {viewerError}
                  </div>
                )}
              </div>
            )}
          </div>

          <div className="mt-8 p-6 bg-white/5 backdrop-blur-sm rounded-lg border border-white/10">
            <h3 className="text-xl font-semibold text-white mb-4">3D Mouth Model</h3>
            <p className="text-white/80 mb-6">
              Explore a realistic 3D model of the human mouth to better understand dental anatomy.
            </p>
            <ThreeDMouthModel />
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="flex justify-center mb-8 space-x-4">
          {[
            { id: 'courses', label: 'Course Tracker' },
            { id: 'progress', label: 'My Progress' },
            { id: 'stats', label: 'Admission Stats' },
            { id: 'financial', label: 'Financial Resources' },
            { id: 'requirements', label: 'Requirements' },
            { id: 'resources', label: 'Official Resources' },
            { id: 'timeline', label: 'Timeline' },
            { id: 'testimonials', label: 'Success Stories' }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`px-6 py-2 rounded-full text-sm font-medium transition-colors duration-200
                ${activeTab === tab.id 
                  ? 'bg-blue-500 text-white' 
                  : 'bg-white/10 text-white/80 hover:bg-white/20'}`}
            >
              {tab.label}
            </button>
          ))}
        </div>

        {/* Content Sections */}
        <div className="space-y-8">
          <div className="bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 hover:border-blue-400/30 transition-all duration-300 p-6">
            {activeTab === 'courses' && <CourseTracker />}
            {activeTab === 'progress' && <ProgressDashboard selectedSchools={selectedSchools} />}
            {activeTab === 'stats' && <AdmissionStats />}
            {activeTab === 'financial' && <FinancialResources />}
            {activeTab === 'requirements' && <RequirementsList selectedSchools={selectedSchools} />}
            {activeTab === 'resources' && <OfficialResources />}
            {activeTab === 'timeline' && <TimelineTracker />}
            {activeTab === 'testimonials' && <TestimonialsSection />}
          </div>
        </div>

        <div className="mt-8 p-6 bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-blue-500/10 rounded-lg border border-blue-500/20 hover:border-blue-400/30 transition-all duration-300">
          <h3 className="text-lg font-semibold text-white mb-4">Need Help?</h3>
          <p className="text-white/80">
            Remember, every dental student's journey is different. If you're feeling overwhelmed or have questions,
            connect with our community or reach out to pre-dental advisors. Your determination matters more than perfect numbers!
          </p>
        </div>

        {/* Smilo World Section */}
        <div className="mt-8 p-6 bg-gradient-to-r from-blue-800/80 via-purple-900/80 to-blue-800/80 rounded-lg border border-blue-500/50 hover:border-blue-400/60 transition-all duration-300">
          <div className="flex flex-col md:flex-row items-center justify-between gap-8">
            <div className="w-full md:w-1/3 flex justify-center">
              <div className="w-48 h-48 md:w-full md:max-w-[200px] relative">
                {/* Enhanced but contained glow effect */}
                <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-indigo-400/50 to-purple-500/50 rounded-full filter blur-xl"></div>
                
                {/* Logo container with position relative to prevent page shifts */}
                <div className="relative z-10" style={{ transform: 'translateZ(0)' }}>
                  {/* Ring with fixed size */}
                  <div className="absolute -inset-1 bg-gradient-to-r from-blue-400/30 via-purple-500/30 to-blue-400/30 rounded-full blur-sm"></div>
                  
                  {/* Logo with static effects */}
                  <div className="relative">
                    <img 
                      src="/images/smilo-world.jpg" 
                      alt="Smilo World Logo" 
                      className="w-full h-full object-contain rounded-full drop-shadow-2xl"
                    />
                    
                    {/* Subtle inner glow */}
                    <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-500/20 via-purple-400/20 to-blue-500/20 rounded-full blur-sm"></div>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="w-full md:w-2/3 text-center md:text-left">
              <h3 className="text-2xl font-bold text-white mb-4">Are you interested in having a better pre-dental experience?</h3>
              <p className="text-white text-lg mb-6">
                Join Smilo World and discover a comprehensive platform designed to enhance your pre-dental journey with exclusive resources, personalized guidance, and an innovative community of future dental professionals.
              </p>
              <div className="flex justify-center md:justify-start">
                <a 
                  href="https://smilo.world/" 
                  target="_blank" 
                  rel="noopener noreferrer" 
                  className="px-8 py-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full text-white font-medium text-lg transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-blue-500/50 flex items-center gap-2"
                >
                  <span>Join Smilo World</span>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </svg>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
      </div>
      <FloatingAssistant />
    </div>
  );
}