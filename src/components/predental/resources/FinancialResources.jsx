import React from 'react';
import { Card } from '../../common';
import { FINANCIAL_RESOURCES, FINANCIAL_PLANNING_TIPS } from '../../../lib/constants/financialResources';

// Helper function to safely map over arrays
const safeMap = (array, callback) => {
  return Array.isArray(array) ? array.map(callback) : [];
};

export default function FinancialResources() {
  return (
    <div className="space-y-8">
      {/* Cost Awareness Section */}
      <Card>
        <h2 className="text-2xl font-bold text-white mb-6">Understanding the Investment</h2>
        <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-6 mb-8">
          <p className="text-yellow-200 font-medium mb-4">
            Important Notice About Dental School Costs
          </p>
          <p className="text-white/80">
            Dental education represents a significant financial investment. Understanding these costs
            upfront is crucial for making informed decisions about your career path and financial future.
            The information below will help you evaluate if this investment aligns with your goals and
            financial capabilities.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white/5 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-2">Public School (In-State)</h3>
            <p className="text-3xl font-bold text-blue-400 mb-2">{FINANCIAL_RESOURCES.costs.totalCost.publicResident}</p>
            <p className="text-white/60 text-sm">Total 4-year cost estimate</p>
          </div>
          <div className="bg-white/5 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-2">Public School (Out-of-State)</h3>
            <p className="text-3xl font-bold text-purple-400 mb-2">{FINANCIAL_RESOURCES.costs.totalCost.publicNonResident}</p>
            <p className="text-white/60 text-sm">Total 4-year cost estimate</p>
          </div>
          <div className="bg-white/5 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-2">Private School</h3>
            <p className="text-3xl font-bold text-red-400 mb-2">{FINANCIAL_RESOURCES.costs.totalCost.private}</p>
            <p className="text-white/60 text-sm">Total 4-year cost estimate</p>
          </div>
        </div>

        <div className="space-y-6">
          {safeMap(FINANCIAL_RESOURCES.costs.additionalExpenses, (expense, index) => (
            <div key={index} className="bg-white/5 rounded-lg p-6">
              <div className="flex justify-between items-start mb-4">
                <h4 className="text-lg font-medium text-white">{expense.category}</h4>
                <span className="px-4 py-1 bg-blue-500/10 text-blue-400 rounded-full">
                  {expense.range}/year
                </span>
              </div>
              <ul className="list-disc list-inside text-white/70 space-y-1">
                {safeMap(expense.items, (item, i) => (
                  <li key={i}>{item}</li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </Card>

      {/* Scholarships Section */}
      <Card>
        <h2 className="text-2xl font-bold text-white mb-6">Major Scholarship Programs</h2>
        <div className="space-y-6">
          {safeMap(FINANCIAL_RESOURCES.scholarships.programs, (program, index) => (
            <div key={index} className="bg-white/5 rounded-lg p-6">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="text-xl font-semibold text-white mb-2">{program.name}</h3>
                  <p className="text-blue-400">{program.provider}</p>
                </div>
                <a
                  href={program.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center text-blue-400 hover:text-blue-300"
                >
                  Learn More
                  <svg className="w-4 h-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                </a>
              </div>

              <div className="mb-4">
                <h4 className="text-sm font-medium text-white/90 mb-2">Benefits:</h4>
                <ul className="list-disc list-inside text-white/70 space-y-1">
                  {safeMap(program.benefits, (benefit, i) => (
                    <li key={i}>{benefit}</li>
                  ))}
                </ul>
              </div>

              {program.eligibility && (
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-white/90 mb-2">Eligibility:</h4>
                  <ul className="list-disc list-inside text-white/70 space-y-1">
                    {safeMap(program.eligibility, (req, i) => (
                      <li key={i}>{req}</li>
                    ))}
                  </ul>
                </div>
              )}

              {program.commitment && (
                <div className="mt-4 p-3 bg-blue-500/10 rounded-lg">
                  <p className="text-white/90">
                    <span className="font-medium">Service Commitment:</span> {program.commitment}
                  </p>
                </div>
              )}
            </div>
          ))}
        </div>
      </Card>

      {/* Loans Section */}
      <Card>
        <h2 className="text-2xl font-bold text-white mb-6">Loan Options</h2>
        <div className="space-y-6">
          {safeMap(FINANCIAL_RESOURCES.loans.options, (loan, index) => (
            <div key={index} className="bg-white/5 rounded-lg p-6">
              <h3 className="text-xl font-semibold text-white mb-4">{loan.type}</h3>
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <p className="text-sm font-medium text-white/90 mb-2">Details:</p>
                  <p className="text-white/80">Interest Rate: {loan.details.interestRate}</p>
                  <p className="text-white/80">Limit: {loan.details.limit}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-white/90 mb-2">Features:</p>
                  <ul className="list-disc list-inside text-white/70 space-y-1">
                    {safeMap(loan.details.features, (feature, i) => (
                      <li key={i}>{feature}</li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Repayment Strategies */}
      <Card>
        <h2 className="text-2xl font-bold text-white mb-6">Loan Repayment Strategies</h2>
        <div className="space-y-6">
          {safeMap(FINANCIAL_RESOURCES.repaymentStrategies.programs, (program, index) => (
            <div key={index} className="bg-white/5 rounded-lg p-6">
              <h3 className="text-xl font-semibold text-white mb-2">{program.name}</h3>
              <p className="text-white/80 mb-4">{program.description}</p>

              {program.eligibility && (
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-white/90 mb-2">Eligibility Requirements:</h4>
                  <ul className="list-disc list-inside text-white/70 space-y-1">
                    {safeMap(program.eligibility, (req, i) => (
                      <li key={i}>{req}</li>
                    ))}
                  </ul>
                </div>
              )}

              {program.benefits && (
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-white/90 mb-2">Benefits:</h4>
                  <ul className="list-disc list-inside text-white/70 space-y-1">
                    {safeMap(program.benefits, (benefit, i) => (
                      <li key={i}>{benefit}</li>
                    ))}
                  </ul>
                </div>
              )}

              {program.website && (
                <a
                  href={program.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center text-blue-400 hover:text-blue-300 mt-2"
                >
                  Learn More
                  <svg className="w-4 h-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                </a>
              )}
            </div>
          ))}
        </div>
      </Card>

      {/* Financial Planning Tips */}
      <Card>
        <h2 className="text-2xl font-bold text-white mb-6">Financial Planning Tips</h2>
        <div className="grid md:grid-cols-3 gap-6">
          {safeMap(FINANCIAL_PLANNING_TIPS, (section, index) => (
            <div key={index} className="bg-white/5 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-white mb-4">{section.title}</h3>
              <ul className="space-y-3">
                {safeMap(section.tips, (tip, i) => (
                  <li key={i} className="flex items-start">
                    <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 mr-2 flex-shrink-0" />
                    <span className="text-white/80">{tip}</span>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
}