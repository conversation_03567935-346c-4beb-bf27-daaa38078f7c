import React from 'react';
import { Card } from '../../common';
import { PRE_DENTAL_RESOURCES, PROFESSIONAL_ORGANIZATIONS } from '../../../lib/constants/preDentalResources';

export default function OfficialResources() {
  return (
    <div className="space-y-8">
      {/* Professional Organizations Section */}
      <Card>
        <h2 className="text-2xl font-bold text-white mb-6">Professional Organizations</h2>
        <p className="text-white/80 mb-8">
          Join professional organizations to enhance your pre-dental journey through networking,
          resources, and leadership opportunities.
        </p>

        {Object.entries(PROFESSIONAL_ORGANIZATIONS).map(([key, section]) => (
          <div key={key} className="mb-12">
            <h3 className="text-xl font-semibold text-white mb-4">
              {section.title}
            </h3>
            <p className="text-white/80 mb-6">{section.description}</p>
            
            <div className="grid gap-6">
              {section.organizations.map((org, index) => (
                <div
                  key={index}
                  className="bg-white/5 rounded-lg p-6 hover:bg-white/10 transition-all duration-300"
                >
                  <div className="flex justify-between items-start mb-4">
                    <h4 className="text-lg font-medium text-white">{org.name}</h4>
                    <a
                      href={org.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center text-blue-400 hover:text-blue-300"
                    >
                      <span className="mr-1">Join</span>
                      <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                      </svg>
                    </a>
                  </div>
                  
                  <p className="text-white/80 mb-4">{org.description}</p>
                  
                  <div className="mb-4">
                    <h5 className="text-sm font-medium text-white/90 mb-2">Key Benefits:</h5>
                    <ul className="list-disc list-inside text-white/70 space-y-1">
                      {org.benefits.map((benefit, i) => (
                        <li key={i}>{benefit}</li>
                      ))}
                    </ul>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-white/60">{org.membership}</span>
                    <span className="px-3 py-1 bg-blue-500/10 text-blue-400 rounded-full">
                      {org.type}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </Card>
      {/* Official Resources Section */}
      <h2 className="text-2xl font-bold text-white mb-6">Official Resources</h2>
      {Object.entries(PRE_DENTAL_RESOURCES).map(([key, section]) => (
        <Card key={key}>
          <h3 className="text-xl font-semibold text-white mb-4">
            {section.title}
          </h3>
          <p className="text-white/80 mb-6">{section.description}</p>
          
          <div className="grid gap-4">
            {section.resources.map((resource, index) => (
              <div
                key={index}
                className="bg-white/5 rounded-lg p-4 hover:bg-white/10 transition-colors"
              >
                <div className="flex justify-between items-start">
                  <div>
                    <h4 className="font-medium text-white mb-2">
                      {resource.title}
                    </h4>
                    <p className="text-white/70 text-sm mb-2">
                      {resource.description}
                    </p>
                    <div className="flex flex-wrap gap-2">
                      {resource.tags?.map(tag => (
                        <span
                          key={tag}
                          className="px-2 py-1 bg-blue-500/10 text-blue-400 text-xs rounded-full"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                  <a
                    href={resource.link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center text-blue-400 hover:text-blue-300"
                  >
                    <span className="mr-1">Visit</span>
                    <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                  </a>
                </div>
              </div>
            ))}
          </div>
        </Card>
      ))}
    </div>
  );
}