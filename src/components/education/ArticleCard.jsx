import React from 'react';

export default function ArticleCard({ article }) {
  const { title, summary, readTime } = article;
  
  return (
    <div className="bg-white/5 backdrop-blur-sm rounded-lg p-6 hover:bg-white/10 transition-colors duration-200 cursor-pointer border border-white/10">
      <h3 className="text-lg font-semibold text-white mb-2">{title}</h3>
      <p className="text-blue-100/80 mb-4">{summary}</p>
      <div className="text-sm text-blue-300">
        {readTime} read
      </div>
    </div>
  );
}