import React from 'react';
import { EDUCATIONAL_CATEGORIES } from '../../lib/constants/educationalContent';
import CategorySection from './CategorySection';

export default function EducationHub() {
  return (
    <div className="py-16 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-3xl font-bold text-white mb-4">Educational Resources</h1>
          <p className="text-xl text-blue-100/80">
            Explore our collection of dental health articles and guides
          </p>
        </div>
        
        {EDUCATIONAL_CATEGORIES.map(category => (
          <CategorySection key={category.title} category={category} />
        ))}
      </div>
    </div>
  );
}