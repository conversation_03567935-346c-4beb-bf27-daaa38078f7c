import React from 'react';
import ArticleCard from './ArticleCard';

export default function CategorySection({ category }) {
  const { title, articles } = category;
  
  return (
    <div className="mb-12">
      <h2 className="text-2xl font-semibold text-white mb-6">{title}</h2>
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {articles.map(article => (
          <ArticleCard key={article.id} article={article} />
        ))}
      </div>
    </div>
  );
}