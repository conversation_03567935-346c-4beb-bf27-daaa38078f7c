import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaArrowRight } from 'react-icons/fa';

/**
 * AdRotatingCarousel component that automatically rotates through ads
 * 
 * @param {Object} props
 * @param {Array} props.ads - Array of ad objects with title, description, image, link
 * @param {number} props.interval - Time in ms between rotations (default: 5000ms)
 * @param {string} props.title - Optional title above the carousel
 */
const AdRotatingCarousel = ({ 
  ads, 
  interval = 5000, 
  title = null,
  subtitle = null
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const timeoutRef = useRef(null);

  // Reset the timer when the currentIndex changes
  useEffect(() => {
    resetTimeout();
    timeoutRef.current = setTimeout(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % ads.length);
    }, interval);

    return () => {
      resetTimeout();
    };
  }, [currentIndex, interval, ads.length]);

  const resetTimeout = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  };

  // Handle manual navigation
  const goToNext = () => {
    resetTimeout();
    setCurrentIndex((prevIndex) => (prevIndex + 1) % ads.length);
  };

  const goToPrev = () => {
    resetTimeout();
    setCurrentIndex((prevIndex) => (prevIndex === 0 ? ads.length - 1 : prevIndex - 1));
  };

  if (!ads || ads.length === 0) {
    return null;
  }

  const currentAd = ads[currentIndex];

  return (
    <div className="my-12 max-w-5xl mx-auto px-4">
      {(title || subtitle) && (
        <div className="text-center mb-6">
          {title && <h2 className="text-2xl font-bold mb-2">{title}</h2>}
          {subtitle && <p className="text-gray-600">{subtitle}</p>}
        </div>
      )}
      
      <div className="relative bg-white rounded-lg shadow-lg overflow-hidden">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentIndex}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.5 }}
            className="w-full"
          >
            <div className="flex flex-col md:flex-row">
              {/* Image section */}
              <div className="w-full md:w-2/5 relative">
                <a href={currentAd.link} target="_blank" rel="noopener noreferrer">
                  <img 
                    src={currentAd.image} 
                    alt={currentAd.title} 
                    className="w-full h-64 md:h-full object-cover"
                  />
                  {currentAd.tag && (
                    <div className="absolute top-4 left-4 bg-blue-600 text-white text-xs font-bold px-2 py-1 rounded-full">
                      {currentAd.tag}
                    </div>
                  )}
                </a>
              </div>
              
              {/* Content section */}
              <div className="w-full md:w-3/5 p-6 flex flex-col">
                <div className="flex-grow">
                  <a 
                    href={currentAd.link} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="block hover:text-blue-600 transition-colors"
                  >
                    <h3 className="text-xl font-bold text-gray-800 mb-2">{currentAd.title}</h3>
                  </a>
                  <p className="text-gray-600 mb-4">{currentAd.description}</p>
                  
                  {currentAd.features && (
                    <div className="mb-6">
                      <h4 className="font-semibold text-gray-700 mb-2">Features:</h4>
                      <ul className="space-y-1">
                        {currentAd.features.map((feature, index) => (
                          <li key={index} className="flex items-start text-sm">
                            <span className="text-green-500 mr-2">✓</span>
                            <span>{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
                
                <div className="flex justify-between items-center mt-4">
                  {currentAd.price && (
                    <span className="text-blue-600 font-bold">{currentAd.price}</span>
                  )}
                  <a
                    href={currentAd.link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                  >
                    {currentAd.ctaText || 'View Details'} <FaArrowRight className="ml-2" />
                  </a>
                </div>
              </div>
            </div>
          </motion.div>
        </AnimatePresence>
        
        {/* Carousel controls */}
        <button 
          className="absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 rounded-full p-2 shadow hover:bg-white transition-colors z-10"
          onClick={goToPrev}
          aria-label="Previous ad"
        >
          <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        <button 
          className="absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 rounded-full p-2 shadow hover:bg-white transition-colors z-10"
          onClick={goToNext}
          aria-label="Next ad"
        >
          <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>
        
        {/* Indicators */}
        <div className="absolute bottom-4 left-0 right-0 flex justify-center space-x-2">
          {ads.map((_, index) => (
            <button
              key={index}
              onClick={() => {
                resetTimeout();
                setCurrentIndex(index);
              }}
              className={`w-2 h-2 rounded-full ${
                index === currentIndex ? 'bg-blue-600' : 'bg-gray-300'
              }`}
              aria-label={`Go to ad ${index + 1}`}
            />
          ))}
        </div>
      </div>
      
      <div className="text-center mt-4">
        <p className="text-xs text-gray-500">
          Advertisement: This content contains affiliate links. We may earn a commission if you make a purchase.
        </p>
      </div>
    </div>
  );
};

export default AdRotatingCarousel; 