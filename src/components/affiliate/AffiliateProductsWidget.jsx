import React from 'react';
import SingleAdRotator from './SingleAdRotator';

/**
 * AffiliateProductsWidget - A compact widget for displaying affiliate products
 * This component is designed to be used in sidebars and other compact spaces
 * 
 * @param {Object} props
 * @param {string} props.title - Optional widget title
 * @param {string} props.className - Additional CSS classes
 */
const AffiliateProductsWidget = ({ 
  title = 'Recommended Products',
  className = ''
}) => {
  return (
    <div className={`affiliate-widget ${className}`}>
      {title && (
        <div className="mb-3">
          <h3 className="text-lg font-medium">{title}</h3>
          <div className="h-px bg-gradient-to-r from-transparent via-blue-200 to-transparent my-2"></div>
        </div>
      )}
      
      <SingleAdRotator interval={7000} />
      
      <div className="text-center mt-3">
        <p className="text-xs text-gray-500">
          Products we recommend based on quality and value
        </p>
      </div>
    </div>
  );
};

export default AffiliateProductsWidget; 