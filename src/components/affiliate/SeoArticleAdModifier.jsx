import React, { useState, useEffect } from 'react';
import ReactMarkdown from 'react-markdown';
import rehypeRaw from 'rehype-raw';
import remarkGfm from 'remark-gfm';
import PRODUCTS, { getProxyImageUrl } from '../../lib/constants/adProducts';
import { motion } from 'framer-motion';

/**
 * InlineAdCard Component 
 * A simplified ad card component specifically for SEO articles
 */
const InlineAdCard = ({ position = 'right', size = 'medium' }) => {
  const [randomIndex, setRandomIndex] = useState(Math.floor(Math.random() * PRODUCTS.length));
  const [imgError, setImgError] = useState(false);
  
  // Auto-rotate ads
  useEffect(() => {
    const interval = setInterval(() => {
      setRandomIndex(prev => (prev + 1) % PRODUCTS.length);
      setImgError(false);
    }, 8000);
    
    return () => clearInterval(interval);
  }, []);
  
  const product = PRODUCTS[randomIndex];
  
  // Size classes
  const sizeClasses = {
    small: 'w-32 md:w-48',
    medium: 'w-48 md:w-64',
    large: 'w-64 md:w-80'
  };
  
  // Position classes
  const positionClasses = {
    left: 'float-left mr-4 mb-4',
    right: 'float-right ml-4 mb-4',
    center: 'mx-auto my-6'
  };
  
  // FallbackImage component for when image fails to load
  const FallbackImage = () => (
    <div className="h-28 flex flex-col items-center justify-center bg-gradient-to-b from-blue-900/30 to-indigo-900/30">
      <div className="w-10 h-10 rounded-full bg-blue-500/20 flex items-center justify-center mb-1">
        <svg className="h-5 w-5 text-blue-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      </div>
      <p className="text-xs text-blue-200">Product Image</p>
    </div>
  );

  return (
    <div className={`${sizeClasses[size]} ${positionClasses[position]} bg-white/10 backdrop-blur-sm rounded-lg border border-white/10 hover:border-blue-400/30 shadow-md overflow-hidden transition-all duration-300 hover:shadow-blue-500/20 hover:shadow-lg`}>
      <div className="absolute top-1 right-1 text-white/70 text-xs bg-blue-500/30 px-1.5 py-0.5 rounded-full z-10">
        Ad
      </div>
      <a 
        href={product.link} 
        target="_blank" 
        rel="noopener noreferrer sponsored"
        className="block w-full h-full"
      >
        <div className="flex flex-col items-center w-full h-full">
          <div className="w-full h-28 bg-gradient-to-b from-white/5 to-white/0 flex items-center justify-center p-2">
            {imgError ? (
              <FallbackImage />
            ) : (
              <img 
                src={getProxyImageUrl(product.image)}
                alt={product.name}
                className="max-h-full object-contain"
                onError={() => setImgError(true)}
                loading="lazy"
              />
            )}
          </div>
          <div className="p-2 text-center">
            <p className="text-white text-xs font-medium truncate">{product.name}</p>
            <p className="text-blue-400 text-xs">View on Amazon</p>
          </div>
        </div>
      </a>
    </div>
  );
};

/**
 * SeoArticleAdModifier - Enhances SEO articles with integrated ads
 * 
 * @param {Object} props
 * @param {string} props.content - The markdown content of the article
 */
const SeoArticleAdModifier = ({ content }) => {
  // Process the content to insert ad cards
  const processedContent = React.useMemo(() => {
    if (!content) return '';
    
    // Split content into paragraphs
    const paragraphs = content.split(/\n\n+/);
    
    // Add ad cards after specific paragraphs
    if (paragraphs.length >= 4) {
      // Insert first ad after the 3rd paragraph
      paragraphs.splice(3, 0, ':::ad-left:::');
      
      // If article is long enough, add more ads
      if (paragraphs.length >= 8) {
        paragraphs.splice(8, 0, ':::ad-right:::');
      }
      
      if (paragraphs.length >= 14) {
        paragraphs.splice(14, 0, ':::ad-left:::');
      }
    }
    
    return paragraphs.join('\n\n');
  }, [content]);

  return (
    <article className="prose prose-invert prose-lg max-w-none">
      <ReactMarkdown
        rehypePlugins={[rehypeRaw]}
        remarkPlugins={[remarkGfm]}
        components={{
          h1: ({ node, ...props }) => <h1 className="text-3xl lg:text-4xl font-bold mb-6 bg-gradient-to-r from-blue-400 via-indigo-400 to-purple-400 text-transparent bg-clip-text" {...props} />,
          h2: ({ node, ...props }) => <h2 className="text-2xl lg:text-3xl font-semibold mb-4 text-blue-300 mt-10" {...props} />,
          h3: ({ node, ...props }) => <h3 className="text-xl font-semibold mb-3 text-indigo-300 mt-8" {...props} />,
          h4: ({ node, ...props }) => <h4 className="text-lg font-medium mb-2 text-purple-300 mt-6" {...props} />,
          p: ({ node, children, ...props }) => {
            // Special case for ad markers
            if (typeof children === 'string' && children.startsWith(':::ad-')) {
              const position = children.includes('ad-left') ? 'left' : 'right';
              return <InlineAdCard position={position} size="medium" />;
            }
            return <p className="text-gray-300 leading-relaxed mb-4" {...props}>{children}</p>;
          },
          ul: ({ node, ...props }) => <ul className="list-disc pl-6 mb-6 text-gray-300" {...props} />,
          ol: ({ node, ...props }) => <ol className="list-decimal pl-6 mb-6 text-gray-300" {...props} />,
          li: ({ node, ...props }) => <li className="mb-2" {...props} />,
          a: ({ node, ...props }) => <a className="text-blue-400 hover:text-blue-300 underline" {...props} />,
          blockquote: ({ node, ...props }) => <blockquote className="border-l-4 border-blue-500 pl-4 py-2 my-4 bg-blue-900/20 rounded-r-lg text-gray-300 italic" {...props} />,
          code: ({ node, ...props }) => <code className="bg-gray-900 px-1 py-0.5 rounded text-pink-300" {...props} />,
          pre: ({ node, ...props }) => <pre className="bg-gray-900 p-4 rounded-lg overflow-x-auto text-gray-300 mb-6" {...props} />,
        }}
      >
        {processedContent}
      </ReactMarkdown>
    </article>
  );
};

export default SeoArticleAdModifier; 