import React, { useState, useEffect, useRef } from 'react';

// Product details for the ad cards
const PRODUCTS = [
  {
    name: "Philips Sonicare ProtectiveClean 5300",
    image: "https://m.media-amazon.com/images/I/71CgW3d+3QL._SL1500_.jpg",
    link: "https://www.amazon.com/Philips-Sonicare-ProtectiveClean-Rechargeable-HX6423/dp/B084TM4XKG?crid=2I8FRMR7EXDNG&linkCode=ll1&tag=jamevo-20&linkId=1c865c3a0f167bf8af6d1e2eba01d2f4&language=en_US&ref_=as_li_ss_tl"
  },
  {
    name: "Listerine Anticavity Mouthwash",
    image: "https://m.media-amazon.com/images/I/71h5+MbEZGL._AC_SL1500_.jpg",
    link: "https://www.amazon.com/Listerine-Anticavity-Mouthwash-Fluoride-Packaging/dp/B00495Q5OW?crid=2XFO85NDJAIED&linkCode=ll1&tag=jamevo-20&linkId=bab2cc306a6c4565a87b057d12fd151c&language=en_US&ref_=as_li_ss_tl"
  },
  {
    name: "Probiotiv Oral Probiotics",
    image: "https://m.media-amazon.com/images/I/711OoCc+VQL._AC_SL1500_.jpg",
    link: "https://www.amazon.com/Probiotiv-Probiotics-Mouth-Breath-Billion/dp/B0CJ23K7HR?crid=2GI0XNL7LTSL8&linkCode=ll1&tag=jamevo-20&linkId=c738517e7ee66bec5ddfb35abac9a45b&language=en_US&ref_=as_li_ss_tl"
  },
  {
    name: "Plackers Micro Mint Flossers",
    image: "https://m.media-amazon.com/images/I/81f0FJDGN8L._AC_SL1500_.jpg",
    link: "https://www.amazon.com/Plackers-Flossers-Fold-Out-Toothpick-Tuffloss/dp/B07ZW46KV4?crid=S5LNGKWS7ZVK&linkCode=ll1&tag=jamevo-20&linkId=debb8442858a57bf736f9e95d5ebc681&language=en_US&ref_=as_li_ss_tl"
  },
  {
    name: "Zollipops Clean Teeth Lollipops",
    image: "https://m.media-amazon.com/images/I/81l51z+Op-L._AC_SL1500_.jpg",
    link: "https://www.amazon.com/Zollipops-Lollipops-Delicious-Assorted-Flavors/dp/B0198DHO76?crid=1OQ2AVQCKQ89W&linkCode=ll1&tag=jamevo-20&linkId=d0696e7eb849ed00e14a98247664573a&language=en_US&ref_=as_li_ss_tl"
  },
  {
    name: "Sensodyne Pronamel Intensive Enamel Toothpaste",
    image: "https://m.media-amazon.com/images/I/61MrXdHDhWL._SL1500_.jpg",
    link: "https://www.amazon.com/Sensodyne-Pronamel-Intensive-Toothpaste-Strengthen/dp/B0DD35MGT1?crid=1QH3MQWPTOH8Y&linkCode=ll1&tag=jamevo-20&linkId=4b9aefb7ace3cef76f0a80e28f0dd432&language=en_US&ref_=as_li_ss_tl"
  }
];

/**
 * Function that transforms Amazon image URLs to use weserv.nl as a proxy
 * to avoid CORS issues
 */
const getProxyImageUrl = (amazonUrl) => {
  try {
    // Extract the Amazon image URL and encode it for the proxy
    const encodedUrl = encodeURIComponent(amazonUrl);
    return `https://images.weserv.nl/?url=${encodedUrl}&n=-1`;
  } catch (error) {
    console.error("Error creating proxy URL:", error);
    return null;
  }
};

/**
 * SimpleAdCards - Clean ad cards that auto-rotate and match the project theme
 * 
 * @param {Object} props
 * @param {number} props.interval - Rotation interval in ms (default: 5000)
 */
const SimpleAdCards = ({ interval = 5000 }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [secondIndex, setSecondIndex] = useState(1);
  const timeoutRef = useRef(null);
  const [imgError1, setImgError1] = useState(false);
  const [imgError2, setImgError2] = useState(false);
  
  // Auto rotate through the ads
  useEffect(() => {
    const nextTimeout = setTimeout(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % PRODUCTS.length);
      setSecondIndex((prevIndex) => {
        const nextIdx = (prevIndex + 1) % PRODUCTS.length;
        return (nextIdx + 1) % PRODUCTS.length;
      });
      // Reset image error states when rotating
      setImgError1(false);
      setImgError2(false);
    }, interval);
    
    timeoutRef.current = nextTimeout;
    
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [currentIndex, interval]);

  // Create a fallback image content when image fails to load
  const FallbackImage = ({ productName }) => (
    <div className="w-full h-full flex items-center justify-center bg-gradient-to-b from-blue-900/30 to-indigo-900/30">
      <div className="text-center p-4">
        <div className="w-16 h-16 mx-auto mb-2 rounded-full bg-blue-500/20 flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-blue-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        </div>
        <p className="text-blue-200 text-sm">Product Image</p>
      </div>
    </div>
  );

  return (
    <div className="flex gap-4 justify-center my-8">
      {/* First ad card - always visible */}
      <div className="relative w-full max-w-md bg-white/10 backdrop-blur-sm rounded-xl border border-white/10 hover:border-blue-400/30 shadow-md overflow-hidden transition-all duration-300 hover:shadow-blue-500/20 hover:shadow-lg">
        <div className="absolute top-2 right-2 text-white/70 text-xs bg-blue-500/30 px-2 py-0.5 rounded-full z-10">
          Ad
        </div>
        <a 
          href={PRODUCTS[currentIndex].link} 
          target="_blank" 
          rel="noopener noreferrer"
          className="block w-full h-full"
        >
          <div className="flex flex-col items-center w-full h-full">
            <div className="w-full h-40 bg-gradient-to-b from-white/5 to-white/0 flex items-center justify-center p-2">
              {imgError1 ? (
                <FallbackImage productName={PRODUCTS[currentIndex].name} />
              ) : (
                <img 
                  src={getProxyImageUrl(PRODUCTS[currentIndex].image)}
                  alt={PRODUCTS[currentIndex].name}
                  className="max-h-full object-contain"
                  onError={() => setImgError1(true)}
                  loading="lazy"
                />
              )}
            </div>
            <div className="p-4 text-center">
              <p className="text-white font-medium mb-1">{PRODUCTS[currentIndex].name}</p>
              <p className="text-blue-400 text-sm">View on Amazon</p>
            </div>
          </div>
        </a>
      </div>
      
      {/* Second ad card - only shown on larger screens */}
      <div className="relative hidden md:block w-full max-w-md bg-white/10 backdrop-blur-sm rounded-xl border border-white/10 hover:border-blue-400/30 shadow-md overflow-hidden transition-all duration-300 hover:shadow-blue-500/20 hover:shadow-lg">
        <div className="absolute top-2 right-2 text-white/70 text-xs bg-blue-500/30 px-2 py-0.5 rounded-full z-10">
          Ad
        </div>
        <a 
          href={PRODUCTS[secondIndex].link} 
          target="_blank" 
          rel="noopener noreferrer"
          className="block w-full h-full"
        >
          <div className="flex flex-col items-center w-full h-full">
            <div className="w-full h-40 bg-gradient-to-b from-white/5 to-white/0 flex items-center justify-center p-2">
              {imgError2 ? (
                <FallbackImage productName={PRODUCTS[secondIndex].name} />
              ) : (
                <img 
                  src={getProxyImageUrl(PRODUCTS[secondIndex].image)}
                  alt={PRODUCTS[secondIndex].name}
                  className="max-h-full object-contain"
                  onError={() => setImgError2(true)}
                  loading="lazy"
                />
              )}
            </div>
            <div className="p-4 text-center">
              <p className="text-white font-medium mb-1">{PRODUCTS[secondIndex].name}</p>
              <p className="text-blue-400 text-sm">View on Amazon</p>
            </div>
          </div>
        </a>
      </div>
    </div>
  );
};

export default SimpleAdCards; 