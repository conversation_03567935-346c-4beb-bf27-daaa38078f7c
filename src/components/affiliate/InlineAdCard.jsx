import React, { useState, useEffect, useCallback } from 'react';
import PRODUCTS, { getProxyImageUrl } from '../../lib/constants/adProducts';

/**
 * Fallback component to display when image fails to load
 */
const FallbackImage = ({ className }) => (
  <div className={`flex flex-col items-center justify-center p-2 bg-gray-100 dark:bg-gray-800 rounded ${className}`}>
    <svg 
      xmlns="http://www.w3.org/2000/svg" 
      className="h-8 w-8 text-gray-400 mb-1" 
      fill="none" 
      viewBox="0 0 24 24" 
      stroke="currentColor"
    >
      <path 
        strokeLinecap="round" 
        strokeLinejoin="round" 
        strokeWidth={1.5} 
        d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" 
      />
      <path 
        strokeLinecap="round" 
        strokeLinejoin="round" 
        strokeWidth={1.5} 
        d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" 
      />
    </svg>
    <span className="text-xs text-gray-500 dark:text-gray-400">Product Image</span>
  </div>
);

/**
 * Inline ad card component for articles
 * Can be positioned left, right, or center within content
 */
const InlineAdCard = ({ 
  position = 'right', // 'left', 'right', 'center'
  interval = 7000,    // rotation interval in ms
  size = 'medium'     // 'small', 'medium', 'large'
}) => {
  const [randomIndex, setRandomIndex] = useState(Math.floor(Math.random() * PRODUCTS.length));
  const [imageError, setImageError] = useState(false);
  
  // Determine width class based on size prop
  const sizeClasses = {
    small: 'w-full sm:w-1/3',
    medium: 'w-full sm:w-2/5',
    large: 'w-full sm:w-1/2'
  };
  
  // Determine position classes
  const positionClasses = {
    left: 'float-left mr-4 mb-4',
    right: 'float-right ml-4 mb-4',
    center: 'mx-auto mb-4'
  };
  
  const baseClasses = `${sizeClasses[size]} ${positionClasses[position]} rounded-lg overflow-hidden shadow-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 transition-all duration-300 hover:shadow-xl`;
  
  // Rotate products periodically
  const rotateProduct = useCallback(() => {
    setRandomIndex(prevIndex => {
      const newIndex = (prevIndex + 1) % PRODUCTS.length;
      return newIndex;
    });
    setImageError(false); // Reset error state on rotation
  }, []);
  
  useEffect(() => {
    const timer = setInterval(rotateProduct, interval);
    return () => clearInterval(timer);
  }, [rotateProduct, interval]);
  
  const product = PRODUCTS[randomIndex];
  
  return (
    <div className={baseClasses}>
      <a 
        href={product.link} 
        target="_blank" 
        rel="noopener noreferrer sponsored"
        className="block"
      >
        <div className="relative">
          {!imageError ? (
            <img
              src={getProxyImageUrl(product.image)}
              alt={product.name}
              className="w-full h-32 sm:h-40 object-contain bg-white p-2"
              onError={() => setImageError(true)}
              loading="lazy"
            />
          ) : (
            <FallbackImage className="w-full h-32 sm:h-40" />
          )}
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-2">
            <span className="text-xs text-white">Sponsored</span>
          </div>
        </div>
        <div className="p-3">
          <h3 className="text-sm font-medium text-gray-800 dark:text-gray-200 line-clamp-2">
            {product.name}
          </h3>
          <div className="mt-2 flex justify-between items-center">
            <span className="text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded-full">
              View on Amazon
            </span>
          </div>
        </div>
      </a>
    </div>
  );
};

export default InlineAdCard; 