import React, { useState, useRef } from 'react';
import { 
  getFeaturedProducts, 
  getProductsByCategory,
  getAllProducts 
} from '../../lib/constants/affiliateProducts';
import { FaArrowRight } from 'react-icons/fa';

const ProductCard = ({ product }) => {
  return (
    <div className="flex-shrink-0 w-64 bg-white rounded-lg shadow-md overflow-hidden">
      <div className="relative">
        {product.image && (
          <img 
            src={product.image} 
            alt={product.name}
            className="w-full h-48 object-cover"
          />
        )}
        {product.tag && (
          <div className="absolute top-2 right-2 bg-blue-600 text-white text-xs font-bold px-2 py-1 rounded-full">
            {product.tag}
          </div>
        )}
      </div>
      
      <div className="p-4">
        <h3 className="text-lg font-bold text-gray-800 mb-2 truncate">{product.name}</h3>
        <p className="text-gray-600 text-sm mb-4 line-clamp-2">{product.description}</p>
        
        <div className="flex justify-between items-center">
          {product.price && (
            <span className="text-blue-600 font-bold">{product.price}</span>
          )}
          <a
            href={product.link}
            target="_blank"
            rel="noopener noreferrer"
            className="bg-blue-100 text-blue-800 text-xs font-semibold px-2.5 py-1 rounded hover:bg-blue-200 transition-colors"
          >
            {product.ctaText || 'View Details'}
          </a>
        </div>
      </div>
    </div>
  );
};

const AffiliateProducts = ({ 
  title = "Recommended Products & Services",
  subtitle = "We've partnered with these trusted providers to help you access affordable dental care",
  category = null, 
  limit = 5,
  showFilters = true
}) => {
  const [selectedCategory, setSelectedCategory] = useState(category);
  const scrollRef = useRef(null);
  
  let products = [];
  if (selectedCategory) {
    products = getProductsByCategory(selectedCategory);
  } else {
    products = getFeaturedProducts(limit);
  }
  
  // Limit the number of products if specified
  if (limit && limit > 0 && !selectedCategory) {
    products = products.slice(0, limit);
  }
  
  const categories = [
    { id: null, name: 'Featured' },
    { id: 'toothbrush', name: 'Toothbrushes' },
    { id: 'toothpaste', name: 'Toothpaste' },
    { id: 'floss', name: 'Floss' },
    { id: 'whitening', name: 'Whitening' },
    { id: 'accessories', name: 'Accessories' }
  ];

  const scroll = (direction) => {
    const { current } = scrollRef;
    if (current) {
      const scrollAmount = direction === 'left' ? -280 : 280;
      current.scrollBy({ left: scrollAmount, behavior: 'smooth' });
    }
  };

  return (
    <div className="py-12 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">{title}</h2>
          {subtitle && <p className="text-gray-600">{subtitle}</p>}
        </div>
        
        {showFilters && (
          <div className="flex flex-wrap justify-center gap-2 mb-8">
            {categories.map(cat => (
              <button
                key={cat.id || 'featured'}
                onClick={() => setSelectedCategory(cat.id)}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-colors duration-300 ${
                  selectedCategory === cat.id
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-700 hover:bg-gray-100'
                }`}
              >
                {cat.name}
              </button>
            ))}
          </div>
        )}
        
        <div className="relative">
          <div 
            ref={scrollRef}
            className="flex overflow-x-auto no-scrollbar gap-4 pb-4 px-4"
            style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
          >
            {products.length > 0 ? (
              products.map(product => (
                <ProductCard key={product.id} product={product} />
              ))
            ) : (
              <div className="w-full text-center py-8">
                <p className="text-gray-500">No products found in this category.</p>
              </div>
            )}
          </div>
          
          {products.length > 3 && (
            <>
              <button 
                onClick={() => scroll('left')}
                className="absolute left-0 top-1/2 -translate-y-1/2 bg-white rounded-full p-2 shadow-md z-10"
                aria-label="Scroll left"
              >
                <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              
              <button 
                onClick={() => scroll('right')}
                className="absolute right-0 top-1/2 -translate-y-1/2 bg-white rounded-full p-2 shadow-md z-10"
                aria-label="Scroll right"
              >
                <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </>
          )}
        </div>
        
        <style jsx>{`
          .no-scrollbar::-webkit-scrollbar {
            display: none;
          }
        `}</style>
        
        <div className="text-center mt-4">
          <p className="text-xs text-gray-500">
            Disclaimer: Some links on this page are affiliate links.
          </p>
        </div>
      </div>
    </div>
  );
};

export default AffiliateProducts; 