import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import SONICARE_ADS from '../../lib/constants/sonicareAds';

/**
 * SingleAdRotator - Shows one ad at a time and rotates through them
 * This simplified version only shows one product with auto-rotation
 * 
 * @param {Object} props
 * @param {number} props.interval - Rotation interval in ms (default: 8000)
 */
const SingleAdRotator = ({ interval = 8000 }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const timeoutRef = useRef(null);
  
  // We use the SONICARE_ADS directly here
  const ads = SONICARE_ADS;

  // Reset the timer when the currentIndex changes
  useEffect(() => {
    const nextTimeout = setTimeout(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % ads.length);
    }, interval);
    
    timeoutRef.current = nextTimeout;
    
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [currentIndex, interval, ads.length]);

  if (!ads || ads.length === 0) {
    return null;
  }

  const currentAd = ads[currentIndex];

  return (
    <div className="my-6 mx-auto max-w-md bg-white rounded-lg shadow-md overflow-hidden">
      <AnimatePresence mode="wait">
        <motion.div
          key={currentIndex}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.5 }}
        >
          <a href={currentAd.link} target="_blank" rel="noopener noreferrer" className="block">
            <div className="relative">
              <img 
                src={currentAd.image} 
                alt={currentAd.title} 
                className="w-full h-48 object-cover"
              />
              {currentAd.tag && (
                <div className="absolute top-2 right-2 bg-blue-600 text-white text-xs font-bold px-2 py-1 rounded-full">
                  {currentAd.tag}
                </div>
              )}
            </div>
            
            <div className="p-4">
              <h3 className="text-lg font-bold text-gray-800 mb-1 line-clamp-1">{currentAd.title}</h3>
              <p className="text-gray-600 text-sm mb-3 line-clamp-2">{currentAd.description}</p>
              
              <div className="flex justify-between items-center">
                <span className="text-blue-600 font-medium text-sm">{currentAd.price}</span>
                <span className="bg-blue-100 text-blue-800 text-xs font-semibold px-2.5 py-0.5 rounded">
                  {currentAd.ctaText}
                </span>
              </div>
            </div>
          </a>
        </motion.div>
      </AnimatePresence>
      
      {/* Indicator dots */}
      <div className="flex justify-center space-x-1 pb-2">
        {ads.map((_, index) => (
          <span
            key={index}
            className={`w-1.5 h-1.5 rounded-full ${
              index === currentIndex ? 'bg-blue-600' : 'bg-gray-300'
            }`}
          />
        ))}
      </div>
      
      <div className="text-center bg-gray-50 text-xs text-gray-500 p-1">
        Advertisement
      </div>
    </div>
  );
};

export default SingleAdRotator; 