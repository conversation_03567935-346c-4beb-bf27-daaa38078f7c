import React, { useEffect, useState, useRef, memo, Suspense } from "react";
import { Canvas } from "@react-three/fiber";
import { OrbitControls, Stage, useGLTF } from "@react-three/drei";
import { STLLoader } from "three/examples/jsm/loaders/STLLoader";
import { useLoader } from "@react-three/fiber";
import { ErrorBoundary as ReactErrorBoundary } from "react-error-boundary";
import * as THREE from 'three';

// Memoize the STL model to prevent unnecessary re-renders
const STLModel = memo(({ modelUrl }) => {
  const [error, setError] = useState(null);
  const materialRef = useRef();

  // Use a ref to track if component is mounted
  const isMounted = useRef(true);

  useEffect(() => {
    return () => {
      isMounted.current = false;
    };
  }, []);

  let geometry;
  try {
    // Handle both blob URLs and regular URLs
    const url = modelUrl.startsWith('blob:') ? modelUrl : `/models/${modelUrl}`;
    geometry = useLoader(STLLoader, url);

    // Dispose of geometry when component unmounts
    useEffect(() => {
      return () => {
        if (geometry && geometry.dispose) {
          geometry.dispose();
        }
      };
    }, [geometry]);

  } catch (error) {
    console.error('Error loading STL:', error);
    if (isMounted.current) {
      setError(error);
    }
    throw error;
  }

  // Apply performance optimizations with useEffect
  useEffect(() => {
    if (materialRef.current) {
      // Optimize material for performance
      materialRef.current.needsUpdate = true;
    }
  }, [modelUrl]);

  return (
    <mesh scale={[0.1, 0.1, 0.1]}>
      <primitive object={geometry} />
      <meshStandardMaterial
        ref={materialRef}
        color="#60A5FA"
        roughness={0.5}
        metalness={0.5}
        transparent={true}
        opacity={0.9}
        // Optimize rendering
        flatShading={true}
        // Disable features we don't need
        fog={false}
        // Optimize for static objects
        toneMapped={false}
      />
    </mesh>
  );
});

const ErrorFallback = ({ error, resetErrorBoundary }) => (
  <div className="flex flex-col items-center justify-center h-full p-4 text-center">
    <p className="text-red-400 mb-4">Error loading 3D model</p>
    <button
      onClick={resetErrorBoundary}
      className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-400"
    >
      Try again
    </button>
  </div>
);

const STLViewer = memo(({ modelUrl, className = "", onError }) => {
  // Use a ref to track the canvas for cleanup
  const canvasRef = useRef();

  // Performance optimization settings
  const canvasProps = {
    dpr: [1, 2], // Responsive pixel ratio (min, max)
    gl: {
      antialias: true,
      alpha: true,
      powerPreference: 'high-performance',
      stencil: false,
      depth: true
    },
    // Optimize performance by limiting frame rate when not interacting
    frameloop: 'demand',
    // Optimize performance with linear color space
    linear: true
  };

  // Cleanup function for the component
  useEffect(() => {
    return () => {
      // Clean up any Three.js resources when component unmounts
      if (canvasRef.current) {
        const scene = canvasRef.current.scene;
        if (scene) {
          scene.traverse((object) => {
            if (object.geometry) object.geometry.dispose();
            if (object.material) {
              if (Array.isArray(object.material)) {
                object.material.forEach(material => material.dispose());
              } else {
                object.material.dispose();
              }
            }
          });
        }
      }
    };
  }, []);

  return (
    <Canvas
      ref={canvasRef}
      className={className}
      {...canvasProps}
    >
      <Suspense fallback={null}>
        <ambientLight intensity={0.5} />
        <pointLight position={[10, 10, 10]} intensity={0.8} />
        <pointLight position={[-10, -10, -10]} intensity={0.5} />
        <Stage
          // Optimize stage settings
          environment={false} // Disable environment map for performance
          shadows={false} // Disable shadows for performance
          intensity={0.5} // Lower light intensity
          preset="soft" // Use a simpler preset
        >
          <ReactErrorBoundary
            FallbackComponent={ErrorFallback}
            onError={onError}
          >
            <STLModel modelUrl={modelUrl} />
          </ReactErrorBoundary>
        </Stage>
        <OrbitControls
          enablePan={true}
          enableZoom={true}
          enableRotate={true}
          autoRotate={true}
          autoRotateSpeed={2}
          // Optimize controls
          enableDamping={false} // Disable damping for performance
          makeDefault
        />
      </Suspense>
    </Canvas>
  );
});

export default STLViewer;