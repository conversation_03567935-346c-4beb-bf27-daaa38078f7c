import React, { useState, useEffect } from 'react';
import { useAnalysisService, BrushingSessionData, AnalysisResult } from './BrushingAnalysisService';

// Define Brush Connection Status
type ConnectionStatus = 'disconnected' | 'scanning' | 'connecting' | 'connected' | 'error';

// Define Session Types
interface BrushingSession {
  id: string;
  date: Date;
  duration: number;
  zonesCovered: Record<string, number>;
  pressureLevel: number;
  imagesCount: number;
  analyzed: boolean;
}

interface SmiloBrushSyncProps {
  userId: string;
  onAnalysisComplete?: (result: AnalysisResult) => void;
}

const SmiloBrushSync: React.FC<SmiloBrushSyncProps> = ({ 
  userId, 
  onAnalysisComplete 
}) => {
  // Connection state
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>('disconnected');
  const [deviceName, setDeviceName] = useState<string | null>(null);
  const [batteryLevel, setBatteryLevel] = useState<number | null>(null);
  
  // Session data
  const [sessions, setSessions] = useState<BrushingSession[]>([]);
  const [selectedSession, setSelectedSession] = useState<string | null>(null);
  const [syncProgress, setSyncProgress] = useState<number>(0);
  const [isSyncing, setIsSyncing] = useState<boolean>(false);
  
  // Analysis service
  const { analyzeSession, isAnalyzing, latestResult } = useAnalysisService();
  
  // Simulated toothbrush connection
  const connectToToothbrush = () => {
    setConnectionStatus('scanning');
    
    // Simulate scanning delay
    setTimeout(() => {
      setConnectionStatus('connecting');
      
      // Simulate connection delay
      setTimeout(() => {
        setConnectionStatus('connected');
        setDeviceName('SmiloBrush Pro');
        setBatteryLevel(78);
        
        // Simulate fetching initial session data
        fetchSessionData();
      }, 1500);
    }, 2000);
  };
  
  // Disconnect from brush
  const disconnectFromToothbrush = () => {
    setConnectionStatus('disconnected');
    setDeviceName(null);
    setBatteryLevel(null);
  };
  
  // Fetch session data from brush (simulated)
  const fetchSessionData = () => {
    // Simulate fetching sessions
    const mockSessions: BrushingSession[] = [
      {
        id: `session-${Date.now()}`,
        date: new Date(Date.now() - 1000 * 60 * 60), // 1 hour ago
        duration: 124, // seconds
        zonesCovered: {
          'upper-front': 32,
          'upper-left': 25,
          'upper-right': 28,
          'lower-front': 20,
          'lower-left': 12,
          'lower-right': 7
        },
        pressureLevel: 6.8,
        imagesCount: 4,
        analyzed: false
      },
      {
        id: `session-${Date.now() - 86400000}`,
        date: new Date(Date.now() - 86400000), // 1 day ago
        duration: 178, // seconds
        zonesCovered: {
          'upper-front': 45,
          'upper-left': 35,
          'upper-right': 30,
          'lower-front': 28,
          'lower-left': 20,
          'lower-right': 20
        },
        pressureLevel: 5.3,
        imagesCount: 6,
        analyzed: true
      },
      {
        id: `session-${Date.now() - 172800000}`,
        date: new Date(Date.now() - 172800000), // 2 days ago
        duration: 95, // seconds
        zonesCovered: {
          'upper-front': 30,
          'upper-left': 15,
          'upper-right': 20,
          'lower-front': 15,
          'lower-left': 8,
          'lower-right': 7
        },
        pressureLevel: 7.5,
        imagesCount: 3,
        analyzed: true
      }
    ];
    
    setSessions(mockSessions);
  };
  
  // Sync data for a specific session
  const syncSessionData = async (sessionId: string) => {
    setSelectedSession(sessionId);
    setIsSyncing(true);
    setSyncProgress(0);
    
    // Find the selected session
    const session = sessions.find(s => s.id === sessionId);
    if (!session) {
      setIsSyncing(false);
      return;
    }
    
    // Simulate progress updates
    const interval = setInterval(() => {
      setSyncProgress(prev => {
        const newProgress = prev + Math.floor(Math.random() * 15) + 5;
        return newProgress > 100 ? 100 : newProgress;
      });
    }, 500);
    
    // Simulate data fetching
    await new Promise(resolve => setTimeout(resolve, 3000));
    clearInterval(interval);
    setSyncProgress(100);
    
    // Prepare mock session data for analysis
    const mockImages = Array(session.imagesCount).fill(null).map(() => 
      `data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEA${Math.random().toString(36).substring(2, 15)}`
    );
    
    // Simulate completed data sync
    const brushingData: BrushingSessionData = {
      sessionId: session.id,
      timestamp: session.date.getTime(),
      duration: session.duration,
      zonesCovered: session.zonesCovered,
      pressure: {
        average: session.pressureLevel,
        max: session.pressureLevel + (Math.random() * 2)
      },
      images: mockImages
    };
    
    // Perform analysis on the session data
    try {
      const analysisResult = await analyzeSession(brushingData);
      
      // Update session to mark as analyzed
      setSessions(prevSessions => 
        prevSessions.map(s => 
          s.id === session.id ? { ...s, analyzed: true } : s
        )
      );
      
      // Notify parent component of the analysis result
      if (onAnalysisComplete) {
        onAnalysisComplete(analysisResult);
      }
    } catch (error) {
      console.error('Analysis failed:', error);
    } finally {
      setIsSyncing(false);
    }
  };
  
  // Format date for display
  const formatDate = (date: Date): string => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric'
    }).format(date);
  };
  
  // Format duration for display
  const formatDuration = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };
  
  // Get color for coverage percentage
  const getCoverageColor = (percentage: number): string => {
    if (percentage >= 80) return 'text-green-600';
    if (percentage >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };
  
  // Calculate coverage percentage
  const calculateCoverage = (session: BrushingSession): number => {
    const totalTime = session.duration;
    const zoneTime = Object.values(session.zonesCovered).reduce((sum, time) => sum + time, 0);
    return Math.round((zoneTime / totalTime) * 100);
  };
  
  return (
    <div className="bg-white rounded-xl shadow-md overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-4">
        <div className="flex items-center justify-between">
          <h2 className="text-white text-xl font-semibold">SmiloBrush Sync</h2>
          <div className="flex items-center">
            {connectionStatus === 'connected' && (
              <div className="bg-white/20 rounded-lg px-3 py-1 text-sm text-white flex items-center mr-3">
                <span className="h-2 w-2 rounded-full bg-green-400 mr-2"></span>
                <span>{deviceName} • {batteryLevel}%</span>
              </div>
            )}
            
            {connectionStatus === 'disconnected' ? (
              <button 
                onClick={connectToToothbrush}
                className="px-4 py-1.5 bg-white text-indigo-700 rounded-lg text-sm font-medium hover:bg-indigo-50 transition-colors"
              >
                Connect Brush
              </button>
            ) : connectionStatus === 'connected' ? (
              <button 
                onClick={disconnectFromToothbrush}
                className="px-4 py-1.5 bg-indigo-800 text-white rounded-lg text-sm font-medium hover:bg-indigo-900 transition-colors"
              >
                Disconnect
              </button>
            ) : (
              <button 
                disabled
                className="px-4 py-1.5 bg-indigo-400 text-white rounded-lg text-sm font-medium cursor-not-allowed"
              >
                {connectionStatus === 'scanning' ? 'Scanning...' : connectionStatus === 'connecting' ? 'Connecting...' : 'Error'}
              </button>
            )}
          </div>
        </div>
      </div>
      
      {/* Status Display */}
      {connectionStatus === 'scanning' && (
        <div className="p-6 text-center">
          <div className="animate-pulse flex flex-col items-center justify-center">
            <svg className="w-10 h-10 text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.143 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0" />
            </svg>
            <p className="mt-3 text-gray-600">Scanning for SmiloBrush devices nearby...</p>
          </div>
        </div>
      )}
      
      {connectionStatus === 'connecting' && (
        <div className="p-6 text-center">
          <div className="animate-pulse flex flex-col items-center justify-center">
            <svg className="w-10 h-10 text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            <p className="mt-3 text-gray-600">Connecting to SmiloBrush...</p>
          </div>
        </div>
      )}
      
      {connectionStatus === 'error' && (
        <div className="p-6 text-center">
          <div className="flex flex-col items-center justify-center">
            <svg className="w-10 h-10 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p className="mt-3 text-gray-800 font-medium">Connection Failed</p>
            <p className="mt-1 text-gray-600 text-sm">Please make sure your brush is turned on and within range.</p>
            <button 
              onClick={connectToToothbrush}
              className="mt-4 px-4 py-2 bg-indigo-600 text-white rounded-lg text-sm font-medium hover:bg-indigo-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      )}
      
      {connectionStatus === 'disconnected' && (
        <div className="p-6">
          <div className="text-center py-8">
            <svg className="w-16 h-16 mx-auto text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.143 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0" />
            </svg>
            <h3 className="mt-4 text-lg font-medium text-gray-900">Connect Your SmiloBrush</h3>
            <p className="mt-2 text-gray-600 max-w-md mx-auto">
              Sync your brushing data for personalized insights and AI analysis of your oral health.
            </p>
            <button 
              onClick={connectToToothbrush}
              className="mt-5 px-5 py-2.5 bg-indigo-600 text-white rounded-lg font-medium hover:bg-indigo-700 transition-colors"
            >
              Connect SmiloBrush
            </button>
          </div>
        </div>
      )}
      
      {connectionStatus === 'connected' && (
        <div className="divide-y divide-gray-200">
          {/* Feature Description */}
          <div className="px-6 py-4 bg-indigo-50">
            <h3 className="text-indigo-800 font-medium">AI-Powered Brushing Analysis</h3>
            <p className="text-sm text-indigo-700/70 mt-1">
              Your SmiloBrush captures data and images to provide personalized insights
            </p>
          </div>
          
          {/* Session List */}
          <div className="divide-y divide-gray-200">
            {sessions.length === 0 ? (
              <div className="p-6 text-center">
                <p className="text-gray-600">No brushing sessions found. Try syncing your brush.</p>
              </div>
            ) : (
              sessions.map(session => (
                <div key={session.id} className="p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div className="flex-1">
                      <div className="flex items-center">
                        <svg className="h-5 w-5 text-indigo-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1M19 20a2 2 0 002-2V8a2 2 0 00-2-2h-1M8 19h1m-1-2h1m-1-2h1m-1-2h1m-1-2h1M12 3v16m0 0h8m-8 0H4" />
                        </svg>
                        <h3 className="text-sm font-medium text-gray-900">
                          Brushing Session • {formatDate(session.date)}
                        </h3>
                        
                        {session.analyzed && (
                          <span className="ml-2 px-2 py-0.5 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                            Analyzed
                          </span>
                        )}
                      </div>
                      
                      <div className="mt-2 grid grid-cols-2 gap-x-4 gap-y-2 text-xs text-gray-600">
                        <div className="flex items-center">
                          <svg className="flex-shrink-0 mr-1.5 h-4 w-4 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          Duration: {formatDuration(session.duration)}
                        </div>
                        <div className="flex items-center">
                          <svg className="flex-shrink-0 mr-1.5 h-4 w-4 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                          Pressure: {session.pressureLevel.toFixed(1)}/10
                        </div>
                        <div className="flex items-center">
                          <svg className="flex-shrink-0 mr-1.5 h-4 w-4 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                          Images: {session.imagesCount}
                        </div>
                        <div className="flex items-center">
                          <svg className="flex-shrink-0 mr-1.5 h-4 w-4 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                          </svg>
                          <span className={getCoverageColor(calculateCoverage(session))}>
                            Coverage: {calculateCoverage(session)}%
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="mt-4 md:mt-0 md:ml-6">
                      {selectedSession === session.id && isSyncing ? (
                        <div className="w-full">
                          <div className="flex justify-between text-xs text-gray-600 mb-1">
                            <span>Syncing data...</span>
                            <span>{syncProgress}%</span>
                          </div>
                          <div className="w-48 bg-gray-200 rounded-full h-2.5">
                            <div 
                              className="bg-indigo-600 h-2.5 rounded-full" 
                              style={{ width: `${syncProgress}%` }}
                            ></div>
                          </div>
                        </div>
                      ) : selectedSession === session.id && isAnalyzing ? (
                        <div className="w-full">
                          <div className="flex justify-between text-xs text-gray-600 mb-1">
                            <span>Analyzing...</span>
                          </div>
                          <div className="w-48 bg-gray-200 rounded-full h-2.5">
                            <div className="bg-indigo-600 h-2.5 rounded-full animate-pulse"></div>
                          </div>
                        </div>
                      ) : (
                        <button
                          onClick={() => syncSessionData(session.id)}
                          disabled={isSyncing || isAnalyzing}
                          className={`px-4 py-2 rounded-md text-sm font-medium 
                            ${!session.analyzed 
                              ? 'bg-indigo-600 text-white hover:bg-indigo-700' 
                              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'} 
                            transition-colors
                            ${isSyncing || isAnalyzing ? 'opacity-50 cursor-not-allowed' : ''}
                          `}
                        >
                          {!session.analyzed ? 'Sync & Analyze' : 'Re-analyze'}
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
          
          {/* AI Analysis Results (shows when latestResult is available) */}
          {latestResult && (
            <div className="p-6 bg-gradient-to-r from-blue-50 to-indigo-50">
              <h3 className="text-indigo-800 font-medium flex items-center">
                <svg className="w-5 h-5 mr-2 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
                Latest Analysis Results
              </h3>
              
              <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                {latestResult.plaqueDetection && (
                  <div className="bg-white p-3 rounded-lg shadow-sm">
                    <h4 className="text-sm font-medium text-gray-900">Plaque Detection</h4>
                    <div className="mt-2 flex items-center">
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        <div 
                          className={`h-2.5 rounded-full ${
                            latestResult.plaqueDetection.score > 80 ? 'bg-green-500' : 
                            latestResult.plaqueDetection.score > 60 ? 'bg-yellow-500' : 'bg-red-500'
                          }`}
                          style={{ width: `${latestResult.plaqueDetection.score}%` }}
                        ></div>
                      </div>
                      <span className="ml-2 text-sm font-medium text-gray-700">
                        {latestResult.plaqueDetection.score}%
                      </span>
                    </div>
                    {latestResult.plaqueDetection.problemAreas.length > 0 && (
                      <div className="mt-2">
                        <p className="text-xs text-gray-500">Problem areas:</p>
                        <ul className="mt-1 text-xs">
                          {latestResult.plaqueDetection.problemAreas.map((area, i) => (
                            <li key={i} className="text-gray-700">
                              • {area.zone} ({Math.round(area.coverage * 100)}% coverage)
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                )}
                
                {latestResult.cavityDetection && (
                  <div className="bg-white p-3 rounded-lg shadow-sm">
                    <h4 className="text-sm font-medium text-gray-900">Cavity Detection</h4>
                    <div className="mt-1">
                      {latestResult.cavityDetection.detected ? (
                        <div>
                          <p className="text-red-600 text-sm font-medium">
                            Potential cavity detected
                          </p>
                          <ul className="mt-1 text-xs text-gray-700">
                            {latestResult.cavityDetection.locations.map((loc, i) => (
                              <li key={i} className="flex items-center">
                                • Tooth {loc.tooth}: {loc.severity} severity
                                <span className="ml-1 text-gray-500">(Confidence: {Math.round(loc.confidence * 100)}%)</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      ) : (
                        <p className="text-green-600 text-sm">No cavities detected</p>
                      )}
                    </div>
                  </div>
                )}
                
                {latestResult.gumInflammation && (
                  <div className="bg-white p-3 rounded-lg shadow-sm">
                    <h4 className="text-sm font-medium text-gray-900">Gum Health</h4>
                    <div className="mt-1">
                      {latestResult.gumInflammation.detected ? (
                        <div>
                          <p className={`text-sm font-medium ${
                            latestResult.gumInflammation.severity === 'severe' ? 'text-red-600' :
                            latestResult.gumInflammation.severity === 'moderate' ? 'text-yellow-600' : 'text-yellow-500'
                          }`}>
                            {latestResult.gumInflammation.severity} inflammation detected
                          </p>
                          {latestResult.gumInflammation.locations.length > 0 && (
                            <p className="mt-1 text-xs text-gray-700">
                              Areas: {latestResult.gumInflammation.locations.join(', ')}
                            </p>
                          )}
                        </div>
                      ) : (
                        <p className="text-green-600 text-sm">Gums appear healthy</p>
                      )}
                    </div>
                  </div>
                )}
                
                {latestResult.toothMovement && (
                  <div className="bg-white p-3 rounded-lg shadow-sm">
                    <h4 className="text-sm font-medium text-gray-900">Tooth Movement</h4>
                    <div className="mt-1">
                      {latestResult.toothMovement.detected && latestResult.toothMovement.locations.length > 0 ? (
                        <div>
                          <p className="text-indigo-600 text-sm">
                            Movement detected since {latestResult.toothMovement.changesFrom}
                          </p>
                          <ul className="mt-1 text-xs text-gray-700">
                            {latestResult.toothMovement.locations.map((loc, i) => (
                              <li key={i}>
                                • Tooth {loc.tooth}: {loc.movementMm}mm {loc.direction}
                              </li>
                            ))}
                          </ul>
                        </div>
                      ) : (
                        <p className="text-gray-600 text-sm">No significant movement detected</p>
                      )}
                    </div>
                  </div>
                )}
              </div>
              
              {/* Recommendations */}
              <div className="mt-5">
                <h4 className="text-sm font-medium text-gray-900 mb-2">Recommendations</h4>
                <ul className="space-y-2">
                  {latestResult.recommendations.sort((a, b) => a.priority - b.priority).map((rec, i) => (
                    <li key={i} className="flex">
                      <div className={`flex-shrink-0 w-1.5 mt-1 mr-2 rounded-full ${
                        rec.priority === 1 ? 'bg-red-500' :
                        rec.priority === 2 ? 'bg-yellow-500' : 'bg-green-500'
                      }`} style={{height: '14px'}}></div>
                      <span className="text-sm text-gray-700">{rec.text}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default SmiloBrushSync; 