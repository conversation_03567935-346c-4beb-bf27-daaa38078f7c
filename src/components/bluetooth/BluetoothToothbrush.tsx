import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  Image,
  Alert,
  ActivityIndicator,
  SafeAreaView,
  Platform,
} from 'react-native';
import { BleManager, Device } from 'react-native-ble-plx';
import { PERMISSIONS, requestMultiple } from 'react-native-permissions';
import base64 from 'react-native-base64';

// Mock service and characteristic UUIDs - in a real app these would match the device
const TOOTHBRUSH_SERVICE_UUID = '00001234-0000-1000-8000-00805f9b34fb';
const SESSION_DATA_CHARACTERISTIC_UUID = '00002345-0000-1000-8000-00805f9b34fb';
const IMAGE_DATA_CHARACTERISTIC_UUID = '00003456-0000-1000-8000-00805f9b34fb';

// Mock API endpoint
const AI_ANALYSIS_API_URL = 'https://api.smilo.dental/v1/brushing-analysis';

// Main component
const BluetoothToothbrush = () => {
  // Initialize BLE manager
  const [manager] = useState(() => new BleManager());
  
  // State for devices, connection, and data
  const [scanning, setScanning] = useState(false);
  const [devices, setDevices] = useState<Device[]>([]);
  const [connectedDevice, setConnectedDevice] = useState<Device | null>(null);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [permissionsGranted, setPermissionsGranted] = useState(false);
  
  // Brushing session data
  const [sessionData, setSessionData] = useState<BrushingSession | null>(null);
  const [imageData, setImageData] = useState<string | null>(null);
  const [syncing, setSyncing] = useState(false);
  const [analysisResults, setAnalysisResults] = useState<any>(null);

  // Request permissions when component mounts
  useEffect(() => {
    requestPermissions();
    
    return () => {
      // Clean up BLE manager on unmount
      manager.destroy();
    };
  }, []);

  // Handle requesting necessary permissions
  const requestPermissions = async () => {
    try {
      const permissions = Platform.select({
        android: [
          PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
          PERMISSIONS.ANDROID.BLUETOOTH_SCAN,
          PERMISSIONS.ANDROID.BLUETOOTH_CONNECT
        ],
        ios: [
          PERMISSIONS.IOS.LOCATION_WHEN_IN_USE,
          PERMISSIONS.IOS.BLUETOOTH
        ],
        default: []
      }) || [];

      const result = await requestMultiple(permissions);
      
      // Check if all permissions were granted
      const allGranted = Object.values(result).every(status => status === 'granted');
      
      if (allGranted) {
        setPermissionsGranted(true);
      } else {
        Alert.alert(
          'Permissions Required',
          'Bluetooth and location permissions are required to connect to your toothbrush.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Failed to request permissions:', error);
    }
  };

  // Handle scanning for devices
  const startScan = useCallback(() => {
    if (!permissionsGranted) {
      requestPermissions();
      return;
    }

    setScanning(true);
    setDevices([]);

    // In a real app, we'd scan for actual devices
    // For this mock, we'll simulate finding the SmiloBrush after a delay
    setTimeout(() => {
      // Create a mock device
      const mockDevice: Partial<Device> = {
        id: 'smilo-brush-12345',
        name: 'SmiloBrush',
        localName: 'SmiloBrush',
        isConnectable: true,
      };
      
      setDevices([mockDevice as Device]);
      setScanning(false);
    }, 2000);
  }, [permissionsGranted, manager]);

  // Handle connecting to device
  const connectToDevice = useCallback(async (device: Device) => {
    try {
      setConnectionStatus('connecting');
      
      // In a real app, we'd actually connect to the device
      // For this mock, we'll simulate a successful connection after a delay
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      setConnectedDevice(device);
      setConnectionStatus('connected');
      
      // Start listening for data
      startListeningForData();
      
    } catch (error) {
      console.error('Connection error:', error);
      setConnectionStatus('disconnected');
      Alert.alert('Connection Failed', 'Failed to connect to the toothbrush. Please try again.');
    }
  }, [manager]);

  // Handle disconnecting from device
  const disconnectFromDevice = useCallback(async () => {
    if (connectedDevice) {
      try {
        // In a real app, we'd actually disconnect from the device
        // For this mock, we'll simulate a successful disconnection
        await new Promise(resolve => setTimeout(resolve, 500));
        
        setConnectedDevice(null);
        setConnectionStatus('disconnected');
        setSessionData(null);
        setImageData(null);
        setAnalysisResults(null);
      } catch (error) {
        console.error('Disconnection error:', error);
      }
    }
  }, [connectedDevice, manager]);

  // Mock function to simulate receiving data from the toothbrush
  const startListeningForData = () => {
    // In a real app, we'd set up notifications for the device's characteristics
    // For this mock, we'll simulate receiving data after a delay
    
    // Simulate receiving session data
    setTimeout(() => {
      const mockSessionData: BrushingSession = {
        sessionId: 'session_' + Date.now(),
        startTime: new Date(Date.now() - 120000).toISOString(), // 2 minutes ago
        duration: 120, // seconds
        pressureLevel: 'medium',
        zonesCovered: ['upper_front', 'upper_left', 'upper_right', 'lower_front'],
        zonesMissed: ['lower_left', 'lower_right'],
        brushingScore: 85,
        timestamp: new Date().toISOString(),
      };
      
      setSessionData(mockSessionData);
    }, 2000);
    
    // Simulate receiving image data
    setTimeout(() => {
      // This would be a base64 string in a real scenario
      // Using a placeholder image URL for the mock
      setImageData('data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEASABIAAD/...');
    }, 3000);
  };

  // Handle syncing data with the cloud for AI analysis
  const syncWithCloud = async () => {
    if (!sessionData || !imageData) {
      Alert.alert('No Data', 'No brushing session data available to sync.');
      return;
    }
    
    setSyncing(true);
    
    try {
      // In a real app, we'd make an actual API call
      // For this mock, we'll simulate a successful API response after a delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Simulate API response
      const mockAnalysisResults = {
        sessionId: sessionData.sessionId,
        analysisId: 'analysis_' + Date.now(),
        brushingEfficiency: 78,
        recommendations: [
          'Spend more time on lower left and right zones',
          'Reduce brushing pressure slightly',
          'Increase brushing time to recommended 2 minutes'
        ],
        aiConfidence: 92,
        processedImages: [
          { url: 'https://smilo.dental/images/processed/1.jpg', zone: 'upper_front' },
          { url: 'https://smilo.dental/images/processed/2.jpg', zone: 'upper_left' },
        ],
        timestamp: new Date().toISOString(),
      };
      
      setAnalysisResults(mockAnalysisResults);
      
      Alert.alert('Sync Complete', 'Your brushing data has been analyzed!');
    } catch (error) {
      console.error('Sync error:', error);
      Alert.alert('Sync Failed', 'Failed to sync data with the cloud. Please try again.');
    } finally {
      setSyncing(false);
    }
  };

  // Render a single device item
  const renderDeviceItem = ({ item }: { item: Device }) => (
    <TouchableOpacity
      style={styles.deviceItem}
      onPress={() => connectToDevice(item)}
      disabled={connectionStatus === 'connecting'}
    >
      <Text style={styles.deviceName}>{item.name || 'Unknown Device'}</Text>
      <Text style={styles.deviceId}>ID: {item.id}</Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>SmiloBrush Connect</Text>
        <Text style={styles.subtitle}>
          Status: {
            connectionStatus === 'connected' ? '🟢 Connected' :
            connectionStatus === 'connecting' ? '🟠 Connecting...' :
            '🔴 Disconnected'
          }
        </Text>
      </View>

      {!connectedDevice ? (
        <View style={styles.scanSection}>
          <TouchableOpacity
            style={styles.scanButton}
            onPress={startScan}
            disabled={scanning || !permissionsGranted}
          >
            <Text style={styles.scanButtonText}>
              {scanning ? 'Scanning...' : 'Scan for Toothbrushes'}
            </Text>
          </TouchableOpacity>
          
          {scanning && (
            <ActivityIndicator size="large" color="#4f46e5" style={styles.loader} />
          )}
          
          {devices.length > 0 && (
            <View style={styles.devicesContainer}>
              <Text style={styles.devicesTitle}>Available Devices</Text>
              <FlatList
                data={devices}
                renderItem={renderDeviceItem}
                keyExtractor={item => item.id}
                style={styles.devicesList}
              />
            </View>
          )}
        </View>
      ) : (
        <View style={styles.sessionSection}>
          <View style={styles.sessionHeader}>
            <Text style={styles.connectedDevice}>Connected to: {connectedDevice.name}</Text>
            <TouchableOpacity
              style={styles.disconnectButton}
              onPress={disconnectFromDevice}
            >
              <Text style={styles.disconnectButtonText}>Disconnect</Text>
            </TouchableOpacity>
          </View>
          
          {sessionData ? (
            <View style={styles.sessionData}>
              <Text style={styles.sessionTitle}>Latest Brushing Session</Text>
              
              <View style={styles.sessionDetails}>
                <Text style={styles.sessionDetail}>Duration: {sessionData.duration} seconds</Text>
                <Text style={styles.sessionDetail}>Pressure: {sessionData.pressureLevel}</Text>
                <Text style={styles.sessionDetail}>Score: {sessionData.brushingScore}/100</Text>
                
                <Text style={styles.sessionDetailTitle}>Zones Covered:</Text>
                <View style={styles.zonesList}>
                  {sessionData.zonesCovered.map((zone, index) => (
                    <View key={index} style={styles.zoneItem}>
                      <Text style={styles.zoneText}>✓ {formatZoneName(zone)}</Text>
                    </View>
                  ))}
                </View>
                
                <Text style={styles.sessionDetailTitle}>Zones Missed:</Text>
                <View style={styles.zonesList}>
                  {sessionData.zonesMissed.map((zone, index) => (
                    <View key={index} style={styles.zoneItem}>
                      <Text style={styles.zoneTextMissed}>✗ {formatZoneName(zone)}</Text>
                    </View>
                  ))}
                </View>
              </View>
              
              {imageData && (
                <View style={styles.imageSection}>
                  <Text style={styles.imageTitle}>Captured Image Preview</Text>
                  <View style={styles.imageContainer}>
                    {/* In a real app, we'd display the actual base64 image */}
                    {imageData ? (
                      <Image
                        source={{ uri: imageData }}
                        style={styles.imagePreview}
                        resizeMode="contain"
                        onError={() => console.warn('Image failed to load')}
                      />
                    ) : (
                      <View style={styles.placeholderContainer}>
                        <Text style={styles.placeholderText}>Waiting for image data...</Text>
                      </View>
                    )}
                  </View>
                  
                  <TouchableOpacity
                    style={styles.syncButton}
                    onPress={syncWithCloud}
                    disabled={syncing}
                  >
                    <Text style={styles.syncButtonText}>
                      {syncing ? 'Syncing...' : 'Sync Images for AI Analysis'}
                    </Text>
                  </TouchableOpacity>
                  
                  {syncing && (
                    <ActivityIndicator size="small" color="#4f46e5" style={styles.smallLoader} />
                  )}
                </View>
              )}
              
              {analysisResults && (
                <View style={styles.analysisSection}>
                  <Text style={styles.analysisTitle}>AI Analysis Results</Text>
                  
                  <View style={styles.analysisDetails}>
                    <Text style={styles.analysisDetail}>
                      Brushing Efficiency: {analysisResults.brushingEfficiency}%
                    </Text>
                    
                    <Text style={styles.analysisDetailTitle}>Recommendations:</Text>
                    {analysisResults.recommendations.map((rec, index) => (
                      <Text key={index} style={styles.recommendationItem}>• {rec}</Text>
                    ))}
                    
                    <Text style={styles.analysisDetail}>
                      AI Confidence: {analysisResults.aiConfidence}%
                    </Text>
                  </View>
                </View>
              )}
            </View>
          ) : (
            <View style={styles.loadingSession}>
              <ActivityIndicator size="large" color="#4f46e5" />
              <Text style={styles.loadingText}>Receiving brushing data...</Text>
            </View>
          )}
        </View>
      )}
    </SafeAreaView>
  );
};

// Helper function to format zone names
const formatZoneName = (zone: string): string => {
  return zone
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

// Type definition for brushing session data
interface BrushingSession {
  sessionId: string;
  startTime: string;
  duration: number;
  pressureLevel: 'low' | 'medium' | 'high';
  zonesCovered: string[];
  zonesMissed: string[];
  brushingScore: number;
  timestamp: string;
}

// Styles
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    padding: 16,
    backgroundColor: '#4f46e5',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: 'white',
    opacity: 0.9,
  },
  scanSection: {
    flex: 1,
    padding: 16,
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  scanButton: {
    backgroundColor: '#4f46e5',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    marginVertical: 20,
  },
  scanButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  loader: {
    marginVertical: 20,
  },
  devicesContainer: {
    width: '100%',
    marginTop: 20,
  },
  devicesTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  devicesList: {
    width: '100%',
  },
  deviceItem: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 8,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  deviceName: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  deviceId: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  sessionSection: {
    flex: 1,
    padding: 16,
  },
  sessionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  connectedDevice: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  disconnectButton: {
    backgroundColor: '#ef4444',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
  },
  disconnectButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  loadingSession: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  sessionData: {
    flex: 1,
  },
  sessionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  sessionDetails: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
    marginBottom: 16,
  },
  sessionDetail: {
    fontSize: 16,
    marginBottom: 8,
  },
  sessionDetailTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 12,
    marginBottom: 8,
  },
  zonesList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  zoneItem: {
    backgroundColor: '#f3f4f6',
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 4,
    marginRight: 8,
    marginBottom: 8,
  },
  zoneText: {
    color: '#047857',
  },
  zoneTextMissed: {
    color: '#b91c1c',
  },
  imageSection: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
    marginBottom: 16,
    alignItems: 'center',
  },
  imageTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
    alignSelf: 'flex-start',
  },
  imageContainer: {
    width: '100%',
    height: 200,
    backgroundColor: '#f3f4f6',
    borderRadius: 8,
    overflow: 'hidden',
    marginBottom: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imagePreview: {
    width: '100%',
    height: '100%',
  },
  syncButton: {
    backgroundColor: '#4f46e5',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    marginTop: 8,
  },
  syncButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  smallLoader: {
    marginTop: 12,
  },
  analysisSection: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  analysisTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  analysisDetails: {
    marginBottom: 12,
  },
  analysisDetail: {
    fontSize: 16,
    marginBottom: 8,
  },
  analysisDetailTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 12,
    marginBottom: 8,
  },
  recommendationItem: {
    fontSize: 15,
    marginBottom: 4,
    paddingLeft: 8,
  },
  placeholderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholderText: {
    fontSize: 16,
    color: '#666',
  },
});

export default BluetoothToothbrush; 