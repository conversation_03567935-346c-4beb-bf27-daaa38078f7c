import React from 'react';

// Define types for analysis results
export interface BrushingSessionData {
  sessionId: string;
  timestamp: number;
  duration: number; // in seconds
  zonesCovered: Record<string, number>; // zone name → seconds spent
  pressure: { average: number; max: number }; // on scale 1-10
  images: string[]; // base64 encoded images
}

export interface AnalysisResult {
  sessionId: string;
  analysisTimestamp: number;
  cavityDetection?: {
    detected: boolean;
    locations: Array<{ tooth: string; severity: 'low' | 'medium' | 'high'; confidence: number }>;
  };
  plaqueDetection?: {
    score: number; // 0-100
    problemAreas: Array<{ zone: string; coverage: number }>;
  };
  gumInflammation?: {
    detected: boolean;
    severity: 'none' | 'mild' | 'moderate' | 'severe';
    locations: string[];
  };
  toothMovement?: {
    detected: boolean;
    changesFrom: string; // date of comparison baseline
    locations: Array<{ tooth: string; movementMm: number; direction: string }>;
  };
  oralCancerMarkers?: {
    detected: boolean;
    suspiciousAreas: Array<{ location: string; confidence: number; recommendation: string }>;
  };
  recommendations: Array<{ type: string; priority: number; text: string }>;
}

interface BrushingAnalysisServiceProps {
  children: React.ReactNode;
}

// Create context for the analysis service
export const AnalysisContext = React.createContext<{
  analyzeSession: (session: BrushingSessionData) => Promise<AnalysisResult>;
  getHistoricalAnalysis: (userId: string) => Promise<AnalysisResult[]>;
  isAnalyzing: boolean;
  latestResult: AnalysisResult | null;
}>({
  analyzeSession: async () => ({
    sessionId: '',
    analysisTimestamp: Date.now(),
    recommendations: []
  }),
  getHistoricalAnalysis: async () => [],
  isAnalyzing: false,
  latestResult: null
});

const BrushingAnalysisService: React.FC<BrushingAnalysisServiceProps> = ({ children }) => {
  const [isAnalyzing, setIsAnalyzing] = React.useState(false);
  const [latestResult, setLatestResult] = React.useState<AnalysisResult | null>(null);

  // This would be replaced with actual API calls in a production app
  const analyzeSession = async (session: BrushingSessionData): Promise<AnalysisResult> => {
    setIsAnalyzing(true);
    
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Mock analysis result
      const result: AnalysisResult = {
        sessionId: session.sessionId,
        analysisTimestamp: Date.now(),
        cavityDetection: {
          detected: Math.random() > 0.7,
          locations: Math.random() > 0.7 ? [
            { tooth: '36', severity: 'low', confidence: 0.82 },
            { tooth: '17', severity: 'medium', confidence: 0.91 }
          ] : []
        },
        plaqueDetection: {
          score: Math.floor(Math.random() * 40) + 60, // 60-100
          problemAreas: [
            { zone: 'Upper Left Molars', coverage: Math.random() * 0.4 },
            { zone: 'Lower Front', coverage: Math.random() * 0.3 }
          ]
        },
        gumInflammation: {
          detected: Math.random() > 0.6,
          severity: Math.random() > 0.7 ? 'moderate' : 'mild',
          locations: ['Upper Right Gums', 'Lower Left Gums']
        },
        toothMovement: session.images.length > 3 ? {
          detected: Math.random() > 0.8,
          changesFrom: '2023-12-15',
          locations: Math.random() > 0.7 ? [
            { tooth: '11', movementMm: 0.3, direction: 'mesial' }
          ] : []
        } : undefined,
        oralCancerMarkers: session.images.length > 5 ? {
          detected: Math.random() > 0.95, // Very rare
          suspiciousAreas: Math.random() > 0.95 ? [
            { 
              location: 'Left cheek mucosa', 
              confidence: 0.68, 
              recommendation: 'Consult with dentist for visual examination' 
            }
          ] : []
        } : undefined,
        recommendations: [
          {
            type: 'brushing',
            priority: 1,
            text: 'Increase brushing time in the upper left molar region'
          },
          {
            type: 'flossing',
            priority: 2,
            text: 'Floss between teeth 24-25 and 35-36 more thoroughly'
          },
          {
            type: 'checkup',
            priority: Math.random() > 0.7 ? 1 : 3,
            text: 'Schedule a dental checkup within the next 30 days'
          }
        ]
      };
      
      setLatestResult(result);
      return result;
      
    } catch (error) {
      console.error('Error analyzing brushing session:', error);
      throw error;
    } finally {
      setIsAnalyzing(false);
    }
  };
  
  const getHistoricalAnalysis = async (userId: string): Promise<AnalysisResult[]> => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Return mock historical data
    return Array(5).fill(null).map((_, i) => ({
      sessionId: `session-${Date.now() - i * 86400000}`,
      analysisTimestamp: Date.now() - i * 86400000,
      cavityDetection: {
        detected: i === 1 || i === 4,
        locations: i === 1 || i === 4 ? [
          { tooth: '36', severity: 'low', confidence: 0.82 },
        ] : []
      },
      plaqueDetection: {
        score: Math.floor(Math.random() * 40) + 60, // 60-100
        problemAreas: [
          { zone: 'Upper Left Molars', coverage: Math.random() * 0.4 },
        ]
      },
      gumInflammation: {
        detected: i < 2,
        severity: i === 0 ? 'moderate' : 'mild',
        locations: ['Upper Right Gums']
      },
      recommendations: [
        {
          type: 'brushing',
          priority: i < 3 ? 1 : 2,
          text: 'Focus more on gumline while brushing'
        },
        {
          type: 'checkup',
          priority: i === 0 ? 1 : 3,
          text: i === 0 ? 'Schedule a dental checkup soon' : 'Continue regular checkups'
        }
      ]
    }));
  };

  return (
    <AnalysisContext.Provider 
      value={{ 
        analyzeSession, 
        getHistoricalAnalysis,
        isAnalyzing,
        latestResult
      }}
    >
      {children}
    </AnalysisContext.Provider>
  );
};

// Custom hook to use the analysis service
export const useAnalysisService = () => {
  const context = React.useContext(AnalysisContext);
  if (context === undefined) {
    throw new Error('useAnalysisService must be used within a BrushingAnalysisService');
  }
  return context;
};

export default BrushingAnalysisService; 