import React, { useState, useEffect } from 'react';
import { Card } from './common';
import ChatTest from './chat/ChatTest';
import { checkSystemStatus, runBrowserDiagnostics, fixCommonIssues } from '../lib/services/diagnostics';

export default function SystemDiagnostics() {
  const [systemStatus, setSystemStatus] = useState(null);
  const [browserDiagnostics, setBrowserDiagnostics] = useState(null);
  const [fixing, setFixing] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    checkStatus();
  }, []);

  const checkStatus = async () => {
    try {
      const [status, diagnostics] = await Promise.all([
        checkSystemStatus(),
        runBrowserDiagnostics()
      ]);
      setSystemStatus(status);
      setBrowserDiagnostics(diagnostics);
    } catch (err) {
      setError(err.message);
    }
  };

  const handleFix = async () => {
    setFixing(true);
    try {
      const result = await fixCommonIssues();
      if (result.success) {
        await checkStatus();
      } else {
        setError('Unable to fix all issues automatically. Please try manual steps.');
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setFixing(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <h2 className="text-xl font-semibold text-white mb-4">System Status</h2>
        
        {error && (
          <div className="mb-4 p-3 bg-red-500/10 border border-red-500/20 rounded text-red-400">
            {error}
          </div>
        )}

        {systemStatus && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-white/80">Overall Status</span>
              <span className={`px-2 py-1 rounded ${
                systemStatus.status === 'operational' 
                  ? 'bg-green-500/10 text-green-400'
                  : 'bg-red-500/10 text-red-400'
              }`}>
                {systemStatus.status === 'operational' ? 'Operational' : 'Issues Detected'}
              </span>
            </div>

            <div className="grid gap-4">
              {Object.entries(systemStatus.checks).map(([key, value]) => (
                <div key={key} className="flex items-center justify-between bg-white/5 p-3 rounded">
                  <span className="capitalize text-white/80">{key}</span>
                  <span className={value ? 'text-green-400' : 'text-red-400'}>
                    {value ? '✓' : '✗'}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}

        {browserDiagnostics && (
          <div className="mt-6">
            <h3 className="text-lg font-medium text-white mb-3">Browser Diagnostics</h3>
            <div className="grid gap-3">
              <div className="flex justify-between items-center">
                <span className="text-white/80">Online Status</span>
                <span className={browserDiagnostics.online ? 'text-green-400' : 'text-red-400'}>
                  {browserDiagnostics.online ? 'Connected' : 'Offline'}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-white/80">Cookies Enabled</span>
                <span className={browserDiagnostics.cookies ? 'text-green-400' : 'text-red-400'}>
                  {browserDiagnostics.cookies ? 'Yes' : 'No'}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-white/80">Local Storage</span>
                <span className={browserDiagnostics.localStorage ? 'text-green-400' : 'text-red-400'}>
                  {browserDiagnostics.localStorage ? 'Available' : 'Unavailable'}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-white/80">SSL/HTTPS</span>
                <span className={browserDiagnostics.ssl ? 'text-green-400' : 'text-red-400'}>
                  {browserDiagnostics.ssl ? 'Secure' : 'Insecure'}
                </span>
              </div>
            </div>
          </div>
        )}

        <div className="mt-6 flex gap-4">
          <button
            onClick={checkStatus}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-400 transition-colors"
          >
            Refresh Status
          </button>
          <button
            onClick={handleFix}
            disabled={fixing || systemStatus?.status === 'operational'}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-400 disabled:opacity-50 transition-colors"
          >
            {fixing ? 'Fixing...' : 'Fix Issues'}
          </button>
        </div>
      </Card>

      <Card>
        <h2 className="text-xl font-semibold text-white mb-4">AI Functionality Test</h2>
        <ChatTest />
      </Card>

      <Card>
        <h2 className="text-xl font-semibold text-white mb-4">Troubleshooting Steps</h2>
        <div className="space-y-4">
          <div className="p-4 bg-white/5 rounded">
            <h3 className="font-medium text-white mb-2">1. Browser Cache and Cookies</h3>
            <ul className="list-disc list-inside text-white/80 space-y-1">
              <li>Clear browser cache and cookies</li>
              <li>Try opening in an incognito/private window</li>
              <li>Disable browser extensions temporarily</li>
            </ul>
          </div>

          <div className="p-4 bg-white/5 rounded">
            <h3 className="font-medium text-white mb-2">2. Connection Issues</h3>
            <ul className="list-disc list-inside text-white/80 space-y-1">
              <li>Check your internet connection</li>
              <li>Try a different network if available</li>
              <li>Check if other websites are loading</li>
            </ul>
          </div>

          <div className="p-4 bg-white/5 rounded">
            <h3 className="font-medium text-white mb-2">3. Browser Compatibility</h3>
            <ul className="list-disc list-inside text-white/80 space-y-1">
              <li>Update your browser to the latest version</li>
              <li>Try a different browser (Chrome, Firefox, Safari)</li>
              <li>Enable JavaScript and cookies</li>
            </ul>
          </div>

          <div className="p-4 bg-white/5 rounded">
            <h3 className="font-medium text-white mb-2">4. Security Settings</h3>
            <ul className="list-disc list-inside text-white/80 space-y-1">
              <li>Check SSL/HTTPS connection</li>
              <li>Review browser security settings</li>
              <li>Disable VPN or proxy temporarily</li>
            </ul>
          </div>
        </div>
      </Card>
    </div>
  );
}