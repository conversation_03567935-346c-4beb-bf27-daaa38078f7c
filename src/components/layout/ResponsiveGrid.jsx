import React from 'react';

/**
 * ResponsiveGrid component creates a responsive grid layout that adapts to different screen sizes.
 * It can be configured for different column counts at various breakpoints.
 * 
 * @param {Object} props - Component props
 * @param {ReactNode} props.children - Child elements
 * @param {string} props.className - Additional CSS classes
 * @param {number} props.cols - Default number of columns (1-12)
 * @param {number} props.smCols - Columns for small screens (640px+)
 * @param {number} props.mdCols - Columns for medium screens (768px+)
 * @param {number} props.lgCols - Columns for large screens (1024px+)
 * @param {number} props.xlCols - Columns for extra large screens (1280px+)
 * @param {string} props.gap - Gap size: 'none', 'sm', 'md', 'lg', 'xl' (default: 'md')
 * @param {boolean} props.autoFit - Use auto-fit instead of fixed columns
 * @param {string} props.minItemWidth - Minimum item width when using autoFit (e.g. '250px')
 */
const ResponsiveGrid = ({
  children,
  className = '',
  cols = 1,
  smCols,
  mdCols,
  lgCols,
  xlCols,
  gap = 'md',
  autoFit = false,
  minItemWidth = '250px',
  ...rest
}) => {
  // If autoFit is true, use grid-template-columns: repeat(auto-fit, minmax(minItemWidth, 1fr))
  // Otherwise, use specific column counts for each breakpoint
  const gridTemplateColumns = autoFit
    ? `repeat(auto-fit, minmax(${minItemWidth}, 1fr))`
    : `repeat(${cols}, minmax(0, 1fr))`;

  // Responsive column counts
  const smGridCols = smCols ? `sm:grid-cols-${smCols}` : '';
  const mdGridCols = mdCols ? `md:grid-cols-${mdCols}` : '';
  const lgGridCols = lgCols ? `lg:grid-cols-${lgCols}` : '';
  const xlGridCols = xlCols ? `xl:grid-cols-${xlCols}` : '';

  // Gap sizes
  const gapSizes = {
    none: 'gap-0',
    xs: 'gap-1 md:gap-2',
    sm: 'gap-2 md:gap-3',
    md: 'gap-4 md:gap-6',
    lg: 'gap-6 md:gap-8',
    xl: 'gap-8 md:gap-10'
  };

  const gapClass = gapSizes[gap] || gapSizes.md;

  return (
    <div
      className={`
        grid
        ${autoFit ? '' : `grid-cols-${cols}`}
        ${!autoFit ? `${smGridCols} ${mdGridCols} ${lgGridCols} ${xlGridCols}` : ''}
        ${gapClass}
        ${className}
      `}
      style={autoFit ? { gridTemplateColumns } : {}}
      {...rest}
    >
      {children}
    </div>
  );
};

export default ResponsiveGrid; 