import React from 'react';

/**
 * ResponsiveContainer component provides consistent layout margins and padding
 * across different screen sizes, with special handling for mobile devices.
 * 
 * @param {Object} props - Component props
 * @param {ReactNode} props.children - Child elements
 * @param {string} props.className - Additional CSS classes
 * @param {string} props.as - HTML element to render (default: 'div')
 * @param {string} props.size - Container size: 'sm', 'md', 'lg', 'xl', 'full' (default: 'md')
 * @param {boolean} props.noPadding - Remove horizontal padding
 * @param {boolean} props.noVerticalPadding - Remove vertical padding
 * @param {boolean} props.centered - Center content horizontally
 * @param {Object} props.rest - Additional props passed to the container element
 */
const ResponsiveContainer = ({
  children,
  className = '',
  as: Component = 'div',
  size = 'md',
  noPadding = false,
  noVerticalPadding = false,
  centered = false,
  ...rest
}) => {
  // Container width based on size
  const sizeClasses = {
    sm: 'max-w-screen-sm',
    md: 'max-w-screen-md',
    lg: 'max-w-screen-lg',
    xl: 'max-w-screen-xl',
    '2xl': 'max-w-screen-2xl',
    full: 'max-w-full'
  };

  // Horizontal padding that adjusts for different screen sizes
  const paddingX = noPadding 
    ? '' 
    : 'px-4 sm:px-6 md:px-8 lg:px-10';

  // Vertical padding
  const paddingY = noVerticalPadding 
    ? '' 
    : 'py-4 sm:py-6 md:py-8';

  // Center content if requested
  const centerClasses = centered 
    ? 'mx-auto' 
    : '';

  return (
    <Component
      className={`
        w-full
        ${sizeClasses[size] || sizeClasses.md}
        ${paddingX}
        ${paddingY}
        ${centerClasses}
        ${className}
      `}
      {...rest}
    >
      {children}
    </Component>
  );
};

export default ResponsiveContainer; 