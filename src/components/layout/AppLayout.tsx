import React, { ReactNode } from 'react';
import { Link } from 'react-router-dom';
import MainNavigation from '../navigation/MainNavigation';
import NewSignInButton from '../auth/NewSignInButton';

interface AppLayoutProps {
  children: ReactNode;
}

const AppLayout: React.FC<AppLayoutProps> = ({ children }) => {
  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Header */}
      <header className="fixed top-0 left-0 right-0 bg-gray-900 border-b border-gray-800 z-30">
        <div className="flex items-center justify-between px-4 lg:px-8 py-3">
          <div className="flex items-center">
            <Link to="/" className="flex items-center">
              <img
                src="/logo.svg"
                alt="Smilo.Dental"
                className="h-8 w-auto mr-3"
                onError={(e) => {
                  // Fallback if logo image is missing
                  (e.target as HTMLImageElement).style.display = 'none';
                }}
              />
              <span className="text-xl font-bold bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent">
                Smilo.Dental
              </span>
            </Link>
          </div>

          <div className="flex items-center space-x-4">
            <div className="hidden md:flex items-center space-x-3">
              <Link
                to="/tools"
                className="px-3 py-1.5 text-sm text-gray-300 hover:text-white transition-colors"
              >
                AI Tools
              </Link>
              <Link
                to="/leads/marketplace"
                className="px-3 py-1.5 text-sm text-gray-300 hover:text-white transition-colors"
              >
                Lead Marketplace
              </Link>
              <Link
                to="/matching/find-provider"
                className="px-3 py-1.5 text-sm text-gray-300 hover:text-white transition-colors"
              >
                Find Provider
              </Link>
            </div>

            <NewSignInButton
              userType="patient"
              className="px-4 py-1.5 bg-gradient-to-r from-cyan-600 to-blue-600 rounded-lg text-white text-sm font-medium shadow-md hover:shadow-lg hover:shadow-cyan-500/20 transition"
            >
              Sign In
            </NewSignInButton>
          </div>
        </div>
      </header>

      {/* Navigation */}
      <MainNavigation />

      {/* Main Content */}
      <main className="lg:pl-64 pt-16">
        <div className="p-4 lg:p-8">
          {children}
        </div>
      </main>

      {/* Footer */}
      <footer className="lg:pl-64 bg-gray-900 border-t border-gray-800 py-6 px-4 lg:px-8">
        <div className="flex flex-col md:flex-row justify-between items-center">
          <div className="mb-4 md:mb-0">
            <p className="text-gray-400 text-sm">
              &copy; {new Date().getFullYear()} Smilo.Dental. All rights reserved.
            </p>
          </div>

          <div className="flex space-x-6">
            <Link to="/terms" className="text-gray-400 hover:text-white text-sm transition-colors">
              Terms of Service
            </Link>
            <Link to="/privacy" className="text-gray-400 hover:text-white text-sm transition-colors">
              Privacy Policy
            </Link>
            <Link to="/partnerships" className="text-gray-400 hover:text-white text-sm transition-colors">
              Partnerships
            </Link>
            <Link to="/contact" className="text-gray-400 hover:text-white text-sm transition-colors">
              Contact
            </Link>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default AppLayout;