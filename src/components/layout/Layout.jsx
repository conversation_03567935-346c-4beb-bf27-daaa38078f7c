import React, { useRef } from "react";
import Header from "../navigation/Header";
import Footer from "../navigation/Footer";
import AdminNav from "../admin/AdminNav";
import AdminHeader from "../admin/AdminHeader";
import { useAdmin } from "../../lib/hooks/useAdmin";
import { useLocation } from "react-router-dom";

export default function Layout({ children }) {
  const { isAdmin } = useAdmin();
  const location = useLocation();
  const mainContentRef = useRef(null);
  
  // Check if currently on an admin page
  const isAdminPage = location.pathname.startsWith('/admin');
  
  // Only show admin sidebar when user is an admin AND not already on an admin page
  const showAdminSidebar = isAdmin && !isAdminPage;

  return (
    <div className="min-h-screen w-full flex flex-col relative">
      {/* Animated background elements */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Base gradients */}
        <div className="absolute inset-0 bg-gradient-to-br from-indigo-900/30 via-gray-900 to-purple-900/30" />
        
        {/* Top right blue glow */}
        <div className="absolute top-0 right-0 w-[50%] h-[50%] bg-blue-500/20 blur-[120px] rounded-full opacity-60" />
        
        {/* Bottom left purple glow */}
        <div className="absolute bottom-0 left-0 w-[50%] h-[50%] bg-purple-500/20 blur-[120px] rounded-full opacity-60" />
        
        {/* Subtle radial gradients */}
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_top_right,rgba(99,102,241,0.15),transparent_50%)]" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_bottom_left,rgba(168,85,247,0.15),transparent_50%)]" />
      </div>
      
      <div className="relative flex-grow flex flex-col w-full">
        {/* Show main site header when not on admin page */}
        {!isAdminPage && <Header />}
        
        {/* Main content layout - changes to a row flex when admin sidebar is visible */}
        <div className={`flex-grow w-full flex ${showAdminSidebar ? 'flex-row' : 'flex-col'}`}>
          {/* Show admin sidebar to the left when in admin mode */}
          {showAdminSidebar && (
            <div className="w-64 border-r border-gray-700/40 bg-gradient-to-b from-gray-800 to-gray-900 shadow-xl">
              <AdminNav integrated={true} />
            </div>
          )}
          
          {/* Main content area */}
          <main 
            ref={mainContentRef}
            className={`${showAdminSidebar ? 'flex-1' : 'w-full'} flex-grow`}
          >
            <div className="scroll-container">
              {children}
            </div>
          </main>
        </div>
        
        <Footer />
      </div>
    </div>
  );
}
