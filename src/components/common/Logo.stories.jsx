import React from 'react';
import Logo from './Logo';

export default {
  title: 'Common/Logo',
  component: Logo,
  argTypes: {
    size: {
      control: { type: 'select' },
      options: ['small', 'default', 'large']
    }
  }
};

// Default logo
export const Default = {
  args: {
    size: 'default'
  }
};

// Small logo
export const Small = {
  args: {
    size: 'small'
  }
};

// Large logo
export const Large = {
  args: {
    size: 'large'
  }
};

// With custom class
export const WithCustomClass = {
  args: {
    size: 'default',
    className: 'shadow-lg'
  }
};