import React, { useEffect } from 'react';

/**
 * Component to preload critical resources
 * This helps improve performance by loading critical resources early
 */
const ResourcePreloader = () => {
  useEffect(() => {
    // Function to preload images
    const preloadImage = (url) => {
      const img = new Image();
      img.src = url;
    };

    // Function to preload CSS
    const preloadCSS = (url) => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'style';
      link.href = url;
      document.head.appendChild(link);
    };

    // Function to preload JavaScript
    const preloadScript = (url) => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'script';
      link.href = url;
      document.head.appendChild(link);
    };

    // Function to preload fonts
    const preloadFont = (url, type = 'woff2') => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'font';
      link.type = `font/${type}`;
      link.href = url;
      link.crossOrigin = 'anonymous';
      document.head.appendChild(link);
    };

    // Preload critical images
    preloadImage('/images/smilo-logo1.jpg');
    
    // Preload Google Fonts
    const fontUrl = 'https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@400;500;600;700&display=swap';
    preloadCSS(fontUrl);
    
    // Add font-display: swap to all font faces
    const style = document.createElement('style');
    style.textContent = `
      @font-face {
        font-display: swap !important;
      }
    `;
    document.head.appendChild(style);
    
    // Add meta tags for better performance
    const metaViewport = document.createElement('meta');
    metaViewport.name = 'viewport';
    metaViewport.content = 'width=device-width, initial-scale=1, shrink-to-fit=no';
    document.head.appendChild(metaViewport);
    
    // Add DNS prefetch for external domains
    const domains = [
      'fonts.googleapis.com',
      'fonts.gstatic.com',
      'www.googletagmanager.com'
    ];
    
    domains.forEach(domain => {
      const link = document.createElement('link');
      link.rel = 'dns-prefetch';
      link.href = `//${domain}`;
      document.head.appendChild(link);
      
      // Also add preconnect for critical domains
      const preconnect = document.createElement('link');
      preconnect.rel = 'preconnect';
      preconnect.href = `//${domain}`;
      preconnect.crossOrigin = 'anonymous';
      document.head.appendChild(preconnect);
    });
    
    // Clean up function
    return () => {
      // No cleanup needed as these are permanent additions to the head
    };
  }, []);

  // This component doesn't render anything
  return null;
};

export default ResourcePreloader;
