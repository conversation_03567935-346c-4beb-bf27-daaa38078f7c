import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import cookieManager, { 
  COOKIE_CATEGORIES,
  hasConsentBeenGiven,
  isConsentRenewalNeeded,
  getCookieBannerText
} from '../../lib/utils/cookieManager';

const CookieConsent = () => {
  const [showConsent, setShowConsent] = useState(false);
  const [showCustomizePanel, setShowCustomizePanel] = useState(false);
  const [reduceMotion, setReduceMotion] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [locale] = useState('en'); // Could be extended for multilingual support
  const cookieText = getCookieBannerText(locale);
  
  // State for custom cookie preferences
  const [preferences, setPreferences] = useState({
    [COOKIE_CATEGORIES.NECESSARY]: true, // Always true and disabled
    [COOKIE_CATEGORIES.PREFERENCES]: true,
    [COOKIE_CATEGORIES.ANALYTICS]: true,
    [COOKIE_CATEGORIES.MARKETING]: false,
    [COOKIE_CATEGORIES.THIRD_PARTY]: false
  });

  useEffect(() => {
    // Check if the user has already given consent
    const hasConsent = hasConsentBeenGiven();
    const needsRenewal = isConsentRenewalNeeded();

    // Check if animations should be reduced
    setReduceMotion(false);
    setIsMobile(typeof window !== 'undefined' && window.innerWidth <= 768);

    if (!hasConsent || needsRenewal) {
      // Show the consent banner after a small delay
      const timer = setTimeout(() => {
        setShowConsent(true);
      }, 1500);

      return () => clearTimeout(timer);
    }

    // Re-check on window resize
    const handleResize = () => {
      setReduceMotion(false);
      setIsMobile(typeof window !== 'undefined' && window.innerWidth <= 768);
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const handleAcceptAll = () => {
    cookieManager.saveFullConsent();
    setShowConsent(false);
    setShowCustomizePanel(false);
  };

  const handleDeclineAll = () => {
    cookieManager.saveDeclineConsent();
    setShowConsent(false);
    setShowCustomizePanel(false);
  };
  
  const handleTogglePreference = (category) => {
    if (category === COOKIE_CATEGORIES.NECESSARY) return; // Can't toggle necessary cookies
    
    setPreferences(prev => ({
      ...prev,
      [category]: !prev[category]
    }));
  };
  
  const handleSavePreferences = () => {
    cookieManager.saveCustomConsent(preferences);
    setShowConsent(false);
    setShowCustomizePanel(false);
  };
  
  const toggleCustomizePanel = () => {
    setShowCustomizePanel(!showCustomizePanel);
  };

  // Simplified animation for mobile and reduced motion
  const animationVariants = {
    hidden: reduceMotion ? { opacity: 0 } : { y: 100, opacity: 0 },
    visible: reduceMotion ? { opacity: 1 } : { y: 0, opacity: 1 },
    exit: reduceMotion ? { opacity: 0 } : { y: 100, opacity: 0 }
  };

  // Simplified transition for mobile and reduced motion
  const transitionProps = reduceMotion ? {
    duration: 0.2
  } : {
    duration: 0.3
  };

  return (
    <AnimatePresence>
      {showConsent && (
        <motion.div
          initial="hidden"
          animate="visible"
          exit="exit"
          variants={animationVariants}
          transition={transitionProps}
          className={`fixed bottom-0 left-0 right-0 z-50 ${isMobile ? 'p-3' : 'p-4'} bg-gradient-to-r from-gray-900 to-gray-800 border-t border-gray-700 shadow-lg safe-area-padding-horizontal`}
        >
          {!showCustomizePanel ? (
            // Main consent banner
            <div className="max-w-6xl mx-auto flex flex-col md:flex-row items-center justify-between">
              <div className="mb-4 md:mb-0 text-center md:text-left">
                <h3 className={`text-white ${isMobile ? 'text-base' : 'text-lg'} font-medium mb-2`}>{cookieText.title}</h3>
                <p className={`text-gray-300 ${isMobile ? 'text-xs' : 'text-sm'} md:max-w-2xl`}>
                  {cookieText.description}{' '}
                  <a href="/privacy" className="text-blue-400 hover:underline">
                    {cookieText.privacyPolicy}
                  </a>
                </p>
              </div>
              <div className="flex flex-wrap gap-3 justify-center md:justify-end">
                <button
                  onClick={handleDeclineAll}
                  className={`${isMobile ? 'px-3 py-2 text-xs' : 'px-4 py-2 text-sm'} bg-gray-700 hover:bg-gray-600 text-white rounded-md transition-colors mobile-optimize touch-target-mobile`}
                >
                  {cookieText.decline}
                </button>
                <button
                  onClick={toggleCustomizePanel}
                  className={`${isMobile ? 'px-3 py-2 text-xs' : 'px-4 py-2 text-sm'} bg-gray-600 hover:bg-gray-500 text-white rounded-md transition-colors mobile-optimize touch-target-mobile`}
                >
                  {cookieText.customize}
                </button>
                <button
                  onClick={handleAcceptAll}
                  className={`${isMobile ? 'px-3 py-2 text-xs' : 'px-4 py-2 text-sm'} bg-indigo-600 hover:bg-indigo-700 text-white rounded-md transition-colors mobile-optimize touch-target-mobile`}
                >
                  {cookieText.acceptAll}
                </button>
              </div>
            </div>
          ) : (
            // Customize panel with detailed cookie options
            <div className="max-w-6xl mx-auto">
              <div className="mb-4 text-center md:text-left">
                <h3 className={`text-white ${isMobile ? 'text-base' : 'text-lg'} font-medium mb-2`}>Cookie Preferences</h3>
                <p className={`text-gray-300 ${isMobile ? 'text-xs' : 'text-sm'} mb-4`}>
                  Customize which cookies you want to allow. Learn more in our{' '}
                  <a href="/privacy" className="text-blue-400 hover:underline">
                    Privacy Policy
                  </a>
                </p>
              </div>
              
              <div className="space-y-4 mb-6">
                {Object.values(COOKIE_CATEGORIES).map(category => (
                  <div key={category} className="bg-gray-800/50 p-4 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center">
                        <h4 className="text-white font-medium">{cookieText.categories[category].title}</h4>
                        {category === COOKIE_CATEGORIES.NECESSARY && (
                          <span className="ml-2 text-xs bg-blue-500/30 text-blue-200 py-0.5 px-2 rounded">Required</span>
                        )}
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input 
                          type="checkbox"
                          className="sr-only peer"
                          checked={preferences[category]}
                          onChange={() => handleTogglePreference(category)}
                          disabled={category === COOKIE_CATEGORIES.NECESSARY}
                        />
                        <div className={`w-11 h-6 bg-gray-700 peer-focus:outline-none rounded-full peer 
                          ${preferences[category] ? 'after:translate-x-full after:border-white bg-indigo-600' : 'after:border-gray-300'} 
                          after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all
                          ${category === COOKIE_CATEGORIES.NECESSARY ? 'opacity-75' : 'peer-hover:brightness-110'}`}>
                        </div>
                      </label>
                    </div>
                    <p className={`text-gray-300 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                      {cookieText.categories[category].description}
                    </p>
                  </div>
                ))}
              </div>
              
              <div className="flex flex-wrap justify-end gap-3">
                <button
                  onClick={toggleCustomizePanel}
                  className={`${isMobile ? 'px-3 py-2 text-xs' : 'px-4 py-2 text-sm'} bg-gray-700 hover:bg-gray-600 text-white rounded-md transition-colors`}
                >
                  Back
                </button>
                <button
                  onClick={handleSavePreferences}
                  className={`${isMobile ? 'px-3 py-2 text-xs' : 'px-4 py-2 text-sm'} bg-indigo-600 hover:bg-indigo-700 text-white rounded-md transition-colors`}
                >
                  Save Preferences
                </button>
              </div>
            </div>
          )}
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default CookieConsent;