import React from 'react';
import { fadeInUp, glowOnHover } from '../../lib/utils/animations';

export function Card({ 
  title, 
  children, 
  className = "", 
  animated = false,
  hoverable = false,
  glowEffect = false,
  variant = 'default'
}) {
  const variants = {
    default: 'bg-gradient-to-b from-gray-900 to-gray-800',
    glass: 'bg-white/5 backdrop-blur-md',
    dark: 'bg-gray-950',
    accent: 'bg-gradient-to-br from-indigo-900/40 via-gray-900 to-purple-900/40',
    glow: 'bg-gradient-to-b from-gray-900 to-gray-800'
  };

  return (
    <div 
      className={`
        relative rounded-2xl p-6 
        ${variants[variant]}
        ${hoverable ? 'transition-all duration-300 hover:shadow-lg hover:shadow-indigo-500/10 hover:-translate-y-1' : ''}
        ${glowEffect ? 'before:absolute before:inset-0 before:-z-10 before:rounded-2xl before:bg-gradient-to-r before:from-indigo-500/20 before:via-purple-500/20 before:to-blue-500/20 before:blur-xl before:opacity-50 before:transition-opacity hover:before:opacity-75' : ''}
        ${animated ? 'animate-fadeIn' : ''}
        border border-white/10
        ${className}
      `}
    >
      {title && (
        <h3 className="text-xl font-semibold bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 via-purple-400 to-indigo-400 mb-4">{title}</h3>
      )}
      {children}
    </div>
  );
}

export default Card;