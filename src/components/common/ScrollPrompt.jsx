import React from 'react';
import { motion } from 'framer-motion';

export default function ScrollPrompt({ text = "Scroll to Learn More", className = "" }) {
  return (
    <div className={`flex flex-col items-center ${className}`}>
      <motion.svg 
        className="w-5 h-5 text-white/70 mb-2" 
        fill="none" 
        viewBox="0 0 24 24"
        stroke="currentColor"
        animate={{ y: [0, 4, 0] }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      >
        <path 
          strokeLinecap="round" 
          strokeLinejoin="round" 
          strokeWidth={1.5} 
          d="M19 14l-7 7m0 0l-7-7m7 7V3"
        />
      </motion.svg>
      <span className="text-sm text-white/70 font-light tracking-wide">
        {text}
      </span>
    </div>
  );
}