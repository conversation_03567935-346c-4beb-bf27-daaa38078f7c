import React, { useState, useEffect, useRef } from 'react';
import { useInView } from 'react-intersection-observer';
import { usePerformance } from '../../hooks/usePerformance';

/**
 * OptimizedImage component that handles:
 * - Lazy loading
 * - Responsive image sizing
 * - Blur-up loading effect
 * - Network-aware loading
 * - WebP/AVIF format for supported browsers
 * - Reduced quality for slow connections
 * 
 * @param {Object} props Component props
 * @param {string} props.src Image source URL
 * @param {string} props.alt Image alt text
 * @param {string} props.className Additional CSS classes
 * @param {number} props.width Image width
 * @param {number} props.height Image height
 * @param {boolean} props.critical If true, load image immediately without lazy loading
 * @param {Function} props.onLoad Callback when image loads
 * @param {Object} props.style Additional inline styles
 * @param {string} props.objectFit Object-fit property (cover, contain, etc.)
 * @param {boolean} props.blurUp Enable blur-up loading effect
 * @returns {JSX.Element} OptimizedImage component
 */
const OptimizedImage = ({
  src,
  alt,
  className = '',
  width,
  height,
  critical = false,
  onLoad,
  style = {},
  objectFit = 'cover',
  blurUp = true,
  ...props
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isError, setIsError] = useState(false);
  const imgRef = useRef(null);
  const { ref: inViewRef, inView } = useInView({
    triggerOnce: true,
    rootMargin: '200px 0px', // Start loading when image is 200px from viewport
  });
  
  // Get performance metrics
  const { isSlowNetwork, networkStatus } = usePerformance();
  
  // Create a blur-up tiny preview
  const [tinyPreview, setTinyPreview] = useState(null);
  
  // Set loading priority based on connection and critical flag
  const loadingPriority = critical ? 'eager' : 'lazy';
  
  // Create a combined ref callback to attach both refs
  const setRefs = (element) => {
    // Save a reference to the node
    imgRef.current = element;
    // Set the inView ref
    inViewRef(element);
  };
  
  // Generate tiny preview
  useEffect(() => {
    if (!blurUp || !src || isError) return;
    
    // Skip blur-up on very slow connections to save data
    if (networkStatus.saveData || networkStatus.effectiveType === 'slow-2g') {
      return;
    }
    
    const generateTinyPreview = async () => {
      try {
        // Create a tiny version of the image (data URL)
        const img = new Image();
        img.src = src;
        await new Promise((resolve, reject) => {
          img.onload = resolve;
          img.onerror = reject;
        });
        
        // Create a canvas to resize the image
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        // Make a tiny 20px wide version
        const aspectRatio = img.naturalWidth / img.naturalHeight;
        canvas.width = 20;
        canvas.height = 20 / aspectRatio;
        
        // Draw the image and get a low quality data URL
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
        const tinyUrl = canvas.toDataURL('image/jpeg', 0.1);
        
        setTinyPreview(tinyUrl);
      } catch (err) {
        console.log('Failed to generate preview:', err);
      }
    };
    
    // Only generate preview for images that should be displayed
    if (inView || critical) {
      generateTinyPreview();
    }
  }, [src, inView, critical, blurUp, networkStatus.saveData, networkStatus.effectiveType, isError]);
  
  // Handle image load event
  const handleLoad = (e) => {
    setIsLoaded(true);
    if (onLoad) onLoad(e);
  };
  
  // Handle image load error
  const handleError = () => {
    setIsError(true);
    setIsLoaded(false);
  };
  
  // Create srcset for responsive images if width and height are provided
  const createSrcSet = () => {
    if (!src || !width || !height) return undefined;
    
    // Skip srcset on very slow connections to save data and reduce complexity
    if (networkStatus.saveData || networkStatus.effectiveType === 'slow-2g') {
      return undefined;
    }
    
    // Extract extension and base URL
    const lastDot = src.lastIndexOf('.');
    const baseSrc = lastDot === -1 ? src : src.substring(0, lastDot);
    const extension = lastDot === -1 ? '' : src.substring(lastDot);
    
    // Define widths for srcset (adjust based on original image width)
    const originalWidth = parseInt(width);
    
    // For smaller originals, don't create larger sizes
    if (originalWidth <= 640) {
      return `${src} ${originalWidth}w`;
    }
    
    // Create multiple sizes for larger images
    const widths = [
      Math.min(originalWidth, 640),
      Math.min(originalWidth, 768),
      Math.min(originalWidth, 1024),
      Math.min(originalWidth, 1280),
      originalWidth,
    ].filter((w, i, arr) => arr.indexOf(w) === i); // Remove duplicates
    
    // Generate srcset string with available sizes
    return widths
      .map(w => {
        // For images likely already optimized with width in filename (e.g. image-800w.jpg)
        if (src.includes(`-${w}w${extension}`)) {
          return `${src} ${w}w`;
        }
        // For images with no size info in filename
        return `${baseSrc}-${w}w${extension} ${w}w`;
      })
      .join(', ');
  };
  
  // Create sizes attribute for responsive images
  const sizes = width && height ? '(max-width: 640px) 100vw, (max-width: 768px) 75vw, 50vw' : undefined;
  
  // Determine image quality based on network speed
  const getImageSrc = () => {
    // If user has enabled data saver, or on slow connection, use lower quality
    if (isSlowNetwork || networkStatus.saveData) {
      // Check if image URL contains quality parameter
      if (src.includes('?')) {
        return `${src}&q=60`;
      }
      return `${src}?q=60`;
    }
    return src;
  };
  
  return (
    <div
      className={`relative overflow-hidden ${className}`}
      style={{
        width: width ? `${width}px` : '100%',
        height: height ? `${height}px` : 'auto',
        aspectRatio: width && height ? `${width} / ${height}` : undefined,
        ...style,
      }}
    >
      {/* Blurred tiny preview */}
      {blurUp && tinyPreview && !isLoaded && (
        <div
          className="absolute inset-0 bg-no-repeat bg-cover transition-opacity duration-300"
          style={{
            backgroundImage: `url('${tinyPreview}')`,
            filter: 'blur(10px)',
            transform: 'scale(1.1)', // Slightly larger to prevent blur edges
            opacity: isLoaded ? 0 : 1,
            zIndex: 1,
          }}
        />
      )}
      
      {/* Loading placeholder */}
      {!isLoaded && !tinyPreview && (
        <div
          className="absolute inset-0 bg-gray-800/40 flex items-center justify-center"
          style={{ zIndex: 1 }}
        >
          <div className="w-6 h-6 border-2 border-gray-300 border-t-transparent rounded-full animate-spin" />
        </div>
      )}
      
      {/* Actual image */}
      <img
        ref={setRefs}
        src={getImageSrc()}
        alt={alt || ''}
        className={`w-full h-full transition-opacity duration-500 ${
          isLoaded ? 'opacity-100' : 'opacity-0'
        } ${isError ? 'hidden' : ''}`}
        loading={loadingPriority}
        onLoad={handleLoad}
        onError={handleError}
        style={{ objectFit }}
        srcSet={createSrcSet()}
        sizes={sizes}
        {...props}
      />
      
      {/* Error fallback */}
      {isError && (
        <div className="absolute inset-0 bg-gray-800 flex items-center justify-center text-white text-sm">
          <svg
            className="w-6 h-6 mr-2 text-red-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
            />
          </svg>
          <span>Failed to load image</span>
        </div>
      )}
    </div>
  );
};

export default OptimizedImage; 