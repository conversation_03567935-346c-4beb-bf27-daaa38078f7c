import React, { useState, useEffect, useRef, useMemo } from 'react';
import { LOGO_PATH, LOGO_DIMENSIONS } from '../../lib/constants/assets';

/**
 * Logo component that displays the Smilo logo with enhanced animations
 * @param {Object} props
 * @param {'small' | 'default' | 'large'} [props.size='default'] - Size variant of the logo
 * @param {string} [props.className] - Additional CSS classes
 * @param {Object} [props.imgProps] - Additional props for the img element
 * @param {boolean} [props.showText=true] - Whether to show text next to logo
 * @param {boolean} [props.animated=false] - Whether to apply animations - now defaults to false
 */
export default function Logo({ 
  size = 'default',
  className = '',
  imgProps = {},
  showText = true,
  animated = false  // Change default to false so navigation logos are static
}) {
  const dimensions = LOGO_DIMENSIONS[size] || LOGO_DIMENSIONS.default;
  const [rotation, setRotation] = useState({ x: 0, y: 0 });
  const [hover, setHover] = useState(false);
  const animationRef = useRef(null);
  const isMountedRef = useRef(true);
  
  // Detect browser for Safari-specific optimizations
  const isSafari = useMemo(() => {
    if (typeof navigator !== 'undefined') {
      return /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
    }
    return false;
  }, []);
  
  // 3D rotation effect on mouse move - with proper cleanup
  useEffect(() => {
    if (!animated) return () => {};
    
    // Mark component as mounted
    isMountedRef.current = true;
    
    // Limit updates for performance
    let lastUpdateTime = 0;
    const THROTTLE_MS = 16; // ~60fps
    
    const handleMouseMove = (e) => {
      if (!hover || !isMountedRef.current) return;
      
      // Throttle updates
      const now = performance.now();
      if (now - lastUpdateTime < THROTTLE_MS) return;
      lastUpdateTime = now;
      
      // Use requestAnimationFrame for smoother performance
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      
      animationRef.current = requestAnimationFrame(() => {
        if (!isMountedRef.current) return;
        
        // Calculate rotation based on mouse position relative to window center
        // Limit rotation angle for Safari for better performance
        const maxAngle = isSafari ? 3 : 5;
        const x = Math.min(maxAngle, Math.max(-maxAngle, (e.clientY - window.innerHeight / 2) / 50));
        const y = Math.min(maxAngle, Math.max(-maxAngle, -(e.clientX - window.innerWidth / 2) / 50));
        
        setRotation({ x: x, y: y });
        animationRef.current = null;
      });
    };
    
    window.addEventListener('mousemove', handleMouseMove, { passive: true });
    
    // Proper cleanup
    return () => {
      isMountedRef.current = false;
      window.removeEventListener('mousemove', handleMouseMove);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
        animationRef.current = null;
      }
    };
  }, [animated, hover, isSafari]);
  
  // Generate random particles more efficiently with useMemo
  const particles = useMemo(() => {
    // Skip particles in Safari to improve performance
    if (isSafari) return [];
    
    if (animated) {
      // Fewer particles for better performance
      const particleCount = 4;
      return Array.from({ length: particleCount }, (_, i) => ({
        id: i,
        size: 1 + Math.random() * 2, // Smaller size range
        top: Math.random() * 100,
        left: Math.random() * 100,
        duration: 6 + Math.random() * 6, // Shorter animation duration
        delay: Math.random() * 2
      }));
    }
    return [];
  }, [animated, isSafari]);
  
  // Larger dimensions for the actual container to accommodate the glow
  const containerDimensions = {
    width: dimensions.width * 1.2,
    height: dimensions.height * 1.2
  };

  return (
    <div className={`inline-flex items-center gap-3 ${className}`}>
      {/* Dynamic logo container with 3D effect */}
      <div 
        className={`
          relative 
          ${animated ? 'transform-gpu transition-all duration-300' : ''}
        `}
        style={{
          width: containerDimensions.width,
          height: containerDimensions.height,
          transform: animated ? `perspective(800px) rotateX(${rotation.x}deg) rotateY(${rotation.y}deg)` : undefined,
          transformStyle: animated ? 'preserve-3d' : undefined,
          backfaceVisibility: animated ? 'hidden' : undefined // Safari optimization
        }}
        onMouseEnter={() => animated && setHover(true)}
        onMouseLeave={() => {
          if (animated) {
            setHover(false);
            // Smoothly reset rotation
            setRotation({ x: 0, y: 0 });
          }
        }}
      >
        {/* Multi-layered background glow effects - only if animated */}
        {animated && (
          <>
            {/* Use standard CSS for outer glow instead of complex gradients */}
            <div 
              className="absolute inset-0 rounded-full opacity-40"
              style={{
                background: 'radial-gradient(circle, rgba(59,130,246,0.6) 0%, rgba(147,51,234,0.3) 100%)',
                filter: 'blur(15px)',
                transform: 'translateZ(-10px)',
                willChange: hover ? 'transform' : 'auto'
              }} 
            />
            
            {/* Inner glow - simplified for performance */}
            <div 
              className="absolute inset-1 rounded-full opacity-60"
              style={{
                background: 'radial-gradient(circle, rgba(59,130,246,0.7) 0%, rgba(147,51,234,0.3) 100%)',
                filter: 'blur(10px)',
                transform: 'translateZ(-5px)',
                willChange: hover ? 'transform' : 'auto'
              }} 
            />
            
            {/* Floating particles - only show if not Safari and only a few for performance */}
            {particles.map(particle => (
              <div
                key={particle.id}
                className="absolute bg-blue-400/60 rounded-full"
                style={{
                  width: `${particle.size}px`,
                  height: `${particle.size}px`,
                  top: `${particle.top}%`,
                  left: `${particle.left}%`,
                  animation: `float ${particle.duration}s ease-in-out ${particle.delay}s infinite`,
                  transform: `translateZ(${particle.id}px)`,
                  filter: 'blur(1px)',
                  willChange: 'transform' // Optimize animation
                }}
              />
            ))}
          </>
        )}
        
        {/* Logo image with subtle floating animation */}
        <div 
          className={`
            absolute inset-0 flex items-center justify-center
            ${animated && !isSafari ? 'animate-float' : ''}
          `}
          style={{ 
            animationDuration: animated ? '6s' : undefined, 
            transform: animated ? 'translateZ(10px)' : undefined,
            willChange: animated ? 'transform' : 'auto'
          }}
        >
          <img
            src={LOGO_PATH}
            alt="Smilo Logo"
            width={dimensions.width}
            height={dimensions.height}
            className={`
              object-contain
              rounded-full
              ${animated ? 'transition-transform duration-300' : ''}
              ${hover && animated ? 'scale-105' : 'scale-100'}
              ${animated ? 'shadow-lg shadow-blue-500/30' : ''}
            `}
            style={{ 
              transform: animated ? 'translateZ(15px)' : undefined,
              // Safari-friendly shadow
              WebkitFilter: animated ? 'drop-shadow(0 10px 8px rgba(59, 130, 246, 0.3))' : undefined
            }}
            {...imgProps}
          />
        </div>
      </div>
      
      {/* Text with shimmer effect */}
      {showText && (
        <div 
          className={`
            flex flex-col
            ${animated && !isSafari ? 'animate-float' : ''}
          `}
          style={{ animationDuration: '5s', animationDelay: '0.2s' }}
        >
          <span 
            className={`
              ${size === 'small' ? 'text-base' : 'text-xl'} 
              font-semibold tracking-wide leading-none
              ${animated ? 'bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-indigo-300 to-purple-400' : 'text-white'}
            `}
          >
            SMILO
          </span>
          <span 
            className={`
              ${size === 'small' ? 'text-xs' : 'text-sm'} 
              ${animated ? 'text-blue-200/90' : 'text-white/80'} 
              font-light tracking-wide
            `}
          >
            DentAI Assistant
          </span>
        </div>
      )}
    </div>
  );
}