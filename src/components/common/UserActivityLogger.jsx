import React, { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { useUser } from '../../contexts/UserContext';
import { logPageVisit } from '../../lib/services/userHistoryService';
import { supabase } from '../../lib/supabase';

/**
 * Component that logs page visits for logged-in users
 * This should be mounted high in the component tree, such as in the App.jsx file
 */
export default function UserActivityLogger() {
  const location = useLocation();
  const { user } = useUser();
  const [tablesExist, setTablesExist] = useState(true);

  // Check if tables exist on first render
  useEffect(() => {
    const checkTables = async () => {
      try {
        // Try to query the table to see if it exists
        const { error } = await supabase
          .from('user_page_history')
          .select('id')
          .limit(1);

        // If there's a 404 error, the table doesn't exist
        if (error && (error.code === '404' || error.code === 'PGRST301')) {
          console.info('User history tables not yet created - logging disabled');
          setTablesExist(false);
        }
      } catch (e) {
        console.info('Error checking for history tables:', e);
        setTablesExist(false);
      }
    };
    
    checkTables();
  }, []);

  useEffect(() => {
    // Skip if tables don't exist or user isn't logged in
    if (!tablesExist || !user?.id) return;
    
    const recordPageView = async () => {
      try {
        // Get page title
        const pageTitle = document.title || 'Unknown Page';
        
        // Log the page visit
        await logPageVisit(
          location.pathname,
          pageTitle,
          user.id
        );
      } catch (error) {
        // Just log the error and continue - don't disrupt the user experience
        console.error('Error logging page visit:', error);
      }
    };

    // Small delay to ensure document.title is updated
    const timeoutId = setTimeout(recordPageView, 300);
    
    return () => clearTimeout(timeoutId);
  }, [location.pathname, user?.id, tablesExist]);

  // This component doesn't render anything visible
  return null;
} 