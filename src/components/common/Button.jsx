import React, { useRef, useEffect } from 'react';
import { buttonPress, pulseOnHover } from '../../lib/utils/animations';

export default function Button({ children, variant = 'primary', className = '', ...props }) {
  const buttonRef = useRef(null);

  useEffect(() => {
    const button = buttonRef.current;
    if (!button) return;

    const handleMouseMove = (e) => {
      const { left, top, width, height } = button.getBoundingClientRect();
      const x = (e.clientX - left) / width - 0.5;
      const y = (e.clientY - top) / height - 0.5;
      
      const multiplier = 20;
      button.style.transform = `
        perspective(1000px)
        rotateY(${x * multiplier}deg)
        rotateX(${-y * multiplier}deg)
        translateZ(10px)
      `;
    };

    const handleMouseLeave = () => {
      button.style.transform = 'none';
    };

    button.addEventListener('mousemove', handleMouseMove);
    button.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      button.removeEventListener('mousemove', handleMouseMove);
      button.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, []);

  const baseClasses = `
    px-6 
    py-3 
    rounded-full
    font-medium 
    shadow-lg
    transform
    transition-all
    duration-500
    ${buttonPress} 
    ${pulseOnHover}
  `;
  
  const variants = {
    primary: `
      bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500 
      hover:from-blue-600 hover:via-indigo-600 hover:to-purple-600
      text-white shadow-lg shadow-indigo-500/20
      focus:ring-2 focus:ring-indigo-500/50
      active:shadow-inner active:from-blue-700 active:via-indigo-700 active:to-purple-700
    `,
    secondary: `
      bg-white/10 backdrop-blur-sm 
      hover:bg-white/15 text-white 
      border border-white/10
      shadow-sm shadow-black/5
      focus:ring-2 focus:ring-indigo-500/30
    `,
    outline: `
      bg-transparent border border-indigo-400 text-indigo-400
      hover:bg-indigo-400/10 hover:border-indigo-300 hover:text-indigo-300
      focus:ring-2 focus:ring-indigo-500/30
    `,
    ghost: `
      bg-transparent hover:bg-white/10 text-white
      focus:ring-2 focus:ring-indigo-500/20
    `,
    link: `
      bg-transparent p-0 h-auto text-indigo-400 hover:text-indigo-300 hover:underline
      focus:ring-0
    `,
    danger: `
      bg-gradient-to-r from-red-500 to-pink-500 
      hover:from-red-600 hover:to-pink-600
      text-white shadow-lg shadow-red-500/20
      focus:ring-2 focus:ring-red-500/50
    `
  };

  return (
    <button
      ref={buttonRef}
      className={`${baseClasses} ${variants[variant]} ${className}`}
      {...props}
    >
      {children}
    </button>
  );
}