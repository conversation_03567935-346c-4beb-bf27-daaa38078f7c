import React from 'react';
import { motion } from 'framer-motion';

const KofiButton = () => {
  return (
    <motion.div
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      className="inline-block"
    >
      <a
        href="https://ko-fi.com/T6T31CZKPK"
        target="_blank"
        rel="noopener noreferrer"
        className="inline-flex items-center px-3 py-1.5 bg-[#72a4f2] text-white rounded-md text-sm font-medium hover:bg-[#5a93e8] transition-colors duration-200"
      >
        {/* Ko-fi cup icon */}
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="mr-1.5"
        >
          <path d="M18 8h1a4 4 0 0 1 0 8h-1"></path>
          <path d="M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8z"></path>
          <line x1="6" y1="1" x2="6" y2="4"></line>
          <line x1="10" y1="1" x2="10" y2="4"></line>
          <line x1="14" y1="1" x2="14" y2="4"></line>
        </svg>
        Support Me
      </a>
    </motion.div>
  );
};

export default KofiButton;
