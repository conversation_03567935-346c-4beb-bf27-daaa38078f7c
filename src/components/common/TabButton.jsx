import React from 'react';
import { motion } from 'framer-motion';

const TabButton = ({ active, onClick, children }) => {
  return (
    <motion.button
      className={`px-4 py-2 rounded-md font-medium text-sm transition-all duration-200 relative ${
        active 
          ? 'text-white'
          : 'text-white/60 hover:text-white/80'
      }`}
      onClick={onClick}
      whileTap={{ scale: 0.97 }}
    >
      {children}
      {active && (
        <motion.div
          className="absolute bottom-0 left-0 right-0 h-0.5 bg-indigo-500 rounded-full"
          layoutId="activeTab"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        />
      )}
    </motion.button>
  );
};

export default TabButton; 