import React from 'react';
import Button from './Button';

/**
 * Modal component for confirming logout
 * 
 * @param {Object} props - Component props
 * @param {boolean} props.isOpen - Whether the modal is open
 * @param {Function} props.onClose - Function to close the modal
 * @param {Function} props.onConfirm - Function to confirm logout
 */
export default function LogoutConfirmationModal({ isOpen, onClose, onConfirm }) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-70 transition-opacity"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className="bg-gray-900 border border-gray-700 rounded-lg shadow-xl max-w-md w-full mx-4 z-10 transform transition-all">
        <div className="p-6">
          <div className="flex items-center justify-center mb-4">
            <div className="h-14 w-14 rounded-full bg-red-500/10 flex items-center justify-center">
              <svg className="h-7 w-7 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  strokeWidth={2} 
                  d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" 
                />
              </svg>
            </div>
          </div>
          
          <h3 className="text-xl font-bold text-white text-center mb-2">
            Sign Out Confirmation
          </h3>
          
          <p className="text-gray-300 text-center mb-6">
            Are you sure you want to sign out? Your session will end and you'll need to sign in again to access your account.
          </p>
          
          <div className="flex flex-col sm:flex-row sm:justify-center gap-3">
            <Button
              variant="secondary"
              onClick={onClose}
              className="flex-1"
            >
              Cancel
            </Button>
            
            <Button
              variant="primary"
              onClick={() => {
                onConfirm();
                onClose();
              }}
              className="flex-1 bg-red-600 hover:bg-red-700"
            >
              Sign Out
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
} 