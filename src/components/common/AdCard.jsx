import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';

/**
 * AdCard component for displaying affiliate products
 * 
 * @param {Object} props
 * @param {string} props.image - Image URL of the product
 * @param {string} props.title - Title of the product
 * @param {string} props.description - Short description of the product
 * @param {string} props.link - Affiliate link to product
 * @param {string} props.price - Price of the product
 * @param {string} props.tag - Optional tag like "Best Value" or "Top Pick"
 * @param {string} props.size - Size of card: 'small', 'medium', or 'large'
 */
const AdCard = ({ 
  image, 
  title, 
  description, 
  link, 
  price, 
  tag = null, 
  size = 'medium' 
}) => {
  // Determine classes based on size
  const sizeClasses = {
    small: 'max-w-xs',
    medium: 'max-w-sm',
    large: 'max-w-md',
  };
  
  const cardClass = sizeClasses[size] || sizeClasses.medium;
  
  return (
    <motion.div
      className={`${cardClass} bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 overflow-hidden hover:border-blue-400/30 transition-all duration-300 group`}
      whileHover={{ y: -5 }}
      transition={{ type: "spring", stiffness: 300, damping: 10 }}
    >
      <a href={link} target="_blank" rel="noopener noreferrer" className="block">
        {/* Product Image */}
        <div className="relative overflow-hidden">
          <img 
            src={image} 
            alt={title} 
            className="w-full h-48 object-cover transform group-hover:scale-105 transition-transform duration-300"
          />
          
          {/* Optional tag */}
          {tag && (
            <div className="absolute top-3 right-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white text-xs font-bold px-2 py-1 rounded-full">
              {tag}
            </div>
          )}
        </div>
        
        {/* Content */}
        <div className="p-4">
          <h3 className="text-lg font-medium text-white mb-1 line-clamp-2">{title}</h3>
          <p className="text-white/60 text-sm mb-3 line-clamp-2">{description}</p>
          
          <div className="flex items-center justify-between">
            <span className="text-blue-400 font-semibold">{price}</span>
            <span className="text-white/70 text-sm bg-white/10 px-3 py-1 rounded-full">View Details</span>
          </div>
        </div>
      </a>
    </motion.div>
  );
};

/**
 * AdCarousel component for displaying multiple affiliate products in a row
 * 
 * @param {Object} props
 * @param {Array} props.products - Array of product objects
 * @param {string} props.title - Optional title for the carousel
 */
export const AdCarousel = ({ products, title = null }) => {
  return (
    <div className="w-full my-8 py-4">
      {title && (
        <div className="flex items-center mb-4">
          <span className="h-px flex-1 bg-gradient-to-r from-transparent via-indigo-500/30 to-transparent"></span>
          <h3 className="text-white text-lg font-medium px-4">{title}</h3>
          <span className="h-px flex-1 bg-gradient-to-r from-transparent via-indigo-500/30 to-transparent"></span>
        </div>
      )}
      
      <div className="flex overflow-x-auto pb-4 -mx-4 px-4 hide-scrollbar">
        <div className="flex space-x-4">
          {products.map((product, index) => (
            <div key={index} className="flex-shrink-0 w-64">
              <AdCard {...product} size="small" />
            </div>
          ))}
        </div>
      </div>
      
      {/* Add some custom scroll styling */}
      <style jsx>{`
        .hide-scrollbar::-webkit-scrollbar {
          display: none;
        }
        .hide-scrollbar {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
      `}</style>
    </div>
  );
};

/**
 * Recommended products component that blends into content
 * 
 * @param {Object} props
 * @param {Array} props.products - Array of product objects (limit 1-3)
 * @param {string} props.context - Context to use for recommendation text
 */
export const RecommendedProducts = ({ products, context = "dental care" }) => {
  if (!products?.length) return null;
  
  return (
    <div className="my-8 bg-gradient-to-r from-blue-900/20 to-indigo-900/20 rounded-lg border border-blue-800/20 p-5">
      <div className="mb-4">
        <h3 className="text-white text-lg font-medium mb-2">Recommended for {context}</h3>
        <p className="text-white/60 text-sm">Products we've researched and recommend</p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {products.slice(0, 3).map((product, index) => (
          <AdCard key={index} {...product} size="small" />
        ))}
      </div>
    </div>
  );
};

export default AdCard; 