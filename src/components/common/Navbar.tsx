import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import NewSignInButton from '../auth/NewSignInButton';

interface NavbarProps {
  transparent?: boolean;
}

const Navbar: React.FC<NavbarProps> = ({ transparent = false }) => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Handle scroll event
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Determine background color based on scroll position and transparency prop
  const getNavbarClasses = () => {
    if (transparent && !isScrolled) {
      return 'bg-transparent text-white';
    }
    return 'bg-white shadow-md text-gray-800';
  };

  return (
    <nav className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${getNavbarClasses()}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* Logo and nav links */}
          <div className="flex">
            <div className="flex-shrink-0 flex items-center">
              <Link to="/" className="font-bold text-xl">
                Smilo<span className="text-blue-600">.Dental</span>
              </Link>
            </div>

            {/* Desktop navigation */}
            <div className="hidden md:ml-6 md:flex md:space-x-4 md:items-center">
              <Link to="/services" className="px-3 py-2 rounded-md text-sm font-medium hover:text-blue-600 transition-colors">
                Services
              </Link>
              <Link to="/for-patients" className="px-3 py-2 rounded-md text-sm font-medium hover:text-blue-600 transition-colors">
                For Patients
              </Link>
              <Link to="/for-dentists" className="px-3 py-2 rounded-md text-sm font-medium hover:text-blue-600 transition-colors">
                For Dentists
              </Link>
              <Link to="/about" className="px-3 py-2 rounded-md text-sm font-medium hover:text-blue-600 transition-colors">
                About Us
              </Link>
              <Link to="/contact" className="px-3 py-2 rounded-md text-sm font-medium hover:text-blue-600 transition-colors">
                Contact
              </Link>
            </div>
          </div>

          {/* Right side actions */}
          <div className="flex items-center">
            <div className="hidden md:flex items-center space-x-3">
              {/* Use our new SignInButton component */}
              <NewSignInButton
                userType="patient"
                className={transparent && !isScrolled ? 'bg-white/20 hover:bg-white/30 text-white rounded-full px-6 py-2' : 'bg-blue-600 hover:bg-blue-700 text-white rounded-full px-6 py-2'}
              />
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden flex items-center">
              <button
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className={`p-2 rounded-md ${transparent && !isScrolled ? 'text-white hover:bg-white hover:bg-opacity-10' : 'text-gray-700 hover:bg-gray-100'}`}
              >
                <span className="sr-only">Open main menu</span>
                {isMobileMenuOpen ? (
                  <svg
                    className="h-6 w-6"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    aria-hidden="true"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                ) : (
                  <svg
                    className="h-6 w-6"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    aria-hidden="true"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                  </svg>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile menu, show/hide based on menu state */}
      <motion.div
        className={`md:hidden ${isScrolled || !transparent ? 'bg-white' : 'bg-gray-800 bg-opacity-90 backdrop-filter backdrop-blur-sm'}`}
        initial={{ height: 0, opacity: 0 }}
        animate={{
          height: isMobileMenuOpen ? 'auto' : 0,
          opacity: isMobileMenuOpen ? 1 : 0
        }}
        transition={{ duration: 0.3 }}
      >
        {isMobileMenuOpen && (
          <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
            <Link
              to="/services"
              className={`block px-3 py-2 rounded-md text-base font-medium ${isScrolled || !transparent ? 'text-gray-700 hover:bg-gray-100' : 'text-white hover:bg-white hover:bg-opacity-10'}`}
            >
              Services
            </Link>
            <Link
              to="/for-patients"
              className={`block px-3 py-2 rounded-md text-base font-medium ${isScrolled || !transparent ? 'text-gray-700 hover:bg-gray-100' : 'text-white hover:bg-white hover:bg-opacity-10'}`}
            >
              For Patients
            </Link>
            <Link
              to="/for-dentists"
              className={`block px-3 py-2 rounded-md text-base font-medium ${isScrolled || !transparent ? 'text-gray-700 hover:bg-gray-100' : 'text-white hover:bg-white hover:bg-opacity-10'}`}
            >
              For Dentists
            </Link>
            <Link
              to="/about"
              className={`block px-3 py-2 rounded-md text-base font-medium ${isScrolled || !transparent ? 'text-gray-700 hover:bg-gray-100' : 'text-white hover:bg-white hover:bg-opacity-10'}`}
            >
              About Us
            </Link>
            <Link
              to="/contact"
              className={`block px-3 py-2 rounded-md text-base font-medium ${isScrolled || !transparent ? 'text-gray-700 hover:bg-gray-100' : 'text-white hover:bg-white hover:bg-opacity-10'}`}
            >
              Contact
            </Link>
            <div className="mt-4 px-3">
              <NewSignInButton
                userType="patient"
                className={`w-full justify-center rounded-full px-6 py-2 ${transparent && !isScrolled ? 'bg-white/20 hover:bg-white/30 text-white' : 'bg-blue-600 hover:bg-blue-700 text-white'}`}
              />
            </div>
          </div>
        )}
      </motion.div>
    </nav>
  );
};

export default Navbar;