import React from 'react';

/**
 * A responsive container component that applies different styles based on screen size
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @param {string} props.className - Additional CSS classes
 * @param {string} props.mobileClassName - Classes to apply only on mobile screens
 * @param {string} props.tabletClassName - Classes to apply only on tablet screens
 * @param {string} props.desktopClassName - Classes to apply only on desktop screens
 * @param {string} props.as - HTML element to render (default: 'div')
 * @returns {JSX.Element} Responsive container
 */
export default function ResponsiveContainer({
  children,
  className = '',
  mobileClassName = '',
  tabletClassName = '',
  desktopClassName = '',
  as: Component = 'div',
  ...props
}) {
  return (
    <Component
      className={`
        ${className}
        ${mobileClassName} 
        md:${tabletClassName} 
        lg:${desktopClassName}
      `}
      {...props}
    >
      {children}
    </Component>
  );
}
