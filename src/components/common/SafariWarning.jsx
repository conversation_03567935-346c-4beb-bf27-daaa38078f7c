import React, { useEffect, useState } from 'react';
import { isSafari } from '../../utils/safariOptimizer';

/**
 * Shows a warning to Safari users about compatibility issues
 * Provides recommendations to improve performance
 * @param {Object} props Component props
 * @param {Function} props.onDismiss Optional callback when warning is dismissed
 */
const SafariWarning = ({ onDismiss }) => {
  const [showWarning, setShowWarning] = useState(false);
  const [safariVersion, setSafariVersion] = useState(null);
  const [isOldVersion, setIsOldVersion] = useState(false);
  const [isIOS, setIsIOS] = useState(false);
  const [dismissed, setDismissed] = useState(false);
  
  useEffect(() => {
    // Only check Safari
    if (!isSafari()) {
      return;
    }
    
    // Check if user has already dismissed the warning
    const hasDismissed = localStorage.getItem('safari_warning_dismissed');
    if (hasDismissed) {
      setDismissed(true);
      return;
    }
    
    // Get Safari version
    const ua = navigator.userAgent;
    const match = ua.match(/Version\/(\d+)\.(\d+)/);
    
    if (match) {
      const majorVersion = parseInt(match[1], 10);
      const minorVersion = parseInt(match[2], 10);
      setSafariVersion(`${majorVersion}.${minorVersion}`);
      
      // Check if it's an old version (< 15)
      if (majorVersion < 15) {
        setIsOldVersion(true);
      }
    }
    
    // Check if it's iOS Safari
    const isIOSSafari = /iPad|iPhone|iPod/.test(ua) && !window.MSStream;
    setIsIOS(isIOSSafari);
    
    // Show warning for all Safari users
    setShowWarning(true);
  }, []);
  
  const handleDismiss = () => {
    localStorage.setItem('safari_warning_dismissed', 'true');
    setDismissed(true);
    
    // Call onDismiss callback if provided
    if (typeof onDismiss === 'function') {
      onDismiss();
    }
  };
  
  const switchToSimplifiedMode = () => {
    // Redirect to simplified mode
    window.location.href = window.location.href.split('?')[0] + '?simplified=true';
  };
  
  // Don't render anything if not Safari or already dismissed
  if (!showWarning || dismissed) {
    return null;
  }
  
  return (
    <div className="fixed bottom-0 left-0 right-0 bg-indigo-900 bg-opacity-95 text-white p-4 z-50 safe-area-pb">
      <div className="max-w-4xl mx-auto">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h3 className="text-lg font-bold mb-1">Safari Compatibility Notice</h3>
            
            {isOldVersion ? (
              <p className="mb-2">
                You're using Safari {safariVersion}, which may have issues with this site.
                For the best experience, please update to Safari 15+ or try Chrome/Firefox.
              </p>
            ) : (
              <p className="mb-2">
                We've detected you're using Safari, which may have some performance issues with this site.
              </p>
            )}
            
            <div className="flex flex-wrap gap-2 mt-3">
              <button
                onClick={switchToSimplifiedMode}
                className="bg-blue-600 hover:bg-blue-700 text-white py-1 px-3 rounded text-sm"
              >
                Use Simplified Mode
              </button>
              
              <button
                onClick={handleDismiss}
                className="bg-gray-600 hover:bg-gray-700 text-white py-1 px-3 rounded text-sm"
              >
                Dismiss
              </button>
              
              {isIOS && (
                <a
                  href="https://apps.apple.com/us/app/google-chrome/id535886823"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-green-600 hover:bg-green-700 text-white py-1 px-3 rounded text-sm"
                >
                  Get Chrome for iOS
                </a>
              )}
            </div>
          </div>
          
          <button
            onClick={handleDismiss}
            className="ml-4 text-white opacity-70 hover:opacity-100"
            aria-label="Close"
          >
            &times;
          </button>
        </div>
      </div>
    </div>
  );
};

export default SafariWarning; 