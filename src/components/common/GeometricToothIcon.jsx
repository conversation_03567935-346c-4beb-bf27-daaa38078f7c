import React, { useEffect, useRef } from 'react';

export default function GeometricToothIcon({ className = "" }) {
  const particleContainerRef = useRef(null);
  
  // Set up particles animation
  useEffect(() => {
    if (!particleContainerRef.current) return;
    
    // Clean up any existing particles
    const container = particleContainerRef.current;
    container.innerHTML = '';
    
    // Create particles
    for (let i = 0; i < 15; i++) {
      const particle = document.createElement('div');
      
      // Random properties
      const size = 1 + Math.random() * 2;
      const posX = Math.random() * 100;
      const posY = Math.random() * 100;
      const duration = 3 + Math.random() * 4;
      const delay = Math.random() * 2;
      
      // Apply styles
      particle.className = 'absolute rounded-full bg-blue-400';
      particle.style.width = `${size}px`;
      particle.style.height = `${size}px`;
      particle.style.left = `${posX}%`;
      particle.style.top = `${posY}%`;
      particle.style.opacity = '0';
      particle.style.filter = 'blur(1px)';
      particle.style.animation = `
        floating ${duration}s ease-in-out ${delay}s infinite alternate,
        glowing 2s ease-in-out ${delay}s infinite alternate
      `;
      
      container.appendChild(particle);
    }
    
    // Clean up function
    return () => {
      if (container) {
        container.innerHTML = '';
      }
    };
  }, []);

  return (
    <div className={`relative ${className} group`}>
      {/* Dramatic outer glow effect with multiple layers */}
      <div className="absolute inset-0 -m-4 bg-gradient-to-r from-blue-500/30 to-cyan-400/30 rounded-full blur-3xl animate-pulse-slow" />
      <div className="absolute inset-0 -m-2 bg-gradient-to-r from-blue-400/30 to-indigo-400/30 rounded-full blur-2xl animate-pulse-slow [animation-delay:400ms]" />
      <div className="absolute inset-0 bg-gradient-to-br from-blue-500/40 to-cyan-400/40 rounded-full blur-xl animate-pulse-slow [animation-delay:200ms]" />
      
      {/* Particles container */}
      <div 
        ref={particleContainerRef} 
        className="absolute inset-0 -m-8 overflow-hidden rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-1000"
      ></div>

      {/* Main SVG */}
      <svg 
        viewBox="0 0 100 100" 
        className="relative w-full h-full transition-transform duration-700 group-hover:scale-105"
        style={{
          filter: "drop-shadow(0 0 8px rgba(59, 130, 246, 0.5))"
        }}
      >
        {/* Enhanced gradient definitions */}
        <defs>
          {/* Metallic base gradient - enhanced with more stops */}
          <linearGradient id="metallicBase" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#FFFFFF" stopOpacity="0.95">
              <animate attributeName="stopOpacity" values="0.95;0.85;0.95" dur="3s" repeatCount="indefinite" />
            </stop>
            <stop offset="25%" stopColor="#F8FAFC" stopOpacity="0.92">
              <animate attributeName="stopOpacity" values="0.92;0.82;0.92" dur="3s" repeatCount="indefinite" />
            </stop>
            <stop offset="50%" stopColor="#E2E8F0" stopOpacity="0.9">
              <animate attributeName="stopOpacity" values="0.9;0.8;0.9" dur="3s" repeatCount="indefinite" />
            </stop>
            <stop offset="75%" stopColor="#CBD5E1" stopOpacity="0.87">
              <animate attributeName="stopOpacity" values="0.87;0.77;0.87" dur="3s" repeatCount="indefinite" />
            </stop>
            <stop offset="100%" stopColor="#94A3B8" stopOpacity="0.85">
              <animate attributeName="stopOpacity" values="0.85;0.75;0.85" dur="3s" repeatCount="indefinite" />
            </stop>
          </linearGradient>

          {/* Enhanced metallic highlight gradient */}
          <linearGradient id="metallicHighlight" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#FFFFFF" stopOpacity="0.8">
              <animate attributeName="offset" values="0;0.2;0" dur="3s" repeatCount="indefinite" />
              <animate attributeName="stopOpacity" values="0.8;0.6;0.8" dur="4s" repeatCount="indefinite" />
            </stop>
            <stop offset="50%" stopColor="#FFFFFF" stopOpacity="0.5">
              <animate attributeName="offset" values="0.5;0.7;0.5" dur="3s" repeatCount="indefinite" />
              <animate attributeName="stopOpacity" values="0.5;0.3;0.5" dur="4s" repeatCount="indefinite" />
            </stop>
            <stop offset="100%" stopColor="#FFFFFF" stopOpacity="0.8">
              <animate attributeName="offset" values="1;1.2;1" dur="3s" repeatCount="indefinite" />
              <animate attributeName="stopOpacity" values="0.8;0.6;0.8" dur="4s" repeatCount="indefinite" />
            </stop>
          </linearGradient>

          {/* Enhanced animated circuit gradient with more vibrant colors */}
          <linearGradient id="circuitGlow" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#60A5FA" stopOpacity="0.9">
              <animate attributeName="stopOpacity" values="0.9;0.5;0.9" dur="2s" repeatCount="indefinite" />
              <animate attributeName="stopColor" values="#60A5FA;#93C5FD;#60A5FA" dur="4s" repeatCount="indefinite" />
            </stop>
            <stop offset="50%" stopColor="#3B82F6" stopOpacity="1">
              <animate attributeName="stopOpacity" values="1;0.7;1" dur="2s" repeatCount="indefinite" />
              <animate attributeName="stopColor" values="#3B82F6;#2563EB;#3B82F6" dur="4s" repeatCount="indefinite" />
            </stop>
            <stop offset="100%" stopColor="#60A5FA" stopOpacity="0.9">
              <animate attributeName="stopOpacity" values="0.9;0.5;0.9" dur="2s" repeatCount="indefinite" />
              <animate attributeName="stopColor" values="#60A5FA;#93C5FD;#60A5FA" dur="4s" repeatCount="indefinite" />
            </stop>
          </linearGradient>

          {/* Enhanced metallic filter with better lighting */}
          <filter id="metallicFilter">
            <feGaussianBlur in="SourceAlpha" stdDeviation="2" result="blur" />
            <feSpecularLighting in="blur" surfaceScale="5" specularConstant=".75" specularExponent="20" result="specOut">
              <fePointLight x="50" y="50" z="200">
                <animate attributeName="z" values="200;180;200" dur="4s" repeatCount="indefinite" />
              </fePointLight>
            </feSpecularLighting>
            <feComposite in="specOut" in2="SourceAlpha" operator="in" result="specOut2" />
            <feComposite in="SourceGraphic" in2="specOut2" operator="arithmetic" k1="0" k2="1" k3="1" k4="0" />
          </filter>
          
          {/* Shimmering effect filter */}
          <filter id="shimmer">
            <feTurbulence baseFrequency="0.1" numOctaves="2" seed="1" result="turbulence">
              <animate attributeName="seed" values="1;10;1" dur="8s" repeatCount="indefinite" />
            </feTurbulence>
            <feDisplacementMap in="SourceGraphic" in2="turbulence" scale="2" />
          </filter>
        </defs>

        {/* Metallic tooth shape with improved animation */}
        <path
          d="M50 20
             C35 20 25 30 25 45
             C25 60 35 75 40 80
             C45 85 48 85 50 85
             C52 85 55 85 60 80
             C65 75 75 60 75 45
             C75 30 65 20 50 20Z"
          fill="url(#metallicBase)"
          filter="url(#metallicFilter)"
          className="transform-origin-center"
        >
          <animate
            attributeName="d"
            values="
              M50 20 C35 20 25 30 25 45 C25 60 35 75 40 80 C45 85 48 85 50 85 C52 85 55 85 60 80 C65 75 75 60 75 45 C75 30 65 20 50 20Z;
              M50 22 C37 22 27 32 27 47 C27 62 37 77 42 82 C47 87 49 87 51 87 C53 87 56 87 61 82 C66 77 76 62 76 47 C76 32 66 22 50 22Z;
              M50 20 C35 20 25 30 25 45 C25 60 35 75 40 80 C45 85 48 85 50 85 C52 85 55 85 60 80 C65 75 75 60 75 45 C75 30 65 20 50 20Z"
            dur="4s"
            repeatCount="indefinite"
          />
          
          {/* Subtle 3D effect */}
          <animateTransform
            attributeName="transform"
            type="rotate"
            from="-2 50 50"
            to="2 50 50"
            dur="6s"
            repeatCount="indefinite"
            additive="sum"
          />
        </path>

        {/* Enhanced metallic highlight overlay with shimmer */}
        <path
          d="M50 20
             C35 20 25 30 25 45
             C25 60 35 75 40 80
             C45 85 48 85 50 85
             C52 85 55 85 60 80
             C65 75 75 60 75 45
             C75 30 65 20 50 20Z"
          fill="url(#metallicHighlight)"
          opacity="0.5"
          filter="url(#shimmer)"
        >
          <animateTransform
            attributeName="transform"
            type="rotate"
            from="0 50 50"
            to="360 50 50"
            dur="8s"
            repeatCount="indefinite"
          />
        </path>

        {/* Enhanced circuit patterns with animation */}
        <g className="stroke-blue-400" strokeWidth="0.75" fill="none">
          {/* Animated horizontal lines with improved dash animation */}
          <path d="M35 40 H65">
            <animate
              attributeName="stroke-dasharray"
              values="0,30;30,0"
              dur="2s"
              repeatCount="indefinite"
            />
            <animate 
              attributeName="stroke"
              values="url(#circuitGlow);rgba(96, 165, 250, 0.2);url(#circuitGlow)"
              dur="3s"
              repeatCount="indefinite"
            />
          </path>
          
          <path d="M35 50 H65">
            <animate
              attributeName="stroke-dasharray"
              values="0,30;30,0"
              dur="2s"
              begin="0.4s"
              repeatCount="indefinite"
            />
            <animate 
              attributeName="stroke"
              values="url(#circuitGlow);rgba(96, 165, 250, 0.2);url(#circuitGlow)"
              dur="3s"
              begin="0.4s"
              repeatCount="indefinite"
            />
          </path>
          
          <path d="M35 60 H65">
            <animate
              attributeName="stroke-dasharray"
              values="0,30;30,0"
              dur="2s"
              begin="0.8s"
              repeatCount="indefinite"
            />
            <animate 
              attributeName="stroke"
              values="url(#circuitGlow);rgba(96, 165, 250, 0.2);url(#circuitGlow)"
              dur="3s"
              begin="0.8s"
              repeatCount="indefinite"
            />
          </path>
          
          {/* Animated vertical lines with improved dash animation */}
          <path d="M40 35 V65">
            <animate
              attributeName="stroke-dasharray"
              values="0,30;30,0"
              dur="2s"
              begin="1.2s"
              repeatCount="indefinite"
            />
            <animate 
              attributeName="stroke"
              values="url(#circuitGlow);rgba(96, 165, 250, 0.2);url(#circuitGlow)"
              dur="3s"
              begin="1.2s"
              repeatCount="indefinite"
            />
          </path>
          
          <path d="M50 35 V65">
            <animate
              attributeName="stroke-dasharray"
              values="0,30;30,0"
              dur="2s"
              begin="1.6s"
              repeatCount="indefinite"
            />
            <animate 
              attributeName="stroke"
              values="url(#circuitGlow);rgba(96, 165, 250, 0.2);url(#circuitGlow)"
              dur="3s"
              begin="1.6s"
              repeatCount="indefinite"
            />
          </path>
          
          <path d="M60 35 V65">
            <animate
              attributeName="stroke-dasharray"
              values="0,30;30,0"
              dur="2s"
              begin="2s"
              repeatCount="indefinite"
            />
            <animate 
              attributeName="stroke"
              values="url(#circuitGlow);rgba(96, 165, 250, 0.2);url(#circuitGlow)"
              dur="3s"
              begin="2s"
              repeatCount="indefinite"
            />
          </path>
          
          {/* Enhanced pulsing connection nodes with flare effect */}
          {[
            { cx: 40, cy: 40, delay: 0 },
            { cx: 50, cy: 40, delay: 0.3 },
            { cx: 60, cy: 40, delay: 0.6 },
            { cx: 40, cy: 50, delay: 0.9 },
            { cx: 50, cy: 50, delay: 1.2 },
            { cx: 60, cy: 50, delay: 1.5 },
            { cx: 40, cy: 60, delay: 1.8 },
            { cx: 50, cy: 60, delay: 2.1 },
            { cx: 60, cy: 60, delay: 2.4 }
          ].map(({ cx, cy, delay }) => (
            <g key={`node-${cx}-${cy}`}>
              {/* Base circle */}
              <circle
                cx={cx}
                cy={cy}
                r="1.5"
                className="fill-blue-400"
              >
                <animate
                  attributeName="r"
                  values="1.5;2.5;1.5"
                  dur="2s"
                  begin={`${delay}s`}
                  repeatCount="indefinite"
                />
                <animate
                  attributeName="opacity"
                  values="1;0.6;1"
                  dur="2s"
                  begin={`${delay}s`}
                  repeatCount="indefinite"
                />
                <animate
                  attributeName="fill"
                  values="#60A5FA;#3B82F6;#60A5FA"
                  dur="4s"
                  begin={`${delay}s`}
                  repeatCount="indefinite"
                />
              </circle>
              
              {/* Glow effect */}
              <circle
                cx={cx}
                cy={cy}
                r="3"
                fill="none"
                stroke="#60A5FA"
                strokeWidth="0.5"
                opacity="0"
              >
                <animate
                  attributeName="r"
                  values="2;5;2"
                  dur="2s"
                  begin={`${delay}s`}
                  repeatCount="indefinite"
                />
                <animate
                  attributeName="opacity"
                  values="0.8;0;0.8"
                  dur="2s"
                  begin={`${delay}s`}
                  repeatCount="indefinite"
                />
              </circle>
            </g>
          ))}
        </g>

        {/* Enhanced inner glow */}
        <circle
          cx="50"
          cy="50"
          r="25"
          className="fill-blue-400/20"
          filter="url(#glow)"
        >
          <animate
            attributeName="r"
            values="25;28;25"
            dur="4s"
            repeatCount="indefinite"
          />
          <animate
            attributeName="opacity"
            values="0.2;0.4;0.2"
            dur="4s"
            repeatCount="indefinite"
          />
        </circle>
      </svg>

      {/* Enhanced SVG filters */}
      <svg width="0" height="0">
        <defs>
          <filter id="glow">
            <feGaussianBlur stdDeviation="5" result="blur" />
            <feFlood floodColor="#3B82F6" floodOpacity="0.7" result="color" />
            <feComposite in="color" in2="blur" operator="in" result="glow" />
            <feMerge>
              <feMergeNode in="glow" />
              <feMergeNode in="glow" />
              <feMergeNode in="SourceGraphic" />
            </feMerge>
          </filter>
        </defs>
      </svg>
      
      {/* Add CSS for particle animations */}
      <style jsx>{`
        @keyframes floating {
          0% { transform: translateY(0px) translateX(0px); opacity: 0; }
          50% { opacity: 0.7; }
          100% { transform: translateY(-20px) translateX(5px); opacity: 0; }
        }
        
        @keyframes glowing {
          0% { box-shadow: 0 0 2px #60A5FA; }
          100% { box-shadow: 0 0 8px #60A5FA; }
        }
        
        @keyframes pulse-slow {
          0% { opacity: 0.5; }
          50% { opacity: 0.8; }
          100% { opacity: 0.5; }
        }
      `}</style>
    </div>
  );
}