import React, { useState } from 'react';
import { DENTAL_RESOURCES } from '../lib/constants';
import ResourceCategory from './resources/ResourceCategory';

export default function DentalResources() {
  const [activeQuestion, setActiveQuestion] = useState('concern');
  const [selectedConcern, setSelectedConcern] = useState('');
  const [filteredCategory, setFilteredCategory] = useState('');
  
  const handleQuestionSelect = (question) => {
    setActiveQuestion(question);
    if (question === 'concern' && selectedConcern) {
      setSelectedConcern('');
    }
    if (question === 'browse') {
      setFilteredCategory('');
    }
  };
  
  const handleConcernSelect = (concern) => {
    setSelectedConcern(concern);
    
    // Map concerns to categories
    const concernMap = {
      'tooth-pain': 'Emergency Care',
      'cleaning': 'Preventive Care',
      'braces': 'Orthodontics',
      'checkup': 'Preventive Care',
      'whitening': 'Cosmetic Dentistry'
    };
    
    setFilteredCategory(concernMap[concern] || '');
    setActiveQuestion('results');
  };
  
  const handleCategorySelect = (category) => {
    setFilteredCategory(category);
  };
  
  return (
    <div className="mt-16 sm:mt-20 md:mt-24">
      <div className="mb-0 mx-auto w-full bg-gradient-to-r from-blue-900/60 via-indigo-900/60 to-purple-900/60 rounded-xl overflow-hidden shadow-xl">
        <div className="relative py-12 px-6 md:px-10 lg:px-12">
          {/* Animated background elements */}
          <div className="absolute inset-0 pointer-events-none overflow-hidden">
            <div className="absolute -top-24 -right-24 w-48 h-48 bg-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
            <div className="absolute -bottom-24 -left-24 w-48 h-48 bg-purple-500/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>
          </div>
          
          <div className="relative z-10 w-full">
            <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-blue-300 via-indigo-300 to-purple-300 bg-clip-text text-transparent mb-4 text-center">
              Smart Dental Navigator
            </h2>
            <p className="text-blue-100/90 mb-8 text-center w-full max-w-3xl mx-auto">
              Find personalized dental resources tailored to your specific needs. Get expert guidance, educational content, and solutions for your oral health journey.
            </p>
            
            {/* Navigation Tabs */}
            <div className="flex justify-center mb-10">
              <div className="flex space-x-2 bg-white/5 p-1 rounded-full backdrop-blur-sm">
                <button 
                  onClick={() => handleQuestionSelect('concern')} 
                  className={`px-5 py-2.5 rounded-full text-sm font-medium transition-all duration-300 ${
                    activeQuestion === 'concern' 
                      ? 'bg-blue-500 text-white shadow-md' 
                      : 'text-blue-300 hover:bg-white/10'
                  }`}
                >
                  I have a concern
                </button>
                <button 
                  onClick={() => handleQuestionSelect('browse')} 
                  className={`px-5 py-2.5 rounded-full text-sm font-medium transition-all duration-300 ${
                    activeQuestion === 'browse' 
                      ? 'bg-indigo-500 text-white shadow-md' 
                      : 'text-blue-100 hover:bg-white/10'
                  }`}
                >
                  Browse categories
                </button>
                <button 
                  onClick={() => handleQuestionSelect('popular')} 
                  className={`px-5 py-2.5 rounded-full text-sm font-medium transition-all duration-300 ${
                    activeQuestion === 'popular' 
                      ? 'bg-purple-500 text-white shadow-md' 
                      : 'text-blue-100 hover:bg-white/10'
                  }`}
                >
                  Popular resources
                </button>
              </div>
            </div>
            
            {/* Dynamic Content Based on Tab */}
            <div className="w-full px-4 md:px-6 lg:px-8">
              {activeQuestion === 'concern' && (
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4 md:gap-6">
                  <button 
                    onClick={() => handleConcernSelect('tooth-pain')}
                    className="group p-5 bg-blue-900/30 rounded-lg hover:bg-blue-900/40 transition-all duration-300 text-left"
                  >
                    <div className="text-red-300 font-medium mb-1 group-hover:text-red-200">Tooth Pain</div>
                    <div className="text-sm text-blue-100/70">Find relief and emergency solutions</div>
                  </button>
                  <button 
                    onClick={() => handleConcernSelect('cleaning')}
                    className="group p-5 bg-blue-900/30 rounded-lg hover:bg-blue-900/40 transition-all duration-300 text-left"
                  >
                    <div className="text-blue-300 font-medium mb-1 group-hover:text-blue-200">Teeth Cleaning</div>
                    <div className="text-sm text-blue-100/70">Maintenance and hygiene tips</div>
                  </button>
                  <button 
                    onClick={() => handleConcernSelect('braces')}
                    className="group p-5 bg-blue-900/30 rounded-lg hover:bg-blue-900/40 transition-all duration-300 text-left"
                  >
                    <div className="text-green-300 font-medium mb-1 group-hover:text-green-200">Braces & Aligners</div>
                    <div className="text-sm text-blue-100/70">Orthodontic treatments info</div>
                  </button>
                  <button 
                    onClick={() => handleConcernSelect('checkup')}
                    className="group p-5 bg-blue-900/30 rounded-lg hover:bg-blue-900/40 transition-all duration-300 text-left"
                  >
                    <div className="text-indigo-300 font-medium mb-1 group-hover:text-indigo-200">Regular Checkups</div>
                    <div className="text-sm text-blue-100/70">Preventative care schedule</div>
                  </button>
                  <button 
                    onClick={() => handleConcernSelect('whitening')}
                    className="group p-5 bg-blue-900/30 rounded-lg hover:bg-blue-900/40 transition-all duration-300 text-left"
                  >
                    <div className="text-purple-300 font-medium mb-1 group-hover:text-purple-200">Teeth Whitening</div>
                    <div className="text-sm text-blue-100/70">Cosmetic procedures explained</div>
                  </button>
                  <button 
                    onClick={() => handleQuestionSelect('browse')}
                    className="group p-5 bg-blue-900/30 rounded-lg hover:bg-blue-900/40 transition-all duration-300 text-center flex items-center justify-center"
                  >
                    <div className="text-blue-100/70 group-hover:text-blue-100/90">View more concerns...</div>
                  </button>
                </div>
              )}
              
              {activeQuestion === 'browse' && (
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6">
                  {DENTAL_RESOURCES.map((category, index) => (
                    <button 
                      key={index}
                      onClick={() => handleCategorySelect(category.category)}
                      className={`group p-5 bg-blue-900/30 rounded-lg hover:bg-blue-900/40 transition-all duration-300 text-left ${
                        filteredCategory === category.category ? 'ring-2 ring-blue-400 bg-blue-900/40' : ''
                      }`}
                    >
                      <div className="text-blue-300 font-medium mb-1 group-hover:text-blue-200">{category.category}</div>
                      <div className="text-sm text-blue-100/70 group-hover:text-blue-100/90">{category.resources.length} resources</div>
                    </button>
                  ))}
                </div>
              )}
              
              {activeQuestion === 'popular' && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
                  <div className="p-5 bg-blue-900/30 rounded-lg flex items-center gap-4 hover:bg-blue-900/40 transition-all duration-300">
                    <div className="bg-blue-500/20 rounded-full p-3">
                      <svg className="w-6 h-6 text-blue-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                    </div>
                    <div className="flex-1">
                      <h3 className="font-medium text-blue-200">The Complete Guide to Flossing</h3>
                      <p className="text-sm text-blue-100/70">Viewed 423 times this week</p>
                    </div>
                  </div>
                  
                  <div className="p-5 bg-blue-900/30 rounded-lg flex items-center gap-4 hover:bg-blue-900/40 transition-all duration-300">
                    <div className="bg-purple-500/20 rounded-full p-3">
                      <svg className="w-6 h-6 text-purple-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                      </svg>
                    </div>
                    <div className="flex-1">
                      <h3 className="font-medium text-purple-200">When to Seek Emergency Dental Care</h3>
                      <p className="text-sm text-blue-100/70">Viewed 356 times this week</p>
                    </div>
                  </div>
                  
                  <div className="p-5 bg-blue-900/30 rounded-lg flex items-center gap-4 hover:bg-blue-900/40 transition-all duration-300">
                    <div className="bg-indigo-500/20 rounded-full p-3">
                      <svg className="w-6 h-6 text-indigo-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <div className="flex-1">
                      <h3 className="font-medium text-indigo-200">How Often Should You Replace Your Toothbrush?</h3>
                      <p className="text-sm text-blue-100/70">Viewed 289 times this week</p>
                    </div>
                  </div>
                  
                  <div className="md:col-span-2 lg:col-span-3">
                    <button className="w-full p-3 rounded-lg border border-dashed border-blue-500/30 text-blue-300 hover:bg-blue-900/20 hover:border-blue-500/50 transition-all duration-300 text-sm font-medium">
                      View more popular resources
                    </button>
                  </div>
                </div>
              )}
              
              {activeQuestion === 'results' && selectedConcern && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
                  <div className="bg-blue-900/30 p-5 rounded-lg md:col-span-2">
                    <h3 className="text-xl text-blue-200 mb-3 font-semibold">Resources for {selectedConcern.replace('-', ' ')}</h3>
                    <p className="text-blue-100/70 mb-5">We've found these resources that might help with your concern:</p>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <a href="#" className="block p-4 bg-blue-900/20 rounded-lg hover:bg-blue-900/30 transition-all duration-300 group">
                        <div className="flex flex-col h-full">
                          <div className="flex-1">
                            <h4 className="text-blue-300 group-hover:text-blue-200 font-medium">Understanding {selectedConcern.replace('-', ' ')}</h4>
                            <p className="text-sm text-blue-100/70 mt-1">Educational article • 5 min read</p>
                          </div>
                          <div className="flex justify-end mt-4 group-hover:translate-x-1 transition-transform">
                            <svg className="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                            </svg>
                          </div>
                        </div>
                      </a>
                      
                      <a href="#" className="block p-4 bg-blue-900/20 rounded-lg hover:bg-blue-900/30 transition-all duration-300 group">
                        <div className="flex flex-col h-full">
                          <div className="flex-1">
                            <h4 className="text-indigo-300 group-hover:text-indigo-200 font-medium">Treatment options for {selectedConcern.replace('-', ' ')}</h4>
                            <p className="text-sm text-blue-100/70 mt-1">Video guide • 3:45</p>
                          </div>
                          <div className="flex justify-end mt-4 group-hover:translate-x-1 transition-transform">
                            <svg className="w-5 h-5 text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                            </svg>
                          </div>
                        </div>
                      </a>
                      
                      <a href="#" className="block p-4 bg-blue-900/20 rounded-lg hover:bg-blue-900/30 transition-all duration-300 group">
                        <div className="flex flex-col h-full">
                          <div className="flex-1">
                            <h4 className="text-purple-300 group-hover:text-purple-200 font-medium">FAQ: Common questions about {selectedConcern.replace('-', ' ')}</h4>
                            <p className="text-sm text-blue-100/70 mt-1">Q&A guide • 10+ questions</p>
                          </div>
                          <div className="flex justify-end mt-4 group-hover:translate-x-1 transition-transform">
                            <svg className="w-5 h-5 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                            </svg>
                          </div>
                        </div>
                      </a>
                    </div>
                    
                    <div className="mt-6 flex justify-between border-t border-blue-500/10 pt-5">
                      <button 
                        onClick={() => handleQuestionSelect('concern')}
                        className="text-blue-300 text-sm flex items-center gap-1 hover:text-blue-200 transition-colors group"
                      >
                        <svg className="w-4 h-4 group-hover:-translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                        </svg>
                        Back to concerns
                      </button>
                      
                      <button className="text-white bg-blue-500 hover:bg-blue-600 px-4 py-2 rounded-md text-sm transition-colors shadow-md hover:shadow-lg font-medium">
                        Connect with an expert
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      
      <div className="space-y-3 mt-6">
        {DENTAL_RESOURCES.filter(cat => !filteredCategory || cat.category === filteredCategory).map((category, index) => (
          <ResourceCategory
            key={index}
            category={category.category}
            description={category.description}
            resources={category.resources}
          />
        ))}
      </div>
    </div>
  );
}