import React, { useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { XMarkIcon } from '@heroicons/react/24/outline';

export default function Modal({ 
  isOpen, 
  onClose, 
  title, 
  children, 
  actions,
  size = 'md', // sm, md, lg, xl, or full
  closeOnOutsideClick = true,
  showCloseButton = true,
  position = 'center',
  contentClassName = ''
}) {
  const modalRef = useRef(null);
  
  // Handle escape key press
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    window.addEventListener('keydown', handleEscape);
    return () => window.removeEventListener('keydown', handleEscape);
  }, [isOpen, onClose]);

  // Handle outside click
  const handleOutsideClick = (e) => {
    if (closeOnOutsideClick && modalRef.current && !modalRef.current.contains(e.target)) {
      onClose();
    }
  };

  // Determine width based on size prop
  const getWidth = () => {
    switch(size) {
      case 'sm': return 'max-w-sm';
      case 'md': return 'max-w-md';
      case 'lg': return 'max-w-lg';
      case 'xl': return 'max-w-xl';
      case 'full': return 'max-w-full';
      default: return 'max-w-md';
    }
  };

  // Determine position classes
  const getPositionClasses = () => {
    switch(position) {
      case 'top': return 'items-start mt-16';
      case 'bottom': return 'items-end mb-16';
      case 'center':
      default: return 'items-center';
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className={`flex min-h-full justify-center px-4 py-6 ${getPositionClasses()}`} onClick={handleOutsideClick}>
            {/* Backdrop */}
            <motion.div 
              className="fixed inset-0 bg-black/50 backdrop-blur-sm"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.2 }}
            />
            
            {/* Modal Content */}
            <motion.div
              ref={modalRef}
              className={`${getWidth()} relative w-full rounded-lg bg-gray-800 border border-gray-700 shadow-xl ${contentClassName}`}
              initial={{ opacity: 0, scale: 0.95, y: 10 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: 10 }}
              transition={{ duration: 0.2 }}
            >
              {/* Header */}
              {(title || showCloseButton) && (
                <div className="flex items-center justify-between px-6 py-4 border-b border-gray-700">
                  {title && <h3 className="text-lg font-medium text-white">{title}</h3>}
                  {showCloseButton && (
                    <button
                      onClick={onClose}
                      className="text-gray-400 hover:text-white rounded-full p-1 hover:bg-gray-700/50 transition-colors"
                    >
                      <XMarkIcon className="h-5 w-5" />
                    </button>
                  )}
                </div>
              )}
              
              {/* Content */}
              <div className="p-6">{children}</div>
              
              {/* Footer with Actions */}
              {actions && (
                <div className="px-6 py-4 border-t border-gray-700 flex justify-end space-x-3">
                  {actions}
                </div>
              )}
            </motion.div>
          </div>
        </div>
      )}
    </AnimatePresence>
  );
} 