import React from 'react';

export const Card = ({ 
  children, 
  className = '', 
  padding = 'normal',  // 'none', 'small', 'normal', 'large'
  elevation = 'medium', // 'none', 'low', 'medium', 'high'
  rounded = 'medium',  // 'none', 'small', 'medium', 'large', 'full'
  mobileFullWidth = true,
  ...props 
}) => {
  // Generate padding classes
  const paddingClasses = {
    none: '',
    small: 'p-3 md:p-4',
    normal: 'p-4 md:p-6',
    large: 'p-5 md:p-8'
  };

  // Generate elevation (shadow) classes
  const elevationClasses = {
    none: '',
    low: 'shadow-sm',
    medium: 'shadow-md',
    high: 'shadow-lg'
  };

  // Generate border radius classes
  const roundedClasses = {
    none: 'rounded-none',
    small: 'rounded-sm',
    medium: 'rounded-lg',
    large: 'rounded-xl',
    full: 'rounded-full'
  };

  // Mobile width handling
  const mobileWidthClass = mobileFullWidth ? 'w-full md:w-auto' : '';

  return (
    <div 
      className={`
        relative overflow-hidden bg-gray-800/90 backdrop-blur-sm
        border border-gray-700/50
        ${paddingClasses[padding] || paddingClasses.normal}
        ${elevationClasses[elevation] || elevationClasses.medium}
        ${roundedClasses[rounded] || roundedClasses.medium}
        ${mobileWidthClass}
        transition-all duration-200
        ${className}
      `}
      {...props}
    >
      {children}
    </div>
  );
}; 