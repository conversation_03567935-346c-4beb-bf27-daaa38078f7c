import React, { useState } from 'react';
import { Card } from '../common';

const SPECIALTIES = [
  'General Dentistry',
  'Orthodontics',
  'Periodontics',
  'Endodontics',
  'Oral Surgery'
];

export default function MentorList({ onSelectMentor, selectedMentor }) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSpecialty, setSelectedSpecialty] = useState('');

  // Example mentors data - in production, this would come from your database
  const mentors = [
    {
      id: 1,
      name: 'Dr. <PERSON>',
      specialty: 'General Dentistry',
      school: 'Harvard School of Dental Medicine',
      experience: '5 years',
      rating: 4.9,
      reviews: 124,
      online: true,
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=sarah'
    },
    {
      id: 2,
      name: 'Dr. <PERSON>',
      specialty: 'Orthodontics',
      school: 'UCLA School of Dentistry',
      experience: '8 years',
      rating: 4.8,
      reviews: 98,
      online: false,
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=michael'
    }
  ];

  const filteredMentors = mentors.filter(mentor => {
    const matchesSearch = mentor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         mentor.specialty.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesSpecialty = !selectedSpecialty || mentor.specialty === selectedSpecialty;
    return matchesSearch && matchesSpecialty;
  });

  return (
    <div className="space-y-6">
      {/* Search and Filter */}
      <div className="grid md:grid-cols-2 gap-4">
        <input
          type="text"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          placeholder="Search mentors..."
          className="bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white placeholder-white/50"
        />
        <select
          value={selectedSpecialty}
          onChange={(e) => setSelectedSpecialty(e.target.value)}
          className="bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white"
        >
          <option value="">All Specialties</option>
          {SPECIALTIES.map(specialty => (
            <option key={specialty} value={specialty}>{specialty}</option>
          ))}
        </select>
      </div>

      {/* Mentor Cards */}
      <div className="grid md:grid-cols-2 gap-6">
        {filteredMentors.map(mentor => (
          <Card
            key={mentor.id}
            className={`cursor-pointer transition-all duration-300 ${
              selectedMentor?.id === mentor.id
                ? 'ring-2 ring-blue-500'
                : 'hover:bg-white/10'
            }`}
            onClick={() => onSelectMentor(mentor)}
          >
            <div className="flex items-start gap-4">
              <div className="relative">
                <img
                  src={mentor.avatar}
                  alt={mentor.name}
                  className="w-16 h-16 rounded-full"
                />
                <span className={`absolute bottom-0 right-0 w-4 h-4 rounded-full border-2 border-gray-900 ${
                  mentor.online ? 'bg-green-400' : 'bg-gray-400'
                }`} />
              </div>
              
              <div className="flex-1">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-medium text-white">{mentor.name}</h3>
                    <p className="text-white/60">{mentor.specialty}</p>
                  </div>
                  <div className="flex items-center text-yellow-400">
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                    <span className="ml-1 text-white">{mentor.rating}</span>
                    <span className="ml-1 text-white/60">({mentor.reviews})</span>
                  </div>
                </div>
                
                <div className="mt-2 space-y-1 text-sm">
                  <p className="text-white/80">{mentor.school}</p>
                  <p className="text-white/60">{mentor.experience} experience</p>
                </div>
                
                <div className="mt-4 flex justify-between items-center">
                  <span className={`text-sm ${mentor.online ? 'text-green-400' : 'text-white/60'}`}>
                    {mentor.online ? 'Available Now' : 'Offline'}
                  </span>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onSelectMentor(mentor);
                    }}
                    className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-400 transition-colors"
                  >
                    Start Chat
                  </button>
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
}