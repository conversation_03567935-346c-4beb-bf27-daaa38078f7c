import React, { useState, useRef } from 'react';
import { useChat } from '../../lib/hooks/useChat';
import { Card } from '../common';
import ResponseDisplay from '../ResponseDisplay';
import Logo from '../common/Logo';
import { signUp } from '../lib/services/authService';
import { supabase } from '../lib/supabase';

export default function FloatingAssistant() {
  const [isOpen, setIsOpen] = useState(false);
  const constraintsRef = useRef(null);
  
  const {
    question,
    setQuestion,
    response,
    loading,
    error, 
    clearError,
    handleSubmit: handleChatSubmit
  } = useChat('mentor');

  const handleSubmit = async (e) => {
    e.preventDefault();
    handleChatSubmit(e);
  };

  const handleQuestionSubmit = (e) => {
    e.preventDefault();
    handleSubmit(e);
  };

  return (
    <div
      ref={constraintsRef}
      className="fixed bottom-6 right-6 z-50"
    >
      <div
        className="pointer-events-auto"
      >
        {isOpen ? (
          <Card className="w-96 shadow-xl">
            <div className="flex justify-between items-center mb-4">
              <Logo size="small" />
              <div className="text-sm text-white/60">Mentor Assistant</div>
              <button
                onClick={() => setIsOpen(false)}
                className="text-white/60 hover:text-white"
              >
                <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="space-y-4">
              <form onSubmit={handleQuestionSubmit} className="space-y-4">
                <textarea
                  value={question}
                  onChange={(e) => setQuestion(e.target.value)}
                  placeholder="Ask me about mentorship opportunities, career advice, or how to find a dental mentor..."
                  className="w-full h-24 bg-white/5 border-white/10 rounded-lg text-white resize-none"
                />
                <button
                  type="submit"
                  disabled={loading || !question.trim()}
                  className="w-full px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-400 disabled:opacity-50 transition-colors"
                >
                  {loading ? 'Thinking...' : 'Ask'}
                </button>
                <p className="text-xs text-white/60 text-center mt-2">
                  Your personal dental mentor assistant
                </p>
              </form>

              {response && <ResponseDisplay response={response} typingSpeed={30} />}
            </div>
          </Card>
        ) : (
          <button
            onClick={() => setIsOpen(true)}
            className="p-3 bg-white/10 backdrop-blur-sm rounded-full border border-white/20 shadow-xl hover:bg-white/20 transition-colors"
          >
            <Logo size="small" />
          </button>
        )}
      </div>
    </div>
  );
}