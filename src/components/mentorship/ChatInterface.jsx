import React, { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useMessaging } from '../../lib/hooks/useMessaging';

export default function ChatInterface({ mentor, onClose }) {
  const [message, setMessage] = useState('');
  const messagesEndRef = useRef(null);
  const {
    messages,
    sendMessage,
    loading,
    error,
    typing,
    setTypingStatus
  } = useMessaging(mentor?.conversationId);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!message.trim() || loading) return;

    try {
      await sendMessage(message);
      setMessage('');
    } catch (err) {
      console.error('Error sending message:', err);
    }
  };

  const handleTyping = (e) => {
    setMessage(e.target.value);
    setTypingStatus(e.target.value.length > 0);
  };

  return (
    <div className="flex flex-col h-[600px]">
      {/* Chat Header */}
      <div className="flex items-center justify-between p-4 border-b border-white/10">
        <div className="flex items-center gap-3">
          <div className="relative">
            <img
              src={mentor.avatar}
              alt={mentor.name}
              className="w-10 h-10 rounded-full"
            />
            <span className={`absolute bottom-0 right-0 w-3 h-3 rounded-full ${
              mentor.online ? 'bg-green-400' : 'bg-gray-400'
            }`} />
          </div>
          <div>
            <h3 className="font-medium text-white">{mentor.name}</h3>
            <p className="text-sm text-white/60">{mentor.specialty}</p>
          </div>
        </div>
        <button
          onClick={onClose}
          className="p-2 text-white/60 hover:text-white rounded-lg hover:bg-white/5"
        >
          <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((msg, index) => (
          <motion.div
            key={msg.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
            className={`flex ${msg.sender_id === mentor.id ? 'justify-start' : 'justify-end'}`}
          >
            <div className={`
              max-w-[80%] p-3 rounded-lg
              ${msg.sender_id === mentor.id 
                ? 'bg-white/10 text-white' 
                : 'bg-blue-500 text-white'
              }
            `}>
              <p>{msg.content}</p>
              <span className="text-xs opacity-60 mt-1 block">
                {new Date(msg.created_at).toLocaleTimeString()}
              </span>
            </div>
          </motion.div>
        ))}
        <div ref={messagesEndRef} />
        
        {typing && Object.keys(typing).length > 0 && (
          <div className="text-white/60 text-sm">
            {Object.values(typing)[0].username} is typing...
          </div>
        )}
      </div>

      {/* Message Input */}
      <form onSubmit={handleSubmit} className="p-4 border-t border-white/10">
        <div className="flex gap-2">
          <input
            type="text"
            value={message}
            onChange={handleTyping}
            placeholder="Type your message..."
            className="flex-1 bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white placeholder-white/50"
          />
          <button
            type="submit"
            disabled={!message.trim() || loading}
            className="px-5 py-2 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white rounded-full shadow-lg disabled:opacity-50 transition-all"
          >
            {loading ? (
              <div className="w-6 h-6 border-2 border-white/20 border-t-white rounded-full animate-spin" />
            ) : (
              <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
              </svg>
            )}
          </button>
        </div>
      </form>
    </div>
  );
}