import React, { useState } from 'react';
import { Card } from '../common';

const FORUM_CATEGORIES = [
  {
    id: 'admissions',
    name: 'Admissions',
    description: 'Discuss dental school applications and admissions',
    topics: ['Personal Statements', 'Interviews', 'School Selection']
  },
  {
    id: 'academics',
    name: 'Academics',
    description: 'Share study tips and course experiences',
    topics: ['DAT Prep', 'Science Prerequisites', 'GPA']
  },
  {
    id: 'experience',
    name: 'Clinical Experience',
    description: 'Exchange shadowing and volunteering experiences',
    topics: ['Shadowing', 'Volunteering', 'Research']
  }
];

export default function DiscussionForums() {
  const [selectedCategory, setSelectedCategory] = useState(null);

  return (
    <div className="space-y-6">
      {/* Category Selection */}
      <div className="grid md:grid-cols-3 gap-4">
        {FORUM_CATEGORIES.map(category => (
          <Card
            key={category.id}
            className={`cursor-pointer transition-all duration-300 ${
              selectedCategory?.id === category.id
                ? 'ring-2 ring-blue-500'
                : 'hover:bg-white/10'
            }`}
            onClick={() => setSelectedCategory(category)}
          >
            <h3 className="text-lg font-semibold text-white mb-2">
              {category.name}
            </h3>
            <p className="text-white/80 text-sm mb-4">
              {category.description}
            </p>
            <div className="flex flex-wrap gap-2">
              {category.topics.map(topic => (
                <span
                  key={topic}
                  className="px-2 py-1 bg-white/5 rounded-full text-xs text-white/60"
                >
                  {topic}
                </span>
              ))}
            </div>
          </Card>
        ))}
      </div>

      {/* Selected Category View */}
      {selectedCategory && (
        <Card>
          <div className="flex justify-between items-center mb-6">
            <div>
              <h2 className="text-xl font-semibold text-white">
                {selectedCategory.name}
              </h2>
              <p className="text-white/60">
                {selectedCategory.description}
              </p>
            </div>
            <button
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-400 transition-colors"
            >
              New Discussion
            </button>
          </div>

          <div className="space-y-4">
            {/* Example discussions - in production, fetch from database */}
            {[1, 2, 3].map(i => (
              <div
                key={i}
                className="p-4 bg-white/5 rounded-lg hover:bg-white/10 transition-colors cursor-pointer"
              >
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-medium text-white">
                      Example Discussion Title {i}
                    </h3>
                    <p className="text-white/60 text-sm mt-1">
                      Started by User{i} • 2 hours ago
                    </p>
                  </div>
                  <div className="text-sm text-white/60">
                    <span className="mr-4">👥 {i * 5} replies</span>
                    <span>👁️ {i * 20} views</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}
    </div>
  );
}