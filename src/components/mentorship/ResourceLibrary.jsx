import React from 'react';
import { Card } from '../common';

const RESOURCE_CATEGORIES = [
  {
    name: 'Application Materials',
    resources: [
      {
        title: 'Personal Statement Guide',
        description: 'Tips and examples for crafting your dental school personal statement',
        type: 'PDF',
        size: '2.1 MB'
      },
      {
        title: 'Interview Preparation',
        description: 'Common dental school interview questions and strategies',
        type: 'PDF',
        size: '1.8 MB'
      }
    ]
  },
  {
    name: 'Study Materials',
    resources: [
      {
        title: 'DAT Study Schedule',
        description: '12-week study plan for the Dental Admission Test',
        type: 'XLSX',
        size: '245 KB'
      },
      {
        title: 'Science Review Notes',
        description: 'Comprehensive notes for biology, chemistry, and organic chemistry',
        type: 'PDF',
        size: '5.2 MB'
      }
    ]
  }
];

export default function ResourceLibrary() {
  return (
    <div className="space-y-8">
      {RESOURCE_CATEGORIES.map(category => (
        <Card key={category.name}>
          <h3 className="text-xl font-semibold text-white mb-6">
            {category.name}
          </h3>
          
          <div className="grid gap-4">
            {category.resources.map((resource, index) => (
              <div
                key={index}
                className="p-4 bg-white/5 rounded-lg hover:bg-white/10 transition-colors"
              >
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <h4 className="font-medium text-white mb-1">
                      {resource.title}
                    </h4>
                    <p className="text-white/60 text-sm">
                      {resource.description}
                    </p>
                  </div>
                  
                  <div className="flex items-center gap-4">
                    <div className="text-sm text-white/60">
                      <span className="mr-2">{resource.type}</span>
                      <span>{resource.size}</span>
                    </div>
                    <button
                      className="p-2 text-blue-400 hover:text-blue-300 hover:bg-white/5 rounded transition-colors"
                    >
                      <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>
      ))}
    </div>
  );
}