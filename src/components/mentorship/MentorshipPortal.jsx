import React, { useState } from 'react';
import { useUser } from '../../contexts/UserContext';
import { useMessaging } from '../../lib/hooks/useMessaging';
import { Card } from '../common';
import MentorList from './MentorList';
import ChatInterface from './ChatInterface';
import DiscussionForums from './DiscussionForums';
import ResourceLibrary from './ResourceLibrary';

export default function MentorshipPortal() {
  const { user } = useUser();
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedMentor, setSelectedMentor] = useState(null);
  const { messages, sendMessage, loading } = useMessaging(
    selectedMentor?.conversationId
  );

  const tabs = [
    { id: 'overview', label: 'Overview' },
    { id: 'mentors', label: 'Find Mentors' },
    { id: 'forums', label: 'Discussion Forums' },
    { id: 'resources', label: 'Resources' }
  ];

  if (!user) {
    return (
      <Card>
        <div className="text-center py-8">
          <h3 className="text-xl font-semibold text-white mb-4">
            Sign in to access the Mentorship Portal
          </h3>
          <p className="text-white/80 mb-6">
            Connect with experienced dental students and professionals
          </p>
          <AuthButton />
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Program Overview */}
      <Card>
        <h2 className="text-2xl font-bold text-white mb-6">
          DentalMentor Connect
        </h2>
        <p className="text-lg text-white/80 mb-8">
          Connect with experienced dental students and professionals who can guide you
          through your pre-dental journey. Get personalized advice, application tips,
          and insights from those who've successfully navigated the path to dental school.
        </p>
        
        <div className="grid md:grid-cols-3 gap-6">
          <div className="bg-white/5 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-2">
              1:1 Mentorship
            </h3>
            <p className="text-white/80">
              Get matched with a mentor based on your interests and goals
            </p>
          </div>
          
          <div className="bg-white/5 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-2">
              Discussion Forums
            </h3>
            <p className="text-white/80">
              Join topic-based discussions with peers and mentors
            </p>
          </div>
          
          <div className="bg-white/5 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-2">
              Resource Library
            </h3>
            <p className="text-white/80">
              Access curated resources shared by mentors
            </p>
          </div>
        </div>
      </Card>

      {/* Navigation Tabs */}
      <div className="flex overflow-x-auto scrollbar-hide space-x-4">
        {tabs.map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`
              px-6 py-2 rounded-full text-sm font-medium whitespace-nowrap
              transition-colors duration-200 touch-target
              ${activeTab === tab.id 
                ? 'bg-blue-500 text-white' 
                : 'bg-white/10 text-white/80 hover:bg-white/20'
              }
            `}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="min-h-[400px]">
        {activeTab === 'overview' && (
          <Card>
            <h3 className="text-xl font-semibold text-white mb-4">
              Getting Started
            </h3>
            <div className="space-y-4">
              <p className="text-white/80">
                Welcome to DentalMentor Connect! Here's how to make the most of your experience:
              </p>
              <ol className="list-decimal list-inside space-y-2 text-white/80">
                <li>Complete your profile with your interests and goals</li>
                <li>Browse available mentors and find your match</li>
                <li>Join relevant discussion forums</li>
                <li>Schedule your first mentoring session</li>
              </ol>
            </div>
          </Card>
        )}

        {activeTab === 'mentors' && (
          <MentorList
            onSelectMentor={setSelectedMentor}
            selectedMentor={selectedMentor}
          />
        )}

        {activeTab === 'forums' && (
          <DiscussionForums />
        )}

        {activeTab === 'resources' && (
          <ResourceLibrary />
        )}
      </div>

      {/* Chat Interface */}
      {selectedMentor && (
        <Card>
          <ChatInterface
            mentor={selectedMentor}
            messages={messages}
            onSendMessage={sendMessage}
            loading={loading}
          />
        </Card>
      )}
    </div>
  );
}