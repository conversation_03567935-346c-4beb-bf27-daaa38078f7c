import React, { useState, useRef, useEffect } from 'react';
import ImageUpload from './ImageUpload';
import { validateImage } from '../lib/utils/validation';
import { buttonPress, pulseOnHover, fadeInUp } from '../lib/utils/animations';
import { motion } from 'framer-motion';

export default function QuestionForm({
  question,
  setQuestion,
  placeholder = "Ask a dental health question...",
  image,
  onImageUpload,
  loading,
  onSubmit,
  disabled,
  preventViewSwitch
}) {
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [permissionDenied, setPermissionDenied] = useState(false);
  const mediaRecorderRef = useRef(null);
  const audioChunksRef = useRef([]);
  const timerRef = useRef(null);
  const streamRef = useRef(null);
  const [imageError, setImageError] = useState('');
  const fileInputRef = useRef(null);

  // Cleanup function
  useEffect(() => {
    return () => {
      stopRecording();
    };
  }, []);

  const handleInputChange = (e) => {
    setQuestion(e.target.value);
    // Don't auto-switch to chat view if preventViewSwitch is true
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (question.trim()) {
      onSubmit(question);
    }
  };

  const handleImageClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    setImageError('');
    if (file) {
      try {
        // Validate file type and size
        if (!file.type.startsWith('image/')) {
          setImageError('Please select an image file');
          return;
        }
        
        const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
          setImageError('Only JPEG, PNG and WebP images are supported');
          return;
        }
        
        const maxSize = 5 * 1024 * 1024; // 5MB
        if (file.size > maxSize) {
          setImageError('Image must be less than 5MB');
          return;
        }
        
        // Pass the raw File object to onImageUpload
        onImageUpload(file);
      } catch (error) {
        console.error('Error handling image upload:', error);
        setImageError(error.message);
      }
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handlePaste = (e) => {
    const items = e.clipboardData?.items;
    if (items) {
      for (let i = 0; i < items.length; i++) {
        if (items[i].type.indexOf('image') !== -1) {
          const file = items[i].getAsFile();
          onImageUpload(file);
          break;
        }
      }
    }
  };

  const handleImageUpload = (file) => {
    try {
      const validatedImage = validateImage(file);
      onImageUpload(validatedImage);
    } catch (error) {
      alert(error.message);
    }
  };

  const startRecording = async () => {
    try {
      // Reset states
      setPermissionDenied(false);
      setRecordingTime(0);
      audioChunksRef.current = [];

      // Request microphone access
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: true,
        video: false
      });

      streamRef.current = stream;

      // Create MediaRecorder instance
      let mimeType = 'audio/webm';
      if (MediaRecorder.isTypeSupported('audio/webm;codecs=opus')) {
        mimeType = 'audio/webm;codecs=opus';
      }

      const mediaRecorder = new MediaRecorder(stream, {
        mimeType,
        audioBitsPerSecond: 128000
      });

      mediaRecorderRef.current = mediaRecorder;

      // Handle data available event
      mediaRecorder.addEventListener('dataavailable', (event) => {
        if (event.data && event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      });

      // Handle stop event
      mediaRecorder.addEventListener('stop', async () => {
        try {
          if (audioChunksRef.current.length > 0) {
            const audioBlob = new Blob(audioChunksRef.current, { type: mimeType });
            const audioText = await convertSpeechToText(audioBlob);
            if (audioText) {
              setQuestion(audioText);
            }
          }
        } catch (error) {
          console.error('Error processing audio:', error);
          alert('Failed to convert speech to text. Please try again.');
        } finally {
          // Cleanup
          if (streamRef.current) {
            streamRef.current.getTracks().forEach(track => track.stop());
          }
          setIsRecording(false);
          setRecordingTime(0);
          if (timerRef.current) {
            clearInterval(timerRef.current);
          }
        }
      });

      // Start recording
      mediaRecorder.start();
      setIsRecording(true);
      console.log('Recording started');

      // Start timer
      timerRef.current = setInterval(() => {
        setRecordingTime(prevTime => {
          const newTime = prevTime + 1;
          if (newTime >= 15) {
            stopRecording();
            return 15;
          }
          return newTime;
        });
      }, 1000);

    } catch (error) {
      console.error('Error starting recording:', error);
      setPermissionDenied(true);
      alert('Could not access microphone. Please check your browser permissions.');
    }
  };

  const stopRecording = () => {
    try {
      if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
        mediaRecorderRef.current.stop();
      }

      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
      }

      if (timerRef.current) {
        clearInterval(timerRef.current);
      }

      setIsRecording(false);
      setRecordingTime(0);
    } catch (error) {
      console.error('Error stopping recording:', error);
    }
  };

  const convertSpeechToText = async (audioBlob) => {
    try {
      console.log('Converting speech to text...');
      const formData = new FormData();
      formData.append('audio', audioBlob, 'audio.webm');

      const response = await fetch('/api/speech-to-text', {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        throw new Error('Speech to text conversion failed');
      }

      const data = await response.json();
      console.log('Conversion successful:', data.text);
      return data.text;
    } catch (error) {
      console.error('Speech to text error:', error);
      throw error;
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="flex gap-3 relative">
        <div className="relative flex-1 group">
          {/* Enhanced glow effect on focus */}
          <div className="absolute -inset-0.5 bg-gradient-to-r from-indigo-500 to-blue-500 rounded-full opacity-0 group-focus-within:opacity-50 blur-md transition-opacity duration-300"></div>

          <input
            type="text"
            value={question}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            onPaste={handlePaste}
            placeholder={placeholder}
            className="relative w-full rounded-full px-6 py-3.5 border-0 bg-white/10 text-white placeholder-white/50 shadow-lg backdrop-blur-xl focus:ring-2 focus:ring-indigo-500 focus:bg-white/15 transition-all duration-200 focus:shadow-indigo-500/20"
            maxLength={500}
            disabled={disabled || loading}
          />

          {/* Character count */}
          <div className="absolute right-24 top-1/2 -translate-y-1/2 text-xs text-white/40 pointer-events-none transition-opacity duration-200 opacity-0 group-focus-within:opacity-100">
            {question.length}/500
          </div>
        </div>

        {/* Voice Recording Button */}
        <motion.button
          type="button"
          onClick={isRecording ? stopRecording : startRecording}
          variants={pulseOnHover}
          whileTap={buttonPress}
          animate={isRecording ? 'pulse' : 'initial'}
          disabled={disabled || loading}
          className={`relative rounded-full p-3 ${
            isRecording
              ? 'bg-rose-500 shadow-lg shadow-rose-500/50 text-white'
              : 'bg-indigo-600 hover:bg-indigo-500 shadow-lg text-white hover:shadow-indigo-500/50'
          } transition-all duration-200 flex items-center justify-center ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          {isRecording ? (
            <>
              <span className="absolute -top-2 -right-2 bg-rose-600 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                {15 - recordingTime}
              </span>
              <span className="sr-only">Stop Recording</span>
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5">
                <path fillRule="evenodd" d="M4.5 7.5a3 3 0 013-3h9a3 3 0 013 3v9a3 3 0 01-3 3h-9a3 3 0 01-3-3v-9z" clipRule="evenodd" />
              </svg>
            </>
          ) : (
            <>
              <span className="sr-only">Start Voice Recording</span>
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5">
                <path d="M8.25 4.5a3.75 3.75 0 117.5 0v8.25a3.75 3.75 0 01-7.5 0V4.5z" />
                <path d="M6 10.5a.75.75 0 01.75.75v1.5a5.25 5.25 0 1010.5 0v-1.5a.75.75 0 011.5 0v1.5a6.751 6.751 0 01-6 6.709v2.291h3a.75.75 0 010 1.5h-7.5a.75.75 0 010-1.5h3v-2.291a6.751 6.751 0 01-6-6.709v-1.5A.75.75 0 016 10.5z" />
              </svg>
            </>
          )}
        </motion.button>

        {/* Image Upload Button */}
        <motion.button
          type="button"
          onClick={handleImageClick}
          variants={pulseOnHover}
          whileTap={buttonPress}
          disabled={disabled || loading}
          className={`rounded-full p-3 bg-indigo-600 hover:bg-indigo-500 shadow-lg text-white transition-all duration-200 flex items-center justify-center hover:shadow-indigo-500/50 ${
            image ? 'ring-2 ring-indigo-400 ring-offset-2 ring-offset-black' : ''
          } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          <span className="sr-only">Upload Image</span>
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5">
            <path fillRule="evenodd" d="M1.5 6a2.25 2.25 0 012.25-2.25h16.5A2.25 2.25 0 0122.5 6v12a2.25 2.25 0 01-2.25 2.25H3.75A2.25 2.25 0 011.5 18V6zM3 16.06V18c0 .414.336.75.75.75h16.5A.75.75 0 0021 18v-1.94l-2.69-2.689a1.5 1.5 0 00-2.12 0l-.88.879.97.97a.75.75 0 11-1.06 1.06l-5.16-5.159a1.5 1.5 0 00-2.12 0L3 16.061zm10.125-7.81a1.125 1.125 0 112.25 0 1.125 1.125 0 01-2.25 0z" clipRule="evenodd" />
          </svg>
          <input
            ref={fileInputRef}
            type="file"
            accept="image/jpeg, image/png, image/webp"
            onChange={handleFileChange}
            className="hidden"
            disabled={disabled || loading}
          />
        </motion.button>

        {/* Submit Button */}
        <motion.button
          type="submit"
          variants={pulseOnHover}
          whileTap={buttonPress}
          disabled={!question.trim() || disabled || loading}
          className={`rounded-full px-6 shadow-lg flex items-center justify-center transition-all duration-200 
            ${
              !question.trim() || disabled || loading
                ? 'bg-gray-600/50 text-white/50 cursor-not-allowed'
                : 'bg-gradient-to-r from-indigo-600 to-blue-600 hover:from-indigo-500 hover:to-blue-500 text-white hover:shadow-indigo-500/50'
            }`}
        >
          {loading ? (
            <div className="py-2.5">
              <div className="flex space-x-1.5">
                <div className="w-2 h-2 bg-white/80 rounded-full animate-bounce" style={{ animationDelay: '0s' }} />
                <div className="w-2 h-2 bg-white/80 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                <div className="w-2 h-2 bg-white/80 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
              </div>
            </div>
          ) : (
            <div className="flex items-center space-x-2 py-2.5">
              <span>Ask</span>
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-4 h-4">
                <path d="M3.105 2.289a.75.75 0 00-.826.95l1.414 4.925A1.5 1.5 0 005.135 9.25h6.115a.75.75 0 010 1.5H5.135a1.5 1.5 0 00-1.442 1.086l-1.414 4.926a.75.75 0 00.826.95 28.896 28.896 0 0015.293-7.154.75.75 0 000-1.115A28.897 28.897 0 003.105 2.289z" />
              </svg>
            </div>
          )}
        </motion.button>
      </div>

      {/* Image preview section */}
      {image && (
        <motion.div 
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 10 }}
          transition={{ duration: 0.3 }}
          className="mt-3 relative bg-white/5 backdrop-blur-sm rounded-xl border border-indigo-500/20 p-3 overflow-hidden group"
        >
          <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/5 to-blue-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
          <div className="flex items-start space-x-3 relative z-10">
            <div className="relative w-16 h-16 overflow-hidden rounded-lg border border-white/10">
              <img src={URL.createObjectURL(image)} alt="Uploaded" className="w-full h-full object-cover" />
            </div>
            <div className="flex-1">
              <div className="flex justify-between">
                <div>
                  <p className="text-sm text-white font-medium">{image.name}</p>
                  <p className="text-xs text-white/60">
                    {image.type} · {(image.size / 1024).toFixed(1)} KB
                  </p>
                </div>
                <button
                  type="button"
                  onClick={() => onImageUpload(null)}
                  className="text-white/60 hover:text-white p-1 transition-colors"
                >
                  <span className="sr-only">Remove image</span>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-5 h-5">
                    <path d="M6.28 5.22a.75.75 0 00-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 101.06 1.06L10 11.06l3.72 3.72a.75.75 0 101.06-1.06L11.06 10l3.72-3.72a.75.75 0 00-1.06-1.06L10 8.94 6.28 5.22z" />
                  </svg>
                </button>
              </div>
              <p className="text-xs text-white/70 mt-1.5">This image will be analyzed as part of your question.</p>
            </div>
          </div>
          
          {imageError && (
            <div className="mt-2 text-sm text-rose-400 bg-rose-500/10 px-3 py-1.5 rounded-lg border border-rose-500/20">
              {imageError}
            </div>
          )}
        </motion.div>
      )}
    </form>
  );
}