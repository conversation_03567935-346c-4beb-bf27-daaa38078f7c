import React, { useEffect } from 'react';
import { motion, useAnimation } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { ABOUT_CONTENT } from '../../lib/constants/about';
import Logo from '../common/Logo';
import { useParallax } from '../../lib/hooks/useParallax';
import { usePreventAnimationOverload } from '../../lib/hooks/usePreventAnimationOverload';

export default function AboutPage() {
  // Implement animation performance optimization
  const { isReduced } = usePreventAnimationOverload(120000); // 2 minutes before reducing animations
  
  // Pass the enabled flag to useParallax based on performance
  useParallax(!isReduced);

  // Animation density control based on device capability
  const particleCount = isReduced ? 5 : 20;
  const orbOpacity = isReduced ? 0.05 : 0.1;

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-900 via-gray-800 to-gray-900 relative overflow-hidden">
      {/* Animated background particles - conditionally render based on performance */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        {/* Large glowing orbs - with performance optimizations */}
        <div
          className={`absolute top-1/4 right-1/4 w-96 h-96 rounded-full blur-[120px] ${isReduced ? '' : 'animate-pulse-soft'}`}
          style={{
            backgroundColor: `rgba(59, 130, 246, ${orbOpacity})`,
            animationDuration: isReduced ? '8s' : '4s'
          }}
        ></div>
        <div
          className={`absolute bottom-1/4 left-1/4 w-96 h-96 rounded-full blur-[120px] ${isReduced ? '' : 'animate-pulse-soft'}`}
          style={{
            backgroundColor: `rgba(168, 85, 247, ${orbOpacity})`,
            animationDelay: '2s',
            animationDuration: isReduced ? '8s' : '4s'
          }}
        ></div>

        {/* Small floating particles - reduced count on lower performance */}
        {[...Array(particleCount)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-white/30 rounded-full animate-float"
            style={{
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
              animationDuration: `${10 + Math.random() * 20}s`,
              animationDelay: `${Math.random() * 10}s`,
              opacity: isReduced ? 0.15 : (0.2 + Math.random() * 0.5)
            }}
          />
        ))}
      </div>

      <HeroSection isReduced={isReduced} />
      <MissionSection isReduced={isReduced} />
      <FounderSection isReduced={isReduced} />
      <VisionSection isReduced={isReduced} />
      <JoinSection isReduced={isReduced} />
    </div>
  );
}

function HeroSection({ isReduced }) {
  const controls = useAnimation();
  const [ref, inView] = useInView({ triggerOnce: true });

  useEffect(() => {
    if (inView) {
      controls.start('visible');
    }

    // Cleanup function to prevent animation memory leaks
    return () => {
      controls.stop();
    };
  }, [controls, inView]);

  return (
    <motion.section
      ref={ref}
      initial="hidden"
      animate={controls}
      variants={{
        hidden: { opacity: 0 },
        visible: { opacity: 1, transition: { duration: 0.6 } }
      }}
      className="relative min-h-[90vh] flex items-center justify-center overflow-hidden py-24 px-4"
    >
      {/* Enhanced animated background elements - with performance optimizations */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-br from-indigo-900/40 via-gray-900 to-purple-900/40" data-parallax={isReduced ? "none" : "slow"} />
        <div
          className="absolute inset-0 bg-[radial-gradient(circle_at_top_right,rgba(79,70,229,0.3),transparent_50%)]"
          data-parallax={isReduced ? "none" : "medium"}
          style={{animation: isReduced ? 'none' : 'pulse 8s ease-in-out infinite'}}
        />
        <div
          className="absolute inset-0 bg-[radial-gradient(circle_at_bottom_left,rgba(168,85,247,0.3),transparent_50%)]"
          data-parallax={isReduced ? "none" : "fast"}
          style={{animation: isReduced ? 'none' : 'pulse 8s ease-in-out infinite', animationDelay: '4s'}}
        />
      </div>

      <div className="relative max-w-7xl mx-auto text-center">
        <motion.div
          variants={{
            hidden: { opacity: 0, y: 20 },
            visible: { opacity: 1, y: 0, transition: { duration: 0.6 } }
          }}
          className="mb-8 relative"
        >
          {/* Glow effect behind text - subtle ambient glow */}
          {!isReduced && (
            <div className="absolute -inset-10 blur-3xl bg-gradient-to-r from-blue-500/10 via-indigo-500/10 to-purple-500/10 rounded-full opacity-40 transition-opacity duration-1000 ease-in-out"></div>
          )}

          <h1 className="text-6xl md:text-7xl lg:text-8xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-indigo-300 to-purple-400 font-heading mb-6">
            About SMILO
          </h1>
          <p className="text-xl md:text-2xl text-blue-100/80 max-w-3xl mx-auto leading-relaxed">
            {ABOUT_CONTENT.welcome.content}
          </p>

          {/* Decorative underline */}
          <div className="w-32 h-1 bg-gradient-to-r from-blue-500 to-purple-500 mx-auto mt-8 rounded-full"></div>
        </motion.div>
      </div>
    </motion.section>
  );
}

function FounderSection({ isReduced }) {
  const controls = useAnimation();
  const [ref, inView] = useInView({ triggerOnce: true });

  useEffect(() => {
    if (inView) {
      controls.start('visible');
    }

    // Cleanup function to prevent animation memory leaks
    return () => {
      controls.stop();
    };
  }, [controls, inView]);

  return (
    <motion.section
      ref={ref}
      initial="hidden"
      animate={controls}
      variants={{
        hidden: { opacity: 0 },
        visible: { opacity: 1, transition: { duration: 0.6 } }
      }}
      className="py-32 relative overflow-hidden"
    >
      {/* Enhanced background effects - with performance optimizations */}
      <div className="absolute inset-0 bg-gradient-to-br from-indigo-900/20 via-transparent to-purple-900/20 pointer-events-none"></div>

      {/* Dynamic animated background particles - reduced count on lower performance */}
      {!isReduced && [...Array(isReduced ? 5 : 15)].map((_, i) => (
        <div
          key={i}
          className="absolute w-2 h-2 bg-blue-400/20 rounded-full animate-float"
          style={{
            top: `${Math.random() * 100}%`,
            left: `${Math.random() * 100}%`,
            animationDuration: `${20 + Math.random() * 30}s`,
            animationDelay: `${Math.random() * 5}s`
          }}
        />
      ))}

      {/* Decorative flowing light lines */}
      <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-indigo-500/50 to-transparent"></div>
      <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-purple-500/50 to-transparent"></div>

      {/* Subtle ambient glowing orbs */}
      <div
        className="absolute top-1/3 -right-20 w-96 h-96 bg-indigo-500/5 rounded-full blur-3xl transition-opacity duration-1000 ease-in-out"
      ></div>
      <div
        className="absolute bottom-1/4 -left-20 w-96 h-96 bg-blue-400/5 rounded-full blur-3xl transition-opacity duration-1000 ease-in-out"
      ></div>
      <div
        className="absolute top-3/4 right-1/4 w-64 h-64 bg-purple-500/5 rounded-full blur-3xl transition-opacity duration-1000 ease-in-out"
      ></div>

      <div className="max-w-7xl mx-auto px-4 relative z-10">
        {/* Stunning profile header with cinematic styling */}
        <motion.div
          className="text-center mb-20 relative"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1 }}
        >
          {/* Subtle ambient glow behind the name */}
          {!isReduced && (
            <div className="absolute -inset-20 blur-3xl bg-gradient-to-r from-blue-500/10 via-indigo-500/15 to-purple-500/10 rounded-full opacity-40 transition-all duration-1000 ease-in-out"></div>
          )}

          {/* Pre-title elegant detail */}
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.6 }}
            className="mb-3"
          >
            <span className="text-lg text-indigo-300 tracking-widest uppercase font-light">The Visionary Behind Smilo</span>
          </motion.div>

          {/* Grand name display with animated underline */}
          <h2 className={`text-7xl sm:text-8xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-indigo-300 to-purple-400 font-heading mb-4 tracking-tight ${isReduced ? '' : 'animate-glow'}`}>
            Michael Coffie
          </h2>

          <motion.div
            className="h-1 bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500 mx-auto rounded-full mb-6"
            initial={{ width: "0%" }}
            animate={{ width: "280px", maxWidth: "50%" }}
            transition={{ delay: 0.5, duration: 0.8 }}
          ></motion.div>

          {/* Title with shimmering effect */}
          <motion.p
            className="text-2xl text-blue-300 tracking-wide mb-3"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.7, duration: 0.8 }}
          >
            Founder & Visionary
          </motion.p>

          {/* Brief elegant description below name */}
          <motion.p
            className="max-w-2xl mx-auto text-lg text-white/70 italic"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.9, duration: 0.8 }}
          >
            "Bridging innovation and compassion to transform dental healthcare access for everyone."
          </motion.p>
        </motion.div>

        {/* Main profile showcase with dramatic layout */}
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-8 lg:gap-16 items-center mb-20">
          {/* Left column: Stunning portrait with premium effects */}
          <motion.div
            className="lg:col-span-5 flex justify-center"
            initial={{ opacity: 0, x: -40 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.6, duration: 1 }}
          >
            <div className="relative z-10 group">
              {/* Subtle glow effect with slow transition */}
              <motion.div
                className="absolute -inset-4 bg-gradient-to-r from-blue-600/20 via-indigo-600/20 to-purple-600/20 rounded-xl blur-lg opacity-40 group-hover:opacity-60 transition-all duration-1000 ease-in-out z-0"
                animate={isReduced ? { boxShadow: '0 0 10px rgba(79, 70, 229, 0.3)' } : {
                  boxShadow: ['0 0 10px rgba(79, 70, 229, 0.3)', '0 0 20px rgba(79, 70, 229, 0.4)', '0 0 10px rgba(79, 70, 229, 0.3)']
                }}
                transition={{
                  duration: isReduced ? 0 : 5,
                  ease: 'easeInOut',
                  repeat: isReduced ? 0 : Infinity,
                  repeatType: "reverse"
                }}
              ></motion.div>

              {/* Premium portrait frame with gradients */}
              <div className="relative rounded-xl p-1.5 bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 shadow-2xl z-10">
                {/* Image container with hover effects */}
                <div className="relative overflow-hidden rounded-lg bg-gray-900 aspect-[3/4] w-full max-w-sm">
                  {/* Primary portrait image */}
                  <img
                    src={ABOUT_CONTENT.founder.image}
                    alt={ABOUT_CONTENT.founder.name}
                    className="w-full h-full object-cover object-top transform transition duration-700 group-hover:scale-105"
                  />

                  {/* Overlaid gradient for depth */}
                  <div className="absolute inset-0 rounded-lg bg-gradient-to-t from-indigo-900/80 via-indigo-900/10 to-transparent opacity-50 group-hover:opacity-70 transition-opacity duration-700"></div>

                  {/* Cinematic light beam effect - conditionally rendered based on performance */}
                  {!isReduced && (
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-tr from-transparent via-indigo-500/20 to-transparent"
                      animate={{
                        backgroundPosition: ['0% 0%', '100% 100%']
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        repeatType: "reverse"
                      }}
                    ></motion.div>
                  )}
                </div>
              </div>

              {/* Decorative geometric accent */}
              <div className="absolute -bottom-6 -right-6 w-28 h-28 bg-gradient-to-r from-blue-500/30 to-indigo-600/30 rounded-br-xl blur-sm opacity-80 z-[-1]"></div>
            </div>
          </motion.div>

          {/* Right column: Bio with premium styling */}
          <motion.div
            className="lg:col-span-7 space-y-8"
            initial={{ opacity: 0, x: 40 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.8, duration: 1 }}
          >
            {/* Main bio card with premium glass effect */}
            <div className="bg-gradient-to-br from-indigo-900/50 to-purple-900/40 backdrop-blur-xl rounded-2xl p-8 border border-white/10 hover:border-indigo-500/40 transition-all duration-500 shadow-2xl relative overflow-hidden group">
              {/* Animated background elements - with performance optimizations */}
              {!isReduced && (
                <>
                  <div className="absolute top-0 right-0 w-80 h-80 bg-indigo-500/10 rounded-full blur-3xl -translate-y-1/2 translate-x-1/2 group-hover:bg-indigo-500/20 transition-all duration-700"></div>
                  <div className="absolute bottom-0 left-0 w-80 h-80 bg-blue-500/10 rounded-full blur-3xl translate-y-1/2 -translate-x-1/2 group-hover:bg-blue-500/20 transition-all duration-700"></div>
                </>
              )}

              {/* Stylized quote marks */}
              <svg className="w-16 h-16 text-indigo-500/10 absolute top-4 left-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
              </svg>

              {/* Premium biography text with elegant styling */}
              <div className="relative z-10 space-y-6">
                <p className="text-xl text-white/90 leading-relaxed first-letter:text-5xl first-letter:font-bold first-letter:text-indigo-300 first-letter:mr-2 first-letter:float-left">{ABOUT_CONTENT.founder.bio}</p>

                {/* Elegant separator */}
                <div className="h-px w-full bg-gradient-to-r from-blue-500/0 via-indigo-500/50 to-blue-500/0 my-6"></div>

                {/* Signature element */}
                <div className="flex justify-end">
                  <div className="text-right">
                    <div className="inline-block text-2xl font-handwriting text-indigo-300 -rotate-2">Michael Coffie</div>
                    <div className="text-sm text-indigo-400/70">Founder, Smilo Dental</div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Experience and Leadership cards with premium styling */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16 w-full max-w-6xl mx-auto">
          <motion.div
            className="bg-gradient-to-br from-indigo-900/40 to-blue-900/30 backdrop-blur-xl rounded-2xl border border-white/10 hover:border-indigo-400/40 transition-all duration-300 p-7 shadow-xl relative overflow-hidden group"
            whileHover={isReduced ? {} : { y: -5, boxShadow: "0 25px 50px rgba(79, 70, 229, 0.15)" }}
            transition={{ duration: 0.4 }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            custom={1}
          >
            {/* Animation optimizations */}
            {!isReduced && (
              <div className="absolute top-0 right-0 w-40 h-40 bg-indigo-500/10 rounded-full blur-3xl -translate-y-1/2 translate-x-1/2 group-hover:bg-indigo-500/20 transition-all duration-500"></div>
            )}

            <div className="relative">
              <div className="flex items-center mb-5">
                <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-indigo-500 to-blue-500 flex items-center justify-center mr-4 shadow-lg shadow-indigo-500/30">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </div>
                <h3 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-indigo-300 to-blue-300">Experience</h3>
              </div>

              <p className="text-white/80 leading-relaxed">{ABOUT_CONTENT.founder.experience}</p>
            </div>
          </motion.div>

          <motion.div
            className="bg-gradient-to-br from-indigo-900/40 to-purple-900/30 backdrop-blur-xl rounded-2xl border border-white/10 hover:border-indigo-400/40 transition-all duration-300 p-7 shadow-xl relative overflow-hidden group"
            whileHover={isReduced ? {} : { y: -5, boxShadow: "0 25px 50px rgba(79, 70, 229, 0.15)" }}
            transition={{ duration: 0.4 }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            custom={2}
          >
            {/* Animation optimizations */}
            {!isReduced && (
              <div className="absolute top-0 right-0 w-40 h-40 bg-purple-500/10 rounded-full blur-3xl -translate-y-1/2 translate-x-1/2 group-hover:bg-purple-500/20 transition-all duration-500"></div>
            )}

            <div className="relative">
              <div className="flex items-center mb-5">
                <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-indigo-500 to-purple-500 flex items-center justify-center mr-4 shadow-lg shadow-indigo-500/30">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                </div>
                <h3 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-indigo-300 to-purple-300">Leadership</h3>
              </div>

              <p className="text-white/80 leading-relaxed">{ABOUT_CONTENT.founder.leadership}</p>
            </div>
          </motion.div>
        </div>

        {/* Impact & Vision section with premium styling */}
        <motion.div
          className="bg-gradient-to-br from-indigo-900/40 to-blue-900/30 backdrop-blur-xl rounded-2xl border border-white/10 hover:border-indigo-400/40 transition-all duration-300 p-8 shadow-xl relative overflow-hidden group w-full max-w-6xl mx-auto"
          whileHover={isReduced ? {} : { y: -5, boxShadow: "0 25px 50px rgba(79, 70, 229, 0.2)" }}
          transition={{ duration: 0.4 }}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          custom={3}
        >
          <div className="absolute top-0 right-0 w-96 h-96 bg-indigo-500/10 rounded-full blur-3xl -translate-y-1/2 translate-x-1/2 group-hover:bg-indigo-500/20 transition-all duration-700"></div>
          <div className="absolute bottom-0 left-0 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl translate-y-1/2 -translate-x-1/2 group-hover:bg-purple-500/20 transition-all duration-700"></div>

          <div className="relative z-10">
            <div className="flex items-center mb-6">
              <div className="w-14 h-14 rounded-full bg-gradient-to-br from-blue-500 to-purple-500 flex items-center justify-center mr-4 shadow-lg shadow-indigo-500/30">
                <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-300 via-indigo-300 to-purple-300">Impact & Vision</h3>
            </div>

            <div className="space-y-6">
              <div className="pl-4 border-l-2 border-indigo-500/50">
                <h4 className="text-xl font-medium text-indigo-300 mb-3">Impact</h4>
                <p className="text-lg text-white/80 leading-relaxed">{ABOUT_CONTENT.founder.impact}</p>
              </div>

              <div className="h-px w-full bg-gradient-to-r from-transparent via-indigo-500/30 to-transparent"></div>

              <div className="pl-4 border-l-2 border-purple-500/50">
                <h4 className="text-xl font-medium text-purple-300 mb-3">Vision</h4>
                <p className="text-lg text-white/80 leading-relaxed">{ABOUT_CONTENT.founder.vision}</p>
              </div>

              <div className="h-px w-full bg-gradient-to-r from-transparent via-indigo-500/30 to-transparent"></div>

              <p className="text-2xl bg-clip-text text-transparent bg-gradient-to-r from-indigo-300 via-blue-300 to-purple-300 font-medium text-center mt-6 py-4">
                {ABOUT_CONTENT.founder.closing}
              </p>
            </div>
          </div>
        </motion.div>
      </div>
    </motion.section>
  );
}

function VisionSection({ isReduced }) {
  const controls = useAnimation();
  const [ref, inView] = useInView({ triggerOnce: true });

  useEffect(() => {
    if (inView) {
      controls.start('visible');
    }

    // Cleanup function to prevent animation memory leaks
    return () => {
      controls.stop();
    };
  }, [controls, inView]);

  return (
    <motion.section
      ref={ref}
      initial="hidden"
      animate={controls}
      variants={{
        hidden: { opacity: 0 },
        visible: { opacity: 1, transition: { duration: 0.6 } }
      }}
      className="py-24 bg-gradient-to-b from-indigo-900/40 via-purple-900/30 to-indigo-900/40 relative"
    >
      {/* Decorative elements */}
      <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-blue-500/0 via-indigo-500/50 to-purple-500/0"></div>
      <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-purple-500/0 via-indigo-500/50 to-blue-500/0"></div>

      {/* Glowing orb decoration */}
      <div className="absolute top-1/2 left-1/4 transform -translate-y-1/2 w-64 h-64 bg-indigo-500/10 rounded-full blur-[100px] animate-pulse-soft"></div>
      <div className="absolute top-1/2 right-1/4 transform -translate-y-1/2 w-64 h-64 bg-purple-500/10 rounded-full blur-[100px] animate-pulse-soft" style={{animationDelay: '2s'}}></div>

      <div className="max-w-7xl mx-auto text-center px-4 relative z-10">
        <h2 className="text-4xl font-bold gradient-text mb-8 animate-glow">What's Michael's Why?</h2>
        <div className="max-w-4xl mx-auto glass-card shadow-xl shadow-indigo-500/5 hover:shadow-indigo-500/20 transition-all duration-500">
          {ABOUT_CONTENT.mission.michaelsWhy.split('\n\n').map((paragraph, index) => (
            <p key={index} className="text-xl text-white/80 leading-relaxed mb-6 last:mb-0">
              {paragraph}
            </p>
          ))}
        </div>
      </div>
    </motion.section>
  );
}

function JoinSection({ isReduced }) {
  const controls = useAnimation();
  const [ref, inView] = useInView({ triggerOnce: true });

  useEffect(() => {
    if (inView) {
      controls.start('visible');
    }

    // Cleanup function to prevent animation memory leaks
    return () => {
      controls.stop();
    };
  }, [controls, inView]);

  return (
    <motion.section
      ref={ref}
      initial="hidden"
      animate={controls}
      variants={{
        hidden: { opacity: 0 },
        visible: { opacity: 1, transition: { duration: 0.6 } }
      }}
      className="py-24 relative"
    >
      {/* Background glow - subtle ambient effect */}
      <div className="absolute bottom-0 right-0 w-96 h-96 bg-blue-500/5 rounded-full blur-[150px] transition-opacity duration-1000 ease-in-out"></div>

      <div className="max-w-4xl mx-auto text-center px-4 relative z-10">
        <h2 className="text-4xl font-bold gradient-text mb-8">{ABOUT_CONTENT.joinTeam.title}</h2>
        <div className="glass-card shadow-xl shadow-indigo-500/5">
          <p className="text-xl text-white/80 mb-12">{ABOUT_CONTENT.joinTeam.content}</p>

          <div className="inline-block relative group">
            <div className="absolute -inset-1 bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 rounded-full blur-xl opacity-70 group-hover:opacity-100 transition duration-500 animate-pulse-soft"></div>
            <a
              href={`mailto:${ABOUT_CONTENT.joinTeam.contact.email}`}
              className="relative px-8 py-4 bg-gradient-to-r from-blue-700/80 via-indigo-700/80 to-purple-700/80 hover:from-blue-600/90 hover:via-indigo-600/90 hover:to-purple-600/90 text-white rounded-full transition-all duration-300 inline-flex items-center gap-2 shadow-lg shadow-indigo-500/20"
            >
              Message Michael
              <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
              </svg>
            </a>
          </div>
        </div>
      </div>
    </motion.section>
  );
}

function MissionSection({ isReduced }) {
  const controls = useAnimation();
  const [ref, inView] = useInView({ triggerOnce: true });

  useEffect(() => {
    if (inView) {
      controls.start('visible');
    }

    // Cleanup function to prevent animation memory leaks
    return () => {
      controls.stop();
    };
  }, [controls, inView]);

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: i => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: i * (isReduced ? 0.1 : 0.2),
        duration: isReduced ? 0.3 : 0.5
      }
    })
  };

  return (
    <motion.section
      ref={ref}
      initial="hidden"
      animate={controls}
      variants={{
        hidden: { opacity: 0 },
        visible: { opacity: 1, transition: { duration: 0.6, staggerChildren: isReduced ? 0.1 : 0.2 } }
      }}
      className="py-24 relative section-gradient"
    >
      {/* Gradient overlay for top and bottom */}
      <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-blue-500/0 via-indigo-500/50 to-purple-500/0"></div>
      <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-purple-500/0 via-indigo-500/50 to-blue-500/0"></div>

      <div className="max-w-7xl mx-auto px-4 relative z-10">
        <h2 className="text-4xl font-bold gradient-text text-center mb-12 animate-glow">Our Mission</h2>

        <div className="grid md:grid-cols-3 gap-8">
          {ABOUT_CONTENT.mission.mission.map((item, index) => (
            <motion.div
              key={index}
              custom={index}
              variants={cardVariants}
              className="group bg-gradient-to-br from-indigo-900/40 to-purple-900/30 backdrop-blur-sm rounded-xl p-6 border border-white/10 hover:border-indigo-400/50 transition-all duration-500 shadow-lg shadow-indigo-500/5 hover:shadow-indigo-500/20 hover:-translate-y-1"
            >
              <div className="text-3xl mb-4 group-hover:scale-110 transition-transform duration-300">{['🌟', '📚', '🤝'][index]}</div>
              <h3 className="text-xl font-semibold bg-clip-text text-transparent bg-gradient-to-r from-blue-300 to-indigo-300 mb-4">{item.title}</h3>
              <p className="text-white/80 leading-relaxed">{item.content}</p>
            </motion.div>
          ))}
        </div>

        <motion.div
          variants={cardVariants}
          custom={3}
          className="mt-12 bg-gradient-to-br from-indigo-900/40 to-purple-900/30 backdrop-blur-sm rounded-xl p-8 border border-white/10 hover:border-indigo-400/50 transition-all duration-500 shadow-lg shadow-indigo-500/5 hover:shadow-indigo-500/20"
        >
          <h3 className="text-2xl font-semibold bg-clip-text text-transparent bg-gradient-to-r from-blue-300 to-indigo-300 mb-4">Our Vision</h3>
          <p className="text-white/80 leading-relaxed">{ABOUT_CONTENT.mission.vision}</p>

          {/* Decorative graphic */}
          <div className="w-full h-1 mt-6 bg-gradient-to-r from-blue-500/0 via-indigo-500/50 to-purple-500/0 rounded-full"></div>
        </motion.div>
      </div>
    </motion.section>
  );
}