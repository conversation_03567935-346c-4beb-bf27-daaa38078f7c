import React, { useState } from 'react';
import SmiloBrushSync from '../../components/bluetooth/SmiloBrushSync';
import BrushingAnalysisService, { AnalysisResult } from '../../components/bluetooth/BrushingAnalysisService';

const ToothbrushDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'dashboard' | 'history' | 'settings'>('dashboard');
  const [latestAnalysis, setLatestAnalysis] = useState<AnalysisResult | null>(null);
  
  // Handle when analysis completes
  const handleAnalysisComplete = (result: AnalysisResult) => {
    setLatestAnalysis(result);
  };
  
  // Format date for display
  const formatDate = (timestamp: number): string => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: 'numeric',
      minute: 'numeric'
    }).format(new Date(timestamp));
  };

  return (
    <BrushingAnalysisService>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-gradient-to-r from-indigo-700 to-blue-600 shadow-md">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex justify-between items-center">
              <h1 className="text-white text-3xl font-bold">Smilo.Dental</h1>
              <div className="flex space-x-4">
                <button className="bg-white text-indigo-600 px-4 py-2 rounded-md shadow-sm font-medium hover:bg-indigo-50 transition-colors">
                  Dashboard
                </button>
                <button className="bg-indigo-800 text-white px-4 py-2 rounded-md shadow-sm font-medium hover:bg-indigo-900 transition-colors">
                  My Profile
                </button>
              </div>
            </div>
          </div>
        </header>
        
        {/* Main content */}
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Page Title */}
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900">SmiloBrush AI Dashboard</h2>
            <p className="mt-2 text-gray-600">Sync your smart toothbrush to track and improve your oral health with AI-powered insights.</p>
          </div>
          
          {/* Tab Navigation */}
          <div className="border-b border-gray-200 mb-6">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('dashboard')}
                className={`pb-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'dashboard'
                    ? 'border-indigo-500 text-indigo-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Dashboard
              </button>
              <button
                onClick={() => setActiveTab('history')}
                className={`pb-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'history'
                    ? 'border-indigo-500 text-indigo-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Brushing History
              </button>
              <button
                onClick={() => setActiveTab('settings')}
                className={`pb-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'settings'
                    ? 'border-indigo-500 text-indigo-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Device Settings
              </button>
            </nav>
          </div>
          
          {/* Dashboard Content */}
          {activeTab === 'dashboard' && (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Left Column - Sync & Status */}
              <div className="lg:col-span-2">
                <SmiloBrushSync 
                  userId="user123" 
                  onAnalysisComplete={handleAnalysisComplete}
                />
                
                {/* Analysis Quick Stats */}
                {latestAnalysis && (
                  <div className="mt-6 bg-white rounded-xl shadow-md overflow-hidden">
                    <div className="px-6 py-4 bg-gradient-to-r from-indigo-600 to-blue-500">
                      <h3 className="text-white font-medium">Latest Analysis Summary</h3>
                      <p className="text-indigo-100 text-sm">
                        Analyzed on {formatDate(latestAnalysis.analysisTimestamp)}
                      </p>
                    </div>
                    <div className="p-6">
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div className="bg-indigo-50 rounded-lg p-3 text-center">
                          <span className="text-sm text-gray-600">Plaque Score</span>
                          <p className={`text-xl font-bold ${
                            latestAnalysis.plaqueDetection && latestAnalysis.plaqueDetection.score > 80 
                              ? 'text-green-600' 
                              : latestAnalysis.plaqueDetection && latestAnalysis.plaqueDetection.score > 60 
                                ? 'text-yellow-600' 
                                : 'text-red-600'
                          }`}>
                            {latestAnalysis.plaqueDetection ? latestAnalysis.plaqueDetection.score : 'N/A'}
                          </p>
                        </div>
                        
                        <div className="bg-indigo-50 rounded-lg p-3 text-center">
                          <span className="text-sm text-gray-600">Cavities</span>
                          <p className={`text-xl font-bold ${
                            latestAnalysis.cavityDetection && latestAnalysis.cavityDetection.detected
                              ? 'text-red-600' 
                              : 'text-green-600'
                          }`}>
                            {latestAnalysis.cavityDetection && latestAnalysis.cavityDetection.detected 
                              ? latestAnalysis.cavityDetection.locations.length 
                              : '0'}
                          </p>
                        </div>
                        
                        <div className="bg-indigo-50 rounded-lg p-3 text-center">
                          <span className="text-sm text-gray-600">Gum Health</span>
                          <p className={`text-xl font-bold ${
                            !latestAnalysis.gumInflammation || !latestAnalysis.gumInflammation.detected
                              ? 'text-green-600' 
                              : latestAnalysis.gumInflammation.severity === 'mild'
                                ? 'text-yellow-600'
                                : 'text-red-600'
                          }`}>
                            {latestAnalysis.gumInflammation 
                              ? (latestAnalysis.gumInflammation.detected 
                                ? latestAnalysis.gumInflammation.severity
                                : 'Good')
                              : 'N/A'}
                          </p>
                        </div>
                        
                        <div className="bg-indigo-50 rounded-lg p-3 text-center">
                          <span className="text-sm text-gray-600">Overall</span>
                          <p className={`text-xl font-bold ${getOverallHealthColor(latestAnalysis)}`}>
                            {getOverallHealth(latestAnalysis)}
                          </p>
                        </div>
                      </div>
                      
                      {/* Primary Recommendation */}
                      {latestAnalysis.recommendations.length > 0 && (
                        <div className="mt-4 p-3 bg-yellow-50 border border-yellow-100 rounded-lg">
                          <h4 className="text-sm font-medium text-gray-900 flex items-center">
                            <svg className="w-4 h-4 mr-1 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            Primary Recommendation
                          </h4>
                          <p className="text-sm text-gray-700 mt-1">
                            {latestAnalysis.recommendations.sort((a, b) => a.priority - b.priority)[0].text}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
              
              {/* Right Column - Tips and Stats */}
              <div className="space-y-6">
                {/* Usage Stats */}
                <div className="bg-white rounded-xl shadow-md overflow-hidden">
                  <div className="px-6 py-4 bg-gradient-to-r from-green-600 to-teal-500">
                    <h3 className="text-white font-medium">Brushing Stats</h3>
                  </div>
                  <div className="p-6">
                    <div className="space-y-4">
                      <div>
                        <div className="flex justify-between mb-1">
                          <span className="text-sm text-gray-700">Weekly Target</span>
                          <span className="text-sm font-medium text-gray-700">9 of 14 sessions</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2.5">
                          <div className="bg-green-600 h-2.5 rounded-full" style={{ width: '64%' }}></div>
                        </div>
                      </div>
                      
                      <div>
                        <div className="flex justify-between mb-1">
                          <span className="text-sm text-gray-700">Average Duration</span>
                          <span className="text-sm font-medium text-gray-700">2m 12s</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2.5">
                          <div className="bg-yellow-500 h-2.5 rounded-full" style={{ width: '73%' }}></div>
                        </div>
                      </div>
                      
                      <div>
                        <div className="flex justify-between mb-1">
                          <span className="text-sm text-gray-700">Coverage Score</span>
                          <span className="text-sm font-medium text-gray-700">81%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2.5">
                          <div className="bg-green-600 h-2.5 rounded-full" style={{ width: '81%' }}></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Tips and Guidance */}
                <div className="bg-white rounded-xl shadow-md overflow-hidden">
                  <div className="px-6 py-4 bg-gradient-to-r from-blue-600 to-indigo-500">
                    <h3 className="text-white font-medium">Smart Tips</h3>
                  </div>
                  <div className="p-6">
                    <ul className="space-y-3">
                      <li className="flex">
                        <svg className="flex-shrink-0 h-5 w-5 text-indigo-500 mt-1 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                        </svg>
                        <div>
                          <h4 className="text-sm font-medium text-gray-900">Tilt the Brush</h4>
                          <p className="mt-1 text-xs text-gray-600">Hold your brush at a 45° angle to your gums for better plaque removal.</p>
                        </div>
                      </li>
                      <li className="flex">
                        <svg className="flex-shrink-0 h-5 w-5 text-indigo-500 mt-1 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <div>
                          <h4 className="text-sm font-medium text-gray-900">Stay 2 Minutes</h4>
                          <p className="mt-1 text-xs text-gray-600">Brush for a full 2 minutes for optimal cleaning and plaque removal.</p>
                        </div>
                      </li>
                      <li className="flex">
                        <svg className="flex-shrink-0 h-5 w-5 text-indigo-500 mt-1 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                        <div>
                          <h4 className="text-sm font-medium text-gray-900">Personalized Brushing</h4>
                          <p className="mt-1 text-xs text-gray-600">Focus on areas highlighted in your analysis for improved oral health.</p>
                        </div>
                      </li>
                    </ul>
                    <div className="mt-5">
                      <button className="w-full px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-md hover:bg-indigo-700 transition-colors">
                        View All Brushing Tips
                      </button>
                    </div>
                  </div>
                </div>
                
                {/* Dental Checkup Reminder */}
                <div className="bg-white rounded-xl shadow-md overflow-hidden">
                  <div className="px-6 py-4 bg-gradient-to-r from-purple-600 to-pink-500">
                    <h3 className="text-white font-medium">Dental Checkup</h3>
                  </div>
                  <div className="p-6">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 bg-purple-100 rounded-full p-3">
                        <svg className="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                      </div>
                      <div className="ml-4">
                        <h4 className="text-sm font-medium text-gray-900">Next Recommended Checkup</h4>
                        <p className="text-sm text-gray-600">August 12, 2023</p>
                      </div>
                    </div>
                    <div className="mt-5">
                      <button className="w-full px-4 py-2 bg-purple-600 text-white text-sm font-medium rounded-md hover:bg-purple-700 transition-colors">
                        Schedule Appointment
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          {/* History Tab Content */}
          {activeTab === 'history' && (
            <div className="bg-white rounded-xl shadow-md overflow-hidden">
              <div className="px-6 py-4 bg-gradient-to-r from-indigo-600 to-blue-500">
                <h3 className="text-white font-medium">Brushing History</h3>
                <p className="text-indigo-100 text-sm">
                  Track your progress over time
                </p>
              </div>
              
              <div className="p-6">
                <p className="text-gray-500 text-sm">This feature will display your brushing history and trends over time.</p>
                
                {/* Placeholder for brushing history charts */}
                <div className="mt-4 border-2 border-dashed border-gray-300 rounded-lg p-12 text-center">
                  <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                  <h3 className="mt-2 text-sm font-medium text-gray-900">Brushing History Charts</h3>
                  <p className="mt-1 text-sm text-gray-500">Sync your SmiloBrush to see your brushing history and trends.</p>
                </div>
              </div>
            </div>
          )}
          
          {/* Settings Tab Content */}
          {activeTab === 'settings' && (
            <div className="bg-white rounded-xl shadow-md overflow-hidden">
              <div className="px-6 py-4 bg-gradient-to-r from-indigo-600 to-blue-500">
                <h3 className="text-white font-medium">Device Settings</h3>
                <p className="text-indigo-100 text-sm">
                  Manage your SmiloBrush preferences
                </p>
              </div>
              
              <div className="p-6">
                <p className="text-gray-500 text-sm mb-6">Configure your SmiloBrush settings and preferences.</p>
                
                {/* Settings Form Placeholder */}
                <div className="space-y-6">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-3">Device Information</h4>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <p className="text-gray-500">Device Name</p>
                          <p className="font-medium text-gray-900">SmiloBrush Pro</p>
                        </div>
                        <div>
                          <p className="text-gray-500">Firmware Version</p>
                          <p className="font-medium text-gray-900">v2.1.4</p>
                        </div>
                        <div>
                          <p className="text-gray-500">Last Synced</p>
                          <p className="font-medium text-gray-900">Today, 10:32 AM</p>
                        </div>
                        <div>
                          <p className="text-gray-500">Battery Level</p>
                          <p className="font-medium text-gray-900">78%</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-3">Notification Preferences</h4>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <label htmlFor="brushing-reminders" className="text-sm text-gray-700">Brushing Reminders</label>
                        <div className="relative inline-flex items-center h-6 rounded-full w-11 bg-indigo-600">
                          <span className="inline-block h-4 w-4 transform translate-x-6 rounded-full bg-white transition"></span>
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <label htmlFor="analysis-notifications" className="text-sm text-gray-700">Analysis Notifications</label>
                        <div className="relative inline-flex items-center h-6 rounded-full w-11 bg-indigo-600">
                          <span className="inline-block h-4 w-4 transform translate-x-6 rounded-full bg-white transition"></span>
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <label htmlFor="dentist-recommendations" className="text-sm text-gray-700">Dentist Recommendations</label>
                        <div className="relative inline-flex items-center h-6 rounded-full w-11 bg-gray-200">
                          <span className="inline-block h-4 w-4 transform translate-x-1 rounded-full bg-white transition"></span>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-3">Data Sharing</h4>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="flex items-start">
                        <div className="flex items-center h-5">
                          <input id="share-dentist" name="share-dentist" type="checkbox" className="h-4 w-4 text-indigo-600 border-gray-300 rounded" defaultChecked />
                        </div>
                        <div className="ml-3 text-sm">
                          <label htmlFor="share-dentist" className="font-medium text-gray-700">Share with my dentist</label>
                          <p className="text-gray-500">Allow your dentist to access your brushing data and analysis results</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="pt-5">
                    <div className="flex justify-end">
                      <button type="button" className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50">
                        Reset to Default
                      </button>
                      <button type="submit" className="ml-3 px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700">
                        Save Settings
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </main>
      </div>
    </BrushingAnalysisService>
  );
};

// Helper functions for health assessment
const getOverallHealth = (analysis: AnalysisResult): string => {
  const hasCavities = analysis.cavityDetection?.detected;
  const hasGumIssues = analysis.gumInflammation?.detected;
  const plaqueScore = analysis.plaqueDetection?.score || 0;
  
  if (hasCavities || (hasGumIssues && analysis.gumInflammation?.severity === 'severe') || plaqueScore < 50) {
    return 'Needs Work';
  } else if (hasGumIssues || plaqueScore < 70) {
    return 'Fair';
  } else {
    return 'Good';
  }
};

const getOverallHealthColor = (analysis: AnalysisResult): string => {
  const health = getOverallHealth(analysis);
  
  switch (health) {
    case 'Good':
      return 'text-green-600';
    case 'Fair':
      return 'text-yellow-600';
    default:
      return 'text-red-600';
  }
};

export default ToothbrushDashboard; 