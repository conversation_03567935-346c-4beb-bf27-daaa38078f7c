import React, { useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import '../styles/aitools.css';
import BackgroundParticles from '../components/BackgroundParticles';

const AITools = () => {
  const containerRef = useRef(null);

  useEffect(() => {
    const handleMouseMove = (e) => {
      const cards = document.querySelectorAll('.glow');
      cards.forEach(card => {
        const rect = card.getBoundingClientRect();
        const x = ((e.clientX - rect.left) / card.offsetWidth) * 100;
        const y = ((e.clientY - rect.top) / card.offsetHeight) * 100;
        card.style.setProperty('--mouse-x', `${x}%`);
        card.style.setProperty('--mouse-y', `${y}%`);
      });
    };

    const container = containerRef.current;
    if (container) {
      container.addEventListener('mousemove', handleMouseMove);
    }

    return () => {
      if (container) {
        container.removeEventListener('mousemove', handleMouseMove);
      }
    };
  }, []);

  const tools = [
    {
      id: 'webcam',
      title: 'Webcam Oral Scanner',
      description: 'Use your webcam to scan your mouth for potential oral health issues',
      icon: (
        <svg className="w-12 h-12 text-blue-400" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M15.2 2.4H8.8C4 2.4 2 4.4 2 9.2V14.8C2 19.6 4 21.6 8.8 21.6H15.2C20 21.6 22 19.6 22 14.8V9.2C22 4.4 20 2.4 15.2 2.4Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M12 15.6C14.2091 15.6 16 13.8091 16 11.6C16 9.39086 14.2091 7.6 12 7.6C9.79086 7.6 8 9.39086 8 11.6C8 13.8091 9.79086 15.6 12 15.6Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      ),
      color: 'bg-blue-500',
      theme: 'webcam-theme',
      route: '/tools/webcam-scanner'
    },
    {
      id: 'voice',
      title: 'Voice Analysis',
      description: 'Analyze your voice patterns to detect potential breath and oral health concerns',
      icon: (
        <svg className="w-12 h-12 text-purple-400" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 15.5C14.21 15.5 16 13.71 16 11.5V6C16 3.79 14.21 2 12 2C9.79 2 8 3.79 8 6V11.5C8 13.71 9.79 15.5 12 15.5Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M4.35 9.65V11.35C4.35 15.57 7.78 19 12 19C16.22 19 19.65 15.57 19.65 11.35V9.65" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M12 19V22" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      ),
      color: 'bg-purple-500',
      theme: 'voice-theme',
      route: '/tools/voice-analysis'
    },
    {
      id: 'thermal',
      title: 'Thermal Imaging',
      description: 'Detect inflammation and infections using thermal imaging technology',
      icon: (
        <svg className="w-12 h-12 text-orange-400" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M12 18C15.3137 18 18 15.3137 18 12C18 8.68629 15.3137 6 12 6C8.68629 6 6 8.68629 6 12C6 15.3137 8.68629 18 12 18Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M12 14C13.1046 14 14 13.1046 14 12C14 10.8954 13.1046 10 12 10C10.8954 10 10 10.8954 10 12C10 13.1046 10.8954 14 12 14Z" fill="currentColor"/>
        </svg>
      ),
      color: 'bg-orange-500',
      theme: 'thermal-theme',
      route: '/tools/thermal-imaging'
    },
    {
      id: 'breath',
      title: 'Breath Analysis',
      description: 'Evaluate your breath health with AI-powered breath analysis',
      icon: (
        <svg className="w-12 h-12 text-cyan-400" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M8.5 10C10.433 10 12 8.433 12 6.5C12 4.567 10.433 3 8.5 3C6.567 3 5 4.567 5 6.5C5 8.433 6.567 10 8.5 10Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M19 21L15.5 17.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M15.5 21L19 17.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M2 21.4V18C2 15.6 3.9 13.7 6.3 13.7C7.1 13.7 7.9 13.9 8.5 14.3" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      ),
      color: 'bg-cyan-500',
      theme: 'breath-theme',
      route: '/tools/breath-analysis'
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100
      }
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-navy-900 via-navy-800 to-navy-900 py-12 px-4 sm:px-6 lg:px-8" ref={containerRef}>
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="text-center mb-16"
      >
        <h1 className="text-5xl font-bold gradient-text bg-gradient-to-r from-blue-400 via-purple-400 to-cyan-400 bg-clip-text text-transparent mb-6">
          DenTech AI Tools
        </h1>
        <p className="text-xl text-gray-300 max-w-3xl mx-auto">
          Cutting-edge AI-powered dental technology tools to monitor and improve your oral health,
          all accessible through your device.
        </p>
      </motion.div>

      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="max-w-7xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-8 px-4"
      >
        {tools.map((tool) => (
          <motion.div
            key={tool.id}
            variants={itemVariants}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className={`relative group ${tool.theme}`}
          >
            <Link
              to={tool.route}
              className="block h-full"
            >
              <div className="tool-card glow relative overflow-hidden rounded-2xl bg-navy-700 shadow-xl transition-all duration-300">
                <div className="absolute inset-0 bg-gradient-to-br from-navy-800 to-navy-900 opacity-90"></div>
                <div className="relative p-8">
                  <div className={`icon-container inline-flex items-center justify-center w-16 h-16 rounded-xl ${tool.color} bg-opacity-10 mb-6`}>
                    {tool.icon}
                  </div>
                  <h3 className="text-2xl font-semibold text-white mb-4 group-hover:text-blue-400 transition-colors">
                    {tool.title}
                  </h3>
                  <p className="text-gray-300 text-lg">
                    {tool.description}
                  </p>
                  <div className="mt-8 flex items-center text-blue-400 group-hover:text-blue-300 transition-colors">
                    <span className="font-medium">Launch Tool</span>
                    <svg className="w-5 h-5 ml-2 transform group-hover:translate-x-1 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  </div>
                </div>
                <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-blue-500 to-transparent opacity-0 group-hover:opacity-100 transition-opacity"></div>
              </div>
            </Link>
          </motion.div>
        ))}
      </motion.div>

      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1, duration: 0.6 }}
        className="mt-16 text-center"
      >
        <p className="text-gray-400 text-sm">
          All tools use advanced AI algorithms and are regularly updated for accuracy
        </p>
      </motion.div>
    </div>
  );
};

export default AITools; 