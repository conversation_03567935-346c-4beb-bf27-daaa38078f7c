import React, { useState, useEffect } from 'react';
import { detectBrowser, detectFeatures, generateCompatibilityReport } from '../utils/browserCompatibility';

const BrowserCompatibilityPage = () => {
  const [report, setReport] = useState(null);
  const [showAllFeatures, setShowAllFeatures] = useState(false);
  const [testResults, setTestResults] = useState({});
  const [runningTests, setRunningTests] = useState(false);

  useEffect(() => {
    // Generate compatibility report on component mount
    setReport(generateCompatibilityReport());
  }, []);

  // Run visual rendering tests
  const runVisualTests = () => {
    setRunningTests(true);
    const results = {};

    // Test CSS Grid
    try {
      const gridTest = document.createElement('div');
      gridTest.style.display = 'grid';
      gridTest.style.gridTemplateColumns = '1fr 1fr';
      results.grid = gridTest.style.gridTemplateColumns ? 'pass' : 'fail';
    } catch (e) {
      results.grid = 'error';
    }

    // Test Flexbox
    try {
      const flexTest = document.createElement('div');
      flexTest.style.display = 'flex';
      flexTest.style.justifyContent = 'center';
      results.flexbox = flexTest.style.justifyContent ? 'pass' : 'fail';
    } catch (e) {
      results.flexbox = 'error';
    }

    // Test CSS Variables
    try {
      const root = document.documentElement;
      const testValue = 'test-value';
      root.style.setProperty('--test-var', testValue);
      const computed = getComputedStyle(root).getPropertyValue('--test-var').trim();
      results.cssVariables = computed === testValue ? 'pass' : 'fail';
      root.style.removeProperty('--test-var');
    } catch (e) {
      results.cssVariables = 'error';
    }

    // Test CSS Animations
    try {
      const animTest = document.createElement('div');
      animTest.style.animation = 'test 1s';
      results.cssAnimations = animTest.style.animation ? 'pass' : 'fail';
    } catch (e) {
      results.cssAnimations = 'error';
    }

    // Test WebGL
    try {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      results.webgl = gl ? 'pass' : 'fail';
    } catch (e) {
      results.webgl = 'error';
    }

    // Test Local Storage
    try {
      localStorage.setItem('test', 'test');
      localStorage.removeItem('test');
      results.localStorage = 'pass';
    } catch (e) {
      results.localStorage = 'error';
    }

    // Test Session Storage
    try {
      sessionStorage.setItem('test', 'test');
      sessionStorage.removeItem('test');
      results.sessionStorage = 'pass';
    } catch (e) {
      results.sessionStorage = 'error';
    }

    // Test Fetch API
    fetch('https://jsonplaceholder.typicode.com/todos/1')
      .then(response => response.json())
      .then(() => {
        setTestResults(prev => ({ ...prev, fetch: 'pass' }));
      })
      .catch(() => {
        setTestResults(prev => ({ ...prev, fetch: 'fail' }));
      });

    setTestResults(results);
    setRunningTests(false);
  };

  if (!report) {
    return <div className="p-8 text-center">Loading browser compatibility report...</div>;
  }

  const { browser, features, knownIssues, meetsMinimumRequirements } = report;
  const criticalFeatures = ['promises', 'querySelector', 'classList', 'localStorage', 'flexbox'];
  
  return (
    <div className="container mx-auto p-4 max-w-4xl">
      <h1 className="text-3xl font-bold mb-6">Browser Compatibility Diagnostics</h1>
      
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Browser Information</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p><strong>Browser:</strong> {browser.name}</p>
            <p><strong>Version:</strong> {browser.version}</p>
            <p><strong>Operating System:</strong> {browser.os}</p>
            <p><strong>Mobile Device:</strong> {browser.mobile ? 'Yes' : 'No'}</p>
          </div>
          <div>
            <p><strong>User Agent:</strong></p>
            <p className="text-xs break-all bg-gray-100 p-2 rounded">
              {navigator.userAgent}
            </p>
          </div>
        </div>
      </div>

      {knownIssues.length > 0 && (
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">
                Known Compatibility Issues
              </h3>
              <div className="mt-2 text-sm text-yellow-700">
                <ul className="list-disc pl-5 space-y-1">
                  {knownIssues.map((issue, index) => (
                    <li key={index}>{issue}</li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Feature Support</h2>
          <button 
            onClick={() => setShowAllFeatures(!showAllFeatures)}
            className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm"
          >
            {showAllFeatures ? 'Show Critical Only' : 'Show All Features'}
          </button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Object.entries(features)
            .filter(([key]) => showAllFeatures || criticalFeatures.includes(key))
            .map(([key, supported]) => (
              <div 
                key={key} 
                className={`p-3 rounded flex justify-between items-center ${
                  supported ? 'bg-green-50' : 'bg-red-50'
                }`}
              >
                <span className="font-medium">{key}</span>
                <span className={`px-2 py-1 rounded text-xs font-bold ${
                  supported ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {supported ? 'Supported' : 'Not Supported'}
                </span>
              </div>
            ))}
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Visual Rendering Tests</h2>
          <button 
            onClick={runVisualTests}
            disabled={runningTests}
            className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm disabled:bg-blue-300"
          >
            {runningTests ? 'Running Tests...' : 'Run Tests'}
          </button>
        </div>
        
        {Object.keys(testResults).length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {Object.entries(testResults).map(([test, result]) => (
              <div 
                key={test} 
                className={`p-3 rounded flex justify-between items-center ${
                  result === 'pass' ? 'bg-green-50' : result === 'fail' ? 'bg-red-50' : 'bg-yellow-50'
                }`}
              >
                <span className="font-medium">{test}</span>
                <span className={`px-2 py-1 rounded text-xs font-bold ${
                  result === 'pass' ? 'bg-green-100 text-green-800' : 
                  result === 'fail' ? 'bg-red-100 text-red-800' : 
                  'bg-yellow-100 text-yellow-800'
                }`}>
                  {result.toUpperCase()}
                </span>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-gray-500 italic">Click "Run Tests" to check visual rendering capabilities</p>
        )}
      </div>

      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4">Compatibility Summary</h2>
        <div className={`p-4 rounded ${meetsMinimumRequirements ? 'bg-green-50' : 'bg-red-50'}`}>
          <p className="font-medium">
            {meetsMinimumRequirements 
              ? 'Your browser meets the minimum requirements for this application.' 
              : 'Your browser does not meet the minimum requirements for this application.'}
          </p>
          {!meetsMinimumRequirements && (
            <div className="mt-2">
              <p className="font-medium">Recommended browsers:</p>
              <ul className="list-disc pl-5 mt-1">
                <li>Google Chrome (latest version)</li>
                <li>Mozilla Firefox (latest version)</li>
                <li>Microsoft Edge (latest version)</li>
                <li>Safari (version 14 or newer)</li>
              </ul>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default BrowserCompatibilityPage;
