import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { NONPROFIT_ORGANIZATIONS } from '../lib/constants/nonProfitOrganizations';

export default function NonProfitOrganizations() {
  const [activeTab, setActiveTab] = useState('types');
  
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };
  
  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 10
      }
    }
  };

  // Render icon based on icon name
  const renderIcon = (iconName) => {
    switch(iconName) {
      case 'heart':
        return (
          <svg className="w-8 h-8 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
          </svg>
        );
      case 'tooth':
        return (
          <svg className="w-8 h-8 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14zm-4 6v-7.5l4-2.222" />
          </svg>
        );
      case 'hands':
        return (
          <svg className="w-8 h-8 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M7 11.5V14m0-2.5v-6a1.5 1.5 0 113 0m-3 6a1.5 1.5 0 00-3 0v2a7.5 7.5 0 0015 0v-5a1.5 1.5 0 00-3 0m-6-3V11m0-5.5v-1a1.5 1.5 0 013 0v1m0 0V11m0-5.5a1.5 1.5 0 013 0v3m0 0V11" />
          </svg>
        );
      default:
        return (
          <svg className="w-8 h-8 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
    }
  };

  // Render tab content based on active tab
  const renderTabContent = () => {
    switch(activeTab) {
      case 'types':
        return (
          <div className="space-y-8">
            {NONPROFIT_ORGANIZATIONS.organizationTypes.map((type, index) => (
              <motion.div 
                key={index}
                className="bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <h3 className="text-xl font-semibold text-white mb-2">{type.type}</h3>
                <p className="text-white/70 mb-4">{type.description}</p>
                
                {type.examples && type.examples.length > 0 && (
                  <div className="mt-4">
                    <h4 className="text-lg font-medium text-white mb-2">Examples</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {type.examples.map((example, i) => (
                        <div key={i} className="bg-white/10 rounded-lg p-4">
                          <h5 className="font-medium text-white mb-1">{example.name}</h5>
                          <p className="text-white/70 text-sm mb-2">{example.description}</p>
                          <a 
                            href={example.website} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="text-blue-400 hover:text-blue-300 text-sm inline-flex items-center"
                          >
                            Visit Website
                            <svg className="w-3 h-3 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                            </svg>
                          </a>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </motion.div>
            ))}
          </div>
        );
      case 'national':
        return (
          <div className="space-y-6">
            {NONPROFIT_ORGANIZATIONS.nationalOrganizations.map((org, index) => (
              <motion.div 
                key={index}
                className="bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <h3 className="text-xl font-semibold text-white mb-2">{org.name}</h3>
                <p className="text-white/70 mb-3">{org.description}</p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <h4 className="text-sm font-medium text-white/90 mb-1">Eligibility</h4>
                    <p className="text-white/70">{org.eligibility}</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-white/90 mb-1">Contact</h4>
                    <p className="text-white/70">{org.phone}</p>
                  </div>
                </div>
                <a 
                  href={org.website} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-blue-400 hover:text-blue-300 inline-flex items-center"
                >
                  Visit Website
                  <svg className="w-4 h-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                </a>
              </motion.div>
            ))}
          </div>
        );
      case 'resources':
        return (
          <div className="bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10">
            <h3 className="text-xl font-semibold text-white mb-4">Finding Help Near You</h3>
            <p className="text-white/70 mb-6">
              Use these resources to find non-profit dental organizations and free or low-cost dental care in your area.
            </p>
            <ul className="space-y-4">
              {NONPROFIT_ORGANIZATIONS.resources.map((resource, index) => (
                <motion.li 
                  key={index} 
                  className="flex items-start"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <div className="flex-shrink-0 h-6 w-6 rounded-full bg-blue-500/20 flex items-center justify-center mr-3 mt-0.5">
                    <svg className="h-4 w-4 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="text-white font-medium">{resource.name}</h4>
                    <p className="text-white/70">{resource.description}</p>
                    <a href={resource.link} target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:text-blue-300">
                      Visit Website
                    </a>
                  </div>
                </motion.li>
              ))}
            </ul>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="py-16 px-4 sm:px-6 lg:px-8 relative">
      {/* Background decorations */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        <div className="absolute top-0 right-0 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 left-20 w-80 h-80 bg-indigo-500/5 rounded-full blur-3xl"></div>
      </div>

      <motion.div 
        className="max-w-7xl mx-auto relative"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Hero Section */}
        <motion.div 
          className="mb-12 text-center"
          variants={itemVariants}
        >
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-blue-400 via-sky-400 to-indigo-400 text-transparent bg-clip-text mb-4">
            Non-Profit Organizations
          </h1>
          <p className="text-xl text-white/80 max-w-3xl mx-auto">
            {NONPROFIT_ORGANIZATIONS.description}
          </p>
        </motion.div>

        {/* Featured Organizations Section */}
        <motion.div 
          className="mb-16"
          variants={itemVariants}
        >
          <h2 className="text-2xl font-bold text-white mb-8 text-center">Featured Organizations</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {NONPROFIT_ORGANIZATIONS.featuredOrganizations.map((org, index) => (
              <motion.div 
                key={index}
                className="bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10 hover:border-blue-500/30 transition-all duration-300"
                whileHover={{ y: -5 }}
                transition={{ type: "spring", stiffness: 300, damping: 10 }}
              >
                <div className="mb-4">
                  {renderIcon(org.icon)}
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">{org.name}</h3>
                <p className="text-white/70 mb-4">{org.description}</p>
                <a 
                  href={org.link} 
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-400 hover:text-blue-300 inline-flex items-center"
                >
                  Visit Website
                  <svg className="w-4 h-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                </a>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Tabs Section */}
        <motion.div 
          className="mb-16"
          variants={itemVariants}
        >
          <div className="flex flex-wrap justify-center mb-8 border-b border-white/10">
            <button
              className={`px-4 py-2 font-medium text-sm transition-colors ${
                activeTab === 'types' 
                  ? 'text-white border-b-2 border-blue-500' 
                  : 'text-white/60 hover:text-white'
              }`}
              onClick={() => setActiveTab('types')}
            >
              Types of Organizations
            </button>
            <button
              className={`px-4 py-2 font-medium text-sm transition-colors ${
                activeTab === 'national' 
                  ? 'text-white border-b-2 border-blue-500' 
                  : 'text-white/60 hover:text-white'
              }`}
              onClick={() => setActiveTab('national')}
            >
              National Organizations
            </button>
            <button
              className={`px-4 py-2 font-medium text-sm transition-colors ${
                activeTab === 'resources' 
                  ? 'text-white border-b-2 border-blue-500' 
                  : 'text-white/60 hover:text-white'
              }`}
              onClick={() => setActiveTab('resources')}
            >
              Find Help Near You
            </button>
          </div>
          
          {renderTabContent()}
        </motion.div>

        {/* CTA Section */}
        <motion.div 
          className="mt-12 text-center"
          variants={itemVariants}
        >
          <h2 className="text-2xl font-bold text-white mb-4">Explore More Affordable Care Options</h2>
          <p className="text-white/80 mb-6">
            Discover other ways to make dental care more affordable for you and your family.
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <Link 
              to="/state-dental-programs" 
              className="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition"
            >
              State Dental Programs
            </Link>
            <Link 
              to="/dental-insurance-plans" 
              className="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition"
            >
              Dental Insurance Plans
            </Link>
            <Link 
              to="/payment-plans" 
              className="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition"
            >
              Payment Plans
            </Link>
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
}
