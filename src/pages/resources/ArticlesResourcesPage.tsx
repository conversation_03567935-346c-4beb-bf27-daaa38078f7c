import React, { useState } from 'react';
import DynamicArticleList from '../../components/resources/DynamicArticleList';
import { motion } from 'framer-motion';
import NutritionGuidance from '../../components/resources/NutritionGuidance';

const ArticlesResourcesPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'articles' | 'nutrition'>('articles');

  return (
    <div className="min-h-screen bg-gradient-to-b from-indigo-900 via-blue-900 to-indigo-800 pb-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-12">
        {/* Header */}
        <div className="text-center mb-12">
          <motion.h1 
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-4xl md:text-5xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-300 to-purple-400"
          >
            Dental Resources & Knowledge Hub
          </motion.h1>
          <motion.p 
            initial={{ opacity: 0, y: -5 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="mt-3 text-xl text-blue-100 max-w-3xl mx-auto"
          >
            Stay informed with evidence-based research, dental news, and nutrition guidance
          </motion.p>
        </div>

        {/* Tab Navigation */}
        <div className="flex justify-center mb-8">
          <div className="inline-flex rounded-lg p-1 bg-white/5 backdrop-blur-sm">
            <button
              onClick={() => setActiveTab('articles')}
              className={`px-6 py-2.5 rounded-md text-sm font-medium transition-all ${
                activeTab === 'articles'
                  ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg'
                  : 'text-blue-100 hover:text-white'
              }`}
            >
              Research & Articles
            </button>
            <button
              onClick={() => setActiveTab('nutrition')}
              className={`px-6 py-2.5 rounded-md text-sm font-medium transition-all ${
                activeTab === 'nutrition'
                  ? 'bg-gradient-to-r from-emerald-500 to-teal-600 text-white shadow-lg'
                  : 'text-blue-100 hover:text-white'
              }`}
            >
              Nutrition & Oral Health
            </button>
          </div>
        </div>

        {/* Content Container */}
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
          className="bg-white/10 backdrop-blur-md rounded-2xl p-6 md:p-8 border border-white/20 shadow-xl"
        >
          {activeTab === 'articles' ? (
            <DynamicArticleList />
          ) : (
            <NutritionGuidance />
          )}
        </motion.div>
      </div>
    </div>
  );
};

export default ArticlesResourcesPage; 