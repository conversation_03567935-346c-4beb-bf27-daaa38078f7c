import React, { useState } from 'react';
import { motion } from 'framer-motion';
import DentalSchoolsSection from '../components/resources/DentalSchoolsSection';
import CombinedDentalLocationsMap from '../components/resources/CombinedDentalLocationsMap';
import { MentorBiographiesSection } from '../components/mentors';
import { DENTAL_OFFICES } from '../lib/constants/dentalOffices';
import { Card } from '../components/common';

export default function AffordableCare() {
  const [showCombinedMap, setShowCombinedMap] = useState(true);
  
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 10
      }
    }
  };

  const handleToggleView = () => {
    setShowCombinedMap(!showCombinedMap);
  };

  return (
    <div className="py-16 px-4 sm:px-6 lg:px-8 relative bg-indigo-950 min-h-screen">
      {/* Background decorations */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        <div className="absolute top-0 right-0 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 left-20 w-80 h-80 bg-indigo-500/5 rounded-full blur-3xl"></div>
      </div>

      <motion.div 
        className="max-w-6xl mx-auto relative"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <motion.div 
          className="mb-12 text-center"
          variants={itemVariants}
        >
          <h1 className="text-6xl md:text-7xl font-bold text-blue-300 mb-4">
            Find Affordable Dental Care
          </h1>
          <p className="text-xl text-white/80 max-w-3xl mx-auto">
            Discover dental schools, community health centers, and highlighted dentists
            offering quality dental care at various locations.
          </p>
        </motion.div>
        
        <motion.div 
          className="mb-8"
          variants={itemVariants}
        >
          <Card className="p-4 bg-indigo-900/40">
            <div className="flex flex-col sm:flex-row justify-between items-center">
              <div>
                <h2 className="text-xl font-bold text-white">Featured Dental Locations</h2>
                <p className="text-indigo-200 text-sm mt-1">View dental schools and featured dental offices</p>
              </div>
              <button 
                onClick={handleToggleView}
                className="mt-4 sm:mt-0 px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition flex items-center space-x-2"
              >
                <span>{showCombinedMap ? "Show Only Dental Schools" : "Show Combined Map"}</span>
                <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              </button>
            </div>
          </Card>
        </motion.div>

        <motion.div variants={itemVariants}>
          {showCombinedMap ? (
            <CombinedDentalLocationsMap customOffices={DENTAL_OFFICES} />
          ) : (
            <DentalSchoolsSection />
          )}
        </motion.div>
        
        <motion.div 
          className="mt-12"
          variants={itemVariants}
        >
          <MentorBiographiesSection />
        </motion.div>

        <motion.div 
          className="mt-12 text-center"
          variants={itemVariants}
        >
          <h2 className="text-2xl font-bold text-white mb-4">Need More Help?</h2>
          <p className="text-white/80 mb-6">
            Speak with our AI assistant to get personalized recommendations
            for affordable dental care options in your area.
          </p>
          <a 
            href="/chat" 
            className="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition"
          >
            Chat With Our Assistant
            <svg className="w-5 h-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
            </svg>
          </a>
        </motion.div>
      </motion.div>
    </div>
  );
}
