import React, { useState } from 'react';
import { Link, useNavigate, useParams } from 'react-router-dom';

const StandaloneSignIn: React.FC = () => {
  // Get the user type from URL parameters
  const { userType = 'patient' } = useParams<{ userType: string }>();
  const isPatient = userType === 'patient';

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setIsLoading(true);

    try {
      // Log the attempt for debugging
      console.log(`Attempting to sign in as ${userType} with email: ${email}`);
      
      // This would be replaced with actual API call in a real app
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Simulate successful login and redirect
      if (isPatient) {
        console.log('Navigating to patient dashboard');
        navigate('/dashboards/patient');
      } else {
        console.log('Navigating to practice dashboard');
        navigate('/dashboards/practice');
      }
    } catch (err) {
      console.error('Sign in error:', err);
      setError('Invalid email or password. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-gray-900 to-gray-800 p-4">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-md overflow-hidden">
        {/* Header with gradient background */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-8 text-white text-center">
          <div className="mx-auto w-16 h-16 bg-white rounded-full flex items-center justify-center mb-4">
            <svg className="h-10 w-10 text-blue-600" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 15h-2v-6h2v6zm4 0h-2v-6h2v6zm-6-8H7V7h2v2zm4 0h-2V7h2v2zm4 0h-2V7h2v2z" />
            </svg>
          </div>
          <h1 className="text-2xl font-bold">
            {isPatient ? 'Patient Sign In' : 'Practice Sign In'}
          </h1>
          <p className="mt-2 text-blue-100">
            {isPatient 
              ? 'Access your appointments and dental care' 
              : 'Manage your practice and patient leads'}
          </p>
        </div>
        
        {/* Sign in form */}
        <div className="px-6 py-8">
          {error && (
            <div className="mb-4 bg-red-50 text-red-600 px-4 py-3 rounded-lg text-sm">
              {error}
            </div>
          )}
          
          <form onSubmit={handleSubmit}>
            <div className="mb-4">
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                Email Address
              </label>
              <input
                id="email"
                name="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="<EMAIL>"
                required
                autoFocus
                autoComplete="email"
              />
            </div>
            
            <div className="mb-6">
              <div className="flex items-center justify-between mb-1">
                <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                  Password
                </label>
                <a href="#" className="text-xs text-blue-600 hover:text-blue-800">
                  Forgot password?
                </a>
              </div>
              <input
                id="password"
                name="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="••••••••"
                required
                autoComplete="current-password"
              />
            </div>
            
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center">
                <input
                  id="rememberMe"
                  name="rememberMe"
                  type="checkbox"
                  checked={rememberMe}
                  onChange={(e) => setRememberMe(e.target.checked)}
                  className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <label htmlFor="rememberMe" className="ml-2 block text-sm text-gray-700">
                  Remember me
                </label>
              </div>
            </div>
            
            <button
              type="submit"
              disabled={isLoading}
              className={`w-full py-3 px-4 rounded-lg text-white font-medium ${
                isLoading ? 'bg-blue-400' : 'bg-blue-600 hover:bg-blue-700'
              } transition-colors duration-200`}
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Signing in...
                </div>
              ) : (
                'Sign In'
              )}
            </button>
          </form>
          
          <div className="mt-6 text-center">
            <p className="text-sm text-gray-600">
              Don't have an account?{' '}
              <Link to="/auth/register" className="text-blue-600 hover:text-blue-800 font-medium">
                Sign up
              </Link>
            </p>
          </div>
        </div>
        
        {/* Footer with user type toggle */}
        <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
          <div className="text-center">
            <p className="text-sm text-gray-600">
              {isPatient 
                ? 'Are you a dental practice?' 
                : 'Are you a patient?'}
              {' '}
              <Link 
                to={isPatient ? '/auth/sign-in/practice' : '/auth/sign-in/patient'} 
                className="text-blue-600 hover:text-blue-800 font-medium"
              >
                {isPatient ? 'Sign in as practice' : 'Sign in as patient'}
              </Link>
            </p>
          </div>
        </div>
        
        {/* Back to selection */}
        <div className="px-6 py-4 text-center">
          <Link to="/auth/user-type" className="text-sm text-gray-500 hover:text-gray-700">
            ← Back to user selection
          </Link>
        </div>
      </div>
    </div>
  );
};

export default StandaloneSignIn; 