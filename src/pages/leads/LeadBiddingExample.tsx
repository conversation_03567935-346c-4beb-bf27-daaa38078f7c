import React from 'react';
import LeadBiddingMarketplace, { Lead } from '../../components/leads/LeadBiddingMarketplace';

// Sample data for the marketplace
const sampleLeads: Lead[] = [
  {
    id: 'lead-001',
    patientNeeds: 'Wisdom Tooth Extraction',
    urgency: 'High',
    insurance: 'Cigna PPO',
    estimatedValue: 680,
    location: 'Chicago, IL',
    postedDate: '2 hours ago'
  },
  {
    id: 'lead-002',
    patientNeeds: 'Full Mouth Reconstruction',
    urgency: 'Medium',
    insurance: 'Aetna',
    estimatedValue: 3200,
    location: 'Austin, TX',
    postedDate: '1 day ago'
  },
  {
    id: 'lead-003',
    patientNeeds: 'Invisalign Treatment',
    urgency: 'Low',
    insurance: 'Delta Dental',
    estimatedValue: 1500,
    location: 'Seattle, WA',
    postedDate: '3 days ago'
  },
  {
    id: 'lead-004',
    patientNeeds: 'Root Canal Therapy',
    urgency: 'High',
    insurance: 'MetLife',
    estimatedValue: 750,
    location: 'Miami, FL',
    postedDate: '4 hours ago'
  },
  {
    id: 'lead-005',
    patientNeeds: 'Dental Implant Consultation',
    urgency: 'Medium',
    insurance: 'United Healthcare',
    estimatedValue: 420,
    location: 'Denver, CO',
    postedDate: '2 days ago'
  }
];

const LeadBiddingExample: React.FC = () => {
  // Handler for bid submission
  const handleBidSubmit = async (leadId: string, bidAmount: number): Promise<boolean> => {
    console.log(`Bid submitted for lead ${leadId}: $${bidAmount}`);
    
    // Simulate API call with a delay
    return new Promise((resolve) => {
      setTimeout(() => {
        // Successful bid (in a real app, this would be based on API response)
        resolve(true);
      }, 1500);
    });
  };

  return (
    <div className="bg-gray-50 min-h-screen">
      {/* Header */}
      <header className="bg-gradient-to-r from-indigo-700 to-blue-600 shadow-md">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex justify-between items-center">
            <h1 className="text-white text-3xl font-bold">Smilo.Dental</h1>
            <div className="flex space-x-4">
              <button className="bg-white text-indigo-600 px-4 py-2 rounded-md shadow-sm font-medium hover:bg-indigo-50 transition-colors">
                Dashboard
              </button>
              <button className="bg-indigo-800 text-white px-4 py-2 rounded-md shadow-sm font-medium hover:bg-indigo-900 transition-colors">
                My Practice
              </button>
            </div>
          </div>
        </div>
      </header>
      
      {/* Main content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumbs */}
        <nav className="mb-6">
          <ol className="flex text-sm text-gray-500">
            <li className="flex items-center">
              <a href="#" className="hover:text-indigo-600">Dashboard</a>
              <svg className="h-4 w-4 mx-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
            </li>
            <li className="flex items-center">
              <a href="#" className="hover:text-indigo-600">Patient Acquisition</a>
              <svg className="h-4 w-4 mx-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
            </li>
            <li className="text-indigo-600 font-medium">Lead Marketplace</li>
          </ol>
        </nav>
        
        {/* Page Title */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900">Patient Lead Marketplace</h2>
          <p className="mt-2 text-gray-600">Find new patients looking for dental services in your area. Place bids on leads to connect with potential patients.</p>
        </div>
        
        {/* Lead Marketplace */}
        <div className="mb-8">
          <LeadBiddingMarketplace 
            leads={sampleLeads} 
            onBidSubmit={handleBidSubmit}
          />
        </div>
        
        {/* Additional Information */}
        <div className="bg-white p-6 rounded-xl shadow-md mt-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">How the Marketplace Works</h3>
          <div className="grid md:grid-cols-3 gap-6">
            <div className="bg-indigo-50 p-4 rounded-lg">
              <div className="flex items-center mb-3">
                <div className="bg-indigo-600 rounded-full w-8 h-8 flex items-center justify-center text-white font-bold mr-3">1</div>
                <h4 className="font-medium text-indigo-900">Review Available Leads</h4>
              </div>
              <p className="text-sm text-gray-600">Browse through patient leads that match your practice's services and location.</p>
            </div>
            <div className="bg-indigo-50 p-4 rounded-lg">
              <div className="flex items-center mb-3">
                <div className="bg-indigo-600 rounded-full w-8 h-8 flex items-center justify-center text-white font-bold mr-3">2</div>
                <h4 className="font-medium text-indigo-900">Place Your Bid</h4>
              </div>
              <p className="text-sm text-gray-600">Enter a competitive bid amount based on the estimated value of the treatment.</p>
            </div>
            <div className="bg-indigo-50 p-4 rounded-lg">
              <div className="flex items-center mb-3">
                <div className="bg-indigo-600 rounded-full w-8 h-8 flex items-center justify-center text-white font-bold mr-3">3</div>
                <h4 className="font-medium text-indigo-900">Connect With Patients</h4>
              </div>
              <p className="text-sm text-gray-600">If your bid is accepted, you'll receive the patient's contact information to schedule an appointment.</p>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default LeadBiddingExample; 