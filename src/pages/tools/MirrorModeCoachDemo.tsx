import React from 'react';
import { MirrorModeCoach } from '../../components/ai-tools';

const MirrorModeCoachDemo: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-900 to-gray-800 py-12 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-white mb-4">
            Smart Mirror Brushing Coach
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            AI-powered brushing guidance using your webcam. Track coverage, get real-time feedback, and improve your dental hygiene.
          </p>
        </div>
        
        <MirrorModeCoach />
        
        <div className="mt-16 bg-gray-800/50 rounded-xl p-8 border border-gray-700">
          <h2 className="text-2xl font-bold text-white mb-6">How It Works</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-gray-800 p-6 rounded-lg">
              <div className="w-12 h-12 bg-cyan-900/50 rounded-full flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-cyan-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">Face Detection</h3>
              <p className="text-gray-300">
                Our AI uses TensorFlow.js and face landmark detection to identify and track your face and mouth regions in real-time.
              </p>
            </div>
            
            <div className="bg-gray-800 p-6 rounded-lg">
              <div className="w-12 h-12 bg-cyan-900/50 rounded-full flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-cyan-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">Brushing Coverage</h3>
              <p className="text-gray-300">
                The system tracks which areas of your mouth you've brushed and identifies missed spots that need more attention.
              </p>
            </div>
            
            <div className="bg-gray-800 p-6 rounded-lg">
              <div className="w-12 h-12 bg-cyan-900/50 rounded-full flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-cyan-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">Timer & Guidance</h3>
              <p className="text-gray-300">
                A built-in timer ensures you brush for the dentist-recommended 2 minutes, with guided prompts for each section of your mouth.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MirrorModeCoachDemo; 