import { Configuration, OpenAIApi } from 'openai';
import formidable from 'formidable';
import fs from 'fs';
import path from 'path';
import { voiceAnalysisService } from '../../lib/services/voiceAnalysisService';

// Configuration for OpenAI API
const configuration = new Configuration({
  apiKey: process.env.OPENAI_API_KEY,
});

const openai = new OpenAIApi(configuration);

// Disable body parser to handle form data with files
export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  console.log('Speech-to-text API called');

  try {
    // Create uploads directory if it doesn't exist
    const uploadsDir = path.join(process.cwd(), 'uploads');
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
      console.log('Created uploads directory:', uploadsDir);
    }

    // Configure formidable for file uploads
    const form = formidable({
      uploadDir: uploadsDir,
      keepExtensions: true,
      maxFileSize: 10 * 1024 * 1024, // 10MB max
      multiples: false,
    });

    console.log('Parsing form data...');

    // Parse the form
    const [fields, files] = await new Promise((resolve, reject) => {
      form.parse(req, (err, fields, files) => {
        if (err) {
          console.error('Form parsing error:', err);
          reject(err);
        } else {
          resolve([fields, files]);
        }
      });
    });

    // Get the audio file
    const audioFile = files.audio;
    if (!audioFile) {
      console.error('No audio file provided in request');
      return res.status(400).json({ error: 'No audio file provided' });
    }

    console.log(`Received audio file: ${audioFile.originalFilename || 'unnamed'}, size: ${audioFile.size} bytes`);

    // Path to the uploaded file
    const filePath = audioFile.filepath;
    let audioData = null;
    let transcriptionText = '';

    try {
      // Read the audio file for analysis
      try {
        const fileStats = fs.statSync(filePath);
        console.log(`File on disk: ${filePath}, size: ${fileStats.size} bytes`);
        
        if (fileStats.size === 0) {
          throw new Error('Audio file is empty');
        }
        
        // Read file for analysis (in production, you would process audio data properly)
        audioData = fs.readFileSync(filePath);
        console.log(`Read ${audioData.length} bytes of audio data`);
      } catch (readError) {
        console.error('Error reading audio file:', readError);
        throw new Error(`Could not read audio file: ${readError.message}`);
      }

      // Create an audio buffer from the data for analysis
      // Note: In a real implementation, we would properly decode and analyze the audio
      // For simplicity in this demo, we're simulating analysis
      const simulatedAudioData = new Float32Array(1024).fill(0.5);

      // Perform dental health analysis
      console.log('Performing dental health analysis...');
      const dentalAnalysis = {
        clarity: voiceAnalysisService.analyzeSpeechClarity(simulatedAudioData),
        dryMouth: voiceAnalysisService.analyzeDryMouth(simulatedAudioData),
        jawAlignment: voiceAnalysisService.analyzeJawAlignment(simulatedAudioData)
      };

      console.log('Dental analysis complete');

      // Create a read stream for OpenAI transcription
      const audioStream = fs.createReadStream(filePath);

      try {
        // Send to OpenAI Whisper API
        console.log('Sending audio to OpenAI for transcription...');
        const response = await openai.createTranscription(
          audioStream,
          'whisper-1'
        );
        transcriptionText = response.data.text;
        console.log('Transcription received:', transcriptionText);
      } catch (openaiError) {
        console.error('OpenAI API error:', openaiError);
        const errorMsg = openaiError.response?.data?.error?.message || openaiError.message;
        throw new Error(`Transcription failed: ${errorMsg}`);
      }

      // Clean up the temporary file
      try {
        fs.unlinkSync(filePath);
        console.log('Temporary audio file deleted');
      } catch (unlinkError) {
        console.error('Failed to delete temporary file:', unlinkError);
        // Non-critical error, continue execution
      }

      // Return transcribed text and analysis
      return res.status(200).json({
        text: transcriptionText,
        analysis: {
          clarity: {
            score: dentalAnalysis.clarity.score,
            issues: dentalAnalysis.clarity.issues,
            recommendation: dentalAnalysis.clarity.recommendation
          },
          dryMouth: {
            score: dentalAnalysis.dryMouth.score,
            severity: dentalAnalysis.dryMouth.severity,
            recommendation: dentalAnalysis.dryMouth.recommendation
          },
          jawAlignment: {
            score: dentalAnalysis.jawAlignment.score,
            issues: dentalAnalysis.jawAlignment.issues,
            recommendation: dentalAnalysis.jawAlignment.recommendation
          },
          overallHealth: calculateOverallHealth(dentalAnalysis)
        }
      });
    } catch (error) {
      // Clean up the temporary file even if there's an error
      if (filePath && fs.existsSync(filePath)) {
        try {
          fs.unlinkSync(filePath);
          console.log('Temporary audio file deleted after error');
        } catch (unlinkError) {
          console.error('Failed to delete temporary file after error:', unlinkError);
        }
      }
      throw error;
    }
  } catch (error) {
    console.error('Speech to text error:', error);
    return res.status(500).json({ 
      error: 'Speech to text conversion failed',
      details: error.message 
    });
  }
}

function calculateOverallHealth(analysis) {
  const weights = {
    clarity: 0.4,
    dryMouth: 0.3,
    jawAlignment: 0.3
  };

  const overallScore = 
    analysis.clarity.score * weights.clarity +
    analysis.dryMouth.score * weights.dryMouth +
    analysis.jawAlignment.score * weights.jawAlignment;

  let status;
  let recommendation;

  if (overallScore >= 0.8) {
    status = 'Good';
    recommendation = 'Your oral health indicators are good. Continue maintaining your current dental care routine.';
  } else if (overallScore >= 0.6) {
    status = 'Fair';
    recommendation = 'Some minor issues detected. Consider scheduling a routine dental check-up.';
  } else {
    status = 'Needs Attention';
    recommendation = 'Several potential issues detected. We recommend consulting a dental professional for a thorough evaluation.';
  }

  return {
    score: overallScore,
    status,
    recommendation
  };
} 