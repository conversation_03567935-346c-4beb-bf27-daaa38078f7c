import { createClient } from '@supabase/supabase-js';
import nodemailer from 'nodemailer';

// Initialize Supabase client
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Configure email transporter
const transporter = nodemailer.createTransport({
  host: import.meta.env.VITE_EMAIL_HOST,
  port: import.meta.env.VITE_EMAIL_PORT,
  secure: import.meta.env.VITE_EMAIL_SECURE === 'true',
  auth: {
    user: import.meta.env.VITE_EMAIL_USER,
    pass: import.meta.env.VITE_EMAIL_PASS,
  },
});

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const { agreementId, dentistName, email, location, ipAddress } = req.body;

    if (!agreementId || !dentistName || !email) {
      return res.status(400).json({ message: 'Missing required fields' });
    }

    // Get admin emails from environment variable or database
    const adminEmails = import.meta.env.VITE_ADMIN_EMAILS?.split(',') || ['<EMAIL>'];

    // Send notification email
    await transporter.sendMail({
      from: `"Smilo Dental NDA System" <${import.meta.env.VITE_EMAIL_FROM || '<EMAIL>'}>`,
      to: adminEmails.join(','),
      subject: `🔒 New NDA Submission - ${dentistName}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
          <h2 style="color: #2563eb; margin-top: 0;">New NDA Submission Received</h2>
          <p>A new NDA submission has been received and is pending review:</p>
          
          <div style="background-color: #f3f4f6; padding: 15px; border-radius: 5px; margin: 15px 0;">
            <p><strong>Agreement ID:</strong> ${agreementId}</p>
            <p><strong>Dentist Name:</strong> ${dentistName}</p>
            <p><strong>Email:</strong> ${email}</p>
            ${location ? `<p><strong>Location:</strong> ${location}</p>` : ''}
            ${ipAddress ? `<p><strong>IP Address:</strong> ${ipAddress}</p>` : ''}
            <p><strong>Submission Date:</strong> ${new Date().toLocaleString()}</p>
          </div>
          
          <p>Please review this submission in the admin dashboard:</p>
          <a href="${import.meta.env.VITE_SITE_URL || 'https://smilo.dental'}/admin/nda/${agreementId}" 
             style="display: inline-block; background-color: #2563eb; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;">
            Review Submission
          </a>
          
          <p style="margin-top: 20px; font-size: 12px; color: #6b7280;">
            This is an automated message from the Smilo Dental NDA System.
          </p>
        </div>
      `,
    });

    // Try to log the notification, but continue if it fails
    try {
      // Check if nda_notifications table exists using direct query approach
      try {
        const { data, error } = await supabase
          .from('nda_notifications')
          .select('id')
          .limit(1);
        
        // If no error, table exists, so insert the notification
        if (!error) {
          await supabase
            .from('nda_notifications')
            .insert([
              {
                agreement_id: agreementId,
                recipient_emails: adminEmails.join(','),
                notification_type: 'admin_submission_alert',
                sent_at: new Date().toISOString(),
                dentist_name: dentistName,
                dentist_email: email,
                ip_address: ipAddress,
                location: location
              }
            ]);
          console.log('Notification logged successfully');
        } else {
          console.warn('nda_notifications table does not exist - skipping notification logging');
        }
      } catch (tableError) {
        console.warn('Unable to check nda_notifications table - skipping notification logging');
      }
    } catch (logError) {
      console.error('Error logging notification (non-critical):', logError);
      // Continue processing - this is non-critical
    }

    return res.status(200).json({ message: 'Notification sent successfully' });
  } catch (error) {
    console.error('Error sending notification:', error);
    return res.status(500).json({ message: 'Failed to send notification', error: error.message });
  }
} 