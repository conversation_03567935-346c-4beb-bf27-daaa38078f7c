import React, { useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate, Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { MENTOR_BIOGRAPHIES } from '../../lib/constants/mentorBiographies';
import { Card } from '../../components/common';

const MentorDetailPage = () => {
  const { mentorId } = useParams();
  const navigate = useNavigate();
  
  // Find the mentor by ID
  const mentor = MENTOR_BIOGRAPHIES.find(m => m.id === mentorId);
  
  // Redirect to affordable locator if mentor not found
  useEffect(() => {
    if (!mentor && mentorId) {
      navigate('/affordable-care');
    }
  }, [mentor, mentorId, navigate]);
  
  // Get object position based on mentor ID to focus on faces
  const getHeaderImagePosition = (mentorId) => {
    switch (mentorId) {
      case 'benita-de-mirza':
        return 'object-[center_15%] object-cover';
      case 'joshua-golden':
        return 'object-[center_30%] object-cover';
      case 'hajar-hasan-verrett':
        return 'object-top object-cover';
      default:
        return 'object-center object-cover';
    }
  };
  
  const getPortraitImagePosition = (mentorId) => {
    switch (mentorId) {
      case 'benita-de-mirza':
        return 'object-top object-cover';
      case 'joshua-golden':
        return 'object-[center_20%] object-cover';
      case 'hajar-hasan-verrett':
        return 'object-top object-cover';
      default:
        return 'object-center object-cover';
    }
  };
  
  if (!mentor) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-500"></div>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto px-4 py-12 max-w-4xl">
      {/* Back Navigation */}
      <div className="mb-6">
        <Link 
          to="/affordable-care" 
          className="inline-flex items-center text-cyan-300 hover:text-cyan-200 transition-colors"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
          </svg>
          <span>Back to Mentors</span>
        </Link>
      </div>
      
      {/* Mentor Detail Card */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card className="overflow-hidden">
          {/* Profile Image (if available) */}
          {mentor.profileImage && (
            <div className="w-full h-64 md:h-80 overflow-hidden bg-gradient-to-b from-indigo-900/20 to-indigo-900/80">
              <img 
                src={mentor.profileImage} 
                alt={`Dr. ${mentor.name}`}
                className={`w-full h-full object-cover ${getHeaderImagePosition(mentor.id)} transition-transform duration-500`}
              />
            </div>
          )}
          
          {/* Header Section */}
          <div className={`bg-gradient-to-r from-indigo-900 to-indigo-800 p-6 ${mentor.profileImage ? 'border-t border-indigo-700' : ''}`}>
            <h1 className="text-3xl font-bold text-white">{mentor.name}</h1>
            <p className="text-cyan-300 mt-2">{mentor.title}</p>
            
            {/* Mentor Quick Info */}
            <div className="mt-4 flex flex-wrap text-sm text-white/80 gap-x-6 gap-y-2">
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                <span>{mentor.practice}</span>
              </div>
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                <span>{mentor.location}</span>
              </div>
              {mentor.practiceWebsite && (
                <div className="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                  </svg>
                  <a 
                    href={mentor.practiceWebsite} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-cyan-300 hover:text-cyan-200 transition-colors"
                  >
                    Visit Practice Website
                  </a>
                </div>
              )}
            </div>
            
            {/* Specialties/Tags */}
            <div className="mt-5 flex flex-wrap gap-2">
              {mentor.specialties.map((specialty, index) => (
                <span 
                  key={index} 
                  className="text-xs px-3 py-1 bg-indigo-700/60 text-indigo-100 rounded-full"
                >
                  {specialty}
                </span>
              ))}
            </div>
          </div>
          
          {/* Biography Content */}
          <div className="p-6">
            <div className="flex flex-col md:flex-row gap-8">
              {/* Biography Column */}
              <div className="flex-1">
                <h2 className="text-xl font-semibold text-cyan-300 mb-6">{mentor.pageTitle}</h2>
                
                {/* Biography Paragraphs */}
                <div className="space-y-5 text-white/90 leading-relaxed">
                  {mentor.biography.slice(0, -1).map((paragraph, index) => (
                    <p key={index} className="text-base">{paragraph}</p>
                  ))}
                </div>
                
                {/* Quote (if present) */}
                {mentor.quote && (
                  <div className="mt-8 bg-indigo-900/30 p-5 rounded-lg border-l-4 border-cyan-500">
                    <blockquote className="italic text-cyan-200 text-lg">
                      "{mentor.quote}"
                    </blockquote>
                  </div>
                )}
              </div>
              
              {/* Portrait Image (if available) */}
              {mentor.portraitImage && (
                <div className="md:w-1/3 shrink-0">
                  <div className="sticky top-6 rounded-lg overflow-hidden border-2 border-indigo-700/30 shadow-xl shadow-indigo-900/20">
                    <img 
                      src={mentor.portraitImage} 
                      alt={`Portrait of ${mentor.name}`}
                      className={`w-full h-auto max-h-[450px] object-cover ${getPortraitImagePosition(mentor.id)} transition-all duration-300`}
                    />
                  </div>
                </div>
              )}
            </div>
            
            {/* Practice Promotion */}
            <div className="mt-12 bg-gradient-to-r from-indigo-900/40 to-indigo-800/40 p-5 rounded-lg border border-indigo-700/30">
              <h3 className="text-lg font-semibold text-white mb-3">Visit {mentor.practice}</h3>
              <p className="text-white/80">{mentor.biography[mentor.biography.length - 1]}</p>
              
              {mentor.practiceWebsite && (
                <div className="mt-4">
                  <a 
                    href={mentor.practiceWebsite}
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="inline-flex items-center px-4 py-2 bg-cyan-600 hover:bg-cyan-700 text-white rounded transition-colors"
                  >
                    <span>Visit Practice Website</span>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                  </a>
                </div>
              )}
            </div>
          </div>
        </Card>
      </motion.div>
      
      {/* Return to Mentors Button */}
      <div className="mt-8 flex justify-center">
        <Link 
          to="/affordable-care"
          className="px-6 py-3 bg-indigo-700 hover:bg-indigo-600 text-white rounded-lg shadow transition-colors flex items-center space-x-2"
        >
          <span>Return to Mentors</span>
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
          </svg>
        </Link>
      </div>
    </div>
  );
};

export default MentorDetailPage; 