import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { matchPatientToProvider } from '../../services/matching';

// Provider interface based on the matching service
interface Provider {
  id: string;
  name: string;
  location: string;
  specialties: string[];
  insuranceAccepted: string[];
  rating: number;
  availability: string;
  compatibilityScore: number;
}

// Form data interface
interface MatchingFormData {
  condition: string;
  location: string;
  insurance: string;
  urgency: string;
}

const ProviderMatchingPage: React.FC = () => {
  // State for form and results
  const [formData, setFormData] = useState<MatchingFormData>({
    condition: '',
    location: '',
    insurance: '',
    urgency: 'Medium'
  });
  const [matchedProviders, setMatchedProviders] = useState<Provider[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);
  
  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSearching(true);
    
    // Call the matching service
    try {
      const results = matchPatientToProvider(formData);
      setMatchedProviders(results);
      setHasSearched(true);
    } catch (error) {
      console.error('Error finding matching providers:', error);
    } finally {
      setIsSearching(false);
    }
  };
  
  // Predefined lists for form options
  const conditions = [
    'Invisalign consultation',
    'Teeth cleaning',
    'Tooth pain',
    'Cavity filling',
    'Root canal',
    'Teeth whitening',
    'Dental implants',
    'Braces consultation',
    'Emergency dental care'
  ];
  
  const locations = [
    'Miami, FL',
    'Coral Gables, FL',
    'Miami Beach, FL',
    'Fort Lauderdale, FL',
    'Orlando, FL',
    'Tampa, FL'
  ];
  
  const insuranceProviders = [
    'Delta Dental',
    'Cigna',
    'Aetna',
    'MetLife',
    'United Healthcare',
    'Blue Cross',
    'Guardian',
    'None'
  ];
  
  return (
    <div className="max-w-6xl mx-auto">
      <div className="text-center mb-12">
        <h1 className="text-3xl md:text-4xl font-bold text-white mb-4">
          Find Your Perfect Dental Provider
        </h1>
        <p className="text-xl text-gray-300 max-w-3xl mx-auto">
          Tell us what you need, and we'll match you with compatible dental providers
          based on your condition, location, and insurance.
        </p>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Matching Form */}
        <div className="lg:col-span-1">
          <div className="bg-gray-800/50 rounded-xl p-6 border border-gray-700 sticky top-24">
            <h2 className="text-xl font-semibold text-white mb-6">
              Tell Us What You Need
            </h2>
            
            <form onSubmit={handleSubmit}>
              <div className="space-y-5">
                <div>
                  <label htmlFor="condition" className="block text-sm font-medium text-gray-300 mb-1">
                    What dental service do you need?*
                  </label>
                  <select
                    id="condition"
                    name="condition"
                    value={formData.condition}
                    onChange={handleChange}
                    required
                    className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                  >
                    <option value="">Select a service</option>
                    {conditions.map(condition => (
                      <option key={condition} value={condition}>{condition}</option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label htmlFor="location" className="block text-sm font-medium text-gray-300 mb-1">
                    Your location*
                  </label>
                  <select
                    id="location"
                    name="location"
                    value={formData.location}
                    onChange={handleChange}
                    required
                    className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                  >
                    <option value="">Select your location</option>
                    {locations.map(location => (
                      <option key={location} value={location}>{location}</option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label htmlFor="insurance" className="block text-sm font-medium text-gray-300 mb-1">
                    Your dental insurance
                  </label>
                  <select
                    id="insurance"
                    name="insurance"
                    value={formData.insurance}
                    onChange={handleChange}
                    className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                  >
                    <option value="">Select your insurance (optional)</option>
                    {insuranceProviders.map(insurance => (
                      <option key={insurance} value={insurance}>{insurance}</option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label htmlFor="urgency" className="block text-sm font-medium text-gray-300 mb-1">
                    How urgent is your need?
                  </label>
                  <div className="flex space-x-4">
                    {['Low', 'Medium', 'High'].map(level => (
                      <label key={level} className="flex items-center">
                        <input
                          id={`urgency-${level.toLowerCase()}`}
                          type="radio"
                          name="urgency"
                          value={level}
                          checked={formData.urgency === level}
                          onChange={handleChange}
                          className="mr-2 text-cyan-500 focus:ring-cyan-500"
                        />
                        <span className="text-gray-300">{level}</span>
                      </label>
                    ))}
                  </div>
                </div>
                
                <button
                  type="submit"
                  disabled={isSearching || !formData.condition || !formData.location}
                  className="w-full mt-2 px-4 py-3 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-lg text-white font-medium shadow-lg hover:shadow-cyan-500/20 transition disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSearching ? 'Finding matches...' : 'Find Matching Providers'}
                </button>
              </div>
            </form>
          </div>
        </div>
        
        {/* Results Section */}
        <div className="lg:col-span-2">
          {hasSearched ? (
            <>
              <h2 className="text-2xl font-semibold text-white mb-6">
                {matchedProviders.length > 0 
                  ? `We found ${matchedProviders.length} providers for you` 
                  : 'No providers found'}
              </h2>
              
              {matchedProviders.length > 0 ? (
                <div className="space-y-6">
                  {matchedProviders.map((provider, index) => (
                    <ProviderCard 
                      key={provider.id} 
                      provider={provider} 
                      index={index}
                    />
                  ))}
                </div>
              ) : (
                <div className="bg-gray-800/50 rounded-xl p-8 border border-gray-700 text-center">
                  <div className="w-20 h-20 mx-auto bg-gray-700/50 rounded-full flex items-center justify-center mb-4">
                    <svg className="w-10 h-10 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-medium text-white mb-2">No matching providers found</h3>
                  <p className="text-gray-400 mb-4">
                    Try adjusting your search criteria to find more providers in your area.
                  </p>
                  <button
                    onClick={() => setHasSearched(false)}
                    className="px-4 py-2 bg-gray-700 rounded-lg text-white hover:bg-gray-600 transition"
                  >
                    Modify Search
                  </button>
                </div>
              )}
            </>
          ) : (
            <div className="bg-gray-800/30 rounded-xl p-8 border border-gray-700/50">
              <div className="text-center">
                <div className="w-24 h-24 mx-auto bg-cyan-900/20 rounded-full flex items-center justify-center mb-6">
                  <svg className="w-12 h-12 text-cyan-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <h2 className="text-2xl font-semibold text-white mb-3">
                  Find Your Dental Match
                </h2>
                <p className="text-gray-300 mb-6 max-w-md mx-auto">
                  Fill out the form to find dental providers that match your needs, 
                  location, and insurance coverage.
                </p>
                
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto">
                  {['Invisalign', 'Cleaning', 'Emergency', 'Implants'].map(service => (
                    <button
                      key={service}
                      onClick={() => setFormData(prev => ({ ...prev, condition: `${service} ${service === 'Cleaning' ? '' : 'consultation'}` }))}
                      className="px-4 py-3 bg-gray-700 rounded-lg text-white hover:bg-gray-600 transition text-sm"
                    >
                      {service}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Provider Card Component
const ProviderCard: React.FC<{
  provider: Provider;
  index: number;
}> = ({ provider, index }) => {
  return (
    <motion.div 
      className="bg-gray-800/50 rounded-xl border border-gray-700 overflow-hidden"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: index * 0.1 }}
    >
      <div className="flex flex-col md:flex-row">
        {/* Provider Image/Logo Area */}
        <div className="w-full md:w-1/4 bg-gradient-to-br from-cyan-900/30 to-blue-900/30 p-6 flex items-center justify-center">
          <div className="w-20 h-20 bg-gray-700 rounded-full flex items-center justify-center">
            <span className="text-2xl font-bold text-cyan-400">
              {provider.name.split(' ').map(word => word[0]).join('')}
            </span>
          </div>
        </div>
        
        {/* Provider Info */}
        <div className="w-full md:w-3/4 p-6">
          <div className="flex justify-between mb-2">
            <h3 className="text-xl font-semibold text-white">{provider.name}</h3>
            <div className="flex items-center">
              <span className="text-yellow-400 mr-1">★</span>
              <span className="text-white">{provider.rating.toFixed(1)}</span>
            </div>
          </div>
          
          <div className="mb-4">
            <div className="flex flex-wrap gap-2 mb-2">
              {provider.specialties.map(specialty => (
                <span 
                  key={specialty} 
                  className="px-2 py-1 bg-gray-700 rounded-md text-sm text-gray-300"
                >
                  {specialty}
                </span>
              ))}
            </div>
            
            <div className="flex items-center text-gray-400 text-sm">
              <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              {provider.location}
            </div>
          </div>
          
          <div className="mb-4">
            <div className="flex items-center mb-1">
              <span className="text-sm text-gray-400 mr-2">Compatibility</span>
              <div className="flex-grow h-2 bg-gray-700 rounded-full overflow-hidden">
                <div 
                  className={`h-full rounded-full ${
                    provider.compatibilityScore >= 80 
                      ? 'bg-green-500' 
                      : provider.compatibilityScore >= 50 
                        ? 'bg-cyan-500' 
                        : 'bg-yellow-500'
                  }`}
                  style={{ width: `${provider.compatibilityScore}%` }}
                />
              </div>
              <span className="ml-2 text-sm font-medium text-white">{provider.compatibilityScore}%</span>
            </div>
            
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div className="flex items-center text-gray-300">
                <svg className="w-4 h-4 text-cyan-400 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Accepts {provider.insuranceAccepted[0]} {provider.insuranceAccepted.length > 1 ? '& more' : ''}
              </div>
              
              <div className="flex items-center text-gray-300">
                <svg className="w-4 h-4 text-cyan-400 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                {provider.availability} Availability
              </div>
            </div>
          </div>
          
          <div className="flex space-x-3">
            <button className="flex-1 px-4 py-2 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-lg text-white font-medium shadow-md hover:shadow-lg hover:shadow-cyan-500/20 transition text-sm">
              Request Appointment
            </button>
            <button className="px-4 py-2 bg-gray-700 rounded-lg text-white hover:bg-gray-600 transition text-sm">
              View Profile
            </button>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default ProviderMatchingPage; 