import React, { useState } from 'react';
import { FiArrowRight, FiMonitor, FiRefreshCw, FiSmile, FiX } from 'react-icons/fi';
import emailjs from 'emailjs-com';

const initialForm = {
  type: '',
  name: '',
  email: '',
  practice: '',
  phone: '',
  website: '',
  goals: '',
  features: '',
  message: '',
};

function SurveyModal({ open, onClose, type, onSubmit }) {
  const [form, setForm] = useState({ ...initialForm, type });
  const [submitting, setSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);
  const [error, setError] = useState('');

  const handleChange = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitting(true);
    setError('');
    try {
      await emailjs.send(
        'Smilo', // Service ID
        'template_p3atdbr', // Template ID
        {
          ...form,
          title: form.type === 'website' ? 'Website Creation' : 'Website Redesign',
        },
        'v3fkB_veX53Wg9H84' // Public Key
      );
      setSubmitting(false);
      setSubmitted(true);
      onSubmit(form);
    } catch (err) {
      setSubmitting(false);
      setError('There was an error sending your request. Please try again <NAME_EMAIL> directly.');
    }
  };

  if (!open) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm">
      <div className="bg-gradient-to-br from-blue-900 via-indigo-900 to-purple-900 rounded-2xl shadow-2xl p-8 w-full max-w-lg relative border border-white/10">
        <button onClick={onClose} className="absolute top-4 right-4 text-white/70 hover:text-white text-2xl"><FiX /></button>
        {!submitted ? (
          <form onSubmit={handleSubmit} className="space-y-5">
            <h2 className="text-2xl font-bold text-white mb-2 text-center">
              {type === 'website' ? 'Request a Website' : 'Request a Redesign'}
            </h2>
            <div className="grid grid-cols-1 gap-4">
              <input name="name" required value={form.name} onChange={handleChange} placeholder="Your Name" className="px-4 py-2 rounded bg-white/10 text-white placeholder-white/60 border border-white/10 focus:outline-none focus:ring-2 focus:ring-blue-400" />
              <input name="email" required type="email" value={form.email} onChange={handleChange} placeholder="Your Email" className="px-4 py-2 rounded bg-white/10 text-white placeholder-white/60 border border-white/10 focus:outline-none focus:ring-2 focus:ring-blue-400" />
              <input name="practice" value={form.practice} onChange={handleChange} placeholder="Practice Name" className="px-4 py-2 rounded bg-white/10 text-white placeholder-white/60 border border-white/10 focus:outline-none focus:ring-2 focus:ring-blue-400" />
              <input name="phone" value={form.phone} onChange={handleChange} placeholder="Phone Number" className="px-4 py-2 rounded bg-white/10 text-white placeholder-white/60 border border-white/10 focus:outline-none focus:ring-2 focus:ring-blue-400" />
              <input name="website" value={form.website} onChange={handleChange} placeholder="Current Website (if any)" className="px-4 py-2 rounded bg-white/10 text-white placeholder-white/60 border border-white/10 focus:outline-none focus:ring-2 focus:ring-blue-400" />
              <textarea name="goals" value={form.goals} onChange={handleChange} placeholder="What are your goals for this project?" className="px-4 py-2 rounded bg-white/10 text-white placeholder-white/60 border border-white/10 focus:outline-none focus:ring-2 focus:ring-blue-400 min-h-[60px]" />
              <textarea name="features" value={form.features} onChange={handleChange} placeholder="Features or integrations you want (e.g., online booking, Smilo Assist, reviews, etc.)" className="px-4 py-2 rounded bg-white/10 text-white placeholder-white/60 border border-white/10 focus:outline-none focus:ring-2 focus:ring-blue-400 min-h-[60px]" />
              <textarea name="message" value={form.message} onChange={handleChange} placeholder="Anything else you'd like to share?" className="px-4 py-2 rounded bg-white/10 text-white placeholder-white/60 border border-white/10 focus:outline-none focus:ring-2 focus:ring-blue-400 min-h-[60px]" />
            </div>
            {error && <div className="text-red-400 text-sm text-center">{error}</div>}
            <button type="submit" disabled={submitting} className="w-full mt-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-full font-semibold shadow-lg hover:from-blue-600 hover:to-indigo-700 transition text-lg disabled:opacity-60 disabled:cursor-not-allowed">
              {submitting ? 'Submitting...' : 'Submit Request'}
            </button>
          </form>
        ) : (
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-white mb-4">Thank you!</h2>
            <p className="text-blue-100/90 mb-2">Your request has been received. We'll reach out to you soon.</p>
            <button onClick={onClose} className="mt-6 px-6 py-2 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-full font-semibold shadow hover:from-blue-600 hover:to-indigo-700 transition">Close</button>
          </div>
        )}
      </div>
    </div>
  );
}

export default function Providers() {
  const [modal, setModal] = useState({ open: false, type: 'website' });

  const handleOpenModal = (type) => setModal({ open: true, type });
  const handleCloseModal = () => setModal({ open: false, type: 'website' });
  const handleSurveySubmit = (data) => {
    // You can add analytics or other logic here if needed
  };

  return (
    <div className="relative min-h-screen bg-gradient-to-br from-indigo-900 via-blue-900 to-purple-900 flex items-center justify-center py-20 px-4 overflow-hidden">
      {/* Animated background orbs */}
      <div className="absolute top-0 left-0 w-96 h-96 bg-blue-500/20 rounded-full blur-[120px] -z-10 animate-float-slow" style={{ filter: 'blur(120px)' }} />
      <div className="absolute bottom-0 right-0 w-[32rem] h-[32rem] bg-purple-500/20 rounded-full blur-[160px] -z-10 animate-float-medium" style={{ filter: 'blur(160px)' }} />
      <div className="absolute top-1/2 left-1/2 w-[60vw] h-[60vw] max-w-3xl max-h-3xl bg-gradient-to-br from-blue-400/10 via-indigo-400/10 to-purple-400/10 rounded-full blur-[180px] -translate-x-1/2 -translate-y-1/2 -z-10" />

      <SurveyModal open={modal.open} onClose={handleCloseModal} type={modal.type} onSubmit={handleSurveySubmit} />

      <div className="w-full max-w-6xl mx-auto bg-gradient-to-br from-blue-900/70 via-indigo-900/70 to-purple-900/70 rounded-3xl shadow-2xl p-10 md:p-16 flex flex-col items-center border border-white/10 backdrop-blur-xl">
        <h1 className="text-5xl md:text-6xl font-extrabold bg-clip-text text-transparent bg-gradient-to-r from-blue-300 via-indigo-300 to-purple-400 mb-6 drop-shadow-lg text-center">
          Provider Solutions
        </h1>
        <p className="text-blue-100/90 mb-12 text-xl md:text-2xl max-w-3xl mx-auto text-center font-light">
          Smilo offers modern, patient-focused digital solutions for dental practices. Whether you need a new website, a redesign, or want to empower your practice with Smilo Assist, we have you covered.
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-10 w-full mb-12">
          {/* Website Creation */}
          <div className="bg-gradient-to-br from-blue-800/60 via-indigo-800/50 to-purple-800/60 rounded-2xl p-8 border border-blue-400/20 shadow-xl flex flex-col items-center hover:scale-[1.03] transition-transform duration-300 group relative overflow-hidden">
            <div className="absolute -top-10 -right-10 w-32 h-32 bg-blue-400/20 rounded-full blur-2xl group-hover:scale-110 transition-transform duration-300" />
            <FiMonitor className="text-5xl text-blue-300 mb-4 drop-shadow-lg" />
            <h2 className="text-2xl font-bold text-white mb-3">Custom Website Creation</h2>
            <p className="text-blue-100/80 mb-6 text-center">Get a beautiful, modern website tailored to your practice and patients. Mobile-friendly, fast, and designed to convert visitors into patients.</p>
            <button onClick={() => handleOpenModal('website')} className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-full font-semibold shadow-lg hover:from-blue-600 hover:to-indigo-700 transition text-lg">
              Request a Website <FiArrowRight className="ml-2" />
            </button>
          </div>
          {/* Website Redesign */}
          <div className="bg-gradient-to-br from-purple-800/60 via-indigo-800/50 to-blue-800/60 rounded-2xl p-8 border border-purple-400/20 shadow-xl flex flex-col items-center hover:scale-[1.03] transition-transform duration-300 group relative overflow-hidden">
            <div className="absolute -bottom-10 -left-10 w-32 h-32 bg-purple-400/20 rounded-full blur-2xl group-hover:scale-110 transition-transform duration-300" />
            <FiRefreshCw className="text-5xl text-purple-300 mb-4 drop-shadow-lg" />
            <h2 className="text-2xl font-bold text-white mb-3">Website Redesign</h2>
            <p className="text-blue-100/80 mb-6 text-center">Already have a website? Let us modernize it for you—improve speed, design, and patient experience. Stand out from the competition.</p>
            <button onClick={() => handleOpenModal('redesign')} className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-full font-semibold shadow-lg hover:from-purple-600 hover:to-pink-600 transition text-lg">
              Request a Redesign <FiArrowRight className="ml-2" />
            </button>
          </div>
        </div>
        {/* Smilo Assist Integration */}
        <div className="w-full bg-gradient-to-br from-indigo-800/60 via-blue-800/50 to-purple-800/60 rounded-2xl p-8 border border-indigo-400/20 shadow-xl flex flex-col items-center mb-8 relative overflow-hidden">
          <div className="absolute -top-8 right-1/2 w-40 h-40 bg-indigo-400/20 rounded-full blur-2xl" />
          <FiSmile className="text-5xl text-indigo-300 mb-4 drop-shadow-lg" />
          <h2 className="text-2xl font-bold text-white mb-3">Smilo Assist Integration</h2>
          <p className="text-blue-100/80 mb-6 text-center">Boost your practice with AI-powered scheduling, patient comfort features, and more. Seamlessly integrate Smilo Assist into your new or existing website.</p>
          <a href="/assist" className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-indigo-500 to-blue-500 text-white rounded-full font-semibold shadow-lg hover:from-indigo-600 hover:to-blue-600 transition text-lg">
            Learn About Smilo Assist <FiArrowRight className="ml-2" />
          </a>
        </div>
        <div className="text-blue-200/80 text-base mt-6 text-center">
          Have questions or want a custom solution? <a href="mailto:<EMAIL>" className="underline hover:text-white">Contact us</a> and we'll help you get started.
        </div>
      </div>
    </div>
  );
} 