import React from 'react';
import SignInButton from '../components/auth/SignInButton';

const TestPage: React.FC = () => {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-4 bg-gray-900">
      <div className="max-w-lg w-full bg-gray-800 rounded-xl p-8 shadow-lg">
        <h1 className="text-3xl font-bold text-white mb-6 text-center">Sign-In Modal Test</h1>
        
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-white mb-4">Patient Sign-In</h2>
          <div className="flex flex-col space-y-4">
            <SignInButton 
              userType="patient"
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
            >
              Open Patient Sign-In Modal
            </SignInButton>
          </div>
        </div>
        
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-white mb-4">Practice Sign-In</h2>
          <div className="flex flex-col space-y-4">
            <SignInButton 
              userType="practice"
              className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors"
            >
              Open Practice Sign-In Modal
            </SignInButton>
          </div>
        </div>
        
        <div className="border-t border-gray-700 pt-6 mt-6">
          <h2 className="text-xl font-semibold text-white mb-4">Modal Features</h2>
          <ul className="list-disc list-inside text-gray-300 space-y-2">
            <li>Click outside to keep the modal open</li>
            <li>Press ESC key to close the modal</li>
            <li>Click X button to close the modal</li>
            <li>Form validation for email and password</li>
            <li>Loading state during sign-in process</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default TestPage; 