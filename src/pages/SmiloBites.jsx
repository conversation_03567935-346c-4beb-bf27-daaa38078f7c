import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { createClient } from '@supabase/supabase-js';
import proxyImage from '../lib/utils/imageProxy';

// Import our components
import EbtBalanceCard from '../components/smiloBites/EbtBalanceCard';
import GrocerySuggestions from '../components/smiloBites/GrocerySuggestions';
import CartBuilder from '../components/smiloBites/CartBuilder';
import ChatAssistant from '../components/smiloBites/ChatAssistant';
import RecipeCarousel from '../components/smiloBites/RecipeCarousel';
import HealthyGrocerySearch from '../components/smiloBites/HealthyGrocerySearch';

// Initialize Supabase client - replace with your actual Supabase URL and anon key
const supabaseUrl = 'https://your-supabase-url.supabase.co';
const supabaseAnonKey = 'your-supabase-anon-key';
const supabase = createClient(supabaseUrl, supabaseAnonKey);

export default function SmiloBites() {
  // App states - removed authentication-specific states
  const [loading, setLoading] = useState(false);
  const [userData, setUserData] = useState({
    name: 'Friend',
    ebtBalance: '$0.00',
    dietaryPreferences: [],
    dentalHealthGoals: [],
    previousCarts: []
  });
  const [searchQuery, setSearchQuery] = useState('');
  const [activeFilter, setActiveFilter] = useState('tooth-safe');
  const [showSearch, setShowSearch] = useState(false);
  const [hideSmiloSnap, setHideSmiloSnap] = useState(true); // Default to hidden
  
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };
  
  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 10
      }
    }
  };

  const filterCategories = [
    { id: 'tooth-safe', name: 'Tooth-Safe Snacks' },
    { id: 'calcium', name: 'Calcium Boosters' },
    { id: 'budget', name: '$5 Meals' },
    { id: 'kids', name: 'Quick for Kids' },
    { id: 'fiber', name: 'High Fiber' },
  ];

  // Featured recipes collection for the carousel
  const featuredRecipes = [
    {
      id: 'dental-boost-smoothie',
      title: 'Dental-Boost Smoothie',
      cost: '$3 per serving',
      image: 'https://images.unsplash.com/photo-1623428187969-5da2dcea5ebf?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1064&q=80',
      tags: ['High Calcium', 'Tooth Safe'],
      description: 'This delicious smoothie is packed with calcium and vitamin D for stronger teeth and contains no added sugars.',
      ingredients: [
        '1 cup plain Greek yogurt',
        '1/2 cup frozen berries',
        '1 tablespoon ground flaxseed',
        '1/2 banana',
        'Splash of milk or water'
      ],
      instructions: [
        'Add all ingredients to a blender',
        'Blend until smooth, adding liquid as needed for desired consistency',
        'Enjoy immediately for maximum nutritional benefits'
      ],
      dentalBenefits: 'Greek yogurt is high in calcium and protein, which are essential for strong teeth. Berries are lower in sugar than many fruits and contain antioxidants that may help fight inflammation in the gums. Flaxseed provides fiber and omega-3 fatty acids that can help reduce gum inflammation.'
    },
    {
      id: 'calcium-packed-overnight-oats',
      title: 'Calcium-Packed Overnight Oats',
      cost: '$1.75 per serving',
      image: 'https://images.unsplash.com/photo-1515543904379-3d757afe72e1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1064&q=80',
      tags: ['Breakfast', 'High Calcium', 'Kid-Friendly'],
      description: 'A simple make-ahead breakfast that\'s rich in calcium and low in added sugars. Perfect for busy mornings!',
      ingredients: [
        '1/2 cup rolled oats',
        '1/2 cup milk or calcium-fortified plant milk',
        '1/4 cup plain yogurt',
        '1 tbsp chia seeds',
        '1/2 tsp cinnamon',
        'Small amount of honey or maple syrup (optional)'
      ],
      instructions: [
        'Mix all ingredients in a jar or container',
        'Cover and refrigerate overnight (at least 6 hours)',
        'Top with fresh berries, sliced almonds, or diced apple in the morning'
      ],
      dentalBenefits: 'Oats are low in sugar and rich in nutrients. The calcium from dairy or fortified plant milk helps strengthen tooth enamel. Chia seeds contain phosphorus, which works with calcium to build strong teeth.'
    },
    {
      id: 'salmon-veggie-bake',
      title: 'Simple Salmon & Veggie Bake',
      cost: '$4.50 per serving',
      image: 'https://images.unsplash.com/photo-1519708227418-c8fd9a32b7a2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1064&q=80',
      tags: ['High Protein', 'Omega-3'],
      description: 'A sheet pan dinner that\'s rich in omega-3 fatty acids to reduce inflammation and promote gum health.',
      ingredients: [
        '4 oz canned or fresh salmon',
        '1 cup broccoli florets',
        '1 cup sliced bell peppers',
        '1/2 cup cherry tomatoes',
        '1 tbsp olive oil',
        'Garlic, lemon, herbs to taste'
      ],
      instructions: [
        'Preheat oven to 400°F',
        'Toss vegetables with olive oil and seasonings',
        'Place salmon and vegetables on a sheet pan',
        'Bake for 15-20 minutes until salmon is cooked and vegetables are tender'
      ],
      dentalBenefits: 'Salmon is rich in vitamin D, which helps your body absorb calcium. The omega-3 fatty acids in salmon may help reduce gum inflammation. Crunchy vegetables stimulate saliva production, which helps wash away food particles and neutralize acids.'
    },
    {
      id: 'crunchy-veggie-wraps',
      title: 'Crunchy Veggie Wraps',
      cost: '$2.50 per serving',
      image: 'https://images.unsplash.com/photo-1600850056064-a8b380df8395?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1064&q=80',
      tags: ['Quick Lunch', 'Fiber-Rich'],
      description: 'These wraps feature crunchy vegetables that help clean teeth naturally while providing essential nutrients.',
      ingredients: [
        'Whole grain tortilla or wrap',
        '2 tbsp hummus or Greek yogurt spread',
        '1/4 cup grated carrots',
        '1/4 cup sliced bell peppers',
        '1/4 cup sliced cucumbers',
        '1/4 avocado, sliced',
        'Handful of spinach'
      ],
      instructions: [
        'Spread hummus or Greek yogurt on the wrap',
        'Layer vegetables and avocado',
        'Roll up tightly and slice in half'
      ],
      dentalBenefits: 'Crunchy vegetables like carrots and bell peppers act as natural toothbrushes, scrubbing away plaque as you chew. The fiber content stimulates saliva production, which helps neutralize acids. Calcium from the Greek yogurt spread (if used) supports enamel strength.'
    },
    {
      id: 'cheese-and-apple-plate',
      title: 'Cheese & Apple Plate',
      cost: '$3.25 per serving',
      image: 'https://images.unsplash.com/photo-1505575967455-40e256f73376?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1064&q=80',
      tags: ['High Calcium', 'pH Balancing'],
      description: 'A simple, tooth-friendly snack plate that combines crunchy apples with calcium-rich cheese.',
      ingredients: [
        '1-2 oz hard cheese (like cheddar or gouda)',
        '1 crisp apple, sliced',
        '1 tbsp nuts (almonds or walnuts)',
        'Optional: small handful of grapes'
      ],
      instructions: [
        'Slice cheese into small cubes or thin slices',
        'Arrange cheese, apple slices, and nuts on a plate',
        'Enjoy immediately or pack for a tooth-healthy snack on the go'
      ],
      dentalBenefits: 'Cheese raises the pH level in your mouth, countering acidity that damages tooth enamel. It also provides calcium and phosphates needed for remineralization. Crunchy apples stimulate saliva production and help clean teeth surfaces, while their fiber content scrubs away bacteria and food particles.'
    },
    {
      id: 'mineral-rich-bone-broth-soup',
      title: 'Mineral-Rich Bone Broth Soup',
      cost: '$3.00 per serving',
      image: 'https://images.unsplash.com/photo-**********-85f173990554?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1064&q=80',
      tags: ['Mineral-Rich', 'Gum Health'],
      description: 'A nutrient-dense soup that delivers minerals essential for tooth and gum health in an easy-to-absorb form.',
      ingredients: [
        '2 cups bone broth (chicken or beef)',
        '1/2 cup chopped carrots',
        '1/2 cup chopped celery',
        '1/4 cup chopped onion',
        '1 cup leafy greens (spinach or kale)',
        'Herbs and spices to taste',
        '1 tbsp apple cider vinegar'
      ],
      instructions: [
        'Sauté onions, carrots, and celery until soft',
        'Add bone broth and bring to a simmer',
        'Add leafy greens in the last few minutes of cooking',
        'Season with herbs, spices, and a splash of apple cider vinegar'
      ],
      dentalBenefits: 'Bone broth is rich in calcium, phosphorus, and magnesium that are essential for tooth remineralization. The collagen in bone broth supports gum tissue health. Apple cider vinegar in small amounts helps extract minerals from the bones during cooking and supports a healthy oral microbiome.'
    },
    {
      id: 'crunchy-nut-mix',
      title: 'Phosphorus-Rich Nut Mix',
      cost: '$2.80 per serving',
      image: 'https://images.unsplash.com/photo-1606913419066-0087e9ea71db?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1064&q=80',
      tags: ['Sugar-Free', 'Phosphorus-Rich'],
      description: 'A satisfying, nutrient-dense snack that provides minerals needed for strong teeth without any added sugars.',
      ingredients: [
        '1/4 cup almonds',
        '1/4 cup walnuts',
        '2 tbsp pumpkin seeds',
        '1 tbsp sesame seeds',
        'Pinch of sea salt (optional)'
      ],
      instructions: [
        'Mix all nuts and seeds in a small container',
        'Add a pinch of sea salt if desired',
        'Store in an airtight container for a grab-and-go snack'
      ],
      dentalBenefits: 'Nuts and seeds are packed with phosphorus, magnesium, and calcium – minerals essential for tooth strength. Their crunchy texture stimulates saliva production, which helps wash away food particles and neutralize acids. Nuts also contain vitamin E, an antioxidant that helps reduce inflammation in gum tissue.'
    },
    {
      id: 'green-calcium-smoothie',
      title: 'Green Calcium Smoothie',
      cost: '$2.25 per serving',
      image: 'https://images.unsplash.com/photo-1610970881699-44a5587cabec?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1064&q=80',
      tags: ['Vitamin-Rich', 'Calcium-Boosting'],
      description: 'A leafy green smoothie that delivers calcium from plant sources along with vitamins for optimal absorption.',
      ingredients: [
        '1 cup kale or spinach',
        '1/2 cup calcium-fortified plant milk',
        '1/2 cup frozen cauliflower florets',
        '1/2 banana',
        '1 tbsp almond butter',
        '1/2 tsp ground cinnamon',
        'Water as needed for desired consistency'
      ],
      instructions: [
        'Add all ingredients to a blender',
        'Blend until smooth, adding water as needed',
        'Drink immediately for optimal nutrition'
      ],
      dentalBenefits: 'Leafy greens provide calcium and vitamin K, which work together to strengthen teeth and bones. Cauliflower adds a creamy texture without added sugars while providing vitamin C for gum health. The smooth texture doesn\'t leave residue on teeth like sugary drinks, and the neutral pH doesn\'t contribute to acid erosion.'
    },
    {
      id: 'mineral-rich-sardine-toast',
      title: 'Mineral-Rich Sardine Toast',
      cost: '$3.75 per serving',
      image: 'https://images.unsplash.com/photo-1524177536702-5e458c7d4b1b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1064&q=80',
      tags: ['Calcium-Rich', 'Vitamin D'],
      description: 'A savory toast topped with sardines – one of the most nutrient-dense foods for dental health.',
      ingredients: [
        '1 slice whole grain bread',
        '1 can sardines in olive oil',
        '1/4 avocado, mashed',
        'Squeeze of lemon juice',
        'Chopped fresh herbs (parsley, dill, or chives)',
        'Black pepper to taste'
      ],
      instructions: [
        'Toast the bread until golden',
        'Spread mashed avocado on the toast',
        'Top with drained sardines',
        'Add a squeeze of lemon, herbs, and black pepper'
      ],
      dentalBenefits: 'Sardines are one of the richest sources of bioavailable calcium because they contain edible bones. They\'re also high in vitamin D, which is essential for calcium absorption and utilization. The omega-3 fatty acids in sardines help reduce inflammation, potentially benefiting gum health. Whole grain bread provides minerals and fiber without the enamel-damaging effects of refined carbohydrates.'
    },
    {
      id: 'crunchy-veggie-sticks-with-hummus',
      title: 'Veggie Sticks with Hummus',
      cost: '$2.00 per serving',
      image: 'https://images.unsplash.com/photo-1619722087489-f0b1a6d2e620?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1064&q=80',
      tags: ['Natural Cleansing', 'Gum Stimulating'],
      description: 'Crunchy raw vegetables paired with protein-rich hummus that help clean teeth while you snack.',
      ingredients: [
        '1 cup mixed vegetable sticks (carrots, celery, cucumber, bell peppers)',
        '1/4 cup hummus',
        '1 tbsp olive oil (drizzled on hummus)',
        'Pinch of paprika (optional)'
      ],
      instructions: [
        'Wash and cut vegetables into sticks',
        'Serve with a side of hummus for dipping',
        'Drizzle olive oil and sprinkle paprika on hummus if desired'
      ],
      dentalBenefits: 'Crunchy vegetables act as natural toothbrushes, scrubbing away plaque and food particles. Chewing raw vegetables stimulates saliva production, which helps neutralize acids and remineralize teeth. Hummus provides protein without added sugars, and chickpeas contain minerals like calcium and phosphorus that support tooth health.'
    }
  ];

  // Add item to cart
  const handleAddToCart = (item) => {
    // This function will be passed to GrocerySuggestions component
    console.log("Adding to cart:", item);
    // In a real implementation, this would add the item to the cart
  };

  // Add recipe ingredients to cart
  const handleAddIngredientsToCart = (ingredients, recipeName) => {
    console.log(`Adding ingredients for ${recipeName} to cart:`, ingredients);
    // In a real implementation, this would add all the ingredients to the cart
    ingredients.forEach(ingredient => {
      handleAddToCart(ingredient);
    });
  };

  // Update EBT balance
  const handleBalanceUpdate = (newBalance) => {
    setUserData(prev => ({
      ...prev,
      ebtBalance: newBalance
    }));
  };

  // Main content - accessible to all users without login
  return (
    <div className="min-h-screen bg-gradient-to-b from-indigo-50 to-purple-50 py-12 px-4 sm:px-6">
      <motion.div 
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="max-w-4xl mx-auto"
      >
        {/* Header */}
        <motion.div variants={itemVariants} className="mb-8 text-center sm:text-left">
          <h1 className="text-3xl font-bold text-indigo-800 mb-2">
            Hi {userData.name}, here's your plan for a healthier smile this week!
          </h1>
          <p className="text-indigo-600">Find tooth-friendly foods that are SNAP/EBT eligible</p>
          {/* Toggle SmiloSnap - admin only functionality */}
          <button 
            onClick={() => setHideSmiloSnap(!hideSmiloSnap)}
            className="mt-2 text-xs text-gray-400 underline"
          >
            {hideSmiloSnap ? "Enable SmiloSnap" : "Disable SmiloSnap"}
          </button>
        </motion.div>

        {/* Main app content in 2-column layout */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column (2/3 width on large screens) */}
          <div className="lg:col-span-2 space-y-6">
            {/* EBT Balance Card */}
            <motion.div variants={itemVariants}>
              <EbtBalanceCard 
                initialBalance={userData.ebtBalance} 
                onBalanceUpdate={handleBalanceUpdate}
                supabase={supabase}
              />
            </motion.div>

            {!hideSmiloSnap && (
              <>
                {/* Search Bar */}
                <motion.div variants={itemVariants} className="mb-8">
                  <div className="relative">
                    <input
                      type="text"
                      placeholder="Search affordable, SNAP-eligible groceries..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      onKeyUp={(e) => {
                        if (e.key === 'Enter' && searchQuery.trim()) {
                          setShowSearch(true);
                        }
                      }}
                      className="w-full bg-white border border-gray-200 rounded-full py-3 px-5 pl-12 text-gray-800 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500/50 focus:border-indigo-500 shadow-sm"
                    />
                    <svg 
                      className="absolute left-4 top-3.5 h-5 w-5 text-indigo-500 cursor-pointer" 
                      fill="none" 
                      viewBox="0 0 24 24" 
                      stroke="currentColor"
                      onClick={() => {
                        if (searchQuery.trim()) {
                          setShowSearch(true);
                        }
                      }}
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                </motion.div>

                {/* Filter Categories */}
                <motion.div variants={itemVariants} className="mb-8">
                  <div className="flex space-x-3 overflow-x-auto pb-2 no-scrollbar">
                    {filterCategories.map(category => (
                      <button
                        key={category.id}
                        className={`px-4 py-2 rounded-full text-sm font-medium flex-shrink-0 transition-colors ${
                          activeFilter === category.id 
                            ? 'bg-indigo-500 text-white shadow-md' 
                            : 'bg-white text-indigo-700 border border-indigo-200 hover:bg-indigo-50'
                        }`}
                        onClick={() => {
                          setActiveFilter(category.id);
                          setShowSearch(false);
                        }}
                      >
                        {category.name}
                      </button>
                    ))}
                  </div>
                </motion.div>

                {/* Recipe Carousel */}
                <motion.div variants={itemVariants}>
                  <RecipeCarousel 
                    recipes={featuredRecipes} 
                    title="Tooth-Friendly Recipes"
                    onAddIngredientsToCart={handleAddIngredientsToCart}
                  />
                </motion.div>

                {/* Show either grocery suggestions or search results */}
                {!showSearch ? (
                  <motion.div variants={itemVariants}>
                    <GrocerySuggestions
                      activeFilter={activeFilter}
                      onAddToCart={handleAddToCart}
                    />
                  </motion.div>
                ) : (
                  <motion.div variants={itemVariants} className="mt-8">
                    <HealthyGrocerySearch
                      onAddToCart={handleAddToCart}
                      initialSearchTerm={searchQuery}
                    />
                  </motion.div>
                )}
              </>
            )}

            {hideSmiloSnap && (
              <motion.div variants={itemVariants}>
                <div className="bg-white p-8 rounded-lg shadow text-center">
                  <h2 className="text-xl font-bold text-indigo-800 mb-4">SmiloSnap Feature Currently Disabled</h2>
                  <p className="text-gray-600 mb-4">
                    The SmiloSnap grocery pricing feature is temporarily unavailable. 
                    Our team is working on improving this feature to better serve your dental health needs.
                  </p>
                  <p className="text-gray-500 text-sm">
                    Please check back soon for updates.
                  </p>
                </div>
              </motion.div>
            )}
          </div>

          {/* Right Column (1/3 width on large screens) */}
          <div className="lg:col-span-1">
            <motion.div variants={itemVariants}>
              <CartBuilder
                userData={userData}
                supabase={supabase}
              />
            </motion.div>
          </div>
        </div>

        {/* Back Link */}
        <motion.div variants={itemVariants} className="text-center mt-8 mb-16">
          <Link 
            to="/affordable-care"
            className="inline-flex items-center text-indigo-600 hover:text-indigo-700"
          >
            <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Back to Affordable Care Options
          </Link>
        </motion.div>

        {/* Chat Assistant */}
        <ChatAssistant userData={userData} supabase={supabase} />
      </motion.div>
    </div>
  );
} 