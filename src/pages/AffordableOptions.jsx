import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import DentalSchoolMap from '../components/resources/DentalSchoolMap';
import { ACCREDITED_SCHOOLS } from '../lib/constants/dentalSchools/accreditedSchools';
import { RecommendedProducts } from '../components/common/AdCard';
import useAffiliateProducts from '../lib/hooks/useAffiliateProducts';
import SimpleAdCards from '../components/affiliate/SimpleAdCards';
import AD_LINKS from '../lib/constants/adLinks';

export default function AffordableOptions() {
  const location = useLocation();
  const [activeTab, setActiveTab] = useState('options');
  const [userLocation, setUserLocation] = useState(null);
  const [selectedSchool, setSelectedSchool] = useState(null);
  
  // Fetch affiliate products for dental care
  const { products: dentalProducts, getRandomProducts } = useAffiliateProducts('dental', 5);
  const { products: affordableProducts } = useAffiliateProducts('affordable', 2);
  
  // Handle URL query parameters for tab selection
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const tabParam = searchParams.get('tab');
    if (tabParam && ['options', 'map', 'reviews'].includes(tabParam)) {
      setActiveTab(tabParam);
    }
  }, [location.search]);

  // Get user location if available
  useEffect(() => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setUserLocation({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude
          });
        },
        (error) => {
          console.error('Error getting location:', error);
        }
      );
    }
  }, []);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 10
      }
    }
  };

  // Sample real reviews (normally these would come from a database)
  const reviews = [
    {
      id: 1,
      name: "Sarah Johnson",
      location: "Boston University Dental School",
      rating: 5,
      comment: "I received a complete dental cleaning and x-rays for about 40% less than my regular dentist. The student was supervised closely by their professor the entire time. Highly recommend!",
      avatar: "https://randomuser.me/api/portraits/women/41.jpg",
      date: "May 12, 2023"
    },
    {
      id: 2,
      name: "Marcus Chen",
      location: "UCLA Dental Clinic",
      rating: 4,
      comment: "Great experience overall. It does take longer than a regular dental visit, but the care was excellent and the savings were significant. The facility was modern and clean.",
      avatar: "https://randomuser.me/api/portraits/men/22.jpg",
      date: "March 3, 2023"
    },
    {
      id: 3,
      name: "Elena Rodriguez",
      location: "Community Health Center, Miami",
      rating: 5,
      comment: "The sliding scale fees made dental work affordable for my family. The staff was caring and professional. They worked with us to create a payment plan that fit our budget.",
      avatar: "https://randomuser.me/api/portraits/women/63.jpg",
      date: "June 19, 2023"
    }
  ];

  const renderStars = (rating) => {
    return Array.from({ length: 5 }).map((_, i) => (
      <span key={i} className={`text-xl ${i < rating ? 'text-yellow-400' : 'text-gray-500/30'}`}>
        ★
      </span>
    ));
  };
  
  const handleSchoolSelect = (school) => {
    setSelectedSchool(school);
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'map':
        return (
          <div className="space-y-8">
            <div className="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10">
              <div className="h-[600px] w-full rounded-lg overflow-hidden">
                <DentalSchoolMap 
                  schools={ACCREDITED_SCHOOLS}
                  userLocation={userLocation}
                  onSchoolSelect={handleSchoolSelect}
                />
              </div>
            </div>
            
            {/* Add a subtle recommendation for student dental tools */}
            <RecommendedProducts 
              products={useAffiliateProducts('schoolReviews', 2).products} 
              context="dental students and professionals" 
            />
          </div>
        );
      case 'reviews':
        return (
          <div className="space-y-8">
            <div className="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10">
              <h2 className="text-2xl md:text-3xl font-bold text-white mb-8">
                <span className="bg-gradient-to-r from-purple-400 to-pink-400 text-transparent bg-clip-text">
                  Dental School Patient Reviews
                </span>
              </h2>
              
              <div className="space-y-6">
                {selectedSchool && selectedSchool.reviews ? (
                  // Show selected school reviews if available
                  <div>
                    <h3 className="text-xl font-semibold text-white mb-4">{selectedSchool.name} Reviews</h3>
                    {selectedSchool.reviews.length > 0 ? (
                      selectedSchool.reviews.map(review => (
                        <div key={review.id} className="bg-white/10 rounded-lg p-5 mb-4 border border-white/10">
                          <div className="flex items-start">
                            {review.avatar && (
                              <img src={review.avatar} alt={review.author} className="w-10 h-10 rounded-full mr-4" />
                            )}
                            <div>
                              <h4 className="font-medium text-white">{review.author}</h4>
                              <div className="flex items-center my-1">
                                {renderStars(review.rating)}
                                <span className="ml-2 text-white/50 text-sm">{review.time}</span>
                              </div>
                              <p className="text-white/80 mt-2">{review.text}</p>
                            </div>
                          </div>
                        </div>
                      ))
                    ) : (
                      <p className="text-white/70">No reviews available for this school yet.</p>
                    )}
                  </div>
                ) : (
                  // Show sample reviews
                  <>
                    <p className="text-white/70 text-lg mb-8">
                      Read patient experiences at dental schools and community clinics. These reviews can help you make informed decisions about where to seek affordable dental care.
                    </p>
                    
                    {reviews.map((review) => (
                      <div key={review.id} className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/10 hover:border-purple-400/30 transition-all duration-300">
                        <div className="flex items-start">
                          <img src={review.avatar} alt={review.name} className="w-12 h-12 rounded-full mr-4" />
                          <div className="flex-1">
                            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-2">
                              <h3 className="text-xl font-semibold text-white">{review.name}</h3>
                              <span className="text-white/50 text-sm">{review.date}</span>
                            </div>
                            <p className="text-blue-300 mb-2">{review.location}</p>
                            <div className="flex mb-3">
                              {renderStars(review.rating)}
                            </div>
                            <p className="text-white/80">{review.comment}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                    
                    <div className="text-center mt-8">
                      <button
                        onClick={() => setActiveTab('map')}
                        className="inline-flex items-center px-5 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
                      >
                        Find Schools & View More Reviews
                        <svg className="w-5 h-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                        </svg>
                      </button>
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        );
      case 'options':
        return (
          <>
            {/* Main options section with larger cards */}
            <motion.div 
              variants={itemVariants}
              className="mb-16"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
                {/* Restore the Dental Schools card */}
                <div className="bg-white/10 backdrop-blur-sm rounded-xl border border-white/10 hover:border-blue-400/30 hover:shadow-lg hover:shadow-blue-500/10 transition-all duration-300 p-8 transform hover:-translate-y-2">
                  <div className="flex items-center justify-center w-20 h-20 rounded-full bg-gradient-to-r from-blue-500/20 to-indigo-500/20 mb-6 mx-auto">
                    <span className="text-4xl">🎓</span>
                  </div>
                  <h3 className="text-2xl text-white font-medium text-center mb-4">Dental Schools</h3>
                  <p className="text-white/70 text-lg text-center mb-6">Receive quality care from supervised dental students at significantly reduced costs.</p>
                  <Link 
                    to="/affordable-care?tab=map"
                    className="block w-full py-4 px-6 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors text-center text-lg font-medium"
                  >
                    Find Dental Schools
                  </Link>
                </div>

                <div className="bg-white/10 backdrop-blur-sm rounded-xl border border-white/10 hover:border-emerald-400/30 hover:shadow-lg hover:shadow-emerald-500/10 transition-all duration-300 p-8 transform hover:-translate-y-2">
                  <div className="flex items-center justify-center w-20 h-20 rounded-full bg-gradient-to-r from-green-500/20 to-emerald-500/20 mb-6 mx-auto">
                    <span className="text-4xl">🏥</span>
                  </div>
                  <h3 className="text-2xl text-white font-medium text-center mb-4">Community Health Centers</h3>
                  <p className="text-white/70 text-lg text-center mb-6">Federally qualified health centers offering sliding scale fees based on income.</p>
                  <a 
                    href="https://findahealthcenter.hrsa.gov/" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="block w-full py-4 px-6 bg-emerald-600 hover:bg-emerald-700 text-white rounded-lg transition-colors text-center text-lg font-medium"
                  >
                    Locate Health Centers
                  </a>
                </div>

                {/* Hidden the Reviews & Ratings card as requested */}
                {/* 
                <div className="bg-white/10 backdrop-blur-sm rounded-xl border border-white/10 hover:border-purple-400/30 hover:shadow-lg hover:shadow-purple-500/10 transition-all duration-300 p-8 transform hover:-translate-y-2">
                  <div className="flex items-center justify-center w-20 h-20 rounded-full bg-gradient-to-r from-purple-500/20 to-pink-500/20 mb-6 mx-auto">
                    <span className="text-4xl">⭐</span>
                  </div>
                  <h3 className="text-2xl text-white font-medium text-center mb-4">Reviews & Ratings</h3>
                  <p className="text-white/70 text-lg text-center mb-6">Read patient reviews and ratings for dental schools and community clinics.</p>
                  <button
                    onClick={() => setActiveTab('reviews')}
                    className="block w-full py-4 px-6 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors text-center text-lg font-medium"
                  >
                    View Reviews
                  </button>
                </div>
                */}
              </div>
            </motion.div>

            {/* Additional options section */}
            <motion.div
              variants={itemVariants}
              className="mb-16"
            >
              <SimpleAdCards interval={5000} />
              
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-12 text-center">
                <span className="bg-gradient-to-r from-blue-400 to-cyan-400 text-transparent bg-clip-text">
                  Additional Affordable Care Options
                </span>
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="bg-white/5 backdrop-blur-sm rounded-xl p-8 hover:bg-white/10 transition-colors border border-white/10 hover:border-blue-400/20">
                  <div className="flex items-start">
                    <div className="flex-shrink-0 h-12 w-12 rounded-full bg-blue-500/20 flex items-center justify-center mr-5 mt-1">
                      <svg className="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-2xl font-semibold text-white mb-3">State Dental Programs</h3>
                      <p className="text-white/70 text-lg mb-5">State-specific dental assistance programs for eligible residents. These programs vary by location but often provide comprehensive care for qualifying individuals.</p>
                      <Link 
                        to="/state-dental-programs"
                        className="inline-flex items-center px-5 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                      >
                        Learn More
                        <svg className="w-5 h-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                        </svg>
                      </Link>
                    </div>
                  </div>
                </div>

                <div className="bg-white/5 backdrop-blur-sm rounded-xl p-8 hover:bg-white/10 transition-colors border border-white/10 hover:border-indigo-400/20">
                  <div className="flex items-start">
                    <div className="flex-shrink-0 h-12 w-12 rounded-full bg-indigo-500/20 flex items-center justify-center mr-5 mt-1">
                      <svg className="h-6 w-6 text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-2xl font-semibold text-white mb-3">Non-Profit Organizations</h3>
                      <p className="text-white/70 text-lg mb-5">Organizations providing free or low-cost dental care to those in need. Many focus on underserved communities and emergency dental services.</p>
                      <Link 
                        to="/non-profit-organizations"
                        className="inline-flex items-center px-5 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors"
                      >
                        Learn More
                        <svg className="w-5 h-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                        </svg>
                      </Link>
                    </div>
                  </div>
                </div>

                <div className="bg-white/5 backdrop-blur-sm rounded-xl p-8 hover:bg-white/10 transition-colors border border-white/10 hover:border-purple-400/20">
                  <div className="flex items-start">
                    <div className="flex-shrink-0 h-12 w-12 rounded-full bg-purple-500/20 flex items-center justify-center mr-5 mt-1">
                      <svg className="h-6 w-6 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-2xl font-semibold text-white mb-3">Dental Insurance Plans</h3>
                      <p className="text-white/70 text-lg mb-5">Compare affordable dental insurance options for individuals and families. Find plans that cover preventive, basic, and major services.</p>
                      <Link 
                        to="/dental-insurance-plans"
                        className="inline-flex items-center px-5 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
                      >
                        Learn More
                        <svg className="w-5 h-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                        </svg>
                      </Link>
                    </div>
                  </div>
                </div>

                <div className="bg-white/5 backdrop-blur-sm rounded-xl p-8 hover:bg-white/10 transition-colors border border-white/10 hover:border-teal-400/20">
                  <div className="flex items-start">
                    <div className="flex-shrink-0 h-12 w-12 rounded-full bg-teal-500/20 flex items-center justify-center mr-5 mt-1">
                      <svg className="h-6 w-6 text-teal-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-2xl font-semibold text-white mb-3">Payment Plans</h3>
                      <p className="text-white/70 text-lg mb-5">Learn about dental payment plans and financing options to make dental care more manageable. Find information on interest-free plans and credit options.</p>
                      <Link 
                        to="/payment-plans"
                        className="inline-flex items-center px-5 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-lg transition-colors"
                      >
                        Learn More
                        <svg className="w-5 h-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                        </svg>
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </>
        );
      default:
        return null;
    }
  };

  return (
    <main className="max-w-7xl mx-auto px-4 py-12">
      {/* Page Title */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-center mb-12"
      >
        <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
          <span className="bg-gradient-to-r from-blue-400 via-indigo-400 to-purple-400 text-transparent bg-clip-text">
            Affordable Dental Care Options
          </span>
        </h1>
        <p className="text-xl text-white/70 max-w-3xl mx-auto">
          Find quality dental care at reduced costs through dental schools, community clinics, and other affordable options.
        </p>
      </motion.div>
      
      {/* Tab Navigation */}
      <div className="mb-8 flex justify-center">
        <div className="inline-flex bg-white/5 backdrop-blur-sm rounded-lg p-1 border border-white/10">
          <button
            onClick={() => setActiveTab('options')}
            className={`px-6 py-2.5 text-sm font-medium rounded-md transition-all ${
              activeTab === 'options'
                ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg'
                : 'text-white/70 hover:text-white'
            }`}
          >
            Care Options
          </button>
          <Link
            to="/affordable-care?tab=map"
            className={`px-6 py-2.5 text-sm font-medium rounded-md transition-all ${
              activeTab === 'map'
                ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg'
                : 'text-white/70 hover:text-white'
            }`}
          >
            Find Dental Schools
          </Link>
          {/* Removed Smilo Bites tab as requested */}
          {/* Hidden the Reviews button as requested */}
          {/* 
          <button
            onClick={() => setActiveTab('reviews')}
            className={`px-6 py-2.5 text-sm font-medium rounded-md transition-all ${
              activeTab === 'reviews'
                ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg'
                : 'text-white/70 hover:text-white'
            }`}
          >
            Reviews
          </button>
          */}
        </div>
      </div>
      
      {/* Tab Content */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
        key={activeTab}
        className="mb-16"
      >
        {renderTabContent()}
      </motion.div>
    </main>
  );
} 