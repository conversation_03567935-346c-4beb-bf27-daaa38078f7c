import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { DENTAL_INSURANCE_PLANS } from '../lib/constants/dentalInsurancePlans';

export default function DentalInsurancePlans() {
  const [expandedPlan, setExpandedPlan] = useState(null);
  const [expandedCoverage, setExpandedCoverage] = useState(null);
  
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };
  
  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 10
      }
    }
  };

  // Toggle expanded plan
  const togglePlan = (index) => {
    if (expandedPlan === index) {
      setExpandedPlan(null);
    } else {
      setExpandedPlan(index);
    }
  };

  // Toggle expanded coverage
  const toggleCoverage = (index) => {
    if (expandedCoverage === index) {
      setExpandedCoverage(null);
    } else {
      setExpandedCoverage(index);
    }
  };

  return (
    <div className="py-16 px-4 sm:px-6 lg:px-8 relative">
      {/* Background decorations */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        <div className="absolute top-0 right-0 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 left-20 w-80 h-80 bg-indigo-500/5 rounded-full blur-3xl"></div>
      </div>

      <motion.div 
        className="max-w-7xl mx-auto relative"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Hero Section */}
        <motion.div 
          className="mb-12 text-center"
          variants={itemVariants}
        >
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-blue-400 via-sky-400 to-indigo-400 text-transparent bg-clip-text mb-4">
            Dental Insurance Plans
          </h1>
          <p className="text-xl text-white/80 max-w-3xl mx-auto">
            {DENTAL_INSURANCE_PLANS.description}
          </p>
        </motion.div>

        {/* Plan Types Section */}
        <motion.div 
          className="mb-16"
          variants={itemVariants}
        >
          <h2 className="text-2xl font-bold text-white mb-8 text-center">Types of Dental Insurance Plans</h2>
          <div className="space-y-6">
            {DENTAL_INSURANCE_PLANS.planTypes.map((plan, index) => (
              <motion.div 
                key={index}
                className="bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 overflow-hidden"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <div 
                  className="p-6 cursor-pointer hover:bg-white/5 transition-colors"
                  onClick={() => togglePlan(index)}
                >
                  <div className="flex justify-between items-center">
                    <h3 className="text-xl font-semibold text-white">{plan.type}</h3>
                    <svg 
                      className={`w-5 h-5 text-white/70 transition-transform ${expandedPlan === index ? 'rotate-180' : ''}`} 
                      fill="none" 
                      viewBox="0 0 24 24" 
                      stroke="currentColor"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                  <p className="text-white/70 mt-2">{plan.description}</p>
                </div>
                
                {expandedPlan === index && (
                  <motion.div 
                    className="px-6 pb-6 border-t border-white/10 pt-4"
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    transition={{ duration: 0.3 }}
                  >
                    <div className="grid md:grid-cols-2 gap-6">
                      <div>
                        <h4 className="text-lg font-medium text-white mb-3">Pros</h4>
                        <ul className="space-y-2">
                          {plan.pros.map((pro, i) => (
                            <li key={i} className="flex items-start">
                              <svg className="w-5 h-5 text-green-400 mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                              </svg>
                              <span className="text-white/80">{pro}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                      <div>
                        <h4 className="text-lg font-medium text-white mb-3">Cons</h4>
                        <ul className="space-y-2">
                          {plan.cons.map((con, i) => (
                            <li key={i} className="flex items-start">
                              <svg className="w-5 h-5 text-red-400 mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                              </svg>
                              <span className="text-white/80">{con}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                    <div className="mt-4 pt-4 border-t border-white/10">
                      <h4 className="text-lg font-medium text-white mb-2">Best For</h4>
                      <p className="text-white/80">{plan.bestFor}</p>
                    </div>
                  </motion.div>
                )}
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Coverage Levels Section */}
        <motion.div 
          className="mb-16"
          variants={itemVariants}
        >
          <h2 className="text-2xl font-bold text-white mb-8 text-center">Coverage Levels</h2>
          <div className="space-y-6">
            {DENTAL_INSURANCE_PLANS.coverageLevels.map((coverage, index) => (
              <motion.div 
                key={index}
                className="bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 overflow-hidden"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <div 
                  className="p-6 cursor-pointer hover:bg-white/5 transition-colors"
                  onClick={() => toggleCoverage(index)}
                >
                  <div className="flex justify-between items-center">
                    <h3 className="text-xl font-semibold text-white">{coverage.level}</h3>
                    <svg 
                      className={`w-5 h-5 text-white/70 transition-transform ${expandedCoverage === index ? 'rotate-180' : ''}`} 
                      fill="none" 
                      viewBox="0 0 24 24" 
                      stroke="currentColor"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                  <p className="text-white/70 mt-2">{coverage.description}</p>
                  <p className="text-blue-400 mt-1 font-medium">Average Cost: {coverage.averageCost}</p>
                </div>
                
                {expandedCoverage === index && (
                  <motion.div 
                    className="px-6 pb-6 border-t border-white/10 pt-4"
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    transition={{ duration: 0.3 }}
                  >
                    <h4 className="text-lg font-medium text-white mb-3">Typical Coverage</h4>
                    <ul className="space-y-2">
                      {coverage.typicalCoverage.map((item, i) => (
                        <li key={i} className="flex items-start">
                          <svg className="w-5 h-5 text-blue-400 mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <span className="text-white/80">{item}</span>
                        </li>
                      ))}
                    </ul>
                  </motion.div>
                )}
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Where to Find Insurance Section */}
        <motion.div 
          className="mb-16"
          variants={itemVariants}
        >
          <h2 className="text-2xl font-bold text-white mb-8 text-center">Where to Find Dental Insurance</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {DENTAL_INSURANCE_PLANS.insuranceSources.map((source, index) => (
              <motion.div 
                key={index}
                className="bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <h3 className="text-xl font-semibold text-white mb-2">{source.source}</h3>
                <p className="text-white/70 mb-4">{source.description}</p>
                
                <h4 className="text-lg font-medium text-white mb-2">Tips</h4>
                <ul className="space-y-2">
                  {source.tips.map((tip, i) => (
                    <li key={i} className="flex items-start">
                      <svg className="w-5 h-5 text-blue-400 mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span className="text-white/80">{tip}</span>
                    </li>
                  ))}
                </ul>
                
                {source.link && (
                  <a 
                    href={source.link} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="inline-flex items-center mt-4 text-blue-400 hover:text-blue-300"
                  >
                    Learn More
                    <svg className="w-4 h-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                  </a>
                )}
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Tips for Choosing Section */}
        <motion.div 
          className="mb-16"
          variants={itemVariants}
        >
          <h2 className="text-2xl font-bold text-white mb-8 text-center">Tips for Choosing a Plan</h2>
          <div className="bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {DENTAL_INSURANCE_PLANS.selectionTips.map((tip, index) => (
                <motion.div 
                  key={index}
                  className="bg-white/10 rounded-lg p-4"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <h3 className="text-lg font-medium text-white mb-2">{tip.tip}</h3>
                  <p className="text-white/70">{tip.description}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Resources Section */}
        <motion.div 
          className="mb-16"
          variants={itemVariants}
        >
          <h2 className="text-2xl font-bold text-white mb-8 text-center">Resources</h2>
          <div className="bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10">
            <ul className="space-y-4">
              {DENTAL_INSURANCE_PLANS.resources.map((resource, index) => (
                <li key={index} className="flex items-start">
                  <div className="flex-shrink-0 h-6 w-6 rounded-full bg-blue-500/20 flex items-center justify-center mr-3 mt-0.5">
                    <svg className="h-4 w-4 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="text-white font-medium">{resource.name}</h4>
                    <p className="text-white/70">{resource.description}</p>
                    <a href={resource.link} target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:text-blue-300">
                      Visit Website
                    </a>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        </motion.div>

        {/* CTA Section */}
        <motion.div 
          className="mt-12 text-center"
          variants={itemVariants}
        >
          <h2 className="text-2xl font-bold text-white mb-4">Explore More Affordable Care Options</h2>
          <p className="text-white/80 mb-6">
            Discover other ways to make dental care more affordable for you and your family.
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <Link 
              to="/state-dental-programs" 
              className="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition"
            >
              State Dental Programs
            </Link>
            <Link 
              to="/non-profit-organizations" 
              className="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition"
            >
              Non-Profit Organizations
            </Link>
            <Link 
              to="/payment-plans" 
              className="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition"
            >
              Payment Plans
            </Link>
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
}
