import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { STATE_DENTAL_PROGRAMS } from '../lib/constants/stateDentalPrograms';

export default function StateDentalPrograms() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedState, setSelectedState] = useState(null);
  
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };
  
  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 10
      }
    }
  };

  // Filter states based on search term
  const filteredStates = STATE_DENTAL_PROGRAMS.statePrograms.filter(state => 
    state.state.toLowerCase().includes(searchTerm.toLowerCase())
  );
  
  // Get programs for selected state
  const selectedStatePrograms = selectedState 
    ? STATE_DENTAL_PROGRAMS.statePrograms.find(state => state.state === selectedState)?.programs 
    : [];

  // Render icon based on icon name
  const renderIcon = (iconName) => {
    switch(iconName) {
      case 'government':
        return (
          <svg className="w-8 h-8 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
          </svg>
        );
      case 'child':
        return (
          <svg className="w-8 h-8 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
          </svg>
        );
      case 'map':
        return (
          <svg className="w-8 h-8 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
          </svg>
        );
      default:
        return (
          <svg className="w-8 h-8 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
    }
  };

  return (
    <div className="py-16 px-4 sm:px-6 lg:px-8 relative">
      {/* Background decorations */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        <div className="absolute top-0 right-0 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 left-20 w-80 h-80 bg-indigo-500/5 rounded-full blur-3xl"></div>
      </div>

      <motion.div 
        className="max-w-7xl mx-auto relative"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Hero Section */}
        <motion.div 
          className="mb-12 text-center"
          variants={itemVariants}
        >
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-blue-400 via-sky-400 to-indigo-400 text-transparent bg-clip-text mb-4">
            State Dental Programs
          </h1>
          <p className="text-xl text-white/80 max-w-3xl mx-auto">
            {STATE_DENTAL_PROGRAMS.description}
          </p>
        </motion.div>

        {/* Featured Programs Section */}
        <motion.div 
          className="mb-16"
          variants={itemVariants}
        >
          <h2 className="text-2xl font-bold text-white mb-8 text-center">Featured Programs</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {STATE_DENTAL_PROGRAMS.featuredPrograms.map((program, index) => (
              <motion.div 
                key={index}
                className="bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10 hover:border-blue-500/30 transition-all duration-300"
                whileHover={{ y: -5 }}
                transition={{ type: "spring", stiffness: 300, damping: 10 }}
              >
                <div className="mb-4">
                  {renderIcon(program.icon)}
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">{program.name}</h3>
                <p className="text-white/70 mb-4">{program.description}</p>
                <a 
                  href={program.link} 
                  className="text-blue-400 hover:text-blue-300 inline-flex items-center"
                >
                  Learn More
                  <svg className="w-4 h-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </svg>
                </a>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* State Programs Section */}
        <motion.div 
          className="mb-16"
          variants={itemVariants}
          id="state-list"
        >
          <h2 className="text-2xl font-bold text-white mb-8 text-center">Find Programs in Your State</h2>
          
          {/* Search input */}
          <div className="max-w-md mx-auto mb-8">
            <div className="relative">
              <input
                type="text"
                placeholder="Search for your state..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full bg-white/10 border border-white/20 rounded-lg py-3 px-4 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500/50"
              />
              <svg className="absolute right-3 top-3.5 w-5 h-5 text-white/50" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
          </div>
          
          {/* State grid */}
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 mb-8">
            {filteredStates.map((state, index) => (
              <motion.button
                key={index}
                className={`p-4 rounded-lg text-center transition-all ${
                  selectedState === state.state
                    ? 'bg-blue-600 text-white'
                    : 'bg-white/5 text-white/80 hover:bg-white/10'
                }`}
                onClick={() => setSelectedState(state.state)}
                whileHover={{ scale: 1.03 }}
                whileTap={{ scale: 0.98 }}
              >
                {state.state}
              </motion.button>
            ))}
          </div>
          
          {/* Selected state programs */}
          {selectedState && (
            <motion.div
              className="bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ type: "spring", stiffness: 100, damping: 10 }}
            >
              <h3 className="text-xl font-semibold text-white mb-4">{selectedState} Dental Programs</h3>
              <div className="space-y-6">
                {selectedStatePrograms.map((program, index) => (
                  <div key={index} className="border-b border-white/10 pb-4 last:border-0">
                    <h4 className="text-lg font-medium text-white mb-2">{program.name}</h4>
                    <p className="text-white/70 mb-2">{program.description}</p>
                    <p className="text-white/70 mb-2"><span className="text-blue-400">Eligibility:</span> {program.eligibility}</p>
                    <a 
                      href={program.website} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-blue-400 hover:text-blue-300 inline-flex items-center"
                    >
                      Visit Website
                      <svg className="w-4 h-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                      </svg>
                    </a>
                  </div>
                ))}
              </div>
            </motion.div>
          )}
        </motion.div>

        {/* Resources Section */}
        <motion.div 
          className="mb-16"
          variants={itemVariants}
        >
          <h2 className="text-2xl font-bold text-white mb-8 text-center">Additional Resources</h2>
          <div className="bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10">
            <ul className="space-y-4">
              {STATE_DENTAL_PROGRAMS.resources.map((resource, index) => (
                <li key={index} className="flex items-start">
                  <div className="flex-shrink-0 h-6 w-6 rounded-full bg-blue-500/20 flex items-center justify-center mr-3 mt-0.5">
                    <svg className="h-4 w-4 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="text-white font-medium">{resource.name}</h4>
                    <p className="text-white/70">{resource.description}</p>
                    <a href={resource.link} target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:text-blue-300">
                      Visit Website
                    </a>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        </motion.div>

        {/* CTA Section */}
        <motion.div 
          className="mt-12 text-center"
          variants={itemVariants}
        >
          <h2 className="text-2xl font-bold text-white mb-4">Explore More Affordable Care Options</h2>
          <p className="text-white/80 mb-6">
            Discover other ways to make dental care more affordable for you and your family.
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <Link 
              to="/non-profit-organizations" 
              className="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition"
            >
              Non-Profit Organizations
            </Link>
            <Link 
              to="/dental-insurance-plans" 
              className="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition"
            >
              Dental Insurance Plans
            </Link>
            <Link 
              to="/payment-plans" 
              className="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition"
            >
              Payment Plans
            </Link>
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
}
