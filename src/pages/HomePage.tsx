import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import NewSignInButton from '../components/auth/NewSignInButton';

const HomePage: React.FC = () => {
  return (
    <div>
      {/* Hero Section */}
      <section className="py-12 mb-12">
        <div className="max-w-5xl mx-auto text-center">
          <motion.h1
            className="text-4xl md:text-6xl font-bold text-white mb-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            Smart Dental Care for Everyone
          </motion.h1>
          <motion.p
            className="text-xl text-gray-300 mb-10 max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            Connect patients with providers, enhance your dental health knowledge,
            and transform your dental practice with our cutting-edge platform.
          </motion.p>

          <motion.div
            className="flex flex-wrap justify-center gap-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <NewSignInButton
              userType="patient"
              className="px-8 py-3 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-lg text-white font-medium shadow-lg hover:shadow-cyan-500/20 transition"
            >
              Get Started
            </NewSignInButton>
            {/* Temporarily hidden until ready
            <Link
              to="/tools/mirror-coach"
              className="px-8 py-3 bg-gray-800 rounded-lg text-white font-medium shadow-lg hover:bg-gray-700 transition"
            >
              Try AI Tools
            </Link>
            */}
          </motion.div>
        </div>
      </section>

      {/* Feature Boxes */}
      <section className="mb-20">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          <FeatureBox
            title="Find Your Perfect Dental Match"
            description="Our advanced matching system connects patients with the right dental providers based on needs, location, and insurance."
            icon={
              <svg className="w-10 h-10 text-cyan-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            }
            linkTo="/matching/find-provider"
            delay={0}
          />

          {/* Temporarily hidden until ready
          <FeatureBox
            title="AI-Powered Dental Tools"
            description="Transform your dental care routine with our suite of AI tools, from brushing coaches to oral scanners."
            icon={
              <svg className="w-10 h-10 text-cyan-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            }
            linkTo="/tools"
            delay={0.2}
          />
          */}

          <FeatureBox
            title="Lead Marketplace"
            description="For dental practices: Find and bid on high-quality patient leads that match your specialty and location."
            icon={
              <svg className="w-10 h-10 text-cyan-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            }
            linkTo="/leads/marketplace"
            delay={0.4}
          />
        </div>
      </section>

      {/* Dashboard Showcase */}
      <section className="mb-20">
        <div className="max-w-6xl mx-auto">
          <div className="mb-12 text-center">
            <h2 className="text-3xl font-bold text-white mb-4">Personalized Dashboards</h2>
            <p className="text-gray-300 max-w-2xl mx-auto">
              Whether you're a patient or a dental provider, Smilo.Dental offers tailored dashboards to manage
              your dental health or practice efficiently.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-10">
            <DashboardCard
              title="Patient Dashboard"
              features={[
                "Track your dental appointments",
                "View treatment plans",
                "Access oral health reports",
                "Connect with dental providers"
              ]}
              linkTo="/dashboards/patient"
              image="/images/patient-dashboard.jpg"
              delay={0}
            />

            <DashboardCard
              title="Practice Dashboard"
              features={[
                "Manage patient leads",
                "Track bidding activity",
                "View analytics and insights",
                "Optimize your practice"
              ]}
              linkTo="/dashboards/practice"
              image="/images/practice-dashboard.jpg"
              delay={0.3}
            />
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="mb-16">
        <div className="max-w-4xl mx-auto text-center bg-gradient-to-r from-cyan-900/50 to-blue-900/50 rounded-2xl p-10 border border-cyan-800/30">
          <h2 className="text-3xl font-bold text-white mb-4">Ready to Transform Your Dental Experience?</h2>
          <p className="text-gray-300 mb-8">
            Join Smilo.Dental today and experience the future of dental care—where patients find their perfect
            match and providers grow their practice.
          </p>
          <NewSignInButton
            userType="patient"
            className="px-8 py-3 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-lg text-white font-medium shadow-lg hover:shadow-cyan-500/20 transition"
          >
            Create Your Account
          </NewSignInButton>
        </div>
      </section>
    </div>
  );
};

// Feature Box Component
const FeatureBox: React.FC<{
  title: string;
  description: string;
  icon: React.ReactNode;
  linkTo: string;
  delay: number;
}> = ({ title, description, icon, linkTo, delay }) => {
  return (
    <motion.div
      className="bg-gray-800/50 rounded-xl p-6 border border-gray-700 hover:border-cyan-700 transition"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay }}
    >
      <div className="w-16 h-16 bg-cyan-900/30 rounded-full flex items-center justify-center mb-4">
        {icon}
      </div>
      <h3 className="text-xl font-semibold text-white mb-3">{title}</h3>
      <p className="text-gray-300 mb-5">{description}</p>
      <Link
        to={linkTo}
        className="inline-flex items-center text-cyan-400 hover:text-cyan-300 transition"
      >
        Learn more
        <svg className="w-4 h-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
        </svg>
      </Link>
    </motion.div>
  );
};

// Dashboard Card Component
const DashboardCard: React.FC<{
  title: string;
  features: string[];
  linkTo: string;
  image: string;
  delay: number;
}> = ({ title, features, linkTo, image, delay }) => {
  return (
    <motion.div
      className="bg-gray-800/50 rounded-xl overflow-hidden border border-gray-700"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay }}
    >
      <div className="h-52 bg-gray-700 relative">
        <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-t from-gray-900 to-transparent">
          <h3 className="text-2xl font-bold text-white">{title}</h3>
        </div>
      </div>
      <div className="p-6">
        <ul className="space-y-2 mb-6">
          {features.map((feature, index) => (
            <li key={index} className="flex items-center">
              <svg className="w-5 h-5 text-cyan-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <span className="text-gray-300">{feature}</span>
            </li>
          ))}
        </ul>
        <Link
          to={linkTo}
          className="w-full flex items-center justify-center px-4 py-2 bg-gradient-to-r from-cyan-600 to-blue-600 rounded-lg text-white font-medium shadow-md hover:shadow-lg hover:shadow-cyan-500/20 transition"
        >
          Access Dashboard
        </Link>
      </div>
    </motion.div>
  );
};

export default HomePage;