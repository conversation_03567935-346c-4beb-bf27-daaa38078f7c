import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import { useRouter } from 'next/router';

export default function NDAAcceptances() {
  const router = useRouter();
  const [acceptances, setAcceptances] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedAcceptance, setSelectedAcceptance] = useState(null);
  const [filter, setFilter] = useState('pending'); // pending, approved, rejected

  useEffect(() => {
    checkAdminAuth();
    loadAcceptances();
  }, [filter]);

  const checkAdminAuth = async () => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user || !user.email.endsWith('@smilo.dental')) {
      router.push('/');
    }
  };

  const loadAcceptances = async () => {
    try {
      let query = supabase
        .from('nda_acceptances')
        .select('*')
        .order('created_at', { ascending: false });

      if (filter === 'pending') {
        query = query.is('is_approved', null);
      } else if (filter === 'approved') {
        query = query.eq('is_approved', true);
      } else if (filter === 'rejected') {
        query = query.eq('is_approved', false);
      }

      const { data, error } = await query;
      if (error) throw error;
      setAcceptances(data);
    } catch (error) {
      console.error('Error loading acceptances:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleApprove = async (id) => {
    try {
      const { error } = await supabase
        .from('nda_acceptances')
        .update({ 
          is_approved: true,
          approved_at: new Date().toISOString(),
          approved_by: (await supabase.auth.getUser()).data.user.email
        })
        .eq('id', id);

      if (error) throw error;

      // Send approval email
      await fetch('/api/notify-approval', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          agreementId: selectedAcceptance.agreement_id,
          email: selectedAcceptance.email
        })
      });

      loadAcceptances();
    } catch (error) {
      console.error('Error approving acceptance:', error);
    }
  };

  const handleReject = async (id) => {
    try {
      const { error } = await supabase
        .from('nda_acceptances')
        .update({ 
          is_approved: false,
          rejected_at: new Date().toISOString(),
          rejected_by: (await supabase.auth.getUser()).data.user.email
        })
        .eq('id', id);

      if (error) throw error;

      // Send rejection email
      await fetch('/api/notify-rejection', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          agreementId: selectedAcceptance.agreement_id,
          email: selectedAcceptance.email
        })
      });

      loadAcceptances();
    } catch (error) {
      console.error('Error rejecting acceptance:', error);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-900 via-blue-900/20 to-indigo-900/20 p-8">
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-white">NDA Acceptances</h1>
          <div className="flex space-x-4">
            <button
              onClick={() => setFilter('pending')}
              className={`px-4 py-2 rounded-lg ${
                filter === 'pending'
                  ? 'bg-yellow-500 text-white'
                  : 'bg-gray-700 text-gray-300'
              }`}
            >
              Pending
            </button>
            <button
              onClick={() => setFilter('approved')}
              className={`px-4 py-2 rounded-lg ${
                filter === 'approved'
                  ? 'bg-green-500 text-white'
                  : 'bg-gray-700 text-gray-300'
              }`}
            >
              Approved
            </button>
            <button
              onClick={() => setFilter('rejected')}
              className={`px-4 py-2 rounded-lg ${
                filter === 'rejected'
                  ? 'bg-red-500 text-white'
                  : 'bg-gray-700 text-gray-300'
              }`}
            >
              Rejected
            </button>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
          </div>
        ) : (
          <div className="grid gap-6">
            {acceptances.map((acceptance) => (
              <div
                key={acceptance.id}
                className="bg-gray-800/50 rounded-lg p-6 border border-gray-700 hover:border-indigo-500/50 transition-all duration-300"
              >
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="text-xl font-semibold text-white mb-2">
                      {acceptance.full_name}
                    </h3>
                    <div className="grid grid-cols-2 gap-4 text-sm text-gray-300">
                      <div>
                        <p><span className="text-gray-500">Email:</span> {acceptance.email}</p>
                        <p><span className="text-gray-500">Practice:</span> {acceptance.company}</p>
                        <p><span className="text-gray-500">Title:</span> {acceptance.title}</p>
                        <p><span className="text-gray-500">License #:</span> {acceptance.dentist_license_number}</p>
                      </div>
                      <div>
                        <p><span className="text-gray-500">NPI:</span> {acceptance.practice_npi}</p>
                        <p><span className="text-gray-500">State:</span> {acceptance.state_of_practice}</p>
                        <p><span className="text-gray-500">Years in Practice:</span> {acceptance.years_in_practice}</p>
                        <p><span className="text-gray-500">LinkedIn:</span> {acceptance.linkedin_profile}</p>
                      </div>
                    </div>
                  </div>
                  <div className="flex space-x-4">
                    {acceptance.is_approved === null && (
                      <>
                        <button
                          onClick={() => handleApprove(acceptance.id)}
                          className="px-4 py-2 bg-green-600 hover:bg-green-500 text-white rounded-lg transition-all duration-300"
                        >
                          Approve
                        </button>
                        <button
                          onClick={() => handleReject(acceptance.id)}
                          className="px-4 py-2 bg-red-600 hover:bg-red-500 text-white rounded-lg transition-all duration-300"
                        >
                          Reject
                        </button>
                      </>
                    )}
                    <button
                      onClick={() => setSelectedAcceptance(acceptance)}
                      className="px-4 py-2 bg-blue-600 hover:bg-blue-500 text-white rounded-lg transition-all duration-300"
                    >
                      Details
                    </button>
                  </div>
                </div>

                <div className="mt-4 pt-4 border-t border-gray-700">
                  <div className="grid grid-cols-2 gap-4 text-sm text-gray-400">
                    <div>
                      <p><span className="text-gray-500">IP Address:</span> {acceptance.ip_address}</p>
                      <p><span className="text-gray-500">Browser:</span> {acceptance.user_agent}</p>
                      <p><span className="text-gray-500">Platform:</span> {acceptance.platform}</p>
                    </div>
                    <div>
                      <p><span className="text-gray-500">Submitted:</span> {new Date(acceptance.created_at).toLocaleString()}</p>
                      <p><span className="text-gray-500">Agreement ID:</span> {acceptance.agreement_id}</p>
                      <p><span className="text-gray-500">Version:</span> {acceptance.agreement_version}</p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Details Modal */}
        {selectedAcceptance && (
          <div className="fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center p-4">
            <div className="bg-gray-800 rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold text-white">Detailed Information</h2>
                <button
                  onClick={() => setSelectedAcceptance(null)}
                  className="text-gray-400 hover:text-white"
                >
                  ✕
                </button>
              </div>
              
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-4">Personal Information</h3>
                    <div className="space-y-2 text-gray-300">
                      <p><span className="text-gray-500">Full Name:</span> {selectedAcceptance.full_name}</p>
                      <p><span className="text-gray-500">Email:</span> {selectedAcceptance.email}</p>
                      <p><span className="text-gray-500">Practice:</span> {selectedAcceptance.company}</p>
                      <p><span className="text-gray-500">Title:</span> {selectedAcceptance.title}</p>
                      <p><span className="text-gray-500">License #:</span> {selectedAcceptance.dentist_license_number}</p>
                      <p><span className="text-gray-500">NPI:</span> {selectedAcceptance.practice_npi}</p>
                      <p><span className="text-gray-500">State:</span> {selectedAcceptance.state_of_practice}</p>
                      <p><span className="text-gray-500">Years in Practice:</span> {selectedAcceptance.years_in_practice}</p>
                      <p><span className="text-gray-500">LinkedIn:</span> {selectedAcceptance.linkedin_profile}</p>
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-4">Technical Information</h3>
                    <div className="space-y-2 text-gray-300">
                      <p><span className="text-gray-500">IP Address:</span> {selectedAcceptance.ip_address}</p>
                      <p><span className="text-gray-500">Browser:</span> {selectedAcceptance.user_agent}</p>
                      <p><span className="text-gray-500">Platform:</span> {selectedAcceptance.platform}</p>
                      <p><span className="text-gray-500">Language:</span> {selectedAcceptance.language}</p>
                      <p><span className="text-gray-500">Screen Resolution:</span> {selectedAcceptance.screen_resolution}</p>
                      <p><span className="text-gray-500">Timezone:</span> {selectedAcceptance.timezone}</p>
                      <p><span className="text-gray-500">Device Fingerprint:</span> {selectedAcceptance.device_fingerprint}</p>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-white mb-4">Agreement Information</h3>
                  <div className="space-y-2 text-gray-300">
                    <p><span className="text-gray-500">Agreement ID:</span> {selectedAcceptance.agreement_id}</p>
                    <p><span className="text-gray-500">Version:</span> {selectedAcceptance.agreement_version}</p>
                    <p><span className="text-gray-500">Submitted:</span> {new Date(selectedAcceptance.created_at).toLocaleString()}</p>
                    <p><span className="text-gray-500">Status:</span> 
                      <span className={`ml-2 px-2 py-1 rounded-full text-xs ${
                        selectedAcceptance.is_approved === null
                          ? 'bg-yellow-500/20 text-yellow-300'
                          : selectedAcceptance.is_approved
                          ? 'bg-green-500/20 text-green-300'
                          : 'bg-red-500/20 text-red-300'
                      }`}>
                        {selectedAcceptance.is_approved === null
                          ? 'Pending'
                          : selectedAcceptance.is_approved
                          ? 'Approved'
                          : 'Rejected'}
                      </span>
                    </p>
                    {selectedAcceptance.approved_at && (
                      <p><span className="text-gray-500">Approved:</span> {new Date(selectedAcceptance.approved_at).toLocaleString()} by {selectedAcceptance.approved_by}</p>
                    )}
                    {selectedAcceptance.rejected_at && (
                      <p><span className="text-gray-500">Rejected:</span> {new Date(selectedAcceptance.rejected_at).toLocaleString()} by {selectedAcceptance.rejected_by}</p>
                    )}
                  </div>
                </div>

                <div className="pt-6 border-t border-gray-700">
                  <div className="flex justify-end space-x-4">
                    {selectedAcceptance.is_approved === null && (
                      <>
                        <button
                          onClick={() => {
                            handleApprove(selectedAcceptance.id);
                            setSelectedAcceptance(null);
                          }}
                          className="px-6 py-2 bg-green-600 hover:bg-green-500 text-white rounded-lg transition-all duration-300"
                        >
                          Approve Access
                        </button>
                        <button
                          onClick={() => {
                            handleReject(selectedAcceptance.id);
                            setSelectedAcceptance(null);
                          }}
                          className="px-6 py-2 bg-red-600 hover:bg-red-500 text-white rounded-lg transition-all duration-300"
                        >
                          Reject Access
                        </button>
                      </>
                    )}
                    <button
                      onClick={() => setSelectedAcceptance(null)}
                      className="px-6 py-2 bg-gray-600 hover:bg-gray-500 text-white rounded-lg transition-all duration-300"
                    >
                      Close
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 