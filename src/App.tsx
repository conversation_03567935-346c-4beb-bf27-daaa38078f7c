import React from 'react';
import { <PERSON><PERSON>er<PERSON>outer, Routes, Route, Navigate, useLocation } from 'react-router-dom';
import AppLayout from './components/layout/AppLayout';
import { AnimatePresence } from 'framer-motion';

// Auth Components
import UserTypeSelection from './components/auth/UserTypeSelection';
import SignInPage from './pages/auth/SignInPage';
import StandaloneSignIn from './pages/auth/StandaloneSignIn';

// Dashboard Components
import PatientDashboard from './components/dashboard/patient/PatientDashboard';

// Lead Marketplace
import LeadBiddingExample from './pages/leads/LeadBiddingExample';

// AI Tools
import MirrorModeCoachDemo from './pages/tools/MirrorModeCoachDemo';

// Landing/Home
import HomePage from './pages/HomePage';

// Test Page
import TestPage from './pages/TestPage';

// Telehealth
import VirtualConsultationsPage from './pages/telehealth/VirtualConsultationsPage';

// Resources
import ArticlesResourcesPage from './pages/resources/ArticlesResourcesPage';
import ExpertResourcesPage from './components/resources/ExpertResourcesPage';

// Auth Modal Context
import { AuthModalProvider, useAuthModal } from './contexts/AuthModalContext';

// App Routes component with modal handling
const AppRoutes = () => {
  const location = useLocation();
  const { isOpen, userType } = useAuthModal();

  // Store the location to use as the prev location for the modal
  const backgroundLocation = location.state?.backgroundLocation || location;

  return (
    <>
      <Routes location={backgroundLocation}>
        {/* Authentication Routes */}
        <Route path="/auth/user-type" element={<UserTypeSelection />} />

        {/* New standalone sign-in pages */}
        <Route path="/auth/sign-in/:userType" element={<StandaloneSignIn />} />

        {/* Test Route */}
        <Route path="/test" element={<TestPage />} />

        {/* Main Application Routes with AppLayout */}
        <Route path="/" element={<Navigate to="/home" replace />} />
        <Route path="/home" element={<AppLayout><HomePage /></AppLayout>} />

        {/* Dashboard Routes */}
        <Route path="/dashboards/patient" element={
          <AppLayout><PatientDashboard /></AppLayout>
        } />
        <Route path="/dashboards/practice" element={
          <AppLayout><ComingSoon title="Practice Dashboard" /></AppLayout>
        } />
        <Route path="/dashboards/analytics" element={
          <AppLayout><ComingSoon title="Analytics Dashboard" /></AppLayout>
        } />

        {/* Lead Marketplace Routes */}
        <Route path="/leads/marketplace" element={
          <AppLayout><LeadBiddingExample /></AppLayout>
        } />
        <Route path="/leads/my-bids" element={
          <AppLayout><ComingSoon title="My Bids" /></AppLayout>
        } />

        {/* AI Tools Routes - Temporarily hidden until ready
        <Route path="/tools" element={
          <AppLayout><ComingSoon title="AI Tools Hub" /></AppLayout>
        } />
        <Route path="/tools/mirror-coach" element={
          <AppLayout><MirrorModeCoachDemo /></AppLayout>
        } />
        <Route path="/tools/smilo-brush" element={
          <AppLayout><ComingSoon title="SmiloBrush Integration" /></AppLayout>
        } />
        <Route path="/tools/oral-scanner" element={
          <AppLayout><ComingSoon title="Oral Scanner" /></AppLayout>
        } />
        */}

        {/* Patient Matching Routes */}
        <Route path="/matching/find-provider" element={
          <AppLayout><ComingSoon title="Find a Provider" /></AppLayout>
        } />
        <Route path="/matching/providers" element={
          <AppLayout><ComingSoon title="Provider Directory" /></AppLayout>
        } />

        {/* Partnerships Route */}
        <Route path="/partnerships" element={
          <AppLayout><ComingSoon title="Partnership Opportunities" /></AppLayout>
        } />

        {/* Other Routes */}
        <Route path="/terms" element={
          <AppLayout><ComingSoon title="Terms of Service" /></AppLayout>
        } />
        <Route path="/privacy" element={
          <AppLayout><ComingSoon title="Privacy Policy" /></AppLayout>
        } />
        <Route path="/contact" element={
          <AppLayout><ComingSoon title="Contact Us" /></AppLayout>
        } />

        {/* Telehealth Routes - Temporarily hidden until ready
        <Route path="/telehealth/consultations" element={
          <AppLayout><VirtualConsultationsPage /></AppLayout>
        } />
        */}

        {/* Resources Routes */}
        <Route path="/resources" element={
          <AppLayout><ExpertResourcesPage /></AppLayout>
        } />
        <Route path="/resources/articles" element={
          <AppLayout><ArticlesResourcesPage /></AppLayout>
        } />

        {/* Default Route */}
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>

      {/* Modal Sign-in routes rendered on top of the current page */}
      {backgroundLocation !== location && (
        <Routes>
          <Route path="/auth/patient-sign-in" element={<SignInPage userType="patient" />} />
          <Route path="/auth/practice-sign-in" element={<SignInPage userType="practice" />} />
        </Routes>
      )}

      {/* Render modal directly based on context state */}
      {isOpen && (
        <div className="modal-container">
          <SignInPage userType={userType} />
        </div>
      )}
    </>
  );
};

const App: React.FC = () => {
  return (
    <BrowserRouter>
      <AuthModalProvider>
        <AppRoutes />
      </AuthModalProvider>
    </BrowserRouter>
  );
};

// Simple coming soon component for placeholder pages
const ComingSoon: React.FC<{title: string}> = ({ title }) => {
  return (
    <div className="flex flex-col items-center justify-center py-20">
      <h1 className="text-3xl font-bold text-white mb-4">{title}</h1>
      <div className="w-24 h-24 bg-cyan-900/30 rounded-full flex items-center justify-center mb-6">
        <svg className="w-12 h-12 text-cyan-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      </div>
      <p className="text-xl text-gray-300 mb-8 text-center">
        This feature is coming soon. We're working hard to bring it to you!
      </p>
      <p className="text-gray-400 max-w-md text-center">
        Check back later for updates or contact our team for more information on when this feature will be available.
      </p>
    </div>
  );
};

export default App;