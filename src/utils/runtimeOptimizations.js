/**
 * Runtime Performance Optimization System
 * Provides intelligent performance monitoring and adaptive optimizations
 * for enhanced user experience across all devices and browsers
 */

let performanceState = {
  isOptimized: false,
  currentFPS: 60,
  averageFrameTime: 16.67,
  memoryUsage: 0,
  adaptiveLevel: 'normal',
  monitoringEnabled: false
};

/**
 * Initialize runtime performance optimizations
 * This is the main function that sets up all performance monitoring and optimizations
 */
export const initializeRuntimeOptimizations = () => {
  if (typeof window === 'undefined') return;

  console.log('🚀 Initializing runtime performance optimizations...');

  // Start adaptive performance monitoring
  startAdaptivePerformanceMonitoring();

  // Apply immediate optimizations based on device
  applyDeviceBasedOptimizations();

  // Setup memory management
  setupMemoryManagement();

  // Initialize lazy loading observers
  initializeLazyLoading();

  // Setup event optimization
  optimizeEventHandlers();

  // Apply browser-specific optimizations
  applyBrowserOptimizations();

  console.log('✅ Runtime optimizations initialized');
};

/**
 * Start adaptive performance monitoring that adjusts optimizations in real-time
 */
const startAdaptivePerformanceMonitoring = () => {
  if (performanceState.monitoringEnabled) return;

  performanceState.monitoringEnabled = true;
  let frameCount = 0;
  let lastFrameTime = performance.now();
  const frameTimes = [];
  const maxFrameTimes = 60; // Monitor last 60 frames

  const monitorFrame = (currentTime) => {
    if (!performanceState.monitoringEnabled) return;

    const deltaTime = currentTime - lastFrameTime;
    frameTimes.push(deltaTime);

    if (frameTimes.length > maxFrameTimes) {
      frameTimes.shift();
    }

    frameCount++;

    // Analyze performance every 60 frames (about 1 second at 60fps)
    if (frameCount % 60 === 0) {
      const averageFrameTime = frameTimes.reduce((a, b) => a + b, 0) / frameTimes.length;
      const currentFPS = 1000 / averageFrameTime;
      
      performanceState.currentFPS = currentFPS;
      performanceState.averageFrameTime = averageFrameTime;

      // Apply adaptive optimizations based on performance
      applyAdaptiveOptimizations(currentFPS, averageFrameTime);

      // Log performance metrics periodically
      if (frameCount % 300 === 0) {
        console.log(`Performance: ${currentFPS.toFixed(1)} FPS (${averageFrameTime.toFixed(1)}ms frame time)`);
      }
    }

    lastFrameTime = currentTime;
    requestAnimationFrame(monitorFrame);
  };

  requestAnimationFrame(monitorFrame);
};

/**
 * Apply adaptive optimizations based on current performance metrics
 */
const applyAdaptiveOptimizations = (fps, frameTime) => {
  const html = document.documentElement;
  
  if (fps < 25) {
    // Critical performance - maximum optimizations
    if (performanceState.adaptiveLevel !== 'critical') {
      console.log('⚠️ Critical performance detected, applying maximum optimizations');
      html.classList.add('performance-critical', 'reduced-animations', 'minimal-effects');
      html.classList.remove('performance-good', 'performance-medium');
      performanceState.adaptiveLevel = 'critical';
      disableNonEssentialFeatures();
    }
  } else if (fps < 40) {
    // Poor performance - moderate optimizations
    if (performanceState.adaptiveLevel !== 'poor') {
      console.log('⚠️ Poor performance detected, applying moderate optimizations');
      html.classList.add('performance-poor', 'reduced-animations');
      html.classList.remove('performance-critical', 'performance-good', 'minimal-effects');
      performanceState.adaptiveLevel = 'poor';
    }
  } else if (fps < 50) {
    // Medium performance - light optimizations
    if (performanceState.adaptiveLevel !== 'medium') {
      console.log('📊 Medium performance detected, applying light optimizations');
      html.classList.add('performance-medium');
      html.classList.remove('performance-critical', 'performance-poor', 'reduced-animations', 'minimal-effects');
      performanceState.adaptiveLevel = 'medium';
      enableOptimalFeatures();
    }
  } else {
    // Good performance - full features
    if (performanceState.adaptiveLevel !== 'good') {
      console.log('✅ Good performance detected, enabling full features');
      html.classList.add('performance-good');
      html.classList.remove('performance-critical', 'performance-poor', 'performance-medium', 'reduced-animations', 'minimal-effects');
      performanceState.adaptiveLevel = 'good';
      enableOptimalFeatures();
    }
  }
};

/**
 * Apply device-based optimizations immediately on initialization
 */
const applyDeviceBasedOptimizations = () => {
  const html = document.documentElement;

  // Memory-based optimizations
  if (navigator.deviceMemory) {
    if (navigator.deviceMemory <= 2) {
      html.classList.add('low-memory-device');
    } else if (navigator.deviceMemory <= 4) {
      html.classList.add('medium-memory-device');
    } else {
      html.classList.add('high-memory-device');
    }
  }

  // CPU-based optimizations
  if (navigator.hardwareConcurrency) {
    if (navigator.hardwareConcurrency <= 2) {
      html.classList.add('low-cpu-device');
    } else if (navigator.hardwareConcurrency <= 4) {
      html.classList.add('medium-cpu-device');
    } else {
      html.classList.add('high-cpu-device');
    }
  }

  // Network-based optimizations
  if (navigator.connection) {
    const connectionType = navigator.connection.effectiveType;
    html.classList.add(`connection-${connectionType}`);
    
    if (['slow-2g', '2g'].includes(connectionType)) {
      html.classList.add('very-slow-connection');
      enableDataSaverMode();
    } else if (connectionType === '3g') {
      html.classList.add('slow-connection');
    }
  }
};

/**
 * Setup memory management to prevent memory leaks
 */
const setupMemoryManagement = () => {
  // Monitor memory usage if available
  if (performance.memory) {
    const checkMemoryUsage = () => {
      const memoryInfo = performance.memory;
      const usedMB = memoryInfo.usedJSHeapSize / 1024 / 1024;
      
      performanceState.memoryUsage = usedMB;

      // If memory usage is high, trigger cleanup
      if (usedMB > 100) { // More than 100MB
        triggerMemoryCleanup();
      }
    };

    setInterval(checkMemoryUsage, 30000); // Check every 30 seconds
  }

  // Setup cleanup on page unload
  window.addEventListener('beforeunload', () => {
    performanceState.monitoringEnabled = false;
    triggerMemoryCleanup();
  });
};

/**
 * Initialize lazy loading for images and other resources
 */
const initializeLazyLoading = () => {
  // Enhanced intersection observer for lazy loading
  const imageObserver = new IntersectionObserver(
    (entries, observer) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target;
          if (img.dataset.src) {
            img.src = img.dataset.src;
            img.removeAttribute('data-src');
          }
          observer.unobserve(img);
        }
      });
    },
    {
      rootMargin: '50px 0px',
      threshold: 0.01
    }
  );

  // Observe all images with data-src
  document.querySelectorAll('img[data-src]').forEach(img => {
    imageObserver.observe(img);
  });

  // Setup mutation observer for dynamically added images
  const mutationObserver = new MutationObserver(mutations => {
    mutations.forEach(mutation => {
      mutation.addedNodes.forEach(node => {
        if (node.nodeType === 1) {
          const images = node.querySelectorAll ? node.querySelectorAll('img[data-src]') : [];
          images.forEach(img => imageObserver.observe(img));
        }
      });
    });
  });

  mutationObserver.observe(document.body, {
    childList: true,
    subtree: true
  });
};

/**
 * Optimize event handlers for better performance
 */
const optimizeEventHandlers = () => {
  // Passive event listeners for scroll events
  let scrollTimeout;
  const optimizedScrollHandler = () => {
    clearTimeout(scrollTimeout);
    scrollTimeout = setTimeout(() => {
      // Scroll handling logic here
      checkViewportElements();
    }, 16); // ~60fps throttling
  };

  document.addEventListener('scroll', optimizedScrollHandler, { passive: true });

  // Optimized resize handler
  let resizeTimeout;
  const optimizedResizeHandler = () => {
    clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(() => {
      updateViewportVariables();
    }, 100);
  };

  window.addEventListener('resize', optimizedResizeHandler, { passive: true });
};

/**
 * Apply browser-specific optimizations
 */
const applyBrowserOptimizations = () => {
  const html = document.documentElement;
  const userAgent = navigator.userAgent;

  // Safari optimizations
  if (/Safari/.test(userAgent) && !/Chrome/.test(userAgent)) {
    html.classList.add('safari-browser');
    applySafariSpecificOptimizations();
  }

  // Chrome optimizations
  if (/Chrome/.test(userAgent)) {
    html.classList.add('chrome-browser');
    applyChromeSpecificOptimizations();
  }

  // Firefox optimizations
  if (/Firefox/.test(userAgent)) {
    html.classList.add('firefox-browser');
    applyFirefoxSpecificOptimizations();
  }
};

/**
 * Safari-specific performance optimizations
 */
const applySafariSpecificOptimizations = () => {
  // Disable transform3d optimization for Safari to prevent rendering issues
  const style = document.createElement('style');
  style.textContent = `
    .safari-browser * {
      -webkit-transform-style: preserve-3d;
      -webkit-backface-visibility: hidden;
    }
  `;
  document.head.appendChild(style);
};

/**
 * Chrome-specific performance optimizations
 */
const applyChromeSpecificOptimizations = () => {
  // Enable hardware acceleration for Chrome
  const style = document.createElement('style');
  style.textContent = `
    .chrome-browser .hardware-accelerated {
      will-change: transform;
      transform: translateZ(0);
    }
  `;
  document.head.appendChild(style);
};

/**
 * Firefox-specific performance optimizations
 */
const applyFirefoxSpecificOptimizations = () => {
  // Firefox-specific optimizations
  document.documentElement.classList.add('firefox-optimized');
};

/**
 * Disable non-essential features for critical performance
 */
const disableNonEssentialFeatures = () => {
  // Disable all animations
  const style = document.createElement('style');
  style.id = 'critical-performance-styles';
  style.textContent = `
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  `;
  
  if (!document.getElementById('critical-performance-styles')) {
    document.head.appendChild(style);
  }
};

/**
 * Enable optimal features for good performance
 */
const enableOptimalFeatures = () => {
  // Remove critical performance styles
  const criticalStyles = document.getElementById('critical-performance-styles');
  if (criticalStyles) {
    criticalStyles.remove();
  }
};

/**
 * Enable data saver mode for slow connections
 */
const enableDataSaverMode = () => {
  document.documentElement.classList.add('data-saver-mode');
  
  // Disable autoplay videos
  document.querySelectorAll('video[autoplay]').forEach(video => {
    video.removeAttribute('autoplay');
    video.preload = 'none';
  });
};

/**
 * Trigger memory cleanup
 */
const triggerMemoryCleanup = () => {
  // Force garbage collection if available
  if (window.gc) {
    window.gc();
  }
  
  // Clear any large data structures or caches
  console.log('🧹 Memory cleanup triggered');
};

/**
 * Check and update viewport elements
 */
const checkViewportElements = () => {
  // Implement viewport-based optimizations
  const viewportHeight = window.innerHeight;
  document.documentElement.style.setProperty('--viewport-height', `${viewportHeight}px`);
};

/**
 * Update viewport CSS variables
 */
const updateViewportVariables = () => {
  const vw = window.innerWidth / 100;
  const vh = window.innerHeight / 100;
  
  document.documentElement.style.setProperty('--vw', `${vw}px`);
  document.documentElement.style.setProperty('--vh', `${vh}px`);
};

/**
 * Get current performance state
 */
export const getPerformanceState = () => {
  return { ...performanceState };
};

/**
 * Stop performance monitoring
 */
export const stopPerformanceMonitoring = () => {
  performanceState.monitoringEnabled = false;
  console.log('Performance monitoring stopped');
};

export default {
  initializeRuntimeOptimizations,
  getPerformanceState,
  stopPerformanceMonitoring
}; 