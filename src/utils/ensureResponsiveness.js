/**
 * Utility functions to ensure proper responsiveness across all devices
 */

import { initViewportHeightFix } from './viewportFix';
import { applyDeviceClasses, isMobileDevice } from './deviceDetection';

/**
 * Sets the CSS viewport height variable (--vh) to fix the 100vh issue on mobile browsers
 * This should be called on initial load and on resize/orientation change
 */
export const setViewportHeight = () => {
  if (typeof window === 'undefined' || typeof document === 'undefined') return;
  
  // Calculate viewport height (1% of the viewport height)
  const vh = window.innerHeight * 0.01;
  
  // Set the --vh custom property on the root element
  document.documentElement.style.setProperty('--vh', `${vh}px`);
  
  // Also update width-based breakpoints for JS access
  const vw = window.innerWidth * 0.01;
  document.documentElement.style.setProperty('--vw', `${vw}px`);
};

/**
 * Optimizes touch targets for better mobile interaction
 * Finds interactive elements that are too small and adds a class to make them larger
 */
export const optimizeTouchTargets = () => {
  if (typeof document === 'undefined') return;
  
  // Check if we're on a mobile device
  if (!isMobileDevice()) return;
  
  // Find all interactive elements that might be too small
  const smallTouchTargets = document.querySelectorAll(
    'button, a, input[type="button"], input[type="submit"], input[type="reset"], input[type="checkbox"], input[type="radio"]'
  );

  smallTouchTargets.forEach(el => {
    // Skip elements that already have the class
    if (el.classList.contains('touch-target-mobile')) return;

    // Get element dimensions
    const rect = el.getBoundingClientRect();
    if (rect.width < 44 || rect.height < 44) {
      el.classList.add('touch-target-mobile');
    }
  });
};

/**
 * Makes all images fully responsive
 */
export const makeImagesResponsive = () => {
  if (typeof document === 'undefined') return;
  
  // Select all images that don't already have responsive handling
  const images = document.querySelectorAll('img:not([loading])');
  
  images.forEach(img => {
    // Add lazy loading where appropriate
    if (!img.hasAttribute('loading')) {
      img.setAttribute('loading', 'lazy');
    }
    
    // Ensure images have alt text for accessibility
    if (!img.hasAttribute('alt')) {
      img.setAttribute('alt', '');
    }
    
    // Add responsive class if needed
    if (!img.classList.contains('responsive')) {
      img.classList.add('responsive');
    }
  });
};

/**
 * Handle orientation changes specifically
 */
export const handleOrientationChange = () => {
  if (typeof window === 'undefined' || typeof document === 'undefined') return;
  
  const isLandscape = window.matchMedia('(orientation: landscape)').matches;
  const html = document.documentElement;
  
  if (isLandscape) {
    html.classList.add('is-landscape');
    html.classList.remove('is-portrait');
  } else {
    html.classList.add('is-portrait');
    html.classList.remove('is-landscape');
  }
  
  // Recalculate viewport height after orientation change
  // Small delay for iOS to complete the orientation change
  setTimeout(setViewportHeight, 100);
};

/**
 * Make tables responsive by wrapping them
 */
export const makeTablesResponsive = () => {
  if (typeof document === 'undefined') return;
  
  const tables = document.querySelectorAll('table:not(.table-responsive)');
  
  tables.forEach(table => {
    // Skip if already wrapped
    if (table.parentElement.classList.contains('table-responsive')) return;
    
    // Create wrapper element
    const wrapper = document.createElement('div');
    wrapper.classList.add('table-responsive');
    
    // Insert wrapper before table in the DOM
    table.parentNode.insertBefore(wrapper, table);
    
    // Move table into wrapper
    wrapper.appendChild(table);
  });
};

/**
 * Fix overflow issues that can cause horizontal scrolling
 */
export const fixOverflowIssues = () => {
  if (typeof document === 'undefined') return;
  
  // Add prevent-overflow class to elements that might cause horizontal scrolling
  const potentialOverflowElements = document.querySelectorAll(
    'pre, code, table, .overflow-auto, [style*="overflow:auto"], [style*="overflow: auto"]'
  );
  
  potentialOverflowElements.forEach(el => {
    if (!el.classList.contains('prevent-overflow')) {
      el.classList.add('prevent-overflow');
    }
  });
};

/**
 * Initializes all responsiveness fixes
 * This should be called once when the application loads
 */
export const initResponsivenessFixes = () => {
  if (typeof window === 'undefined') return;
  
  // Initialize the improved viewport height fix
  initViewportHeightFix();
  
  // Add device classes
  applyDeviceClasses();
  
  // Add event listeners for resize and orientation change
  window.addEventListener('resize', () => {
    applyDeviceClasses(); // Update device classes on resize
    fixOverflowIssues(); // Check for overflow issues
  }, { passive: true });
  
  window.addEventListener('orientationchange', () => {
    handleOrientationChange();
  }, { passive: true });
  
  // Initial orientation setup
  handleOrientationChange();
  
  // Initial image responsiveness
  makeImagesResponsive();
  
  // Initial table responsiveness
  makeTablesResponsive();
  
  // Initial overflow check
  fixOverflowIssues();
  
  // Optimize touch targets after a short delay to ensure DOM is ready
  setTimeout(optimizeTouchTargets, 1000);
  
  // Re-check touch targets after any DOM changes
  window.addEventListener('DOMContentLoaded', () => {
    optimizeTouchTargets();
    makeImagesResponsive();
    makeTablesResponsive();
    fixOverflowIssues();
  }, { passive: true });
  
  window.addEventListener('load', () => {
    optimizeTouchTargets();
    makeImagesResponsive();
    makeTablesResponsive();
    fixOverflowIssues();
    applyDeviceClasses();
  }, { passive: true });
  
  // Also check after scrolling stops (for lazy-loaded content)
  let scrollTimeout;
  window.addEventListener('scroll', () => {
    clearTimeout(scrollTimeout);
    scrollTimeout = setTimeout(() => {
      optimizeTouchTargets();
    }, 200);
  }, { passive: true });
  
  // Setup a MutationObserver to monitor DOM changes
  if (typeof MutationObserver !== 'undefined') {
    const observer = new MutationObserver((mutations) => {
      // Check for new nodes that might need responsive treatment
      const hasNewNodes = mutations.some(mutation => mutation.addedNodes.length > 0);
      if (hasNewNodes) {
        makeImagesResponsive();
        makeTablesResponsive();
        optimizeTouchTargets();
        fixOverflowIssues();
      }
    });
    
    // Start observing the document with the configured parameters
    observer.observe(document.body, { 
      childList: true, 
      subtree: true 
    });
  }
};

/**
 * Checks if the device is an iPad
 * @returns {boolean} True if the device is an iPad
 */
export const isIPad = () => {
  if (typeof navigator === 'undefined') return false;
  
  // Check for iPad specifically
  const isIpad = /iPad/i.test(navigator.userAgent);
  
  // Also check for iPad Pro which might identify as MacOS
  const isIpadPro = 
    /Macintosh/i.test(navigator.userAgent) && 
    navigator.maxTouchPoints && 
    navigator.maxTouchPoints > 1;
    
  return isIpad || isIpadPro;
};

/**
 * Checks if the device is in landscape orientation
 * @returns {boolean} True if the device is in landscape orientation
 */
export const isLandscapeOrientation = () => {
  if (typeof window === 'undefined') return false;
  
  if (window.matchMedia) {
    return window.matchMedia('(orientation: landscape)').matches;
  }
  
  // Fallback if matchMedia is not available
  return window.innerWidth > window.innerHeight;
};

export default {
  setViewportHeight,
  optimizeTouchTargets,
  makeImagesResponsive,
  makeTablesResponsive,
  handleOrientationChange,
  initResponsivenessFixes,
  isIPad,
  isLandscapeOrientation,
  applyDeviceClasses
};
