/**
 * Galen Framework Integration Helper
 * 
 * This file provides utilities to use Galen framework for responsive testing
 * and validation of layouts across different device sizes.
 */

// Device definitions for responsive testing
export const DEVICE_SIZES = {
  mobile: {
    width: 375,
    height: 667,
    deviceScaleFactor: 2,
    userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1'
  },
  tablet: {
    width: 768, 
    height: 1024,
    deviceScaleFactor: 2,
    userAgent: 'Mozilla/5.0 (iPad; CPU OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1'
  },
  desktop: {
    width: 1280,
    height: 800,
    deviceScaleFactor: 1,
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36'
  }
};

/**
 * Check if a DOM element has proper mobile touch target size
 * @param {HTMLElement} element DOM element to check
 * @returns {boolean} True if the element meets minimum touch target size requirements
 */
export const hasSufficientTouchTarget = (element) => {
  if (!element) return false;
  
  const rect = element.getBoundingClientRect();
  return rect.width >= 44 && rect.height >= 44;
};

/**
 * Find all interactive elements with insufficient touch targets
 * @param {string} selector CSS selector for interactive elements
 * @returns {Array} Array of elements with insufficient touch targets
 */
export const findInsufficientTouchTargets = (selector = 'a, button, input, select, textarea') => {
  if (typeof document === 'undefined') return [];
  
  const elements = document.querySelectorAll(selector);
  const insufficientElements = [];
  
  elements.forEach(el => {
    if (!hasSufficientTouchTarget(el)) {
      insufficientElements.push({
        element: el,
        rect: el.getBoundingClientRect()
      });
    }
  });
  
  return insufficientElements;
};

/**
 * Validate an element's position and dimensions against Galen-like specs
 * @param {HTMLElement} element DOM element to validate
 * @param {Object} specs Object with layout specs (e.g., {width: '>= 320px', height: '~100px'})
 * @returns {Object} Validation results
 */
export const validateElementLayout = (element, specs) => {
  if (!element || !specs) return { valid: false, errors: ['Invalid input'] };
  
  const rect = element.getBoundingClientRect();
  const results = {
    valid: true,
    errors: []
  };
  
  // Helper to parse spec values like '>= 10px', '~ 20px', etc.
  const parseSpec = (spec, value) => {
    if (typeof spec !== 'string') return true;
    
    // Extract operator and value
    const matches = spec.match(/^([<>=~]+)?\s*(\d+(?:\.\d+)?)\s*(px|%|rem|em|vh|vw)?$/i);
    if (!matches) return true; // Can't parse, assume valid
    
    const [, operator = '=', numValue, unit = 'px'] = matches;
    const specValue = parseFloat(numValue);
    
    // Convert value to pixels if needed
    let pixelValue = value;
    if (unit === '%') {
      // For percentage, would need parent size, simplifying for now
      pixelValue = value;
    } else if (unit === 'rem' || unit === 'em') {
      const fontSize = unit === 'rem' 
        ? parseFloat(getComputedStyle(document.documentElement).fontSize)
        : parseFloat(getComputedStyle(element).fontSize);
      pixelValue = specValue * fontSize;
    } else if (unit === 'vh') {
      pixelValue = (specValue / 100) * window.innerHeight;
    } else if (unit === 'vw') {
      pixelValue = (specValue / 100) * window.innerWidth;
    }
    
    // Check against operator
    switch (operator.trim()) {
      case '>':
        return value > pixelValue;
      case '>=':
        return value >= pixelValue;
      case '<':
        return value < pixelValue;
      case '<=':
        return value <= pixelValue;
      case '~': // Approximately equal (within 5%)
        const tolerance = pixelValue * 0.05;
        return Math.abs(value - pixelValue) <= tolerance;
      case '=':
      default:
        return value === pixelValue;
    }
  };
  
  // Validate each spec
  Object.entries(specs).forEach(([property, spec]) => {
    let isValid = true;
    let value;
    
    switch (property) {
      case 'width':
        value = rect.width;
        isValid = parseSpec(spec, value);
        break;
      case 'height':
        value = rect.height;
        isValid = parseSpec(spec, value);
        break;
      case 'top':
        value = rect.top;
        isValid = parseSpec(spec, value);
        break;
      case 'left':
        value = rect.left;
        isValid = parseSpec(spec, value);
        break;
      case 'visible':
        const style = window.getComputedStyle(element);
        isValid = spec === true && 
                 style.display !== 'none' && 
                 style.visibility !== 'hidden' && 
                 style.opacity !== '0';
        break;
      default:
        // For other properties like padding, margin, etc., we'd need getComputedStyle
        break;
    }
    
    if (!isValid) {
      results.valid = false;
      results.errors.push(`Element failed "${property}" spec: expected "${spec}", got ${value}px`);
    }
  });
  
  return results;
};

/**
 * Run Galen-like responsive tests on the current page
 * @param {Object} tests Object mapping CSS selectors to spec objects
 * @returns {Object} Test results for all elements
 */
export const runResponsiveTests = (tests) => {
  if (typeof document === 'undefined' || !tests) return { valid: false };
  
  const results = {
    valid: true,
    tests: []
  };
  
  Object.entries(tests).forEach(([selector, specs]) => {
    const elements = document.querySelectorAll(selector);
    
    if (elements.length === 0) {
      results.tests.push({
        selector,
        valid: false,
        error: `No elements found matching selector: ${selector}`
      });
      results.valid = false;
      return;
    }
    
    elements.forEach((element, index) => {
      const elementResult = validateElementLayout(element, specs);
      
      results.tests.push({
        selector,
        index,
        valid: elementResult.valid,
        errors: elementResult.errors
      });
      
      if (!elementResult.valid) {
        results.valid = false;
      }
    });
  });
  
  return results;
};

/**
 * Detect and fix common mobile layout issues
 * @returns {Object} Information about detected and fixed issues
 */
export const detectAndFixMobileIssues = () => {
  if (typeof document === 'undefined') return {};
  
  const issues = {
    touchTargetsFixed: 0,
    scrollingIssuesFixed: 0,
    viewportIssuesFixed: 0
  };
  
  // 1. Fix insufficient touch targets
  const insufficientTouchTargets = findInsufficientTouchTargets();
  insufficientTouchTargets.forEach(({ element }) => {
    element.classList.add('touch-target-mobile');
    issues.touchTargetsFixed++;
  });
  
  // 2. Check for and fix scrolling container issues
  const scrollContainers = document.querySelectorAll('div[style*="overflow"], [class*="scroll"]');
  scrollContainers.forEach(container => {
    const style = window.getComputedStyle(container);
    
    if (style.overflowY === 'scroll' || style.overflowY === 'auto') {
      // Make sure scroll containers have proper touch handling
      if (container.style.WebkitOverflowScrolling !== 'touch') {
        container.style.WebkitOverflowScrolling = 'touch';
        issues.scrollingIssuesFixed++;
      }
    }
  });
  
  // 3. Check and fix viewport height issues (iOS Safari)
  const fullHeightElements = document.querySelectorAll('[class*="h-screen"], [style*="height: 100vh"]');
  const vh = window.innerHeight * 0.01;
  
  if (!document.documentElement.style.getPropertyValue('--vh')) {
    document.documentElement.style.setProperty('--vh', `${vh}px`);
    issues.viewportIssuesFixed++;
  }
  
  fullHeightElements.forEach(element => {
    if (!element.classList.contains('h-screen-fixed')) {
      element.classList.add('min-h-screen-fixed');
      issues.viewportIssuesFixed++;
    }
  });
  
  return issues;
};

export default {
  DEVICE_SIZES,
  hasSufficientTouchTarget,
  findInsufficientTouchTargets,
  validateElementLayout,
  runResponsiveTests,
  detectAndFixMobileIssues
}; 