/**
 * Utility to generate SVG icons for the PWA manifest
 * This creates placeholder icons if they don't exist
 */

/**
 * Generate a simple SVG icon with text
 * @param {number} size - Size of the icon
 * @param {string} text - Text to display in the icon
 * @param {string} bgColor - Background color
 * @param {string} textColor - Text color
 * @returns {string} - SVG string
 */
export const generateSvgIcon = (size, text, bgColor = '#0d6efd', textColor = '#ffffff') => {
  const fontSize = Math.floor(size / 4);
  return `<svg xmlns="http://www.w3.org/2000/svg" width="${size}" height="${size}" viewBox="0 0 ${size} ${size}">
    <rect width="${size}" height="${size}" fill="${bgColor}" rx="${size / 10}" ry="${size / 10}" />
    <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="${fontSize}px" font-weight="bold" 
      fill="${textColor}" text-anchor="middle" dominant-baseline="middle">${text}</text>
  </svg>`;
};

/**
 * Convert SVG string to PNG Blob
 * @param {string} svgString - SVG as string
 * @param {number} width - Target width
 * @param {number} height - Target height
 * @returns {Promise<Blob>} - PNG as Blob
 */
export const svgToPng = async (svgString, width, height) => {
  return new Promise((resolve, reject) => {
    try {
      // Create a canvas element
      const canvas = document.createElement('canvas');
      canvas.width = width;
      canvas.height = height;
      const ctx = canvas.getContext('2d');
      
      // Create an image element and set the SVG as source
      const img = new Image();
      img.onload = () => {
        // Draw the image on the canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
        
        // Convert canvas to blob
        canvas.toBlob(blob => {
          resolve(blob);
        }, 'image/png');
      };
      
      img.onerror = (e) => {
        reject(new Error('Failed to load SVG image'));
      };
      
      // Set the image source to a data URL of the SVG
      const svgBlob = new Blob([svgString], { type: 'image/svg+xml' });
      const url = URL.createObjectURL(svgBlob);
      img.src = url;
      
      // Clean up the URL object
      setTimeout(() => URL.revokeObjectURL(url), 5000);
    } catch (err) {
      reject(err);
    }
  });
};

/**
 * Save a Blob as a file
 * @param {Blob} blob - Blob to save
 * @param {string} filename - Filename
 */
export const saveBlobAsFile = (blob, filename) => {
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = filename;
  link.click();
  URL.revokeObjectURL(link.href);
};

/**
 * Generate all missing PWA icons
 */
export const generateMissingIcons = async () => {
  try {
    // Just check if the main logo exists
    const response = await fetch(`/images/smilo-logo1.jpg`);
    
    if (!response.ok) {
      console.log('Main logo image not found');
      
      // Create a notification for developers
      if (import.meta.env.DEV) {
        const message = document.createElement('div');
        message.style.position = 'fixed';
        message.style.bottom = '20px';
        message.style.right = '20px';
        message.style.padding = '15px';
        message.style.background = '#333';
        message.style.color = '#fff';
        message.style.borderRadius = '5px';
        message.style.boxShadow = '0 2px 10px rgba(0,0,0,0.2)';
        message.style.zIndex = '9999';
        message.style.maxWidth = '400px';
        message.innerHTML = `
          <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 10px;">
            <strong>Missing Logo</strong>
            <button id="dismiss-icon-notification" style="background: none; border: none; color: #fff; cursor: pointer;">✕</button>
          </div>
          <p style="margin: 0 0 10px 0;">
            The Smilo logo image (smilo-logo1.jpg) was not found. Please add it to the /images directory.
          </p>
        `;
        
        document.body.appendChild(message);
        
        // Add event listener to dismiss button
        document.getElementById('dismiss-icon-notification').addEventListener('click', () => {
          message.remove();
        });
        
        // Auto-dismiss after 30 seconds
        setTimeout(() => {
          if (document.body.contains(message)) {
            message.remove();
          }
        }, 30000);
      }
    }
  } catch (err) {
    console.error('Error checking for logo image:', err);
  }
}; 