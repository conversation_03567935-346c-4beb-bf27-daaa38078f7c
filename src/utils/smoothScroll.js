/**
 * Smooth Scroll Utility
 * 
 * Provides smooth scrolling functionality optimized for mobile devices
 * - Uses passive event listeners for better scroll performance
 * - Implements touch-friendly momentum scrolling
 * - Reduces animation frame rate on low-power devices
 */

// Detect low-powered devices (approximation)
const isLowPowerDevice = () => {
  return (
    // Check for mobile device based on userAgent
    /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) &&
    // Additional check for low memory (if available)
    (navigator.deviceMemory && navigator.deviceMemory < 4)
  );
};

/**
 * Smooth scroll to element with optimized performance for mobile
 * @param {string|HTMLElement} target - Element ID or element to scroll to
 * @param {Object} options - Scrolling options
 * @param {number} options.offset - Offset from the element in pixels (default: 0)
 * @param {number} options.duration - Duration of scroll animation in ms (default: 500)
 * @param {string} options.easing - Easing function to use (default: 'easeInOutCubic')
 */
export const smoothScrollTo = (target, options = {}) => {
  // Default options
  const { 
    offset = 0, 
    duration = isLowPowerDevice() ? 300 : 500, // Shorter duration on low-power devices
    easing = 'easeInOutCubic'
  } = options;

  // Get target element
  const targetElement = typeof target === 'string' 
    ? document.getElementById(target) || document.querySelector(target)
    : target;

  if (!targetElement) return;

  // Get target position
  const targetPosition = targetElement.getBoundingClientRect().top + window.scrollY - offset;
  const startPosition = window.scrollY;
  const distance = targetPosition - startPosition;
  let startTime = null;

  // Easing functions
  const easings = {
    linear: t => t,
    easeInOutCubic: t => t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2,
    easeOutQuint: t => 1 - Math.pow(1 - t, 5),
  };

  // Optimize RAF for mobile - use fewer frames on low-power devices
  const frameRate = isLowPowerDevice() ? 2 : 1; // Skip frames on low-power devices
  let frameCount = 0;

  // Animation function
  function animation(currentTime) {
    if (startTime === null) startTime = currentTime;
    const timeElapsed = currentTime - startTime;
    const progress = Math.min(timeElapsed / duration, 1);
    const easedProgress = easings[easing](progress);

    // Skip frames for performance on low-power devices
    if (isLowPowerDevice() && frameCount++ % frameRate !== 0) {
      window.requestAnimationFrame(animation);
      return;
    }

    window.scrollTo({
      top: startPosition + distance * easedProgress,
      behavior: 'auto' // Use our custom animation, not 'smooth'
    });

    if (timeElapsed < duration) {
      window.requestAnimationFrame(animation);
    }
  }

  window.requestAnimationFrame(animation);
};

/**
 * Add smooth scroll behavior to all internal links
 * @param {string} selector - Link selector to apply smooth scrolling to (default: 'a[href^="#"]')
 * @param {Object} options - Default scrolling options for all links
 */
export const initSmoothScrolling = (selector = 'a[href^="#"]', options = {}) => {
  document.querySelectorAll(selector).forEach(anchor => {
    anchor.addEventListener('click', function(e) {
      const href = this.getAttribute('href');
      if (href.startsWith('#')) {
        e.preventDefault();
        
        const targetId = href === '#' ? 'body' : href;
        smoothScrollTo(targetId, options);
      }
    }, { passive: false });
  });
};

export default {
  smoothScrollTo,
  initSmoothScrolling
}; 