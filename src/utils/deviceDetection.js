/**
 * Utility functions for device and browser detection
 * Used to apply specific optimizations based on device capabilities
 */

/**
 * Detects if the current device is mobile
 * @returns {boolean} True if the device is mobile
 */
export const isMobileDevice = () => {
  if (typeof navigator === 'undefined') return false;
  
  // Check for mobile user agent
  const mobileUA = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
    navigator.userAgent
  );
  
  // Check for touch capability
  const hasTouch = 
    typeof navigator !== 'undefined' && 
    ('maxTouchPoints' in navigator && navigator.maxTouchPoints > 0);
  
  // Check for small screen
  const isSmallScreen = 
    typeof window !== 'undefined' && window.innerWidth <= 768;
  
  return mobileUA || (hasTouch && isSmallScreen);
};

/**
 * Detects if the current device is iOS (iPhone, iPad, iPod)
 * @returns {boolean} True if the device is iOS
 */
export const isIOSDevice = () => {
  if (typeof navigator === 'undefined') return false;
  
  return /iPhone|iPad|iPod/i.test(navigator.userAgent) && !window.MSStream;
};

/**
 * Detects if the current device is an iPad
 * @returns {boolean} True if the device is an iPad
 */
export const isIPad = () => {
  if (typeof navigator === 'undefined') return false;
  
  // Check for iPad in user agent
  const isIpadOS = /iPad/i.test(navigator.userAgent);
  
  // iPad Pro with iPadOS 13+ reports as MacOS, so check for touch points
  const isIpadPro = 
    /Macintosh/i.test(navigator.userAgent) && 
    navigator.maxTouchPoints && 
    navigator.maxTouchPoints > 1;
    
  return isIpadOS || isIpadPro;
};

/**
 * Detects if the current device is Android
 * @returns {boolean} True if the device is Android
 */
export const isAndroidDevice = () => {
  if (typeof navigator === 'undefined') return false;
  
  return /Android/i.test(navigator.userAgent);
};

/**
 * Detects if the current browser is Safari
 * @returns {boolean} True if the browser is Safari
 */
export const isSafariBrowser = () => {
  if (typeof navigator === 'undefined') return false;
  
  return /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
};

/**
 * Detects if the current browser is Chrome
 * @returns {boolean} True if the browser is Chrome
 */
export const isChromeBrowser = () => {
  if (typeof navigator === 'undefined') return false;
  
  return /Chrome/i.test(navigator.userAgent) && !/Edge|Edg/i.test(navigator.userAgent);
};

/**
 * Detects if the current browser is Firefox
 * @returns {boolean} True if the browser is Firefox
 */
export const isFirefoxBrowser = () => {
  if (typeof navigator === 'undefined') return false;
  
  return /Firefox/i.test(navigator.userAgent);
};

/**
 * Detects if the device is in landscape orientation
 * @returns {boolean} True if the device is in landscape orientation
 */
export const isLandscapeOrientation = () => {
  if (typeof window === 'undefined') return false;
  
  // Use matchMedia for more reliable orientation detection
  return window.matchMedia('(orientation: landscape)').matches;
};

/**
 * Detects if the device has a high-DPI screen (Retina or similar)
 * @returns {boolean} True if the device has a high-DPI screen
 */
export const isHighDPIScreen = () => {
  if (typeof window === 'undefined') return false;
  
  return window.devicePixelRatio > 1;
};

/**
 * Detects if the device has limited memory or processing power
 * This is a best-effort detection and not 100% reliable
 * @returns {boolean} True if the device likely has limited resources
 */
export const isLowEndDevice = () => {
  if (typeof navigator === 'undefined') return false;
  
  // Check for memory - available in Chrome and some browsers
  const limitedMemory = 
    navigator.deviceMemory !== undefined && navigator.deviceMemory < 4;
  
  // Check for hardware concurrency (CPU cores)
  const limitedCPU = 
    navigator.hardwareConcurrency !== undefined && navigator.hardwareConcurrency <= 4;
  
  // Check for connection type - available in some browsers
  const slowConnection = 
    navigator.connection && 
    (navigator.connection.saveData || 
     ['slow-2g', '2g', '3g'].includes(navigator.connection.effectiveType));
  
  return limitedMemory || limitedCPU || slowConnection || isMobileDevice();
};

/**
 * Applies appropriate CSS classes to the document based on device detection
 */
export const applyDeviceClasses = () => {
  if (typeof document === 'undefined') return;
  
  const html = document.documentElement;
  
  // Device type
  if (isMobileDevice()) html.classList.add('is-mobile');
  else html.classList.remove('is-mobile');
  
  // Specific mobile OS
  if (isIOSDevice()) html.classList.add('is-ios');
  else html.classList.remove('is-ios');
  
  if (isAndroidDevice()) html.classList.add('is-android');
  else html.classList.remove('is-android');
  
  if (isIPad()) html.classList.add('is-ipad');
  else html.classList.remove('is-ipad');
  
  // Browser type
  if (isSafariBrowser()) html.classList.add('is-safari');
  else html.classList.remove('is-safari');
  
  if (isChromeBrowser()) html.classList.add('is-chrome');
  else html.classList.remove('is-chrome');
  
  if (isFirefoxBrowser()) html.classList.add('is-firefox');
  else html.classList.remove('is-firefox');
  
  // Screen properties
  if (isHighDPIScreen()) html.classList.add('is-high-dpi');
  else html.classList.remove('is-high-dpi');
  
  // Device capabilities
  if (isLowEndDevice()) html.classList.add('is-low-end-device');
  else html.classList.remove('is-low-end-device');
  
  // Orientation
  if (isLandscapeOrientation()) {
    html.classList.add('is-landscape');
    html.classList.remove('is-portrait');
  } else {
    html.classList.add('is-portrait');
    html.classList.remove('is-landscape');
  }
}; 