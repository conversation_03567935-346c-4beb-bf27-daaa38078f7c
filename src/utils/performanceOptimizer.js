/**
 * Performance optimization utilities
 * Helps improve performance on different devices by applying appropriate optimizations
 */

import { isLowEndDevice, isSafariBrowser, isIOSDevice } from './deviceDetection';

/**
 * Applies performance optimizations based on device capabilities
 */
export const applyPerformanceOptimizations = () => {
  if (typeof document === 'undefined') return;
  
  const html = document.documentElement;
  
  // Check if we're on a low-end device
  if (isLowEndDevice()) {
    // Add class to reduce animations and effects
    html.classList.add('reduced-animations');
    
    // Disable heavy animations
    html.classList.add('optimize-performance');
    
    // Apply more aggressive optimizations for very low-end devices
    if (isVeryLowEndDevice()) {
      html.classList.add('minimal-mode');
    }
  }
  
  // Safari-specific optimizations
  if (isSafariBrowser()) {
    html.classList.add('safari-optimized');
    
    // iOS Safari needs special handling
    if (isIOSDevice()) {
      html.classList.add('ios-optimized');
    }
  }
  
  // Apply appropriate image loading strategy
  optimizeImageLoading();
  
  // Optimize event listeners
  optimizeEventListeners();
};

/**
 * Detects if the device is very low-end (extremely limited resources)
 * @returns {boolean} True if the device is very low-end
 */
export const isVeryLowEndDevice = () => {
  if (typeof navigator === 'undefined') return false;
  
  // Check for memory - available in Chrome and some browsers
  const veryLimitedMemory = 
    navigator.deviceMemory !== undefined && navigator.deviceMemory <= 2;
  
  // Check for hardware concurrency (CPU cores)
  const veryLimitedCPU = 
    navigator.hardwareConcurrency !== undefined && navigator.hardwareConcurrency <= 2;
  
  // Check for connection type - available in some browsers
  const verySlowConnection = 
    navigator.connection && 
    (navigator.connection.saveData || 
     ['slow-2g', '2g'].includes(navigator.connection.effectiveType));
  
  return veryLimitedMemory || veryLimitedCPU || verySlowConnection;
};

/**
 * Optimizes image loading strategy based on device capabilities
 */
export const optimizeImageLoading = () => {
  if (typeof document === 'undefined') return;
  
  const images = document.querySelectorAll('img:not([loading])');
  
  // For low-end devices, use lazy loading for all images
  if (isLowEndDevice()) {
    images.forEach(img => {
      if (!img.hasAttribute('loading')) {
        img.setAttribute('loading', 'lazy');
      }
      
      // For very low-end devices, also use low-quality images if available
      if (isVeryLowEndDevice() && img.hasAttribute('data-src-low-quality')) {
        img.src = img.getAttribute('data-src-low-quality');
      }
    });
  } else {
    // For high-end devices, only lazy load images below the fold
    images.forEach(img => {
      // Check if image is likely below the fold
      const rect = img.getBoundingClientRect();
      if (rect.top > window.innerHeight) {
        img.setAttribute('loading', 'lazy');
      } else {
        img.setAttribute('loading', 'eager');
      }
    });
  }
};

/**
 * Optimizes event listeners for better performance
 */
export const optimizeEventListeners = () => {
  if (typeof window === 'undefined') return;
  
  // For low-end devices, use more aggressive throttling
  if (isLowEndDevice()) {
    // Store original functions
    if (!window._originalRequestAnimationFrame) {
      window._originalRequestAnimationFrame = window.requestAnimationFrame;
      
      // Throttle requestAnimationFrame on low-end devices
      window.requestAnimationFrame = function(callback) {
        return window._originalRequestAnimationFrame(() => {
          if (typeof callback === 'function') {
            callback();
          }
        });
      };
    }
  }
  
  // Make sure all event listeners use passive option when possible
  // This is especially important for scroll and touch events
  if (isPassiveSupported()) {
    const originalAddEventListener = EventTarget.prototype.addEventListener;
    EventTarget.prototype.addEventListener = function(type, listener, options) {
      // For scroll, touchstart, touchmove, wheel events, make passive by default
      const passiveEvents = ['scroll', 'touchstart', 'touchmove', 'wheel'];
      
      let modifiedOptions = options;
      if (passiveEvents.includes(type)) {
        if (typeof options === 'object') {
          // Don't override if explicitly set to false
          if (options.passive !== false) {
            modifiedOptions = { ...options, passive: true };
          }
        } else {
          modifiedOptions = { passive: true };
        }
      }
      
      return originalAddEventListener.call(this, type, listener, modifiedOptions);
    };
  }
};

/**
 * Checks if passive event listeners are supported
 * @returns {boolean} True if passive event listeners are supported
 */
export const isPassiveSupported = () => {
  let passive = false;
  
  try {
    // Test via a getter in the options object to see if the passive property is accessed
    const opts = Object.defineProperty({}, 'passive', {
      get: function() {
        passive = true;
        return true;
      }
    });
    
    // Create a dummy event listener to test
    window.addEventListener('testPassive', null, opts);
    window.removeEventListener('testPassive', null, opts);
  } catch (e) {
    // Do nothing
  }
  
  return passive;
};

export default {
  applyPerformanceOptimizations,
  isVeryLowEndDevice,
  optimizeImageLoading,
  optimizeEventListeners,
  isPassiveSupported
}; 