/**
 * Secure API Request Utility
 * 
 * This utility provides a secure way to make API requests with proper
 * headers, error handling, and rate limiting protection.
 */

import { getSecurityHeaders } from '../middleware/security';

/**
 * Make a secure API request
 * @param {string} url - The URL to request
 * @param {Object} options - The fetch options
 * @returns {Promise<Object>} The response data
 */
export async function secureApiRequest(url, options = {}) {
  // Add security headers
  const securityHeaders = getSecurityHeaders();
  
  // Merge headers
  const headers = {
    ...securityHeaders,
    ...options.headers
  };
  
  try {
    // Add request timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout
    
    const response = await fetch(url, {
      ...options,
      headers,
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    
    // Handle rate limiting
    if (response.status === 429) {
      const retryAfter = response.headers.get('Retry-After') || 60;
      console.warn(`Rate limited. Retry after ${retryAfter} seconds.`);
      throw new Error(`Rate limited. Retry after ${retryAfter} seconds.`);
    }
    
    // Handle other error responses
    if (!response.ok) {
      throw new Error(`API request failed with status ${response.status}: ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    // Handle network errors and timeouts
    if (error.name === 'AbortError') {
      throw new Error('API request timed out');
    }
    
    console.error('API request failed:', error);
    throw error;
  }
}

/**
 * Make a secure API request to OpenAI
 * @param {string} endpoint - The endpoint to request
 * @param {Object} data - The request data
 * @param {string} apiKey - The OpenAI API key
 * @returns {Promise<Object>} The response data
 */
export async function secureOpenAIRequest(endpoint, data, apiKey) {
  if (!apiKey) {
    throw new Error('OpenAI API key is required');
  }
  
  const url = `https://api.openai.com/v1/${endpoint}`;
  
  return secureApiRequest(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${apiKey}`
    },
    body: JSON.stringify(data)
  });
}

/**
 * Make a secure API request to Google Maps
 * @param {string} endpoint - The endpoint to request
 * @param {Object} params - The request parameters
 * @param {string} apiKey - The Google Maps API key
 * @returns {Promise<Object>} The response data
 */
export async function secureGoogleMapsRequest(endpoint, params, apiKey) {
  if (!apiKey) {
    throw new Error('Google Maps API key is required');
  }
  
  const url = new URL(`https://maps.googleapis.com/maps/api/${endpoint}`);
  
  // Add API key and parameters to URL
  url.searchParams.append('key', apiKey);
  
  for (const [key, value] of Object.entries(params)) {
    url.searchParams.append(key, value);
  }
  
  return secureApiRequest(url.toString(), {
    method: 'GET'
  });
}

export default {
  secureApiRequest,
  secureOpenAIRequest,
  secureGoogleMapsRequest
};
