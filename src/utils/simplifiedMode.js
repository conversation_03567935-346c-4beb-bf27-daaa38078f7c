/**
 * Simplified Mode Utilities
 * Provides a streamlined experience for browsers with performance issues
 */

/**
 * Checks if the app should run in simplified mode
 * This can be triggered by URL parameter or automatically for slow browsers
 * @returns {boolean} True if simplified mode should be activated
 */
export const shouldUseSimplifiedMode = () => {
  if (typeof window === 'undefined') return false;
  
  // Check URL parameter
  const urlParams = new URLSearchParams(window.location.search);
  if (urlParams.has('simplified') && urlParams.get('simplified') === 'true') {
    return true;
  }
  
  // Check Safari versions that need simplified mode
  const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
  if (isSafari) {
    const match = navigator.userAgent.match(/Version\/(\d+)\.(\d+)/);
    if (match) {
      const majorVersion = parseInt(match[1], 10);
      // Safari versions older than 15 should use simplified mode
      if (majorVersion < 15) {
        return true;
      }
    }
    
    // Check for iOS Safari
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
    if (isIOS) {
      // iOS Safari often needs simplified mode
      const isOlderIOS = /OS (9|10|11|12|13)_/.test(navigator.userAgent);
      if (isOlderIOS) {
        return true;
      }
    }
  }
  
  // Check for low memory devices
  if (navigator.deviceMemory && navigator.deviceMemory < 2) {
    return true;
  }
  
  // Check for slow CPU
  if (navigator.hardwareConcurrency && navigator.hardwareConcurrency < 2) {
    return true;
  }
  
  return false;
};

/**
 * Applies simplified mode optimizations
 * Reduces complexity of the app for better performance
 */
export const applySimplifiedMode = () => {
  if (typeof document === 'undefined') return false;
  
  if (!shouldUseSimplifiedMode()) return false;
  
  console.log('Applying simplified mode for better performance');
  
  // Add class to HTML element
  document.documentElement.classList.add('simplified-mode');
  
  // Disable all animations
  document.documentElement.classList.add('reduced-motion');
  
  // Apply simpler styles
  simplifyStyles();
  
  // Remove complex elements
  removeComplexElements();
  
  // Set up performance monitoring
  monitorPerformance();
  
  return true;
};

/**
 * Applies simplified styles for better performance
 */
const simplifyStyles = () => {
  if (typeof document === 'undefined') return;
  
  // Create a style element
  const style = document.createElement('style');
  
  style.textContent = `
    /* Disable all animations in simplified mode */
    .simplified-mode * {
      animation: none !important;
      transition: none !important;
      transform: none !important;
    }
    
    /* COMMENTED OUT: Use simple backgrounds - this was making the site gray
    .simplified-mode body {
      background: #0f172a !important;
      background-image: none !important;
    }
    */
    
    /* Simplify shadows */
    .simplified-mode [class*="shadow"] {
      box-shadow: none !important;
    }
    
    /* Disable filters */
    .simplified-mode [class*="filter"],
    .simplified-mode [class*="blur"] {
      filter: none !important;
      backdrop-filter: none !important;
    }
    
    /* COMMENTED OUT: Simplify gradients - this was making elements gray
    .simplified-mode [class*="gradient"] {
      background: #1e293b !important;
    }
    */
    
    /* Optimize images */
    .simplified-mode img {
      image-rendering: optimizeSpeed;
    }
  `;
  
  document.head.appendChild(style);
};

/**
 * Removes complex elements that might cause performance issues
 */
const removeComplexElements = () => {
  if (typeof document === 'undefined') return;
  
  // Remove complex animations and effects after the page has loaded
  window.addEventListener('load', () => {
    setTimeout(() => {
      // Find and remove complex animated elements that aren't essential
      const complexAnimations = document.querySelectorAll(
        '.animate-pulse, .animate-ping, .animate-float, ' +
        '[class*="animate-"], [class*="motion-"], ' +
        '.backdrop-blur, [class*="hover:"]'
      );
      
      complexAnimations.forEach(el => {
        // Don't remove the element, just disable its animations
        el.style.animation = 'none';
        el.style.transition = 'none';
        el.style.transform = 'none';
        el.style.opacity = '1';
      });
      
      // Remove or simplify backdrop effects
      const backdropElements = document.querySelectorAll(
        '[class*="backdrop-blur"], [class*="glassmorphism"]'
      );
      
      backdropElements.forEach(el => {
        el.style.backdropFilter = 'none';
        el.style.backgroundColor = '#1e293b';
      });
      
      // Simplify box-shadows
      const shadowElements = document.querySelectorAll('[class*="shadow"]');
      shadowElements.forEach(el => {
        el.style.boxShadow = 'none';
      });
    }, 1000);
  });
};

/**
 * Monitor performance and apply more aggressive optimizations if needed
 */
const monitorPerformance = () => {
  if (typeof window === 'undefined' || typeof document === 'undefined') return;
  
  let lagCount = 0;
  let lastFrameTime = performance.now();
  
  // Monitor frame rate
  const checkFrameRate = () => {
    const now = performance.now();
    const delta = now - lastFrameTime;
    lastFrameTime = now;
    
    // If frame took too long, count it as lag
    if (delta > 50) { // More than 50ms per frame (less than 20fps)
      lagCount++;
      
      // If we detect significant lag, apply more aggressive optimizations
      if (lagCount > 5) {
        console.log('Performance issues detected, applying emergency optimizations');
        
        // Apply more aggressive optimizations
        document.documentElement.classList.add('emergency-mode');
        
        // Create and inject emergency styles
        const emergencyStyle = document.createElement('style');
        emergencyStyle.textContent = `
          /* Emergency mode - absolute minimum styling */
          .emergency-mode * {
            background-image: none !important;
            box-shadow: none !important;
            text-shadow: none !important;
            filter: none !important;
            opacity: 1 !important;
            transform: none !important;
          }
          
          /* Disable all non-essential images */
          .emergency-mode img:not(.essential-image) {
            display: none !important;
          }
          
          /* Simplify all text */
          .emergency-mode {
            font-family: -apple-system, BlinkMacSystemFont, sans-serif !important;
            text-rendering: optimizeSpeed !important;
          }
        `;
        document.head.appendChild(emergencyStyle);
        
        // Stop monitoring since we've already applied emergency mode
        return;
      }
    }
    
    // Continue monitoring
    requestAnimationFrame(checkFrameRate);
  };
  
  // Start monitoring
  requestAnimationFrame(checkFrameRate);
};

export default {
  shouldUseSimplifiedMode,
  applySimplifiedMode
}; 