/**
 * Safari detection and optimization utilities
 * These functions help identify Safari browsers and apply optimizations
 */

/**
 * More accurate Safari detection (handles edge cases better than simple UA detection)
 * @returns {boolean} True if browser is Safari
 */
export const isSafari = () => {
  // Main Safari detection
  const ua = navigator.userAgent.toLowerCase();
  
  // Check for Safari-specific user agent (excluding Chrome)
  const isSafariUA = ua.indexOf('safari') !== -1 && ua.indexOf('chrome') === -1;
  
  // Additional check for Safari quirks
  const hasSafariQuirks = 
    // Check for Safari-specific window properties
    typeof window.webkitAudioContext !== 'undefined' && 
    typeof window.webkitOfflineAudioContext !== 'undefined' &&
    // Chrome detection shouldn't pass this check
    typeof window.chrome === 'undefined';
  
  // Check for Safari-specific CSS supports
  const hasSafariCSSQuirks = (function() {
    if (typeof CSS === 'undefined' || typeof CSS.supports !== 'function') {
      return false;
    }
    
    // Safari has specific behavior with these properties
    return (
      // <PERSON><PERSON> has unique behavior with -webkit-sticky
      CSS.supports('-webkit-sticky', 'sticky') &&
      // <PERSON>fari implements this differently
      !CSS.supports('selector(:focus-visible)')
    );
  })();
  
  return isSafariUA || hasSafariQuirks || hasSafariCSSQuirks;
};

/**
 * Detects if browser is running on iOS device (any browser on iOS)
 * @returns {boolean} True if iOS device is detected
 */
export const isIOS = () => {
  // iOS detection
  return /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
};

/**
 * Applies all Safari specific optimizations
 * Call this early in the application lifecycle
 */
export const applySafariOptimizations = () => {
  if (typeof document === 'undefined') return;
  
  const htmlElement = document.documentElement;
  const bodyElement = document.body;
  
  // Only process if Safari is detected
  if (!isSafari() && !isIOS()) return false;
  
  // Add Safari class to HTML element for CSS targeting
  htmlElement.classList.add('safari');
  
  // Add iOS class if needed
  if (isIOS()) {
    htmlElement.classList.add('safari-ios');
  }
  
  // Check for older Safari versions that need more aggressive optimizations
  const ua = navigator.userAgent;
  const safariVersionMatch = ua.match(/Version\/(\d+)\.(\d+)\.?(\d+)? Safari/);
  
  if (safariVersionMatch) {
    const majorVersion = parseInt(safariVersionMatch[1], 10);
    
    // Versions below 14 need more aggressive optimizations
    if (majorVersion < 14) {
      htmlElement.classList.add('safari-legacy');
    }
    
    // Apply version-specific class
    htmlElement.classList.add(`safari-${majorVersion}`);
  }
  
  // Apply reduced motion for better performance
  const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  
  if (prefersReducedMotion || isLowPowerDevice()) {
    htmlElement.classList.add('reduced-motion');
  }
  
  // Fix 100vh issue specifically for Safari
  fixViewportHeight();
  
  // Configure IntersectionObserver with Safari-friendly options
  configureIntersectionObserver();
  
  // Optimize text rendering
  document.body.style.textRendering = 'optimizeSpeed';
  
  // Disable certain animations in Safari
  disableProblemAnimations();
  
  // Apply scrolling fixes
  applyScrollingFixes();
  
  console.log('Safari optimizations applied');
  
  return true;
};

/**
 * Fixes the viewport height issue in Safari
 * Safari has issues with 100vh on mobile
 */
export const fixViewportHeight = () => {
  if (typeof window === 'undefined' || typeof document === 'undefined') return;
  
  // Set the --vh property
  const setVhProperty = () => {
    const vh = window.innerHeight * 0.01;
    document.documentElement.style.setProperty('--vh', `${vh}px`);
  };
  
  // Initial setting
  setVhProperty();
  
  // Update on resize or orientation change
  window.addEventListener('resize', setVhProperty, { passive: true });
  window.addEventListener('orientationchange', () => {
    // Small delay for iOS Safari
    setTimeout(setVhProperty, 200);
  }, { passive: true });
};

/**
 * Configures IntersectionObserver with Safari-friendly threshold settings
 * Safari struggles with many thresholds in IntersectionObserver
 */
export const configureIntersectionObserver = () => {
  if (typeof window === 'undefined' || 
      typeof window.IntersectionObserver === 'undefined') return;
  
  // The original IntersectionObserver constructor
  const OriginalIntersectionObserver = window.IntersectionObserver;
  
  // Override to use simpler thresholds for Safari
  window.IntersectionObserver = function(callback, options = {}) {
    // Simplify thresholds for Safari to improve performance
    if (options.threshold && Array.isArray(options.threshold) && options.threshold.length > 2) {
      options.threshold = [0, 1]; // Simplify to just start and end
    }
    
    return new OriginalIntersectionObserver(callback, options);
  };
};

/**
 * Disable problematic animations in Safari
 * Some animations are known to cause performance issues in Safari
 */
export const disableProblemAnimations = () => {
  if (typeof document === 'undefined') return;
  
  // Find elements with problematic animations
  const animatedElements = document.querySelectorAll(
    '.animate-pulse, .animate-ping, [class*="bg-gradient"]'
  );
  
  // Disable animations on these elements
  animatedElements.forEach(el => {
    el.style.animation = 'none';
    el.style.transform = 'translateZ(0)'; // Force hardware acceleration
    el.style.willChange = 'transform';
  });
  
  // Find all CSS animations that might cause issues
  const styleSheets = Array.from(document.styleSheets);
  
  try {
    styleSheets.forEach(sheet => {
      try {
        // Skip cross-origin stylesheets
        if (sheet.href && (new URL(sheet.href)).origin !== window.location.origin) {
          return;
        }
        
        const rules = Array.from(sheet.cssRules || []);
        
        rules.forEach(rule => {
          // Find keyframe animations
          if (rule.type === CSSRule.KEYFRAMES_RULE) {
            // Add a simpler alternative animation
            const name = rule.name;
            
            // Create a simple version
            const simpleKeyframe = `@keyframes ${name}-safari { 
              from { opacity: 0.9; } 
              to { opacity: 1; } 
            }`;
            
            // Inject the simplified keyframe
            const style = document.createElement('style');
            style.innerHTML = simpleKeyframe;
            document.head.appendChild(style);
          }
        });
      } catch (err) {
        // Ignore security errors for cross-origin stylesheets
        console.warn('Could not analyze stylesheet:', err);
      }
    });
  } catch (err) {
    console.warn('Error optimizing animations for Safari:', err);
  }
};

/**
 * Applies fixes for scrolling issues in Safari
 */
export const applyScrollingFixes = () => {
  if (typeof document === 'undefined') return;
  
  // Find scrollable containers
  const scrollContainers = document.querySelectorAll('.overflow-auto, .overflow-y-auto, .overflow-x-auto');
  
  // Apply fixes
  scrollContainers.forEach(container => {
    container.style.webkitOverflowScrolling = 'touch';
    
    // Fix momentum scrolling on iOS
    if (isIOS()) {
      container.classList.add('scrollable');
    }
  });
};

/**
 * Detect if this is likely a low-power device that needs optimization
 * @returns {boolean} True if device appears to be low-powered
 */
export const isLowPowerDevice = () => {
  if (typeof navigator === 'undefined') return false;
  
  // Check for known slow devices
  const ua = navigator.userAgent.toLowerCase();
  
  // Check if it's a mobile device
  const isMobile = /iphone|ipad|ipod|android/.test(ua);
  
  // Check for device memory API
  const hasLowMemory = navigator.deviceMemory && navigator.deviceMemory < 4;
  
  // Check for slow CPU
  const hasSlowCPU = navigator.hardwareConcurrency && navigator.hardwareConcurrency < 4;
  
  return isMobile || hasLowMemory || hasSlowCPU;
};

/**
 * Forces repaint to fix rendering issues in Safari
 * @param {HTMLElement} element The element to force repaint
 */
export const forceRepaint = (element) => {
  if (!element) return;
  
  // Force a repaint by briefly making a style change
  const originalDisplay = element.style.display;
  element.style.display = 'none';
  
  // Read a property to force layout calculation
  void element.offsetHeight;
  
  // Restore the original display value
  element.style.display = originalDisplay;
};

export default {
  isSafari,
  isIOS,
  applySafariOptimizations,
  fixViewportHeight,
  configureIntersectionObserver,
  disableProblemAnimations,
  applyScrollingFixes,
  isLowPowerDevice,
  forceRepaint
}; 