// Create icon SVG and convert to PNG
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get current file directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create directory if it doesn't exist
const iconDir = path.join(__dirname, '../../public/images');
if (!fs.existsSync(iconDir)) {
  fs.mkdirSync(iconDir, { recursive: true });
}

// Generate SVG for icon
function generateSvgIcon(size, text = 'SD', bgColor = '#0d6efd', textColor = '#ffffff') {
  const fontSize = Math.floor(size / 3);
  return `<svg xmlns="http://www.w3.org/2000/svg" width="${size}" height="${size}" viewBox="0 0 ${size} ${size}">
    <rect width="${size}" height="${size}" fill="${bgColor}" rx="${size / 10}" ry="${size / 10}" />
    <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="${fontSize}px" font-weight="bold"
      fill="${textColor}" text-anchor="middle" dominant-baseline="middle">${text}</text>
  </svg>`;
}

// Create SVG files
const sizes = [192, 512];
sizes.forEach(size => {
  const svgContent = generateSvgIcon(size);
  fs.writeFileSync(path.join(iconDir, `icon-${size}x${size}.svg`), svgContent);
  console.log(`Created SVG icon: icon-${size}x${size}.svg`);
});

console.log('Icons created successfully!');
