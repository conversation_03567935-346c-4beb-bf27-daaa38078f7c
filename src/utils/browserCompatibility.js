/**
 * Browser Compatibility Utility
 * 
 * This utility provides functions to detect browser capabilities,
 * test for specific features, and report compatibility issues.
 */

// Browser detection with version information
export const detectBrowser = () => {
  if (typeof navigator === 'undefined') return null;
  
  const ua = navigator.userAgent.toLowerCase();
  let browser = 'unknown';
  let version = 'unknown';
  let mobile = false;
  
  // Detect browser and version
  if (/edge|edga|edgios|edg/i.test(ua)) {
    browser = 'edge';
    version = ua.match(/(?:edge|edg)\/([\d.]+)/)?.[1] || '';
  } else if (/firefox|fxios/i.test(ua)) {
    browser = 'firefox';
    version = ua.match(/(?:firefox|fxios)\/([\d.]+)/)?.[1] || '';
  } else if (/chrome|crios/i.test(ua)) {
    browser = 'chrome';
    version = ua.match(/(?:chrome|crios)\/([\d.]+)/)?.[1] || '';
  } else if (/safari/i.test(ua) && !/chrome|crios/i.test(ua)) {
    browser = 'safari';
    version = ua.match(/version\/([\d.]+)/)?.[1] || '';
  } else if (/trident|msie/i.test(ua)) {
    browser = 'ie';
    version = ua.match(/(?:trident\/.*rv:|msie\s)([\d.]+)/)?.[1] || '';
  } else if (/opera|opr/i.test(ua)) {
    browser = 'opera';
    version = ua.match(/(?:opera|opr)\/([\d.]+)/)?.[1] || '';
  }
  
  // Detect mobile
  mobile = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(ua);
  
  // Detect OS
  let os = 'unknown';
  if (/windows/i.test(ua)) {
    os = 'windows';
  } else if (/macintosh|mac os x/i.test(ua)) {
    os = 'mac';
  } else if (/iphone|ipad|ipod/i.test(ua)) {
    os = 'ios';
  } else if (/android/i.test(ua)) {
    os = 'android';
  } else if (/linux/i.test(ua)) {
    os = 'linux';
  }
  
  return {
    name: browser,
    version,
    versionNumber: parseFloat(version) || 0,
    mobile,
    os
  };
};

// Feature detection for critical features
export const detectFeatures = () => {
  if (typeof window === 'undefined') return {};
  
  return {
    // ES6+ Features
    promises: typeof Promise !== 'undefined',
    asyncAwait: typeof async function(){} !== 'undefined',
    arrowFunctions: typeof (() => {}) !== 'undefined',
    
    // DOM APIs
    querySelector: typeof document.querySelector === 'function',
    classList: 'classList' in document.documentElement,
    dataset: 'dataset' in document.documentElement,
    
    // Modern Web APIs
    fetch: typeof fetch !== 'undefined',
    localStorage: (() => {
      try {
        localStorage.setItem('test', 'test');
        localStorage.removeItem('test');
        return true;
      } catch (e) {
        return false;
      }
    })(),
    sessionStorage: (() => {
      try {
        sessionStorage.setItem('test', 'test');
        sessionStorage.removeItem('test');
        return true;
      } catch (e) {
        return false;
      }
    })(),
    webWorkers: typeof Worker !== 'undefined',
    
    // Layout and Rendering
    flexbox: (() => {
      if (typeof document === 'undefined') return false;
      const el = document.createElement('div');
      return 'flexBasis' in el.style || 
             'webkitFlexBasis' in el.style || 
             'mozFlexBasis' in el.style;
    })(),
    grid: (() => {
      if (typeof document === 'undefined') return false;
      const el = document.createElement('div');
      return 'gridArea' in el.style || 
             'webkitGridArea' in el.style || 
             'mozGridArea' in el.style;
    })(),
    
    // Advanced Features
    webgl: (() => {
      try {
        const canvas = document.createElement('canvas');
        return !!(window.WebGLRenderingContext && 
                 (canvas.getContext('webgl') || 
                  canvas.getContext('experimental-webgl')));
      } catch (e) {
        return false;
      }
    })(),
    webgl2: (() => {
      try {
        const canvas = document.createElement('canvas');
        return !!(window.WebGL2RenderingContext && 
                 canvas.getContext('webgl2'));
      } catch (e) {
        return false;
      }
    })(),
    
    // Animation and Visual Effects
    webAnimations: typeof document.createElement('div').animate === 'function',
    requestAnimationFrame: typeof requestAnimationFrame !== 'undefined',
    
    // Observers
    mutationObserver: typeof MutationObserver !== 'undefined',
    intersectionObserver: typeof IntersectionObserver !== 'undefined',
    resizeObserver: typeof ResizeObserver !== 'undefined',
    
    // Touch and Input
    touchEvents: 'ontouchstart' in window,
    pointerEvents: !!window.PointerEvent
  };
};

// Check if the browser meets minimum requirements
export const meetsMinimumRequirements = () => {
  const features = detectFeatures();
  
  // Define critical features that must be supported
  const criticalFeatures = [
    'promises',
    'querySelector',
    'classList',
    'localStorage',
    'flexbox'
  ];
  
  // Check if all critical features are supported
  return criticalFeatures.every(feature => features[feature]);
};

// Generate a compatibility report
export const generateCompatibilityReport = () => {
  const browser = detectBrowser();
  const features = detectFeatures();
  
  // Check for known issues with specific browsers
  const knownIssues = [];
  
  if (browser.name === 'ie') {
    knownIssues.push('Internet Explorer is not fully supported. Please use a modern browser.');
  }
  
  if (browser.name === 'safari' && browser.versionNumber < 14) {
    knownIssues.push('This version of Safari may have limited functionality. Consider upgrading.');
  }
  
  if (!features.webAnimations) {
    knownIssues.push('Your browser has limited animation support. Some visual effects may not work properly.');
  }
  
  if (!features.intersectionObserver) {
    knownIssues.push('Your browser has limited support for lazy loading and scroll effects.');
  }
  
  return {
    browser,
    features,
    knownIssues,
    meetsMinimumRequirements: meetsMinimumRequirements(),
    timestamp: new Date().toISOString()
  };
};

// Display compatibility warning if needed
export const showCompatibilityWarning = () => {
  if (typeof document === 'undefined') return;
  
  const report = generateCompatibilityReport();
  
  if (!report.meetsMinimumRequirements || report.knownIssues.length > 0) {
    // Create warning element
    const warningEl = document.createElement('div');
    warningEl.style.cssText = `
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      background: #f44336;
      color: white;
      padding: 10px;
      font-family: sans-serif;
      font-size: 14px;
      text-align: center;
      z-index: 9999;
      display: flex;
      justify-content: space-between;
      align-items: center;
    `;
    
    // Add warning message
    const messageEl = document.createElement('div');
    messageEl.innerHTML = `
      <strong>Browser Compatibility Warning:</strong> 
      ${!report.meetsMinimumRequirements 
        ? 'Your browser does not meet the minimum requirements for this application.' 
        : report.knownIssues[0]}
    `;
    warningEl.appendChild(messageEl);
    
    // Add close button
    const closeBtn = document.createElement('button');
    closeBtn.textContent = '×';
    closeBtn.style.cssText = `
      background: none;
      border: none;
      color: white;
      font-size: 20px;
      cursor: pointer;
      padding: 0 10px;
    `;
    closeBtn.onclick = () => {
      warningEl.remove();
      // Remember dismissal for 24 hours
      if (features.localStorage) {
        localStorage.setItem('compatibility_warning_dismissed', Date.now().toString());
      }
    };
    warningEl.appendChild(closeBtn);
    
    // Only show if not recently dismissed
    const lastDismissed = parseInt(localStorage.getItem('compatibility_warning_dismissed') || '0', 10);
    const oneDayMs = 24 * 60 * 60 * 1000;
    if (Date.now() - lastDismissed > oneDayMs) {
      document.body.appendChild(warningEl);
    }
  }
};

export default {
  detectBrowser,
  detectFeatures,
  meetsMinimumRequirements,
  generateCompatibilityReport,
  showCompatibilityWarning
};
