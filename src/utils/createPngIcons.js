/**
 * Utility script to convert SVG icons to PNG files for the PWA manifest
 * Run this script during the build process to ensure PNG icons are available
 */
const fs = require('fs');
const path = require('path');
const { createCanvas, loadImage } = require('canvas');
const sharp = require('sharp');

// Icon sizes to generate
const ICON_SIZES = [192, 512];
const SOURCE_DIR = path.resolve(__dirname, '../../public/images');
const DEST_DIR = path.resolve(__dirname, '../../public/images');

/**
 * Ensure destination directory exists
 */
if (!fs.existsSync(DEST_DIR)) {
  fs.mkdirSync(DEST_DIR, { recursive: true });
}

/**
 * Convert SVG to PNG using sharp
 * @param {string} svgPath - Path to SVG file
 * @param {string} outputPath - Path for output PNG file
 * @param {number} size - Desired size of the PNG
 */
async function convertSvgToPng(svgPath, outputPath, size) {
  try {
    console.log(`Converting ${svgPath} to ${outputPath} at ${size}px...`);
    
    await sharp(svgPath)
      .resize(size, size)
      .png()
      .toFile(outputPath);
      
    console.log(`Successfully created ${outputPath}`);
  } catch (error) {
    console.error(`Error converting SVG to PNG: ${error.message}`);
  }
}

/**
 * Generate PNG icons for all sizes
 */
async function generateIcons() {
  for (const size of ICON_SIZES) {
    const svgPath = path.join(SOURCE_DIR, `icon-${size}x${size}.svg`);
    const pngPath = path.join(DEST_DIR, `icon-${size}x${size}.png`);
    
    // Check if SVG exists
    if (fs.existsSync(svgPath)) {
      // Check if PNG already exists
      if (!fs.existsSync(pngPath)) {
        await convertSvgToPng(svgPath, pngPath, size);
      } else {
        console.log(`PNG icon already exists: ${pngPath}`);
      }
    } else {
      console.error(`SVG icon not found: ${svgPath}`);
    }
  }
}

// Run the icon generation if this is the main module
if (require.main === module) {
  generateIcons().catch(error => {
    console.error('Error generating icons:', error);
    process.exit(1);
  });
}

module.exports = { generateIcons }; 