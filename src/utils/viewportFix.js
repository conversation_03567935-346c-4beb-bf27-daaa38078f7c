/**
 * Viewport height fix for mobile browsers
 * 
 * This script addresses the issue where 100vh doesn't account for 
 * mobile browser UI elements (address bar, navigation bar, etc.)
 * 
 * It sets a CSS variable --vh that can be used instead of vh units
 * Example usage: height: calc(var(--vh, 1vh) * 100);
 */

export function initViewportHeightFix() {
  // Initial calculation
  updateViewportHeight();
  
  // Update on resize and orientation change
  window.addEventListener('resize', updateViewportHeight);
  window.addEventListener('orientationchange', updateViewportHeight);
  
  // Some mobile browsers need a delay after orientation change
  window.addEventListener('orientationchange', () => {
    setTimeout(updateViewportHeight, 100);
  });
  
  // Some iOS devices need an additional update after scrolling
  // because the address bar can show/hide
  if (/iPhone|iPad|iPod/.test(navigator.userAgent)) {
    window.addEventListener('scroll', debounce(updateViewportHeight, 300));
  }
}

function updateViewportHeight() {
  // Get the viewport height and divide by 100
  const vh = window.innerHeight * 0.01;
  
  // Set the --vh custom property to the root of the document
  document.documentElement.style.setProperty('--vh', `${vh}px`);
}

// Simple debounce function to limit how often a function runs
function debounce(func, wait) {
  let timeout;
  return function() {
    const context = this;
    const args = arguments;
    clearTimeout(timeout);
    timeout = setTimeout(() => func.apply(context, args), wait);
  };
}

// Additional function to fix specific elements with JS
export function fixElementHeight(element, heightPercentage = 100) {
  if (!element) return;
  
  const updateHeight = () => {
    const vh = window.innerHeight * (heightPercentage / 100);
    element.style.height = `${vh}px`;
  };
  
  updateHeight();
  window.addEventListener('resize', updateHeight);
  window.addEventListener('orientationchange', () => {
    setTimeout(updateHeight, 100);
  });
  
  return () => {
    window.removeEventListener('resize', updateHeight);
    window.removeEventListener('orientationchange', updateHeight);
  };
} 