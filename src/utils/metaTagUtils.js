/**
 * Utility functions for managing meta tags
 * Especially important for responsive design and mobile optimization
 */

/**
 * Sets up all necessary meta tags for responsive design and mobile optimization
 */
export const initMetaTags = () => {
  if (typeof document === 'undefined') return;
  
  // Get the head element
  const head = document.head || document.getElementsByTagName('head')[0];
  
  // Viewport meta tag - critical for responsive design
  ensureMetaTag('viewport', 'width=device-width, initial-scale=1.0, viewport-fit=cover, maximum-scale=1.0, user-scalable=0');
  
  // Apple mobile web app capable
  ensureMetaTag('apple-mobile-web-app-capable', 'yes');
  
  // Apple status bar style
  ensureMetaTag('apple-mobile-web-app-status-bar-style', 'black-translucent');
  
  // Theme color for browsers that support it
  ensureMetaTag('theme-color', '#0f172a');
  
  // Microsoft application tag
  ensureMetaTag('msapplication-tap-highlight', 'no');
  
  // Format detection (prevent auto-detection of phone numbers, etc.)
  ensureMetaTag('format-detection', 'telephone=no');
};

/**
 * Ensures a meta tag exists with the specified name and content
 * Creates it if it doesn't exist, updates it if it does
 * 
 * @param {string} name - The name or property attribute value
 * @param {string} content - The content attribute value
 * @param {string} attribute - Whether to use 'name' (default) or 'property' attribute
 */
export const ensureMetaTag = (name, content, attribute = 'name') => {
  if (typeof document === 'undefined') return;
  
  // Get the head element
  const head = document.head || document.getElementsByTagName('head')[0];
  
  // Try to find an existing meta tag
  let meta = document.querySelector(`meta[${attribute}="${name}"]`);
  
  // If it doesn't exist, create it
  if (!meta) {
    meta = document.createElement('meta');
    meta.setAttribute(attribute, name);
    head.appendChild(meta);
  }
  
  // Set or update the content
  meta.setAttribute('content', content);
};

/**
 * Sets up meta tags specifically for iOS devices
 * Addresses common iOS-specific issues
 */
export const setupIOSMetaTags = () => {
  if (typeof document === 'undefined') return;
  
  // Prevent iOS from zooming on form inputs
  const viewportMeta = document.querySelector('meta[name="viewport"]');
  if (viewportMeta) {
    viewportMeta.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0, viewport-fit=cover');
  }
  
  // Add iOS specific meta tags
  ensureMetaTag('apple-mobile-web-app-capable', 'yes');
  ensureMetaTag('apple-mobile-web-app-status-bar-style', 'black-translucent');
  
  // Add touch icon for iOS
  const touchIcon = document.querySelector('link[rel="apple-touch-icon"]');
  if (!touchIcon) {
    const link = document.createElement('link');
    link.setAttribute('rel', 'apple-touch-icon');
    link.setAttribute('href', '/apple-touch-icon.png');
    document.head.appendChild(link);
  }
};

/**
 * Adds or updates Open Graph meta tags for better sharing on social media
 * 
 * @param {Object} options - Open Graph options
 * @param {string} options.title - The title of the page
 * @param {string} options.description - The description of the page
 * @param {string} options.image - The URL of the image to use
 * @param {string} options.url - The canonical URL of the page
 */
export const setOpenGraphTags = ({ title, description, image, url }) => {
  if (typeof document === 'undefined') return;
  
  if (title) ensureMetaTag('og:title', title, 'property');
  if (description) ensureMetaTag('og:description', description, 'property');
  if (image) ensureMetaTag('og:image', image, 'property');
  if (url) ensureMetaTag('og:url', url, 'property');
  
  // Always set these
  ensureMetaTag('og:type', 'website', 'property');
  ensureMetaTag('og:site_name', 'Smilo Dental', 'property');
};

export default {
  initMetaTags,
  ensureMetaTag,
  setupIOSMetaTags,
  setOpenGraphTags
}; 