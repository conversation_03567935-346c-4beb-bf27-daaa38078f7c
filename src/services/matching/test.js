import { matchPatientToProvider } from './providerMatching.js';

// Test case 1: Invisalign consultation in Miami with Delta Dental
console.log('Test Case 1: Invisalign consultation in Miami with Delta Dental');
const testCase1 = {
  condition: "Invisalign consultation",
  location: "Miami, FL",
  insurance: "Delta Dental",
  urgency: "Medium"
};

const matches1 = matchPatientToProvider(testCase1);
console.log(JSON.stringify(matches1, null, 2));
console.log('----------------------------');

// Test case 2: Emergency dental care with different insurance
console.log('Test Case 2: Emergency dental care with different insurance');
const testCase2 = {
  condition: "Emergency dental care",
  location: "Coral Gables, FL",
  insurance: "Guardian",
  urgency: "High"
};

const matches2 = matchPatientToProvider(testCase2);
console.log(JSON.stringify(matches2, null, 2));
console.log('----------------------------');

// Test case 3: Teeth whitening with insurance not widely accepted
console.log('Test Case 3: Teeth whitening with insurance not widely accepted');
const testCase3 = {
  condition: "Teeth whitening",
  location: "Miami Beach, FL",
  insurance: "MetLife",
  urgency: "Low"
};

const matches3 = matchPatientToProvider(testCase3);
console.log(JSON.stringify(matches3, null, 2)); 