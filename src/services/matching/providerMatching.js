// Mock provider database
const providers = [
  {
    id: 'provider-1',
    name: 'Miami Dental Associates',
    location: 'Miami, FL',
    specialties: ['General Dentistry', 'Cosmetic Dentistry', 'Invisalign'],
    insuranceAccepted: ['Delta Dental', 'Cigna', 'Aetna'],
    rating: 4.8,
    availability: 'High'
  },
  {
    id: 'provider-2',
    name: 'Orthodontic Specialists of Florida',
    location: 'Miami, FL',
    specialties: ['Orthodontics', 'Invisalign', 'Braces'],
    insuranceAccepted: ['MetLife', 'United Healthcare', 'Blue Cross'],
    rating: 4.9,
    availability: 'Medium'
  },
  {
    id: 'provider-3',
    name: 'Coral Gables Family Dentistry',
    location: 'Coral Gables, FL',
    specialties: ['General Dentistry', 'Pediatric Dentistry', 'Emergency Care'],
    insuranceAccepted: ['Delta Dental', 'Guardian', 'Aetna'],
    rating: 4.7,
    availability: 'High'
  },
  {
    id: 'provider-4',
    name: 'South Beach Dental Spa',
    location: 'Miami Beach, FL',
    specialties: ['Cosmetic Dentistry', 'Invisalign', 'Teeth Whitening'],
    insuranceAccepted: ['Delta Dental', 'Cigna', 'Aetna'],
    rating: 4.6,
    availability: 'Low'
  },
  {
    id: 'provider-5',
    name: 'Downtown Miami Dental Group',
    location: 'Miami, FL',
    specialties: ['General Dentistry', 'Root Canal', 'Dental Implants'],
    insuranceAccepted: ['United Healthcare', 'Blue Cross', 'Guardian'],
    rating: 4.5,
    availability: 'Medium'
  }
];

/**
 * Maps patient conditions to provider specialties for matching
 */
const conditionToSpecialtyMap = {
  'Invisalign consultation': ['Invisalign', 'Orthodontics'],
  'Teeth cleaning': ['General Dentistry', 'Preventive Care'],
  'Tooth pain': ['Emergency Care', 'General Dentistry'],
  'Cavity filling': ['General Dentistry', 'Restorative Dentistry'],
  'Root canal': ['Endodontics', 'Root Canal'],
  'Teeth whitening': ['Cosmetic Dentistry', 'Teeth Whitening'],
  'Dental implants': ['Dental Implants', 'Oral Surgery'],
  'Braces consultation': ['Orthodontics', 'Braces'],
  'Emergency dental care': ['Emergency Care']
};

/**
 * Matches a patient to provider based on compatibility criteria
 * @param {Object} patientData - Patient data including condition, location, insurance, urgency
 * @returns {Array} Sorted array of providers with compatibility scores
 */
function matchPatientToProvider(patientData) {
  // Extract patient data
  const { condition, location, insurance, urgency } = patientData;
  
  // Get matching specialties for the condition
  const matchingSpecialties = conditionToSpecialtyMap[condition] || [];
  
  // Calculate scores for each provider
  const scoredProviders = providers.map(provider => {
    let score = 0;
    
    // Location matching (highest weight)
    if (provider.location === location) {
      score += 40;
    } else if (provider.location.includes(location.split(',')[0])) {
      // Partial location match (same city)
      score += 20;
    }
    
    // Insurance matching
    if (provider.insuranceAccepted.includes(insurance)) {
      score += 30;
    }
    
    // Specialty matching
    const hasSpecialty = matchingSpecialties.some(specialty => 
      provider.specialties.includes(specialty)
    );
    
    if (hasSpecialty) {
      score += 20;
    }
    
    // Rating bonus
    if (provider.rating >= 4.8) {
      score += 10;
    } else if (provider.rating >= 4.5) {
      score += 5;
    }
    
    // Availability adjustment based on urgency
    if (urgency === 'High' && provider.availability === 'High') {
      score += 5;
    }
    
    return {
      ...provider,
      compatibilityScore: Math.min(score, 100)  // Cap at 100
    };
  });
  
  // Sort by compatibility score (descending)
  const sortedProviders = scoredProviders.sort(
    (a, b) => b.compatibilityScore - a.compatibilityScore
  );
  
  // Return top 3 matches
  return sortedProviders.slice(0, 3);
}

/**
 * Example usage:
 * 
 * const patientRequest = {
 *   condition: "Invisalign consultation",
 *   location: "Miami, FL",
 *   insurance: "Delta Dental",
 *   urgency: "Medium"
 * };
 * 
 * const matches = matchPatientToProvider(patientRequest);
 * console.log(matches);
 */

export { matchPatientToProvider }; 