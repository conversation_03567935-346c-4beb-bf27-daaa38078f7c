// DeepSeek AI Service
// This service handles integration with DeepSeek API for food suggestions and dental health insights

const DEEPSEEK_API_KEY = '***********************************';
const API_ENDPOINT = 'https://api.openai.com/v1/chat/completions';

class DeepSeekService {
  constructor() {
    this.apiKey = DEEPSEEK_API_KEY;
  }

  // Generate SNAP grocery suggestions based on user preferences
  async getSuggestions(userPreferences, budget) {
    const prompt = `Generate a list of tooth-friendly, SNAP-eligible grocery items based on these preferences: 
      ${userPreferences.dietaryPreferences.join(', ')}. 
      Dental health goals: ${userPreferences.dentalHealthGoals.join(', ')}.
      Budget: $${budget}.
      For each item, include: name, approximate price, dental health benefit, and SNAP eligibility.`;
    
    return this.queryDeepSeek(prompt);
  }

  // Answer user questions about dental-friendly foods
  async answerQuestion(question, userContext) {
    const prompt = `As a dental nutrition expert, answer this question: "${question}"
      Consider the user's context:
      - Dietary preferences: ${userContext.dietaryPreferences.join(', ')}
      - Dental health goals: ${userContext.dentalHealthGoals.join(', ')}
      - Budget constraints: Consider SNAP/EBT eligibility`;
    
    return this.queryDeepSeek(prompt);
  }

  // Evaluate food items for dental health
  async evaluateCartItems(items) {
    const itemsList = items.map(item => item.name).join(', ');
    const prompt = `Evaluate these grocery items for dental health benefits/concerns: ${itemsList}.
      For each item provide:
      1. Dental health rating (1-5 stars)
      2. Brief explanation of impact on teeth/gums
      3. Suggestion for how to consume it in a tooth-friendly way (if applicable)`;
    
    return this.queryDeepSeek(prompt);
  }

  // Generate meal plans optimized for dental health
  async generateMealPlan(userPreferences, days = 7) {
    const prompt = `Create a ${days}-day meal plan using SNAP-eligible ingredients that promote dental health.
      User preferences: ${userPreferences.dietaryPreferences.join(', ')}
      Dental goals: ${userPreferences.dentalHealthGoals.join(', ')}
      
      For each day include:
      - Breakfast, lunch, dinner, and 2 snacks
      - Approximate cost per meal
      - Key dental health benefits
      - Simple preparation instructions`;
    
    return this.queryDeepSeek(prompt);
  }

  // Suggest healthier alternatives to less tooth-friendly items
  async suggestAlternatives(item) {
    const prompt = `Suggest 3 tooth-friendly alternatives to "${item}" that are:
      1. SNAP/EBT eligible
      2. Similar in taste/usage
      3. Better for dental health
      
      For each alternative, explain why it's better for dental health.`;
    
    return this.queryDeepSeek(prompt);
  }

  // Core method to interact with DeepSeek API
  async queryDeepSeek(prompt) {
    try {
      const response = await fetch(API_ENDPOINT, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          model: "gpt-3.5-turbo",
          messages: [
            {
              role: "system",
              content: "You are SmiloSnap, an AI assistant specialized in providing advice on foods that are beneficial for dental health and are eligible for SNAP/EBT benefits. Your responses should be concise, accurate, and focused on helping users maintain good dental health while staying within their budget."
            },
            {
              role: "user",
              content: prompt
            }
          ],
          max_tokens: 1000
        })
      });

      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }

      const data = await response.json();
      return data.choices[0].message.content;
    } catch (error) {
      console.error('API error:', error);
      return this.mockResponse(prompt);
    }
  }

  // Mock response method for development when API isn't available
  mockResponse(prompt) {
    // Mock responses for different query types
    if (prompt.includes('grocery items')) {
      return [
        { name: "Greek Yogurt", price: "$3.99", benefit: "High in calcium for stronger teeth", snapEligible: true },
        { name: "Eggs", price: "$2.49", benefit: "Protein and vitamin D support oral health", snapEligible: true },
        { name: "Broccoli", price: "$1.99", benefit: "Fiber helps clean teeth naturally", snapEligible: true },
        { name: "Cheddar Cheese", price: "$3.79", benefit: "Calcium and phosphates strengthen enamel", snapEligible: true },
        { name: "Almonds", price: "$4.99", benefit: "Calcium and low sugar protect teeth", snapEligible: true }
      ];
    } else if (prompt.includes('meal plan')) {
      return {
        days: [
          {
            day: "Monday",
            meals: {
              breakfast: { name: "Yogurt with berries", cost: "$2.50", benefit: "Calcium + low sugar" },
              lunch: { name: "Tuna salad sandwich", cost: "$3.25", benefit: "Phosphorus for strong teeth" },
              dinner: { name: "Chicken with steamed vegetables", cost: "$4.50", benefit: "Protein + fibrous veggies" },
              snacks: ["Apple slices", "Cheese stick"]
            }
          },
          // Additional days would be included
        ]
      };
    } else {
      return "Greek yogurt is an excellent choice for dental health. It's high in calcium which strengthens tooth enamel and its probiotics may help reduce gum disease.";
    }
  }
}

export default new DeepSeekService(); 