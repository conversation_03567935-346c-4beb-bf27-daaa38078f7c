import React, { useEffect, useRef, Suspense, useState } from 'react';
// Responsiveness initialization moved to main.jsx
import { BrowserRouter as Router, Routes, Route, useLocation } from 'react-router-dom';
import { AnimatePresence } from 'framer-motion';
import { AuthProvider } from './contexts/AuthContext';
import { ProgressProvider } from './contexts/ProgressContext';
import { UserProvider } from './contexts/UserContext';
import { AnimationProvider } from './contexts/AnimationContext';
import { useSystemSettings } from './lib/hooks/useSystemSettings';
import { ensureNDANotificationsTable } from './lib/supabase';
import { checkDatabaseStatus } from './lib/database/initDatabase';
import MaintenanceMode from './components/MaintenanceMode';
import Layout from './components/layout/Layout';
import LandingPage from './components/LandingPage';
import AboutPage from './components/pages/AboutPage';
import SystemDiagnostics from './components/SystemDiagnostics';
import ChatPage from './components/chat/ChatPage';
import ChatHistoryPage from './components/chat/ChatHistoryPage';
import { PreDentalTracker } from './components/predental/DISABLED';
import ExpertResourcesPage from './components/resources/ExpertResourcesPage';
import SeoResourcesPage from './components/resources/SeoResourcesPage';
import AdminLoginForm from './components/auth/AdminLoginForm';
import AdminDashboard from './components/admin/AdminDashboard';
import AdminProtectedRoute from './components/admin/AdminProtectedRoute';
import PrivacyPolicy from './components/legal/PrivacyPolicy';
import TermsOfService from './components/legal/TermsOfService';
import DenTechPage from './components/dentech/DenTechPage';
import TelehealthPage from './components/telehealth/TelehealthPage';
import SmiloAssistPage from './components/assist/SmiloAssistPage';
import PartnershipsPage from './components/business/PartnershipsPage';
import ScrollToTop from './components/common/ScrollToTop';
import CookieConsent from './components/common/CookieConsent';
import ResourcePreloader from './components/common/ResourcePreloader';
import UserActivityLogger from './components/common/UserActivityLogger';
import SafariWarning from './components/common/SafariWarning';
import { motion } from 'framer-motion';
import AutoRedirectScript from './components/admin/AutoRedirectScript';
import SeoArticleView from './components/resources/SeoArticleView';
import MentorDetailPage from './pages/mentor/MentorDetailPage';
import { applyMobileOptimizations, startPerformanceMonitoring } from './lib/utils/deviceDetection';
import { isSafari } from './utils/safariOptimizer';

// Affordable Care Pages
import AffordableCare from './pages/AffordableCare';
import AffordableOptions from './pages/AffordableOptions';
import StateDentalPrograms from './pages/StateDentalPrograms';
import NonProfitOrganizations from './pages/NonProfitOrganizations';
import DentalInsurancePlans from './pages/DentalInsurancePlans';
import PaymentPlans from './pages/PaymentPlans';
import SmiloBites from './pages/SmiloBites';
import Providers from './pages/Providers';
import BrowserCompatibilityPage from './pages/BrowserCompatibilityPage';

// Browser-specific initializations are now in main.jsx
// to avoid build-time issues

// Responsive fixes initialization moved to main.jsx

// Define page transition variants based on performance mode
const getPageVariants = (isSimplified) => {
  // In simplified mode, use simpler transitions with no transforms
  if (isSimplified) {
    return {
      initial: { opacity: 0 },
      enter: { opacity: 1 },
      exit: { opacity: 0 }
    };
  }
  
  // Default animations for normal mode
  return {
    initial: { opacity: 0, y: 20 },
    enter: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 }
  };
};

// Wrap routes with providers in correct order
function AnimatedRoutes({ isSimplified }) {
  const location = useLocation();
  const { maintenanceMode, loading } = useSystemSettings();
  const isTransitioning = useRef(false);
  const pageVariants = getPageVariants(isSimplified);
  
  // Calculate transition duration based on simplified mode
  const transitionDuration = isSimplified ? 0.15 : 0.3;

  useEffect(() => {
    isTransitioning.current = true;
    const timer = setTimeout(() => {
      isTransitioning.current = false;
    }, transitionDuration * 1000); // Match transition duration
    return () => clearTimeout(timer);
  }, [location.pathname, transitionDuration]);

  // Add a fallback for system settings errors
  if (loading) {
    console.log("System settings are loading...");
    return <div className="flex items-center justify-center min-h-screen">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
    </div>;
  }

  // Safety check for maintenance mode
  const isInMaintenanceMode = maintenanceMode === true;
  if (isInMaintenanceMode) {
    console.log("System is in maintenance mode");
    return <MaintenanceMode />;
  }

  // For simplified mode, wrap each component in a div instead of motion.div
  const PageWrapper = isSimplified ? ({ children }) => <div>{children}</div> : motion.div;

  return (
    <>
      <ScrollToTop />
      <Layout isSimplified={isSimplified}>
        <AnimatePresence mode={isSimplified ? "wait" : "sync"}>
          <Routes location={location} key={location.pathname}>
            <Route path="/" element={
              <PageWrapper
                initial={isSimplified ? undefined : "initial"}
                animate={isSimplified ? undefined : "enter"}
                exit={isSimplified ? undefined : "exit"}
                variants={isSimplified ? undefined : pageVariants}
                transition={isSimplified ? undefined : { duration: transitionDuration }}
              >
                <LandingPage isSimplified={isSimplified} />
              </PageWrapper>
            } />
            <Route path="/about" element={
              <PageWrapper
                initial={isSimplified ? undefined : "initial"}
                animate={isSimplified ? undefined : "enter"}
                exit={isSimplified ? undefined : "exit"}
                variants={isSimplified ? undefined : pageVariants}
                transition={isSimplified ? undefined : { duration: transitionDuration }}
              >
                <AboutPage isSimplified={isSimplified} />
              </PageWrapper>
            } />
            <Route path="/chat" element={
              <PageWrapper
                initial={isSimplified ? undefined : "initial"}
                animate={isSimplified ? undefined : "enter"}
                exit={isSimplified ? undefined : "exit"}
                variants={isSimplified ? undefined : pageVariants}
                transition={isSimplified ? undefined : { duration: transitionDuration }}
              >
                <ChatPage />
              </PageWrapper>
            } />
            <Route path="/chat/history" element={
              <PageWrapper
                initial={isSimplified ? undefined : "initial"}
                animate={isSimplified ? undefined : "enter"}
                exit={isSimplified ? undefined : "exit"}
                variants={isSimplified ? undefined : pageVariants}
                transition={isSimplified ? undefined : { duration: transitionDuration }}
              >
                <ChatHistoryPage />
              </PageWrapper>
            } />
            {/* Temporarily hidden until ready
            <Route path="/predental" element={
              <PageWrapper
                initial={isSimplified ? undefined : "initial"}
                animate={isSimplified ? undefined : "enter"}
                exit={isSimplified ? undefined : "exit"}
                variants={isSimplified ? undefined : pageVariants}
                transition={isSimplified ? undefined : { duration: transitionDuration }}
              >
                <PreDentalTracker />
              </PageWrapper>
            } />
            */}
            <Route path="/expert-resources" element={
              <PageWrapper
                initial={isSimplified ? undefined : "initial"}
                animate={isSimplified ? undefined : "enter"}
                exit={isSimplified ? undefined : "exit"}
                variants={isSimplified ? undefined : pageVariants}
                transition={isSimplified ? undefined : { duration: transitionDuration }}
              >
                <ExpertResourcesPage />
              </PageWrapper>
            } />
            {/* Temporarily hidden until ready
            <Route path="/dentech" element={
              <PageWrapper
                initial={isSimplified ? undefined : "initial"}
                animate={isSimplified ? undefined : "enter"}
                exit={isSimplified ? undefined : "exit"}
                variants={isSimplified ? undefined : pageVariants}
                transition={isSimplified ? undefined : { duration: transitionDuration }}
              >
                <DenTechPage />
              </PageWrapper>
            } />
            */}
            <Route path="/system-status" element={
              <PageWrapper
                initial={isSimplified ? undefined : "initial"}
                animate={isSimplified ? undefined : "enter"}
                exit={isSimplified ? undefined : "exit"}
                variants={isSimplified ? undefined : pageVariants}
                transition={isSimplified ? undefined : { duration: transitionDuration }}
              >
                <SystemDiagnostics />
              </PageWrapper>
            } />
            <Route path="/admin/login" element={
              <PageWrapper
                initial={isSimplified ? undefined : "initial"}
                animate={isSimplified ? undefined : "enter"}
                exit={isSimplified ? undefined : "exit"}
                variants={isSimplified ? undefined : pageVariants}
                transition={isSimplified ? undefined : { duration: transitionDuration }}
              >
                <AdminLoginForm />
              </PageWrapper>
            } />
            <Route path="/admin" element={<AdminProtectedRoute />}>
              <Route index element={
                <PageWrapper
                  initial={isSimplified ? undefined : "initial"}
                  animate={isSimplified ? undefined : "enter"}
                  exit={isSimplified ? undefined : "exit"}
                  variants={isSimplified ? undefined : pageVariants}
                  transition={isSimplified ? undefined : { duration: transitionDuration }}
                >
                  <AdminDashboard />
                </PageWrapper>
              } />
              <Route path="nda" element={
                <PageWrapper
                  initial={isSimplified ? undefined : "initial"}
                  animate={isSimplified ? undefined : "enter"}
                  exit={isSimplified ? undefined : "exit"}
                  variants={isSimplified ? undefined : pageVariants}
                  transition={isSimplified ? undefined : { duration: transitionDuration }}
                >
                  <AdminDashboard />
                </PageWrapper>
              } />
              <Route path="nda/:id" element={
                <PageWrapper
                  initial={isSimplified ? undefined : "initial"}
                  animate={isSimplified ? undefined : "enter"}
                  exit={isSimplified ? undefined : "exit"}
                  variants={isSimplified ? undefined : pageVariants}
                  transition={isSimplified ? undefined : { duration: transitionDuration }}
                >
                  <AdminDashboard />
                </PageWrapper>
              } />
              <Route path="*" element={
                <PageWrapper
                  initial={isSimplified ? undefined : "initial"}
                  animate={isSimplified ? undefined : "enter"}
                  exit={isSimplified ? undefined : "exit"}
                  variants={isSimplified ? undefined : pageVariants}
                  transition={isSimplified ? undefined : { duration: transitionDuration }}
                >
                  <AdminDashboard />
                </PageWrapper>
              } />
            </Route>
            <Route path="/privacy" element={
              <PageWrapper
                initial={isSimplified ? undefined : "initial"}
                animate={isSimplified ? undefined : "enter"}
                exit={isSimplified ? undefined : "exit"}
                variants={isSimplified ? undefined : pageVariants}
                transition={isSimplified ? undefined : { duration: transitionDuration }}
              >
                <PrivacyPolicy />
              </PageWrapper>
            } />
            <Route path="/terms" element={
              <PageWrapper
                initial={isSimplified ? undefined : "initial"}
                animate={isSimplified ? undefined : "enter"}
                exit={isSimplified ? undefined : "exit"}
                variants={isSimplified ? undefined : pageVariants}
                transition={isSimplified ? undefined : { duration: transitionDuration }}
              >
                <TermsOfService />
              </PageWrapper>
            } />
            {/* Temporarily hidden until ready
            <Route path="/telehealth" element={
              <PageWrapper
                initial={isSimplified ? undefined : "initial"}
                animate={isSimplified ? undefined : "enter"}
                exit={isSimplified ? undefined : "exit"}
                variants={isSimplified ? undefined : pageVariants}
                transition={isSimplified ? undefined : { duration: transitionDuration }}
              >
                <TelehealthPage />
              </PageWrapper>
            } />
            */}
            <Route path="/partnerships" element={
              <PageWrapper
                initial={isSimplified ? undefined : "initial"}
                animate={isSimplified ? undefined : "enter"}
                exit={isSimplified ? undefined : "exit"}
                variants={isSimplified ? undefined : pageVariants}
                transition={isSimplified ? undefined : { duration: transitionDuration }}
              >
                <PartnershipsPage />
              </PageWrapper>
            } />
            <Route path="/resources/seo" element={
              <PageWrapper
                initial={isSimplified ? undefined : "initial"}
                animate={isSimplified ? undefined : "enter"}
                exit={isSimplified ? undefined : "exit"}
                variants={isSimplified ? undefined : pageVariants}
                transition={isSimplified ? undefined : { duration: transitionDuration }}
              >
                <SeoResourcesPage />
              </PageWrapper>
            } />
            <Route path="/resources/seo/:articleId" element={
              <PageWrapper
                initial={isSimplified ? undefined : "initial"}
                animate={isSimplified ? undefined : "enter"}
                exit={isSimplified ? undefined : "exit"}
                variants={isSimplified ? undefined : pageVariants}
                transition={isSimplified ? undefined : { duration: transitionDuration }}
              >
                <SeoArticleView />
              </PageWrapper>
            } />

            {/* Affordable Care Routes */}
            <Route path="/affordable-care" element={
              <PageWrapper
                initial={isSimplified ? undefined : "initial"}
                animate={isSimplified ? undefined : "enter"}
                exit={isSimplified ? undefined : "exit"}
                variants={isSimplified ? undefined : pageVariants}
                transition={isSimplified ? undefined : { duration: transitionDuration }}
              >
                <AffordableCare />
              </PageWrapper>
            } />
            <Route path="/options" element={
              <PageWrapper
                initial={isSimplified ? undefined : "initial"}
                animate={isSimplified ? undefined : "enter"}
                exit={isSimplified ? undefined : "exit"}
                variants={isSimplified ? undefined : pageVariants}
                transition={isSimplified ? undefined : { duration: transitionDuration }}
              >
                <AffordableOptions />
              </PageWrapper>
            } />
            <Route path="/state-dental-programs" element={
              <PageWrapper
                initial={isSimplified ? undefined : "initial"}
                animate={isSimplified ? undefined : "enter"}
                exit={isSimplified ? undefined : "exit"}
                variants={isSimplified ? undefined : pageVariants}
                transition={isSimplified ? undefined : { duration: transitionDuration }}
              >
                <StateDentalPrograms />
              </PageWrapper>
            } />
            <Route path="/non-profit-organizations" element={
              <PageWrapper
                initial={isSimplified ? undefined : "initial"}
                animate={isSimplified ? undefined : "enter"}
                exit={isSimplified ? undefined : "exit"}
                variants={isSimplified ? undefined : pageVariants}
                transition={isSimplified ? undefined : { duration: transitionDuration }}
              >
                <NonProfitOrganizations />
              </PageWrapper>
            } />
            <Route path="/dental-insurance-plans" element={
              <PageWrapper
                initial={isSimplified ? undefined : "initial"}
                animate={isSimplified ? undefined : "enter"}
                exit={isSimplified ? undefined : "exit"}
                variants={isSimplified ? undefined : pageVariants}
                transition={isSimplified ? undefined : { duration: transitionDuration }}
              >
                <DentalInsurancePlans />
              </PageWrapper>
            } />
            <Route path="/payment-plans" element={
              <PageWrapper
                initial={isSimplified ? undefined : "initial"}
                animate={isSimplified ? undefined : "enter"}
                exit={isSimplified ? undefined : "exit"}
                variants={isSimplified ? undefined : pageVariants}
                transition={isSimplified ? undefined : { duration: transitionDuration }}
              >
                <PaymentPlans />
              </PageWrapper>
            } />
            
            {/* SmiloSnap route disabled */}
            {/* 
            <Route path="/smilo-bites" element={
              <PageWrapper
                initial={isSimplified ? undefined : "initial"}
                animate={isSimplified ? undefined : "enter"}
                exit={isSimplified ? undefined : "exit"}
                variants={isSimplified ? undefined : pageVariants}
                transition={isSimplified ? undefined : { duration: transitionDuration }}
              >
                <SmiloBites />
              </PageWrapper>
            } />
            */}

            <Route path="/assist" element={
              <PageWrapper
                initial={isSimplified ? undefined : "initial"}
                animate={isSimplified ? undefined : "enter"}
                exit={isSimplified ? undefined : "exit"}
                variants={isSimplified ? undefined : pageVariants}
                transition={isSimplified ? undefined : { duration: transitionDuration }}
              >
                <SmiloAssistPage />
              </PageWrapper>
            } />
            <Route path="/providers" element={<Providers />} />
            <Route path="/browser-compatibility" element={
              <PageWrapper
                initial={isSimplified ? undefined : "initial"}
                animate={isSimplified ? undefined : "enter"}
                exit={isSimplified ? undefined : "exit"}
                variants={isSimplified ? undefined : pageVariants}
                transition={isSimplified ? undefined : { duration: transitionDuration }}
              >
                <BrowserCompatibilityPage />
              </PageWrapper>
            } />
            
            {/* Mentor Detail Page Route */}
            <Route path="/mentor/:mentorId" element={
              <PageWrapper
                initial={isSimplified ? undefined : "initial"}
                animate={isSimplified ? undefined : "enter"}
                exit={isSimplified ? undefined : "exit"}
                variants={isSimplified ? undefined : pageVariants}
                transition={isSimplified ? undefined : { duration: transitionDuration }}
              >
                <MentorDetailPage />
              </PageWrapper>
            } />

            <Route path="/telehealth" element={
              <PageWrapper
                initial={isSimplified ? undefined : "initial"}
                animate={isSimplified ? undefined : "enter"}
                exit={isSimplified ? undefined : "exit"}
                variants={isSimplified ? undefined : pageVariants}
                transition={isSimplified ? undefined : { duration: transitionDuration }}
              >
                <TelehealthPage />
              </PageWrapper>
            } />
          </Routes>
        </AnimatePresence>
      </Layout>
    </>
  );
}

export default function App({ isSimplified = false }) {
  const [showSafariWarning, setShowSafariWarning] = useState(!isSimplified && isSafari());
  
  useEffect(() => {
    // In simplified mode, skip some optimizations
    if (!isSimplified) {
      // Apply mobile optimizations on mount
      const deviceInfo = applyMobileOptimizations();
      
      // Start performance monitoring
      startPerformanceMonitoring(({ type, needsOptimization }) => {
        if (type === 'performance' && needsOptimization) {
          document.body.classList.add('reduced-animations');
        }
      });
      
      // Log device info for debugging
      console.log('Device Info:', deviceInfo);
    }

    // Initialize third-party services based on cookie consent
    // We still initialize third-party services even in simplified mode
    import('./lib/utils/thirdPartyConsentManager').then(({ default: thirdPartyConsentManager }) => {
      // Only initialize services when consent has been given and the appropriate categories are allowed
      thirdPartyConsentManager.initializeThirdPartyServices({
        googleAnalyticsId: import.meta.env.VITE_GOOGLE_ANALYTICS_ID,
        googleTagManagerId: import.meta.env.VITE_GOOGLE_TAG_MANAGER_ID,
        facebookPixelId: import.meta.env.VITE_FACEBOOK_PIXEL_ID
      });
    }).catch(err => {
      console.error('Error initializing third-party services:', err);
    });
  }, [isSimplified]);

  return (
    <Router future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
      <Suspense fallback={<div className="flex items-center justify-center min-h-screen">
        <div className={isSimplified ? "" : "animate-spin"} style={{
          width: "2rem",
          height: "2rem",
          borderRadius: "50%",
          border: "0.25rem solid rgba(59, 130, 246, 0.1)",
          borderTopColor: "#3b82f6" 
        }}></div>
      </div>}>
        <AuthProvider>
          <UserProvider>
            <ProgressProvider>
              <AnimationProvider>
                <AnimatedRoutes isSimplified={isSimplified} />
                {!isSimplified && <ResourcePreloader />}
                <CookieConsent />
                {!isSimplified && <UserActivityLogger />}
                {!isSimplified && <AutoRedirectScript />}
                {showSafariWarning && <SafariWarning onDismiss={() => setShowSafariWarning(false)} />}
              </AnimationProvider>
            </ProgressProvider>
          </UserProvider>
        </AuthProvider>
      </Suspense>
    </Router>
  );
}