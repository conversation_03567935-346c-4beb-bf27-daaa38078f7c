import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { handleApiError } from '../lib/utils/errorHandler';

const UserContext = createContext();

export function UserProvider({ children }) {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [profile, setProfile] = useState(null);

  // Handle auth state changes
  const handleAuthChange = useCallback((session) => {
    const currentUser = session?.user || null;
    setUser(currentUser);
    setLoading(true);
    
    if (currentUser) {
      // Load user profile
      supabase
        .from('user_profiles')
        .select('*')
        .eq('user_id', currentUser.id)
        .maybeSingle()
        .then(({ data, error }) => {
          if (error && error.code !== 'PGRST116') {
            console.error('Error loading user profile:', error);
            setError(handleApiError(error));
            setLoading(false);
            return;
          }
          
          if (!data) {
            // Generate default username
            const defaultUsername = `user_${Date.now().toString(36)}`;
            
            supabase
              .from('user_profiles')
              .insert([{
                user_id: currentUser.id,
                username: defaultUsername
              }])
              .select()
              .maybeSingle()
              .then(({ data: newProfile, error: createError }) => {
                if (createError) {
                  console.error('Error creating user profile:', createError);
                  setError(handleApiError(createError));
                } else {
                  setProfile(newProfile);
                }
                setLoading(false);
              });
          } else {
            setProfile(data);
            setLoading(false);
          }
        });
    } else {
      setProfile(null);
      setLoading(false);
      setLoading(false);
    }
  }, []);

  // Initialize auth state
  useEffect(() => {
    let mounted = true;

    // Set up auth state listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(handleAuthChange);
    
    // Check current session
    supabase.auth.getSession().then(({ data: { session } }) => {
      if (mounted) {
        handleAuthChange(session);
      }
    }).catch(err => {
      console.error('Error getting session:', err);
      if (mounted) {
        setError(err.message);
        setLoading(false);
      }
    });

    return () => {
      mounted = false;
      subscription.unsubscribe();
    };
  }, [handleAuthChange]);

  // Provide auth methods
  const signOut = useCallback(async () => {
    try {
      await supabase.auth.signOut();
    } catch (err) {
      console.error('Error signing out:', err);
      setError(err.message);
    }
  }, []);

  const value = {
    user,
    profile,
    loading,
    error,
    setLoading,
    signOut,
    clearError: () => setError(null)
  };

  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  );
}

export const useUser = () => {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error('useUser must be used within a UserProvider');
  }

  return context;
};