import React, { createContext, useContext, useState, useEffect, useRef } from 'react';

// Create the Animation Context
const AnimationContext = createContext({
  animationsEnabled: false,
  toggleAnimations: () => {},
  isHighPerformanceDevice: false,
});

// Custom hook to use the Animation Context
export const useAnimations = () => useContext(AnimationContext);

// Provider component for the Animation Context
export const AnimationProvider = ({ children }) => {
  const [animationsEnabled, setAnimationsEnabled] = useState(false);
  const timeoutRef = useRef(null);
  const [isHighPerformanceDevice, setIsHighPerformanceDevice] = useState(false);
  const warningShownRef = useRef(false);
  const performanceMonitorRef = useRef(null);

  // Check device performance on mount
  useEffect(() => {
    const checkDevicePerformance = () => {
      // Check if browser supports DeviceMemory API
      const memory = navigator.deviceMemory;
      // Check if browser supports hardware concurrency API
      const cpuCores = navigator.hardwareConcurrency || 0;
      
      // Consider high performance if:
      // 1. <PERSON>ce has 4GB+ RAM (if API is available), or
      // 2. <PERSON>ce has 4+ CPU cores
      const isHighEnd = (memory && memory >= 4) || cpuCores >= 4;
      
      // Check for Safari and iOS
      const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
      const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
      
      // More conservative performance settings for Safari and iOS
      setIsHighPerformanceDevice(isHighEnd && !isSafari && !isIOS);
      
      // Add Safari-specific optimizations
      if (isSafari || isIOS) {
        document.body.classList.add('reduce-animations');
        document.body.classList.add('safari-optimized');
      }
      
      return isHighEnd && !isSafari && !isIOS;
    };

    const isHighEnd = checkDevicePerformance();
    
    // Load animation preference from localStorage or decide based on device performance
    const storedPreference = localStorage.getItem('smiloAnimationsEnabled');
    
    if (storedPreference !== null) {
      setAnimationsEnabled(storedPreference === 'true');
    } else {
      // For first-time visitors, only enable animations on high-end devices by default
      const defaultValue = isHighEnd;
      setAnimationsEnabled(defaultValue);
      localStorage.setItem('smiloAnimationsEnabled', defaultValue.toString());
    }

    // Set up performance monitoring
    if (window.performance && window.performance.memory) {
      performanceMonitorRef.current = setInterval(() => {
        const memoryUsage = window.performance.memory.usedJSHeapSize;
        const memoryLimit = window.performance.memory.jsHeapSizeLimit;
        
        // If using more than 80% of available memory, disable animations
        if (memoryUsage > memoryLimit * 0.8 && animationsEnabled) {
          setAnimationsEnabled(false);
          localStorage.setItem('smiloAnimationsEnabled', 'false');
          console.log('Automatically disabled animations due to high memory usage');
        }
      }, 10000); // Check every 10 seconds
    }
    
    // Monitor for performance issues
    const handleVisibilityChange = () => {
      // DISABLED: This was causing the site to turn gray when switching tabs
      // The aggressive performance monitoring was too intrusive for user experience
      // if (document.visibilityState === 'visible' && animationsEnabled) {
      //   const pageLoadTime = window._pageLoadTime || Date.now();
      //   const timeOnPage = Date.now() - pageLoadTime;
      //   
      //   // Reduce timeout to 2 minutes for Safari/iOS
      //   const timeoutThreshold = (isSafari || isIOS) ? 120000 : 300000;
      //   
      //   if (timeOnPage > timeoutThreshold && animationsEnabled) {
      //     setAnimationsEnabled(false);
      //     localStorage.setItem('smiloAnimationsEnabled', 'false');
      //     console.log('Automatically disabled animations after extended session');
      //   }
      // }
    };
    
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      if (performanceMonitorRef.current) {
        clearInterval(performanceMonitorRef.current);
      }
    };
  }, []);

  // Reset memory leak timeout whenever animationsEnabled changes
  useEffect(() => {
    window._smiloAnimationsEnabled = animationsEnabled;
    
    if (animationsEnabled) {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      // Shorter timeout for Safari/iOS
      const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
      const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
      const timeoutDuration = (isSafari || isIOS) ? 90000 : 180000; // 1.5 minutes for Safari/iOS, 3 minutes for others
      
      timeoutRef.current = setTimeout(() => {
        setAnimationsEnabled(false);
        localStorage.setItem('smiloAnimationsEnabled', 'false');
        console.log('Safety timeout: Disabling animations to prevent performance degradation');
        window.dispatchEvent(new CustomEvent('smiloAnimationsToggled', { 
          detail: { enabled: false } 
        }));
      }, timeoutDuration);
    }
    
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [animationsEnabled]);
  
  const toggleAnimations = () => {
    const newValue = !animationsEnabled;
    
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }

    if (newValue && !warningShownRef.current) {
      const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
      const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
      const isLowMemoryDevice = navigator.deviceMemory && navigator.deviceMemory < 4;
      const isOlderDevice = navigator.hardwareConcurrency && navigator.hardwareConcurrency < 4;
      
      if (isSafari || isIOS || isLowMemoryDevice || isOlderDevice) {
        if (window.confirm('Enabling animations may affect performance on your browser or device. Continue?')) {
          warningShownRef.current = true;
        } else {
          return;
        }
      }
    }

    setAnimationsEnabled(newValue);
    localStorage.setItem('smiloAnimationsEnabled', newValue.toString());
    
    // Update body classes for Safari optimizations
    if (newValue) {
      document.body.classList.remove('reduce-animations');
    } else {
      document.body.classList.add('reduce-animations');
    }
    
    window.dispatchEvent(new CustomEvent('smiloAnimationsToggled', { 
      detail: { enabled: newValue } 
    }));
  };

  const contextValue = {
    animationsEnabled,
    toggleAnimations,
    isHighPerformanceDevice
  };

  return (
    <AnimationContext.Provider value={contextValue}>
      {children}
    </AnimationContext.Provider>
  );
};

export default AnimationContext; 