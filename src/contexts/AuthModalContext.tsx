import React, { createContext, useState, useContext, ReactNode } from 'react';

type UserType = 'patient' | 'practice';

interface AuthModalContextType {
  isOpen: boolean;
  userType: UserType;
  openModal: (type: UserType) => void;
  closeModal: () => void;
}

// Create context with default values
export const AuthModalContext = createContext<AuthModalContextType>({
  isOpen: false,
  userType: 'patient',
  openModal: () => {},
  closeModal: () => {}
});

// Hook for using the auth modal context
export const useAuthModal = () => useContext(AuthModalContext);

interface AuthModalProviderProps {
  children: ReactNode;
}

// Provider component
export const AuthModalProvider: React.FC<AuthModalProviderProps> = ({ children }) => {
  const [modalState, setModalState] = useState({
    isOpen: false,
    userType: 'patient' as UserType
  });

  const openModal = (type: UserType) => {
    // Ensure body scroll is disabled when modal opens
    document.body.style.overflow = 'hidden';
    // Force a small delay to ensure DOM is ready
    setTimeout(() => {
      setModalState({ isOpen: true, userType: type });
    }, 0);
  };

  const closeModal = () => {
    // Restore body scroll when modal closes
    document.body.style.overflow = 'auto';
    setModalState({ ...modalState, isOpen: false });
  };

  return (
    <AuthModalContext.Provider
      value={{
        isOpen: modalState.isOpen,
        userType: modalState.userType,
        openModal,
        closeModal
      }}
    >
      {children}
    </AuthModalContext.Provider>
  );
};

export default AuthModalProvider;