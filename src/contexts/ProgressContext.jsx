import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { useAuth } from './AuthContext';
import { handleApiError } from '../lib/utils/errorHandler';

const ProgressContext = createContext();

export function ProgressProvider({ children }) {
  const { user } = useAuth();
  const [progress, setProgress] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!user) {
      setProgress(null);
      setLoading(false);
      return;
    }

    loadProgress();
  }, [user]);

  const loadProgress = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('predental_progress')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      setProgress(data || { courses: [], gpa: 0, scienceGpa: 0 });
    } catch (err) {
      console.error('Error loading progress:', err);
      setError(handleApiError(err));
    } finally {
      setLoading(false);
    }
  };

  const updateProgress = async (updates) => {
    try {
      const { data, error } = await supabase
        .from('predental_progress')
        .upsert({
          user_id: user.id,
          ...updates,
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;
      setProgress(data);
      return data;
    } catch (err) {
      console.error('Error updating progress:', err);
      throw new Error(handleApiError(err));
    }
  };

  const value = {
    progress,
    loading,
    error,
    updateProgress,
    refreshProgress: loadProgress
  };

  return (
    <ProgressContext.Provider value={value}>
      {children}
    </ProgressContext.Provider>
  );
}

export const useProgress = () => {
  const context = useContext(ProgressContext);
  if (!context) {
    throw new Error('useProgress must be used within a ProgressProvider');
  }
  return context;
};