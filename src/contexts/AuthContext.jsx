import React, { createContext, useContext, useState, useEffect, useCallback, useMemo } from 'react';
import { supabase } from '../lib/supabase';
import { handleApiError } from '../lib/utils/errorHandler';
import { validateEmail, validatePassword } from '../lib/utils/validation';

const AuthContext = createContext(null);

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [session, setSession] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchUser = async () => {
      try {
        // Normal auth flow with better error handling
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();
        
        if (sessionError) {
          console.warn("Session retrieval error - please login again");
          setUser(null);
          setSession(null);
          setLoading(false);
          return;
        }
        
        if (session?.user) {
          // Validate session expiration
          const currentTime = Math.floor(Date.now() / 1000);
          if (session.expires_at && session.expires_at < currentTime) {
            console.log("Session expired, logging out");
            await supabase.auth.signOut();
            setUser(null);
            setSession(null);
            setLoading(false);
            return;
          }
          
          console.log("User session found");
          setUser(session.user);
          setSession(session);
        } else {
          console.log("No active session found");
          setUser(null);
          setSession(null);
        }
      } catch (err) {
        console.error('Error fetching user session');
        setUser(null);
        setSession(null);
      } finally {
        setLoading(false);
      }
    };

    fetchUser();

    // Subscribe to auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log(`Auth state changed: ${event}`);
        
        if (event === 'SIGNED_IN' && session?.user) {
          console.log("User signed in");
          setUser(session.user);
          setSession(session);
        } else if (event === 'SIGNED_OUT') {
          console.log("User signed out");
          setUser(null);
          setSession(null);
        } else if (event === 'TOKEN_REFRESHED' && session) {
          console.log("Session token refreshed");
          setSession(session);
        } else if (event === 'USER_UPDATED' && session?.user) {
          console.log("User data updated");
          setUser(session.user);
        }
      }
    );

    return () => {
      console.log("Cleaning up auth listener");
      subscription.unsubscribe();
    };
  }, []);

  const signIn = useCallback(async ({ email, password }) => {
    try {
      setError(null);
      
      if (!email || !password) {
        throw new Error('Email and password are required');
      }
      
      setLoading(true);
      console.log(`Attempting to sign in user: ${email}`);
      
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });
      
      if (error) {
        console.error("Authentication error:", error.message);
        setError(error.message);
        return null;
      }
      
      if (data?.user) {
        console.log("Sign in successful");
        setUser(data.user);
        setSession(data.session);
        return data;
      } else {
        console.error("No user data returned from sign in");
        setError("Failed to retrieve user data");
        return null;
      }
    } catch (error) {
      console.error("Error during sign in:", error.message);
      setError(error.message);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const signUp = async ({ email, password, username, firstName, lastName }) => {
    try {
      setError(null);
      setLoading(true);

      if (!validateEmail(email)) {
        throw new Error('Please enter a valid email address');
      }
      if (!validatePassword(password)) {
        throw new Error('Password must be at least 8 characters with numbers and special characters');
      }

      console.log(`Attempting to create user account: ${email}`);
      
      const { data, error: signUpError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            username,
            first_name: firstName,
            last_name: lastName,
            created_at: new Date().toISOString()
          }
        }
      });

      if (signUpError) {
        console.error("Sign up error:", signUpError.message);
        throw signUpError;
      }
      
      // Create user profile if signup successful
      if (data?.user?.id) {
        console.log("User account created, creating profile");
        try {
          const { error: profileError } = await supabase
            .from('user_profiles')
            .insert([{
              user_id: data.user.id,
              username: username || `user_${Date.now().toString(36)}`,
              first_name: firstName,
              last_name: lastName,
              created_at: new Date().toISOString()
            }]);
  
          if (profileError) {
            console.warn("Error creating user profile:", profileError.message);
          }
        } catch (profileErr) {
          console.warn("Exception creating profile:", profileErr.message);
        }
      }
      
      return data;
    } catch (err) {
      console.error('Sign up error:', err);
      throw new Error(handleApiError(err));
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    try {
      setError(null);
      setLoading(true);
      console.log("Signing out user");
      
      const { error } = await supabase.auth.signOut();
      if (error) {
        console.error("Sign out error:", error.message);
        throw error;
      }
      
      // Clear state even if there's an error
      setUser(null);
      setSession(null);
      console.log("User signed out successfully");
    } catch (err) {
      console.error('Sign out error:', err);
      throw new Error(handleApiError(err));
    } finally {
      setLoading(false);
    }
  };

  const resetPassword = async (email) => {
    try {
      setError(null);
      setLoading(true);
      
      if (!validateEmail(email)) {
        throw new Error('Please enter a valid email address');
      }

      console.log(`Sending password reset email to: ${email}`);
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`
      });

      if (error) {
        console.error("Password reset error:", error.message);
        throw error;
      }
      
      console.log("Password reset email sent successfully");
    } catch (err) {
      console.error('Password reset error:', err);
      throw new Error(handleApiError(err));
    } finally {
      setLoading(false);
    }
  };

  const clearError = () => setError(null);

  const value = useMemo(() => ({
    user,
    session,
    loading,
    error,
    signIn,
    signUp,
    signOut,
    resetPassword,
    clearError,
  }), [user, session, loading, error, signIn]);

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = React.useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};