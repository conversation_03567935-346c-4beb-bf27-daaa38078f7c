/* Import responsive utilities */
@import './styles/responsive.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Performance-optimized base styles */
@layer base {
  :root {
    color-scheme: dark;
    height: 100%;
    min-height: 100vh;
    min-height: calc(var(--vh, 1vh) * 100);
    -webkit-text-size-adjust: 100%;
    background-color: #0f172a;
    --spacing-mobile: 1rem;
    --min-touch-target: 44px;
    
    /* Performance optimization variables */
    --animation-duration-fast: 0.15s;
    --animation-duration-normal: 0.3s;
    --animation-duration-slow: 0.5s;
  }

  html {
    background: #0f172a;
    height: 100%;
    color: white;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    font-size: var(--font-size-base);
    /* Optimize font rendering */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  body {
    @apply text-white font-sans antialiased;
    min-height: 100%;
    min-height: calc(var(--vh, 1vh) * 100);
    width: 100%;
    background: linear-gradient(135deg,
      rgba(79, 70, 229, 0.4),
      rgba(99, 102, 241, 0.35),
      rgba(168, 85, 247, 0.4),
      rgba(59, 130, 246, 0.3)
    );
    background-attachment: fixed;
    background-size: 400% 400%;
    margin: 0;
    padding: 0;
    color: white;
    overscroll-behavior-y: none;
    /* Performance optimizations */
    contain: layout style;
    will-change: auto;
  }

  /* Conditionally apply gradient animation based on device capability */
  body:not(.reduced-animations):not(.is-low-end-device) {
    animation: gradientShift 8s ease infinite;
  }

  /* Simplified background for low-end devices */
  body.is-low-end-device {
    background: #0f172a;
    background-attachment: scroll;
  }

  #root {
    min-height: 100%;
    min-height: calc(var(--vh, 1vh) * 100);
    display: flex;
    flex-direction: column;
    isolation: isolate;
    position: relative;
    width: 100%;
    scroll-behavior: smooth;
    /* Performance optimization */
    contain: layout;
  }

  /* Improve input elements on mobile */
  input, select, textarea, button {
    -webkit-appearance: none;
    appearance: none;
    font-size: 16px; /* Prevents iOS zoom on focus */
    border-radius: 0;
    /* Optimize for better performance */
    will-change: auto;
  }

  /* Smooth font sizing with performance consideration */
  @media (max-width: 640px) {
    html {
      font-size: var(--font-size-mobile, 14px);
    }
  }

  @media (min-width: 641px) and (max-width: 768px) {
    html {
      font-size: calc(var(--font-size-mobile, 14px) + 1px);
    }
  }
}

/* Performance-optimized animations */
@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes pulse {
  0% { opacity: 0.7; }
  50% { opacity: 1; }
  100% { opacity: 0.7; }
}

@keyframes floating {
  0% { transform: translate3d(0, 0, 0); }
  50% { transform: translate3d(0, -20px, 0); }
  100% { transform: translate3d(0, 0, 0); }
}

@keyframes glow {
  0% { filter: drop-shadow(0 0 4px rgba(59, 130, 246, 0.2)); }
  50% { filter: drop-shadow(0 0 12px rgba(99, 102, 241, 0.5)); }
  100% { filter: drop-shadow(0 0 4px rgba(59, 130, 246, 0.2)); }
}

@layer utilities {
  .backdrop-blur-sm {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
  }

  /* Performance-aware backdrop blur for low-end devices */
  .is-low-end-device .backdrop-blur-sm {
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
    background-color: rgba(15, 23, 42, 0.9);
  }

  .smooth-transition {
    transition: all var(--animation-duration-normal) ease-in-out;
  }

  /* Optimized gradient text with fallback */
  .gradient-text {
    @apply bg-gradient-to-r from-blue-400 via-indigo-300 to-purple-400 text-transparent bg-clip-text;
    /* Fallback for better performance on some devices */
    -webkit-background-clip: text;
    background-clip: text;
  }

  .gradient-border {
    @apply relative before:absolute before:inset-0 before:p-[1px] before:rounded-lg before:bg-gradient-to-r before:from-indigo-500 before:via-purple-500 before:to-blue-500 before:-z-10;
  }

  /* Optimized animations with hardware acceleration */
  .animate-pulse-soft {
    animation: pulse 4s ease-in-out infinite;
    will-change: opacity;
  }

  .animate-float {
    animation: floating 6s ease-in-out infinite;
    will-change: transform;
  }

  .animate-glow {
    animation: glow 3s ease-in-out infinite;
    will-change: filter;
  }

  /* Disable animations for reduced-motion preference */
  @media (prefers-reduced-motion: reduce) {
    .animate-pulse-soft,
    .animate-float,
    .animate-glow,
    .animate-spin,
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }

  /* Aggressive animation reduction for performance */
  .reduced-animations .animate-pulse-soft,
  .reduced-animations .animate-float,
  .reduced-animations .animate-glow,
  .reduced-animations .animate-spin,
  .is-low-end-device .animate-pulse-soft,
  .is-low-end-device .animate-float,
  .is-low-end-device .animate-glow,
  .is-low-end-device .animate-spin {
    animation: none !important;
    will-change: auto !important;
  }

  /* Mobile-optimized animations class */
  .is-mobile .reduce-animations .animate-pulse-soft,
  .is-mobile .reduce-animations .animate-float,
  .is-mobile .reduce-animations .animate-glow {
    animation: none;
  }
}

@keyframes dash {
  from {
    stroke-dashoffset: 20;
  }
  to {
    stroke-dashoffset: 0;
  }
}

.animate-dash {
  stroke-dasharray: 20;
  animation: dash 3s linear infinite;
  /* Disable on low-end devices */
}

.is-low-end-device .animate-dash {
  animation: none;
}

/* Performance-optimized modal animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 20px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale3d(0.95, 0.95, 1);
  }
  to {
    opacity: 1;
    transform: scale3d(1, 1, 1);
  }
}

/* Performance-aware animation controls */
.reduce-animations *,
.reduced-animations *,
.is-low-end-device * {
  transition-duration: var(--animation-duration-fast) !important;
  animation-duration: var(--animation-duration-fast) !important;
  animation-iteration-count: 1 !important;
  will-change: auto !important;
}

/* Modal Classes with performance optimization */
.modal-overlay {
  @apply fixed inset-0 bg-black/80 z-[100];
  animation: fadeIn 0.2s ease-out;
  /* Hardware acceleration */
  transform: translateZ(0);
  will-change: opacity;
}

/* Disable modal animations for low-end devices */
.is-low-end-device .modal-overlay {
  animation: none;
  will-change: auto;
}

/* Only apply backdrop blur on capable devices */
@media (min-width: 768px) {
  .modal-overlay {
    @apply backdrop-blur-sm;
  }
}

.modal-container {
  @apply fixed inset-0 z-[110] overflow-y-auto;
}

/* Gradient Text */
.text-gradient-blue-purple {
  @apply bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-indigo-300 to-purple-400;
}

.modal-content {
  @apply inline-block w-full max-w-md p-6 my-8 text-left align-middle bg-gradient-to-b from-gray-900 to-gray-800 rounded-2xl shadow-xl transform transition-all;
  animation: scaleIn 0.3s ease-out;
}

/* Section styling */
.section-gradient {
  @apply relative overflow-hidden;
}

.section-gradient::before {
  @apply content-[''] absolute inset-0 bg-gradient-to-br from-indigo-900/20 via-transparent to-purple-900/20 pointer-events-none;
}

.glass-card {
  @apply bg-gray-900/80 border border-white/10 rounded-2xl p-6;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Only apply backdrop blur on non-mobile devices */
@media (min-width: 768px) {
  .glass-card {
    @apply bg-white/5 backdrop-blur-md;
  }
}

/* Mobile-first media queries */
@layer base {
  html {
    font-size: 14px;
  }

  @screen sm {
    html {
      font-size: 16px;
    }
  }
}

/* Touch target sizes */
@layer components {
  .touch-target {
    @apply min-h-[44px] min-w-[44px];
  }

  .touch-target-sm {
    @apply min-h-[36px] min-w-[36px];
  }

  /* Improved touch targets for mobile */
  @media (max-width: 768px) {
    button:not(.touch-exempt),
    a:not(.touch-exempt),
    label:not(.touch-exempt),
    input[type="checkbox"] + label,
    input[type="radio"] + label {
      min-height: var(--min-touch-target, 44px);
      min-width: var(--min-touch-target, 44px);
      padding: calc(var(--spacing-mobile, 1rem) / 2);
      display: inline-flex;
      align-items: center;
      justify-content: center;
    }

    input, select, textarea {
      min-height: var(--min-touch-target, 44px);
      font-size: 16px; /* Prevents iOS zoom */
    }
  }
}

/* Responsive container classes */
@layer utilities {
  .container-fluid {
    @apply w-full px-4 mx-auto;
    @screen sm { @apply px-6; }
    @screen lg { @apply px-8; }
  }

  .container-narrow {
    @apply w-full px-4 mx-auto max-w-3xl;
    @screen sm { @apply px-6; }
    @screen lg { @apply px-8; }
  }

  .container-wide {
    @apply w-full px-4 mx-auto max-w-7xl;
    @screen sm { @apply px-6; }
    @screen lg { @apply px-8; }
  }

  /* Mobile-specific spacing utilities */
  .p-mobile {
    padding: var(--spacing-mobile, 1rem);
  }

  .m-mobile {
    margin: var(--spacing-mobile, 1rem);
  }

  .px-mobile {
    padding-left: var(--spacing-mobile, 1rem);
    padding-right: var(--spacing-mobile, 1rem);
  }

  .py-mobile {
    padding-top: var(--spacing-mobile, 1rem);
    padding-bottom: var(--spacing-mobile, 1rem);
  }

  .mx-mobile {
    margin-left: var(--spacing-mobile, 1rem);
    margin-right: var(--spacing-mobile, 1rem);
  }

  .my-mobile {
    margin-top: var(--spacing-mobile, 1rem);
    margin-bottom: var(--spacing-mobile, 1rem);
  }
}

/* Responsive typography */
@layer utilities {
  .h1 {
    @apply text-3xl font-bold sm:text-4xl lg:text-5xl;
  }

  .h2 {
    @apply text-2xl font-bold sm:text-3xl lg:text-4xl;
  }

  .h3 {
    @apply text-xl font-semibold sm:text-2xl lg:text-3xl;
  }

  /* Mobile-optimized text utilities */
  .text-mobile-sm {
    font-size: 0.875rem;
  }

  .text-mobile-base {
    font-size: 1rem;
  }

  .text-mobile-lg {
    font-size: 1.125rem;
  }

  /* Adjust line heights for mobile */
  @media (max-width: 640px) {
    .h1, .h2, .h3, h1, h2, h3 {
      line-height: 1.2;
    }

    p, li, div {
      line-height: 1.5;
    }
  }
}

/* Enhanced Logo Animation Effects */
@keyframes ping-slow {
  0% {
    transform: scale(0.95);
    opacity: 0;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    transform: scale(1.3);
    opacity: 0;
  }
}

@keyframes pulse-slow {
  0% {
    opacity: 0.2;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 0.2;
  }
}

@keyframes floating-particles {
  0% {
    transform: translateY(0px) translateX(0px);
    opacity: 0;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    transform: translateY(-20px) translateX(5px);
    opacity: 0;
  }
}

@keyframes glowing-particles {
  0% {
    box-shadow: 0 0 2px #60A5FA;
  }
  100% {
    box-shadow: 0 0 8px #60A5FA;
  }
}

.animate-ping-slow {
  animation: ping-slow 3s cubic-bezier(0, 0, 0.2, 1) infinite;
}

.animate-pulse-slow {
  animation: pulse-slow 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Add cross-browser rendering optimizations */
/* This should be added near the top of your CSS */

/* Prevent Safari text rendering issues */
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Optimize animations for all browsers */
@media (prefers-reduced-motion: no-preference) {
  .animate-float {
    animation-name: float;
    animation-duration: 6s;
    animation-timing-function: ease-in-out;
    animation-iteration-count: infinite;
  }

  /* More efficient animation curve for float */
  @keyframes float {
    0% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
    100% {
      transform: translateY(0px);
    }
  }

  /* Optimize GPU acceleration for animations */
  .transform-gpu,
  .animate-float,
  .transition-transform,
  .transition-all {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
  }
}

/* Prevent Safari/Firefox glitches with background blurs */
.backdrop-blur-sm,
.blur-sm,
.blur-md,
.blur-lg,
.blur-xl,
.blur-2xl,
.blur-3xl {
  transform: translateZ(0);
}

/* Safari-specific fixes for gradient rendering */
@supports (-webkit-touch-callout: none) {
  .bg-gradient-to-r,
  .bg-gradient-to-l,
  .bg-gradient-to-br,
  .bg-gradient-to-bl,
  .bg-gradient-to-tr,
  .bg-gradient-to-tl {
    background-attachment: scroll;
  }
}

/* Fix Firefox gradient text rendering */
@-moz-document url-prefix() {
  .bg-clip-text {
    -webkit-background-clip: text;
    background-clip: text;
  }
}