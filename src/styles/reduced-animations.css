/**
 * Reduced animations for better performance on mobile devices
 * This file contains styles that reduce or eliminate animations
 * on devices that may struggle with performance
 */

/* Apply reduced animations when the class is present */
.reduced-animations * {
  /* Reduce transition durations */
  transition-duration: 0.15s !important;
  
  /* Limit animation iterations */
  animation-iteration-count: 1 !important;
  
  /* Reduce animation durations */
  animation-duration: 0.3s !important;
  
  /* Optimize GPU usage */
  will-change: auto !important;
}

/* Completely disable animations for very low-end devices */
.minimal-mode * {
  /* Disable transitions */
  transition: none !important;
  
  /* Disable animations */
  animation: none !important;
  
  /* Disable transforms */
  transform: none !important;
  
  /* Disable filters */
  filter: none !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
}

/* Specific optimizations for iOS Safari */
.ios-optimized {
  /* Prevent iOS Safari from creating a new layer for fixed elements */
  .fixed,
  [style*="position:fixed"],
  [style*="position: fixed"] {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
  }
  
  /* Optimize scrolling */
  .overflow-auto,
  .overflow-y-auto,
  .overflow-scroll,
  .overflow-y-scroll {
    -webkit-overflow-scrolling: touch;
  }
  
  /* Prevent text size adjustment */
  * {
    -webkit-text-size-adjust: none;
  }
}

/* Safari-specific optimizations */
.safari-optimized {
  /* Prevent Safari from creating a new layer for these properties */
  [style*="transform"],
  [style*="opacity"],
  [style*="filter"] {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
  }
  
  /* Fix for Safari's handling of position:fixed elements */
  .fixed,
  [style*="position:fixed"],
  [style*="position: fixed"] {
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
  }
}

/* Optimize performance for low-end devices */
.optimize-performance {
  /* Disable box-shadows */
  .shadow-lg,
  .shadow-xl,
  .shadow-2xl {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  }
  
  /* Disable backdrop blur */
  .backdrop-blur-sm,
  .backdrop-blur-md,
  .backdrop-blur-lg,
  .backdrop-blur-xl {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    background-color: rgba(15, 23, 42, 0.9) !important;
  }
}

/* Media query to automatically apply reduced animations on mobile devices */
@media (max-width: 768px) {
  .auto-reduce-animations * {
    transition-duration: 0.15s !important;
    animation-iteration-count: 1 !important;
    animation-duration: 0.3s !important;
  }
}

/* Apply reduced animations when prefers-reduced-motion is set */
@media (prefers-reduced-motion) {
  * {
    transition-duration: 0.15s !important;
    animation-duration: 0.3s !important;
    animation-iteration-count: 1 !important;
    scroll-behavior: auto !important;
  }
  
  .animate-spin,
  .animate-ping,
  .animate-pulse,
  .animate-bounce {
    animation: none !important;
  }
}
