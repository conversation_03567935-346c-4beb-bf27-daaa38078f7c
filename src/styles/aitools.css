/* Custom gradient animations */
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.gradient-text {
  background-size: 200% auto;
  animation: gradient-shift 5s ease infinite;
}

/* Glow effects */
.glow {
  position: relative;
}

.glow::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: inherit;
  background: radial-gradient(
    circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
    rgba(var(--glow-color, 59, 130, 246), 0.15),
    transparent 50%
  );
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s;
}

.glow:hover::before {
  opacity: 1;
}

/* Card hover effects */
.tool-card {
  transform-style: preserve-3d;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.tool-card:hover {
  transform: translateY(-5px);
}

.tool-card::after {
  content: '';
  position: absolute;
  inset: -1px;
  background: linear-gradient(
    to bottom right,
    transparent,
    rgba(var(--card-glow-color, 59, 130, 246), 0.1)
  );
  border-radius: inherit;
  z-index: -1;
  transition: opacity 0.3s;
  opacity: 0;
}

.tool-card:hover::after {
  opacity: 1;
}

/* Icon pulse animation */
@keyframes icon-pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.icon-container {
  animation: icon-pulse 2s ease-in-out infinite;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.1);
}

::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.5);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.7);
}

/* Tool card themes */
.webcam-theme {
  --card-glow-color: 59, 130, 246;
}

.voice-theme {
  --card-glow-color: 168, 85, 247;
}

.thermal-theme {
  --card-glow-color: 249, 115, 22;
}

.breath-theme {
  --card-glow-color: 14, 165, 233;
}

/* Loading animation */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.loading-shimmer {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.03) 25%,
    rgba(255, 255, 255, 0.08) 50%,
    rgba(255, 255, 255, 0.03) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
} 