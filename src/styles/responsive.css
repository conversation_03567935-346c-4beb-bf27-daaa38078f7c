/* Responsive Utilities for Smilo Dental App */

/* Viewport-specific font scaling */
:root {
  --font-size-base: 16px;
  --font-size-mobile: 14px;
  --font-size-tablet: 16px;
  --font-size-desktop: 16px;

  --content-width-sm: 100%;
  --content-width-md: 90%;
  --content-width-lg: 85%;
  --content-width-xl: 75%;

  /* Mobile viewport height fix for iOS and other mobile browsers */
  --vh: 1vh;

  /* Touch target sizes based on device type */
  --min-touch-target-mobile: 44px;
  --min-touch-target-tablet: 40px;
  --min-touch-target-desktop: 36px;

  /* Spacing variables for consistent spacing */
  --space-mobile-xs: 0.25rem;
  --space-mobile-sm: 0.5rem;
  --space-mobile-md: 0.75rem;
  --space-mobile-lg: 1rem;
  --space-mobile-xl: 1.5rem;

  /* Fixed safe area insets */
  --safe-area-top: env(safe-area-inset-top, 0px);
  --safe-area-bottom: env(safe-area-inset-bottom, 0px);
  --safe-area-left: env(safe-area-inset-left, 0px);
  --safe-area-right: env(safe-area-inset-right, 0px);
  
  /* Breakpoints as CSS variables for JS access */
  --breakpoint-xs: 480px;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
}

/* Device-specific variables */
@media (max-width: 480px) {
  :root {
    font-size: var(--font-size-mobile);
    --min-touch-target: var(--min-touch-target-mobile);
    --spacing-mobile: var(--space-mobile-md);
  }
}

@media (min-width: 481px) and (max-width: 767px) {
  :root {
    font-size: calc(var(--font-size-mobile) + 1px);
    --min-touch-target: var(--min-touch-target-mobile);
    --spacing-mobile: var(--space-mobile-lg);
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  :root {
    font-size: var(--font-size-tablet);
    --min-touch-target: var(--min-touch-target-tablet);
    --spacing-mobile: var(--space-mobile-xl);
  }
}

@media (min-width: 1024px) {
  :root {
    font-size: var(--font-size-desktop);
    --min-touch-target: var(--min-touch-target-desktop);
  }
}

/* Safe area insets for notched devices */
.safe-area-padding {
  padding: var(--safe-area-top) var(--safe-area-right) var(--safe-area-bottom) var(--safe-area-left);
}

.safe-top {
  padding-top: var(--safe-area-top);
}

.safe-area-pt {
  padding-top: var(--safe-area-top);
}

/* iOS specific fixes for fixed positioning */
.fixed-ios {
  position: fixed;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}

/* Mobile navigation open state */
.mobile-nav-open {
  position: fixed;
  overflow: hidden;
  width: 100%;
  height: 100%;
  touch-action: none;
  -webkit-overflow-scrolling: none;
}

.safe-area-padding-horizontal {
  padding-left: var(--safe-area-left);
  padding-right: var(--safe-area-right);
}

.safe-area-padding-vertical {
  padding-top: var(--safe-area-top);
  padding-bottom: var(--safe-area-bottom);
}

.safe-area-margin-bottom {
  margin-bottom: var(--safe-area-bottom);
}

.safe-area-margin-top {
  margin-top: var(--safe-area-top);
}

.safe-area-pb {
  padding-bottom: max(0.5rem, var(--safe-area-bottom));
}

.safe-area-pt {
  padding-top: max(0.5rem, var(--safe-area-top));
}

/* Responsive containers that scale with device width */
.responsive-container {
  width: var(--content-width-sm);
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--spacing-mobile, 1rem);
  padding-right: var(--spacing-mobile, 1rem);
}

@media (min-width: 640px) {
  .responsive-container {
    width: var(--content-width-md);
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .responsive-container {
    width: var(--content-width-lg);
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

@media (min-width: 1280px) {
  .responsive-container {
    width: var(--content-width-xl);
    max-width: 1400px;
  }
}

/* Responsive grid system */
.responsive-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-mobile, 1rem);
}

@media (min-width: 640px) {
  .responsive-grid {
    grid-template-columns: repeat(8, 1fr);
    gap: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .responsive-grid {
    grid-template-columns: repeat(12, 1fr);
    gap: 2rem;
  }
}

/* Enhanced responsive grid with auto-fit for card layouts */
.responsive-card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-mobile, 1rem);
}

@media (min-width: 640px) {
  .responsive-card-grid {
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .responsive-card-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
  }
}

/* Responsive padding and margin utilities */
.p-responsive {
  padding: var(--spacing-mobile, 0.75rem);
}

.m-responsive {
  margin: var(--spacing-mobile, 0.75rem);
}

@media (min-width: 640px) {
  .p-responsive {
    padding: 1rem;
  }

  .m-responsive {
    margin: 1rem;
  }
}

@media (min-width: 1024px) {
  .p-responsive {
    padding: 1.5rem;
  }

  .m-responsive {
    margin: 1.5rem;
  }
}

/* Responsive typography */
.text-responsive {
  font-size: 1rem;
  line-height: 1.5;
}

.title-responsive {
  font-size: 1.5rem;
  line-height: 1.25;
}

@media (min-width: 640px) {
  .title-responsive {
    font-size: 1.75rem;
  }
}

@media (min-width: 1024px) {
  .title-responsive {
    font-size: 2rem;
  }
}

/* Improved responsive heading system */
.h1-responsive {
  font-size: 1.75rem;
  line-height: 1.2;
  font-weight: 700;
}

.h2-responsive {
  font-size: 1.5rem;
  line-height: 1.3;
  font-weight: 600;
}

.h3-responsive {
  font-size: 1.25rem;
  line-height: 1.4;
  font-weight: 600;
}

@media (min-width: 640px) {
  .h1-responsive {
    font-size: 2.25rem;
  }
  
  .h2-responsive {
    font-size: 1.75rem;
  }
  
  .h3-responsive {
    font-size: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .h1-responsive {
    font-size: 3rem;
  }
  
  .h2-responsive {
    font-size: 2.25rem;
  }
  
  .h3-responsive {
    font-size: 1.75rem;
  }
}

/* Enhanced mobile viewport height handling */
.vh-fix {
  height: calc(var(--vh, 1vh) * 100);
}

.min-vh-fix {
  min-height: calc(var(--vh, 1vh) * 100);
}

.max-vh-fix {
  max-height: calc(var(--vh, 1vh) * 100);
}

/* Viewport height percentages */
.vh-fix-75 {
  height: calc(var(--vh, 1vh) * 75);
}

.vh-fix-50 {
  height: calc(var(--vh, 1vh) * 50);
}

.min-vh-fix-75 {
  min-height: calc(var(--vh, 1vh) * 75);
}

.min-vh-fix-50 {
  min-height: calc(var(--vh, 1vh) * 50);
}

/* Optimized touch targets for mobile */
@media (max-width: 767px) {
  .touch-target-mobile {
    min-height: var(--min-touch-target-mobile);
    min-width: var(--min-touch-target-mobile);
  }
  
  /* Ensure interactive elements have proper sizing */
  button:not(.touch-exempt),
  a:not(.touch-exempt),
  [role="button"]:not(.touch-exempt),
  input[type="button"]:not(.touch-exempt),
  input[type="submit"]:not(.touch-exempt),
  input[type="reset"]:not(.touch-exempt) {
    min-height: var(--min-touch-target-mobile);
    min-width: var(--min-touch-target-mobile);
    padding: 0.5rem;
  }
  
  /* Ensure proper spacing between interactive elements */
  .touch-spacing > * {
    margin-bottom: 0.75rem;
  }
  
  .touch-spacing > *:last-child {
    margin-bottom: 0;
  }
  
  /* Prevent text from being too small */
  input, select, textarea, button {
    font-size: 16px !important;
  }
}

/* Prevent overflow issues on mobile */
.prevent-overflow {
  overflow-x: hidden;
  max-width: 100vw;
}

/* Responsive images */
img.responsive {
  max-width: 100%;
  height: auto;
}

/* Responsive tables */
.table-responsive {
  display: block;
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

/* Improved mobile scrolling */
.smooth-scroll {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
  scrollbar-width: thin;
}

/* Responsive flex layouts */
.flex-responsive {
  display: flex;
  flex-direction: column;
}

@media (min-width: 768px) {
  .flex-responsive {
    flex-direction: row;
  }
}

/* Improved gap utilities */
.gap-responsive {
  gap: 1rem;
}

@media (min-width: 640px) {
  .gap-responsive {
    gap: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .gap-responsive {
    gap: 2rem;
  }
}

/* Mobile-specific touch target improvements */
@media (max-width: 767px) {
  .list-spacing-mobile > * {
    margin-bottom: var(--spacing-mobile, 1rem);
  }

  .list-spacing-mobile > *:last-child {
    margin-bottom: 0;
  }

  /* Mobile-friendly form elements */
  input,
  textarea,
  select,
  button {
    font-size: 16px !important; /* Prevent auto-zoom on iOS */
    max-width: 100%;
    border-radius: 8px;
  }

  /* Improve scrolling behavior */
  div, section, article, main {
    -webkit-overflow-scrolling: touch;
  }

  .prevent-overflow-x {
    max-width: 100%;
    overflow-x: hidden;
  }
}

/* Responsive images */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Responsive object-fit images for different aspect ratios */
.img-cover {
  object-fit: cover;
  width: 100%;
  height: 100%;
}

.img-contain {
  object-fit: contain;
  width: 100%;
  height: 100%;
}

.aspect-ratio-16-9 {
  aspect-ratio: 16/9;
}

.aspect-ratio-4-3 {
  aspect-ratio: 4/3;
}

.aspect-ratio-1-1 {
  aspect-ratio: 1/1;
}

/* Viewport Height Utilities */
.full-viewport-height {
  height: 100vh;
  height: calc(var(--vh, 1vh) * 100);
}

.half-viewport-height {
  height: 50vh;
  height: calc(var(--vh, 1vh) * 50);
}

/* Orientation specific styles */
@media screen and (orientation: portrait) {
  .portrait-only {
    display: block;
  }

  .landscape-only {
    display: none;
  }
}

@media screen and (orientation: landscape) {
  .portrait-only {
    display: none;
  }

  .landscape-only {
    display: block;
  }
}

/* Compact design for landscape mode on small devices */
@media screen and (max-height: 480px) and (orientation: landscape) {
  .compact-landscape {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }

  .compact-landscape h1,
  .compact-landscape h2,
  .compact-landscape .h1,
  .compact-landscape .h2 {
    font-size: 1.25rem;
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
  }

  .stretch-landscape {
    max-height: 90vh;
    max-height: calc(var(--vh, 1vh) * 90);
    overflow-y: auto;
  }
}

/* Fixed height utilities that work better cross-device */
.min-h-screen-fixed {
  min-height: 100vh;
  min-height: calc(var(--vh, 1vh) * 100);
}

.h-screen-fixed {
  height: 100vh;
  height: calc(var(--vh, 1vh) * 100);
}

/* Mobile visibility utilities */
.mobile-visible {
  display: none;
}

@media (max-width: 767px) {
  .mobile-visible {
    display: block;
  }
}

.mobile-hidden {
  display: block;
}

@media (max-width: 767px) {
  .mobile-hidden {
    display: none;
  }
}

html.mobile-visible,
body.mobile-visible {
  /* For iOS, prevent scroll on mobile menu open */
  position: fixed;
  overflow: hidden;
  width: 100%;
  height: 100%;
  touch-action: none;
  -webkit-overflow-scrolling: none;
}

/* Additional mobile-specific utilities */
@media (max-width: 767px) {
  .mobile-full-height {
    height: 100%;
    height: calc(var(--vh, 1vh) * 100);
  }

  /* Force hardware acceleration for smoother animations on mobile */
  .force-gpu {
    transform: translateZ(0);
    will-change: transform;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
  }

  /* Handle bottom navigation or toolbars on mobile */
  .mobile-safe-bottom {
    padding-bottom: calc(env(safe-area-inset-bottom, 0px) + 1rem);
  }
}

.mobile-visible {
  display: none;
}

@media (max-width: 767px) {
  .mobile-visible {
    display: block;
  }
}

.mobile-hidden {
  display: block;
}

@media (max-width: 767px) {
  .mobile-hidden {
    display: none;
  }
}

@media (max-width: 480px) {
  .sm-content-visible {
    display: block;
  }
}

/* Table responsive utilities */
.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

@media (max-width: 767px) {
  .table-responsive table {
    min-width: 500px;
  }
}

/* Display utilities for different screen sizes */
.hidden-xs {
  display: none;
}

@media (min-width: 481px) {
  .hidden-xs {
    display: block;
  }
}

.hidden-sm {
  display: block;
}

@media (max-width: 640px) {
  .hidden-sm {
    display: none;
  }
}

.shown-sm {
  display: none;
}

@media (max-width: 640px) {
  .shown-sm {
    display: block;
  }
}

.hidden-md {
  display: block;
}

@media (max-width: 768px) {
  .hidden-md {
    display: none;
  }
}

.shown-md {
  display: none;
}

@media (max-width: 768px) {
  .shown-md {
    display: block;
  }
}

/* Stacking order for mobile layouts */
@media (max-width: 767px) {
  .stack-first {
    order: -1;
  }
  
  .stack-last {
    order: 999;
  }
}

/* Center elements on smaller screens */
@media (max-width: 640px) {
  .center-on-mobile {
    text-align: center;
    margin-left: auto;
    margin-right: auto;
  }
  
  .center-on-mobile > * {
    margin-left: auto;
    margin-right: auto;
  }
}

/* Print-friendly styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
  
  body {
    background: white !important;
    color: black !important;
  }
  
  a {
    text-decoration: underline;
  }
}