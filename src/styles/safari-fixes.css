/**
 * Safari-specific fixes and optimizations
 * Applied when Safari browser is detected
 */

/* Fix for flex and grid layout issues in Safari */
.safari .flex,
.safari [class*="flex-"] {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
}

/* Fix for grid layouts in Safari */
.safari .grid,
.safari [class*="grid-"] {
  display: -ms-grid;
  display: grid;
}

/* Force hardware acceleration on all animated elements in Safari */
.safari .animate-spin,
.safari .animate-pulse-soft,
.safari .animate-float,
.safari .animate-glow,
.safari [class*="transition-"],
.safari .motion-safe\:hover\:scale,
.safari .motion-safe\:focus\:scale,
.safari .motion-safe\:active\:scale {
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-perspective: 1000;
  perspective: 1000;
  will-change: transform;
}

/* Fix for sticky positioning in Safari */
.safari .sticky,
.safari [class*="sticky"] {
  position: -webkit-sticky;
  position: sticky;
  z-index: 10;
}

/* Fix for CSS grid auto-placement in Safari */
.safari .grid-flow-row,
.safari .grid-flow-col {
  grid-auto-flow: row;
}

/* Fix for viewport height in Safari */
.safari .h-screen,
.safari .min-h-screen,
.safari .max-h-screen {
  height: calc(var(--vh, 1vh) * 100);
  min-height: calc(var(--vh, 1vh) * 100);
  max-height: calc(var(--vh, 1vh) * 100);
}

/* Fix for background attachment in Safari on iOS */
@supports (-webkit-touch-callout: none) {
  .safari .bg-fixed,
  .safari [style*="background-attachment: fixed"] {
    background-attachment: scroll !important;
  }
}

/* Fix for backdrop filters in Safari */
.safari .backdrop-blur-sm,
.safari .backdrop-blur,
.safari .backdrop-blur-md,
.safari .backdrop-blur-lg,
.safari .backdrop-blur-xl {
  -webkit-backdrop-filter: saturate(180%) blur(8px);
  backdrop-filter: saturate(180%) blur(8px);
}

/* Fix for scrolling in Safari */
.safari {
  -webkit-overflow-scrolling: touch;
}

/* Fix opacity transitions in Safari */
.safari [class*="opacity-"] {
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}

/* Reduce animation complexity for Safari */
.safari.reduced-motion * {
  -webkit-transition-duration: 0.1s !important;
  transition-duration: 0.1s !important;
  -webkit-animation-duration: 0.1s !important;
  animation-duration: 0.1s !important;
}

/* Fix for gradient animations in Safari */
.safari [class*="bg-gradient"] {
  -webkit-animation: none !important;
  animation: none !important;
  background-size: 100% 100% !important;
}

/* Fix flex gap issues in Safari < 14.1 */
@supports not (gap: 1rem) {
  .safari .gap-4 > * + * {
    margin-left: 1rem;
  }
  
  .safari .flex-col.gap-4 > * + * {
    margin-left: 0;
    margin-top: 1rem;
  }
}

/* Fix for transforms in Safari */
.safari [class*="scale-"],
.safari [class*="rotate-"],
.safari [class*="translate-"] {
  -webkit-transform-style: preserve-3d;
  transform-style: preserve-3d;
}

/* Fix for smooth scrolling in Safari */
.safari html {
  scroll-behavior: auto;
}

/* Fix for clipping issues in Safari */
.safari .overflow-hidden {
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}

/* Fix for Safari's rendering of position: fixed elements */
.safari .fixed {
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}

/* Ensure proper height calculation in Safari */
.safari #root {
  min-height: -webkit-fill-available;
}

/* Reduce complexity of box-shadows in Safari */
.safari [class*="shadow"] {
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}

/* Fix input styling in Safari */
.safari input,
.safari select,
.safari textarea {
  -webkit-appearance: none;
  border-radius: 0;
  font-size: 16px !important; /* prevents zoom on focus */
}

/* Fix button styling in Safari */
.safari button {
  -webkit-appearance: none;
  appearance: none;
}

/* Fix iOS form zoom issues */
@media not all and (min-resolution:.001dpcm) { 
  @supports (-webkit-appearance:none) and (stroke-color:transparent) {
    .safari input[type="text"],
    .safari input[type="email"],
    .safari input[type="password"],
    .safari input[type="number"],
    .safari input[type="search"],
    .safari input[type="tel"],
    .safari input[type="url"],
    .safari textarea,
    .safari select {
      font-size: 16px !important;
    }
  }
}

/* Fix text rendering in Safari */
.safari {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Fix for position: sticky bottom */
.safari .sticky-bottom {
  position: -webkit-sticky;
  position: sticky;
  bottom: 0;
}

/* Fix iOS momentum scrolling */
.safari .scrollable {
  -webkit-overflow-scrolling: touch;
  overflow-y: auto;
}

/* Fix for Safari's slow CSS filters */
.safari [class*="filter-"],
.safari [class*="blur-"],
.safari [style*="filter:"] {
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}

/* Fix for SVG rendering in Safari */
.safari svg {
  transform: translateZ(0);
} 