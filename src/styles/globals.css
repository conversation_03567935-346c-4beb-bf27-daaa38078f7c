@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 255, 255, 255;
  --background-start-rgb: 0, 0, 0;
  --background-end-rgb: 0, 0, 0;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

/* Animations for voice analysis */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes progress {
  from {
    width: 0%;
  }
}

.animate-fadeIn {
  animation: fadeIn 0.4s ease-out forwards;
}

.animate-pulse {
  animation: pulse 1.5s infinite;
}

.animate-progress {
  animation: progress 0.6s ease-out forwards;
}

/* Additional utility classes */
.backdrop-blur-lg {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

/* Microphone button active state */
.mic-active {
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.5);
}

/* Custom scrollbar for results */
.analysis-results::-webkit-scrollbar {
  width: 6px;
}

.analysis-results::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.analysis-results::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.analysis-results::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
} 