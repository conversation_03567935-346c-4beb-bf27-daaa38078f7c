/* Recipe Carousel Styles */
.recipe-carousel {
  position: relative;
  padding: 1.5rem;
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  margin-bottom: 2rem;
}

.recipe-carousel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.recipe-counter {
  font-size: 0.875rem;
  color: #6366f1;
  font-weight: 500;
}

/* Navigation buttons */
.recipe-nav-button {
  position: absolute;
  top: 50%;
  width: 40px;
  height: 40px;
  background-color: #598615;
  border-radius: 50%;
  z-index: 10;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  transform: translateY(-50%);
  box-shadow: 0 2px 5px rgba(0,0,0,0.15);
}

.recipe-nav-button:hover {
  background-color: #68A511;
}

.recipe-nav-button::after {
  content: '';
  width: 10px;
  height: 10px;
  border-top: 2px solid white;
  border-right: 2px solid white;
  transition: transform 0.2s ease;
}

.recipe-nav-prev {
  left: 10px;
}

.recipe-nav-next {
  right: 10px;
}

.recipe-nav-prev::after {
  transform: rotate(-135deg);
}

.recipe-nav-next::after {
  transform: rotate(45deg);
}

.recipe-nav-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Recipe Cards */
.recipe-card {
  border-radius: 0.5rem;
  overflow: hidden;
  background-color: white;
  height: 100%;
}

.recipe-card-inner {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.recipe-header {
  display: flex;
  flex-direction: column;
}

@media (min-width: 640px) {
  .recipe-header {
    flex-direction: column;
  }
  
  .recipe-image-container {
    width: 100%;
    height: 240px;
  }
  
  .recipe-cost {
    width: 100%;
    border-radius: 0;
  }
}

.recipe-image-container {
  position: relative;
  width: 100%;
  height: 220px;
  overflow: hidden;
}

@media (min-width: 640px) {
  .recipe-image-container {
    width: 40%;
    height: 240px;
  }
}

.recipe-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.recipe-card:hover .recipe-image {
  transform: scale(1.05);
}

.recipe-cost {
  position: relative;
  bottom: auto;
  left: auto;
  background-color: #598615;
  color: white;
  padding: 0.5rem 0.75rem;
  font-weight: 600;
  font-size: 0.875rem;
  margin-top: 0.5rem;
  width: 100%;
  text-align: center;
}

/* Local Price Styling */
.local-price {
  display: flex;
  flex-direction: column;
  padding: 0.75rem;
  width: 100%;
  background-color: #68A511;
  margin-top: 0.5rem;
  text-align: center;
}

.price-value {
  font-size: 1.1rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.price-store {
  font-size: 0.75rem;
  opacity: 0.9;
}

.price-availability {
  font-size: 0.7rem;
  opacity: 0.8;
  margin-top: 0.25rem;
}

.loading-text {
  font-style: italic;
  opacity: 0.8;
  font-size: 0.8rem;
}

.recipe-main-content {
  padding: 1rem;
  flex: 1;
}

.recipe-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #4338ca;
  margin-bottom: 0.5rem;
}

.recipe-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.recipe-tag {
  background-color: #eef2ff;
  color: #4f46e5;
  font-size: 0.75rem;
  border-radius: 9999px;
  padding: 0.25rem 0.75rem;
}

.recipe-description {
  font-size: 0.875rem;
  color: #4b5563;
  line-height: 1.5;
}

/* Recipe Details */
.recipe-details {
  padding: 1rem;
  border-top: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.recipe-section-title {
  font-weight: 600;
  color: #4338ca;
  margin-bottom: 0.5rem;
  font-size: 1rem;
}

.recipe-list {
  padding-left: 1.5rem;
  margin-bottom: 1rem;
  font-size: 0.875rem;
  color: #4b5563;
}

.recipe-steps li {
  margin-bottom: 0.5rem;
}

.recipe-benefits-text {
  font-size: 0.875rem;
  color: #4b5563;
  line-height: 1.5;
}

/* Recipe Actions */
.recipe-actions {
  display: flex;
  gap: 0.75rem;
  padding: 1rem;
  border-top: 1px solid #e5e7eb;
}

.recipe-details-btn, .recipe-add-btn {
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.recipe-details-btn {
  background-color: #eef2ff;
  color: #4f46e5;
}

.recipe-details-btn:hover {
  background-color: #e0e7ff;
}

.recipe-add-btn {
  background-color: #598615;
  color: white;
  flex: 1;
}

.recipe-add-btn:hover {
  background-color: #68A511;
}

/* Store selector and ZIP code form */
.store-selector {
  background-color: #f3f4f6;
  border-radius: 0.5rem;
  padding: 0.75rem;
  margin-bottom: 1rem;
}

.zip-form {
  display: flex;
  align-items: center;
}

.zip-input {
  width: 150px;
}

.store-dropdown {
  min-width: 200px;
}

/* Store Map */
.store-map-container {
  margin-top: 0.75rem;
  border-radius: 0.375rem;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  height: 180px;
}

.store-map {
  width: 100%;
  height: 100%;
  background-color: #e5e7eb;
}

.store-map-toggle {
  display: flex;
  align-items: center;
  font-size: 0.75rem;
  color: #4f46e5;
  cursor: pointer;
  margin-top: 0.5rem;
}

.store-map-toggle svg {
  margin-right: 0.25rem;
  width: 1rem;
  height: 1rem;
}

/* Radius selector */
.radius-selector {
  display: flex;
  align-items: center;
  margin-right: 0.75rem;
}

.radius-label {
  font-size: 0.875rem;
  color: #4b5563;
  margin-right: 0.5rem;
}

.radius-select {
  background-color: white;
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;
  padding: 0.375rem 0.5rem;
  font-size: 0.875rem;
  color: #1f2937;
}

/* Make sure pagination works with new components */
.swiper-pagination {
  position: static;
  margin-top: 1rem;
}

.swiper-pagination-bullet {
  background-color: #6366f1;
  opacity: 0.5;
}

.swiper-pagination-bullet-active {
  opacity: 1;
  background-color: #4f46e5;
}

/* Single recipe view specific styles */
.single-recipe-view .recipe-slide {
  height: auto;
}

/* SNAP Eligibility Badge */
.snap-eligible-badge {
  display: flex;
  align-items: center;
  font-size: 0.7rem;
  margin-top: 0.25rem;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  padding: 0.1rem 0.3rem;
  width: fit-content;
  margin-left: auto;
  margin-right: auto;
}

.snap-icon {
  margin-right: 0.2rem;
}

.price-note {
  font-size: 0.65rem;
  opacity: 0.8;
  margin-top: 0.25rem;
  font-style: italic;
}

.check-price-btn {
  background-color: rgba(255, 255, 255, 0.9);
  color: #4a7b0f;
  border-radius: 4px;
  padding: 0.2rem 0.5rem;
  font-size: 0.7rem;
  margin-top: 0.4rem;
  display: inline-block;
  transition: all 0.2s ease;
  text-decoration: none;
  font-weight: 500;
}

.check-price-btn:hover {
  background-color: white;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* Dental Health Rating */
.dental-health-rating {
  display: flex;
  align-items: center;
  font-size: 0.75rem;
  margin-bottom: 0.5rem;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  width: fit-content;
}

.dental-excellent {
  background-color: #d1fae5;
  color: #047857;
}

.dental-good {
  background-color: #e0f2fe;
  color: #0369a1;
}

.dental-fair {
  background-color: #fef3c7;
  color: #92400e;
}

.dental-poor {
  background-color: #fee2e2;
  color: #b91c1c;
}

.dental-icon {
  margin-right: 0.3rem;
} 