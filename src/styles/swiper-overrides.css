/* Custom Swiper.js overrides */

/* Carousel container adjustments */
.recipe-carousel {
  position: relative;
  padding: 0 40px; /* Add horizontal padding for navigation buttons */
  margin-bottom: 50px;
  overflow: visible; /* Ensure navigation buttons outside the container are visible */
  z-index: 1; /* Ensure proper stacking context */
}

/* Carousel header with counter */
.recipe-carousel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.recipe-counter {
  font-size: 14px;
  color: #68A511;
  background: #f0f9eb;
  padding: 4px 12px;
  border-radius: 20px;
  font-weight: 600;
}

.recipe-swiper {
  padding: 10px 5px 40px 5px;
  position: relative;
}

.recipe-slide {
  height: auto;
  display: flex;
}

/* Single Recipe View Styles */
.single-recipe-view {
  padding-bottom: 50px; /* Space for pagination bullets */
}

/* Recipe Card Styles */
.recipe-card {
  background-color: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%;
}

.recipe-card-inner {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.recipe-header {
  display: flex;
  flex-direction: column;
}

.recipe-image-container {
  position: relative;
  height: 240px;
  overflow: hidden;
}

.recipe-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.recipe-card:hover .recipe-image {
  transform: scale(1.03);
}

.recipe-cost {
  position: absolute;
  top: 16px;
  left: 16px;
  background-color: #68A511; /* Green accent color */
  color: white;
  padding: 6px 12px;
  border-radius: 8px;
  font-weight: bold;
  font-size: 14px;
  box-shadow: 0 2px 8px rgba(104, 165, 17, 0.3);
}

.recipe-main-content {
  padding: 24px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.recipe-title {
  font-size: 24px;
  font-weight: 700;
  color: #333;
  margin-bottom: 12px;
}

.recipe-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}

.recipe-tag {
  background-color: #f0f9eb; /* Light green for tags */
  color: #68A511;
  font-size: 12px;
  font-weight: 600;
  padding: 4px 10px;
  border-radius: 20px;
}

.recipe-description {
  color: #555;
  line-height: 1.6;
  margin-bottom: 20px;
  flex-grow: 1;
}

.recipe-details {
  padding: 0 24px 16px;
  border-top: 1px solid #eee;
  margin-top: 8px;
  animation: fadeIn 0.4s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.recipe-section-title {
  font-weight: 600;
  color: #68A511;
  margin: 16px 0 10px;
  font-size: 18px;
}

.recipe-list {
  padding-left: 24px;
  margin-bottom: 20px;
  color: #444;
}

.recipe-list li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.recipe-steps {
  list-style-type: decimal;
}

.recipe-benefits {
  background-color: #f0f9eb;
  padding: 16px;
  border-radius: 12px;
  margin: 16px 0;
}

.recipe-benefits-text {
  color: #444;
  line-height: 1.5;
  font-size: 15px;
}

.recipe-actions {
  display: flex;
  gap: 12px;
  padding: 16px 24px 24px;
}

.recipe-details-btn, 
.recipe-add-btn {
  flex: 1;
  padding: 12px;
  border-radius: 8px;
  font-weight: 600;
  text-align: center;
  transition: all 0.2s ease;
  cursor: pointer;
}

.recipe-details-btn {
  border: 2px solid #68A511;
  color: #68A511;
  background-color: white;
}

.recipe-details-btn:hover {
  background-color: #f0f9eb;
}

.recipe-add-btn {
  background-color: #68A511;
  color: white;
  border: none;
  box-shadow: 0 2px 8px rgba(104, 165, 17, 0.2);
}

.recipe-add-btn:hover {
  background-color: #598615;
  box-shadow: 0 4px 12px rgba(104, 165, 17, 0.3);
}

/* Custom Navigation Buttons */
.recipe-nav-button {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #598615; /* Default darker green */
  z-index: 20;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.recipe-nav-prev {
  left: -10px;
}

.recipe-nav-next {
  right: -10px;
}

/* Arrow shapes */
.recipe-nav-button::after {
  content: '';
  width: 12px;
  height: 12px;
  border-style: solid;
  border-color: white;
  border-width: 0 3px 3px 0;
  display: inline-block;
  position: relative;
  transition: transform 0.2s ease;
}

.recipe-nav-prev::after {
  transform: rotate(135deg);
  left: 3px;
}

.recipe-nav-next::after {
  transform: rotate(-45deg);
  right: 3px;
}

/* Hover effects */
.recipe-nav-button:hover {
  background-color: #68A511; /* Lighter green on hover */
  box-shadow: 0 0 20px rgba(104, 165, 17, 0.4); /* Green glow effect */
  transform: translateY(-50%) scale(1.1);
}

.recipe-nav-prev:hover::after {
  transform: rotate(135deg) scale(1.2);
}

.recipe-nav-next:hover::after {
  transform: rotate(-45deg) scale(1.2);
}

/* Focus styles for keyboard navigation */
.recipe-nav-button:focus {
  outline: 2px solid #68A511;
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(104, 165, 17, 0.2);
}

/* Disabled state */
.recipe-nav-disabled {
  opacity: 0.35;
  cursor: not-allowed;
  background-color: #c9d4b8; /* Lighter green when disabled */
  box-shadow: none;
  pointer-events: none; /* Prevent interaction when disabled */
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .recipe-carousel {
    padding: 0 20px;
  }
  
  .recipe-carousel-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .recipe-counter {
    margin-top: 8px;
  }
  
  .recipe-header {
    flex-direction: column;
  }
  
  .recipe-image-container {
    height: 200px;
  }
  
  .recipe-nav-button {
    width: 40px;
    height: 40px;
  }
  
  .recipe-nav-prev {
    left: -5px;
  }
  
  .recipe-nav-next {
    right: -5px;
  }
  
  .recipe-nav-button::after {
    width: 10px;
    height: 10px;
  }
  
  .recipe-title {
    font-size: 20px;
  }
  
  .recipe-main-content,
  .recipe-details,
  .recipe-actions {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .recipe-nav-button {
    width: 36px;
    height: 36px;
    top: 120px; /* Position at the image area on mobile */
  }
  
  .recipe-image-container {
    height: 180px;
  }
  
  .recipe-main-content,
  .recipe-details,
  .recipe-actions {
    padding: 12px;
  }
  
  .recipe-title {
    font-size: 18px;
    margin-bottom: 8px;
  }
  
  .recipe-description {
    font-size: 14px;
  }
  
  .recipe-section-title {
    font-size: 16px;
  }
  
  .recipe-list {
    font-size: 14px;
  }
}

/* Override Swiper pagination for recipe carousel */
.recipe-carousel .swiper-pagination-bullet {
  background: #68A511 !important;
  opacity: 0.5 !important;
  width: 8px;
  height: 8px;
  margin: 0 4px;
}

.recipe-carousel .swiper-pagination-bullet-active {
  opacity: 1 !important;
  background: #68A511 !important;
  width: 10px;
  height: 10px;
}

/* Smooth scrolling effect */
.recipe-carousel .swiper-wrapper {
  transition-timing-function: ease-out !important;
} 