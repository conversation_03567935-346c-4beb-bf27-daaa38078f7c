/**
 * OTP (One-Time Password) Configuration
 * 
 * This file centralizes OTP settings for the application.
 * It provides constants and utilities for OTP generation and verification.
 */

// OTP expiry time in seconds (5 minutes)
export const OTP_EXPIRY_SECONDS = 300;

// OTP length
export const OTP_LENGTH = 6;

// Maximum OTP verification attempts before lockout
export const MAX_OTP_ATTEMPTS = 3;

// Cooldown period after max attempts (in seconds)
export const OTP_LOCKOUT_PERIOD = 300;

/**
 * Utility function to check if an OTP has expired
 * @param {Date} createdAt - When the OTP was created
 * @returns {boolean} - True if expired, false otherwise
 */
export const isOtpExpired = (createdAt) => {
  const now = new Date();
  const expiryTime = new Date(createdAt.getTime() + OTP_EXPIRY_SECONDS * 1000);
  return now > expiryTime;
};

/**
 * Utility function to generate a random OTP
 * @returns {string} - A random numeric OTP of length OTP_LENGTH
 */
export const generateOtp = () => {
  const digits = '0123456789';
  let otp = '';
  
  for (let i = 0; i < OTP_LENGTH; i++) {
    otp += digits[Math.floor(Math.random() * 10)];
  }
  
  return otp;
};
