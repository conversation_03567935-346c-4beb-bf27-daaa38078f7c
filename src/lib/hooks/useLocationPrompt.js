import { useState, useCallback } from 'react';
import { getCurrentLocation } from '../services/location';
import { logSearch } from '../services/searchLogService';
import { ERROR_MESSAGES } from '../services/location/config';

export function useLocationPrompt() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const requestLocation = useCallback(async () => {
    console.log('Starting location request...');
    try {
      setLoading(true);
      setError(null);
      
      const coords = await getCurrentLocation();
      console.log('Location retrieved successfully:', coords);
      
      // Log successful location request
      await logSearch('granted', coords);
      
      return coords;
    } catch (err) {
      console.error('Location request failed:', err);
      const errorMessage = err.code ? ERROR_MESSAGES[err.code] : ERROR_MESSAGES.DEFAULT;
      setError(errorMessage);
      
      // Log failed location request
      await logSearch('denied');
      
      return null;
    } finally {
      setLoading(false);
      console.log('Location request completed');
    }
  }, []);

  return {
    requestLocation,
    loading,
    error
  };
}