import { useState, useCallback } from 'react';
import { getDeepseekResponse, analyzeImage } from '../services/deepseek';
import { handleApiError } from '../utils/errorHandler';

export function useDeepseek(type = 'dental') {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [result, setResult] = useState(null);
  const [question, setQuestion] = useState('');

  const getResponse = useCallback(async (prompt) => {
    try {
      setLoading(true);
      setError(null);
      const response = await getDeepseekResponse(prompt, type);
      setResult(response);
      return response;
    } catch (err) {
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [type]);

  const analyzeImageWithAI = useCallback(async (imageFile, prompt = '') => {
    try {
      setLoading(true);
      setError(null);
      const analysis = await analyzeImage(imageFile, prompt);
      setResult(analysis);
      return analysis;
    } catch (err) {
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    loading,
    error,
    result,
    question,
    setQuestion,
    getResponse,
    analyzeImage: analyzeImageWithAI,
    clearError: () => setError(null)
  };
}