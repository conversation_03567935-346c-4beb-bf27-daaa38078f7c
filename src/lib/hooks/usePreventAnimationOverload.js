import { useEffect, useState } from 'react';

/**
 * Hook to respect user system preference for reduced motion only.
 */
export function usePreventAnimationOverload() {
  const [isReduced, setIsReduced] = useState(false);

  // Function to manually enable reduced animations (for accessibility settings only)
  const enableReducedAnimations = () => {
    document.body.classList.add('reduce-animations');
    setIsReduced(true);
  };

  // Function to restore animations
  const restoreAnimations = () => {
    document.body.classList.remove('reduce-animations');
    setIsReduced(false);
  };

  useEffect(() => {
    // Check if user prefers reduced motion
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    if (prefersReducedMotion) {
      enableReducedAnimations();
    } else {
      restoreAnimations();
    }
    // No timeout, no auto-disable
  }, []);

  return {
    isReduced,
    enableReducedAnimations,
    restoreAnimations
  };
}

/**
 * Utility function to apply animation with restart prevention
 * @param {Object} animation The animation object from framer-motion
 * @param {number} maxDuration Maximum duration in ms to prevent animation compounding
 */
export function safeAnimate(animation, maxDuration = 30000) {
  if (!animation) return;

  // Reset animation if it's been running too long
  if (animation._startTime && Date.now() - animation._startTime > maxDuration) {
    animation.stop();
  }
  
  // Set start time for tracking
  animation._startTime = Date.now();
  
  // Apply animation
  return animation.start();
} 