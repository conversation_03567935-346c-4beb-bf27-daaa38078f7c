import { useState, useEffect, useCallback, useContext } from 'react';
import { UserContext } from '../../contexts/UserContext';
import { supabase } from '../supabase';
import { v4 as uuidv4 } from 'uuid';
import { createMessages } from '../api/openai/config';
import { handleApiError } from '../utils/errorHandler';

export default function useChat() {
  const [question, setQuestion] = useState('');
  const [response, setResponse] = useState('');
  const [loading, setLoading] = useState(false);
  const [image, setImage] = useState(null);
  const [history, setHistory] = useState([]);
  const [error, setError] = useState(null);
  const [currentConversation, setCurrentConversation] = useState(null);
  const [conversationContext, setConversationContext] = useState([]);
  const [lastSubmittedQuestion, setLastSubmittedQuestion] = useState('');
  const { user } = useContext(UserContext);

  const clearError = () => setError(null);

  const fetchHistory = useCallback(async () => {
    if (!user) {
      setHistory([]);
      return;
    }

    try {
      const { data, error } = await supabase
        .from('conversations')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setHistory(data || []);
    } catch (err) {
      console.error('Error fetching chat history:', err);
      setError('Failed to load chat history');
    }
  }, [user]);

  const handleSubmit = async (e) => {
    if (e) e.preventDefault();
    if (!question.trim() && !image) return;
    
    setLoading(true);
    setError(null);
    setLastSubmittedQuestion(question);
    
    try {
      // Simplified for minimal version
      setResponse('This is a placeholder response.');
      setQuestion('');
      setImage(null);
    } catch (err) {
      setError('Failed to get response');
      console.error('Chat error:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user) {
      fetchHistory();
    }
  }, [user, fetchHistory]);

  return {
    question,
    setQuestion,
    response,
    loading,
    image,
    setImage,
    history,
    error,
    clearError,
    currentConversation,
    handleSubmit,
    fetchHistory,
    conversationContext,
    lastSubmittedQuestion
  };
}
