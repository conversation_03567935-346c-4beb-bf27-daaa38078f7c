import { useState, useEffect } from 'react';
import { supabase } from '../supabase';
import { useUser } from '../../contexts/UserContext';

export function useCourseTracking() {
  const { user } = useUser();
  const [courses, setCourses] = useState([]);
  const [semesters, setSemesters] = useState([]);
  const [gpaStats, setGpaStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!user) {
      setCourses([]);
      setSemesters([]);
      setGpaStats(null);
      setLoading(false);
      return;
    }

    loadUserData();
  }, [user]);

  const loadUserData = async () => {
    try {
      setLoading(true);
      
      // Load courses
      const { data: courseData, error: courseError } = await supabase
        .from('course_entries')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (courseError) throw courseError;

      // Load semesters
      const { data: semesterData, error: semesterError } = await supabase
        .from('semester_records')
        .select('*')
        .eq('user_id', user.id)
        .order('year', { ascending: false });

      if (semesterError) throw semesterError;

      // Load GPA stats
      const { data: gpaData, error: gpaError } = await supabase
        .from('gpa_calculations')
        .select('*')
        .eq('user_id', user.id)
        .order('calculated_at', { ascending: false })
        .limit(1)
        .single();

      if (gpaError && gpaError.code !== 'PGRST116') throw gpaError;

      setCourses(courseData || []);
      setSemesters(semesterData || []);
      setGpaStats(gpaData || null);
    } catch (err) {
      console.error('Error loading course data:', err);
      setError('Failed to load course data');
    } finally {
      setLoading(false);
    }
  };

  const addCourse = async (courseData) => {
    try {
      const { data, error } = await supabase
        .from('course_entries')
        .insert([{
          ...courseData,
          user_id: user.id
        }])
        .select()
        .single();

      if (error) throw error;

      setCourses(prev => [data, ...prev]);
      await updateGPACalculations();
      return data;
    } catch (err) {
      console.error('Error adding course:', err);
      throw new Error('Failed to add course');
    }
  };

  const updateCourse = async (courseId, updates) => {
    try {
      const { data, error } = await supabase
        .from('course_entries')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', courseId)
        .select()
        .single();

      if (error) throw error;

      setCourses(prev => prev.map(course => 
        course.id === courseId ? data : course
      ));
      await updateGPACalculations();
      return data;
    } catch (err) {
      console.error('Error updating course:', err);
      throw new Error('Failed to update course');
    }
  };

  const deleteCourse = async (courseId) => {
    try {
      const { error } = await supabase
        .from('course_entries')
        .delete()
        .eq('id', courseId);

      if (error) throw error;

      setCourses(prev => prev.filter(course => course.id !== courseId));
      await updateGPACalculations();
    } catch (err) {
      console.error('Error deleting course:', err);
      throw new Error('Failed to delete course');
    }
  };

  const updateGPACalculations = async () => {
    try {
      const scienceCourses = courses.filter(course => course.is_science_course);
      
      const totalCredits = courses.reduce((sum, course) => sum + course.credits, 0);
      const totalPoints = courses.reduce((sum, course) => 
        sum + (course.credits * course.grade_points), 0);
      
      const scienceCredits = scienceCourses.reduce((sum, course) => 
        sum + course.credits, 0);
      const sciencePoints = scienceCourses.reduce((sum, course) => 
        sum + (course.credits * course.grade_points), 0);

      const overallGPA = totalCredits > 0 ? totalPoints / totalCredits : 0;
      const scienceGPA = scienceCredits > 0 ? sciencePoints / scienceCredits : 0;

      const { data, error } = await supabase
        .from('gpa_calculations')
        .insert([{
          user_id: user.id,
          overall_gpa: overallGPA,
          science_gpa: scienceGPA,
          total_credits: totalCredits,
          science_credits: scienceCredits
        }])
        .select()
        .single();

      if (error) throw error;
      setGpaStats(data);
    } catch (err) {
      console.error('Error updating GPA calculations:', err);
      throw new Error('Failed to update GPA calculations');
    }
  };

  return {
    courses,
    semesters,
    gpaStats,
    loading,
    error,
    addCourse,
    updateCourse,
    deleteCourse,
    refresh: loadUserData
  };
}