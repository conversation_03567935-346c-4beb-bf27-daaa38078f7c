import { useState, useEffect, useCallback } from 'react';
import { fetchLatestArticles } from '../services/articleService';

export function useArticles() {
  const [articles, setArticles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);

  const loadArticles = useCallback(async () => {
    try {
      setLoading(true);
      const latestArticles = await fetchLatestArticles();
      setArticles(latestArticles);
      setLastUpdated(new Date());
    } catch (err) {
      setError('Failed to load articles');
      console.error('Article loading error:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // Initial load
  useEffect(() => {
    loadArticles();
  }, []);

  // Refresh every hour
  useEffect(() => {
    const interval = setInterval(loadArticles, 60 * 60 * 1000);
    return () => clearInterval(interval);
  }, [loadArticles]);

  return { 
    articles, 
    loading, 
    error,
    lastUpdated,
    refresh: loadArticles
  };
}