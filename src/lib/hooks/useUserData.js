import { useState, useEffect, useCallback } from 'react';
import { supabase } from '../supabase';
import { handleApiError } from '../utils/errorHandler';

export function useUserData(userId) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [profile, setProfile] = useState(null);
  const [progress, setProgress] = useState(null);

  const loadUserData = useCallback(async () => {
    if (!userId) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Load user profile
      const { data: profileData, error: profileError } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('user_id', userId)
        .maybeSingle();

      // Only throw non-PGRST116 errors
      if (profileError && profileError.code !== 'PGRST116') {
        throw profileError;
      }

      // Load predental progress
      const { data: progressData, error: progressError } = await supabase
        .from('predental_progress')
        .select('*')
        .eq('user_id', userId)
        .maybeSingle();

      // Only throw non-PGRST116 errors
      if (progressError && progressError.code !== 'PGRST116') {
        throw progressError;
      }

      // Initialize data for new users
      if (!profileData) {
        const { error: createProfileError } = await supabase
          .from('user_profiles')
          .insert([{ user_id: userId }])
          .select()
          .single();

        if (createProfileError) throw createProfileError;
      }

      if (!progressData) {
        const { error: createProgressError } = await supabase
          .from('predental_progress')
          .insert([{
            user_id: userId,
            overall_gpa: 0,
            science_gpa: 0,
            total_credits: 0,
            science_credits: 0
          }])
          .select()
          .single();

        if (createProgressError) throw createProgressError;
      }

      setProfile(profileData);
      setProgress(progressData);
    } catch (err) {
      console.error('Error loading user data:', err);
      setError(handleApiError(err));
    } finally {
      setLoading(false);
    }
  }, [userId]);

  // Load data on mount and userId change
  useEffect(() => {
    loadUserData();
  }, [userId, loadUserData]);

  return {
    profile,
    progress,
    loading,
    error,
    refresh: loadUserData
  };
}