import { useState, useCallback } from 'react';
import { analyzeImage } from '../services/imageAnalysisService';
import { handleApiError } from '../utils/errorHandler';

export function useImageAnalysis(userId) {
  const [analyzing, setAnalyzing] = useState(false);
  const [results, setResults] = useState(null);
  const [error, setError] = useState(null);

  const analyze = useCallback(async (imageUrl) => {
    try {
      setAnalyzing(true);
      setError(null);
      setResults(null);
      
      // Make API call to analyze the image
      const analysis = await analyzeImage(imageUrl, userId);
      setResults(analysis.results);
      return analysis;
    } catch (err) {
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      throw err;
    } finally {
      setAnalyzing(false);
    }
  }, [userId]);

  const clearResults = useCallback(() => {
    setResults(null);
    setError(null);
  }, []);

  return {
    analyze,
    analyzing,
    results,
    error,
    clearResults
  };
}