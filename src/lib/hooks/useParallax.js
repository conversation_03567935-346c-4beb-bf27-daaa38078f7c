import { useEffect, useRef } from 'react';

export function useParallax(enabled = true) {
  // Use refs to track the animation frame
  const requestRef = useRef(null);
  // Use a ref to track the scroll position
  const lastScrollY = useRef(0);
  // Track if we're currently processing a scroll
  const ticking = useRef(false);
  // Track last processed time to limit processing frequency
  const lastProcessedTime = useRef(0);
  // Cache element references
  const elementsRef = useRef([]);
  // Track if hook is mounted to prevent memory leaks
  const isMountedRef = useRef(true);
  // Keep track of browser for optimizations
  const browserRef = useRef({
    isSafari: false,
    isFirefox: false,
    isOlderBrowser: false
  });

  useEffect(() => {
    // If disabled, don't do anything
    if (!enabled) return;
    
    // Set mounted flag
    isMountedRef.current = true;
    
    // Detect browser for specific optimizations
    const userAgent = navigator.userAgent.toLowerCase();
    browserRef.current = {
      isSafari: /safari/.test(userAgent) && !/chrome/.test(userAgent),
      isFirefox: /firefox/.test(userAgent),
      // Older browsers may struggle with certain effects
      isOlderBrowser: !('IntersectionObserver' in window) || !('requestAnimationFrame' in window)
    };
    
    // Skip on low-end devices or when reduced motion is preferred
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    if (prefersReducedMotion || browserRef.current.isOlderBrowser) {
      console.log("Parallax disabled: reduced motion preferred or older browser detected");
      return;
    }
    
    // Performance check - count elements to see if it's worth processing
    const parallaxElements = document.querySelectorAll('[data-parallax]:not([data-parallax="none"])');
    if (parallaxElements.length === 0) return;
    
    // Cache element references for better performance - with element visibility checking
    const elementsWithIO = [];
    let hasIntersectionObserver = 'IntersectionObserver' in window;
    
    // Process all elements and prepare for visibility detection
    elementsRef.current = Array.from(parallaxElements).map(el => {
      // Parse the speed with safe fallbacks
      const speedAttribute = el.dataset.parallax;
      let speed;
      
      if (speedAttribute === 'slow') speed = 0.15;
      else if (speedAttribute === 'medium') speed = 0.3;
      else if (speedAttribute === 'fast') speed = 0.45;
      else speed = 0.2; // Default speed
      
      // Reduce speed for Safari to prevent visual glitches
      if (browserRef.current.isSafari) {
        speed *= 0.7;
      }
      
      // For Firefox, add will-change to optimize
      if (browserRef.current.isFirefox) {
        el.style.willChange = 'transform';
      }
      
      // Return the element with its speed data
      return {
        element: el,
        speed,
        visible: true // Assume visible by default
      };
    });
    
    // Set up Intersection Observer for performance if available
    if (hasIntersectionObserver) {
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          // Find the element in our cached array and update visibility
          const found = elementsRef.current.find(item => item.element === entry.target);
          if (found) {
            found.visible = entry.isIntersecting;
          }
        });
      }, {
        // Extend the root margin to start animating before elements enter viewport
        rootMargin: '100px 0px',
        threshold: 0.01
      });
      
      // Observe all parallax elements
      elementsRef.current.forEach(item => {
        observer.observe(item.element);
      });
    }
    
    // Create a more efficient scroll handler with throttling
    const handleScroll = () => {
      // Store current scroll position
      lastScrollY.current = window.scrollY;
      
      // Throttle processing to max once per 16ms (approx 60fps)
      const now = performance.now();
      if (now - lastProcessedTime.current < 16) return;
      
      // Only update if we're not already processing a frame
      if (!ticking.current && isMountedRef.current) {
        // Use requestAnimationFrame for better performance
        requestRef.current = window.requestAnimationFrame(() => {
          const scrolled = lastScrollY.current;
          
          // Process only visible elements (if using IntersectionObserver)
          const visibleElements = elementsRef.current.filter(item => item.visible);
          
          // Optimize by batching read/write operations
          // First gather all position calculations
          const updates = visibleElements.map(({ element, speed }) => {
            // Calculate translation
            const yPos = -(scrolled * speed);
            return { element, yPos };
          });
          
          // Then batch all DOM updates together
          updates.forEach(({ element, yPos }) => {
            // Use transform3d for GPU acceleration
            try {
              element.style.transform = `translate3d(0, ${yPos}px, 0)`;
            } catch (e) {
              // Fallback for older browsers
              element.style.transform = `translateY(${yPos}px)`;
            }
          });
          
          // Reset ticking flag
          ticking.current = false;
          lastProcessedTime.current = performance.now();
        });
        
        // Set ticking flag to prevent multiple rAF calls
        ticking.current = true;
      }
    };
    
    // Create a more efficient resize handler
    const resizeObserver = new ResizeObserver(throttle(() => {
      // Force recalculation of element positions
      if (elementsRef.current.length > 0) {
        handleScroll();
      }
    }, 100));
    
    // Observe the document body for size changes
    resizeObserver.observe(document.body);

    // Set up the event listener with passive option for better performance
    window.addEventListener('scroll', handleScroll, { passive: true });
    
    // Run once on mount to initialize positions
    setTimeout(handleScroll, 10);

    // Clean up
    return () => {
      // Prevent memory leaks on unmount
      isMountedRef.current = false;
      
      window.removeEventListener('scroll', handleScroll);
      resizeObserver.disconnect();
      
      // Clear event handlers
      if (hasIntersectionObserver) {
        const observer = new IntersectionObserver(() => {}, {});
        elementsRef.current.forEach(item => {
          observer.unobserve(item.element);
        });
        observer.disconnect();
      }
      
      // Cancel any pending animation frame
      if (requestRef.current) {
        window.cancelAnimationFrame(requestRef.current);
        requestRef.current = null;
      }
      
      // Reset transforms on all elements to prevent visual bugs
      elementsRef.current.forEach(({ element }) => {
        element.style.transform = '';
        if (browserRef.current.isFirefox) {
          element.style.willChange = 'auto';
        }
      });
      
      // Clear cached elements
      elementsRef.current = [];
    };
  }, [enabled]);
}

// Throttle function to limit execution frequency
function throttle(func, limit) {
  let lastCall = 0;
  return function(...args) {
    const now = Date.now();
    if (now - lastCall >= limit) {
      lastCall = now;
      return func.apply(this, args);
    }
  };
}