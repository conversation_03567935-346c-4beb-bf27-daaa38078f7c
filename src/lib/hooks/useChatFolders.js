import { useState, useCallback, useEffect } from 'react';
import { supabase } from '../supabase';
import { useUser } from '../../contexts/UserContext';
import { handleApiError } from '../utils/errorHandler';

export function useChatFolders() {
  const { user } = useUser();
  const [folders, setFolders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const loadFolders = useCallback(async () => {
    if (!user) return;

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('chat_folders')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: true });

      if (error) throw error;
      setFolders(data || []);
    } catch (err) {
      console.error('Error loading folders:', err);
      setError(handleApiError(err));
    } finally {
      setLoading(false);
    }
  }, [user]);

  useEffect(() => {
    loadFolders();
  }, [loadFolders]);

  const createFolder = useCallback(async (name) => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('chat_folders')
        .insert([{
          user_id: user.id,
          name,
          created_at: new Date().toISOString()
        }])
        .select()
        .single();

      if (error) throw error;
      setFolders(prev => [...prev, data]);
      return data;
    } catch (err) {
      console.error('Error creating folder:', err);
      throw new Error(handleApiError(err));
    }
  }, [user]);

  const updateFolder = useCallback(async (folderId, name) => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('chat_folders')
        .update({ name, updated_at: new Date().toISOString() })
        .eq('id', folderId)
        .eq('user_id', user.id)
        .select()
        .single();

      if (error) throw error;
      setFolders(prev => prev.map(folder => 
        folder.id === folderId ? data : folder
      ));
      return data;
    } catch (err) {
      console.error('Error updating folder:', err);
      throw new Error(handleApiError(err));
    }
  }, [user]);

  const deleteFolder = useCallback(async (folderId) => {
    if (!user) return;

    try {
      const { error } = await supabase
        .from('chat_folders')
        .delete()
        .eq('id', folderId)
        .eq('user_id', user.id);

      if (error) throw error;
      setFolders(prev => prev.filter(folder => folder.id !== folderId));
    } catch (err) {
      console.error('Error deleting folder:', err);
      throw new Error(handleApiError(err));
    }
  }, [user]);

  return {
    folders,
    loading,
    error,
    createFolder,
    updateFolder,
    deleteFolder,
    refresh: loadFolders,
    clearError: () => setError(null)
  };
}