import { useState, useEffect } from 'react';
import { supabase } from '../supabase';

export function useExperience() {
  const [experience, setExperience] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function loadExperience() {
      try {
        const { data, error } = await supabase
          .from('experience_hours')
          .select('*')
          .order('date', { ascending: true });

        if (error) throw error;
        setExperience(data || []);
      } catch (err) {
        console.error('Error loading experience:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }

    loadExperience();
  }, []);

  return { experience, loading, error };
}