import { useState, useEffect } from 'react';
import { supabase } from '../supabase';

export function useDATScores() {
  const [scores, setScores] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function loadScores() {
      try {
        const { data, error } = await supabase
          .from('dat_scores')
          .select('*')
          .order('test_date', { ascending: true });

        if (error) throw error;
        setScores(data || []);
      } catch (err) {
        console.error('Error loading DAT scores:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }

    loadScores();
  }, []);

  return { scores, loading, error };
}