import { useEffect, useCallback } from 'react';
import { setupRealtimeSubscriptions, initOfflineSupport, monitorPerformance } from '../services/supabaseService';
import { useUser } from '../../contexts/UserContext';

export function useSupabaseOptimization() {
  const { user } = useUser();
  const { addPendingOperation } = initOfflineSupport();

  // Set up real-time subscriptions
  useEffect(() => {
    if (!user?.id) return;

    const cleanup = setupRealtimeSubscriptions(user.id, {
      onProfileUpdate: (profile) => {
        // Handle profile updates
      },
      onChatUpdate: (message) => {
        // Handle new chat messages
      },
      onProgressUpdate: (progress) => {
        // Handle progress updates
      }
    });

    return cleanup;
  }, [user?.id]);

  // Monitor performance
  const logPerformance = useCallback((name, value, metadata = {}) => {
    monitorPerformance({
      name,
      value,
      metadata: {
        ...metadata,
        userId: user?.id,
        timestamp: new Date().toISOString()
      }
    });
  }, [user?.id]);

  // Handle offline operations
  const handleOfflineOperation = useCallback((operation) => {
    if (!navigator.onLine) {
      addPendingOperation(operation);
      return true;
    }
    return false;
  }, [addPendingOperation]);

  return {
    logPerformance,
    handleOfflineOperation
  };
}