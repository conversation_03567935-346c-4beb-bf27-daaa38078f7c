import { useState, useEffect, useCallback, useMemo } from "react";
import { supabase } from "../supabase";
import { useUser } from "../../contexts/UserContext";
import { handleApiError } from '../utils/errorHandler';
import { validateEmail, validatePassword, validateUsername } from '../utils/validation';
import { shouldBypassAuth, createAdminBypassResponse } from "../utils/adminBypass";

export function useAuth() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [session, setSession] = useState(null);
  const [rememberMe, setRememberMe] = useState(false);

  useEffect(() => {
    // Check session on mount
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      // Restore remember me preference
      const remembered = localStorage.getItem('rememberMe') === 'true';
      setRememberMe(remembered);
    });

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
    });

    return () => subscription.unsubscribe();
  }, []);

  const register = useCallback(async ({ email, password, username, firstName, lastName }) => {
    try {
      setLoading(true);
      setError(null);
      
      // Validate inputs
      if (!validateEmail(email)) {
        throw new Error('Invalid email format');
      }
      if (!validatePassword(password)) {
        throw new Error('Password must be at least 8 characters with an uppercase letter, number, and special character');
      }
      if (!validateUsername(username)) {
        throw new Error('Username must be 3-20 characters and can only contain letters, numbers, underscores, and hyphens');
      }

      // Check if username exists
      const { data: existingUser } = await supabase
        .from('user_profiles')
        .select('username')
        .eq('username', username)
        .maybeSingle();

      if (existingUser) {
        throw new Error('Username already taken');
      }

      // Register user
      const { data: authData, error: signUpError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            username,
            first_name: firstName,
            last_name: lastName
          }
        }
      });

      if (signUpError) throw signUpError;

      // Create user profile
      const { error: profileError } = await supabase
        .from('user_profiles')
        .insert([{
          user_id: authData.user.id,
          username,
          first_name: firstName,
          last_name: lastName,
          role: 'user'
        }]);

      if (profileError && profileError.code !== 'PGRST116') {
        throw profileError;
      }

      return authData;
    } catch (err) {
      console.error('Registration error:', err);
      // Handle specific error cases
      if (err.message.includes('duplicate key')) {
        if (err.message.includes('username')) {
          throw new Error('Username already taken');
        }
        if (err.message.includes('email')) {
          throw new Error('Email already registered');
        }
      }
      throw new Error(handleApiError(err));
    } finally {
      setLoading(false);
    }
  }, []);

  const signIn = useCallback(async ({ email, password, remember = false }) => {
    try {
      setLoading(true);
      setError(null);
      
      if (!validateEmail(email)) {
        throw new Error('Please enter a valid email address');
      }

      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
        options: {
          captchaToken: null, // Disable captcha in webcontainer
          data: { lastLoginAt: new Date().toISOString() }
        }
      });

      if (error) throw error;
      
      // Handle remember me
      if (remember) {
        localStorage.setItem('rememberMe', 'true');
      } else {
        localStorage.removeItem('rememberMe');
      }
      
      return data;
    } catch (err) {
      console.error('Sign in error:', err);
      throw new Error(handleApiError(err));
    } finally {
      setLoading(false);
    }
  }, [rememberMe]);

  const signUp = useCallback(async ({ email, password, username, firstName, lastName }) => {
    try {
      setLoading(true);
      setError(null);
      
      // Validate all inputs
      if (!validateEmail(email)) {
        throw new Error('Please enter a valid email address');
      }
      if (!validatePassword(password)) {
        throw new Error('Password must be at least 8 characters with an uppercase letter, number, and special character');
      }
      if (!validateUsername(username)) {
        throw new Error('Username must be 3-20 characters and can only contain letters, numbers, underscores, and hyphens');
      }

      const { data, error: signUpError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            username,
            first_name: firstName,
            last_name: lastName,
            created_at: new Date().toISOString(),
            role: 'user'
          }
        }
      });

      if (signUpError) throw signUpError;
      
      // Create user profile
      const { error: profileError } = await supabase
        .from('user_profiles')
        .insert([{
          user_id: data.user.id,
          username,
          first_name: firstName,
          last_name: lastName,
          role: 'user'
        }]);

      if (profileError && profileError.code !== 'PGRST116') {
        throw profileError;
      }

      return data;
    } catch (err) {
      console.error('Sign up error:', err);
      throw new Error(handleApiError(err));
    } finally {
      setLoading(false);
    }
  }, []);

  const signOut = useCallback(async () => {
    try {
      setLoading(true);
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      localStorage.removeItem('rememberMe');
    } catch (err) {
      console.error('Sign out error:', err);
      throw new Error(handleApiError(err));
    } finally {
      setLoading(false);
    }
  }, []);

  const resetPassword = useCallback(async (email) => {
    try {
      setLoading(true);
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`
      });
      if (error) throw error;
    } catch (err) {
      console.error('Password reset error:', err);
      throw new Error(handleApiError(err));
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    register,
    session,
    signIn,
    signOut,
    resetPassword,
    loading,
    error,
    rememberMe,
    setRememberMe,
    signUp,
    clearError: () => setError(null)
  };
}