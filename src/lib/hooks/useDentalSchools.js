import { useState, useCallback, useEffect } from 'react';
import { getNearbyDentalSchools } from '../services/dentalSchoolService';

export function useDentalSchools() {
  const [schools, setSchools] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [radius, setRadius] = useState(25);
  const [currentCoords, setCurrentCoords] = useState(null);

  const searchSchools = useCallback(async (coords) => {
    if (!coords) return;
    
    console.group('School Search Request');
    console.log('Searching with coordinates:', coords);
    console.log('Current radius:', radius);
    
    setLoading(true);
    setError(null);
    
    try {
      setCurrentCoords(coords);
      const results = await getNearbyDentalSchools(coords, radius);
      console.log('Search results:', {
        total: results.length,
        closest: results[0]?.distance,
        furthest: results[results.length - 1]?.distance
      });
      setSchools(results);
    } catch (err) {
      console.error('School search failed:', err);
      setError('Failed to find dental schools. Please try again.');
      setSchools([]);
    } finally {
      setLoading(false);
      console.groupEnd();
    }
  }, [radius]);

  // Re-search when radius changes
  useEffect(() => {
    if (currentCoords) {
      console.log('Radius changed, re-searching with:', { radius, coords: currentCoords });
      searchSchools(currentCoords);
    }
  }, [radius, currentCoords, searchSchools]);

  return {
    schools,
    loading,
    error,
    radius,
    setRadius,
    searchSchools
  };
}