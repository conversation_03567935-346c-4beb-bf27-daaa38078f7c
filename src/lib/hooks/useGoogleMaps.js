import { useState, useEffect } from 'react';
import { loadGoogleMapsScript } from '../services/googleMapsService';

// Function to create a map instance
function createMap(element, options) {
  return new Promise((resolve, reject) => {
    try {
      console.log("🗺️ Creating Google Map instance...");
      const map = new window.google.maps.Map(element, options);
      resolve(map);
    } catch (error) {
      console.error("❌ Error creating Google Map:", error);
      reject(error);
    }
  });
}

// Function to handle API errors
function handleApiError(error) {
  console.error('Google Maps API Error:', error);
  return 'An error occurred while loading the map. Please try again later.';
}

export function useGoogleMaps(elementRef, options = {}) {
  const [map, setMap] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [markers, setMarkers] = useState([]);
  const [retryCount, setRetryCount] = useState(0);
  const MAX_RETRIES = 5;

  useEffect(() => {
    let mounted = true;

    const createMap = async (container, mapOptions) => {
      const google = await loadGoogleMapsScript();
      return new google.maps.Map(container, {
        center: { lat: 39.8283, lng: -98.5795 }, // USA center
        zoom: 4,
        mapTypeControl: false,
        fullscreenControl: false,
        streetViewControl: false,
        ...mapOptions
      });
    };

    const initMap = async () => {
      console.log("🗺️ Initializing Google Map...");
      console.log("🔍 Checking elementRef:", elementRef?.current);
      console.log("🔍 Map Options:", options);

      if (!elementRef.current) {
        console.error("❌ Map container (elementRef) is NULL! Check if the <div> exists in your JSX.");
        if (retryCount < MAX_RETRIES) {
          console.log(`🔄 Retrying... Attempt ${retryCount + 1} of ${MAX_RETRIES}`);
          setRetryCount(prev => prev + 1);
          setTimeout(initMap, 1000);
          return;
        }
        setError(new Error("Failed to find map container after multiple attempts"));
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // Load the Google Maps script
        await loadGoogleMapsScript();
        console.log("✅ Google Maps script loaded!");

        // Create the map instance
        const mapInstance = await createMap(elementRef.current, options);
        console.log("✅ Google Map initialized successfully!", mapInstance);

        if (mounted) {
          setMap(mapInstance);
          setLoading(false);
          setRetryCount(0);
        }
      } catch (err) {
        console.error("❌ Map initialization error:", err);
        if (mounted) {
          setError(err);
          setLoading(false);
        }
      }
    };

    initMap();

    return () => {
      mounted = false;
      // Cleanup markers
      markers.forEach(marker => marker.setMap(null));
    };
  }, [elementRef, options, retryCount]);

  // Helper functions
  const addMarker = (position, markerOptions = {}) => {
    if (!map) return null;
    const marker = new google.maps.Marker({
      position,
      map,
      ...markerOptions
    });
    setMarkers(prev => [...prev, marker]);
    return marker;
  };

  const clearMarkers = () => {
    markers.forEach(marker => marker.setMap(null));
    setMarkers([]);
  };

  const fitBounds = (points) => {
    if (!map || !points.length) return;
    const bounds = new google.maps.LatLngBounds();
    points.forEach(point => bounds.extend(point));
    map.fitBounds(bounds);
  };

  return {
    map,
    loading,
    error,
    markers,
    addMarker,
    clearMarkers,
    fitBounds
  };
}
