import { useState, useCallback } from 'react';
import { uploadImage, deleteImage } from '../services/imageService';
import { validateImage } from '../utils/validation';
import { handleApiError } from '../utils/errorHandler';

export function useImageUpload(userId) {
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState(null);
  const [progress, setProgress] = useState(0);

  const handleUpload = useCallback(async (file) => {
    try {
      setUploading(true);
      setError(null);
      setProgress(0);

      // Validate file
      validateImage(file);

      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setProgress(prev => Math.min(prev + 10, 90));
      }, 100);

      // Upload image
      const imageUrl = await uploadImage(file, userId);

      clearInterval(progressInterval);
      setProgress(100);

      return imageUrl;
    } catch (err) {
      setError(handleApiError(err));
      throw err;
    } finally {
      setUploading(false);
    }
  }, [userId]);

  const handleDelete = useCallback(async (imageUrl) => {
    try {
      setError(null);
      await deleteImage(imageUrl, userId);
    } catch (err) {
      setError(handleApiError(err));
      throw err;
    }
  }, [userId]);

  return {
    handleUpload,
    handleDelete,
    uploading,
    error,
    progress,
    clearError: () => setError(null)
  };
}