import { useState, useEffect } from 'react';
import { supabase } from '../supabase';
import { handleApiError } from '../utils/errorHandler';
import { isOnline } from '../utils/network';

export function useSystemSettings() {
  const [settings, setSettings] = useState({
    maintenanceMode: false,
    loading: true,
    error: null,
    offline: !isOnline()
  });

  useEffect(() => {
    let mounted = true;

    const fetchSettings = async () => {
      try {
        const { data, error } = await supabase
          .from('system_settings')
          .select('*')
          .maybeSingle();

        // Handle case where no settings exist yet
        if (error?.code === 'PGRST116') {
          if (mounted) {
            setSettings(prev => ({
              ...prev,
              maintenanceMode: false,
              loading: false,
              error: null
            }));
          }
          return;
        }

        if (error) {
          throw error;
        }

        const maintenanceMode = data?.maintenance_mode ?? false;

        if (mounted) {
          setSettings(prev => ({
            ...prev,
            maintenanceMode,
            loading: false,
            error: null
          }));
        }
      } catch (err) {
        console.error('Error fetching system settings:', err);
        if (mounted) {
          setSettings(prev => ({
            ...prev,
            loading: false,
            error: isOnline() 
              ? 'Unable to load system settings'
              : 'You appear to be offline'
          }));
        }
      }
    };

    // Initial fetch
    fetchSettings();

    // Subscribe to changes
    const subscription = supabase
      .channel('system_settings_changes')
      .on('postgres_changes', 
        { 
          event: '*', 
          schema: 'public', 
          table: 'system_settings' 
        }, 
        (payload) => {
          if (mounted && payload?.new) {
            setSettings(prev => ({
              ...prev,
              maintenanceMode: payload.new.maintenance_mode
            }));
          }
        }
      )
      .subscribe();

    return () => {
      mounted = false;
      subscription.unsubscribe();
    };
  }, []);

  return settings;
}