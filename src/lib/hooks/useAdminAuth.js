import { useState, useEffect } from 'react';
import { supabase } from '../supabase';
import { useNavigate } from 'react-router-dom';

// Isolated admin authentication hook - completely separate from the main website
export function useAdminAuth() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [supabaseSession, setSupabaseSession] = useState(null);
  const navigate = useNavigate();

  // Specific credentials for admin access
  const ADMIN_USERNAME = 'MikeyMouse';
  const ADMIN_PASSWORD = 'MillionaireMike1$';
  
  // Admin session storage key - namespaced for isolation
  const ADMIN_SESSION_KEY = 'smilo_admin_session';

  // Check for existing Supabase session on mount
  useEffect(() => {
    const checkSupabaseSession = async () => {
      try {
        const { data, error } = await supabase.auth.getSession();
        if (!error && data.session) {
          setSupabaseSession(data.session);
        }
      } catch (err) {
        console.warn('Error checking Supabase session:', err);
      }
    };
    
    checkSupabaseSession();
    
    // Set up auth state change listener
    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setSupabaseSession(session);
      }
    );
    
    return () => {
      if (authListener && authListener.subscription) {
        authListener.subscription.unsubscribe();
      }
    };
  }, []);

  const authenticate = async (username, password) => {
    setLoading(true);
    setError(null);
    
    // STRICT CREDENTIAL CHECK - only allow the exact admin credentials
    if (username !== ADMIN_USERNAME || password !== ADMIN_PASSWORD) {
      console.log('Admin authentication failed - invalid credentials');
      setError('Invalid username or password');
      setLoading(false);
      return false;
    }
    
    console.log('Admin authentication successful - correct credentials');
    
    try {
      // Clear the logged out flag if it exists
      localStorage.removeItem('admin_logged_out');
      
      // Create a session token for admin access
      const sessionId = Date.now().toString();
      
      // Store admin session in localStorage with expiration
      const adminSession = {
        username: ADMIN_USERNAME,
        sessionId,
        role: 'admin',
        exp: Date.now() + (30 * 24 * 60 * 60 * 1000) // 30 day expiration
      };
      
      localStorage.setItem(ADMIN_SESSION_KEY, JSON.stringify(adminSession));
      
      // Also set the main admin session used in useAdmin
      localStorage.setItem('admin_session', JSON.stringify({
        username: ADMIN_USERNAME,
        role: 'admin',
        exp: Date.now() + (30 * 24 * 60 * 60 * 1000) // 30 days
      }));
      
      // Update window.smiloAdmin object if it exists
      if (window.smiloAdmin) {
        window.smiloAdmin.isLoggedIn = true;
      }
      
      // Also try to authenticate with Supabase if credentials are available
      try {
        // This is a special admin-only signin that won't affect normal user auth
        const { data, error } = await supabase.auth.signInWithPassword({
          email: `${ADMIN_USERNAME.toLowerCase()}@admin.smilo.dental`,
          password: ADMIN_PASSWORD
        });
        
        if (error) {
          console.warn('Supabase admin login failed, but continuing with local auth:', error);
        } else {
          console.log('Supabase admin login successful');
          setSupabaseSession(data.session);
        }
      } catch (supabaseErr) {
        console.warn('Error with Supabase admin auth, but continuing with local auth:', supabaseErr);
      }
      
      // Record login activity in Supabase for audit logging
      try {
        await supabase
          .from('admin_activity_log')
          .insert({
            action: 'login',
            username: ADMIN_USERNAME,
            ip_address: await getClientIP(),
            timestamp: new Date().toISOString()
          });
      } catch (logErr) {
        console.warn('Failed to log admin login:', logErr);
      }
      
      setLoading(false);
      return true;
    } catch (err) {
      console.error('Error creating admin session:', err);
      setError('Error creating admin session');
      setLoading(false);
      return false;
    }
  };

  // Get client IP for logging
  const getClientIP = async () => {
    try {
      // Use a protocol-relative URL to avoid HTTPS redirection issues
      const res = await fetch('//api.ipify.org?format=json');
      const data = await res.json();
      return data.ip;
    } catch (e) {
      console.warn('Could not get client IP:', e);
      return 'unknown';
    }
  };

  const checkAdminSession = () => {
    // Check if user has explicitly logged out
    if (localStorage.getItem('admin_logged_out') === 'true') {
      console.log('Admin session check: User has explicitly logged out');
      return false;
    }
    
    // Check for existing admin session
    try {
      const sessionData = localStorage.getItem(ADMIN_SESSION_KEY);
      if (!sessionData) {
        console.log('Admin session check: No session data found');
        return false;
      }
      
      const session = JSON.parse(sessionData);
      
      // Validate session expiration
      if (!session.exp || session.exp < Date.now()) {
        // Clear expired session
        console.log('Admin session check: Session expired');
        localStorage.removeItem(ADMIN_SESSION_KEY);
        return false;
      }
      
      // Valid session exists
      console.log('Admin session check: Valid session found');
      return true;
    } catch (err) {
      console.error('Error checking admin session:', err);
      return false;
    }
  };

  const adminLogout = async () => {
    console.log('Admin logout initiated');
    
    try {
      // Set the logged out flag first
      localStorage.setItem('admin_logged_out', 'true');
      
      // Clear specific admin tokens
      localStorage.removeItem(ADMIN_SESSION_KEY);
      localStorage.removeItem('admin_session');
      localStorage.removeItem('supabase.auth.token');
      
      // Systematically clear all admin-related tokens to ensure complete logout
      const keysToRemove = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && (
          key.includes('admin') || 
          key.includes('session') || 
          key.includes('supabase') || 
          key.includes('token') ||
          key.includes('smilo')
        )) {
          if (key !== 'admin_logged_out') { // Keep the logout flag
            keysToRemove.push(key);
          }
        }
      }
      
      // Remove identified keys
      keysToRemove.forEach(key => {
        localStorage.removeItem(key);
        console.log(`Removed: ${key}`);
      });
      
      // Sign out from Supabase as well
      if (supabaseSession) {
        try {
          await supabase.auth.signOut();
          setSupabaseSession(null);
        } catch (supabaseErr) {
          console.warn('Error signing out from Supabase:', supabaseErr);
        }
      }
      
      // Record logout activity in Supabase for audit logging
      try {
        await supabase
          .from('admin_activity_log')
          .insert({
            action: 'logout',
            username: ADMIN_USERNAME,
            ip_address: await getClientIP(),
            timestamp: new Date().toISOString()
          });
      } catch (logErr) {
        console.warn('Failed to log admin logout:', logErr);
      }
      
      // Reset any admin state in window object
      if (window && window.smiloAdmin) {
        window.smiloAdmin.isLoggedIn = false;
        console.log('Reset window.smiloAdmin.isLoggedIn to false');
      }
      
      // Also clear sessionStorage
      sessionStorage.clear();
      console.log('Session storage cleared');
      
      // Primary redirect using window.location with cache busting
      const timestamp = new Date().getTime();
      console.log('Redirecting to home page...');
      window.location.href = `/?logout=${timestamp}`;
    } catch (e) {
      console.warn('Error during admin logout:', e);
      
      // Fallback - force navigation to home page
      window.location.href = '/';
    }
  };

  // Check if the user has active admin permissions with Supabase
  const checkAdminPermissions = async () => {
    try {
      if (!supabaseSession) return false;
      
      const { data, error } = await supabase
        .from('admin_users')
        .select('role')
        .eq('username', ADMIN_USERNAME)
        .single();
      
      if (error || !data) return false;
      
      return data.role === 'admin';
    } catch (err) {
      console.warn('Error checking admin permissions:', err);
      return false;
    }
  };

  return {
    authenticate,
    checkAdminSession,
    adminLogout,
    logout: adminLogout, // Alias for backward compatibility
    checkAdminPermissions,
    loading,
    error,
    clearError: () => setError(null),
    supabaseSession
  };
} 