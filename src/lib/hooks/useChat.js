import { useState, useCallback, useEffect } from 'react';
import { getDentalAdvice } from '../openai';
import {
  loadChatHistory,
  saveChatMessage,
  startConversation,
  endConversation
} from '../services/chatService';
import { validateQuestion, validateA<PERSON><PERSON>ey } from '../utils/validation';
import { handleApiError } from '../utils/errorHandler';
import { config } from '../config';

// Maximum retries for failed API calls
const MAX_RETRIES = 2;
// Maximum number of exchanges to keep in context
const MAX_CONTEXT_EXCHANGES = 3;

export function useChat(user) {
  const [question, setQuestion] = useState('');
  const [response, setResponse] = useState('');
  const [conversationContext, setConversationContext] = useState([]);
  const [loading, setLoading] = useState(false);
  const [image, setImage] = useState(null);
  const [history, setHistory] = useState([]);
  const [error, setError] = useState(null);
  const [currentConversation, setCurrentConversation] = useState(null);
  const [retryCount, setRetryCount] = useState(0);
  const [lastSubmittedQuestion, setLastSubmittedQuestion] = useState('');

  // Clear error message
  const clearError = useCallback(() => setError(null), []);

  // Update conversation context with new exchanges
  const updateContext = useCallback((question, answer) => {
    setConversationContext(prev => {
      // Add new exchange
      const updatedContext = [
        ...prev,
        { role: 'user', content: question },
        { role: 'assistant', content: answer }
      ];

      // Keep only the last MAX_CONTEXT_EXCHANGES exchanges
      return updatedContext.slice(-MAX_CONTEXT_EXCHANGES * 2);
    });
  }, []);

  // Start a new conversation session
  const startNewConversation = useCallback(async () => {
    if (!user) return;
    try {
      const conversation = await startConversation(user.id);
      setCurrentConversation(conversation);
      // Clear context when starting a new conversation
      setConversationContext([]);
      return conversation;
    } catch (err) {
      console.error('Error starting conversation:', err);
      setError(handleApiError(err));
      return null;
    }
  }, [user]);

  // Fetch chat history from the server
  const fetchHistory = useCallback(async () => {
    if (!user) return;

    try {
      const data = await loadChatHistory(user.id);
      setHistory(data);
    } catch (err) {
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      console.error('History error:', err);
    }
  }, [user]);

  // Handle form submission
  const handleSubmit = async (e) => {
    // Only prevent default if e is an event object with preventDefault method
    if (e && typeof e.preventDefault === 'function') {
      e.preventDefault();
    }
    console.log("handleSubmit called with question:", question);
    clearError();
    setResponse('');

    // Don't proceed if already loading
    if (loading) {
      console.log("Already loading, not proceeding");
      return;
    }

    // Don't proceed if question is empty
    if (!question.trim() && !image) {
      console.log("Question is empty and no image, not proceeding");
      setError('Please enter a question or upload an image.');
      return;
    }

    console.log("Starting to process question:", question);

    // Store the current question and image for processing
    const currentQuestion = question;
    const currentImage = image;

    // Save the last submitted question for display
    setLastSubmittedQuestion(currentQuestion);

    // Clear the question input immediately after submission
    setQuestion('');

    // Start new conversation if needed and user is logged in
    if (user && !currentConversation) {
      console.log("No current conversation, starting a new one");
      const newConversation = await startNewConversation();
      if (!newConversation) {
        // Failed to start conversation
        console.log("Failed to start conversation");
        // We continue anyway since we don't need a conversation to call OpenAI
      }
    } else if (!user) {
      console.log("No user logged in, skipping conversation creation");
    }

    // Validate API key first
    try {
      console.log("Validating API key");
      validateApiKey(config.openai.apiKey);
    } catch (err) {
      console.error("API key validation failed:", err);
      setError(err.message);
      return;
    }

    try {
      console.log("Setting loading state to true");
      setLoading(true);
      console.log("Validating question");
      const validatedQuestion = validateQuestion(currentQuestion);

      console.log("Calling getDentalAdvice with question:", validatedQuestion);
      let answer = await getDentalAdvice(validatedQuestion, currentImage, conversationContext);

      // Reset retry count on success
      setRetryCount(0);

      // Ensure we have the complete response
      answer = answer.trim();
      if (!answer) {
        console.error("Empty response received");
        throw new Error('Empty response received');
      }

      console.log("Response received:", answer.substring(0, 50) + "...");

      // Update context with new exchange
      updateContext(validatedQuestion, answer);

      // Only set the response if this was an explicit user request
      // This prevents automatic responses without user interaction
      if (currentQuestion.trim()) {
        setResponse(answer);
        console.log("Response set successfully");
      } else {
        console.log("Skipping response update - no explicit question");
      }

      // Save messages to database if user is logged in
      if (user && currentConversation) {
        try {
          await saveChatMessage(user.id, validatedQuestion, 'user', currentImage);
          await saveChatMessage(user.id, answer, 'assistant');
          await fetchHistory();

          // Check if conversation should be ended due to inactivity
          const inactiveTime = Date.now() - new Date(currentConversation.started_at).getTime();
          if (inactiveTime > 30 * 60 * 1000) { // 30 minutes
            await endConversation(currentConversation.id);
            setCurrentConversation(null);
          }
        } catch (saveErr) {
          // Log error but don't disturb the user experience
          console.error('Error saving chat messages:', saveErr);
        }
      }
    } catch (err) {
      // Handle retry logic for transient errors
      console.error("Error in handleSubmit:", err);
      if (retryCount < MAX_RETRIES && (
          err.message.includes('timeout') ||
          err.message.includes('network') ||
          err.message.includes('429') ||
          err.message.includes('rate limit')
      )) {
        setRetryCount(prev => prev + 1);
        console.log(`Retrying API call (${retryCount + 1}/${MAX_RETRIES})...`);

        // Wait before retrying (with exponential backoff)
        const backoffTime = Math.pow(2, retryCount) * 1000;
        setTimeout(() => {
          // Retry the submit with the same question
          handleSubmit();
        }, backoffTime);
        return;
      }

      // Handle non-retryable errors
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      console.error('Chat error:', err);

      // Reset retry count after handling
      setRetryCount(0);
    } finally {
      console.log("Setting loading state to false");
      setLoading(false);
    }
  };

  // Load history when user changes
  useEffect(() => {
    if (user) {
      fetchHistory();
    }
  }, [user, fetchHistory]);

  return {
    question,
    setQuestion,
    response,
    loading,
    image,
    setImage,
    history,
    error,
    clearError,
    currentConversation,
    handleSubmit,
    fetchHistory,
    conversationContext,
    lastSubmittedQuestion
  };
}