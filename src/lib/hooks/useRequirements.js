import { useState, useEffect } from 'react';
import { supabase } from '../supabase';

export function useRequirements(schoolId) {
  const [requirements, setRequirements] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function loadRequirements() {
      if (!schoolId) {
        setRequirements([]);
        setLoading(false);
        return;
      }

      try {
        const { data, error } = await supabase
          .from('school_requirements')
          .select('*')
          .eq('school_id', schoolId)
          .single();

        if (error) throw error;
        setRequirements(data || []);
      } catch (err) {
        console.error('Error loading requirements:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }

    loadRequirements();
  }, [schoolId]);

  return { requirements, loading, error };
}