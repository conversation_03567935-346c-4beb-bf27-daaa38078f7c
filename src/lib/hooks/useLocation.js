import { useState, useCallback } from 'react';
import { 
  getCurrentLocation, 
  getLocationFromZip,
  calculateDistance 
} from '../services/location';
import { logSearch } from '../services/searchLogService';

export function useLocation() {
  const [location, setLocation] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const requestLocation = useCallback(async () => {
    console.log('Starting location request...');
    try {
      setLoading(true);
      setError(null);
      
      const coords = await getCurrentLocation();
      console.log('Location retrieved successfully:', coords);
      
      // Log successful location request
      await logSearch('granted', coords);
      
      setLocation(coords);
      return coords;
    } catch (err) {
      console.error('Location request failed:', err);
      setError(err.message);
      
      // Log failed location request
      await logSearch('denied');
      
      return null;
    } finally {
      setLoading(false);
      console.log('Location request completed');
    }
  }, []);

  const searchByZip = useCallback(async (zipCode) => {
    try {
      setLoading(true);
      setError(null);
      
      const coords = await getLocationFromZip(zipCode);
      setLocation(coords);
      return coords;
    } catch (err) {
      setError(err.message);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const getDistance = useCallback((lat, lon) => {
    if (!location) return null;
    return calculateDistance(
      location.latitude,
      location.longitude,
      lat,
      lon
    );
  }, [location]);

  return { 
    location,
    loading,
    error,
    requestLocation,
    searchByZip,
    getDistance,
    clearError: () => setError(null)
  };
}