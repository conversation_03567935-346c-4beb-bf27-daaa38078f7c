import { useState, useCallback } from 'react';
import { uploadSTLFile, getSTLFiles, deleteSTLFile } from '../services/stlService';

export function useSTLViewer(user) {
  const [files, setFiles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [currentFile, setCurrentFile] = useState(null);

  const loadFiles = useCallback(async () => {
    if (!user) return;
    
    try {
      setLoading(true);
      const data = await getSTLFiles(user.id);
      setFiles(data);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [user]);

  const handleUpload = useCallback(async (file) => {
    if (!user) {
      setError('Please sign in to upload files');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const url = await uploadSTLFile(file, user.id);
      
      // Create blob URL for local preview
      const blobUrl = URL.createObjectURL(file);
      setCurrentFile(blobUrl);
      
      await loadFiles();
      return url;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [user, loadFiles]);

  const handleDelete = useCallback(async (fileId) => {
    if (!user) return;

    try {
      setLoading(true);
      await deleteSTLFile(fileId, user.id);
      await loadFiles();
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [user, loadFiles]);

  return {
    files,
    currentFile,
    loading,
    error,
    handleUpload,
    handleDelete,
    loadFiles,
    clearError: () => setError(null)
  };
}