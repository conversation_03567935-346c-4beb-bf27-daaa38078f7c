import { useState, useEffect, useCallback } from 'react';
import { supabase } from '../supabase';
import { handleApiError } from '../utils/errorHandler';

// Feature flag to completely disable PreDental features
// Set to false to prevent any database queries
const PREDENTAL_FEATURES_ENABLED = false;

export function usePreDentalProgress(userId) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [courses, setCourses] = useState([]);
  const [gpaStats, setGpaStats] = useState({
    overall: 0,
    science: 0,
    totalCredits: 0,
    scienceCredits: 0
  });

  const loadCourses = useCallback(async () => {
    // Skip completely if feature is disabled
    if (!PREDENTAL_FEATURES_ENABLED || !userId) {
      return;
    }
    
    try {
      setLoading(true);
      setError(null);

      // Initialize progress if it doesn't exist
      const { data: progress, error: progressError } = await supabase
        .from('predental_progress')
        .select('*')
        .eq('user_id', userId)
        .maybeSingle();

      if (!progress && (!progressError || progressError.code === 'PGRST116')) {
        await supabase
          .from('predental_progress')
          .insert([{
            user_id: userId,
            overall_gpa: 0,
            science_gpa: 0,
            total_credits: 0,
            science_credits: 0
          }]);
      } else if (progressError && progressError.code !== 'PGRST116') {
        throw progressError;
      }

      const { data, error } = await supabase
        .from('predental_courses')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      // PGRST116 means no data found, which is fine for new users
      if (error && error.code !== 'PGRST116') throw error;
      
      setCourses(data || []);
      await calculateGPA(data || []);
    } catch (err) {
      console.error('Error loading courses:', err);
      setError(handleApiError(err));
    } finally {
      setLoading(false);
    }
  }, [userId]);

  const calculateGPA = async (courseData) => {
    // Skip completely if feature is disabled
    if (!PREDENTAL_FEATURES_ENABLED) {
      return;
    }
    
    const courses = courseData || [];
    
    // Initialize stats with default values
    let stats = {
      overall: 0,
      science: 0,
      totalCredits: 0,
      scienceCredits: 0
    };

    if (courses.length === 0) {
      setGpaStats(stats);
      return;
    }

    const scienceCourses = courses.filter(c => 
      ['BIOLOGY', 'CHEMISTRY', 'PHYSICS'].includes(c.type)
    );

    const totalCredits = courses.reduce((sum, c) => sum + c.credits, 0);
    const scienceCredits = scienceCourses.reduce((sum, c) => sum + c.credits, 0);

    const totalPoints = courses.reduce((sum, c) => sum + (c.credits * getGradePoints(c.grade)), 0);
    const sciencePoints = scienceCourses.reduce((sum, c) => sum + (c.credits * getGradePoints(c.grade)), 0);

    stats = {
      overall: totalCredits > 0 ? totalPoints / totalCredits : 0,
      science: scienceCredits > 0 ? sciencePoints / scienceCredits : 0,
      totalCredits,
      scienceCredits
    };

    setGpaStats(stats);

    // Skip database update if feature is disabled
    if (!PREDENTAL_FEATURES_ENABLED) {
      return;
    }

    // Save GPA stats
    try {
      const { error } = await supabase
        .from('predental_progress')
        .upsert({
          user_id: userId,
          overall_gpa: stats.overall,
          science_gpa: stats.science,
          total_credits: stats.totalCredits,
          science_credits: stats.scienceCredits,
          updated_at: new Date().toISOString()
        });

      // Ignore PGRST116 errors (no data found)
      if (error && error.code !== 'PGRST116') throw error;
    } catch (err) {
      console.error('Error saving GPA stats:', err);
    }
  };

  const addCourse = async (courseData) => {
    // Skip completely if feature is disabled
    if (!PREDENTAL_FEATURES_ENABLED) {
      return null;
    }
    
    try {
      const { data, error } = await supabase
        .from('predental_courses')
        .insert([{
          ...courseData,
          user_id: userId,
          created_at: new Date().toISOString()
        }])
        .select()
        .single();

      if (error) throw error;
      
      setCourses(prev => [data, ...prev]);
      await calculateGPA([data, ...courses]);
      
      return data;
    } catch (err) {
      console.error('Error adding course:', err);
      throw new Error(handleApiError(err));
    }
  };

  const updateCourse = async (courseId, updates) => {
    // Skip completely if feature is disabled
    if (!PREDENTAL_FEATURES_ENABLED) {
      return null;
    }
    
    try {
      const { data, error } = await supabase
        .from('predental_courses')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', courseId)
        .eq('user_id', userId)
        .select()
        .single();

      if (error) throw error;

      setCourses(prev => prev.map(course => 
        course.id === courseId ? data : course
      ));
      await calculateGPA(courses.map(course => 
        course.id === courseId ? data : course
      ));

      return data;
    } catch (err) {
      console.error('Error updating course:', err);
      throw new Error(handleApiError(err));
    }
  };

  const deleteCourse = async (courseId) => {
    // Skip completely if feature is disabled
    if (!PREDENTAL_FEATURES_ENABLED) {
      return;
    }
    
    try {
      const { error } = await supabase
        .from('predental_courses')
        .delete()
        .eq('id', courseId)
        .eq('user_id', userId);

      if (error) throw error;

      const updatedCourses = courses.filter(course => course.id !== courseId);
      setCourses(updatedCourses);
      await calculateGPA(updatedCourses);
    } catch (err) {
      console.error('Error deleting course:', err);
      throw new Error(handleApiError(err));
    }
  };

  // Load courses on mount only if feature is enabled
  useEffect(() => {
    if (PREDENTAL_FEATURES_ENABLED && userId) {
      loadCourses();
    }
  }, [userId, loadCourses]);

  return {
    courses,
    gpaStats,
    loading,
    error,
    addCourse,
    updateCourse,
    deleteCourse,
    refresh: loadCourses
  };
}

// Helper function to convert letter grades to grade points
function getGradePoints(grade) {
  const gradePoints = {
    'A+': 4.0, 'A': 4.0, 'A-': 3.7,
    'B+': 3.3, 'B': 3.0, 'B-': 2.7,
    'C+': 2.3, 'C': 2.0, 'C-': 1.7,
    'D+': 1.3, 'D': 1.0, 'D-': 0.7,
    'F': 0.0
  };
  return gradePoints[grade] || 0;
}