import { useState } from 'react';

export default function useSymptomChecker() {
  const [symptoms, setSymptoms] = useState([]);
  const [results, setResults] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [currentImage, setCurrentImage] = useState(null);

  const addSymptom = (symptom) => {
    if (!symptoms.includes(symptom)) {
      setSymptoms([...symptoms, symptom]);
    }
  };

  const removeSymptom = (symptom) => {
    setSymptoms(symptoms.filter(s => s !== symptom));
  };

  const checkSymptoms = async () => {
    if (symptoms.length === 0 && !currentImage) {
      setError('Please select at least one symptom or upload an image');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Simplified for minimal version
      setResults({
        possibleConditions: [
          { name: 'Placeholder Condition', probability: 'High', description: 'This is a placeholder result.' }
        ],
        recommendations: ['Consult with a dental professional']
      });
    } catch (err) {
      console.error('Error checking symptoms:', err);
      setError('Failed to analyze symptoms');
    } finally {
      setLoading(false);
    }
  };

  const handleImageUpload = (file) => {
    setCurrentImage(file);
  };

  const clearResults = () => {
    setResults(null);
    setSymptoms([]);
    setCurrentImage(null);
  };

  const clearError = () => {
    setError(null);
  };

  return {
    symptoms,
    addSymptom,
    removeSymptom,
    checkSymptoms,
    results,
    loading,
    error,
    handleImageUpload,
    currentImage,
    clearError,
    clearResults
  };
}
