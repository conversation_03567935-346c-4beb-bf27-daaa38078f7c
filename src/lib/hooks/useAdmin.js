import { useState, useEffect } from "react";
import { supabase } from "../supabase";
import { useUser } from "../../contexts/UserContext";

export function useAdmin() {
  const { user } = useUser();
  const [isAdmin, setIsAdmin] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [adminBypass, setAdminBypass] = useState(false);
  
  // Use the same key as in useAdminAuth
  const ADMIN_SESSION_KEY = 'admin_session';
  // Specific admin username to check
  const ADMIN_USERNAME = 'MikeyMouse';
  const ADMIN_PASSWORD = 'MillionaireMike1$';

  // Check if database is available
  const checkDatabaseConnection = async () => {
    try {
      // Try a simple query as a connectivity test
      await supabase.from('health_check').select('*').limit(1);
      return true;
    } catch (error) {
      console.log("Database connectivity issue detected, enabling offline mode");
      // If database is unavailable, don't prevent admin login
      return false;
    }
  };

  useEffect(() => {
    console.log("useAdmin hook initialized - checking admin status");
    
    // Check if user has explicitly logged out previously
    const hasLoggedOut = localStorage.getItem('admin_logged_out') === 'true';
    
    if (hasLoggedOut) {
      console.log("User previously logged out, not creating admin session");
      setIsAdmin(false);
      setAdminBypass(false);
      setLoading(false);
      return;
    }
    
    // Check for existing admin session
    try {
      const sessionData = localStorage.getItem(ADMIN_SESSION_KEY);
      if (sessionData) {
        const session = JSON.parse(sessionData);
        
        // Validate if session is still valid
        if (session.exp && session.exp > Date.now()) {
          console.log("Valid admin session found");
          setIsAdmin(true);
          setAdminBypass(true);
          setLoading(false);
          return;
        } else {
          // Clear expired session
          console.log("Expired admin session found, clearing");
          localStorage.removeItem(ADMIN_SESSION_KEY);
        }
      }
      
      // Don't automatically create an admin session
      setIsAdmin(false);
      setAdminBypass(false);
    } catch (err) {
      console.error('Error checking admin session:', err);
      setIsAdmin(false);
      setAdminBypass(false);
    } finally {
      setLoading(false);
    }
  }, []);

  return { 
    isAdmin, 
    loading, 
    error, 
    adminBypass,
    clearError: () => setError(null),
    checkDatabaseConnection
  };
}
