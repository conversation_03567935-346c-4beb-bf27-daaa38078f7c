import { useEffect } from 'react';
import { supabase } from '../supabase';
import { useLocation } from 'react-router-dom';

const PRELOAD_CONFIG = {
  '/chat': ['users_chat_history', 'image_analysis'],
  '/predental': ['predental_progress', 'predental_courses'],
  '/expert-resources': ['article_cache']
};

export function usePreload() {
  const location = useLocation();

  useEffect(() => {
    const preloadData = async () => {
      const tablesToPreload = PRELOAD_CONFIG[location.pathname] || [];
      
      // Preload data for next likely routes
      const preloadPromises = tablesToPreload.map(table => 
        supabase
          .from(table)
          .select('*')
          .limit(10)
          .range(0, 9)
      );

      try {
        await Promise.all(preloadPromises);
      } catch (error) {
        console.error('Preload error:', error);
      }
    };

    preloadData();
  }, [location.pathname]);
}