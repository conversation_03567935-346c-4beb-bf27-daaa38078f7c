import { useState, useCallback, useEffect } from 'react';
import { supabase } from '../supabase';
import { useUser } from '../../contexts/UserContext';
import { 
  createConversation, 
  sendMessage, 
  getMessages,
  editMessage,
  deleteMessage,
  addReaction,
  removeReaction
} from '../services/messageService';
import { handleApiError } from '../utils/errorHandler';

export function useMessaging(conversationId) {
  const { user } = useUser();
  const [messages, setMessages] = useState([]);
  const [participants, setParticipants] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [typing, setTyping] = useState({});

  // Load initial messages and set up real-time subscription
  useEffect(() => {
    if (!conversationId || !user) return;

    let messageSubscription;
    let typingSubscription;

    const loadMessages = async () => {
      try {
        setLoading(true);
        const messages = await getMessages(conversationId);
        setMessages(messages);
        
        // Subscribe to new messages
        messageSubscription = supabase
          .channel(`messages:${conversationId}`)
          .on('postgres_changes', {
            event: '*',
            schema: 'public',
            table: 'messages',
            filter: `conversation_id=eq.${conversationId}`
          }, handleMessageChange)
          .subscribe();

        // Subscribe to typing indicators
        typingSubscription = supabase
          .channel(`typing:${conversationId}`)
          .on('presence', { event: 'sync' }, () => {
            const state = typingSubscription.presenceState();
            setTyping(state);
          })
          .subscribe();

      } catch (err) {
        setError(handleApiError(err));
      } finally {
        setLoading(false);
      }
    };

    loadMessages();

    return () => {
      messageSubscription?.unsubscribe();
      typingSubscription?.unsubscribe();
    };
  }, [conversationId, user]);

  // Load participants
  useEffect(() => {
    if (!conversationId) return;

    const loadParticipants = async () => {
      try {
        const { data, error } = await supabase
          .from('conversation_participants')
          .select(`
            user_id,
            role,
            joined_at,
            users:user_id (
              username,
              first_name,
              last_name
            )
          `)
          .eq('conversation_id', conversationId);

        if (error) throw error;
        setParticipants(data);
      } catch (err) {
        console.error('Error loading participants:', err);
      }
    };

    loadParticipants();
  }, [conversationId]);

  const handleMessageChange = (payload) => {
    if (!payload) return;

    const { eventType, new: newMessage, old: oldMessage } = payload;

    setMessages(current => {
      switch (eventType) {
        case 'INSERT':
          return [...current, newMessage];
        case 'UPDATE':
          return current.map(msg => 
            msg.id === newMessage.id ? newMessage : msg
          );
        case 'DELETE':
          return current.filter(msg => msg.id !== oldMessage.id);
        default:
          return current;
      }
    });
  };

  const startConversation = useCallback(async (type, participants, metadata) => {
    try {
      return await createConversation(type, participants, metadata);
    } catch (err) {
      setError(handleApiError(err));
      throw err;
    }
  }, []);

  const sendMessageToConversation = useCallback(async (content, options = {}) => {
    if (!conversationId || !user) return;

    try {
      return await sendMessage({
        conversationId,
        content,
        contentType: options.contentType,
        parentId: options.parentId
      });
    } catch (err) {
      setError(handleApiError(err));
      throw err;
    }
  }, [conversationId, user]);

  const editMessageInConversation = useCallback(async (messageId, newContent) => {
    try {
      return await editMessage(messageId, newContent);
    } catch (err) {
      setError(handleApiError(err));
      throw err;
    }
  }, []);

  const deleteMessageFromConversation = useCallback(async (messageId) => {
    try {
      await deleteMessage(messageId);
    } catch (err) {
      setError(handleApiError(err));
      throw err;
    }
  }, []);

  const addReactionToMessage = useCallback(async (messageId, reaction) => {
    try {
      await addReaction(messageId, reaction);
    } catch (err) {
      setError(handleApiError(err));
      throw err;
    }
  }, []);

  const removeReactionFromMessage = useCallback(async (messageId, reaction) => {
    try {
      await removeReaction(messageId, reaction);
    } catch (err) {
      setError(handleApiError(err));
      throw err;
    }
  }, []);

  const setTypingStatus = useCallback((isTyping) => {
    if (!conversationId || !user) return;

    const channel = supabase.channel(`typing:${conversationId}`);
    if (isTyping) {
      channel.track({
        user_id: user.id,
        username: user.username,
        typing: true
      });
    } else {
      channel.untrack();
    }
  }, [conversationId, user]);

  return {
    messages,
    participants,
    loading,
    error,
    typing,
    startConversation,
    sendMessage: sendMessageToConversation,
    editMessage: editMessageInConversation,
    deleteMessage: deleteMessageFromConversation,
    addReaction: addReactionToMessage,
    removeReaction: removeReactionFromMessage,
    setTypingStatus,
    clearError: () => setError(null)
  };
}