import { useState, useEffect } from 'react';

// Sample affiliate products - in a real app these would come from an API or CMS
const AFFILIATE_PRODUCTS = {
  dental: [
    {
      id: 'electric-toothbrush-1',
      title: 'Sonic Pro Electric Toothbrush',
      description: 'Advanced sonic technology with 5 brushing modes and pressure sensor.',
      image: 'https://images.unsplash.com/photo-1559741691-8bfc29c2c3ce?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1920&q=80',
      price: '$49.99',
      link: 'https://amazon.com/your-affiliate-link-1',
      tag: 'Best Seller',
      category: ['toothbrush', 'electric']
    },
    {
      id: 'water-flosser-1',
      title: 'AquaFresh Water Flosser',
      description: 'Cordless water flosser with 3 pressure modes, perfect for braces and implants.',
      image: 'https://images.unsplash.com/photo-1570612861542-284f4c12e75f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1950&q=80',
      price: '$35.95',
      link: 'https://amazon.com/your-affiliate-link-2',
      category: ['flosser', 'water']
    },
    {
      id: 'whitening-kit-1',
      title: 'Professional Teeth Whitening Kit',
      description: 'LED-activated whitening gel that provides professional results at home.',
      image: 'https://images.unsplash.com/photo-1609587312208-cea54be969e7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1940&q=80',
      price: '$29.99',
      link: 'https://amazon.com/your-affiliate-link-3',
      tag: 'Top Rated',
      category: ['whitening', 'kit']
    },
    {
      id: 'sensitive-toothpaste-1',
      title: 'Premium Sensitive Toothpaste',
      description: 'Specially formulated for sensitive teeth with added fluoride protection.',
      image: 'https://images.unsplash.com/photo-1564485377539-4af72d1f6a2f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1887&q=80',
      price: '$8.49',
      link: 'https://amazon.com/your-affiliate-link-4',
      category: ['toothpaste', 'sensitive']
    },
    {
      id: 'tongue-cleaner-1',
      title: 'Stainless Steel Tongue Cleaner',
      description: 'Ergonomic design for effective tongue cleaning and fresh breath.',
      image: 'https://images.unsplash.com/photo-1607613009820-a29f7bb81c04?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1980&q=80',
      price: '$12.99',
      link: 'https://amazon.com/your-affiliate-link-5',
      category: ['tongue cleaner']
    }
  ],
  affordable: [
    {
      id: 'dental-discount-plan-1',
      title: 'Family Dental Discount Plan',
      description: 'Save 20-60% on dental procedures with this annual membership plan.',
      image: 'https://images.unsplash.com/photo-1588776814546-daab30f310ce?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1974&q=80',
      price: '$149.99/yr',
      link: 'https://amazon.com/your-affiliate-link-6',
      tag: 'Best Value',
      category: ['discount plan', 'affordable']
    },
    {
      id: 'dental-insurance-1',
      title: 'Complete Dental Insurance Package',
      description: 'Comprehensive dental coverage for checkups, procedures, and emergencies.',
      image: 'https://images.unsplash.com/photo-1590016030279-33201aa30b3c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1974&q=80',
      price: '$24.99/mo',
      link: 'https://amazon.com/your-affiliate-link-7',
      category: ['insurance', 'affordable']
    }
  ],
  schoolReviews: [
    {
      id: 'dental-textbook-1',
      title: 'Essential Dental Anatomy Textbook',
      description: 'Comprehensive guide to dental anatomy with 3D illustrations.',
      image: 'https://images.unsplash.com/photo-1532187863486-abf9dbad1b69?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
      price: '$59.95',
      link: 'https://amazon.com/your-affiliate-link-8',
      tag: 'Student Pick',
      category: ['books', 'student']
    },
    {
      id: 'dental-loupes-1',
      title: 'Professional Dental Loupes',
      description: 'Lightweight magnification loupes for dental students and professionals.',
      image: 'https://images.unsplash.com/photo-1516549655669-df62a258d52d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1920&q=80',
      price: '$199.99',
      link: 'https://amazon.com/your-affiliate-link-9',
      category: ['tools', 'student']
    }
  ]
};

/**
 * Custom hook to fetch and filter affiliate products
 * 
 * @param {string} category - Category to filter products by
 * @param {number} limit - Maximum number of products to return
 * @param {object} filters - Additional filters to apply
 * @returns {Array} - Array of affiliate products
 */
const useAffiliateProducts = (category = 'dental', limit = 5, filters = {}) => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    const fetchProducts = () => {
      // Simulate API call delay
      setTimeout(() => {
        let results = AFFILIATE_PRODUCTS[category] || [];
        
        // Apply any additional filters
        if (filters.tag) {
          results = results.filter(product => product.tag === filters.tag);
        }
        
        if (filters.priceBelow) {
          results = results.filter(product => {
            const price = parseFloat(product.price.replace(/[^0-9.]/g, ''));
            return price < filters.priceBelow;
          });
        }
        
        // Limit the number of results
        results = results.slice(0, limit);
        
        setProducts(results);
        setLoading(false);
      }, 300);
    };
    
    fetchProducts();
  }, [category, limit, filters]);
  
  // Utility function to get random products from any category
  const getRandomProducts = (count = 3) => {
    const allProducts = Object.values(AFFILIATE_PRODUCTS).flat();
    
    // Shuffle array and get first 'count' items
    return [...allProducts]
      .sort(() => 0.5 - Math.random())
      .slice(0, count);
  };
  
  // Utility function to get related products based on categories
  const getRelatedProducts = (productCategories, count = 3) => {
    const allProducts = Object.values(AFFILIATE_PRODUCTS).flat();
    
    // Filter products that share at least one category with the current product
    const related = allProducts.filter(product => 
      product.category && productCategories.some(cat => product.category.includes(cat))
    );
    
    // Shuffle and take 'count' items
    return [...related]
      .sort(() => 0.5 - Math.random())
      .slice(0, count);
  };
  
  return { 
    products, 
    loading, 
    getRandomProducts, 
    getRelatedProducts 
  };
};

export default useAffiliateProducts; 