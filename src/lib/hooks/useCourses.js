import { useState, useEffect } from 'react';
import { supabase } from '../supabase';
import { useUser } from '../../contexts/UserContext';

export function useCourses() {
  const { user } = useUser();
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!user) {
      setCourses([]);
      setLoading(false);
      return;
    }

    loadCourses();
  }, [user]);

  const loadCourses = async () => {
    try {
      setLoading(true);
      const { data, error: fetchError } = await supabase
        .from('predental_courses')
        .select('*')
        .order('created_at', { ascending: false });

      if (fetchError) throw fetchError;
      setCourses(data || []);
    } catch (err) {
      console.error('Error loading courses:', err);
      setError('Failed to load courses');
    } finally {
      setLoading(false);
    }
  };

  const addCourse = async (courseData) => {
    try {
      const { data, error: insertError } = await supabase
        .from('predental_courses')
        .insert([{ ...courseData, user_id: user.id }])
        .select()
        .single();

      if (insertError) throw insertError;
      setCourses(prev => [data, ...prev]);
      return data;
    } catch (err) {
      console.error('Error adding course:', err);
      throw new Error('Failed to add course');
    }
  };

  const updateCourse = async (courseId, updates) => {
    try {
      const { data, error: updateError } = await supabase
        .from('predental_courses')
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq('id', courseId)
        .select()
        .single();

      if (updateError) throw updateError;
      setCourses(prev => prev.map(course => 
        course.id === courseId ? data : course
      ));
      return data;
    } catch (err) {
      console.error('Error updating course:', err);
      throw new Error('Failed to update course');
    }
  };

  const deleteCourse = async (courseId) => {
    try {
      const { error: deleteError } = await supabase
        .from('predental_courses')
        .delete()
        .eq('id', courseId);

      if (deleteError) throw deleteError;
      setCourses(prev => prev.filter(course => course.id !== courseId));
    } catch (err) {
      console.error('Error deleting course:', err);
      throw new Error('Failed to delete course');
    }
  };

  return {
    courses,
    loading,
    error,
    addCourse,
    updateCourse,
    deleteCourse,
    refresh: loadCourses
  };
}