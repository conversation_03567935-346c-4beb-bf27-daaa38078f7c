import { useState, useCallback } from 'react';
import { supabase } from '../supabase';
import { handleApiError } from '../utils/errorHandler';

export function useChatHistory(userId) {
  const [history, setHistory] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const loadHistory = useCallback(async () => {
    if (!userId) return;

    try {
      setLoading(true);
      setError(null);

      const { data, error: fetchError } = await supabase
        .from('users_chat_history')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (fetchError) throw fetchError;
      setHistory(data || []);
    } catch (err) {
      console.error('Error loading chat history:', err);
      setError(handleApiError(err));
    } finally {
      setLoading(false);
    }
  }, [userId]);

  const addToHistory = useCallback(async (message) => {
    if (!userId) return;

    try {
      const { error: insertError } = await supabase
        .from('users_chat_history')
        .insert([{
          user_id: userId,
          message_type: message.type,
          content: message.content,
          created_at: message.timestamp
        }]);

      if (insertError) throw insertError;
      await loadHistory();
    } catch (err) {
      console.error('Error adding to chat history:', err);
      setError(handleApiError(err));
    }
  }, [userId, loadHistory]);

  return {
    history,
    loading,
    error,
    loadHistory,
    addToHistory,
    clearError: () => setError(null)
  };
}