// Direct implementation using OpenAI API without server proxy
import { config } from './config';
import { DENTAL_KNOWLEDGE, API_CONFIG } from './constants';

export const getDentalAdvice = async (question, imageInput = null, conversationContext = []) => {
  try {
    // Convert File object to data URL if needed
    let imageUrl = imageInput;

    if (imageInput instanceof File) {
      console.log('Converting File object to data URL');
      imageUrl = await new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result);
        reader.onerror = reject;
        reader.readAsDataURL(imageInput);
      });
    }

    console.log('Image type:', typeof imageUrl);
    if (imageUrl) {
      console.log('Image URL starts with:', imageUrl.substring(0, 30) + '...');
    }

    // Create the base system message
    const systemMessage = {
      role: "system",
      content: "You are PEA<PERSON><PERSON>, a dental information assistant. Provide helpful information about dental health, but always include a disclaimer that this is not professional medical advice and users should consult a dentist for proper diagnosis and treatment. Using DENTAL_KNOWLEDGE to provide accurate info: " + JSON.stringify(DENTAL_KNOWLEDGE)
    };

    // Initialize messages array with system message
    let messages = [systemMessage];
    
    // Add conversation context if provided
    if (conversationContext && conversationContext.length > 0) {
      // Filter out any system messages from the context to avoid conflicts
      const contextWithoutSystem = conversationContext.filter(msg => msg.role !== 'system');
      messages = [systemMessage, ...contextWithoutSystem];
    }
    
    // Add the current question
    messages.push({
      role: "user",
      content: question
    });

    // Direct integration with OpenAI API
    if (imageUrl) {
      // Image content for vision model
      const imageMessage = {
        role: "user",
        content: [
          { type: "text", text: "Please analyze this dental image:" },
          { type: "image_url", image_url: { url: imageUrl } }
        ]
      };
      messages.push(imageMessage);

      // Create OpenAI API request with image
      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${config.openai.apiKey}`
        },
        body: JSON.stringify({
          model: API_CONFIG.openai.models.vision,
          messages: messages,
          max_tokens: API_CONFIG.openai.maxTokens.vision
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error?.message || `Failed to get AI response: ${response.status}`);
      }

      const result = await response.json();
      return result.choices[0].message.content;
    } else {
      // Regular text completion
      console.log('Using direct OpenAI integration for text completion');
      console.log('Sending messages:', JSON.stringify(messages));
      
      // Create OpenAI API request for text only
      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${config.openai.apiKey}`
        },
        body: JSON.stringify({
          model: API_CONFIG.openai.models.chat,
          messages: messages,
          max_tokens: API_CONFIG.openai.maxTokens.chat
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error?.message || `Failed to get AI response: ${response.status}`);
      }

      const result = await response.json();
      return result.choices[0].message.content;
    }
  } catch (error) {
    console.error('Error getting dental advice:', error);

    // Check for specific error types
    let errorMessage = 'Sorry, I was unable to process your request. Please try again later.';

    if (error.message) {
      if (error.message.includes('API key')) {
        errorMessage = 'API configuration error. Please check your OpenAI API key.';
      } else if (error.message.includes('rate limit') || error.message.includes('429')) {
        errorMessage = 'Rate limit exceeded. Please try again in a few moments.';
      } else if (error.message.includes('Server error') || error.message.includes('500')) {
        errorMessage = 'The AI service is temporarily unavailable. Please try again later.';
      } else if (error.message.includes('invalid_api_key') || error.message.includes('401')) {
        errorMessage = 'Invalid API key. Please check your OpenAI API key configuration.';
      } else if (error.message.includes('Failed to get AI response')) {
        errorMessage = error.message;
      }
    }

    // If this is mock mode (empty API key), return a fake response 
    if (!config.openai.apiKey) {
      return "Hello! I'm your dental assistant. I notice you're running in demo mode without an OpenAI API key. To get real AI-powered dental advice, please add your OpenAI API key to your .env file as VITE_OPENAI_API_KEY=your-key-here. \n\nFor now, here's what I can tell you: Regular brushing (twice daily for 2 minutes) and flossing are essential for good dental health. Visit your dentist every 6 months for checkups, and consider using fluoride toothpaste to strengthen enamel. Limiting sugary foods and drinks can also help prevent cavities. \n\nDisclaimer: This is general information only and not professional medical advice. Please consult with a dentist for personalized recommendations.";
    }

    return errorMessage;
  }
};