import { REGIONS } from './dentalSchools/regions';

// Northeast Schools
const NORTHEAST_SCHOOLS = [
  {
    id: 'harvard-dental',
    name: 'Harvard School of Dental Medicine',
    address: '188 Longwood Ave, Boston, MA 02115',
    coordinates: { latitude: 42.3359, longitude: -71.1027 },
    region: REGIONS.NORTHEAST,
    website: 'https://hsdm.harvard.edu',
    phone: '(*************',
    accredited: true,
    programs: ['DMD', 'Advanced Education'],
    clinicInfo: 'Teaching clinic offering comprehensive dental care'
  },
  // ... other northeast schools
];

// Southeast Schools
const SOUTHEAST_SCHOOLS = [
  {
    id: 'nova-southeastern',
    name: 'Nova Southeastern University College of Dental Medicine',
    address: '3200 S University Dr, Fort Lauderdale, FL 33328',
    coordinates: { latitude: 26.0798, longitude: -80.2405 },
    region: REGIONS.SOUTHEAST,
    website: 'https://dental.nova.edu',
    phone: '(*************',
    accredited: true,
    programs: ['DMD', 'Advanced Education'],
    clinicInfo: 'Comprehensive dental care at reduced fees'
  },
  // ... other southeast schools
];

// Combine all schools
export const ACCREDITED_SCHOOLS = [
  ...NORTHEAST_SCHOOLS,
  ...SOUTHEAST_SCHOOLS,
  // ... other regions
];

// Utility functions
export const filterSchoolsByDistance = (schools, coords, radius) => {
  if (!coords || radius === Infinity) return schools;
  return schools.filter(school => school.distance <= radius);
};

export const sortByDistance = (schools) => {
  return [...schools].sort((a, b) => {
    if (!a.distance || !b.distance) return 0;
    return a.distance - b.distance;
  });
};