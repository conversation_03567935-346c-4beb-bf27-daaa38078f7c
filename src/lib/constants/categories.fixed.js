// Define resource categories
export const RESOURCE_CATEGORIES = [
  {
    title: "Oral Hygiene",
    description: "Essential daily dental care practices",
    articles: [
      {
        title: "Proper Brushing Techniques",
        summary: "Learn the ADA-recommended method for brushing teeth effectively",
        source: "American Dental Association",
        link: "https://www.ada.org/resources/research/science-and-research-institute/oral-health-topics/toothbrushes",
        tags: ["brushing", "hygiene", "prevention"]
      },
      {
        title: "Flossing Guide",
        summary: "Step-by-step guide to proper flossing technique",
        source: "Mayo Clinic",
        link: "https://www.mayoclinic.org/healthy-lifestyle/adult-health/in-depth/dental/art-20045536",
        tags: ["flossing", "hygiene", "prevention"]
      },
      {
        title: "Electric vs Manual Toothbrushes",
        summary: "Comparative analysis of different toothbrush types and their effectiveness",
        source: "Journal of Clinical Periodontology",
        link: "https://onlinelibrary.wiley.com/doi/10.1111/jcpe.13126",
        tags: ["brushing", "technology", "comparison"]
      },
      {
        title: "Water Flossers: A Modern Solution",
        summary: "Understanding the benefits and proper use of water flossers",
        source: "British Dental Journal",
        link: "https://www.nature.com/articles/sj.bdj.2019.304",
        tags: ["flossing", "technology", "comparison"]
      }
    ]
  },
  {
    title: "Preventive Care",
    description: "Proactive measures to prevent dental issues",
    articles: [
      {
        title: "Fluoride Treatment Benefits",
        summary: "How fluoride strengthens teeth and prevents decay",
        source: "CDC",
        link: "https://www.cdc.gov/fluoridation/basics/index.htm",
        tags: ["prevention", "fluoride", "treatment"]
      },
      {
        title: "Dental Sealants Explained",
        summary: "Protective coatings that help prevent cavities",
        source: "American Dental Association",
        link: "https://www.ada.org/resources/research/science-and-research-institute/oral-health-topics/dental-sealants",
        tags: ["prevention", "sealants", "children"]
      },
      {
        title: "Regular Dental Check-ups",
        summary: "Why routine dental visits are essential for oral health",
        source: "Mayo Clinic",
        link: "https://www.mayoclinic.org/healthy-lifestyle/adult-health/in-depth/dental/art-20045536",
        tags: ["prevention", "check-ups", "professional care"]
      }
    ]
  }
];

// Reading time estimation utility
export const getReadingTime = (article) => {
  const wordsPerMinute = 200;
  const wordCount = (article.summary || '').split(' ').length;
  return Math.ceil(wordCount / wordsPerMinute);
};

// Difficulty level assessment
export const getDifficultyLevel = (article) => {
  const technicalTags = ['research', 'clinical', 'technology', 'scientific'];
  const technicalTerms = article.tags.filter(tag => technicalTags.includes(tag)).length;

  if (technicalTerms >= 2) return 'Advanced';
  if (technicalTerms === 1) return 'Intermediate';
  return 'Beginner';
};

// Article metadata enrichment
export const enrichArticleMetadata = (article) => {
  // Safely handle publishedDate
  const publishDate = article.publishedDate ? new Date(article.publishedDate) : new Date();
  const isValidDate = !isNaN(publishDate.getTime());

  return {
    ...article,
    readingTime: getReadingTime(article),
    difficultyLevel: getDifficultyLevel(article),
    isNew: isValidDate ? (publishDate.getTime() > Date.now() - 30 * 24 * 60 * 60 * 1000) : false,
    hasAttachments: Boolean(article.attachments?.length)
  };
};

// Export enhanced categories with metadata
export const ENHANCED_RESOURCE_CATEGORIES = RESOURCE_CATEGORIES.map(category => ({
  ...category,
  articles: category.articles.map(enrichArticleMetadata)
}));

// Search functionality
export const searchArticles = (query, categories = RESOURCE_CATEGORIES) => {
  const normalizedQuery = query.toLowerCase().trim();
  
  return categories.flatMap(category => 
    category.articles.filter(article => 
      article.title.toLowerCase().includes(normalizedQuery) || 
      article.summary.toLowerCase().includes(normalizedQuery) ||
      article.tags.some(tag => tag.toLowerCase().includes(normalizedQuery))
    )
  );
};

// Get related articles
export const getRelatedArticles = (article, limit = 3, categories = RESOURCE_CATEGORIES) => {
  if (!article || !article.tags || article.tags.length === 0) {
    return [];
  }
  
  return categories
    .flatMap(category => category.articles)
    .filter(a => a.title !== article.title) // Exclude the current article
    .map(a => ({
      ...a,
      relevanceScore: a.tags.filter(tag => article.tags.includes(tag)).length
    }))
    .sort((a, b) => b.relevanceScore - a.relevanceScore)
    .slice(0, limit);
};

export const getPopularTags = (categories = RESOURCE_CATEGORIES) => {
  const tagCount = {};
  categories.forEach(category => {
    category.articles.forEach(article => {
      article.tags.forEach(tag => {
        tagCount[tag] = (tagCount[tag] || 0) + 1;
      });
    });
  });

  return Object.entries(tagCount)
    .sort(([, a], [, b]) => b - a)
    .map(([tag]) => tag);
};

export const getCategoryStats = (categories = RESOURCE_CATEGORIES) => {
  return categories.map(category => ({
    title: category.title,
    articleCount: category.articles.length,
    uniqueTags: [...new Set(category.articles.flatMap(article => article.tags))].length,
    sources: [...new Set(category.articles.map(article => article.source))].length
  }));
};

// Resource tracking and analytics
export const resourceAnalytics = {
  trackView: (articleId) => {
    // Implementation for view tracking
    console.log(`Viewed article: ${articleId}`);
  },
  trackShare: (articleId, platform) => {
    // Implementation for share tracking
    console.log(`Shared article: ${articleId} on ${platform}`);
  },
  trackDownload: (articleId, fileType) => {
    // Implementation for download tracking
    console.log(`Downloaded article: ${articleId} (${fileType})`);
  }
};

// Learning path suggestions
export const suggestLearningPath = (interests, level = 'Beginner') => {
  const articles = RESOURCE_CATEGORIES.flatMap(category => category.articles);
  const enrichedArticles = articles.map(enrichArticleMetadata);

  return enrichedArticles
    .filter(article =>
      article.tags.some(tag => interests.includes(tag)) &&
      article.difficultyLevel === level
    )
    .sort((a, b) => {
      if (a.isNew !== b.isNew) return a.isNew ? -1 : 1;
      return a.readingTime - b.readingTime;
    });
};
