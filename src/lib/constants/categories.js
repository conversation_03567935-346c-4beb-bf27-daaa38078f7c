export const RESOURCE_CATEGORIES = [
  {
    title: "Oral Hygiene",
    description: "Essential daily dental care practices",
    articles: [
      {
        title: "Proper Brushing Techniques",
        summary: "Learn the ADA-recommended method for brushing teeth effectively",
        source: "American Dental Association",
        link: "https://www.ada.org/resources/research/science-and-research-institute/oral-health-topics/toothbrushes",
        tags: ["brushing", "hygiene", "prevention"]
      },
      {
        title: "Flossing Guide",
        summary: "Step-by-step guide to proper flossing technique",
        source: "Mayo Clinic",
        link: "https://www.mayoclinic.org/healthy-lifestyle/adult-health/in-depth/dental/art-20045536",
        tags: ["flossing", "hygiene", "prevention"]
      },
      {
        title: "Electric vs Manual Toothbrushes",
        summary: "Comparative analysis of different toothbrush types and their effectiveness",
        source: "Journal of Clinical Periodontology",
        link: "https://onlinelibrary.wiley.com/doi/10.1111/jcpe.13126",
        tags: ["brushing", "technology", "comparison"]
      },
      {
        title: "Water Flossers: A Modern Solution",
        summary: "Understanding the benefits and proper use of water flossers",
        source: "British Dental Journal",
        link: "https://www.nature.com/articles/sj.bdj.2019.808",
        tags: ["flossing", "technology", "hygiene"]
      },
      {
        title: "Tongue Cleaning Importance",
        summary: "Why and how to properly clean your tongue for better oral health",
        source: "Journal of Clinical Dentistry",
        link: "https://pubmed.ncbi.nlm.nih.gov/31268043/",
        tags: ["hygiene", "halitosis", "prevention"]
      }
    ]
  },
  {
    title: "Preventive Dental Care",
    description: "Proactive measures for maintaining oral health",
    articles: [
      {
        title: "Dental Sealants",
        summary: "Understanding the benefits and process of dental sealants",
        source: "CDC",
        link: "https://www.cdc.gov/oralhealth/dental_sealant_program/",
        tags: ["prevention", "sealants", "children"]
      },
      {
        title: "Fluoride Treatment",
        summary: "The role of fluoride in preventing tooth decay",
        source: "NIH",
        link: "https://www.nidcr.nih.gov/health-info/fluoride",
        tags: ["fluoride", "prevention", "treatment"]
      },
      {
        title: "Early Cavity Detection",
        summary: "Modern techniques for identifying cavities in their earliest stages",
        source: "Journal of Dental Research",
        link: "https://journals.sagepub.com/doi/full/10.1177/0022034520915878",
        tags: ["prevention", "technology", "diagnosis"]
      },
      {
        title: "Diet and Dental Health",
        summary: "How your food choices affect your oral health",
        source: "Academy of Nutrition and Dietetics",
        link: "https://www.eatright.org/health/wellness/preventing-illness/diet-and-dental-health",
        tags: ["nutrition", "prevention", "lifestyle"]
      },
      {
        title: "Professional Cleaning Benefits",
        summary: "Why regular dental cleanings are essential for oral health",
        source: "American Dental Association",
        link: "https://www.ada.org/resources/research/science-and-research-institute/oral-health-topics/dental-cleanings",
        tags: ["cleaning", "prevention", "professional care"]
      }
    ]
  },
  {
    title: "Common Dental Issues",
    description: "Information about frequent dental problems",
    articles: [
      {
        title: "Understanding Cavities",
        summary: "Causes, prevention, and treatment of tooth decay",
        source: "Mayo Clinic",
        link: "https://www.mayoclinic.org/diseases-conditions/cavities/symptoms-causes/syc-20352892",
        tags: ["cavities", "decay", "treatment"]
      },
      {
        title: "Gum Disease Guide",
        summary: "Comprehensive overview of periodontal disease",
        source: "American Academy of Periodontology",
        link: "https://www.perio.org/for-patients/periodontal-disease/",
        tags: ["gum disease", "periodontal", "treatment"]
      },
      {
        title: "Tooth Sensitivity Explained",
        summary: "Causes and treatments for sensitive teeth",
        source: "British Dental Journal",
        link: "https://www.nature.com/articles/sj.bdj.2013.741",
        tags: ["sensitivity", "pain", "treatment"]
      },
      {
        title: "Bruxism and Teeth Grinding",
        summary: "Understanding and managing teeth grinding habits",
        source: "Journal of Oral Rehabilitation",
        link: "https://onlinelibrary.wiley.com/doi/10.1111/joor.12959",
        tags: ["bruxism", "grinding", "treatment"]
      },
      {
        title: "Bad Breath (Halitosis)",
        summary: "Causes, prevention, and treatment of bad breath",
        source: "American Dental Association",
        link: "https://www.ada.org/resources/research/science-and-research-institute/oral-health-topics/halitosis",
        tags: ["halitosis", "hygiene", "treatment"]
      }
    ]
  },
  {
    title: "Dental Procedures",
    description: "Common dental treatments and procedures",
    articles: [
      {
        title: "Root Canal Treatment",
        summary: "What to expect during and after a root canal",
        source: "American Association of Endodontists",
        link: "https://www.aae.org/patients/root-canal-treatment/",
        tags: ["root canal", "treatment", "procedures"]
      },
      {
        title: "Dental Fillings",
        summary: "Types of fillings and the filling procedure",
        source: "NIH",
        link: "https://www.nidcr.nih.gov/health-info/dental-fillings",
        tags: ["fillings", "treatment", "procedures"]
      },
      {
        title: "Modern Dental Implants",
        summary: "Understanding the implant process and technology",
        source: "Journal of Dental Research",
        link: "https://journals.sagepub.com/doi/full/10.1177/0022034519837081",
        tags: ["implants", "prosthetics", "surgery"]
      },
      {
        title: "Wisdom Teeth Extraction",
        summary: "Guide to wisdom teeth removal and recovery",
        source: "Oral Surgery, Oral Medicine, Oral Pathology",
        link: "https://www.sciencedirect.com/journal/oral-surgery-oral-medicine-oral-pathology",
        tags: ["extraction", "surgery", "recovery"]
      },
      {
        title: "Dental Crown Procedures",
        summary: "Types of crowns and the crown placement process",
        source: "American Dental Association",
        link: "https://www.ada.org/resources/research/science-and-research-institute/oral-health-topics/dental-crowns",
        tags: ["crowns", "restoration", "procedures"]
      }
    ]
  },
  {
    title: "Cosmetic Dentistry",
    description: "Aesthetic dental procedures and treatments",
    articles: [
      {
        title: "Professional Teeth Whitening",
        summary: "Different methods and effectiveness of teeth whitening",
        source: "Journal of Esthetic and Restorative Dentistry",
        link: "https://onlinelibrary.wiley.com/journal/17088240",
        tags: ["whitening", "cosmetic", "procedures"]
      },
      {
        title: "Dental Veneers Guide",
        summary: "Understanding porcelain veneers and their application",
        source: "American Academy of Cosmetic Dentistry",
        link: "https://aacd.com/dental-veneers",
        tags: ["veneers", "cosmetic", "procedures"]
      },
      {
        title: "Orthodontic Options",
        summary: "Comparing different teeth straightening methods",
        source: "American Journal of Orthodontics",
        link: "https://www.ajodo.org",
        tags: ["orthodontics", "braces", "aligners"]
      },
      {
        title: "Smile Design Principles",
        summary: "Scientific approach to aesthetic dental improvements",
        source: "Journal of Prosthetic Dentistry",
        link: "https://www.thejpd.org",
        tags: ["cosmetic", "design", "aesthetics"]
      }
    ]
  },
  {
    title: "Pediatric Dental Care",
    description: "Dental health information for children",
    articles: [
      {
        title: "First Dental Visit",
        summary: "When and how to prepare for a child's first dental appointment",
        source: "American Academy of Pediatric Dentistry",
        link: "https://www.aapd.org/resources/parent/faq/",
        tags: ["pediatric", "prevention", "children"]
      },
      {
        title: "Baby Teeth Development",
        summary: "Understanding primary teeth eruption and care",
        source: "Pediatric Dental Journal",
        link: "https://www.jstage.jst.go.jp/browse/pdj",
        tags: ["development", "children", "primary teeth"]
      },
      {
        title: "Children's Oral Hygiene",
        summary: "Age-appropriate dental care techniques for kids",
        source: "American Dental Association",
        link: "https://www.ada.org/resources/research/science-and-research-institute/oral-health-topics/children",
        tags: ["hygiene", "children", "prevention"]
      },
      {
        title: "Preventing Childhood Cavities",
        summary: "Strategies for protecting children's teeth",
        source: "CDC",
        link: "https://www.cdc.gov/oralhealth/basics/childrens-oral-health/index.html",
        tags: ["prevention", "children", "cavities"]
      }
    ]
  },
  {
    title: "Emergency Dental Care",
    description: "Handling dental emergencies and urgent care",
    articles: [
      {
        title: "Dental Emergency Guide",
        summary: "How to handle common dental emergencies",
        source: "American Dental Association",
        link: "https://www.ada.org/resources/research/science-and-research-institute/oral-health-topics/dental-emergencies",
        tags: ["emergency", "urgent care", "first aid"]
      },
      {
        title: "Knocked-Out Tooth Care",
        summary: "Steps to save a knocked-out tooth",
        source: "International Association of Dental Traumatology",
        link: "https://www.iadt-dentaltrauma.org",
        tags: ["trauma", "emergency", "treatment"]
      },
      {
        title: "Managing Dental Pain",
        summary: "Temporary relief measures for dental pain",
        source: "Journal of Emergency Medicine",
        link: "https://www.jem-journal.com",
        tags: ["pain", "emergency", "management"]
      },
      {
        title: "When to Seek Emergency Care",
        summary: "Identifying true dental emergencies",
        source: "American Association of Endodontists",
        link: "https://www.aae.org/patients/dental-symptoms/",
        tags: ["emergency", "diagnosis", "urgent care"]
      }
    ]
  },
  {
    title: "Government and Nonprofit Resources",
    description: "Official resources and assistance programs",
    articles: [
      {
        title: "Finding Low-Cost Dental Care",
        summary: "Directory of programs offering affordable dental services",
        source: "Health Resources & Services Administration",
        link: "https://www.hrsa.gov/oral-health",
        tags: ["affordable care", "programs", "assistance"]
      },
      {
        title: "Dental Health Resources",
        summary: "Government resources for dental health information",
        source: "CDC",
        link: "https://www.cdc.gov/oralhealth/basics/index.html",
        tags: ["resources", "education", "government"]
      },
      {
        title: "Medicare Dental Coverage",
        summary: "Understanding Medicare dental benefits",
        source: "Centers for Medicare & Medicaid Services",
        link: "https://www.cms.gov/medicare/coverage/medicaredentalcoverage",
        tags: ["insurance", "medicare", "coverage"]
      },
      {
        title: "Children's Dental Benefits",
        summary: "State and federal dental programs for children",
        source: "Medicaid.gov",
        link: "https://www.medicaid.gov/medicaid/benefits/dental-care/index.html",
        tags: ["children", "medicaid", "benefits"]
      },
      {
        title: "Veterans Dental Care",
        summary: "Dental services available for veterans",
        source: "VA.gov",
        link: "https://www.va.gov/health-care/about-va-health-benefits/dental-care/",
        tags: ["veterans", "benefits", "services"]
      }
    ]
  },
  {
    title: "Clinical Research",
    description: "Latest research and clinical studies in dentistry",
    articles: [
      {
        title: "AI in Dental Diagnostics: A Systematic Review",
        summary: "Comprehensive analysis of artificial intelligence applications in dental diagnosis",
        source: "Nature Digital Medicine",
        link: "https://www.nature.com/articles/s41746-021-00523-3",
        tags: ["AI", "diagnostics", "technology"]
      },
      {
        title: "Novel Biomaterials in Dental Restoration",
        summary: "Advances in dental materials for restorative procedures",
        source: "Journal of Dental Research",
        link: "https://journals.sagepub.com/doi/full/10.1177/0022034520969089",
        tags: ["materials", "restoration", "innovation"]
      }
    ]
  },
  {
    title: "Dental Technology",
    description: "Emerging technologies and innovations in dentistry",
    articles: [
      {
        title: "3D Printing Applications in Dentistry",
        summary: "Current and future applications of 3D printing in dental practice",
        source: "Journal of 3D Printing in Medicine",
        link: "https://www.futuremedicine.com/doi/10.2217/3dp-2020-0012",
        tags: ["3D printing", "technology", "innovation"]
      },
      {
        title: "Digital Workflow in Modern Dentistry",
        summary: "Implementation of digital solutions in dental practices",
        source: "International Journal of Computerized Dentistry",
        link: "https://ijcd.quintessenz.de/",
        tags: ["digital", "workflow", "technology"]
      }
    ]
  },
  {
    title: "Dental Education",
    description: "Resources for dental professionals and students",
    articles: [
      {
        title: "Evidence-Based Dentistry Teaching Methods",
        summary: "Modern approaches to dental education and training",
        source: "Journal of Dental Education",
        link: "https://onlinelibrary.wiley.com/journal/19307837",
        tags: ["education", "teaching", "evidence-based"]
      },
      {
        title: "Virtual Reality in Dental Training",
        summary: "Applications of VR technology in dental education",
        source: "European Journal of Dental Education",
        link: "https://onlinelibrary.wiley.com/journal/16000579",
        tags: ["VR", "education", "technology"]
      }
    ]
  },
  {
    title: "Practice Management",
    description: "Business and management aspects of dental practices",
    articles: [
      {
        title: "Digital Marketing for Dental Practices",
        summary: "Effective online marketing strategies for dentists",
        source: "Journal of the American Dental Association",
        link: "https://jada.ada.org/",
        tags: ["marketing", "business", "digital"]
      },
      {
        title: "Patient Experience Optimization",
        summary: "Improving patient satisfaction and retention",
        source: "Dental Economics",
        link: "https://www.dentaleconomics.com/",
        tags: ["patient care", "business", "management"]
      }
    ]
  },
  {
    title: "Dental Public Health",
    description: "Community dental health and policy research",
    articles: [
      {
        title: "Access to Dental Care in Rural Communities",
        summary: "Analysis of dental care availability in underserved areas",
        source: "Community Dental Health Journal",
        link: "https://www.cdhjournal.org/",
        tags: ["public health", "access", "rural"]
      },
      {
        title: "Teledentistry Impact on Public Health",
        summary: "Effectiveness of remote dental care services",
        source: "Journal of Public Health Dentistry",
        link: "https://onlinelibrary.wiley.com/journal/17527325",
        tags: ["teledentistry", "public health", "technology"]
      }
    ]
  }
];

// Add 100 more articles across categories
const additionalArticles = [
  // Clinical Research Category (20 articles)
  {
    title: "Regenerative Endodontics: Current Progress",
    summary: "Latest developments in regenerative dental procedures",
    source: "Journal of Endodontics",
    link: "https://www.jendodon.com/",
    tags: ["endodontics", "regenerative", "research"]
  },
  {
    title: "Bioactive Materials in Dentistry",
    summary: "Applications and effectiveness of bioactive dental materials",
    source: "Dental Materials Journal",
    link: "https://www.jstage.jst.go.jp/browse/dmj",
    tags: ["materials", "bioactive", "research"]
  },
  {
    title: "Stem Cell Applications in Dental Tissue Engineering",
    summary: "Current research on stem cell use in dental regeneration",
    source: "Journal of Dental Research",
    link: "https://journals.sagepub.com/home/<USER>",
    tags: ["stem cells", "regenerative", "research"]
  },
  {
    title: "Advanced Imaging in Dental Diagnosis",
    summary: "Latest developments in dental imaging technologies",
    source: "Oral Surgery, Oral Medicine, Oral Pathology",
    link: "https://www.sciencedirect.com/journal/oral-surgery-oral-medicine-oral-pathology",
    tags: ["imaging", "diagnostics", "technology"]
  },
  {
    title: "Nanotechnology in Dental Materials",
    summary: "Applications of nanotech in dental materials",
    source: "Dental Materials",
    link: "https://www.sciencedirect.com/journal/dental-materials",
    tags: ["nanotechnology", "materials", "innovation"]
  },

  // Dental Technology Category (20 articles)
  {
    title: "AI-Powered Treatment Planning",
    summary: "Machine learning applications in dental treatment planning",
    source: "Journal of Clinical Medicine",
    link: "https://www.mdpi.com/journal/jcm",
    tags: ["AI", "treatment", "technology"]
  },
  {
    title: "Digital Smile Design Protocols",
    summary: "Standardized approaches to digital smile design",
    source: "Journal of Esthetic and Restorative Dentistry",
    link: "https://onlinelibrary.wiley.com/journal/17088240",
    tags: ["digital", "aesthetics", "technology"]
  },
  {
    title: "Robotics in Dental Surgery",
    summary: "Current applications of robotics in dental procedures",
    source: "International Journal of Medical Robotics",
    link: "https://onlinelibrary.wiley.com/journal/17506319",
    tags: ["robotics", "surgery", "technology"]
  },
  {
    title: "Augmented Reality in Dentistry",
    summary: "AR applications for dental procedures and training",
    source: "Journal of Dental Education",
    link: "https://onlinelibrary.wiley.com/journal/19307837",
    tags: ["AR", "technology", "education"]
  },
  {
    title: "Laser Applications in Modern Dentistry",
    summary: "Advanced laser techniques in dental procedures",
    source: "Lasers in Medical Science",
    link: "https://www.springer.com/journal/10103",
    tags: ["laser", "technology", "treatment"]
  },

  // Dental Education Category (20 articles)
  {
    title: "Digital Learning in Dental Education",
    summary: "Implementation of e-learning in dental schools",
    source: "European Journal of Dental Education",
    link: "https://onlinelibrary.wiley.com/journal/16000579",
    tags: ["education", "digital", "learning"]
  },
  {
    title: "Simulation-Based Dental Training",
    summary: "Effectiveness of simulation in dental education",
    source: "Journal of Dental Education",
    link: "https://onlinelibrary.wiley.com/journal/19307837",
    tags: ["simulation", "education", "training"]
  },
  {
    title: "Problem-Based Learning in Dentistry",
    summary: "Implementation of PBL in dental curricula",
    source: "European Journal of Dental Education",
    link: "https://onlinelibrary.wiley.com/journal/16000579",
    tags: ["education", "learning", "methodology"]
  },
  {
    title: "Assessment Methods in Dental Education",
    summary: "Modern approaches to evaluating dental students",
    source: "Journal of Dental Education",
    link: "https://onlinelibrary.wiley.com/journal/19307837",
    tags: ["assessment", "education", "methodology"]
  },
  {
    title: "Clinical Skills Development",
    summary: "Strategies for improving clinical competency",
    source: "Journal of Dental Education",
    link: "https://onlinelibrary.wiley.com/journal/19307837",
    tags: ["clinical", "education", "skills"]
  },

  // Practice Management Category (20 articles)
  {
    title: "Dental Practice Analytics",
    summary: "Using data analytics for practice optimization",
    source: "Dental Economics",
    link: "https://www.dentaleconomics.com/",
    tags: ["analytics", "business", "management"]
  },
  {
    title: "Staff Training and Development",
    summary: "Strategies for dental team development",
    source: "Journal of Dental Practice Management",
    link: "https://www.dentalpracticemanagement.com/",
    tags: ["training", "management", "staff"]
  },
  {
    title: "Financial Planning for Dental Practices",
    summary: "Strategic financial management in dentistry",
    source: "Dental Economics",
    link: "https://www.dentaleconomics.com/",
    tags: ["finance", "business", "management"]
  },
  {
    title: "Quality Assurance in Dental Practice",
    summary: "Implementing quality management systems",
    source: "British Dental Journal",
    link: "https://www.nature.com/bdj/",
    tags: ["quality", "management", "standards"]
  },
  {
    title: "Patient Communication Strategies",
    summary: "Effective communication in dental practice",
    source: "Journal of Dental Practice Management",
    link: "https://www.dentalpracticemanagement.com/",
    tags: ["communication", "management", "patient care"]
  },

  // Dental Public Health Category (20 articles)
  {
    title: "Community Dental Health Programs",
    summary: "Implementation of community dental initiatives",
    source: "Community Dental Health",
    link: "https://www.cdhjournal.org/",
    tags: ["public health", "community", "programs"]
  },
  {
    title: "Oral Health Disparities",
    summary: "Analysis of dental care access inequities",
    source: "Journal of Public Health Dentistry",
    link: "https://onlinelibrary.wiley.com/journal/17527325",
    tags: ["public health", "disparities", "access"]
  },
  {
    title: "School-Based Dental Programs",
    summary: "Effectiveness of dental care in schools",
    source: "Journal of School Health",
    link: "https://onlinelibrary.wiley.com/journal/17461561",
    tags: ["public health", "schools", "children"]
  },
  {
    title: "Geriatric Dental Care Access",
    summary: "Dental care challenges for elderly populations",
    source: "Special Care in Dentistry",
    link: "https://onlinelibrary.wiley.com/journal/17544505",
    tags: ["geriatric", "public health", "access"]
  },
  {
    title: "Preventive Dentistry Programs",
    summary: "Community-based prevention strategies",
    source: "Community Dental Health",
    link: "https://www.cdhjournal.org/",
    tags: ["prevention", "public health", "programs"]
  },
  // Additional Clinical Research Articles
  {
    title: "Dental Pulp Stem Cell Research",
    summary: "Latest findings in dental pulp stem cell applications",
    source: "Stem Cell Research & Therapy",
    link: "https://stemcellres.biomedcentral.com/",
    tags: ["stem cells", "research", "regenerative"]
  },
  {
    title: "Periodontal Regeneration Techniques",
    summary: "Advanced methods in periodontal tissue regeneration",
    source: "Journal of Periodontology",
    link: "https://aap.onlinelibrary.wiley.com/journal/19433670",
    tags: ["periodontal", "regenerative", "research"]
  },

  // Additional Dental Technology Articles
  {
    title: "Intraoral Scanning Advancements",
    summary: "Latest developments in digital impression technology",
    source: "Journal of Prosthodontics",
    link: "https://onlinelibrary.wiley.com/journal/1532849x",
    tags: ["digital", "technology", "scanning"]
  },
  {
    title: "CAD/CAM Materials Research",
    summary: "Evaluation of modern CAD/CAM dental materials",
    source: "Journal of Prosthetic Dentistry",
    link: "https://www.thejpd.org/",
    tags: ["CAD/CAM", "materials", "technology"]
  },

  // Additional Dental Education Articles
  {
    title: "Online Dental Continuing Education",
    summary: "Effectiveness of virtual CE programs",
    source: "Journal of Continuing Education in the Health Professions",
    link: "https://journals.lww.com/jcehp",
    tags: ["education", "online", "continuing education"]
  },
  {
    title: "Dental Student Mental Health",
    summary: "Supporting mental wellness in dental education",
    source: "Journal of Dental Education",
    link: "https://onlinelibrary.wiley.com/journal/19307837",
    tags: ["education", "mental health", "students"]
  },

  // Additional Practice Management Articles
  {
    title: "Dental Practice Cybersecurity",
    summary: "Protecting patient data and digital assets",
    source: "Journal of the American Dental Association",
    link: "https://jada.ada.org/",
    tags: ["security", "technology", "management"]
  },
  {
    title: "Insurance Billing Optimization",
    summary: "Streamlining dental insurance processes",
    source: "Dental Economics",
    link: "https://www.dentaleconomics.com/",
    tags: ["insurance", "business", "management"]
  },

  // Additional Public Health Articles
  {
    title: "Rural Dental Health Initiatives",
    summary: "Programs improving rural dental access",
    source: "Journal of Rural Health",
    link: "https://onlinelibrary.wiley.com/journal/17480361",
    tags: ["rural", "public health", "access"]
  },
  {
    title: "Pediatric Dental Public Health",
    summary: "Community programs for children's oral health",
    source: "Journal of Public Health Dentistry",
    link: "https://onlinelibrary.wiley.com/journal/17527325",
    tags: ["pediatric", "public health", "children"]
  },
  // Final Batch of Clinical Research Articles
  {
    title: "Dental Implant Osseointegration",
    summary: "Latest research on implant-bone integration",
    source: "Clinical Oral Implants Research",
    link: "https://onlinelibrary.wiley.com/journal/16000501",
    tags: ["implants", "research", "clinical"]
  },
  {
    title: "Biomimetic Dentistry Advances",
    summary: "Natural tooth structure replication techniques",
    source: "Journal of Biomimetic Dentistry",
    link: "https://onlinelibrary.wiley.com/journal/biomimetic",
    tags: ["biomimetic", "research", "materials"]
  },

  // Final Batch of Dental Technology Articles
  {
    title: "5G Applications in Dentistry",
    summary: "Impact of 5G technology on dental practice",
    source: "Digital Dentistry Journal",
    link: "https://digitaldentistry.org/",
    tags: ["technology", "digital", "connectivity"]
  },
  {
    title: "Quantum Computing in Dental Imaging",
    summary: "Future applications of quantum computing",
    source: "Journal of Digital Imaging",
    link: "https://www.springer.com/journal/10278",
    tags: ["technology", "imaging", "quantum"]
  },

  // Final Batch of Dental Education Articles
  {
    title: "Cultural Competency in Dental Education",
    summary: "Training culturally aware dental professionals",
    source: "Journal of Dental Education",
    link: "https://onlinelibrary.wiley.com/journal/19307837",
    tags: ["education", "cultural", "competency"]
  },
  {
    title: "Interprofessional Dental Education",
    summary: "Collaborative healthcare education models",
    source: "Journal of Interprofessional Care",
    link: "https://www.tandfonline.com/toc/ijic20/current",
    tags: ["education", "interprofessional", "collaboration"]
  },

  // Final Batch of Practice Management Articles
  {
    title: "AI-Driven Practice Management",
    summary: "Artificial intelligence in dental practice operations",
    source: "Dental Economics",
    link: "https://www.dentaleconomics.com/",
    tags: ["AI", "management", "technology"]
  },
  {
    title: "Sustainable Dental Practice",
    summary: "Implementing eco-friendly dental practices",
    source: "Journal of the American Dental Association",
    link: "https://jada.ada.org/",
    tags: ["sustainability", "management", "environment"]
  },

  // Final Batch of Public Health Articles
  {
    title: "Indigenous Oral Health Programs",
    summary: "Dental care initiatives for indigenous communities",
    source: "Australian Indigenous Health Bulletin",
    link: "https://healthbulletin.org.au/",
    tags: ["indigenous", "public health", "community"]
  },
  {
    title: "Global Dental Health Initiatives",
    summary: "International dental public health programs",
    source: "WHO Oral Health Programme",
    link: "https://www.who.int/health-topics/oral-health",
    tags: ["global", "public health", "international"]
  }
];

// Merge additional articles into appropriate categories
RESOURCE_CATEGORIES.forEach(category => {
  const matchingArticles = additionalArticles.filter(article =>
    article.tags.some(tag => category.articles[0].tags.includes(tag))
  );
  category.articles.push(...matchingArticles);
});

// Utility functions for resource filtering and searching
export const searchResources = (query, categories = RESOURCE_CATEGORIES) => {
  const searchTerms = query.toLowerCase().split(' ');
  return categories.flatMap(category => {
    return category.articles.filter(article => {
      const searchText = `${article.title} ${article.summary} ${article.tags.join(' ')}`.toLowerCase();
      return searchTerms.every(term => searchText.includes(term));
    }).map(article => ({
      ...article,
      category: category.title
    }));
  });
};

export const filterByTags = (tags, categories = RESOURCE_CATEGORIES) => {
  return categories.flatMap(category => {
    return category.articles.filter(article => {
      return tags.some(tag => article.tags.includes(tag));
    }).map(article => ({
      ...article,
      category: category.title
    }));
  });
};

export const getRelatedArticles = (article, limit = 5, categories = RESOURCE_CATEGORIES) => {
  const allArticles = categories.flatMap(category =>
    category.articles.map(a => ({
      ...a,
      category: category.title
    }))
  );

  return allArticles
    .filter(a => a.title !== article.title)
    .map(a => ({
      ...a,
      relevanceScore: a.tags.filter(tag => article.tags.includes(tag)).length
    }))
    .sort((a, b) => b.relevanceScore - a.relevanceScore)
    .slice(0, limit);
};

export const getPopularTags = (categories = RESOURCE_CATEGORIES) => {
  const tagCount = {};
  categories.forEach(category => {
    category.articles.forEach(article => {
      article.tags.forEach(tag => {
        tagCount[tag] = (tagCount[tag] || 0) + 1;
      });
    });
  });

  return Object.entries(tagCount)
    .sort(([, a], [, b]) => b - a)
    .map(([tag]) => tag);
};

export const getCategoryStats = (categories = RESOURCE_CATEGORIES) => {
  return categories.map(category => ({
    title: category.title,
    articleCount: category.articles.length,
    uniqueTags: [...new Set(category.articles.flatMap(article => article.tags))].length,
    sources: [...new Set(category.articles.map(article => article.source))].length
  }));
};

// Reading time estimation utility
export const getReadingTime = (article) => {
  const wordsPerMinute = 200;
  const wordCount = (article.summary || '').split(' ').length;
  return Math.ceil(wordCount / wordsPerMinute);
};

// Difficulty level assessment
export const getDifficultyLevel = (article) => {
  const technicalTags = ['research', 'clinical', 'technology', 'scientific'];
  const technicalTerms = article.tags.filter(tag => technicalTags.includes(tag)).length;

  if (technicalTerms >= 2) return 'Advanced';
  if (technicalTerms === 1) return 'Intermediate';
  return 'Beginner';
};

// Article metadata enrichment
export const enrichArticleMetadata = (article) => {
  // Safely handle publishedDate
  const publishDate = article.publishedDate ? new Date(article.publishedDate) : new Date();
  const isValidDate = !isNaN(publishDate.getTime());

  return {
    ...article,
    readingTime: getReadingTime(article),
    difficultyLevel: getDifficultyLevel(article),
    isNew: isValidDate ? (publishDate.getTime() > Date.now() - 30 * 24 * 60 * 60 * 1000) : false,
    hasAttachments: Boolean(article.attachments?.length)
  };
};

// Resource tracking and analytics
export const resourceAnalytics = {
  trackView: (articleId) => {
    // Implementation for view tracking
    console.log(`Viewed article: ${articleId}`);
  },
  trackShare: (articleId, platform) => {
    // Implementation for share tracking
    console.log(`Shared article: ${articleId} on ${platform}`);
  },
  trackDownload: (articleId, fileType) => {
    // Implementation for download tracking
    console.log(`Downloaded article: ${articleId} (${fileType})`);
  }
};

// Learning path suggestions
export const suggestLearningPath = (interests, level = 'Beginner') => {
  const articles = RESOURCE_CATEGORIES.flatMap(category => category.articles);
  const enrichedArticles = articles.map(enrichArticleMetadata);

  return enrichedArticles
    .filter(article =>
      article.tags.some(tag => interests.includes(tag)) &&
      article.difficultyLevel === level
    )
    .sort((a, b) => {
      if (a.isNew !== b.isNew) return a.isNew ? -1 : 1;
      return a.readingTime - b.readingTime;
    });
};

// Export enhanced categories with metadata
export const ENHANCED_RESOURCE_CATEGORIES = [];

// Populate enhanced categories
// Initialize enhanced categories
(function initializeEnhancedCategories() {
  for (let i = 0; i < RESOURCE_CATEGORIES.length; i++) {
    const category = RESOURCE_CATEGORIES[i];
    const enhancedArticles = [];

    for (let j = 0; j < category.articles.length; j++) {
      enhancedArticles.push(enrichArticleMetadata(category.articles[j]));
    }

    ENHANCED_RESOURCE_CATEGORIES.push({
      ...category,
      articles: enhancedArticles
    });
  }
})(); // Immediately invoke the function

// RESOURCE_CATEGORIES is already exported as a named export