export const NAV_ITEMS = [
  {
    name: 'Home',
    href: '/',
  },
  {
    name: '<PERSON><PERSON>',
    href: '/chat',
  },
  // Temporarily hidden until ready
  // {
  //   name: 'Pre-Dental',
  //   href: '/predental',
  // },
  // Temporarily hidden until ready
  // {
  //   name: 'DenTech',
  //   href: '/dentech',
  // },
  {
    name: 'Resources',
    href: '/expert-resources',
  },
  {
    name: 'Options',
    href: '/options',
  },
  // Temporarily hidden until ready
  // {
  //   name: 'Telehealth',
  //   href: '/telehealth',
  // },
  {
    name: 'About',
    href: '/about',
  },
  {
    name: 'Providers',
    href: '/providers',
  },
];

export const DENTECH_TOOLS = [
  {
    id: 'webcam',
    name: 'Webcam Oral Scanner',
    description: 'Use your webcam to scan your mouth for potential oral health issues',
    icon: 'camera',
    color: 'from-blue-500 to-indigo-500'
  },
  {
    id: 'voice',
    name: 'Voice Analysis',
    description: 'Analyze your voice patterns to detect potential breath and oral health concerns',
    icon: 'microphone',
    color: 'from-purple-500 to-pink-500'
  },
  {
    id: 'breath',
    name: 'Breath Analysis',
    description: 'Evaluate your breath health with AI-powered breath analysis',
    icon: 'wind',
    color: 'from-cyan-500 to-blue-500'
  },
  {
    id: 'mirror',
    name: 'Mirror Mode - Dental Coach',
    description: 'Real-time AI coaching while you brush and floss using your webcam',
    icon: 'mirror',
    color: 'from-emerald-500 to-teal-500'
  }
];

export const PREMIUM_DENTECH_TOOLS = [
  {
    id: 'thermal',
    name: 'Thermal Imaging',
    description: 'Detect inflammation and infections using thermal imaging technology',
    icon: 'fire',
    color: 'from-red-500 to-yellow-500',
    requiredHardware: 'Thermal camera attachment',
    hardwarePrice: '$149.99'
  },
  {
    id: 'smilobrush',
    name: 'SmiloBrush Sync',
    description: 'Connect your SmiloBrush device to analyze brushing data and get AI-powered insights',
    icon: 'bluetooth',
    color: 'from-indigo-500 to-blue-500',
    requiredHardware: 'SmiloBrush smart toothbrush',
    hardwarePrice: '$89.99'
  },
  {
    id: 'smilosecure',
    name: 'Smilo Secure',
    description: 'Record dental visits, transcribe conversations, and compare with billing to detect potential fraud',
    icon: 'shield',
    color: 'from-emerald-600 to-green-500',
    requiredHardware: 'High-quality microphone (lapel or portable)',
    hardwarePrice: '$59.99'
  }
];

export const FOOTER_LINKS = [
  {
    section: 'Company',
    links: [
      { name: 'Home', href: '/' },
      { name: 'About', href: '/about' },
      { name: 'Privacy', href: '/privacy' },
      { name: 'Terms', href: '/terms' },
      { name: 'Partnerships', href: '/partnerships' },
    ],
  },
  {
    section: 'Resources',
    links: [
      { name: 'Chat with AI', href: '/chat' },
      // Temporarily hidden until ready
      // { name: 'Pre-Dental Resources', href: '/predental' },
      { name: 'Dental Resources', href: '/expert-resources' },
      { name: 'Options', href: '/options' },
      // Temporarily hidden until ready
      // { name: 'DenTech', href: '/dentech' },
      // { name: 'Telehealth', href: '/telehealth' },
    ],
  },
  {
    section: 'Connect',
    links: [
      { name: 'Twitter', href: 'https://twitter.com/smilo.dental' },
      { name: 'Instagram', href: 'https://instagram.com/smilo.dental' },
      { name: 'LinkedIn', href: 'https://linkedin.com/company/smilo-dentai-assistant' },
      { name: 'Contact', href: 'mailto:<EMAIL>' },
    ],
  },
];