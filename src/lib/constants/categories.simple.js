// Define resource categories
export const RESOURCE_CATEGORIES = [
  {
    title: "Oral Hygiene",
    description: "Essential daily dental care practices",
    articles: [
      {
        title: "Proper Brushing Techniques",
        summary: "Learn the ADA-recommended method for brushing teeth effectively",
        source: "American Dental Association",
        link: "https://www.ada.org/resources/research/science-and-research-institute/oral-health-topics/toothbrushes",
        tags: ["brushing", "hygiene", "prevention"]
      },
      {
        title: "Flossing Guide",
        summary: "Step-by-step guide to proper flossing technique",
        source: "Mayo Clinic",
        link: "https://www.mayoclinic.org/healthy-lifestyle/adult-health/in-depth/dental/art-20045536",
        tags: ["flossing", "hygiene", "prevention"]
      }
    ]
  },
  {
    title: "Preventive Care",
    description: "Proactive measures to prevent dental issues",
    articles: [
      {
        title: "Fluoride Treatment Benefits",
        summary: "How fluoride strengthens teeth and prevents decay",
        source: "CDC",
        link: "https://www.cdc.gov/fluoridation/basics/index.htm",
        tags: ["prevention", "fluoride", "treatment"]
      },
      {
        title: "Dental Sealants Explained",
        summary: "Protective coatings that help prevent cavities",
        source: "American Dental Association",
        link: "https://www.ada.org/resources/research/science-and-research-institute/oral-health-topics/dental-sealants",
        tags: ["prevention", "sealants", "children"]
      }
    ]
  }
];

// Enhanced categories with metadata
export const ENHANCED_RESOURCE_CATEGORIES = RESOURCE_CATEGORIES.map(category => ({
  ...category,
  articles: category.articles.map(article => ({
    ...article,
    readingTime: 2,
    difficultyLevel: 'Beginner',
    isNew: false,
    hasAttachments: false
  }))
}));

// Search functionality
export const searchArticles = (query, categories = RESOURCE_CATEGORIES) => {
  const normalizedQuery = query.toLowerCase().trim();
  
  return categories.flatMap(category => 
    category.articles.filter(article => 
      article.title.toLowerCase().includes(normalizedQuery) || 
      article.summary.toLowerCase().includes(normalizedQuery) ||
      article.tags.some(tag => tag.toLowerCase().includes(normalizedQuery))
    )
  );
};

// Get related articles
export const getRelatedArticles = (article, limit = 3, categories = RESOURCE_CATEGORIES) => {
  if (!article || !article.tags || article.tags.length === 0) {
    return [];
  }
  
  return categories
    .flatMap(category => category.articles)
    .filter(a => a.title !== article.title) // Exclude the current article
    .slice(0, limit);
};

export const getPopularTags = (categories = RESOURCE_CATEGORIES) => {
  const tagCount = {};
  categories.forEach(category => {
    category.articles.forEach(article => {
      article.tags.forEach(tag => {
        tagCount[tag] = (tagCount[tag] || 0) + 1;
      });
    });
  });

  return Object.entries(tagCount)
    .sort(([, a], [, b]) => b - a)
    .map(([tag]) => tag);
};

export const getCategoryStats = (categories = RESOURCE_CATEGORIES) => {
  return categories.map(category => ({
    title: category.title,
    articleCount: category.articles.length,
    uniqueTags: [...new Set(category.articles.flatMap(article => article.tags))].length,
    sources: [...new Set(category.articles.map(article => article.source))].length
  }));
};
