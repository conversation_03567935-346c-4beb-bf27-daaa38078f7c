import { NORTHEAST_SCHOOLS } from './schools/northeast';
import { SOUTHEAST_SCHOOLS } from './schools/southeast';
import { MIDWEST_SCHOOLS } from './schools/midwest';
import { SOUTHWEST_SCHOOLS } from './schools/southwest';
import { WEST_SCHOOLS } from './schools/west';
import { validateSchoolsList } from './validation';
import { filterSchoolsByDistance, filterSchoolsByProgram, filterSchoolsByRegion } from './filters';
import { sortByDistance, sortByName, sortByRegion } from './sorting';
import { REGIONS } from './regions';

// Combine all schools
const allSchools = [
  ...NORTHEAST_SCHOOLS,
  ...SOUTHEAST_SCHOOLS,
  ...MIDWEST_SCHOOLS,
  ...SOUTHWEST_SCHOOLS,
  ...WEST_SCHOOLS
];

// Validate the complete list
validateSchoolsList(allSchools);

// Log school count for debugging
console.log('Dental Schools Loaded:', {
  total: allSchools.length,
  byRegion: {
    [REGIONS.NORTHEAST]: NORTHEAST_SCHOOLS.length,
    [REGIONS.SOUTHEAST]: SOUTHEAST_SCHOOLS.length,
    [REGIONS.MIDWEST]: MIDWEST_SCHOOLS.length,
    [REGIONS.SOUTHWEST]: SOUTHWEST_SCHOOLS.length,
    [REGIONS.WEST]: WEST_SCHOOLS.length
  }
});

export const ACCREDITED_SCHOOLS = allSchools;

export {
  filterSchoolsByDistance,
  filterSchoolsByProgram,
  filterSchoolsByRegion,
  sortByDistance,
  sortByName,
  sortByRegion,
  REGIONS
};