// Filter functions for dental schools
export const filterSchoolsByDistance = (schools, coords, radius) => {
  if (!coords || radius === Infinity) {
    return schools;
  }

  return schools.filter(school => school.distance <= radius);
};

export const filterSchoolsByProgram = (schools, program) => {
  if (!program) return schools;
  return schools.filter(school => school.programs.includes(program));
};

export const filterSchoolsByRegion = (schools, region) => {
  if (!region) return schools;
  return schools.filter(school => school.region === region);
};