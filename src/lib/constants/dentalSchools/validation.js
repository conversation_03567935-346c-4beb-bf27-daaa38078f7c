// Validation functions for dental school data
export const validateSchool = (school) => {
  const requiredFields = [
    'id',
    'name',
    'address',
    'coordinates',
    'region',
    'website',
    'phone',
    'programs',
    'clinicInfo'
  ];

  const missingFields = requiredFields.filter(field => !school[field]);
  if (missingFields.length > 0) {
    throw new Error(`School missing required fields: ${missingFields.join(', ')}`);
  }

  if (!school.coordinates.latitude || !school.coordinates.longitude) {
    throw new Error('School coordinates must include latitude and longitude');
  }

  return true;
};

export const validateSchoolsList = (schools) => {
  const ids = new Set();
  
  schools.forEach(school => {
    validateSchool(school);
    
    if (ids.has(school.id)) {
      throw new Error(`Duplicate school ID found: ${school.id}`);
    }
    ids.add(school.id);
  });

  return true;
};