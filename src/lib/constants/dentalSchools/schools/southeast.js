import { REGIONS } from '../regions';

export const SOUTHEAST_SCHOOLS = [
  {
    id: 'alabama-dental',
    name: 'University of Alabama School of Dentistry',
    address: '1919 7th Avenue South, Birmingham, AL 35233',
    coordinates: { latitude: 33.5021, longitude: -86.8065 },
    region: REGIONS.SOUTHEAST,
    website: 'https://www.uab.edu/dentistry',
    phone: '(*************',
    accredited: true,
    programs: ['DMD', 'Advanced Education'],
    clinicInfo: 'Comprehensive dental care at teaching clinics'
  },
  {
    id: 'nova-southeastern',
    name: 'Nova Southeastern University College of Dental Medicine',
    address: '3200 S University Dr, Fort Lauderdale, FL 33328',
    coordinates: { latitude: 26.0798, longitude: -80.2405 },
    region: REGIONS.SOUTHEAST,
    website: 'https://dental.nova.edu',
    phone: '(*************',
    accredited: true,
    programs: ['DMD', 'Advanced Education'],
    clinicInfo: 'Comprehensive dental care at reduced fees'
  },
  {
    id: 'uf-dental',
    name: 'University of Florida College of Dentistry',
    address: '1395 Center Drive, Gainesville, FL 32610',
    coordinates: { latitude: 29.6399, longitude: -82.3428 },
    region: REGIONS.SOUTHEAST,
    website: 'https://dental.ufl.edu',
    phone: '(*************',
    accredited: true,
    programs: ['DMD', 'Advanced Education'],
    clinicInfo: 'Student-provided dental care under faculty supervision'
  },
  {
    id: 'georgia-dental',
    name: 'Dental College of Georgia at Augusta University',
    address: '1430 John Wesley Gilbert Drive, Augusta, GA 30912',
    coordinates: { latitude: 33.4711, longitude: -81.9875 },
    region: REGIONS.SOUTHEAST,
    website: 'https://www.augusta.edu/dentalmedicine',
    phone: '(*************',
    accredited: true,
    programs: ['DMD', 'Advanced Education'],
    clinicInfo: 'Comprehensive dental services at teaching clinics'
  },
  {
    id: 'louisville-dental',
    name: 'University of Louisville School of Dentistry',
    address: '501 S Preston St, Louisville, KY 40202',
    coordinates: { latitude: 38.2506, longitude: -85.7478 },
    region: REGIONS.SOUTHEAST,
    website: 'https://louisville.edu/dentistry',
    phone: '(*************',
    accredited: true,
    programs: ['DMD', 'Advanced Education'],
    clinicInfo: 'Affordable dental care through student clinics'
  },
  {
    id: 'lsu-dental',
    name: 'LSU School of Dentistry',
    address: '1100 Florida Avenue, New Orleans, LA 70119',
    coordinates: { latitude: 29.9711, longitude: -90.0814 },
    region: REGIONS.SOUTHEAST,
    website: 'https://www.lsusd.lsuhsc.edu',
    phone: '(*************',
    accredited: true,
    programs: ['DDS', 'Advanced Education'],
    clinicInfo: 'Quality dental care at reduced costs'
  },
  {
    id: 'unc-dental',
    name: 'UNC Adams School of Dentistry',
    address: '385 S Columbia St, Chapel Hill, NC 27599',
    coordinates: { latitude: 35.9049, longitude: -79.0469 },
    region: REGIONS.SOUTHEAST,
    website: 'https://www.dentistry.unc.edu',
    phone: '(*************',
    accredited: true,
    programs: ['DDS', 'Advanced Education'],
    clinicInfo: 'Patient-centered dental care at teaching clinics'
  },
  {
    id: 'meharry-dental',
    name: 'Meharry Medical College School of Dentistry',
    address: '1005 Dr DB Todd Jr Blvd, Nashville, TN 37208',
    coordinates: { latitude: 36.1665, longitude: -86.8089 },
    region: REGIONS.SOUTHEAST,
    website: 'https://home.mmc.edu/school-of-dentistry',
    phone: '(*************',
    accredited: true,
    programs: ['DDS', 'Advanced Education'],
    clinicInfo: 'Affordable dental care in teaching environment'
  },
  {
    id: 'vcu-dental',
    name: 'VCU School of Dentistry',
    address: '520 N 12th St, Richmond, VA 23298',
    coordinates: { latitude: 37.5483, longitude: -77.4302 },
    region: REGIONS.SOUTHEAST,
    website: 'https://dentistry.vcu.edu',
    phone: '(*************',
    accredited: true,
    programs: ['DDS', 'Advanced Education'],
    clinicInfo: 'Comprehensive dental care at teaching clinics'
  }
];