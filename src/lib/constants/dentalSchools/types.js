// TypeScript types for dental schools
/**
 * @typedef {Object} Coordinates
 * @property {number} latitude
 * @property {number} longitude
 */

/**
 * @typedef {Object} DentalSchool
 * @property {string} id - Unique identifier
 * @property {string} name - School name
 * @property {string} address - Physical address
 * @property {Coordinates} coordinates - Latitude and longitude
 * @property {string} region - Geographic region
 * @property {string} website - School website URL
 * @property {string} phone - Contact phone number
 * @property {boolean} accredited - Accreditation status
 * @property {string[]} programs - Available dental programs
 * @property {string} clinicInfo - Information about student clinics
 * @property {number} [distance] - Distance from search location (optional)
 */

/**
 * @typedef {'Northeast' | 'Southeast' | 'Midwest' | 'Southwest' | 'West'} Region
 */

// Export empty object to satisfy module requirements
export {};