// Sorting functions for dental schools
export const sortByDistance = (schools) => {
  return [...schools].sort((a, b) => {
    if (!a.distance || !b.distance) return 0;
    return a.distance - b.distance;
  });
};

export const sortByName = (schools) => {
  return [...schools].sort((a, b) => a.name.localeCompare(b.name));
};

export const sortByRegion = (schools) => {
  return [...schools].sort((a, b) => a.region.localeCompare(b.region));
};