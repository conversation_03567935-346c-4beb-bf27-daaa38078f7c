// User role definitions
export const USER_ROLES = {
  STUDENT: 'student',
  PROFESSIONAL: 'professional',
  RESEARCHER: 'researcher'
};

// Role-specific content preferences
export const ROLE_PREFERENCES = {
  [USER_ROLES.STUDENT]: {
    defaultDifficulty: 'Beginner',
    recommendedCategories: ['Dental Education', 'Oral Hygiene', 'Preventive Dental Care'],
    contentEmphasis: ['practical', 'educational', 'fundamental']
  },
  [USER_ROLES.PROFESSIONAL]: {
    defaultDifficulty: 'Intermediate',
    recommendedCategories: ['Clinical Research', 'Practice Management', 'Dental Technology'],
    contentEmphasis: ['clinical', 'practical', 'business']
  },
  [USER_ROLES.RESEARCHER]: {
    defaultDifficulty: 'Advanced',
    recommendedCategories: ['Clinical Research', 'Dental Technology', 'Government and Nonprofit Resources'],
    contentEmphasis: ['research', 'scientific', 'analytical']
  }
};

// Progress tracking
export const trackUserProgress = (userId, articleId) => {
  const progress = JSON.parse(localStorage.getItem(`progress_${userId}`) || '{}');
  progress[articleId] = {
    completed: true,
    timestamp: new Date().toISOString(),
    lastPosition: 100 // Percentage of progress
  };
  localStorage.setItem(`progress_${userId}`, JSON.stringify(progress));
};

// Bookmarks management
export const toggleBookmark = (userId, articleId) => {
  const bookmarks = JSON.parse(localStorage.getItem(`bookmarks_${userId}`) || '[]');
  const index = bookmarks.indexOf(articleId);
  
  if (index === -1) {
    bookmarks.push(articleId);
  } else {
    bookmarks.splice(index, 1);
  }
  
  localStorage.setItem(`bookmarks_${userId}`, JSON.stringify(bookmarks));
  return bookmarks;
};

// Reading history
export const addToHistory = (userId, articleId) => {
  const history = JSON.parse(localStorage.getItem(`history_${userId}`) || '[]');
  history.unshift({ articleId, timestamp: new Date().toISOString() });
  
  // Keep only last 50 items
  if (history.length > 50) history.pop();
  
  localStorage.setItem(`history_${userId}`, JSON.stringify(history));
};

// Learning paths
export const createLearningPath = (userId, interests, role) => {
  const preferences = ROLE_PREFERENCES[role];
  return {
    userId,
    interests,
    role,
    difficulty: preferences.defaultDifficulty,
    categories: preferences.recommendedCategories,
    emphasis: preferences.contentEmphasis,
    created: new Date().toISOString()
  };
};

// Personalized recommendations
export const getPersonalizedRecommendations = (userId, role, history = []) => {
  const preferences = ROLE_PREFERENCES[role];
  const userHistory = JSON.parse(localStorage.getItem(`history_${userId}`) || '[]');
  const bookmarks = JSON.parse(localStorage.getItem(`bookmarks_${userId}`) || '[]');
  
  return {
    preferences,
    history: userHistory,
    bookmarks,
    recommendedCategories: preferences.recommendedCategories
  };
}; 