export const AI_MODELS = {
  VOICE_ANALYSIS: {
    id: 'voice-analysis',
    version: '1.0.0',
    type: 'gpt-4',
    parameters: {
      temperature: 0.5,
      max_tokens: 500,
      presence_penalty: 0.1,
      frequency_penalty: 0.1,
    },
    features: {
      frequency_analysis: true,
      amplitude_analysis: true,
      clarity_detection: true,
      breath_pattern_analysis: true
    },
    confidence_thresholds: {
      high: 0.85,
      medium: 0.7,
      low: 0.5
    }
  },

  BREATH_ANALYSIS: {
    id: 'breath-analysis',
    version: '1.2.0',
    type: 'gpt-4',
    parameters: {
      temperature: 0.3,
      max_tokens: 500,
      presence_penalty: 0.2,
      frequency_penalty: 0.2,
    },
    features: {
      vsc_detection: true,
      flow_analysis: true,
      pattern_recognition: true,
      environmental_noise_filtering: true,
      machine_learning: true,
      spectrogram_analysis: true,
      mfcc_extraction: true
    },
    confidence_thresholds: {
      high: 0.9,
      medium: 0.75,
      low: 0.6
    },
    ml_models: {
      mfcc: {
        enabled: true,
        coefficients: 13,
        melFilterBanks: 40
      },
      spectrogram: {
        enabled: true,
        fftSize: 512,
        hopSize: 256
      },
      classification: {
        inhalation_threshold: 0.7,
        exhalation_threshold: 0.65,
        pause_threshold: 0.8
      }
    }
  },

  ORAL_SCAN: {
    id: 'oral-scan',
    version: '1.0.0',
    type: 'gpt-4-vision-preview',
    parameters: {
      temperature: 0.2,
      max_tokens: 500,
      presence_penalty: 0.1,
      frequency_penalty: 0.1,
    },
    features: {
      anomaly_detection: true,
      color_analysis: true,
      texture_analysis: true,
      measurement_extraction: true
    },
    confidence_thresholds: {
      high: 0.9,
      medium: 0.8,
      low: 0.7
    }
  },

  SYMPTOM_CHECKER: {
    id: 'symptom-checker',
    version: '1.0.0',
    type: 'gpt-4',
    parameters: {
      temperature: 0.3,
      max_tokens: 1000,
      presence_penalty: 0.1,
      frequency_penalty: 0.1,
    },
    features: {
      pattern_matching: true,
      severity_assessment: true,
      correlation_analysis: true,
      history_analysis: true
    },
    confidence_thresholds: {
      high: 0.85,
      medium: 0.7,
      low: 0.5
    }
  }
};

export const AI_PROMPTS = {
  VOICE_ANALYSIS: {
    system: `You are a dental health AI expert specializing in voice analysis.
Focus on detecting oral health indicators through voice patterns.
Consider: clarity, resonance, breathiness, and potential oral health issues.
Provide specific, actionable insights and recommendations.`,
    
    analysis: `Analyze the following voice metrics for oral health indicators:
- Clarity: {clarity_score}
- Resonance: {resonance_score}
- Breathiness: {breathiness_score}
- Pattern consistency: {pattern_score}

Consider previous analyses and patient history if available.
Provide detailed insights and specific recommendations.`,
    
    feedback: `Based on the real-time metrics:
- Volume level: {volume}
- Clarity score: {clarity}
- Pattern consistency: {consistency}

Provide clear, actionable guidance for improvement.`
  },

  BREATH_ANALYSIS: {
    system: `You are a dental health AI expert specializing in breath analysis.
Focus on detecting VSCs and other breath-related health indicators.
Consider: VSC levels, breath patterns, and oral health correlations.
Provide specific, actionable insights and recommendations.`,
    
    analysis: `Analyze the following breath metrics:
- VSC levels: {vsc_levels}
- Flow rate: {flow_rate}
- Pattern consistency: {consistency}
- Duration: {duration}

Consider environmental factors and previous analyses.
Provide detailed insights and specific recommendations.`,
    
    feedback: `Based on the real-time metrics:
- VSC detection: {vsc_level}
- Flow quality: {flow_quality}
- Pattern stability: {stability}

Provide clear, actionable guidance for improvement.`
  },

  ORAL_SCAN: {
    system: `You are a dental health AI expert specializing in oral cavity analysis.
Focus on detecting visual indicators of oral health issues.
Consider: color patterns, textures, anomalies, and measurements.
Provide specific, actionable insights and recommendations.`,
    
    analysis: `Analyze the following scan features:
- Color patterns: {color_metrics}
- Texture analysis: {texture_metrics}
- Detected anomalies: {anomalies}
- Key measurements: {measurements}

Consider previous scans and patient history if available.
Provide detailed insights and specific recommendations.`
  },

  SYMPTOM_CHECKER: {
    system: `You are a dental health AI expert specializing in symptom analysis.
Focus on identifying potential oral health conditions.
Consider: symptom patterns, severity, correlations, and patient history.
Provide specific, actionable insights and recommendations.`,
    
    analysis: `Analyze the following symptoms:
{symptoms_list}

Patient history:
{patient_history}

Consider symptom correlations and potential underlying causes.
Provide detailed insights and specific recommendations.`
  }
};

export const CONFIDENCE_SCORING = {
  calculateConfidence: (metrics, thresholds) => {
    const scores = Object.values(metrics).filter(v => typeof v === 'number');
    const avgScore = scores.reduce((a, b) => a + b, 0) / scores.length;
    
    if (avgScore >= thresholds.high) return { level: 'high', score: avgScore };
    if (avgScore >= thresholds.medium) return { level: 'medium', score: avgScore };
    return { level: 'low', score: avgScore };
  },

  getConfidenceColor: (level) => {
    switch (level) {
      case 'high': return 'text-green-400';
      case 'medium': return 'text-blue-400';
      case 'low': return 'text-yellow-400';
      default: return 'text-gray-400';
    }
  }
};

export const FEATURE_WEIGHTS = {
  VOICE_ANALYSIS: {
    clarity: 0.3,
    resonance: 0.25,
    breathiness: 0.25,
    pattern: 0.2
  },

  BREATH_ANALYSIS: {
    vsc_levels: 0.4,
    flow_rate: 0.3,
    consistency: 0.3
  },

  ORAL_SCAN: {
    anomaly_detection: 0.4,
    color_analysis: 0.2,
    texture_analysis: 0.2,
    measurements: 0.2
  },

  SYMPTOM_CHECKER: {
    specificity: 0.4,
    severity: 0.3,
    correlation: 0.3
  }
};

export const REAL_TIME_ANALYSIS = {
  VOICE_ANALYSIS: {
    sampleRate: 44100,
    bufferSize: 4096,
    minDecibels: -90,
    maxDecibels: -10,
    smoothingTimeConstant: 0.85,
    updateInterval: 100, // ms
    features: {
      pitch: {
        minFrequency: 50,
        maxFrequency: 2000,
        confidenceThreshold: 0.8
      },
      volume: {
        threshold: -50,
        range: { min: -90, max: -10 }
      },
      clarity: {
        threshold: 0.7,
        windowSize: 2048
      }
    }
  },
  BREATH_ANALYSIS: {
    sampleRate: 44100,
    bufferSize: 2048,
    sensitivity: 0.85,
    noiseFloor: -90,
    updateInterval: 100, // ms
    features: {
      spectrogram: {
        enabled: true,
        windowSize: 512,
        hopSize: 256
      },
      mfcc: {
        enabled: true,
        coefficients: 13,
        melBands: 40
      },
      zeroCrossing: {
        enabled: true,
        normalizeByLength: true
      },
      vsc: {
        threshold: 0.3,
        confidence_minimum: 0.7
      }
    },
    phaseDetection: {
      inhalation: {
        threshold: 0.7,
        zeroCrossingRate: 0.1,
        spectralCentroid: 0.3
      },
      exhalation: {
        threshold: 0.65,
        zeroCrossingRate: 0.05,
        spectralRolloff: 0.5
      },
      pause: {
        threshold: 0.8,
        zeroCrossingRate: 0.02,
        spectralCentroid: 0.2
      }
    }
  }
};

export const ERROR_HANDLING = {
  retryAttempts: 3,
  retryDelay: 1000, // ms
  fallbackStrategies: {
    VOICE_ANALYSIS: {
      noMicrophone: 'REQUEST_PERMISSION',
      lowVolume: 'INCREASE_GAIN',
      highNoise: 'NOISE_REDUCTION',
      processingError: 'USE_CACHED_MODEL'
    },
    BREATH_ANALYSIS: {
      sensorError: 'RECALIBRATE',
      lowSignal: 'INCREASE_SENSITIVITY',
      interference: 'FILTER_NOISE',
      processingError: 'USE_CACHED_MODEL'
    }
  },
  recoveryActions: {
    REQUEST_PERMISSION: async () => {
      return navigator.mediaDevices.getUserMedia({ audio: true });
    },
    INCREASE_GAIN: (gainNode, amount = 1.5) => {
      gainNode.gain.value = Math.min(gainNode.gain.value * amount, 5.0);
    },
    NOISE_REDUCTION: async (audioContext, stream) => {
      const denoise = await audioContext.audioWorklet.addModule('/denoise-processor.js');
      return new AudioWorkletNode(audioContext, 'denoise-processor');
    },
    USE_CACHED_MODEL: async (modelType) => {
      const cache = await caches.open('ai-models');
      return cache.match(`/models/${modelType}`);
    }
  }
};

export const ENHANCED_CONFIDENCE_SCORING = {
  calculateWeightedConfidence: (metrics, features, thresholds) => {
    const weights = FEATURE_WEIGHTS[Object.keys(FEATURE_WEIGHTS).find(key => 
      JSON.stringify(features) === JSON.stringify(AI_MODELS[key].features))];
    
    if (!weights) return CONFIDENCE_SCORING.calculateConfidence(metrics, thresholds);
    
    let weightedScore = 0;
    let totalWeight = 0;
    
    Object.entries(metrics).forEach(([key, value]) => {
      if (typeof value === 'number' && weights[key]) {
        weightedScore += value * weights[key];
        totalWeight += weights[key];
      }
    });
    
    const avgScore = totalWeight > 0 ? weightedScore / totalWeight : 0;
    
    if (avgScore >= thresholds.high) return { level: 'high', score: avgScore };
    if (avgScore >= thresholds.medium) return { level: 'medium', score: avgScore };
    return { level: 'low', score: avgScore };
  },

  getConfidenceColor: (level) => {
    switch (level) {
      case 'high': return 'text-green-400';
      case 'medium': return 'text-blue-400';
      case 'low': return 'text-yellow-400';
      default: return 'text-gray-400';
    }
  }
};

export const ML_CONFIDENCE_SCORING = {
  calculateProbabilisticConfidence: (metrics, features, thresholds) => {
    // Base score using weighted calculation
    const baseScore = ENHANCED_CONFIDENCE_SCORING.calculateWeightedConfidence(
      metrics, features, thresholds
    );
    
    // Apply statistical confidence interval
    const samples = metrics.samples || [];
    if (samples.length > 1) {
      const variance = calculateVariance(samples);
      const standardError = Math.sqrt(variance / samples.length);
      const tValue = getTValue(0.95, samples.length - 1); // 95% confidence
      
      // Adjust confidence based on statistical significance
      const margin = tValue * standardError;
      const confidenceInterval = {
        lower: Math.max(0, baseScore.score - margin),
        upper: Math.min(1, baseScore.score + margin)
      };
      
      // Final confidence score with uncertainty adjustment
      return {
        level: baseScore.level,
        score: baseScore.score,
        interval: confidenceInterval,
        uncertainty: margin
      };
    }
    
    return baseScore;
  },
  
  getFeatureImportance: (metrics, weights) => {
    const importance = {};
    let maxValue = 0;
    
    Object.entries(metrics).forEach(([key, value]) => {
      if (typeof value === 'number' && weights[key]) {
        importance[key] = value * weights[key];
        maxValue = Math.max(maxValue, importance[key]);
      }
    });
    
    // Normalize importance scores
    if (maxValue > 0) {
      Object.keys(importance).forEach(key => {
        importance[key] = importance[key] / maxValue;
      });
    }
    
    return importance;
  }
};

// Helper functions for confidence calculations
function calculateVariance(samples) {
  const mean = samples.reduce((a, b) => a + b, 0) / samples.length;
  const squareDiffs = samples.map(x => Math.pow(x - mean, 2));
  return squareDiffs.reduce((a, b) => a + b, 0) / samples.length;
}

function getTValue(confidence, degreesOfFreedom) {
  // Simplified t-value lookup for common confidence levels
  const tTable = {
    0.95: {
      1: 12.706, 5: 2.571, 10: 2.228, 20: 2.086, 30: 2.042, 50: 2.009, 100: 1.984
    }
  };
  
  const confidenceValues = tTable[confidence];
  const dfValues = Object.keys(confidenceValues).map(Number);
  const closestDf = dfValues.reduce((prev, curr) => 
    Math.abs(curr - degreesOfFreedom) < Math.abs(prev - degreesOfFreedom) ? curr : prev
  );
  
  return confidenceValues[closestDf];
} 