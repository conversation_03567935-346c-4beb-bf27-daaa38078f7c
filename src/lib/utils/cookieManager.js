import Cookies from 'js-cookie';

// Cookie categories
export const COOKIE_CATEGORIES = {
  NECESSARY: 'necessary', // Required for the website to function
  PREFERENCES: 'preferences', // Remember user preferences
  ANALYTICS: 'analytics', // Track website usage
  MARKETING: 'marketing', // Used for marketing/advertising
  THIRD_PARTY: 'third_party' // From external sources like social media
};

// Default cookie settings
export const DEFAULT_COOKIE_SETTINGS = {
  path: '/', // Available across the entire site
  expires: 365, // 1 year
  sameSite: 'Lax', // Works with most first-party contexts
  secure: window.location.protocol === 'https:' // Secure if on HTTPS
};

// Cookie consent status (for the main consent cookie)
export const CONSENT_STATUS = {
  ACCEPTED: 'accepted',
  DECLINED: 'declined',
  CUSTOMIZED: 'customized',
  PENDING: 'pending'
};

// Cookie consent configuration
const COOKIE_CONSENT_CONFIG = {
  consentCookieName: 'smilo_cookie_consent',
  consentStatusCookieName: 'smilo_cookie_consent_status',
  consentDateCookieName: 'smilo_cookie_consent_date',
  consentVersionCookieName: 'smilo_cookie_consent_version',
  categoryCookieNames: {
    [COOKIE_CATEGORIES.NECESSARY]: 'smilo_cookies_necessary',
    [COOKIE_CATEGORIES.PREFERENCES]: 'smilo_cookies_preferences',
    [COOKIE_CATEGORIES.ANALYTICS]: 'smilo_cookies_analytics',
    [COOKIE_CATEGORIES.MARKETING]: 'smilo_cookies_marketing',
    [COOKIE_CATEGORIES.THIRD_PARTY]: 'smilo_cookies_third_party'
  },
  // The current version of our cookie policy - increment when policy changes
  currentVersion: '1.0.0'
};

/**
 * Check if consent has been given
 */
export const hasConsentBeenGiven = () => {
  const consentCookie = Cookies.get(COOKIE_CONSENT_CONFIG.consentCookieName);
  const localStorageConsent = localStorage.getItem(COOKIE_CONSENT_CONFIG.consentCookieName);
  
  if (consentCookie === 'true' || localStorageConsent === 'true') {
    return true;
  }
  
  return false;
};

/**
 * Check if a specific cookie category is allowed
 */
export const isCategoryAllowed = (category) => {
  // Necessary cookies are always allowed
  if (category === COOKIE_CATEGORIES.NECESSARY) {
    return true;
  }
  
  // Check if we have overall consent
  const consentStatus = Cookies.get(COOKIE_CONSENT_CONFIG.consentStatusCookieName);
  
  // If full consent was given, all categories are allowed
  if (consentStatus === CONSENT_STATUS.ACCEPTED) {
    return true;
  }
  
  // If custom consent was given, check the specific category
  if (consentStatus === CONSENT_STATUS.CUSTOMIZED) {
    const categoryCookieName = COOKIE_CONSENT_CONFIG.categoryCookieNames[category];
    return Cookies.get(categoryCookieName) === 'true';
  }
  
  // No consent or declined consent
  return false;
};

/**
 * Get the current consent status and preferences
 */
export const getConsentPreferences = () => {
  const consentStatus = Cookies.get(COOKIE_CONSENT_CONFIG.consentStatusCookieName) || CONSENT_STATUS.PENDING;
  
  const preferences = {
    status: consentStatus,
    version: Cookies.get(COOKIE_CONSENT_CONFIG.consentVersionCookieName) || null,
    date: Cookies.get(COOKIE_CONSENT_CONFIG.consentDateCookieName) || null,
    categories: {}
  };
  
  // Get status for each category
  Object.entries(COOKIE_CATEGORIES).forEach(([key, category]) => {
    const cookieName = COOKIE_CONSENT_CONFIG.categoryCookieNames[category];
    
    // Necessary cookies are always allowed
    if (category === COOKIE_CATEGORIES.NECESSARY) {
      preferences.categories[category] = true;
    } else if (consentStatus === CONSENT_STATUS.ACCEPTED) {
      // If full consent was given, all categories are allowed
      preferences.categories[category] = true;
    } else if (consentStatus === CONSENT_STATUS.CUSTOMIZED) {
      // If custom consent was given, check individual preferences
      preferences.categories[category] = Cookies.get(cookieName) === 'true';
    } else {
      // No consent or declined
      preferences.categories[category] = false;
    }
  });
  
  return preferences;
};

/**
 * Save full consent for all cookie categories
 */
export const saveFullConsent = () => {
  const now = new Date().toISOString();
  
  // Set main consent cookies
  Cookies.set(COOKIE_CONSENT_CONFIG.consentCookieName, 'true', DEFAULT_COOKIE_SETTINGS);
  Cookies.set(COOKIE_CONSENT_CONFIG.consentStatusCookieName, CONSENT_STATUS.ACCEPTED, DEFAULT_COOKIE_SETTINGS);
  Cookies.set(COOKIE_CONSENT_CONFIG.consentDateCookieName, now, DEFAULT_COOKIE_SETTINGS);
  Cookies.set(COOKIE_CONSENT_CONFIG.consentVersionCookieName, COOKIE_CONSENT_CONFIG.currentVersion, DEFAULT_COOKIE_SETTINGS);
  
  // Set backup in localStorage
  localStorage.setItem(COOKIE_CONSENT_CONFIG.consentCookieName, 'true');
  localStorage.setItem(COOKIE_CONSENT_CONFIG.consentStatusCookieName, CONSENT_STATUS.ACCEPTED);
  localStorage.setItem(COOKIE_CONSENT_CONFIG.consentDateCookieName, now);
  localStorage.setItem(COOKIE_CONSENT_CONFIG.consentVersionCookieName, COOKIE_CONSENT_CONFIG.currentVersion);
  
  // Allow all cookie categories
  Object.values(COOKIE_CATEGORIES).forEach(category => {
    Cookies.set(COOKIE_CONSENT_CONFIG.categoryCookieNames[category], 'true', DEFAULT_COOKIE_SETTINGS);
  });
  
  // Enable necessary cookies that should be set right after consent
  enableFunctionalCookies();
  
  // Return the saved preferences
  return getConsentPreferences();
};

/**
 * Save custom consent for specific cookie categories
 */
export const saveCustomConsent = (categoryPreferences) => {
  const now = new Date().toISOString();
  
  // Set main consent cookies
  Cookies.set(COOKIE_CONSENT_CONFIG.consentCookieName, 'true', DEFAULT_COOKIE_SETTINGS);
  Cookies.set(COOKIE_CONSENT_CONFIG.consentStatusCookieName, CONSENT_STATUS.CUSTOMIZED, DEFAULT_COOKIE_SETTINGS);
  Cookies.set(COOKIE_CONSENT_CONFIG.consentDateCookieName, now, DEFAULT_COOKIE_SETTINGS);
  Cookies.set(COOKIE_CONSENT_CONFIG.consentVersionCookieName, COOKIE_CONSENT_CONFIG.currentVersion, DEFAULT_COOKIE_SETTINGS);
  
  // Set backup in localStorage
  localStorage.setItem(COOKIE_CONSENT_CONFIG.consentCookieName, 'true');
  localStorage.setItem(COOKIE_CONSENT_CONFIG.consentStatusCookieName, CONSENT_STATUS.CUSTOMIZED);
  localStorage.setItem(COOKIE_CONSENT_CONFIG.consentDateCookieName, now);
  localStorage.setItem(COOKIE_CONSENT_CONFIG.consentVersionCookieName, COOKIE_CONSENT_CONFIG.currentVersion);
  
  // Set preferences for each category
  Object.entries(categoryPreferences).forEach(([category, isAllowed]) => {
    // Force necessary cookies to be allowed
    if (category === COOKIE_CATEGORIES.NECESSARY) {
      Cookies.set(COOKIE_CONSENT_CONFIG.categoryCookieNames[category], 'true', DEFAULT_COOKIE_SETTINGS);
    } else {
      Cookies.set(COOKIE_CONSENT_CONFIG.categoryCookieNames[category], isAllowed ? 'true' : 'false', DEFAULT_COOKIE_SETTINGS);
    }
  });
  
  // Always enable necessary cookies
  enableFunctionalCookies();
  
  // Return the saved preferences
  return getConsentPreferences();
};

/**
 * Save decline for all non-necessary cookies
 */
export const saveDeclineConsent = () => {
  const now = new Date().toISOString();
  
  // Set main consent cookies
  Cookies.set(COOKIE_CONSENT_CONFIG.consentCookieName, 'true', DEFAULT_COOKIE_SETTINGS);
  Cookies.set(COOKIE_CONSENT_CONFIG.consentStatusCookieName, CONSENT_STATUS.DECLINED, DEFAULT_COOKIE_SETTINGS);
  Cookies.set(COOKIE_CONSENT_CONFIG.consentDateCookieName, now, DEFAULT_COOKIE_SETTINGS);
  Cookies.set(COOKIE_CONSENT_CONFIG.consentVersionCookieName, COOKIE_CONSENT_CONFIG.currentVersion, DEFAULT_COOKIE_SETTINGS);
  
  // Set backup in localStorage
  localStorage.setItem(COOKIE_CONSENT_CONFIG.consentCookieName, 'true');
  localStorage.setItem(COOKIE_CONSENT_CONFIG.consentStatusCookieName, CONSENT_STATUS.DECLINED);
  localStorage.setItem(COOKIE_CONSENT_CONFIG.consentDateCookieName, now);
  localStorage.setItem(COOKIE_CONSENT_CONFIG.consentVersionCookieName, COOKIE_CONSENT_CONFIG.currentVersion);
  
  // Set only necessary cookies as allowed
  Object.values(COOKIE_CATEGORIES).forEach(category => {
    if (category === COOKIE_CATEGORIES.NECESSARY) {
      Cookies.set(COOKIE_CONSENT_CONFIG.categoryCookieNames[category], 'true', DEFAULT_COOKIE_SETTINGS);
    } else {
      Cookies.set(COOKIE_CONSENT_CONFIG.categoryCookieNames[category], 'false', DEFAULT_COOKIE_SETTINGS);
    }
  });
  
  // Only enable necessary cookies
  enableFunctionalCookies();
  
  // Return the saved preferences
  return getConsentPreferences();
};

/**
 * Remove all non-necessary cookies
 */
export const removeNonEssentialCookies = () => {
  // Get all cookies
  const allCookies = document.cookie.split(';');
  
  // List of necessary cookies that should not be removed
  const necessaryCookies = [
    COOKIE_CONSENT_CONFIG.consentCookieName,
    COOKIE_CONSENT_CONFIG.consentStatusCookieName,
    COOKIE_CONSENT_CONFIG.consentDateCookieName,
    COOKIE_CONSENT_CONFIG.consentVersionCookieName,
    COOKIE_CONSENT_CONFIG.categoryCookieNames[COOKIE_CATEGORIES.NECESSARY]
  ];
  
  // Remove all cookies except necessary ones
  allCookies.forEach(cookie => {
    const cookieName = cookie.split('=')[0].trim();
    
    if (!necessaryCookies.includes(cookieName)) {
      Cookies.remove(cookieName, { path: '/' });
    }
  });
};

/**
 * Enable core functional cookies
 */
const enableFunctionalCookies = () => {
  // Set cookies required for the website to function properly
  Cookies.set('functional-cookies-enabled', 'true', DEFAULT_COOKIE_SETTINGS);
  
  // Session management cookie
  Cookies.set('session-id', generateSessionId(), { 
    ...DEFAULT_COOKIE_SETTINGS,
    // Session cookie (expires when browser closes)
    expires: undefined 
  });
};

/**
 * Generate a session ID
 */
const generateSessionId = () => {
  return 'session-' + Math.random().toString(36).substring(2, 15);
};

/**
 * Check if consent needs to be renewed (e.g., policy version changed)
 */
export const isConsentRenewalNeeded = () => {
  const savedVersion = Cookies.get(COOKIE_CONSENT_CONFIG.consentVersionCookieName);
  
  // Renewal needed if no version is saved or version is different
  return !savedVersion || savedVersion !== COOKIE_CONSENT_CONFIG.currentVersion;
};

/**
 * Get cookie banner text based on locale
 */
export const getCookieBannerText = (locale = 'en') => {
  const texts = {
    en: {
      title: 'We use cookies for a better experience',
      description: 'We use cookies to remember your preferences and enhance your experience. By clicking "Accept All", you consent to the use of all cookies. Click "Customize" to choose which cookies you accept.',
      acceptAll: 'Accept All',
      decline: 'Decline All',
      customize: 'Customize',
      privacyPolicy: 'Privacy Policy',
      categories: {
        [COOKIE_CATEGORIES.NECESSARY]: {
          title: 'Necessary',
          description: 'These cookies are essential for the website to function properly. They cannot be disabled.'
        },
        [COOKIE_CATEGORIES.PREFERENCES]: {
          title: 'Preferences',
          description: 'These cookies allow us to remember your preferences and provide enhanced features.'
        },
        [COOKIE_CATEGORIES.ANALYTICS]: {
          title: 'Analytics',
          description: 'These cookies help us understand how visitors interact with the website.'
        },
        [COOKIE_CATEGORIES.MARKETING]: {
          title: 'Marketing',
          description: 'These cookies are used to deliver relevant ads and marketing campaigns.'
        },
        [COOKIE_CATEGORIES.THIRD_PARTY]: {
          title: 'Third Party',
          description: 'These cookies are set by third party services like social media platforms.'
        }
      }
    }
  };
  
  return texts[locale] || texts.en;
};

export default {
  hasConsentBeenGiven,
  isCategoryAllowed,
  getConsentPreferences,
  saveFullConsent,
  saveCustomConsent,
  saveDeclineConsent,
  removeNonEssentialCookies,
  isConsentRenewalNeeded,
  getCookieBannerText,
  COOKIE_CATEGORIES,
  CONSENT_STATUS
}; 