// Network utility functions
export const isOnline = () => {
  if (typeof navigator !== 'undefined' && 'onLine' in navigator) {
    return navigator.onLine;
  }
  return true; // Assume online if we can't detect
};

export const checkConnection = async (url = 'https://www.google.com/favicon.ico') => {
  try {
    const response = await fetch(url, {
      mode: 'no-cors',
      cache: 'no-store'
    });
    return true;
  } catch (error) {
    return false;
  }
};

export const waitForOnline = () => {
  return new Promise(resolve => {
    if (isOnline()) {
      resolve();
      return;
    }

    const checkOnline = () => {
      if (isOnline()) {
        window.removeEventListener('online', checkOnline);
        resolve();
      }
    };

    window.addEventListener('online', checkOnline);
  });
};