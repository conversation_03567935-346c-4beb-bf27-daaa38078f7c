// Input validation utilities - CommonJS version

// Image validation
const validateImage = (file) => {
  if (!file) return null;

  const maxSize = 5 * 1024 * 1024; // 5MB
  if (file.size > maxSize) {
    throw new Error('Image size must be less than 5MB');
  }

  const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg'];
  if (!allowedTypes.includes(file.type)) {
    throw new Error('Only JPEG, PNG, and JPG images are supported');
  }

  // Additional image validation
  if (file instanceof File && !file.type.startsWith('image/')) {
    throw new Error('Invalid image file type');
  }

  return file;
};

// Question validation
const validateQuestion = (text) => {
  if (!text?.trim()) {
    throw new Error('Please enter a question');
  }
  if (text.length > 500) {
    throw new Error('Question is too long. Please keep it under 500 characters.');
  }
  return text.trim();
};

// API key validation
const validateApiKey = (key) => {
  console.log("Validating API key:", key ? `${key.substring(0, 5)}...` : "undefined");
  if (!key) {
    console.error("API key is missing");
    throw new Error('OpenAI API key is missing');
  }

  if (!key.startsWith('sk-')) {
    console.error("API key has invalid format. Should start with 'sk-'");
    throw new Error('OpenAI API key has an invalid format');
  }

  console.log("API key is valid");
  return key;
};

// Email validation
const validateEmail = (email) => {
  if (!email || typeof email !== 'string') {
    return false;
  }
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Password validation
const validatePassword = (password) => {
  // At least 8 characters, 1 uppercase, 1 number, 1 special character
  if (!password || typeof password !== 'string') {
    return false;
  }
  const passwordRegex = /^(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])[a-zA-Z0-9!@#$%^&*]{8,64}$/;
  return passwordRegex.test(password);
};

// Username validation
const validateUsername = (username) => {
  // 3-20 characters, letters, numbers, underscores, hyphens
  if (!username || typeof username !== 'string') {
    return false;
  }
  const usernameRegex = /^[a-zA-Z][a-zA-Z0-9_-]{2,19}$/;
  return usernameRegex.test(username);
};

// Export all validations
module.exports = {
  validateImage,
  validateQuestion,
  validateApiKey,
  validateEmail,
  validatePassword,
  validateUsername
};