import { config } from '../config';

export const checkApiKey = () => {
  const apiKey = config.openai.apiKey;
  
  if (!apiKey) {
    console.error('OpenAI API key is missing. Please add it to your .env file as VITE_OPENAI_API_KEY=your-key-here');
    return {
      isValid: false,
      message: 'API key is missing'
    };
  }
  
  // Check if the API key has the correct format (starts with 'sk-' or 'sk-proj-')
  if (!apiKey.startsWith('sk-')) {
    console.error('OpenAI API key has an invalid format. It should start with "sk-"');
    return {
      isValid: false,
      message: 'API key has invalid format'
    };
  }
  
  return {
    isValid: true,
    message: 'API key is valid'
  };
};
