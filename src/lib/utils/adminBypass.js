/**
 * Utility functions for admin authentication bypass
 * UPDATED: Removed admin bypass to enforce strict credential checking
 */

/**
 * Checks if the current user should bypass authentication steps
 * This has been disabled to enforce proper credential checking
 * @returns {boolean} Always returns false
 */
export function shouldBypassAuth() {
  // Disabled to enforce strict credential checking
  return false;
}

/**
 * This function has been disabled to enforce strict credential checking
 * @param {Object} defaultResponse - Default response object to return
 * @returns {Object} Just returns the default response
 */
export function createAdminBypassResponse(defaultResponse = null) {
  // Disabled to enforce strict credential checking
  return defaultResponse;
} 