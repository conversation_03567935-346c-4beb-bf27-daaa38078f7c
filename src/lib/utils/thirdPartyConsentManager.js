import cookieManager, { COOKIE_CATEGORIES } from './cookieManager';

/**
 * List of third-party services we might integrate with
 */
export const THIRD_PARTY_SERVICES = {
  GOOGLE_ANALYTICS: 'google_analytics',
  FACEBOOK_PIXEL: 'facebook_pixel',
  INTERCOM: 'intercom',
  HOT<PERSON><PERSON>: 'hotjar',
  GOOGLE_TAG_MANAGER: 'google_tag_manager',
  YOUTUBE: 'youtube',
  TWITTER: 'twitter',
  LINKEDIN: 'linkedin'
};

/**
 * Map services to their required cookie categories
 */
const SERVICE_CATEGORY_MAP = {
  [THIRD_PARTY_SERVICES.GOOGLE_ANALYTICS]: COOKIE_CATEGORIES.ANALYTICS,
  [THIRD_PARTY_SERVICES.FACEBOOK_PIXEL]: COOKIE_CATEGORIES.MARKETING,
  [THIRD_PARTY_SERVICES.INTERCOM]: COOKIE_CATEGORIES.PREFERENCES,
  [THIRD_PARTY_SERVICES.HOTJAR]: COOKIE_CATEGORIES.ANALYTICS,
  [THIRD_PARTY_SERVICES.GOOGLE_TAG_MANAGER]: COOKIE_CATEGORIES.ANALYTICS,
  [THIRD_PARTY_SERVICES.YOUTUBE]: COOKIE_CATEGORIES.THIRD_PARTY,
  [THIRD_PARTY_SERVICES.TWITTER]: COOKIE_CATEGORIES.THIRD_PARTY,
  [THIRD_PARTY_SERVICES.LINKEDIN]: COOKIE_CATEGORIES.MARKETING
};

/**
 * Check if a third-party service is allowed based on cookie preferences
 * @param {string} service - The service to check
 * @returns {boolean} - Whether the service is allowed
 */
export const isServiceAllowed = (service) => {
  const category = SERVICE_CATEGORY_MAP[service];
  if (!category) {
    console.warn(`Unknown service: ${service}`);
    return false;
  }
  
  return cookieManager.isCategoryAllowed(category);
};

/**
 * Load Google Analytics if analytics cookies are allowed
 * @param {string} measurementId - Google Analytics measurement ID
 */
export const loadGoogleAnalytics = (measurementId) => {
  if (!measurementId) {
    return;
  }

  if (!isServiceAllowed(THIRD_PARTY_SERVICES.GOOGLE_ANALYTICS)) {
    console.log('Google Analytics blocked by cookie preferences');
    return;
  }
  
  // Create script elements
  const gtagScript = document.createElement('script');
  gtagScript.async = true;
  gtagScript.src = `https://www.googletagmanager.com/gtag/js?id=${measurementId}`;
  
  const inlineScript = document.createElement('script');
  inlineScript.innerHTML = `
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());
    gtag('config', '${measurementId}', { 'anonymize_ip': true });
  `;
  
  // Add to document
  document.head.appendChild(gtagScript);
  document.head.appendChild(inlineScript);
  
  console.log('Google Analytics loaded');
};

/**
 * Load Google Tag Manager if analytics cookies are allowed
 * @param {string} containerId - GTM container ID
 */
export const loadGoogleTagManager = (containerId) => {
  if (!containerId) {
    return;
  }

  if (!isServiceAllowed(THIRD_PARTY_SERVICES.GOOGLE_TAG_MANAGER)) {
    console.log('Google Tag Manager blocked by cookie preferences');
    return;
  }
  
  // Create script element for GTM
  const script = document.createElement('script');
  script.innerHTML = `
    (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','${containerId}');
  `;
  
  // Add noscript iframe for browsers with JavaScript disabled
  const noscript = document.createElement('noscript');
  const iframe = document.createElement('iframe');
  iframe.src = `https://www.googletagmanager.com/ns.html?id=${containerId}`;
  iframe.height = '0';
  iframe.width = '0';
  iframe.style.display = 'none';
  iframe.style.visibility = 'hidden';
  noscript.appendChild(iframe);
  
  // Add to document
  document.head.appendChild(script);
  document.body.appendChild(noscript);
  
  console.log('Google Tag Manager loaded');
};

/**
 * Load Facebook Pixel if marketing cookies are allowed
 * @param {string} pixelId - Facebook Pixel ID
 */
export const loadFacebookPixel = (pixelId) => {
  if (!pixelId) {
    return;
  }

  if (!isServiceAllowed(THIRD_PARTY_SERVICES.FACEBOOK_PIXEL)) {
    console.log('Facebook Pixel blocked by cookie preferences');
    return;
  }
  
  // Create script element
  const script = document.createElement('script');
  script.innerHTML = `
    !function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?
    n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;
    n.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;
    t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,
    document,'script','https://connect.facebook.net/en_US/fbevents.js');
    fbq('init', '${pixelId}');
    fbq('track', 'PageView');
  `;
  
  // Add noscript pixel for browsers with JavaScript disabled
  const noscript = document.createElement('noscript');
  const img = document.createElement('img');
  img.height = '1';
  img.width = '1';
  img.style.display = 'none';
  img.src = `https://www.facebook.com/tr?id=${pixelId}&ev=PageView&noscript=1`;
  noscript.appendChild(img);
  
  // Add to document
  document.head.appendChild(script);
  document.body.appendChild(noscript);
  
  console.log('Facebook Pixel loaded');
};

/**
 * Initialize all allowed third-party services
 * @param {Object} config - Configuration object with service IDs
 */
export const initializeThirdPartyServices = (config = {}) => {
  if (typeof cookieManager === 'undefined') {
    console.error('Cookie manager not available - cannot initialize third-party services');
    return;
  }

  // Default export for compat with App.jsx
  const thirdPartyManager = {
    initializeThirdPartyServices
  };

  try {
    const { 
      googleAnalyticsId,
      googleTagManagerId,
      facebookPixelId
    } = config;
    
    // Only load services that are configured
    if (googleAnalyticsId) {
      loadGoogleAnalytics(googleAnalyticsId);
    }
    
    if (googleTagManagerId) {
      loadGoogleTagManager(googleTagManagerId);
    }
    
    if (facebookPixelId) {
      loadFacebookPixel(facebookPixelId);
    }
  } catch (error) {
    console.error('Error initializing third-party services:', error);
  }

  return thirdPartyManager;
};

// Export default object
export default {
  isServiceAllowed,
  loadGoogleAnalytics,
  loadGoogleTagManager,
  loadFacebookPixel,
  initializeThirdPartyServices
}; 