// Error handling utilities
export const handleApiError = (error) => {
  // Handle network errors first
  if (error?.message === 'Failed to fetch' || error?.message?.includes('fetch')) {
    // Check if it's a Supabase error
    if (error?.url?.includes('supabase')) {
      console.error('Supabase connection error:', error);
      return 'Connection error. Please check your network and try again.';
    }
    // Check if it's an OpenAI error
    if (error?.url?.includes('openai')) {
      console.error('OpenAI connection error:', error);
      return 'Unable to connect to AI service. Please try again later.';
    }
    // Generic network error
    console.error('Network error:', error);
    return 'Network connection error. Please check your connection and try again.';
  }

  // Handle JSON parsing errors
  if (error?.message?.includes('JSON')) {
    console.error('JSON parsing error:', error);
    return 'Error processing server response. Please try again.';
  }

  // Handle Supabase errors
  if (error?.code === '23505') {
    if (error.message.includes('username')) {
      return 'Username already taken. Please choose another.';
    }
    if (error.message.includes('email')) {
      return 'This email is already registered. Please sign in instead.';
    }
    return 'A duplicate entry was found. Please try again with different values.';
  }

  if (error?.code === 'PGRST116') {
    // Not an error, just means no results found
    return null;
  }
  if (error?.code === 'PGRST301') {
    console.error('Supabase PGRST301 error:', error);
    return 'Connection error. Please try again in a moment.';
  }
  if (error?.code === 'PGRST204') {
    console.error('Supabase PGRST204 error:', error);
    return 'No data found.';
  }
  if (error?.code === 'PGRST302') {
    console.error('Supabase PGRST302 error:', error);
    return 'Authentication error. Please sign in again.';
  }
  if (error?.code === '42501') {
    console.error('Supabase 42501 error:', error);
    return 'Permission denied. Please check your access rights.';
  }

  // Handle DeepSeek specific errors
  if (error?.message?.includes('DeepSeek')) {
    return 'Secondary AI service error. Please try again.';
  }

  // Handle OpenAI specific errors
  if (error?.message?.includes('openai is not defined')) {
    console.error('OpenAI initialization error:', error);
    return 'AI service initialization error. Please refresh the page and try again.';
  }

  if (error?.message?.includes('API key')) {
    return 'API configuration error. Please check your settings.';
  } else if (error?.response?.status === 401) {
    return 'Authentication failed. Please check the API key.';
  }

  // Handle image upload errors
  if (error?.message?.includes('image')) {
    return error.message;
  }

  // Handle rate limits
  if (error?.response?.status === 429) {
    return 'Rate limit exceeded. Please try again in a moment.';
  }

  // Handle server errors
  if (error?.response?.status === 500) {
    return 'Service is temporarily unavailable. Please try again later.';
  }

  // Handle geolocation errors
  if (error?.code === 1) {
    return 'Location access denied. Showing default schools.';
  }
  if (error?.code === 2) {
    return 'Location unavailable. Showing default schools.';
  }
  if (error?.code === 3) {
    return 'Location request timed out. Showing default schools.';
  }

  // Handle initialization errors
  if (error?.message?.includes('configuration')) {
    return 'Service configuration error. Please try again later.';
  }

  // Log unexpected errors with more detail
  console.error('API Error:', {
    message: error?.message,
    status: error?.response?.status,
    data: error?.response?.data,
    source: error?.message?.includes('DeepSeek') ? 'DeepSeek' :
            error?.message?.includes('supabase') ? 'Supabase' : 'OpenAI'
  });

  // Return user-friendly message
  return error?.message || 'An unexpected error occurred. Please try again.';
};

export const handleOpenAIError = (error) => {
  if (error?.message?.includes('openai is not defined')) {
    return new Error('AI service initialization error. Please refresh the page and try again.');
  }

  if (error?.response?.status === 401) {
    return new Error('Invalid API key. Please check your configuration.');
  }
  if (error?.response?.status === 429) {
    return new Error('Rate limit exceeded. Please try again later.');
  }
  if (error?.response?.status === 500) {
    return new Error('AI service is temporarily unavailable.');
  }

  return new Error('An error occurred while processing your request.');
};

// Centralized error handling object
export const errorHandler = {
  api: handleApiError,
  openai: handleOpenAIError
};

/**
 * Handles errors from API calls and returns user-friendly error messages
 * @param {Error} error - The error object
 * @returns {string} A user-friendly error message
 */
export function handleError(error) {
  console.error('Error details:', error);

  // Check if it's an OpenAI API error
  if (error.message?.includes('openai is not defined')) {
    return 'OpenAI client initialization failed. Please check your API key configuration.';
  }

  // Check if it's an API key error
  if (error.message?.includes('API key')) {
    return 'Invalid API key. Please check your OpenAI API key configuration.';
  }

  // Check if it's a rate limit error
  if (error.message?.includes('rate limit') || error.message?.includes('429')) {
    return 'Rate limit exceeded. Please try again in a few moments.';
  }

  // Check if it's a network error
  if (error.message?.includes('network') || error.message?.includes('ECONNREFUSED') || error.message?.includes('fetch')) {
    return 'Network error. Please check your internet connection and try again.';
  }

  // Check if it's a timeout error
  if (error.message?.includes('timeout') || error.message?.includes('ETIMEDOUT')) {
    return 'Request timed out. Please try again later.';
  }

  // Check if it's a model error
  if (error.message?.includes('model')) {
    return 'The AI model is currently unavailable. Please try again later.';
  }

  // Check if it's an image processing error
  if (error.message?.includes('image') || error.message?.includes('base64')) {
    return 'There was an error processing your image. Please try a different image or format.';
  }

  // Default error message
  return error.message || 'An unexpected error occurred. Please try again.';
}