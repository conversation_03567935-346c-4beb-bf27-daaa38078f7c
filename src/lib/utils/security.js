/**
 * Security utilities for input validation and sanitization
 */

/**
 * Sanitizes HTML string to prevent XSS attacks
 * @param {string} input - String that might contain HTML
 * @returns {string} - Sanitized string
 */
export const sanitizeHtml = (input) => {
  if (!input || typeof input !== 'string') return '';
  
  // Replace HTML tags and entities that could be used for XSS
  return input
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;')
    .replace(/`/g, '&#x60;')
    .replace(/\$/g, '&#x24;')
    .replace(/{/g, '&#x7B;')
    .replace(/}/g, '&#x7D;');
};

/**
 * Sanitizes user input to prevent XSS in database or API calls
 * @param {string} input - User input string
 * @returns {string} - Sanitized input string
 */
export const sanitizeInput = (input) => {
  if (!input || typeof input !== 'string') return '';
  
  // Replace problematic characters in user input
  return input.trim()
    .replace(/[<>]/g, '') // Remove angle brackets
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/data:/gi, '') // Remove data: protocol 
    .replace(/on\w+=/gi, ''); // Remove event handlers
};

/**
 * Validate and sanitize search query to prevent SQL injection and XSS
 * @param {string} query - Search query string
 * @returns {string} - Sanitized query
 */
export const sanitizeSearchQuery = (query) => {
  if (!query || typeof query !== 'string') return '';
  
  // Remove SQL injection patterns and sanitize
  return query.trim()
    .replace(/['";]/g, '') // Remove quotes and semicolons
    .replace(/\-\-/g, '') // Remove SQL comments
    .replace(/\/\*/g, '') // Remove block comment start
    .replace(/\*\//g, '') // Remove block comment end
    .replace(/union\s+select/gi, '') // Remove UNION SELECT
    .replace(/exec\s*\(/gi, '') // Remove EXEC(
    .replace(/drop\s+table/gi, ''); // Remove DROP TABLE
};

/**
 * Sanitize filenames to prevent path traversal attacks
 * @param {string} filename - Filename to sanitize
 * @returns {string} - Sanitized filename
 */
export const sanitizeFilename = (filename) => {
  if (!filename || typeof filename !== 'string') return '';
  
  // Remove path traversal patterns and dangerous characters
  return filename
    .replace(/\.\.\//g, '') // Remove directory traversal
    .replace(/\\/g, '') // Remove backslashes
    .replace(/\//g, '') // Remove forward slashes
    .replace(/[&<>:"/\\|?*]/g, '_') // Replace unsafe filename chars with underscore
    .replace(/\s+/g, '_'); // Replace spaces with underscores
};

/**
 * Sanitize object keys and values recursively
 * @param {Object} obj - Object to sanitize 
 * @returns {Object} - Sanitized object
 */
export const sanitizeObject = (obj) => {
  if (!obj || typeof obj !== 'object') return obj;
  
  // Handle arrays
  if (Array.isArray(obj)) {
    return obj.map(item => sanitizeObject(item));
  }
  
  // Process object
  const result = {};
  for (const [key, value] of Object.entries(obj)) {
    // Skip sensitive keys
    if (['password', 'token', 'secret', 'api_key', 'apiKey'].includes(key)) {
      continue;
    }
    
    // Recursive sanitization
    if (typeof value === 'object' && value !== null) {
      result[key] = sanitizeObject(value);
    } else if (typeof value === 'string') {
      result[key] = sanitizeInput(value);
    } else {
      result[key] = value;
    }
  }
  
  return result;
};

/**
 * Validate and sanitize URL to prevent open redirects
 * @param {string} url - URL to validate
 * @param {Array} allowedDomains - List of allowed domains
 * @returns {string|null} - Sanitized URL or null if invalid
 */
export const validateAndSanitizeUrl = (url, allowedDomains = []) => {
  if (!url || typeof url !== 'string') return null;
  
  try {
    const urlObj = new URL(url);
    
    // Check if URL uses http or https protocol
    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      return null;
    }
    
    // If allowedDomains specified, validate against them
    if (allowedDomains.length > 0) {
      const hostname = urlObj.hostname;
      const isAllowed = allowedDomains.some(domain => 
        hostname === domain || hostname.endsWith(`.${domain}`)
      );
      
      if (!isAllowed) return null;
    }
    
    // Return the sanitized URL
    return urlObj.toString();
  } catch (e) {
    // Invalid URL format
    return null;
  }
}; 