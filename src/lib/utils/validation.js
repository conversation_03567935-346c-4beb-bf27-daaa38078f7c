// Input validation utilities - ES Module version for production build

// Image validation 
export function validateImage(file) {
  if (!file) return null;

  const maxSize = 5 * 1024 * 1024; // 5MB
  if (file.size > maxSize) {
    throw new Error('Image size must be less than 5MB');
  }

  const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg'];
  if (!allowedTypes.includes(file.type)) {
    throw new Error('Only JPEG, PNG, and JPG images are supported');
  }

  // Additional image validation
  if (file instanceof File && !file.type.startsWith('image/')) {
    throw new Error('Invalid image file type');
  }

  return file;
}

// Question validation
export function validateQuestion(text) {
  if (!text?.trim()) {
    throw new Error('Please enter a question');
  }
  if (text.length > 500) {
    throw new Error('Question is too long. Please keep it under 500 characters.');
  }
  return text.trim();
}

// API key validation
export function validate<PERSON>pi<PERSON>ey(key) {
  if (!key) {
    throw new Error('OpenAI API key is missing');
  }

  if (!key.startsWith('sk-')) {
    throw new Error('OpenAI API key has an invalid format');
  }

  return key;
}

// Email validation
export function validateEmail(email) {
  if (!email || typeof email !== 'string') {
    return false;
  }
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Password validation
export function validatePassword(password) {
  if (!password || typeof password !== 'string') {
    return false;
  }
  const passwordRegex = /^(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])[a-zA-Z0-9!@#$%^&*]{8,64}$/;
  return passwordRegex.test(password);
}

// Username validation
export function validateUsername(username) {
  if (!username || typeof username !== 'string') {
    return false;
  }
  const usernameRegex = /^[a-zA-Z][a-zA-Z0-9_-]{2,19}$/;
  return usernameRegex.test(username);
}

// Export as default object
const validationUtils = {
  validateImage,
  validateQuestion,
  validateApiKey,
  validateEmail,
  validatePassword,
  validateUsername
};

export default validationUtils;