/**
 * Utility function to proxy external images through allowed domains
 * to comply with Content Security Policy
 */

/**
 * Proxies an image URL through images.weserv.nl to comply with CSP
 * @param {string} url - Original image URL
 * @returns {string} - Proxied image URL that complies with CSP
 */
export const proxyImage = (url) => {
  if (!url) return '';
  
  // If URL is already using an allowed domain, return it as is
  if (
    url.startsWith('data:') || 
    url.startsWith('blob:') ||
    url.includes('.googleapis.com') ||
    url.includes('.gstatic.com') ||
    url.includes('twskhrwvdrebsghyczlu.supabase.co') ||
    url.includes('images.weserv.nl')
  ) {
    return url;
  }
  
  // Encode the URL to be used as a parameter
  const encodedUrl = encodeURIComponent(url);
  
  // Return a proxied URL through images.weserv.nl
  return `https://images.weserv.nl/?url=${encodedUrl}&default=placeholder`;
};

export default proxyImage; 