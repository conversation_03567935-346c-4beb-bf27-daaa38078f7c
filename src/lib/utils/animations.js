/**
 * Animation variants and utility classes for UI components
 */

// Fade in animation - slower and more subtle for mobile
export const fadeIn = 'opacity-0 animate-[fadeIn_0.8s_ease-out_forwards] motion-safe:animate-[fadeIn_0.8s_ease-out_forwards]';

// Fade in down animation (element starts above and fades in while moving down) - reduced movement
export const fadeInDown = 'opacity-0 -translate-y-3 animate-[fadeInDown_0.8s_ease-out_forwards] motion-safe:animate-[fadeInDown_0.8s_ease-out_forwards]';

// Scale in animation (element grows from small to full size) - more subtle scaling
export const scaleIn = 'opacity-0 scale-98 animate-[scaleIn_0.6s_ease-out_forwards] motion-safe:animate-[scaleIn_0.6s_ease-out_forwards]';

// Pulse animation for subtle attention - much slower for better performance
export const pulseAnimation = 'animate-pulse-slow motion-safe:animate-pulse-slow';

// Float animation for a gentle up and down motion - disabled on mobile
export const floatAnimation = 'motion-safe:animate-float';

// Glow animation for text and UI elements - disabled on mobile
export const glowAnimation = 'motion-safe:animate-glow';

// Continuous movement animation variants - all motion-safe to disable on reduced motion
export const continuousMovement = {
  floating: 'motion-safe:animate-float',
  pulse: 'motion-safe:animate-pulse-slow',
  glow: 'motion-safe:animate-glow',
  shimmer: 'motion-safe:animate-shimmer'
};

// Framer motion variants for staggered children animations - optimized for mobile
export const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.05, // Reduced stagger time
      delayChildren: 0.1     // Reduced delay
    }
  }
};

export const staggerItem = {
  hidden: { y: 10, opacity: 0 }, // Reduced movement distance
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      type: "tween",          // Use tween instead of spring for better performance
      duration: 0.4,         // Fixed duration instead of spring physics
      ease: "easeOut"        // Simple easing function
    }
  }
};

// Gradient animations
export const gradientShift = 'bg-gradient-to-r from-blue-400 via-indigo-300 to-purple-400 bg-size-200 animate-gradient-x bg-clip-text text-transparent';

// Page transition variants
export const pageTransitionVariants = {
  initial: { opacity: 0, y: 20 },
  enter: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -20 }
};

// Button press animation - simplified for mobile
export const buttonPress = 'active:scale-98 transition-transform duration-150';

// Card hover animation - simplified shadow and transform for mobile
export const cardHover = 'transition-transform duration-200 hover:-translate-y-0.5 hover:shadow-md';

// Nav link hover animation - simplified for mobile
export const navLinkHover = 'relative after:absolute after:bottom-0 after:left-0 after:h-0.5 after:w-0 hover:after:w-full after:transition-all after:duration-200 after:bg-blue-500';

// Gradient text
export const gradientText = 'bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-indigo-300 to-purple-400';

// Animated gradient background
export const animatedGradientBg = 'bg-gradient-to-r from-indigo-900 via-purple-900 to-indigo-900 bg-size-200 animate-gradient-x';

// Shimmer effect (loading state)
export const shimmerEffect = 'relative overflow-hidden before:absolute before:inset-0 before:-translate-x-full before:animate-shimmer before:bg-gradient-to-r before:from-transparent before:via-white/10 before:to-transparent';

// Legacy animation utility constants
export const scaleOnHover = "transition-transform duration-500 hover:scale-105";
export const pulseOnHover = "transition-all duration-500 hover:shadow-lg hover:shadow-blue-500/20";

// Section animation variants
export const sectionVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: [0.61, 1, 0.88, 1]
    }
  }
};

// Card animation variants
export const cardVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.4,
      ease: [0.61, 1, 0.88, 1]
    }
  }
};

// Animation utilities for the app
// We use these constants to reference our animations consistently

// Data attributes for parallax effects
export const parallaxSlow = "data-parallax='slow'";
export const parallaxMedium = "data-parallax='medium'";
export const parallaxFast = "data-parallax='fast'";

// For subtle pulse effects - slower and more subtle
export const pulseSoft = {
  '0%, 100%': { opacity: 0.8 },
  '50%': { opacity: 1 },
};

// For glow hover effects - simplified for mobile
export const glowOnHover = {
  '0%': { boxShadow: '0 0 2px rgba(120, 120, 255, 0)' },
  '100%': { boxShadow: '0 0 5px rgba(120, 120, 255, 0.3)' },
};

// For items fading in and moving up - reduced movement
export const fadeInUp = {
  '0%': { opacity: 0, transform: 'translateY(10px)' },
  '100%': { opacity: 1, transform: 'translateY(0)' },
};

// For items moving subtly in a floating motion - reduced movement
export const float = {
  '0%, 100%': { transform: 'translateY(0)' },
  '50%': { transform: 'translateY(-5px)' },
};

// For slower pulse effects used in backgrounds and glows - much slower
export const pulseSlow = {
  '0%, 100%': { opacity: 0.5 },
  '50%': { opacity: 0.7 },
};

// Export any animations that might be used elsewhere in the app
// but aren't defined in tailwind config