/**
 * Enhanced mobile optimizations and device detection utilities
 * Provides real-time performance monitoring and adaptive optimizations
 */

import { isMobileDevice, isLowEndDevice, isSafariBrowser, isIOSDevice } from '../../utils/deviceDetection';

// Performance monitoring state
let performanceMonitor = null;
let lastFrameTime = performance.now();
let frameCount = 0;
let averageFPS = 60;
let isMonitoring = false;

/**
 * Apply comprehensive mobile and performance optimizations
 * @returns {Object} Device information for debugging
 */
export const applyMobileOptimizations = () => {
  if (typeof window === 'undefined') return {};

  const deviceInfo = {
    isMobile: isMobileDevice(),
    isLowEnd: isLowEndDevice(),
    isSafari: isSafariBrowser(),
    isIOS: isIOSDevice(),
    deviceMemory: navigator.deviceMemory || 'unknown',
    hardwareConcurrency: navigator.hardwareConcurrency || 'unknown',
    connectionType: navigator.connection?.effectiveType || 'unknown',
    viewport: {
      width: window.innerWidth,
      height: window.innerHeight,
      pixelRatio: window.devicePixelRatio || 1
    }
  };

  // Apply device-specific CSS classes
  const html = document.documentElement;
  
  // Remove existing classes to avoid conflicts
  html.classList.remove(
    'is-mobile', 'is-low-end-device', 'is-safari', 'is-ios',
    'reduced-animations', 'optimize-performance', 'minimal-mode'
  );

  // Apply device detection classes
  if (deviceInfo.isMobile) html.classList.add('is-mobile');
  if (deviceInfo.isLowEnd) html.classList.add('is-low-end-device');
  if (deviceInfo.isSafari) html.classList.add('is-safari');
  if (deviceInfo.isIOS) html.classList.add('is-ios');

  // Apply performance optimizations based on device capabilities
  if (deviceInfo.isLowEnd) {
    html.classList.add('reduced-animations', 'optimize-performance');
    
    // Very aggressive optimizations for extremely low-end devices
    if (isVeryLowEndDevice()) {
      html.classList.add('minimal-mode');
      disableHeavyFeatures();
    }
  }

  // Safari-specific optimizations
  if (deviceInfo.isSafari) {
    applySafariOptimizations();
  }

  // Mobile-specific optimizations
  if (deviceInfo.isMobile) {
    applyMobileSpecificOptimizations();
  }

  // Network-based optimizations
  applyNetworkOptimizations();

  // Initialize viewport fixes
  initViewportFix();

  console.log('Device optimizations applied:', deviceInfo);
  return deviceInfo;
};

/**
 * Detect very low-end devices requiring aggressive optimizations
 * @returns {boolean} True if device needs aggressive optimizations
 */
export const isVeryLowEndDevice = () => {
  if (typeof navigator === 'undefined') return false;

  const veryLimitedMemory = navigator.deviceMemory && navigator.deviceMemory <= 2;
  const veryLimitedCPU = navigator.hardwareConcurrency && navigator.hardwareConcurrency <= 2;
  const verySlowConnection = navigator.connection && 
    (navigator.connection.saveData || ['slow-2g', '2g'].includes(navigator.connection.effectiveType));
  
  // Additional performance indicators
  const lowPixelRatio = window.devicePixelRatio && window.devicePixelRatio < 1.5;
  const smallViewport = window.innerWidth <= 480;

  return veryLimitedMemory || veryLimitedCPU || verySlowConnection || (lowPixelRatio && smallViewport);
};

/**
 * Apply Safari-specific performance optimizations
 */
const applySafariOptimizations = () => {
  const html = document.documentElement;
  
  // Safari-specific performance classes
  html.classList.add('safari-optimized');
  
  // Disable some heavy features that cause issues in Safari
  html.classList.add('reduce-backdrop-filters');
  
  // Apply iOS-specific fixes if needed
  if (isIOSDevice()) {
    html.classList.add('ios-optimized');
    // iOS Safari viewport fixes
    fixIOSViewport();
  }
};

/**
 * Apply mobile-specific performance optimizations
 */
const applyMobileSpecificOptimizations = () => {
  // Optimize touch interactions
  document.body.style.touchAction = 'manipulation';
  
  // Prevent zoom on input focus (iOS)
  const metaViewport = document.querySelector('meta[name="viewport"]');
  if (metaViewport) {
    metaViewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
  }

  // Optimize scrolling
  document.body.style.overscrollBehavior = 'none';
  
  // Reduce motion for better performance
  if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
    document.documentElement.classList.add('reduced-motion');
  }
};

/**
 * Apply network-based optimizations
 */
const applyNetworkOptimizations = () => {
  if (!navigator.connection) return;

  const connection = navigator.connection;
  const isSlowConnection = ['slow-2g', '2g', '3g'].includes(connection.effectiveType);
  
  if (isSlowConnection || connection.saveData) {
    document.documentElement.classList.add('slow-connection');
    
    // Disable autoplay videos and heavy animations
    disableAutoplayMedia();
    
    // Enable data saver mode
    enableDataSaverMode();
  }
};

/**
 * Disable heavy features for very low-end devices
 */
const disableHeavyFeatures = () => {
  // Disable all animations
  const style = document.createElement('style');
  style.textContent = `
    *, *::before, *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
  `;
  document.head.appendChild(style);
  
  // Disable backdrop filters
  document.documentElement.classList.add('no-backdrop-filters');
};

/**
 * Fix iOS viewport issues
 */
const fixIOSViewport = () => {
  // iOS viewport height fix
  const setVH = () => {
    const vh = window.innerHeight * 0.01;
    document.documentElement.style.setProperty('--vh', `${vh}px`);
  };
  
  setVH();
  window.addEventListener('resize', setVH);
  window.addEventListener('orientationchange', () => {
    setTimeout(setVH, 100);
  });
};

/**
 * Initialize viewport fix for all devices
 */
const initViewportFix = () => {
  // Universal viewport height fix
  const updateVH = () => {
    const vh = window.innerHeight * 0.01;
    document.documentElement.style.setProperty('--vh', `${vh}px`);
  };
  
  updateVH();
  window.addEventListener('resize', updateVH);
  
  // Throttled resize handler for performance
  let resizeTimeout;
  window.addEventListener('resize', () => {
    clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(updateVH, 100);
  });
};

/**
 * Disable autoplay media for slow connections
 */
const disableAutoplayMedia = () => {
  const videos = document.querySelectorAll('video[autoplay]');
  videos.forEach(video => {
    video.removeAttribute('autoplay');
    video.preload = 'none';
  });
};

/**
 * Enable data saver mode optimizations
 */
const enableDataSaverMode = () => {
  document.documentElement.classList.add('data-saver-mode');
  
  // Lazy load all images
  const images = document.querySelectorAll('img:not([loading])');
  images.forEach(img => {
    img.loading = 'lazy';
  });
};

/**
 * Start performance monitoring and adaptive optimizations
 * @param {Function} callback Function to call when performance changes
 */
export const startPerformanceMonitoring = (callback) => {
  if (isMonitoring || typeof window === 'undefined') return;
  
  isMonitoring = true;
  let frameIndex = 0;
  const frameTimes = new Array(60).fill(0);
  
  const measurePerformance = (currentTime) => {
    frameCount++;
    
    // Calculate FPS over last 60 frames
    const deltaTime = currentTime - lastFrameTime;
    frameTimes[frameIndex] = deltaTime;
    frameIndex = (frameIndex + 1) % 60;
    
    if (frameCount % 60 === 0) {
      const averageFrameTime = frameTimes.reduce((a, b) => a + b, 0) / 60;
      averageFPS = 1000 / averageFrameTime;
      
      // Adaptive optimizations based on performance
      if (averageFPS < 30) {
        // Very poor performance - enable aggressive optimizations
        document.documentElement.classList.add('reduced-animations', 'optimize-performance');
        callback?.({ type: 'performance', level: 'aggressive', fps: averageFPS, needsOptimization: true });
      } else if (averageFPS < 45) {
        // Moderate performance issues - enable moderate optimizations
        document.documentElement.classList.add('reduced-animations');
        callback?.({ type: 'performance', level: 'moderate', fps: averageFPS, needsOptimization: true });
      } else {
        // Good performance - remove performance restrictions
        document.documentElement.classList.remove('reduced-animations', 'optimize-performance');
        callback?.({ type: 'performance', level: 'good', fps: averageFPS, needsOptimization: false });
      }
    }
    
    lastFrameTime = currentTime;
    
    if (isMonitoring) {
      requestAnimationFrame(measurePerformance);
    }
  };
  
  requestAnimationFrame(measurePerformance);
  
  return () => {
    isMonitoring = false;
  };
};

/**
 * Stop performance monitoring
 */
export const stopPerformanceMonitoring = () => {
  isMonitoring = false;
};

/**
 * Get current performance metrics
 * @returns {Object} Current performance data
 */
export const getPerformanceMetrics = () => {
  return {
    fps: averageFPS,
    frameCount,
    isMonitoring,
    memoryUsage: performance.memory ? {
      used: performance.memory.usedJSHeapSize,
      total: performance.memory.totalJSHeapSize,
      limit: performance.memory.jsHeapSizeLimit
    } : null
  };
};

export default {
  applyMobileOptimizations,
  startPerformanceMonitoring,
  stopPerformanceMonitoring,
  getPerformanceMetrics,
  isVeryLowEndDevice
}; 