import { config } from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { fetchAndStoreArticles } from '../services/articleFetchService.js';
import { supabase } from '../supabaseClient.js';

// Load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = dirname(dirname(dirname(__dirname)));
config({ path: join(rootDir, '.env') });

const FETCH_INTERVAL = 60 * 60 * 1000; // 1 hour in milliseconds
const MAX_ARTICLES = 1000;

// Function to clean up old articles
const cleanupOldArticles = async () => {
  try {
    // Get total count
    const { count } = await supabase
      .from('dental_articles')
      .select('*', { count: 'exact', head: true });
      
    if (count <= MAX_ARTICLES) {
      return { deleted: 0, remaining: count };
    }
    
    // Get IDs of oldest articles to delete
    const toDelete = count - MAX_ARTICLES;
    const { data: oldestArticles } = await supabase
      .from('dental_articles')
      .select('id')
      .order('inserted_at', { ascending: true })
      .limit(toDelete);
      
    // Delete oldest articles
    const ids = oldestArticles.map(article => article.id);
    await supabase
      .from('dental_articles')
      .delete()
      .in('id', ids);
    
    return {
      deleted: ids.length,
      remaining: count - ids.length
    };
  } catch (error) {
    console.error('Error in cleanupOldArticles:', error);
    await supabase
      .from('system_logs')
      .insert({
        component: 'article_cleanup',
        level: 'error',
        message: error.message
      });
  }
};

// Main worker function
const runWorker = async () => {
  try {
    console.log('Article worker starting...');
    
    // Fetch and store articles
    const fetchResults = await fetchAndStoreArticles();
    console.log('Fetch results:', fetchResults);
    
    // Clean up old articles if needed
    const cleanupResults = await cleanupOldArticles();
    console.log('Cleanup results:', cleanupResults);
    
    // Log success
    await supabase
      .from('system_logs')
      .insert({
        component: 'article_worker',
        level: 'info',
        message: 'Worker completed successfully',
        metadata: { fetchResults, cleanupResults }
      });
      
  } catch (error) {
    console.error('Worker error:', error);
    await supabase
      .from('system_logs')
      .insert({
        component: 'article_worker',
        level: 'error',
        message: error.message
      });
  }
};

// Export the worker function that sets up the interval
export const articleWorker = () => {
  runWorker(); // Run immediately on start
  return setInterval(runWorker, FETCH_INTERVAL); // Then run every hour
}; 