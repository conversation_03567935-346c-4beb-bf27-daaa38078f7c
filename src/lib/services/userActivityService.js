import { supabase } from '../supabase';

/**
 * Tracks user activity including searches, clicks, and other interactions
 * @param {Object} activity - Activity data to track
 * @param {string} activity.type - Type of activity (search, click, view, etc.)
 * @param {string} activity.action - Specific action performed
 * @param {Object} activity.details - Additional details about the activity
 * @returns {Promise<Object>} - Result of the tracking operation
 */
export const trackUserActivity = async (activity) => {
  try {
    const user = supabase.auth.user();
    
    if (!user) {
      console.log('User not authenticated, activity not tracked');
      return { success: false, error: 'User not authenticated' };
    }
    
    const { data, error } = await supabase
      .from('user_activity')
      .insert({
        user_id: user.id,
        activity_type: activity.type,
        activity_action: activity.action,
        activity_details: activity.details,
        timestamp: new Date().toISOString()
      });
      
    if (error) {
      console.error('Error tracking user activity:', error);
      return { success: false, error };
    }
    
    return { success: true, data };
  } catch (error) {
    console.error('Error in trackUserActivity:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Tracks user search activity
 * @param {string} query - The search query
 * @param {string} searchType - Type of search (location, dental school, etc.)
 * @param {Object} [details={}] - Additional details about the search
 * @returns {Promise<Object>} - Result of the tracking operation
 */
export const trackSearchActivity = async (query, searchType, details = {}) => {
  return trackUserActivity({
    type: 'search',
    action: searchType,
    details: {
      query,
      ...details
    }
  });
};

/**
 * Tracks user location activity
 * @param {Object} coordinates - User coordinates
 * @param {string} method - How location was obtained (gps, zip, manual)
 * @returns {Promise<Object>} - Result of the tracking operation
 */
export const trackLocationActivity = async (coordinates, method) => {
  return trackUserActivity({
    type: 'location',
    action: method,
    details: {
      coordinates
    }
  });
};

/**
 * Gets user activity history
 * @param {string} [userId] - User ID (defaults to current user)
 * @param {Object} [options] - Query options
 * @param {string} [options.type] - Filter by activity type
 * @param {number} [options.limit=50] - Maximum number of records to return
 * @returns {Promise<Array>} - User activity history
 */
export const getUserActivityHistory = async (userId, options = {}) => {
  try {
    const user = userId || supabase.auth.user()?.id;
    
    if (!user) {
      console.log('User not authenticated, cannot retrieve history');
      return [];
    }
    
    let query = supabase
      .from('user_activity')
      .select('*')
      .eq('user_id', user)
      .order('timestamp', { ascending: false });
    
    if (options.type) {
      query = query.eq('activity_type', options.type);
    }
    
    if (options.limit) {
      query = query.limit(options.limit);
    } else {
      query = query.limit(50); // Default limit
    }
    
    const { data, error } = await query;
    
    if (error) {
      console.error('Error getting user activity history:', error);
      return [];
    }
    
    return data || [];
  } catch (error) {
    console.error('Error in getUserActivityHistory:', error);
    return [];
  }
}; 