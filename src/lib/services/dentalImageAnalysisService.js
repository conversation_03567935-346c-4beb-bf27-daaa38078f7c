import { supabase } from '../supabase';
import { config } from '../config';
import { fetchADAGuidelines, searchDentalImages } from './dentalDatabaseService';

// Types of dental images we can analyze
export const ANALYSIS_TYPES = {
  RADIOGRAPHIC: 'radiographic',
  INTRAORAL: 'intraoral',
  EXTRAORAL: 'extraoral',
  CBCT: 'cbct',
  PANORAMIC: 'panoramic'
};

// Confidence thresholds for findings
export const CONFIDENCE_THRESHOLDS = {
  HIGH: 0.85,
  MEDIUM: 0.7,
  LOW: 0.5
};

/**
 * Analyze a dental image and return findings
 * @param {string} imageUrl - URL of the image to analyze
 * @param {string} imageType - Type of dental image from ANALYSIS_TYPES
 * @param {Array} focusAreas - Optional specific areas to focus on
 * @returns {Promise<Object>} - Analysis results
 */
export const analyzeDentalImage = async (imageUrl, imageType, focusAreas = []) => {
  try {
    // Check if image has already been analyzed
    const { data: cachedAnalysis } = await supabase
      .from('image_analysis_cache')
      .select('*')
      .eq('image_hash', generateImageHash(imageUrl))
      .order('created_at', { ascending: false })
      .limit(1);
    
    // Return cached analysis if found
    if (cachedAnalysis && cachedAnalysis.length > 0) {
      console.log('Using cached image analysis');
      return cachedAnalysis[0].analysis_results;
    }
    
    // In a real implementation, this would call an AI service
    // For demonstration, we'll generate sample analysis results
    const analysisResults = generateSampleAnalysis(imageType, focusAreas);
    
    // Cache the analysis results
    await supabase
      .from('image_analysis_cache')
      .insert({
        image_hash: generateImageHash(imageUrl),
        image_type: imageType,
        analysis_results: analysisResults,
        created_at: new Date().toISOString()
      });
    
    return analysisResults;
  } catch (error) {
    console.error('Error analyzing dental image:', error);
    return {
      error: 'Failed to analyze dental image',
      details: error.message
    };
  }
};

/**
 * Compare two dental images to detect changes
 * @param {string} baselineImageUrl - URL of the baseline image
 * @param {string} comparisonImageUrl - URL of the image to compare
 * @param {string} imageType - Type of dental image from ANALYSIS_TYPES
 * @returns {Promise<Object>} - Comparison results
 */
export const compareDentalImages = async (baselineImageUrl, comparisonImageUrl, imageType) => {
  try {
    // Analyze both images
    const [baselineAnalysis, comparisonAnalysis] = await Promise.all([
      analyzeDentalImage(baselineImageUrl, imageType),
      analyzeDentalImage(comparisonImageUrl, imageType)
    ]);
    
    // In a real implementation, this would intelligently compare the analyses
    // For demonstration, we'll generate sample comparison results
    
    const changes = [];
    
    // Compare findings from both analyses
    baselineAnalysis.findings.forEach(baselineFinding => {
      const matchingFinding = comparisonAnalysis.findings.find(
        f => f.area === baselineFinding.area
      );
      
      if (matchingFinding) {
        // If matching finding exists, check for changes
        if (matchingFinding.severity !== baselineFinding.severity) {
          changes.push({
            area: baselineFinding.area,
            type: 'severity_change',
            from: baselineFinding.severity,
            to: matchingFinding.severity,
            description: `Change in severity for ${baselineFinding.condition} in ${baselineFinding.area}`,
            significance: matchingFinding.severity > baselineFinding.severity ? 'worsened' : 'improved'
          });
        }
      } else {
        // If no matching finding, it might have resolved
        changes.push({
          area: baselineFinding.area,
          type: 'resolved_finding',
          description: `${baselineFinding.condition} in ${baselineFinding.area} is no longer present`,
          significance: 'improved'
        });
      }
    });
    
    // Check for new findings
    comparisonAnalysis.findings.forEach(comparisonFinding => {
      const existsInBaseline = baselineAnalysis.findings.some(
        f => f.area === comparisonFinding.area && f.condition === comparisonFinding.condition
      );
      
      if (!existsInBaseline) {
        changes.push({
          area: comparisonFinding.area,
          type: 'new_finding',
          description: `New ${comparisonFinding.condition} detected in ${comparisonFinding.area}`,
          severity: comparisonFinding.severity,
          significance: 'worsened'
        });
      }
    });
    
    return {
      baselineDate: baselineAnalysis.analyzedAt,
      comparisonDate: comparisonAnalysis.analyzedAt,
      imageType,
      changes,
      summary: changes.length > 0 
        ? `Detected ${changes.length} changes between images` 
        : 'No significant changes detected between images',
      overallProgression: determineOverallProgression(changes),
      comparisonId: generateComparisonId(),
      comparedAt: new Date().toISOString()
    };
  } catch (error) {
    console.error('Error comparing dental images:', error);
    return {
      error: 'Failed to compare dental images',
      details: error.message
    };
  }
};

/**
 * Find similar cases from the dental image database
 * @param {Object} analysisResults - Analysis results to find similar cases for
 * @param {number} limit - Maximum number of similar cases to return
 * @returns {Promise<Array>} - Similar cases
 */
export const findSimilarCases = async (analysisResults, limit = 5) => {
  try {
    // In a real implementation, this would search a database of past cases
    // For demonstration, we'll generate sample similar cases
    
    const similarCases = [];
    
    // Generate sample similar cases based on findings
    for (let i = 0; i < limit; i++) {
      const matchingFindings = analysisResults.findings.filter(() => Math.random() > 0.3);
      const matchScore = 0.5 + (Math.random() * 0.5); // Score between 0.5 and 1.0
      
      similarCases.push({
        caseId: `CASE-${Math.random().toString(36).substring(2, 10).toUpperCase()}`,
        patientAge: 20 + Math.floor(Math.random() * 50), // Age between 20-70
        patientGender: Math.random() > 0.5 ? 'Male' : 'Female',
        matchScore,
        matchingFindings: matchingFindings.map(finding => ({
          condition: finding.condition,
          area: finding.area,
          similarity: 0.7 + (Math.random() * 0.3) // Similarity between 0.7 and 1.0
        })),
        treatmentApproach: generateSampleTreatmentApproach(matchingFindings),
        outcomeDescription: Math.random() > 0.3 ? 'Successful resolution with complete healing' : 'Partial improvement requiring follow-up care',
        timeToResolution: `${1 + Math.floor(Math.random() * 12)} months`,
        anonymized: true
      });
    }
    
    // Sort by match score (highest first)
    return similarCases.sort((a, b) => b.matchScore - a.matchScore);
  } catch (error) {
    console.error('Error finding similar cases:', error);
    return {
      error: 'Failed to find similar cases',
      details: error.message
    };
  }
};

/**
 * Get treatment recommendations based on analysis results
 * @param {Object} analysisResults - Analysis results to get recommendations for
 * @returns {Promise<Object>} - Treatment recommendations
 */
export const getTreatmentRecommendations = async (analysisResults) => {
  try {
    // In a real implementation, this would use a knowledge base of treatment guidelines
    // For demonstration, we'll generate sample recommendations
    
    const recommendations = [];
    const urgentFindings = analysisResults.findings.filter(f => f.severity > 0.7);
    const moderateFindings = analysisResults.findings.filter(f => f.severity > 0.4 && f.severity <= 0.7);
    const minorFindings = analysisResults.findings.filter(f => f.severity <= 0.4);
    
    // Add recommendations for urgent findings
    urgentFindings.forEach(finding => {
      recommendations.push({
        condition: finding.condition,
        area: finding.area,
        urgency: 'High - requires prompt attention',
        recommendedActions: generateRecommendedActions(finding, 'urgent'),
        timeframe: 'Within 1-2 weeks',
        guidelineReference: 'ADA Clinical Practice Guidelines (2022)',
        confidence: 0.85 + (Math.random() * 0.15) // Between 0.85 and 1.0
      });
    });
    
    // Add recommendations for moderate findings
    moderateFindings.forEach(finding => {
      recommendations.push({
        condition: finding.condition,
        area: finding.area,
        urgency: 'Moderate - requires attention',
        recommendedActions: generateRecommendedActions(finding, 'moderate'),
        timeframe: 'Within 1-2 months',
        guidelineReference: 'ADA Clinical Practice Guidelines (2022)',
        confidence: 0.7 + (Math.random() * 0.15) // Between 0.7 and 0.85
      });
    });
    
    // Add recommendations for minor findings
    minorFindings.forEach(finding => {
      recommendations.push({
        condition: finding.condition,
        area: finding.area,
        urgency: 'Low - routine monitoring recommended',
        recommendedActions: generateRecommendedActions(finding, 'minor'),
        timeframe: 'At next routine visit',
        guidelineReference: 'ADA Clinical Practice Guidelines (2022)',
        confidence: 0.5 + (Math.random() * 0.2) // Between 0.5 and 0.7
      });
    });
    
    // Add preventive recommendations
    recommendations.push({
      condition: 'Preventive care',
      area: 'Full mouth',
      urgency: 'Routine',
      recommendedActions: [
        'Regular hygiene visits every 6 months',
        'Daily flossing and brushing with fluoride toothpaste',
        'Consider antimicrobial mouth rinse if indicated'
      ],
      timeframe: 'Ongoing',
      guidelineReference: 'ADA Preventive Care Guidelines (2022)',
      confidence: 0.95
    });
    
    return {
      patientId: analysisResults.patientId || 'ANONYMOUS',
      imageType: analysisResults.imageType,
      recommendations,
      overallAssessment: determineOverallAssessment(analysisResults.findings),
      disclaimers: [
        'These recommendations are generated based on image analysis and should be reviewed by a qualified dental professional.',
        'Additional clinical examination may reveal conditions not visible in the analyzed images.',
        'Treatment plan should be customized based on patient\'s complete health history, preferences, and financial considerations.'
      ],
      generatedAt: new Date().toISOString(),
      validFor: '30 days'
    };
  } catch (error) {
    console.error('Error getting treatment recommendations:', error);
    return {
      error: 'Failed to get treatment recommendations',
      details: error.message
    };
  }
};

/**
 * Initialize the image analysis cache table
 */
export const initImageAnalysisCache = async () => {
  try {
    const { error } = await supabase.rpc('create_table_if_not_exists', {
      table_name: 'image_analysis_cache',
      columns: `
        id uuid primary key default uuid_generate_v4(),
        image_hash text not null,
        image_type text not null,
        analysis_results jsonb not null,
        created_at timestamp with time zone default now()
      `
    });
    
    if (error) throw error;
    console.log('Image analysis cache table initialized');
  } catch (error) {
    console.error('Error initializing image analysis cache:', error);
  }
};

/* Helper functions */

/**
 * Generate a sample analysis for demonstration purposes
 * @param {string} imageType - Type of dental image
 * @param {Array} focusAreas - Specific areas to focus on
 * @returns {Object} - Sample analysis results
 */
function generateSampleAnalysis(imageType, focusAreas = []) {
  // Define possible findings based on image type
  const possibleFindings = {
    [ANALYSIS_TYPES.RADIOGRAPHIC]: [
      { condition: 'Dental caries', possibleAreas: ['tooth 14', 'tooth 19', 'tooth 30'] },
      { condition: 'Periapical lesion', possibleAreas: ['tooth 8', 'tooth 9', 'tooth 19'] },
      { condition: 'Bone loss', possibleAreas: ['posterior maxilla', 'anterior mandible'] },
      { condition: 'Root fracture', possibleAreas: ['tooth 7', 'tooth 8', 'tooth 9'] }
    ],
    [ANALYSIS_TYPES.INTRAORAL]: [
      { condition: 'Gingivitis', possibleAreas: ['upper anterior', 'lower anterior', 'posterior quadrants'] },
      { condition: 'Plaque buildup', possibleAreas: ['lingual surfaces', 'interproximal areas'] },
      { condition: 'Dental erosion', possibleAreas: ['buccal surfaces', 'occlusal surfaces'] },
      { condition: 'Dental staining', possibleAreas: ['anterior teeth', 'posterior teeth'] }
    ],
    [ANALYSIS_TYPES.EXTRAORAL]: [
      { condition: 'Facial asymmetry', possibleAreas: ['mandible', 'maxilla'] },
      { condition: 'Swelling', possibleAreas: ['submandibular region', 'buccal region'] },
      { condition: 'TMJ disorder signs', possibleAreas: ['right TMJ', 'left TMJ'] }
    ],
    [ANALYSIS_TYPES.CBCT]: [
      { condition: 'Impacted tooth', possibleAreas: ['third molars', 'canines'] },
      { condition: 'Sinus pathology', possibleAreas: ['maxillary sinus', 'ethmoid sinus'] },
      { condition: 'Bone density reduction', possibleAreas: ['alveolar ridge', 'condylar head'] },
      { condition: 'Implant site evaluation', possibleAreas: ['site 19', 'site 30', 'site 14'] }
    ],
    [ANALYSIS_TYPES.PANORAMIC]: [
      { condition: 'Wisdom tooth impaction', possibleAreas: ['tooth 1', 'tooth 16', 'tooth 17', 'tooth 32'] },
      { condition: 'Dental anomaly', possibleAreas: ['maxillary arch', 'mandibular arch'] },
      { condition: 'Condylar abnormality', possibleAreas: ['right condyle', 'left condyle'] },
      { condition: 'Radiolucency', possibleAreas: ['periapical region', 'furcation area'] }
    ]
  };
  
  // Generate random findings based on image type
  const findingsForType = possibleFindings[imageType] || possibleFindings[ANALYSIS_TYPES.RADIOGRAPHIC];
  const numFindings = 2 + Math.floor(Math.random() * 3); // 2-4 findings
  const findings = [];
  
  // Make a copy of findings to avoid repeats
  const availableFindings = [...findingsForType];
  
  for (let i = 0; i < numFindings && availableFindings.length > 0; i++) {
    // Select a random finding
    const findingIndex = Math.floor(Math.random() * availableFindings.length);
    const selectedFinding = availableFindings[findingIndex];
    
    // Remove this finding from available options
    availableFindings.splice(findingIndex, 1);
    
    // Select a random area for this finding
    const areaIndex = Math.floor(Math.random() * selectedFinding.possibleAreas.length);
    const selectedArea = selectedFinding.possibleAreas[areaIndex];
    
    // Generate random severity and confidence
    const severity = Math.random();
    const confidence = 0.7 + (Math.random() * 0.3); // Between 0.7 and 1.0
    
    findings.push({
      condition: selectedFinding.condition,
      area: selectedArea,
      severity, // 0-1 scale
      confidence,
      description: `${selectedFinding.condition} detected in ${selectedArea}`,
      recommendation: getRandomRecommendation(selectedFinding.condition, severity),
      urgency: severity > 0.7 ? 'high' : severity > 0.4 ? 'medium' : 'low'
    });
  }
  
  // If focus areas were provided, prioritize findings in those areas
  if (focusAreas.length > 0) {
    findings.forEach(finding => {
      if (focusAreas.some(area => finding.area.toLowerCase().includes(area.toLowerCase()))) {
        finding.priority = 'focus area';
        finding.confidence += 0.05; // Slightly increase confidence for focus areas
      }
    });
  }
  
  return {
    imageType,
    findings,
    normalStructures: ['Mandible', 'Maxilla', 'Dental pulp', 'Periodontal ligament'],
    overallAssessment: determineOverallAssessment(findings),
    confidenceMean: findings.reduce((sum, f) => sum + f.confidence, 0) / findings.length,
    analyzedAt: new Date().toISOString(),
    processingTime: `${100 + Math.floor(Math.random() * 900)}ms`
  };
}

/**
 * Determine the overall assessment based on the severity of findings
 * @param {Array} findings - Array of findings
 * @returns {string} - Overall assessment
 */
function determineOverallAssessment(findings) {
  const highSeverity = findings.some(f => f.severity > 0.7);
  const mediumSeverity = findings.some(f => f.severity > 0.4 && f.severity <= 0.7);
  
  if (highSeverity) {
    return 'Requires prompt attention - significant pathology detected';
  } else if (mediumSeverity) {
    return 'Requires follow-up - moderate pathology detected';
  } else if (findings.length > 0) {
    return 'Routine monitoring recommended - minor findings detected';
  } else {
    return 'Within normal limits - no significant findings';
  }
}

/**
 * Generate a hash for an image URL for caching purposes
 * @param {string} imageUrl - URL of the image
 * @returns {string} - Hash string
 */
function generateImageHash(imageUrl) {
  // Simple hash function for demonstration
  // In production, use a proper hashing algorithm
  
  let hash = 0;
  for (let i = 0; i < imageUrl.length; i++) {
    const char = imageUrl.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  return hash.toString();
}

/**
 * Get a random recommendation for a condition
 * @param {string} condition - The dental condition
 * @param {number} severity - Severity of the condition
 * @returns {string} - Recommendation
 */
function getRandomRecommendation(condition, severity) {
  const recommendations = {
    'Dental caries': [
      'Consider restoration with composite filling',
      'Monitor for progression at next visit',
      'Consider preventive sealant if early stage'
    ],
    'Periapical lesion': [
      'Endodontic evaluation recommended',
      'Consider root canal therapy',
      'Periapical radiograph for further evaluation'
    ],
    'Bone loss': [
      'Periodontal consultation recommended',
      'Evaluate for periodontal therapy',
      'Consider bone grafting in severe cases'
    ],
    'Gingivitis': [
      'Improve oral hygiene regimen',
      'Professional cleaning recommended',
      'Consider antimicrobial mouth rinse'
    ],
    'Impacted tooth': [
      'Surgical consultation for possible extraction',
      'Monitor for potential complications',
      'Evaluate space for possible orthodontic treatment'
    ],
    'Wisdom tooth impaction': [
      'Oral surgery consultation recommended',
      'Extract if symptomatic or at risk for pathology',
      'Monitor if asymptomatic with adequate space'
    ]
  };
  
  const defaultRecommendations = [
    'Clinical correlation recommended',
    'Follow up imaging in 6 months',
    'Consider specialist consultation'
  ];
  
  const options = recommendations[condition] || defaultRecommendations;
  
  // Select based on severity
  if (severity > 0.7) {
    return options[0]; // Most interventional option
  } else if (severity > 0.4) {
    return options[1]; // Moderate option
  } else {
    return options[2]; // Most conservative option
  }
}

/**
 * Determine the overall progression from a list of changes
 * @param {Array} changes - List of changes between images
 * @returns {string} - Overall progression assessment
 */
function determineOverallProgression(changes) {
  const worseningChanges = changes.filter(c => c.significance === 'worsened');
  const improvingChanges = changes.filter(c => c.significance === 'improved');
  
  if (worseningChanges.length > improvingChanges.length * 2) {
    return 'Significant deterioration observed';
  } else if (worseningChanges.length > improvingChanges.length) {
    return 'Mild deterioration observed';
  } else if (improvingChanges.length > worseningChanges.length * 2) {
    return 'Significant improvement observed';
  } else if (improvingChanges.length > worseningChanges.length) {
    return 'Mild improvement observed';
  } else if (changes.length > 0) {
    return 'Mixed changes with no clear overall direction';
  } else {
    return 'No significant changes observed';
  }
}

/**
 * Generate a unique comparison ID
 * @returns {string} - Unique ID
 */
function generateComparisonId() {
  return `CMP-${Date.now().toString(36)}-${Math.random().toString(36).substring(2, 7).toUpperCase()}`;
}

/**
 * Generate a sample treatment approach for similar cases
 * @param {Array} findings - List of findings
 * @returns {Array} - Treatment approach steps
 */
function generateSampleTreatmentApproach(findings) {
  const approaches = [];
  
  findings.forEach(finding => {
    switch (finding.condition) {
      case 'Dental caries':
        approaches.push('Conservative restoration with composite material');
        break;
      case 'Periapical lesion':
        approaches.push('Root canal therapy followed by crown restoration');
        break;
      case 'Bone loss':
        approaches.push('Scaling and root planing with adjunctive local antibiotic therapy');
        break;
      case 'Gingivitis':
        approaches.push('Professional cleaning and improved home care protocol');
        break;
      case 'Impacted tooth':
        approaches.push('Surgical extraction with alveolar ridge preservation');
        break;
      case 'Dental erosion':
        approaches.push('Application of desensitizing agent and dietary counseling');
        break;
      default:
        approaches.push('Standard protocol treatment based on clinical guidelines');
    }
  });
  
  // Add some generic approaches if there aren't enough
  if (approaches.length < 2) {
    approaches.push('Preventive regimen including fluoride application');
    approaches.push('6-month follow-up to assess treatment efficacy');
  }
  
  return approaches;
}

/**
 * Generate recommended actions based on finding and urgency level
 * @param {Object} finding - The finding object
 * @param {string} urgencyLevel - Urgency level (urgent, moderate, or minor)
 * @returns {Array} - List of recommended actions
 */
function generateRecommendedActions(finding, urgencyLevel) {
  const actionsByCondition = {
    'Dental caries': {
      urgent: ['Immediate restoration to prevent pulpal involvement', 'Consider indirect pulp capping if deep'],
      moderate: ['Restoration at next available appointment', 'Fluoride varnish application'],
      minor: ['Fluoride application', 'Monitor at next recall visit', 'Review oral hygiene practices']
    },
    'Periapical lesion': {
      urgent: ['Root canal treatment', 'Consider antibiotic therapy if symptomatic'],
      moderate: ['Endodontic evaluation', 'Vitality testing'],
      minor: ['Monitor for changes', 'Follow-up radiograph in 6 months']
    },
    'Bone loss': {
      urgent: ['Comprehensive periodontal therapy', 'Consider antimicrobial adjuncts'],
      moderate: ['Scaling and root planing', 'Oral hygiene instruction'],
      minor: ['Professional cleaning', 'Improved interproximal cleaning techniques']
    },
    'Gingivitis': {
      urgent: ['Professional cleaning', 'Chlorhexidine rinse for 2 weeks'],
      moderate: ['Professional cleaning', 'Oral hygiene instruction'],
      minor: ['Oral hygiene instruction', 'Demonstration of proper brushing technique']
    }
  };
  
  // Default actions if condition-specific ones aren't available
  const defaultActions = {
    urgent: ['Prompt evaluation by dental professional', 'Consider referral to specialist'],
    moderate: ['Evaluation at next dental visit', 'Monitor for changes or symptoms'],
    minor: ['Routine monitoring', 'Document for baseline comparison']
  };
  
  // Get condition-specific actions or fall back to defaults
  const actionSet = actionsByCondition[finding.condition] || defaultActions;
  const actions = actionSet[urgencyLevel] || defaultActions[urgencyLevel];
  
  // Add a generic recommendation
  actions.push('Document in patient record for future comparison');
  
  return actions;
} 