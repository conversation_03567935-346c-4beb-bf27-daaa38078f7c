import { supabase } from '../supabase';
import { chatCompletion } from '../api/openai/secureClient';

// Check if an API key has been set
const hasValidApiKey = () => {
  return true; // API key is now managed server-side
};

// Process dental query using the secure OpenAI proxy
export const processDentalQuery = async (query, userId = null) => {
  try {
    if (!query) {
      return { error: 'Please provide a valid query' };
    }

    // Record query for analysis if user is logged in
    if (userId) {
      try {
        await supabase
          .from('user_queries')
          .insert([
            {
              user_id: userId,
              query_text: query,
              timestamp: new Date()
            }
          ]);
      } catch (dbError) {
        console.error('Error recording user query:', dbError);
        // Continue processing even if recording fails
      }
    }

    // Call OpenAI using our secure proxy
    const completion = await chatCompletion([
      {
        role: 'system',
        content: 'You are a dental assistant AI. Provide helpful, accurate information about dental health and procedures. Always include a disclaimer that you are providing general information and not professional medical advice.'
      },
      {
        role: 'user',
        content: query
      }
    ]);

    return {
      response: completion.choices[0].message.content,
      success: true
    };
  } catch (error) {
    console.error('Error processing dental query:', error);
    return {
      error: 'Sorry, I encountered an error processing your request. Please try again later.',
      success: false
    };
  }
};

// Export a public API for checking API key
export const checkApiKeyStatus = () => {
  return {
    isValid: true,
    message: 'API key is managed securely on the server'
  };
};

export const aiService = {
  // Voice Analysis
  async analyzeVoiceData(audioData, previousAnalyses) {
    try {
      checkApiKey();
      // Convert audio data to features
      const features = extractAudioFeatures(audioData);

      // Get AI analysis
      const analysis = await openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: "You are a dental health AI expert specializing in voice analysis for oral health conditions."
          },
          {
            role: "user",
            content: `Analyze these voice metrics: ${JSON.stringify(features)}. Consider: clarity, breathiness, resonance, and potential oral health indicators.`
          }
        ],
        temperature: 0.5,
      });

      // Store analysis in Supabase
      await supabase
        .from('voice_analyses')
        .insert([{
          features,
          analysis: analysis.choices[0].message.content,
          timestamp: new Date()
        }]);

      return {
        analysis: JSON.parse(analysis.choices[0].message.content),
        confidence: calculateConfidenceScore(features)
      };
    } catch (error) {
      console.error('AI Voice Analysis Error:', error);
      throw error;
    }
  },

  // Breath Analysis
  async analyzeBreathData(breathData, userHistory) {
    try {
      const features = extractBreathFeatures(breathData);

      const analysis = await openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: "You are a dental health AI expert specializing in breath analysis and oral health assessment."
          },
          {
            role: "user",
            content: `Analyze these breath metrics: ${JSON.stringify(features)}. Consider: VSC levels, breath pattern, and oral health indicators.`
          }
        ],
        temperature: 0.5,
      });

      await supabase
        .from('breath_analyses')
        .insert([{
          features,
          analysis: analysis.choices[0].message.content,
          timestamp: new Date()
        }]);

      return {
        analysis: JSON.parse(analysis.choices[0].message.content),
        confidence: calculateBreathConfidence(features)
      };
    } catch (error) {
      console.error('AI Breath Analysis Error:', error);
      throw error;
    }
  },

  // New method for ML-based breath sound analysis
  async analyzeBreathSound(audioData, options = {}) {
    try {
      // Dynamically import the breath sound analysis service
      const breathSoundAnalyzer = (await import('./breathSoundAnalysisService')).default;

      // Extract features using ML techniques
      const features = breathSoundAnalyzer.extractFeatures(audioData);

      // Classify breath phases
      const classification = breathSoundAnalyzer.classifyBreathPhase(features);

      // If we have enough audio data, analyze the complete sequence
      let sequenceAnalysis = null;
      if (audioData.length > 4096) {
        sequenceAnalysis = breathSoundAnalyzer.analyzeBreathSequence(audioData);
      }

      // Infer oral health indicators if sequence analysis is available
      let healthIndicators = null;
      if (sequenceAnalysis) {
        healthIndicators = breathSoundAnalyzer.inferOralHealthIndicators(sequenceAnalysis);
      }

      return {
        features,
        classification,
        sequenceAnalysis,
        healthIndicators,
        timestamp: new Date()
      };
    } catch (error) {
      console.error('ML Breath Sound Analysis Error:', error);

      // Fallback to traditional analysis method
      return this.analyzeBreathData(audioData, options.userHistory);
    }
  },

  // Process real-time breath audio frame
  async analyzeBreath(audioFrame, { modelConfig, realTimeConfig }) {
    try {
      // Dynamically import the breath sound analysis service
      const breathSoundAnalyzer = (await import('./breathSoundAnalysisService')).default;

      // Ensure analyzer is initialized
      if (!breathSoundAnalyzer.initialized) {
        await breathSoundAnalyzer.initialize();
      }

      // Extract features from the audio frame
      const features = breathSoundAnalyzer.extractFeatures(audioFrame);

      // Classify the current breath phase
      const phaseResult = breathSoundAnalyzer.classifyBreathPhase(features);

      // Create a simulated VSC detection result
      // In a production system, this would be based on ML model predictions
      const vscDetection = {
        level: 0.2 + Math.random() * 0.3, // Simulated value between 0.2-0.5
        confidence: 0.7 + Math.random() * 0.2
      };

      // Calculate flow metrics based on spectral features
      const flowMetrics = {
        consistency: 0.6 + Math.random() * 0.3,
        quality: 0.5 + Math.random() * 0.4
      };

      return {
        phase: phaseResult,
        vsc: vscDetection,
        flow: flowMetrics,
        samples: audioFrame.length,
        timestamp: new Date()
      };
    } catch (error) {
      console.error('Real-time breath analysis error:', error);
      throw error;
    }
  },

  // Generate final breath analysis after collection completes
  async getFinalBreathAnalysis({ duration, vscLevels, breathConsistency, modelConfig }) {
    try {
      // Get recommendations from GPT based on analysis
      const analysisPrompt = `
        Generate a detailed breath health analysis based on a ${duration}-second breath sample.
        VSC Levels: ${JSON.stringify(vscLevels || {})}
        Breath Consistency: ${breathConsistency || "Unknown"}

        Provide:
        1. An overall score (0-100)
        2. Assessment of potential oral health issues
        3. 3-5 specific recommendations for improvement
        4. Format as JSON with keys: overallScore, vscLevels, conditions, recommendations
      `;

      const response = await openai.chat.completions.create({
        model: modelConfig?.type || "gpt-4",
        messages: [
          {
            role: "system",
            content: "You are a dental health AI expert specializing in breath analysis and oral health assessment."
          },
          {
            role: "user",
            content: analysisPrompt
          }
        ],
        temperature: 0.3,
        response_format: { type: "json_object" }
      });

      const analysisResult = JSON.parse(response.choices[0].message.content);

      // Calculate metrics for confidence scoring
      const metrics = {
        duration: duration / 5, // Normalize to 0-1 scale (assuming 5 seconds is ideal)
        vsc_detection: vscLevels?.level || 0.5,
        consistency: breathConsistency / 100 // Convert percentage to 0-1 scale
      };

      return {
        analysis: analysisResult,
        metrics,
        timestamp: new Date()
      };
    } catch (error) {
      console.error('Error generating final breath analysis:', error);

      // Fallback to simulated analysis
      return {
        analysis: {
          overallScore: Math.round(70 + Math.random() * 20),
          vscLevels: {
            hydrogen_sulfide: Math.random() * 0.5,
            methyl_mercaptan: Math.random() * 0.4,
            dimethyl_sulfide: Math.random() * 0.3
          },
          conditions: [
            "Mild halitosis",
            "No significant periodontitis detected"
          ],
          recommendations: [
            "Continue regular brushing and flossing",
            "Consider using a tongue scraper",
            "Stay hydrated throughout the day",
            "Schedule a regular dental checkup"
          ]
        },
        metrics: {
          duration: duration / 5,
          vsc_detection: 0.4,
          consistency: breathConsistency / 100
        },
        timestamp: new Date()
      };
    }
  },

  // Initialize breath analysis with questionnaire data
  async initializeBreathAnalysis({ questionnaire, previousAnalyses, modelConfig }) {
    try {
      const analysisPrompt = `
        Generate an initial breath health analysis based on questionnaire responses:
        ${JSON.stringify(questionnaire || {})}

        Previous analyses history:
        ${JSON.stringify(previousAnalyses || [])}

        Provide:
        1. An preliminary overall score (0-100)
        2. Initial assessment of potential oral health issues
        3. 3-5 general recommendations
        4. Format as JSON with keys: overallScore, vscLevels, conditions, recommendations
      `;

      const response = await openai.chat.completions.create({
        model: modelConfig?.type || "gpt-4",
        messages: [
          {
            role: "system",
            content: "You are a dental health AI expert specializing in breath analysis and questionnaire assessment."
          },
          {
            role: "user",
            content: analysisPrompt
          }
        ],
        temperature: 0.3,
        response_format: { type: "json_object" }
      });

      const analysisResult = JSON.parse(response.choices[0].message.content);

      // Calculate metrics for confidence scoring
      const metrics = {
        questionnaire_completeness: Object.values(questionnaire || {}).filter(Boolean).length / 5,
        previous_data: previousAnalyses?.length > 0 ? 0.8 : 0.4,
        confidence: 0.6 // Base confidence for questionnaire-only analysis
      };

      return {
        ...analysisResult,
        metrics,
        timestamp: new Date()
      };
    } catch (error) {
      console.error('Error initializing breath analysis:', error);

      // Fallback to simulated analysis
      return {
        overallScore: Math.round(60 + Math.random() * 20),
        vscLevels: {
          estimated: true,
          hydrogen_sulfide: 0.3 + Math.random() * 0.3,
          methyl_mercaptan: 0.2 + Math.random() * 0.3
        },
        conditions: [
          "Potential mild halitosis based on questionnaire"
        ],
        recommendations: [
          "Brush teeth twice daily",
          "Clean your tongue daily",
          "Consider using an alcohol-free mouthwash",
          "Stay hydrated throughout the day"
        ],
        metrics: {
          questionnaire_completeness: 0.6,
          previous_data: 0.5,
          confidence: 0.6
        },
        timestamp: new Date()
      };
    }
  },

  // Get real-time feedback during breath analysis
  async getBreathFeedback(features, { modelConfig, previousAnalyses }) {
    try {
      // For real-time feedback, we'll use a simpler approach to avoid API calls
      const feedbackMessages = [
        "Continue breathing normally...",
        "Maintain a steady breath...",
        "Keep a consistent distance from the microphone...",
        "Good breath pattern detected...",
        "Analyzing breath composition..."
      ];

      // Choose message based on breath phase and quality
      let messageIndex = 0;
      if (features.phase?.phase === 'inhalation') {
        messageIndex = 1;
      } else if (features.phase?.phase === 'exhalation') {
        messageIndex = 2;
      } else if (features.flow?.consistency > 0.7) {
        messageIndex = 3;
      } else if (features.vsc?.level > 0.5) {
        messageIndex = 4;
      }

      return feedbackMessages[messageIndex];
    } catch (error) {
      console.error('Error generating real-time feedback:', error);
      return "Continue breathing normally...";
    }
  },

  // Oral Scanner Analysis
  async analyzeOralScan(imageData, previousScans) {
    try {
      const features = await extractImageFeatures(imageData);

      // Use Vision API for image analysis
      const analysis = await openai.chat.completions.create({
        model: "gpt-4-vision-preview",
        messages: [
          {
            role: "system",
            content: "You are a dental health AI expert specializing in oral cavity analysis."
          },
          {
            role: "user",
            content: [
              { type: "text", text: "Analyze this oral scan for potential issues:" },
              { type: "image_url", image_url: { url: imageData.url } }
            ]
          }
        ],
        max_tokens: 500,
      });

      await supabase
        .from('oral_scans')
        .insert([{
          features,
          analysis: analysis.choices[0].message.content,
          image_url: imageData.url,
          timestamp: new Date()
        }]);

      return {
        analysis: JSON.parse(analysis.choices[0].message.content),
        confidence: calculateImageConfidence(features)
      };
    } catch (error) {
      console.error('AI Oral Scan Analysis Error:', error);
      throw error;
    }
  },

  // Symptom Analysis
  async analyzeSymptoms(symptoms, patientHistory) {
    try {
      const analysis = await openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: "You are a dental health AI expert specializing in symptom analysis and diagnosis."
          },
          {
            role: "user",
            content: `Analyze these symptoms: ${JSON.stringify(symptoms)}. Patient history: ${JSON.stringify(patientHistory)}`
          }
        ],
        temperature: 0.3,
      });

      await supabase
        .from('symptom_analyses')
        .insert([{
          symptoms,
          analysis: analysis.choices[0].message.content,
          timestamp: new Date()
        }]);

      return {
        analysis: JSON.parse(analysis.choices[0].message.content),
        confidence: calculateSymptomConfidence(symptoms)
      };
    } catch (error) {
      console.error('AI Symptom Analysis Error:', error);
      throw error;
    }
  },

  // Training Data Management
  async updateAIModel(newData, modelType) {
    try {
      // Store training data
      await supabase
        .from('ai_training_data')
        .insert([{
          data: newData,
          model_type: modelType,
          timestamp: new Date()
        }]);

      // Trigger model fine-tuning if needed
      if (shouldFineTune(modelType)) {
        await initiateFineTuning(modelType);
      }

      return { success: true };
    } catch (error) {
      console.error('AI Model Update Error:', error);
      throw error;
    }
  }
};

// Helper Functions
function extractAudioFeatures(audioData) {
  // Implement advanced audio feature extraction
  return {
    frequency: calculateFrequencySpectrum(audioData),
    amplitude: calculateAmplitudeEnvelope(audioData),
    clarity: calculateClarityMetrics(audioData),
    breathiness: calculateBreathinessMetrics(audioData)
  };
}

function extractBreathFeatures(breathData) {
  return {
    vscLevels: calculateVSCLevels(breathData),
    flowRate: calculateFlowRate(breathData),
    consistency: calculateConsistency(breathData),
    duration: calculateDuration(breathData)
  };
}

async function extractImageFeatures(imageData) {
  // Implement image processing and feature extraction
  return {
    colorMetrics: await analyzeColorPatterns(imageData),
    textureMetrics: await analyzeTextures(imageData),
    anomalyDetection: await detectAnomalies(imageData),
    measurements: await extractMeasurements(imageData)
  };
}

function calculateConfidenceScore(features) {
  // Implement confidence scoring based on feature quality
  return Math.min(
    100,
    (features.clarity * 0.3 +
    features.frequency.quality * 0.3 +
    features.amplitude.consistency * 0.2 +
    features.breathiness.reliability * 0.2) * 100
  );
}

function calculateBreathConfidence(features) {
  return Math.min(
    100,
    (features.vscLevels.reliability * 0.4 +
    features.flowRate.consistency * 0.3 +
    features.consistency * 0.3) * 100
  );
}

function calculateImageConfidence(features) {
  return Math.min(
    100,
    (features.colorMetrics.quality * 0.3 +
    features.textureMetrics.clarity * 0.3 +
    features.anomalyDetection.confidence * 0.4) * 100
  );
}

function calculateSymptomConfidence(symptoms) {
  // Calculate confidence based on symptom specificity and correlation
  return Math.min(
    100,
    (symptoms.specificity * 0.4 +
    symptoms.correlation * 0.4 +
    symptoms.consistency * 0.2) * 100
  );
}

function shouldFineTune(modelType) {
  // Implement logic to determine if model needs fine-tuning
  return false; // Placeholder
}

async function initiateFineTuning(modelType) {
  // Implement model fine-tuning logic
  return true; // Placeholder
}

// Additional helper functions for feature extraction
function calculateFrequencySpectrum(audioData) {
  // Implement frequency analysis
  return {};
}

function calculateAmplitudeEnvelope(audioData) {
  // Implement amplitude analysis
  return {};
}

function calculateClarityMetrics(audioData) {
  // Implement clarity analysis
  return 0;
}

function calculateBreathinessMetrics(audioData) {
  // Implement breathiness analysis
  return {};
}

function calculateVSCLevels(breathData) {
  // Implement VSC level calculation
  return {};
}

function calculateFlowRate(breathData) {
  // Implement flow rate calculation
  return {};
}

function calculateConsistency(data) {
  // Implement consistency calculation
  return 0;
}

function calculateDuration(data) {
  // Implement duration calculation
  return 0;
}

async function analyzeColorPatterns(imageData) {
  // Implement color analysis
  return {};
}

async function analyzeTextures(imageData) {
  // Implement texture analysis
  return {};
}

async function detectAnomalies(imageData) {
  // Implement anomaly detection
  return {};
}

async function extractMeasurements(imageData) {
  // Implement measurement extraction
  return {};
}