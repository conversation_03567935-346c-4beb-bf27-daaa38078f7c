/**
 * Location Service
 * Provides functions for getting user's location and geocoding
 */

// Check if we're in a browser environment
const isBrowser = typeof window !== 'undefined' && typeof navigator !== 'undefined';

// Public geocoding API endpoint - this is a free, widely used service
const NOMINATIM_API = 'https://nominatim.openstreetmap.org';

/**
 * Get user's location via browser geolocation API
 * @returns {Promise<{latitude: number, longitude: number}>} User's coordinates
 */
export const getUserLocation = () => {
  return new Promise((resolve, reject) => {
    if (!isBrowser || !navigator.geolocation) {
      reject(new Error('Geolocation is not supported by your browser'));
      return;
    }

    navigator.geolocation.getCurrentPosition(
      position => {
        resolve({
          latitude: position.coords.latitude,
          longitude: position.coords.longitude
        });
      },
      error => {
        console.error('Error getting user location:', error.message);
        reject(error);
      },
      { 
        enableHighAccuracy: true, 
        timeout: 10000,
        maximumAge: 60000 // Cache for 1 minute for better accuracy
      }
    );
  });
};

/**
 * Get postal code from coordinates using Nominatim directly
 * @param {number} latitude - Latitude coordinate
 * @param {number} longitude - Longitude coordinate
 * @returns {Promise<string>} Postal code (ZIP code)
 */
export const getPostalCodeFromCoordinates = async (latitude, longitude) => {
  try {
    // Call Nominatim API directly
    const apiUrl = `${NOMINATIM_API}/reverse?lat=${latitude}&lon=${longitude}&format=json&addressdetails=1`;
    console.log('Calling location API:', apiUrl);
    
    const response = await fetch(apiUrl, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'SmiloApp/1.0 (https://smilo.dental)' // Required by Nominatim usage policy
      }
    });

    if (!response.ok) {
      throw new Error('Failed to fetch location data');
    }

    const data = await response.json();
    
    // Extract postal code from address
    const postalCode = data.address?.postcode;
    
    if (!postalCode) {
      console.warn('No postal code found in response:', data);
      return null;
    }
    
    // For US zip codes, ensure it's 5 digits
    if (/^\d{5}(?:[-\s]\d{4})?$/.test(postalCode)) {
      // Remove extension if present (12345-6789 -> 12345)
      return postalCode.substring(0, 5);
    }
    
    return postalCode;
  } catch (error) {
    console.error('Error getting postal code:', error);
    return null;
  }
};

/**
 * Get coordinates from ZIP code using Nominatim
 * @param {string} zipCode - ZIP code to geocode
 * @param {string} countryCode - Country code (default: 'US')
 * @returns {Promise<{latitude: number, longitude: number}>} Coordinates
 */
export const getCoordinatesFromZipCode = async (zipCode, countryCode = 'US') => {
  try {
    const response = await fetch(
      `${NOMINATIM_API}/search?format=json&postalcode=${zipCode}&country=${countryCode}&limit=1`,
      {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'SmiloApp/1.0 (https://smilo.dental)'
        }
      }
    );
    
    if (!response.ok) {
      throw new Error('Failed to geocode ZIP code');
    }
    
    const data = await response.json();
    
    if (data.length === 0) {
      throw new Error('No results found for this ZIP code');
    }
    
    return {
      latitude: parseFloat(data[0].lat),
      longitude: parseFloat(data[0].lon),
      displayName: data[0].display_name
    };
  } catch (error) {
    console.error('Error geocoding ZIP code:', error);
    throw error;
  }
};

/**
 * Full location detection workflow:
 * 1. Try to get user's coordinates via browser geolocation
 * 2. Convert coordinates to postal code
 * 3. Fall back to stored ZIP code if available
 * 4. Return null if no location can be determined
 * 
 * @returns {Promise<{postalCode: string, coordinates: {latitude: number, longitude: number}, source: string}>} Location info
 */
export const detectUserLocation = async () => {
  try {
    // First check if we have a stored ZIP code
    const storedZip = isBrowser && localStorage ? localStorage.getItem('userZipCode') : null;
    
    // Only try geolocation in browser environments
    if (isBrowser) {
      try {
        // Try to get precise location from browser
        const coords = await getUserLocation();
        
        // Convert coordinates to postal code
        const postalCode = await getPostalCodeFromCoordinates(
          coords.latitude, 
          coords.longitude
        );
        
        // Save the detected ZIP code for future use
        if (postalCode && localStorage) {
          localStorage.setItem('userZipCode', postalCode);
        }
        
        return { 
          postalCode, 
          coordinates: coords, 
          source: 'geolocation' 
        };
      } catch (geoError) {
        console.warn('Geolocation failed, falling back to stored ZIP:', geoError);
        // Continue to fallback methods
      }
    }
    
    // Fall back to stored ZIP and attempt to get coordinates for it
    if (storedZip) {
      try {
        // Try to get coordinates for the stored ZIP
        const coords = await getCoordinatesFromZipCode(storedZip);
        return { 
          postalCode: storedZip, 
          coordinates: coords, 
          source: 'stored' 
        };
      } catch (zipError) {
        console.warn('Failed to get coordinates for stored ZIP:', zipError);
        // Return just the ZIP code without coordinates
        return { 
          postalCode: storedZip, 
          coordinates: null, 
          source: 'stored' 
        };
      }
    }
    
    // No location could be determined
    return null;
  } catch (error) {
    console.error('Error in location detection workflow:', error);
    
    // Fall back to stored ZIP if geolocation fails
    const storedZip = isBrowser && localStorage ? localStorage.getItem('userZipCode') : null;
    if (storedZip) {
      return { postalCode: storedZip, coordinates: null, source: 'stored' };
    }
    
    // No location could be determined
    return null;
  }
};

export default {
  getUserLocation,
  getPostalCodeFromCoordinates,
  getCoordinatesFromZipCode,
  detectUserLocation
};