import { supabase } from '../supabase';
import { formatISO, subHours } from 'date-fns';
import cheerio from 'cheerio';
import pLimit from 'p-limit';
import Parser from 'rss-parser';

const parser = new Parser();
const limit = pLimit(2); // Limit concurrent requests

const TRUSTED_SOURCES = [
  {
    name: 'Journal of Dental Research',
    url: 'https://journals.sagepub.com/loi/jdr',
    rss_url: 'https://journals.sagepub.com/action/showFeed?ui=0&mi=3fndc3&ai=2b4&jc=jdr&type=etoc&feed=rss',
    type: 'rss'
  },
  {
    name: 'Journal of Clinical Periodontology',
    url: 'https://onlinelibrary.wiley.com/journal/1600051x',
    rss_url: 'https://onlinelibrary.wiley.com/feed/1600051x/most-recent',
    type: 'rss'
  },
  {
    name: 'American Dental Association',
    url: 'https://www.ada.org/publications',
    rss_url: 'https://www.ada.org/publications/ada-news/feed/rss',
    type: 'rss'
  },
  {
    name: 'International Journal of Dentistry',
    url: 'https://www.hindawi.com/journals/ijd/',
    rss_url: 'https://www.hindawi.com/journals/ijd/rss.xml',
    type: 'rss'
  },
  {
    name: 'Journal of Dental Education',
    url: 'https://onlinelibrary.wiley.com/journal/19307837',
    rss_url: 'https://onlinelibrary.wiley.com/feed/19307837/most-recent',
    type: 'rss'
  },
  {
    name: 'British Dental Journal',
    url: 'https://www.nature.com/bdj/',
    rss_url: 'https://www.nature.com/bdj.rss',
    type: 'rss'
  },
  {
    name: 'Journal of the American Dental Association',
    url: 'https://jada.ada.org',
    rss_url: 'https://jada.ada.org/current.rss',
    type: 'rss'
  },
  {
    name: 'European Journal of Dental Education',
    url: 'https://onlinelibrary.wiley.com/journal/16000579',
    rss_url: 'https://onlinelibrary.wiley.com/feed/16000579/most-recent',
    type: 'rss'
  },
  {
    name: 'Journal of Evidence-Based Dental Practice',
    url: 'https://www.sciencedirect.com/journal/journal-of-evidence-based-dental-practice',
    rss_url: 'https://rss.sciencedirect.com/publication/science/15323382',
    type: 'rss'
  },
  {
    name: 'Dental Research Journal',
    url: 'https://www.drjjournal.net',
    rss_url: 'https://www.drjjournal.net/feed.rss',
    type: 'rss'
  }
];

// Rate limiting configuration
const RATE_LIMITS = {
  requestsPerMinute: 30,
  requestsPerHour: 500
};

// Monitoring metrics
let metrics = {
  totalRequests: 0,
  successfulRequests: 0,
  failedRequests: 0,
  lastUpdateTime: null,
  errors: []
};

export async function fetchLatestArticles() {
  const { data: cachedArticles, error: cacheError } = await supabase
    .from('article_cache')
    .select('articles, updated_at')
    .single();

  if (cacheError) {
    logError('Cache fetch error', cacheError);
    return [];
  }

  // Check if cache is older than 1 hour
  const cacheAge = new Date(cachedArticles?.updated_at || 0);
  if (cacheAge < subHours(new Date(), 1)) {
    await refreshArticleCache();
    return fetchLatestArticles();
  }

  return cachedArticles?.articles || [];
}

async function fetchArticlesFromSource(source) {
  metrics.totalRequests++;
  
  try {
    let articles = [];
    
    if (source.rss_url) {
      articles = await fetchRssArticles(source);
    } else {
      articles = await fetchHtmlArticles(source);
    }
    
    metrics.successfulRequests++;
    return articles;
  } catch (error) {
    metrics.failedRequests++;
    logError(`Error fetching from ${source.name}`, error);
    return [];
  }
}

async function fetchRssArticles(source) {
  const feed = await parser.parseURL(source.rss_url);
  
  return feed.items.map(item => ({
    title: item.title,
    summary: item.contentSnippet?.slice(0, 200) || '',
    link: item.link,
    source: source.name,
    date: new Date(item.pubDate),
    tags: extractTags(item),
    type: 'research'
  }));
}

async function fetchHtmlArticles(source) {
  const response = await fetch(source.url);
  const html = await response.text();
  const $ = cheerio.load(html);
  const articles = [];

  // Source-specific selectors
  const selectors = {
    'Journal of Dental Research': {
      article: 'article.article-item',
      title: 'h2.title',
      summary: 'div.abstract',
      link: 'a.title-link'
    },
    'American Dental Association': {
      article: 'div.news-item',
      title: 'h3.title',
      summary: 'div.excerpt',
      link: 'a.read-more'
    }
  };

  const sourceSelectors = selectors[source.name];
  
  $(sourceSelectors.article).each((i, el) => {
    articles.push({
      title: $(el).find(sourceSelectors.title).text().trim(),
      summary: $(el).find(sourceSelectors.summary).text().trim().slice(0, 200),
      link: $(el).find(sourceSelectors.link).attr('href'),
      source: source.name,
      date: new Date(),
      tags: extractTagsFromHtml($(el))
    });
  });

  return articles;
}

function extractTags(item) {
  const tags = new Set();
  
  if (item.categories) {
    item.categories.forEach(tag => tags.add(tag.toLowerCase()));
  }
  
  const commonTerms = [
    'dental', 'orthodontics', 'periodontics', 'endodontics',
    'oral surgery', 'dental education', 'clinical research'
  ];
  
  commonTerms.forEach(term => {
    const content = (item.content || item.contentSnippet || '').toLowerCase();
    if (content.includes(term)) {
      tags.add(term);
    }
  });
  
  return Array.from(tags);
}

function extractTagsFromHtml($el) {
  const tags = new Set();
  const text = $el.text().toLowerCase();
  
  const commonTerms = [
    'dental', 'orthodontics', 'periodontics', 'endodontics',
    'oral surgery', 'dental education', 'clinical research'
  ];
  
  commonTerms.forEach(term => {
    if (text.includes(term)) {
      tags.add(term);
    }
  });
  
  return Array.from(tags);
}

async function refreshArticleCache() {
  try {
    console.log('Starting article cache refresh...');
    metrics.lastUpdateTime = new Date();
    
    // Get trusted sources
    const { data: sources } = await supabase
      .from('article_sources')
      .select('*')
      .eq('is_trusted', true);
    
    const articlePromises = sources.map(source => 
      limit(() => fetchArticlesFromSource(source))
    );
    
    const allArticles = await Promise.all(articlePromises);
    
    const articles = allArticles
      .flat()
      .filter((article, index, self) => 
        index === self.findIndex(a => a.link === article.link)
      )
      .filter(article => {
        // Keep articles from the last 30 days
        const articleDate = new Date(article.date);
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        return articleDate >= thirtyDaysAgo;
      })
      .sort((a, b) => new Date(b.date) - new Date(a.date))
      .slice(0, 200); // Keep top 200 most recent

    await updateArticleCache(articles);
    await logMetrics();
    
    console.log('Article cache refreshed successfully');
  } catch (error) {
    logError('Cache refresh error', error);
  }
}

async function updateArticleCache(articles) {
  const { error } = await supabase
    .from('article_cache')
    .upsert({
      id: 1,
      articles,
      updated_at: new Date().toISOString()
    });

  if (error) {
    logError('Cache update error', error);
    throw error;
  }
}

async function logMetrics() {
  try {
    await supabase
      .from('article_metrics')
      .insert([{
        total_requests: metrics.totalRequests,
        successful_requests: metrics.successfulRequests,
        failed_requests: metrics.failedRequests,
        error_count: metrics.errors.length,
        created_at: new Date().toISOString()
      }]);
      
    // Reset metrics after logging
    metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      lastUpdateTime: null,
      errors: []
    };
  } catch (error) {
    console.error('Error logging metrics:', error);
  }
}

function logError(message, error) {
  const errorDetails = {
    message,
    error: error.message,
    stack: error.stack,
    timestamp: new Date().toISOString()
  };
  
  metrics.errors.push(errorDetails);
  console.error(message, error);
  
  // Log to Supabase
  supabase
    .from('error_logs')
    .insert([errorDetails])
    .then(() => console.log('Error logged to database'))
    .catch(err => console.error('Failed to log error:', err));
}