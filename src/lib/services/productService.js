/**
 * Product Service
 * Handles fetching and managing product data from multiple grocery stores
 */

// Check if we're in a browser environment
const isBrowser = typeof window !== 'undefined';

// API keys would be stored securely in environment variables
// These are placeholders and would be replaced with actual keys in production
const API_KEYS = {
  walmart: (isBrowser && window._env && window._env.WALMART_API_KEY) || 'demo-key',
  kroger: (isBrowser && window._env && window._env.KROGER_API_KEY) || 'demo-key',
  instacart: (isBrowser && window._env && window._env.INSTACART_API_KEY) || 'demo-key'
};

// Store configuration
const STORES = [
  { 
    id: 'walmart', 
    name: 'Walmart', 
    logo: '/assets/logos/walmart.png',
    apiBase: 'https://api.walmart.com/v3/items',
    snapEligible: true,
    color: '#0071ce'
  },
  { 
    id: 'kroger', 
    name: '<PERSON><PERSON><PERSON>', 
    logo: '/assets/logos/kroger.png',
    apiBase: 'https://api.kroger.com/v1/products',
    snapEligible: true,
    color: '#e63c2f'
  },
  { 
    id: 'publix', 
    name: 'Publix', 
    logo: '/assets/logos/publix.png',
    apiBase: 'https://api.publix.com/products',
    snapEligible: true,
    color: '#4c9c2e'
  },
  { 
    id: 'target', 
    name: 'Target', 
    logo: '/assets/logos/target.png',
    apiBase: 'https://api.target.com/products',
    snapEligible: true,
    color: '#cc0000'
  },
  { 
    id: 'wholeFoods', 
    name: 'Whole Foods', 
    logo: '/assets/logos/wholefoods.png',
    apiBase: 'https://api.wholefoods.com/products',
    snapEligible: true,
    color: '#3f851c'
  }
];

/**
 * Get stores near a given location by ZIP code
 * @param {string} zipCode - User ZIP code
 * @param {number} radius - Search radius in miles (default: 10)
 * @returns {Promise<Array>} - Array of nearby stores with distance info
 */
export const getNearbyStores = async (zipCode, radius = 10) => {
  // In production, this would call store location APIs
  // For demo purposes, we return mock data with randomized distances
  
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Create a copy of stores with randomized distances
    return STORES.map(store => {
      // Generate random distance between 0.5 and radius miles
      const distance = (Math.random() * (radius - 0.5) + 0.5).toFixed(1);
      
      return {
        ...store,
        distance: `${distance} mi`,
        distanceValue: parseFloat(distance)
      };
    }).sort((a, b) => a.distanceValue - b.distanceValue);
  } catch (error) {
    console.error('Error fetching nearby stores:', error);
    return [];
  }
};

/**
 * Search for products that match an ingredient at specified stores
 * @param {string} ingredient - Ingredient name/description
 * @param {Array} storeIds - Array of store IDs to search
 * @param {object} options - Search options (filters, sort, etc.)
 * @returns {Promise<Array>} - Array of matching products
 */
export const searchProductsByIngredient = async (ingredient, storeIds = [], options = {}) => {
  try {
    // For demo purposes, generate synthetic product data
    // In production, this would call actual store APIs
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 800));
    
    const products = [];
    const storesMap = {};
    
    // Create a map for quick store lookups
    STORES.forEach(store => {
      storesMap[store.id] = store;
    });
    
    // If no stores specified, use all stores
    const targetStores = storeIds.length > 0 
      ? storeIds.filter(id => storesMap[id]) 
      : STORES.map(store => store.id);
    
    // Generate product variants for this ingredient across stores
    const lowerIngredient = ingredient.toLowerCase();
    
    // Base product characteristics derived from ingredient name
    const basePrice = generateBasePrice(lowerIngredient);
    const productName = generateProductName(lowerIngredient);
    const units = determineUnits(lowerIngredient);
    const unitSize = determineUnitSize(lowerIngredient);
    const unitCount = determineUnitCount(lowerIngredient);
    const nutritionTags = determineNutritionTags(lowerIngredient);
    const isOrganic = lowerIngredient.includes('organic') || Math.random() < 0.3;
    const productType = determineProductType(lowerIngredient);
    const baseImage = determineProductImage(productType, isOrganic);
    const isSnapEligible = determineSnapEligibility(productType);
    
    // Generate 1-4 products for each store
    targetStores.forEach(storeId => {
      const store = storesMap[storeId];
      if (!store) return;
      
      // Number of product variants per store (1-4)
      const variantCount = Math.floor(Math.random() * 4) + 1;
      
      for (let i = 0; i < variantCount; i++) {
        // Brand selection
        const isBrandName = Math.random() < 0.7; // 70% chance of brand name
        const isStoreOwn = Math.random() < 0.4 && !isBrandName; // 40% chance of store brand if not brand name
        
        // Price variations
        const priceModifier = isBrandName ? (Math.random() * 0.4 + 1.1) : (Math.random() * 0.2 + 0.8);
        const price = (basePrice * priceModifier * (isOrganic ? 1.3 : 1)).toFixed(2);
        
        // Sale status
        const onSale = Math.random() < 0.25; // 25% chance of being on sale
        const salePrice = onSale ? (price * (Math.random() * 0.2 + 0.7)).toFixed(2) : null;
        
        // Stock availability
        const inStock = Math.random() < 0.9; // 90% chance of being in stock
        
        // Product size variations
        const sizeModifier = Math.random() < 0.3 ? 
          (Math.random() < 0.5 ? 0.5 : 2) : 1; // 30% chance of different size
          
        const currentUnitSize = (unitSize * sizeModifier).toFixed(1).replace('.0', '');
        const currentUnitCount = Math.round(unitCount * sizeModifier);
        
        // Product name construction
        let brandPrefix = '';
        if (isStoreOwn) {
          brandPrefix = `${store.name} `;
        } else if (isBrandName) {
          // Select from common brands for this product type
          const brands = getCommonBrands(productType);
          brandPrefix = `${brands[Math.floor(Math.random() * brands.length)]} `;
        }
        
        const organicPrefix = isOrganic ? 'Organic ' : '';
        const variant = i === 0 ? '' : determineVariant(productType, i);
        
        // Construct final product object
        products.push({
          id: `${storeId}-${productType}-${i}-${Date.now()}`,
          storeId,
          store: {
            id: store.id,
            name: store.name,
            logo: store.logo,
            color: store.color
          },
          name: `${brandPrefix}${organicPrefix}${productName}${variant}`,
          ingredient,
          price: onSale ? salePrice : price,
          originalPrice: onSale ? price : null,
          unitSize: currentUnitSize,
          unitCount: currentUnitCount > 1 ? currentUnitCount : null,
          units,
          image: baseImage,
          inStock,
          isOrganic,
          isBrandName,
          isStoreOwn,
          onSale,
          nutritionTags,
          snapEligible: isSnapEligible,
          rating: (Math.random() * 2 + 3).toFixed(1), // 3-5 star rating
          reviewCount: Math.floor(Math.random() * 500) + 5,
          pricePerUnit: calculatePricePerUnit(
            onSale ? salePrice : price, 
            currentUnitSize, 
            currentUnitCount, 
            units
          )
        });
      }
    });
    
    // Apply sorting if specified
    if (options.sortBy) {
      const { sortBy, sortDirection = 'asc' } = options;
      products.sort((a, b) => {
        let comparison = 0;
        
        switch (sortBy) {
          case 'price':
            comparison = parseFloat(a.price) - parseFloat(b.price);
            break;
          case 'pricePerUnit':
            comparison = parseFloat(a.pricePerUnit.value) - parseFloat(b.pricePerUnit.value);
            break;
          case 'rating':
            comparison = parseFloat(b.rating) - parseFloat(a.rating);
            break;
          default:
            comparison = 0;
        }
        
        return sortDirection === 'desc' ? -comparison : comparison;
      });
    }
    
    // Apply filtering if specified
    if (options.filters) {
      const { 
        onlyInStock, 
        onlySnapEligible, 
        onlyOrganic,
        onlySale,
        maxPrice,
        stores: filterStores
      } = options.filters;
      
      return products.filter(product => {
        if (onlyInStock && !product.inStock) return false;
        if (onlySnapEligible && !product.snapEligible) return false;
        if (onlyOrganic && !product.isOrganic) return false;
        if (onlySale && !product.onSale) return false;
        if (maxPrice && parseFloat(product.price) > maxPrice) return false;
        if (filterStores && filterStores.length > 0 && !filterStores.includes(product.storeId)) return false;
        
        return true;
      });
    }
    
    return products;
  } catch (error) {
    console.error(`Error searching products for "${ingredient}":`, error);
    return [];
  }
};

/**
 * Get product details by ID
 * @param {string} productId - Product ID
 * @param {string} storeId - Store ID
 * @returns {Promise<Object>} - Product details
 */
export const getProductDetails = async (productId, storeId) => {
  try {
    // In production, this would call the store's API for detailed product info
    // For demo, we'll return what we already have
    const products = await searchProductsByIngredient('', [storeId]);
    return products.find(p => p.id === productId) || null;
  } catch (error) {
    console.error(`Error fetching product details for ${productId}:`, error);
    return null;
  }
};

// Helper functions for generating synthetic product data

// Generate a base price for an ingredient
function generateBasePrice(ingredient) {
  // Generate realistic prices based on ingredient type
  if (ingredient.includes('milk')) return 3.49;
  if (ingredient.includes('yogurt')) return 4.29;
  if (ingredient.includes('cheese')) return 4.99;
  if (ingredient.includes('apple')) return 0.79;
  if (ingredient.includes('banana')) return 0.59;
  if (ingredient.includes('berries')) return 3.99;
  if (ingredient.includes('broccoli')) return 1.99;
  if (ingredient.includes('spinach')) return 2.49;
  if (ingredient.includes('kale')) return 2.49;
  if (ingredient.includes('carrot')) return 1.29;
  if (ingredient.includes('pepper')) return 1.49;
  if (ingredient.includes('onion')) return 0.99;
  if (ingredient.includes('garlic')) return 0.89;
  if (ingredient.includes('olive oil')) return 7.99;
  if (ingredient.includes('bread')) return 3.49;
  if (ingredient.includes('rice')) return 2.49;
  if (ingredient.includes('pasta')) return 1.79;
  if (ingredient.includes('oat')) return 3.99;
  if (ingredient.includes('flour')) return 2.99;
  if (ingredient.includes('sugar')) return 2.49;
  if (ingredient.includes('nut') || ingredient.includes('almond')) return 5.99;
  if (ingredient.includes('seed')) return 3.99;
  if (ingredient.includes('salmon')) return 9.99;
  if (ingredient.includes('fish')) return 8.99;
  if (ingredient.includes('chicken')) return 4.99;
  if (ingredient.includes('beef')) return 5.99;
  
  // Default price for unknown ingredients
  return (Math.random() * 4 + 1.99).toFixed(2);
}

// Generate a product name from an ingredient
function generateProductName(ingredient) {
  // Clean up the ingredient description into a product name
  let name = ingredient
    .replace(/^(\d+\/?\d*)\s+/, '') // Remove leading measurements
    .replace(/cup|tablespoon|teaspoon|tbsp|tsp|oz|ounce|pound|lb|gram|g/, '') // Remove units
    .replace(/\b(of|fresh|frozen|chopped|diced|sliced|minced|ground)\b/g, '') // Remove prep words
    .replace(/\(.*\)/, '') // Remove parenthetical notes
    .replace(/,.*$/, '') // Remove anything after a comma
    .trim();
  
  // Capitalize first letter of each word
  return name.split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

// Determine appropriate units for an ingredient
function determineUnits(ingredient) {
  if (ingredient.includes('milk') || 
      ingredient.includes('oil') || 
      ingredient.includes('juice') || 
      ingredient.includes('water') ||
      ingredient.includes('broth') ||
      ingredient.includes('soup')) {
    return 'fl oz';
  }
  
  if (ingredient.includes('cheese') ||
      ingredient.includes('meat') ||
      ingredient.includes('chicken') ||
      ingredient.includes('beef') ||
      ingredient.includes('pork') ||
      ingredient.includes('fish') ||
      ingredient.includes('salmon') ||
      ingredient.includes('nut') ||
      ingredient.includes('seed') ||
      ingredient.includes('flour') ||
      ingredient.includes('sugar') ||
      ingredient.includes('rice') ||
      ingredient.includes('oat')) {
    return 'oz';
  }
  
  if (ingredient.includes('apple') ||
      ingredient.includes('banana') ||
      ingredient.includes('orange') ||
      ingredient.includes('lemon') ||
      ingredient.includes('lime') ||
      ingredient.includes('avocado') ||
      ingredient.includes('onion') ||
      ingredient.includes('potato') ||
      ingredient.includes('egg')) {
    return 'ct';
  }
  
  // Default unit for most ingredients
  return 'oz';
}

// Determine base unit size for an ingredient
function determineUnitSize(ingredient) {
  if (ingredient.includes('milk')) return 64; // Half gallon
  if (ingredient.includes('yogurt')) return 32; // Quart
  if (ingredient.includes('cheese')) return 8; // 8 oz block
  if (ingredient.includes('olive oil')) return 16; // 16 oz bottle
  if (ingredient.includes('bread')) return 20; // 20 oz loaf
  if (ingredient.includes('rice') || 
      ingredient.includes('pasta') ||
      ingredient.includes('flour') ||
      ingredient.includes('sugar')) return 16; // 1 lb
  if (ingredient.includes('nut') || ingredient.includes('seed')) return 8; // 8 oz bag
  
  // Produce typically sold by count
  if (ingredient.includes('apple') ||
      ingredient.includes('banana') ||
      ingredient.includes('orange') ||
      ingredient.includes('lemon') ||
      ingredient.includes('lime') ||
      ingredient.includes('avocado') ||
      ingredient.includes('onion') ||
      ingredient.includes('potato')) return 1;
  
  // Default sizes
  return ingredient.includes('fl oz') ? 16 : 8;
}

// Determine unit count for an ingredient (for items sold in multi-packs)
function determineUnitCount(ingredient) {
  if (ingredient.includes('egg')) return 12; // Dozen eggs
  if (ingredient.includes('yogurt') && ingredient.includes('pack')) return 6;
  if (ingredient.includes('banana')) return 5; // Bunch
  if (ingredient.includes('pepper') || 
      ingredient.includes('tomato') ||
      ingredient.includes('lemon') ||
      ingredient.includes('lime')) return 3;
  
  // Most items sold individually
  return 1;
}

// Determine nutrition tags for an ingredient
function determineNutritionTags(ingredient) {
  const tags = [];
  
  // Dental health tags
  if (ingredient.includes('cheese') ||
      ingredient.includes('milk') ||
      ingredient.includes('yogurt') ||
      ingredient.includes('calcium')) {
    tags.push('Calcium-Rich');
  }
  
  if (ingredient.includes('broccoli') ||
      ingredient.includes('spinach') ||
      ingredient.includes('kale') ||
      ingredient.includes('carrot') ||
      ingredient.includes('apple') ||
      ingredient.includes('crunchy') ||
      ingredient.includes('vegetable')) {
    tags.push('Tooth-Safe');
  }
  
  // Dietary tags
  if (ingredient.includes('organic')) {
    tags.push('Organic');
  }
  
  if (!ingredient.includes('meat') &&
      !ingredient.includes('chicken') &&
      !ingredient.includes('beef') &&
      !ingredient.includes('pork') &&
      !ingredient.includes('fish') &&
      !ingredient.includes('salmon') &&
      !ingredient.includes('milk') &&
      !ingredient.includes('cheese') &&
      !ingredient.includes('yogurt')) {
    tags.push('Plant-Based');
  }
  
  if (ingredient.includes('nut') ||
      ingredient.includes('seed') ||
      ingredient.includes('fish') ||
      ingredient.includes('salmon')) {
    tags.push('Omega-3');
  }
  
  return tags;
}

// Determine product type for an ingredient
function determineProductType(ingredient) {
  if (ingredient.includes('milk')) return 'milk';
  if (ingredient.includes('yogurt')) return 'yogurt';
  if (ingredient.includes('cheese')) return 'cheese';
  if (ingredient.includes('apple')) return 'apple';
  if (ingredient.includes('banana')) return 'banana';
  if (ingredient.includes('berr')) return 'berries';
  if (ingredient.includes('broccoli')) return 'broccoli';
  if (ingredient.includes('spinach') || ingredient.includes('kale')) return 'leafyGreens';
  if (ingredient.includes('carrot')) return 'carrot';
  if (ingredient.includes('pepper')) return 'pepper';
  if (ingredient.includes('onion')) return 'onion';
  if (ingredient.includes('garlic')) return 'garlic';
  if (ingredient.includes('olive oil')) return 'oliveOil';
  if (ingredient.includes('bread')) return 'bread';
  if (ingredient.includes('rice')) return 'rice';
  if (ingredient.includes('pasta')) return 'pasta';
  if (ingredient.includes('oat')) return 'oats';
  if (ingredient.includes('flour')) return 'flour';
  if (ingredient.includes('sugar')) return 'sugar';
  if (ingredient.includes('nut') || ingredient.includes('almond')) return 'nuts';
  if (ingredient.includes('seed')) return 'seeds';
  if (ingredient.includes('salmon')) return 'salmon';
  if (ingredient.includes('fish')) return 'fish';
  if (ingredient.includes('chicken')) return 'chicken';
  if (ingredient.includes('beef')) return 'beef';
  
  // Default type
  return 'grocery';
}

// Determine a product image placeholder URL based on product type
function determineProductImage(productType, isOrganic) {
  const organicPrefix = isOrganic ? 'organic-' : '';
  
  // In a real app, these would be actual image paths
  // For now, use placeholder URLs
  const basePath = 'https://source.unsplash.com/featured/150x150/?';
  
  return `${basePath}${organicPrefix}${productType}`;
}

// Determine if a product is SNAP eligible
function determineSnapEligibility(productType) {
  // Most grocery items are SNAP eligible
  // Non-eligible items typically include:
  // - Alcohol, tobacco
  // - Hot prepared foods
  // - Vitamins, medicines
  
  return true; // For demo purposes, all our products are eligible
}

// Get common brands for a product type
function getCommonBrands(productType) {
  const brandsByType = {
    milk: ['Organic Valley', 'Horizon', 'Fairlife', 'Chobani', 'Land O Lakes'],
    yogurt: ['Chobani', 'Siggi\'s', 'Yoplait', 'Dannon', 'Fage', 'Oikos'],
    cheese: ['Tillamook', 'Kraft', 'Sargento', 'Cabot', 'Philadelphia'],
    bread: ['Arnold', 'Nature\'s Own', 'Dave\'s Killer Bread', 'Wonder', 'Sara Lee'],
    oliveOil: ['Bertolli', 'California Olive Ranch', 'Colavita', 'Filippo Berio'],
    pasta: ['Barilla', 'De Cecco', 'Ronzoni', 'Banza', 'Mueller\'s'],
    rice: ['Uncle Ben\'s', 'Lundberg', 'Minute', 'Mahatma', 'Texmati'],
    flour: ['Gold Medal', 'King Arthur', 'Bob\'s Red Mill', 'Pillsbury', 'Arrowhead Mills'],
    oats: ['Quaker', 'Bob\'s Red Mill', 'Nature\'s Path', 'McCann\'s', 'Cheerios'],
    nuts: ['Planters', 'Blue Diamond', 'Fisher', 'Wonderful', 'Kind'],
    salmon: ['Bumble Bee', 'Wild Planet', 'Chicken of the Sea', 'StarKist'],
    berries: ['Driscoll\'s', 'Naturipe', 'Dole', 'Fresh Express'],
    leafyGreens: ['Earthbound Farm', 'Dole', 'Fresh Express', 'Taylor Farms']
  };
  
  // Default to generic brands if no specific ones are available
  return brandsByType[productType] || [
    'Great Value', 
    'Simple Truth', 
    'Good & Gather', 
    '365', 
    'Signature Select'
  ];
}

// Determine product variant description
function determineVariant(productType, variantIndex) {
  const variantsByType = {
    milk: [' 2%', ' Whole', ' Skim', ' 1%'],
    yogurt: [' Greek', ' Plain', ' Vanilla', ' Strawberry'],
    cheese: [' Sharp', ' Mild', ' Medium', ' Extra Sharp'],
    bread: [' Whole Grain', ' White', ' Sourdough', ' Multigrain'],
    apple: [' Fuji', ' Gala', ' Honeycrisp', ' Granny Smith']
  };
  
  const variants = variantsByType[productType] || ['', ' Premium', ' Select', ' Special'];
  return variants[variantIndex % variants.length] || '';
}

// Calculate price per unit
function calculatePricePerUnit(price, unitSize, unitCount, units) {
  // Handle count-based items differently
  if (units === 'ct') {
    const count = unitCount || 1;
    return {
      value: (parseFloat(price) / count).toFixed(2),
      unit: 'each'
    };
  }
  
  // Calculate price per ounce or fluid ounce
  const totalUnits = unitSize * (unitCount || 1);
  const value = (parseFloat(price) / totalUnits).toFixed(2);
  
  if (units === 'fl oz') {
    return {
      value,
      unit: 'fl oz'
    };
  }
  
  return {
    value,
    unit: 'oz'
  };
}

export default {
  getNearbyStores,
  searchProductsByIngredient,
  getProductDetails
}; 