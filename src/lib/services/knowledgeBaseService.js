import { supabase } from '../supabase';
import { DENTAL_KNOWLEDGE } from '../constants';

/**
 * Get knowledge about a specific dental topic
 * @param {string} topic - The dental topic to get knowledge about
 * @returns {Promise<Object>} - Knowledge about the topic
 */
export const getDentalKnowledge = async (topic) => {
  try {
    // First, check if we have this topic in our local knowledge base
    const localKnowledge = getLocalKnowledge(topic);
    if (localKnowledge) {
      return { source: 'local', data: localKnowledge };
    }
    
    // If not found locally, check Supabase
    const { data, error } = await supabase
      .from('dental_knowledge')
      .select('*')
      .ilike('topic', `%${topic}%`)
      .limit(1);
    
    if (error) throw error;
    
    if (data && data.length > 0) {
      return { source: 'database', data: data[0].content };
    }
    
    // If not found in database, return null
    return null;
  } catch (error) {
    console.error('Error getting dental knowledge:', error);
    throw new Error('Failed to get dental knowledge');
  }
};

/**
 * Get knowledge from the local knowledge base
 * @param {string} topic - The dental topic to get knowledge about
 * @returns {Object|null} - Knowledge about the topic or null if not found
 */
const getLocalKnowledge = (topic) => {
  const lowerTopic = topic.toLowerCase();
  
  // Check for matches in our local knowledge base
  if (lowerTopic.includes('caries') || lowerTopic.includes('cavity') || lowerTopic.includes('decay')) {
    return DENTAL_KNOWLEDGE.commonConditions.caries;
  } else if (lowerTopic.includes('gum') || lowerTopic.includes('periodont')) {
    return DENTAL_KNOWLEDGE.commonConditions.periodontal;
  } else if (lowerTopic.includes('pulp') || lowerTopic.includes('root canal') || lowerTopic.includes('endodont')) {
    return DENTAL_KNOWLEDGE.commonConditions.endodontic;
  } else if (lowerTopic.includes('bite') || lowerTopic.includes('occlus') || lowerTopic.includes('align')) {
    return DENTAL_KNOWLEDGE.commonConditions.occlusal;
  } else if (lowerTopic.includes('emergency') || lowerTopic.includes('pain') || lowerTopic.includes('swelling')) {
    return { emergencyConditions: DENTAL_KNOWLEDGE.emergencyConditions };
  } else if (lowerTopic.includes('x-ray') || lowerTopic.includes('radiograph')) {
    return DENTAL_KNOWLEDGE.radiographicFindings;
  } else if (lowerTopic.includes('filling') || lowerTopic.includes('crown') || lowerTopic.includes('restor')) {
    return DENTAL_KNOWLEDGE.restorations;
  } else if (lowerTopic.includes('prevent') || lowerTopic.includes('brush') || lowerTopic.includes('floss')) {
    return DENTAL_KNOWLEDGE.preventiveMeasures;
  } else if (lowerTopic.includes('tooth') || lowerTopic.includes('teeth') || lowerTopic.includes('number')) {
    return DENTAL_KNOWLEDGE.toothNumberingSystems;
  }
  
  return null;
};

/**
 * Store new dental knowledge in the database
 * @param {string} topic - The dental topic
 * @param {Object} content - The knowledge content
 * @param {string} source - The source of the knowledge
 * @returns {Promise<Object>} - The stored knowledge
 */
export const storeDentalKnowledge = async (topic, content, source) => {
  try {
    // Check if this topic already exists
    const { data: existingData, error: checkError } = await supabase
      .from('dental_knowledge')
      .select('id')
      .eq('topic', topic.toLowerCase())
      .limit(1);
    
    if (checkError) throw checkError;
    
    if (existingData && existingData.length > 0) {
      // Update existing knowledge
      const { data, error } = await supabase
        .from('dental_knowledge')
        .update({
          content,
          source,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingData[0].id)
        .select();
      
      if (error) throw error;
      return data[0];
    } else {
      // Insert new knowledge
      const { data, error } = await supabase
        .from('dental_knowledge')
        .insert({
          topic: topic.toLowerCase(),
          content,
          source,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select();
      
      if (error) throw error;
      return data[0];
    }
  } catch (error) {
    console.error('Error storing dental knowledge:', error);
    throw new Error('Failed to store dental knowledge');
  }
};

/**
 * Get the most frequently accessed dental topics
 * @param {number} limit - Maximum number of topics to return
 * @returns {Promise<Array>} - Array of popular topics
 */
export const getPopularDentalTopics = async (limit = 10) => {
  try {
    const { data, error } = await supabase
      .from('dental_knowledge')
      .select('topic, access_count')
      .order('access_count', { ascending: false })
      .limit(limit);
    
    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error getting popular dental topics:', error);
    throw new Error('Failed to get popular dental topics');
  }
};

/**
 * Record a user query to improve the knowledge base
 * @param {string} question - The user's query
 * @param {string} userId - The user's ID (optional)
 * @returns {Promise<void>}
 */
export const recordUserQuery = async (question, userId = null) => {
  // Don't attempt to record if question is empty
  if (!question?.trim()) return;
  
  try {
    // Create the query record
    const { error } = await supabase
      .from('user_queries')
      .insert({
        user_id: userId,
        query: question.trim(),
        timestamp: new Date().toISOString()
      });
    
    // Log success or error but don't throw
    if (error) {
      console.log('Note: Unable to record user query -', error.message);
    }
  } catch (err) {
    // Just log the error but don't fail the chat functionality
    console.log('Failed to record user query:', err.message);
  }
  
  // Always return successfully to ensure chat continues to work
  return;
}; 