import { config } from '../config';

// Base URL for the places proxy server
const PROXY_BASE_URL = '/api/places';

/**
 * Fetches dental school reviews from Google Places API via proxy
 * @param {Object} coordinates - The coordinates of the school
 * @param {string} name - The name of the school to search for
 * @returns {Promise<Array>} - Array of reviews
 */
export const fetchDentalSchoolReviews = async (coordinates, name) => {
  try {
    // First, find the place ID using the Places API
    const placeIdUrl = `${PROXY_BASE_URL}/search?query=${encodeURIComponent(name)}&type=university`;
    
    const response = await fetch(placeIdUrl);
    const data = await response.json();
    
    if (!data.results || data.results.length === 0) {
      console.warn(`No place found for: ${name}`);
      return [];
    }
    
    const placeId = data.results[0].place_id;
    
    // Then, get the details including reviews
    const fields = 'name,rating,review,formatted_address,formatted_phone_number,website,user_ratings_total,price_level,opening_hours';
    const detailsUrl = `${PROXY_BASE_URL}/details?place_id=${placeId}&fields=${fields}`;
    
    const detailsResponse = await fetch(detailsUrl);
    const detailsData = await detailsResponse.json();
    
    if (!detailsData.result) {
      console.warn(`No details found for place ID: ${placeId}`);
      return [];
    }
    
    return processReviews(detailsData.result);
  } catch (error) {
    console.error('Error fetching dental school reviews:', error);
    return [];
  }
};

/**
 * Process the reviews from the Google Places API response
 * @param {Object} placeDetails - The place details from Google Places API
 * @returns {Object} - Processed reviews and place information
 */
const processReviews = (placeDetails) => {
  const { 
    name, 
    rating, 
    user_ratings_total: totalRatings, 
    reviews = [], 
    formatted_address: address,
    formatted_phone_number: phone,
    website,
    opening_hours
  } = placeDetails;
  
  // Process each review
  const processedReviews = reviews.map(review => ({
    id: review.time, // Using timestamp as ID
    author: review.author_name,
    rating: review.rating,
    text: review.text,
    time: review.relative_time_description,
    avatar: review.profile_photo_url,
    language: review.language
  }));
  
  // Create rating distribution
  const ratingCounts = [0, 0, 0, 0, 0]; // 1-5 stars
  reviews.forEach(review => {
    if (review.rating >= 1 && review.rating <= 5) {
      ratingCounts[review.rating - 1]++;
    }
  });
  
  // Calculate percentages for each star rating
  const ratingDistribution = ratingCounts.map((count, index) => ({
    stars: index + 1,
    count,
    percentage: totalRatings ? Math.round((count / totalRatings) * 100) : 0
  }));
  
  // Format opening hours if available
  const formattedHours = opening_hours?.weekday_text || [];
  
  return {
    placeInfo: {
      name,
      address,
      phone,
      website,
      hours: formattedHours,
      overallRating: rating,
      totalRatings,
      ratingDistribution
    },
    reviews: processedReviews
  };
};

/**
 * Gets nearby dental schools based on user location
 * @param {Object} coordinates - User coordinates {latitude, longitude}
 * @param {number} radius - Search radius in meters
 * @returns {Promise<Array>} - Array of nearby dental schools
 */
export const getNearbyDentalSchools = async (coordinates, radius = 50000) => {
  if (!coordinates?.latitude || !coordinates?.longitude) {
    console.warn('Invalid coordinates provided to getNearbyDentalSchools');
    return [];
  }
  
  try {
    const location = `${coordinates.latitude},${coordinates.longitude}`;
    const url = `${PROXY_BASE_URL}/nearby?location=${location}&radius=${radius}&type=university&keyword=dental+school`;
    
    const response = await fetch(url);
    const data = await response.json();
    
    if (!data.results || data.results.length === 0) {
      console.warn('No nearby dental schools found');
      return [];
    }
    
    return data.results.map(place => ({
      id: place.place_id,
      name: place.name,
      address: place.vicinity,
      coordinates: {
        latitude: place.geometry.location.lat,
        longitude: place.geometry.location.lng
      },
      rating: place.rating,
      totalRatings: place.user_ratings_total,
      distance: calculateDistance(
        coordinates.latitude, 
        coordinates.longitude, 
        place.geometry.location.lat, 
        place.geometry.location.lng
      )
    }));
  } catch (error) {
    console.error('Error fetching nearby dental schools:', error);
    return [];
  }
};

/**
 * Calculates distance between two coordinates
 * @param {number} lat1 - Latitude of first point
 * @param {number} lon1 - Longitude of first point
 * @param {number} lat2 - Latitude of second point
 * @param {number} lon2 - Longitude of second point
 * @returns {number} - Distance in miles
 */
const calculateDistance = (lat1, lon1, lat2, lon2) => {
  const R = 3958.8; // Earth's radius in miles
  const dLat = toRad(lat2 - lat1);
  const dLon = toRad(lon2 - lon1);
  
  const a = 
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRad(lat1)) * Math.cos(toRad(lat2)) * 
    Math.sin(dLon / 2) * Math.sin(dLon / 2);
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c;
  
  return Math.round(distance * 10) / 10; // Round to 1 decimal place
};

/**
 * Converts degrees to radians
 * @param {number} degrees - Degrees to convert
 * @returns {number} - Radians
 */
const toRad = (degrees) => {
  return degrees * Math.PI / 180;
};

/**
 * Search for dental schools by query
 * @param {string} query - Search query
 * @returns {Promise<Array>} - Array of dental schools matching the query
 */
export const searchDentalSchools = async (query) => {
  try {
    const url = `${PROXY_BASE_URL}/search?query=${encodeURIComponent(query + ' dental school')}&type=university`;
    
    const response = await fetch(url);
    const data = await response.json();
    
    if (!data.results || data.results.length === 0) {
      console.warn(`No dental schools found for query: ${query}`);
      return [];
    }
    
    return data.results.map(place => ({
      id: place.place_id,
      name: place.name,
      address: place.formatted_address,
      coordinates: {
        latitude: place.geometry.location.lat,
        longitude: place.geometry.location.lng
      },
      rating: place.rating,
      totalRatings: place.user_ratings_total
    }));
  } catch (error) {
    console.error('Error searching dental schools:', error);
    return [];
  }
};

/**
 * Gets photos for a dental school
 * @param {string} placeId - Google Place ID
 * @param {number} maxWidth - Maximum width of the photo
 * @returns {Promise<Array>} - Array of photo URLs
 */
export const getDentalSchoolPhotos = async (placeId, maxWidth = 400) => {
  try {
    const url = `${PROXY_BASE_URL}/details?place_id=${placeId}&fields=photos`;
    
    const response = await fetch(url);
    const data = await response.json();
    
    if (!data.result?.photos || data.result.photos.length === 0) {
      console.warn(`No photos found for place ID: ${placeId}`);
      return [];
    }
    
    return data.result.photos.map(photo => {
      return `${PROXY_BASE_URL}/photo?photo_reference=${photo.photo_reference}&maxwidth=${maxWidth}`;
    });
  } catch (error) {
    console.error('Error fetching dental school photos:', error);
    return [];
  }
}; 