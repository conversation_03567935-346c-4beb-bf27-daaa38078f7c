import { supabase as browserSupabase } from '../supabase.js';
import { XMLParser } from 'fast-xml-parser';
import { JSDOM } from 'jsdom';
import { encode } from 'html-entities';

// Use global supabase client if available (for Node.js scripts), otherwise use browser client
const supabase = global.supabase || browserSupabase;

// Define trusted sources for RSS feeds
const TRUSTED_SOURCES = {
  ADA: {
    name: 'American Dental Association',
    feedUrl: 'https://www.ada.org/publications/ada-news/feeds/adanews',
    type: 'rss'
  },
  DENTAL_IQ: {
    name: 'DentistryIQ',
    feedUrl: 'https://www.dentistryiq.com/rss/all-content.feed',
    type: 'rss'
  },
  DENTAL_ECONOMICS: {
    name: 'Dental Economics',
    feedUrl: 'https://www.dentaleconomics.com/rss/all-content.feed',
    type: 'rss'
  },
  COLGATE_ORAL_HEALTH: {
    name: 'Colgate Oral Health Network',
    feedUrl: 'https://www.colgateprofessional.com/news/rss',
    type: 'rss'
  },
  DENTAL_TRIBUNE: {
    name: 'Dental Tribune',
    feedUrl: 'https://www.dental-tribune.com/feed/',
    type: 'rss'
  }
};

// Helper function to extract items from an RSS feed
const extractItems = (feed) => {
  if (feed.rss?.channel?.item) {
    return Array.isArray(feed.rss.channel.item) ? feed.rss.channel.item : [feed.rss.channel.item];
  }
  if (feed.feed?.entry) {
    return Array.isArray(feed.feed.entry) ? feed.feed.entry : [feed.feed.entry];
  }
  if (feed.rdf?.item) {
    return Array.isArray(feed.rdf.item) ? feed.rdf.item : [feed.rdf.item];
  }
  return [];
};

// Extract and clean content
const extractContent = (item) => {
  const content = item.description || 
                 item['content:encoded'] || 
                 item.content?.['#text'] ||
                 item.content ||
                 item.summary ||
                 '';
                 
  // Clean the content
  const dom = new JSDOM(content);
  const text = dom.window.document.body.textContent || '';
  return text.trim();
};

// Extract and clean title
const extractTitle = (item) => {
  const title = item.title?.['#text'] || item.title || '';
  const dom = new JSDOM(title);
  return dom.window.document.body.textContent?.trim() || '';
};

// Extract and clean link
const extractLink = (item) => {
  const link = item.link?.['#text'] || item.link || '';
  return link.trim();
};

// Extract summary from content
const extractSummary = (content) => {
  const dom = new JSDOM(content);
  const text = dom.window.document.body.textContent || '';
  return text.slice(0, 300) + (text.length > 300 ? '...' : '');
};

// Extract and normalize tags
const extractTags = (categories) => {
  if (!categories) return [];
  const tags = Array.isArray(categories) ? categories : [categories];
  return tags.map(tag => tag.toLowerCase().trim());
};

// Simple delay function for retries
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Fetch with retry logic
const fetchWithRetry = async (url, options, maxRetries = 3) => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const response = await fetch(url, options);
      if (response.ok) {
        return response;
      }
      
      throw new Error(`HTTP error! status: ${response.status}`);
    } catch (error) {
      if (attempt === maxRetries) {
        throw error;
      }
      
      const delay = 1000 * Math.pow(2, attempt);
      console.log(`Retry attempt ${attempt} for ${url} after ${delay}ms`);
      await sleep(delay);
    }
  }
};

// Fetch articles from an RSS feed
const fetchRssFeed = async (source) => {
  try {
    console.log(`Fetching from ${source.name}...`);
    const response = await fetchWithRetry(source.feedUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; SmiloBot/1.0; +https://smilo.dental)',
        'Accept': 'application/rss+xml, application/xml, text/xml, application/atom+xml'
      }
    });
    
    const xmlData = await response.text();
    
    // Validate XML data
    if (!xmlData.trim() || !xmlData.includes('<?xml')) {
      throw new Error('Invalid XML data received');
    }
    
    const parser = new XMLParser({
      ignoreAttributes: false,
      attributeNamePrefix: "@_",
      parseAttributeValue: true,
      trimValues: true
    });
    
    const feed = parser.parse(xmlData);
    const items = extractItems(feed);
    
    if (!items.length) {
      console.warn(`No items found in feed from ${source.name}`);
      return [];
    }
    
    // Process each item to create articles
    const articles = items.map(item => {
      const content = extractContent(item);
      return {
        title: extractTitle(item),
        content: content,
        summary: extractSummary(content),
        source: source.name,
        source_type: 'rss',
        source_url: source.feedUrl,
        link: extractLink(item),
        tags: extractTags(item.category || item.tags),
        status: 'published',
        is_featured: false
      };
    });
    
    // Filter out invalid articles
    return articles.filter(article => 
      article.title && article.title.trim() && 
      article.content && article.content.trim()
    );
  } catch (error) {
    console.error(`Error fetching RSS feed from ${source.name}:`, error);
    return [];
  }
};

// Check for duplicate articles
const isDuplicate = async (article) => {
  const { data, error } = await supabase
    .from('dental_articles')
    .select('id')
    .eq('title', article.title)
    .limit(1);
    
  if (error) {
    console.error('Error checking for duplicates:', error);
    return false; // Assume not duplicate on error
  }
  
  return data && data.length > 0;
};

// Store article in the database
const storeArticle = async (article) => {
  try {
    const duplicate = await isDuplicate(article);
    if (duplicate) {
      console.log(`Skipping duplicate article: ${article.title}`);
      return { success: false, reason: 'duplicate' };
    }
    
    // Insert article into database
    const { data, error } = await supabase
      .from('dental_articles')
      .insert([article])
      .select();
      
    if (error) {
      console.error('Error storing article:', error);
      return { success: false, reason: 'database_error', error };
    }
    
    console.log(`Stored article: ${article.title}`);
    return { success: true, articleId: data[0].id };
  } catch (error) {
    console.error('Error in storeArticle:', error);
    return { success: false, reason: 'exception', error };
  }
};

// Fetch and store articles from all sources
const fetchAndStoreArticles = async () => {
  const metrics = {
    sources: Object.keys(TRUSTED_SOURCES).length,
    fetched: 0,
    stored: 0,
    skipped: 0,
    failed: 0
  };
  
  console.log(`Starting article fetch from ${metrics.sources} sources...`);
  
  for (const sourceKey of Object.keys(TRUSTED_SOURCES)) {
    const source = TRUSTED_SOURCES[sourceKey];
    try {
      const articles = await fetchRssFeed(source);
      metrics.fetched += articles.length;
      
      for (const article of articles) {
        const result = await storeArticle(article);
        if (result.success) {
          metrics.stored++;
        } else if (result.reason === 'duplicate') {
          metrics.skipped++;
        } else {
          metrics.failed++;
        }
      }
    } catch (error) {
      console.error(`Failed to process source ${source.name}:`, error);
      metrics.failed++;
    }
  }
  
  console.log('Article fetch completed:', metrics);
  return metrics;
};

// Export necessary functions and constants
export {
  TRUSTED_SOURCES,
  fetchRssFeed,
  fetchAndStoreArticles
}; 