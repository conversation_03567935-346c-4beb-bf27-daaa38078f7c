import { supabase } from '../supabase';
import { calculateDistance } from './location/locationService';

// Mock data for when Supabase connection fails
const MOCK_DENTAL_SCHOOLS = [
  {
    id: 1,
    name: "University of California, San Francisco",
    address: "707 Parnassus Ave",
    city: "San Francisco",
    state: "CA",
    zip: "94143",
    latitude: 37.7629,
    longitude: -122.4581,
    phone: "(*************",
    website: "https://dentistry.ucsf.edu/",
    clinic_info: "Student clinic available for reduced-cost care",
    student_clinic_hours: "Mon-Fri: 9AM-5PM",
    payment_info: "Accepts insurance, Medicare/Medicaid, sliding scale payments",
    school_programs: [
      { program_name: "DDS", degree_type: "Doctorate", description: "Doctor of Dental Surgery" },
      { program_name: "Orthodontics", degree_type: "Specialty", description: "Orthodontic specialty program" }
    ],
    clinic_services: [
      { service_name: "General Cleaning", description: "Basic dental cleaning", price_range: "$50-100" },
      { service_name: "Fillings", description: "Dental fillings and restorations", price_range: "$75-200" }
    ]
  },
  {
    id: 2,
    name: "New York University College of Dentistry",
    address: "345 E 24th St",
    city: "New York",
    state: "NY",
    zip: "10010",
    latitude: 40.7382,
    longitude: -73.9774,
    phone: "(*************",
    website: "https://dental.nyu.edu/",
    clinic_info: "Student clinic offers comprehensive care at reduced rates",
    student_clinic_hours: "Mon-Sat: 8AM-5PM",
    payment_info: "Accepts most insurance plans, reduced fees for uninsured",
    school_programs: [
      { program_name: "DDS", degree_type: "Doctorate", description: "Doctor of Dental Surgery" }
    ],
    clinic_services: [
      { service_name: "Preventive Care", description: "Cleanings and check-ups", price_range: "$65-120" },
      { service_name: "Crowns", description: "Dental crowns", price_range: "$500-800" }
    ]
  },
  {
    id: 3,
    name: "University of Michigan School of Dentistry",
    address: "1011 N University Ave",
    city: "Ann Arbor",
    state: "MI",
    zip: "48109",
    latitude: 42.2808,
    longitude: -83.7430,
    phone: "(*************",
    website: "https://www.dent.umich.edu/",
    clinic_info: "Student and faculty clinics available",
    student_clinic_hours: "Mon-Fri: 8AM-5PM",
    payment_info: "Accepts most insurance, discount program available",
    school_programs: [
      { program_name: "DDS", degree_type: "Doctorate", description: "Doctor of Dental Surgery" },
      { program_name: "Periodontics", degree_type: "Specialty", description: "Periodontal specialty program" }
    ],
    clinic_services: [
      { service_name: "General Dentistry", description: "Comprehensive dental care", price_range: "Varies" },
      { service_name: "Orthodontics", description: "Braces and aligners", price_range: "$3000-6000" }
    ]
  }
];

export const getNearbyDentalSchools = async (coords, radius = 25) => {
  try {
    console.log('Searching for schools with params:', { coords, radius });

    // Fetch all schools from Supabase
    const { data: schools, error } = await supabase
      .from('dental_schools')
      .select(`
        id,
        name,
        address,
        city,
        state,
        zip,
        latitude,
        longitude,
        phone,
        website,
        clinic_info,
        student_clinic_hours,
        payment_info,
        school_programs (
          program_name,
          degree_type,
          description
        ),
        clinic_services (
          service_name,
          description,
          price_range
        )
      `);

    if (error) {
      console.error('Supabase error, falling back to mock data:', error);
      // If Supabase fails, use mock data
      const schoolsData = MOCK_DENTAL_SCHOOLS;
      console.log('Using mock data with length:', schoolsData.length);
      
      // Calculate distances for mock schools
      const schoolsWithDistance = schoolsData.map(school => {
        const distance = calculateDistance(
          coords.latitude,
          coords.longitude,
          school.latitude,
          school.longitude
        );
        
        // Format programs for easier use
        const programs = school.school_programs?.map(p => p.program_name) || [];
        
        // Format services
        const services = school.clinic_services || [];

        return { 
          ...school,
          distance,
          programs,
          services
        };
      });

      // Filter by radius if specified
      const filteredSchools = radius === Infinity 
        ? schoolsWithDistance 
        : schoolsWithDistance.filter(school => school.distance <= radius);
      
      // Sort by distance
      const sortedSchools = filteredSchools.sort((a, b) => a.distance - b.distance);

      console.log(`Found ${sortedSchools.length} mock schools within ${radius} miles`);
      return sortedSchools;
    }

    console.log('Total available schools from Supabase:', schools?.length || 0);

    // If no schools from Supabase, use mock data as fallback
    if (!schools || schools.length === 0) {
      console.log('No schools found in Supabase, using mock data');
      return getNearbyDentalSchools(coords, radius);
    }

    // Calculate distances for all schools
    const schoolsWithDistance = schools.map(school => {
      const distance = calculateDistance(
        coords.latitude,
        coords.longitude,
        school.latitude,
        school.longitude
      );
      
      // Format programs for easier use
      const programs = school.school_programs?.map(p => p.program_name) || [];
      
      // Format services
      const services = school.clinic_services || [];

      return { 
        ...school,
        distance,
        programs,
        services
      };
    });

    // Filter by radius if specified
    const filteredSchools = radius === Infinity 
      ? schoolsWithDistance 
      : schoolsWithDistance.filter(school => school.distance <= radius);
    
    // Sort by distance
    const sortedSchools = filteredSchools.sort((a, b) => a.distance - b.distance);

    console.log(`Found ${sortedSchools.length} schools within ${radius} miles`);
    return sortedSchools;
  } catch (error) {
    console.error('Error in dental school search:', error);
    
    // Use mock data as fallback
    console.log('Error occurred, falling back to mock data');
    const schoolsData = MOCK_DENTAL_SCHOOLS;
    
    // Calculate distances for mock schools
    const schoolsWithDistance = schoolsData.map(school => {
      const distance = calculateDistance(
        coords.latitude,
        coords.longitude,
        school.latitude,
        school.longitude
      );
      
      const programs = school.school_programs?.map(p => p.program_name) || [];
      const services = school.clinic_services || [];

      return { 
        ...school,
        distance,
        programs,
        services
      };
    });

    // Filter by radius
    const filteredSchools = radius === Infinity 
      ? schoolsWithDistance 
      : schoolsWithDistance.filter(school => school.distance <= radius);
    
    // Sort by distance
    const sortedSchools = filteredSchools.sort((a, b) => a.distance - b.distance);

    console.log(`Found ${sortedSchools.length} mock schools within ${radius} miles`);
    return sortedSchools;
  }
};

export const getSchoolById = async (schoolId) => {
  try {
    const { data, error } = await supabase
      .from('dental_schools')
      .select(`
        *,
        school_programs (
          program_name,
          degree_type,
          description
        ),
        clinic_services (
          service_name,
          description,
          price_range
        )
      `)
      .eq('id', schoolId)
      .single();

    if (error) {
      console.error('Error fetching school details, using mock:', error);
      // Return a mock school if real data not available
      return MOCK_DENTAL_SCHOOLS.find(s => s.id.toString() === schoolId.toString()) || MOCK_DENTAL_SCHOOLS[0];
    }
    
    return data;
  } catch (error) {
    console.error('Error fetching school details:', error);
    // Return a mock school as fallback
    return MOCK_DENTAL_SCHOOLS.find(s => s.id.toString() === schoolId.toString()) || MOCK_DENTAL_SCHOOLS[0];
  }
};