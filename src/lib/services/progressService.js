import { supabase } from '../supabase';
import { handleApiError } from '../utils/errorHandler';

export const loadUserProgress = async (userId) => {
  try {
    const { data, error } = await supabase
      .from('predental_progress')
      .select(`
        *,
        courses:predental_courses(*)
      `)
      .eq('user_id', userId)
      .single();

    if (error && error.code !== 'PGRST116') throw error;
    return data;
  } catch (error) {
    console.error('Error loading progress:', error);
    throw new Error(handleApiError(error));
  }
};

export const updateUserProgress = async (userId, updates) => {
  try {
    const { data, error } = await supabase
      .from('predental_progress')
      .upsert({
        user_id: userId,
        ...updates,
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error updating progress:', error);
    throw new Error(handleApiError(error));
  }
};

export const addCourse = async (userId, courseData) => {
  try {
    const { data, error } = await supabase
      .from('predental_courses')
      .insert([{
        user_id: userId,
        ...courseData,
        created_at: new Date().toISOString()
      }])
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error adding course:', error);
    throw new Error(handleApiError(error));
  }
};

export const updateCourse = async (userId, courseId, updates) => {
  try {
    const { data, error } = await supabase
      .from('predental_courses')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', courseId)
      .eq('user_id', userId)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error updating course:', error);
    throw new Error(handleApiError(error));
  }
};

export const deleteCourse = async (userId, courseId) => {
  try {
    const { error } = await supabase
      .from('predental_courses')
      .delete()
      .eq('id', courseId)
      .eq('user_id', userId);

    if (error) throw error;
  } catch (error) {
    console.error('Error deleting course:', error);
    throw new Error(handleApiError(error));
  }
};