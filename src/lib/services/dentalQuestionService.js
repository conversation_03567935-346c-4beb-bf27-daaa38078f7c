import { supabase } from '../supabase';
import { config } from '../config';
import { 
  comprehensiveDentalSearch, 
  searchPubMedArticles, 
  fetchADAGuidelines, 
  searchCochraneReviews,
  fetchWHOOralHealthStats
} from './dentalDatabaseService';

// Question types and their confidence thresholds
const QUESTION_TYPES = {
  CLINICAL: 'clinical',
  RESEARCH: 'research',
  PATIENT_EDUCATION: 'patient_education',
  PROCEDURE: 'procedure',
  MATERIAL: 'material',
  EPIDEMIOLOGY: 'epidemiology'
};

const CONFIDENCE_THRESHOLDS = {
  HIGH: 0.85,
  MEDIUM: 0.7,
  LOW: 0.5
};

/**
 * Answer a dental question using AI and dental databases
 * @param {string} question - The dental question to answer
 * @param {string} context - Optional additional context about the question
 * @returns {Promise<Object>} - Answer results
 */
export const answerDentalQuestion = async (question, context = '') => {
  try {
    // Check if answer already exists in cache
    const { data: cachedAnswer } = await supabase
      .from('question_answer_cache')
      .select('*')
      .eq('question_hash', generateQuestionHash(question))
      .order('created_at', { ascending: false })
      .limit(1);
    
    // Return cached answer if found and not older than 7 days
    if (cachedAnswer && cachedAnswer.length > 0) {
      const cacheAge = new Date() - new Date(cachedAnswer[0].created_at);
      const cacheAgeInDays = cacheAge / (1000 * 60 * 60 * 24);
      
      if (cacheAgeInDays < 7) {
        console.log('Using cached question answer');
        return cachedAnswer[0].answer_data;
      }
    }
    
    // Categorize the question type
    const questionType = categorizeQuestion(question);
    
    // Search for relevant information
    const searchResults = await comprehensiveDentalSearch(question);
    
    // Generate an answer based on the search results
    const answer = generateAnswer(question, questionType, searchResults, context);
    
    // Cache the answer
    await supabase
      .from('question_answer_cache')
      .insert({
        question: question,
        question_hash: generateQuestionHash(question),
        question_type: questionType,
        answer_data: answer,
        created_at: new Date().toISOString()
      });
    
    return answer;
  } catch (error) {
    console.error('Error answering dental question:', error);
    return {
      error: 'Failed to answer dental question',
      details: error.message
    };
  }
};

/**
 * Generate follow-up questions based on an initial question and its answer
 * @param {string} originalQuestion - The original question asked
 * @param {Object} answerData - The answer data generated
 * @param {number} limit - Maximum number of follow-up questions to generate
 * @returns {Promise<Array>} - List of follow-up questions
 */
export const generateFollowUpQuestions = async (originalQuestion, answerData, limit = 3) => {
  try {
    // Determine the question type and topic
    const questionType = categorizeQuestion(originalQuestion);
    const topics = extractKeyTopics(originalQuestion, answerData);
    
    // Generate follow-up questions based on question type and extracted topics
    const followUps = [];
    
    // Clinical questions follow-ups
    if (questionType === QUESTION_TYPES.CLINICAL && topics.conditions.length > 0) {
      followUps.push(
        `What are the latest treatment options for ${topics.conditions[0]}?`,
        `What are the risk factors for developing ${topics.conditions[0]}?`,
        `How does ${topics.conditions[0]} typically progress if left untreated?`
      );
    }
    
    // Procedure questions follow-ups
    if (questionType === QUESTION_TYPES.PROCEDURE && topics.procedures.length > 0) {
      followUps.push(
        `What are the success rates for ${topics.procedures[0]}?`,
        `What are the alternatives to ${topics.procedures[0]}?`,
        `What is the recovery time for ${topics.procedures[0]}?`
      );
    }
    
    // Research questions follow-ups
    if (questionType === QUESTION_TYPES.RESEARCH) {
      followUps.push(
        `Are there any recent clinical trials related to this topic?`,
        `What systematic reviews are available on this subject?`,
        `How has the research in this area evolved over the past 5 years?`
      );
    }
    
    // Material questions follow-ups
    if (questionType === QUESTION_TYPES.MATERIAL && topics.materials.length > 0) {
      followUps.push(
        `What are the advantages of ${topics.materials[0]} compared to alternatives?`,
        `Are there any contraindications for using ${topics.materials[0]}?`,
        `What is the longevity of ${topics.materials[0]}?`
      );
    }
    
    // Patient education follow-ups
    if (questionType === QUESTION_TYPES.PATIENT_EDUCATION) {
      followUps.push(
        `What prevention strategies should patients follow?`,
        `What are the warning signs patients should watch for?`,
        `How should patients prepare for their treatment?`
      );
    }
    
    // Generic follow-ups if specific ones couldn't be generated
    if (followUps.length === 0) {
      followUps.push(
        `What are the latest research findings on this topic?`,
        `How does this compare to traditional approaches?`,
        `What are the long-term outcomes for this situation?`,
        `Are there any patient education materials available?`
      );
    }
    
    // Limit the number of follow-up questions and ensure uniqueness
    return [...new Set(followUps)].slice(0, limit);
  } catch (error) {
    console.error('Error generating follow-up questions:', error);
    return [
      'What additional information would you like about this topic?',
      'Would you like more specific details about any aspect?',
      'Are there any related topics you would like to explore?'
    ];
  }
};

/**
 * Find contradictory information about a dental topic
 * @param {string} topic - The dental topic to investigate
 * @returns {Promise<Object>} - Analysis of contradictions and consensus
 */
export const findContradictoryInformation = async (topic) => {
  try {
    // Gather information from multiple sources
    const [pubmedArticles, adaGuidelines, cochraneReviews] = await Promise.all([
      searchPubMedArticles(topic, 5),
      fetchADAGuidelines(topic),
      searchCochraneReviews(topic, 3)
    ]);
    
    // In a real implementation, this would analyze the results for contradictions
    // For demonstration, we'll generate sample contradiction analysis
    
    // Simulated contradictions
    const contradictions = [];
    const hasContradictions = Math.random() > 0.5; // 50% chance of finding contradictions
    
    if (hasContradictions) {
      // Generate 1-3 sample contradictions
      const numContradictions = Math.floor(Math.random() * 3) + 1;
      
      for (let i = 0; i < numContradictions; i++) {
        contradictions.push({
          aspect: generateRandomAspect(topic),
          viewA: {
            source: 'Recent research (2023)',
            claim: `Suggests that ${topic} is most effective when combined with adjunct therapy.`,
            strength: 'Moderate evidence, small sample sizes'
          },
          viewB: {
            source: 'Clinical guidelines (2020)',
            claim: `Recommends ${topic} as a standalone treatment without adjuncts.`,
            strength: 'Expert consensus, limited recent data'
          },
          assessment: 'These contradictions likely reflect evolving research and the lag in updating clinical guidelines.'
        });
      }
    }
    
    // Generate areas of consensus
    const consensusAreas = [
      {
        aspect: 'Diagnosis',
        consensus: `Standard diagnostic criteria for ${topic} include clinical examination and radiographic assessment.`,
        strength: 'Strong consensus across all sources'
      },
      {
        aspect: 'Patient factors',
        consensus: `Individual patient characteristics significantly influence outcomes of ${topic}.`,
        strength: 'Consistent finding across clinical studies'
      },
      {
        aspect: 'Follow-up care',
        consensus: `Regular monitoring is essential following intervention for ${topic}.`,
        strength: 'Universal recommendation in guidelines'
      }
    ];
    
    return {
      topic,
      hasContradictions,
      contradictions,
      consensusAreas,
      analysisDate: new Date().toISOString(),
      disclaimer: 'This analysis is intended to highlight areas where the dental literature contains differing viewpoints. Clinicians should use their judgment and consider patient-specific factors when applying this information.'
    };
  } catch (error) {
    console.error('Error finding contradictory information:', error);
    return {
      error: 'Failed to analyze contradictory information',
      details: error.message
    };
  }
};

/**
 * Get evidence quality assessment for a dental topic
 * @param {string} topic - The dental topic to assess
 * @returns {Promise<Object>} - Evidence quality assessment
 */
export const getEvidenceQualityAssessment = async (topic) => {
  try {
    // Search Cochrane for systematic reviews
    const cochraneReviews = await searchCochraneReviews(topic, 3);
    
    // Search PubMed for recent research
    const pubmedArticles = await searchPubMedArticles(topic, 5);
    
    // In a real implementation, this would analyze the quality of evidence
    // For demonstration, we'll generate a sample quality assessment
    
    const evidenceLevels = [
      { level: '1a', description: 'Systematic reviews of randomized controlled trials' },
      { level: '1b', description: 'Individual randomized controlled trials' },
      { level: '2a', description: 'Systematic reviews of cohort studies' },
      { level: '2b', description: 'Individual cohort studies' },
      { level: '3a', description: 'Systematic reviews of case-control studies' },
      { level: '3b', description: 'Individual case-control studies' },
      { level: '4', description: 'Case series' },
      { level: '5', description: 'Expert opinion without explicit critical appraisal' }
    ];
    
    // Assign random evidence levels to the topic
    const topicEvidenceLevel = evidenceLevels[Math.floor(Math.random() * 3)]; // Biased toward higher quality evidence
    
    // Generate strengths and limitations
    const strengths = [
      'Multiple high-quality randomized controlled trials',
      'Large sample sizes in key studies',
      'Consistent findings across different populations',
      'Long-term follow-up data available'
    ];
    
    const limitations = [
      'Some studies have potential conflicts of interest',
      'Limited research in certain subpopulations',
      'Heterogeneity in outcome measures across studies',
      'Few studies with direct comparisons to alternatives'
    ];
    
    // Randomly select 2 strengths and 2 limitations
    const selectedStrengths = strengths
      .sort(() => 0.5 - Math.random())
      .slice(0, 2);
    
    const selectedLimitations = limitations
      .sort(() => 0.5 - Math.random())
      .slice(0, 2);
    
    return {
      topic,
      evidenceLevel: topicEvidenceLevel,
      evidenceSummary: `The body of evidence for ${topic} is primarily ${topicEvidenceLevel.description}.`,
      strengths: selectedStrengths,
      limitations: selectedLimitations,
      recommendationStrength: determineRecommendationStrength(topicEvidenceLevel),
      researchGaps: [
        `Long-term outcomes of ${topic} in geriatric populations`,
        `Cost-effectiveness of ${topic} compared to alternatives`,
        `Patient-reported outcomes following ${topic}`
      ],
      assessmentDate: new Date().toISOString(),
      disclaimer: 'This evidence assessment is generated based on available literature and should be considered alongside clinical expertise and patient preferences.'
    };
  } catch (error) {
    console.error('Error assessing evidence quality:', error);
    return {
      error: 'Failed to assess evidence quality',
      details: error.message
    };
  }
};

/**
 * Initialize the question answer cache table
 */
export const initQuestionAnswerCache = async () => {
  try {
    const { error } = await supabase.rpc('create_table_if_not_exists', {
      table_name: 'question_answer_cache',
      columns: `
        id uuid primary key default uuid_generate_v4(),
        question text not null,
        question_hash text not null,
        question_type text not null,
        answer_data jsonb not null,
        created_at timestamp with time zone default now()
      `
    });
    
    if (error) throw error;
    console.log('Question answer cache table initialized');
  } catch (error) {
    console.error('Error initializing question answer cache:', error);
  }
};

/* Helper functions */

/**
 * Categorize a dental question by type
 * @param {string} question - The question to categorize
 * @returns {string} - Question type
 */
function categorizeQuestion(question) {
  const questionLower = question.toLowerCase();
  
  // Clinical patterns
  if (
    questionLower.includes('treat') ||
    questionLower.includes('diagnos') ||
    questionLower.includes('symptom') ||
    questionLower.includes('management') ||
    questionLower.includes('therapy')
  ) {
    return QUESTION_TYPES.CLINICAL;
  }
  
  // Research patterns
  if (
    questionLower.includes('study') ||
    questionLower.includes('research') ||
    questionLower.includes('evidence') ||
    questionLower.includes('trial') ||
    questionLower.includes('journal')
  ) {
    return QUESTION_TYPES.RESEARCH;
  }
  
  // Patient education patterns
  if (
    questionLower.includes('explain') ||
    questionLower.includes('patient') ||
    questionLower.includes('hygiene') ||
    questionLower.includes('prevent') ||
    questionLower.includes('home care')
  ) {
    return QUESTION_TYPES.PATIENT_EDUCATION;
  }
  
  // Procedure patterns
  if (
    questionLower.includes('procedure') ||
    questionLower.includes('extraction') ||
    questionLower.includes('root canal') ||
    questionLower.includes('crown') ||
    questionLower.includes('implant') ||
    questionLower.includes('filling')
  ) {
    return QUESTION_TYPES.PROCEDURE;
  }
  
  // Material patterns
  if (
    questionLower.includes('material') ||
    questionLower.includes('composite') ||
    questionLower.includes('amalgam') ||
    questionLower.includes('ceramic') ||
    questionLower.includes('zirconia') ||
    questionLower.includes('resin')
  ) {
    return QUESTION_TYPES.MATERIAL;
  }
  
  // Epidemiology patterns
  if (
    questionLower.includes('prevalence') ||
    questionLower.includes('incidence') ||
    questionLower.includes('statistics') ||
    questionLower.includes('population') ||
    questionLower.includes('demographic')
  ) {
    return QUESTION_TYPES.EPIDEMIOLOGY;
  }
  
  // Default to clinical if no clear pattern
  return QUESTION_TYPES.CLINICAL;
}

/**
 * Generate an answer based on the search results
 * @param {string} question - The original question
 * @param {string} questionType - The categorized question type
 * @param {Object} searchResults - Results from comprehensive search
 * @param {string} context - Additional context
 * @returns {Object} - Structured answer
 */
function generateAnswer(question, questionType, searchResults, context) {
  // In a real implementation, this would intelligently process the search results
  // For demonstration, we'll generate sample answers based on question type
  
  const answers = {
    [QUESTION_TYPES.CLINICAL]: {
      directAnswer: "Based on current clinical guidelines, the recommended approach is to start with conservative treatment and progress to more invasive options if necessary.",
      explanation: "The clinical guidelines suggest a stepwise approach, beginning with patient education and self-care instructions, followed by professional interventions as needed.",
      evidenceLevel: "Moderate - based on clinical studies and expert consensus",
      considerations: [
        "Patient's medical history may require modifications to standard protocols",
        "Consider risk factors that might influence treatment outcomes",
        "Regular follow-up is essential to monitor progress"
      ]
    },
    [QUESTION_TYPES.RESEARCH]: {
      directAnswer: "Recent research indicates promising results for new approaches, though more studies are needed.",
      explanation: "Several recent publications have demonstrated improved outcomes with novel techniques, but sample sizes remain small and long-term data is limited.",
      evidenceLevel: "Emerging - based on recent studies with methodological limitations",
      considerations: [
        "Consider the methodological quality of cited studies",
        "Results may not be generalizable to all populations",
        "Research in this area is evolving rapidly"
      ]
    },
    [QUESTION_TYPES.PATIENT_EDUCATION]: {
      directAnswer: "Patients should focus on regular hygiene practices and attend scheduled follow-ups.",
      explanation: "Patient education should emphasize proper brushing and flossing techniques, dietary considerations, and the importance of professional cleanings.",
      evidenceLevel: "Strong - based on well-established preventive dentistry principles",
      considerations: [
        "Tailor instructions to patient's specific needs and abilities",
        "Visual aids and demonstrations improve patient comprehension",
        "Provide written instructions for reference"
      ]
    },
    [QUESTION_TYPES.PROCEDURE]: {
      directAnswer: "The procedure typically involves multiple steps and requires appropriate preparation and follow-up care.",
      explanation: "The standard protocol includes initial assessment, preparation, the main procedure steps, and post-procedure care instructions.",
      evidenceLevel: "Strong - based on established clinical protocols",
      considerations: [
        "Patient selection criteria are important for optimal outcomes",
        "Proper informed consent should include discussion of alternatives",
        "Post-procedure complications should be clearly communicated"
      ]
    },
    [QUESTION_TYPES.MATERIAL]: {
      directAnswer: "Material selection should be based on the specific clinical situation, considering location, functional demands, and esthetic requirements.",
      explanation: "Different materials offer varying advantages in terms of durability, esthetics, and biocompatibility.",
      evidenceLevel: "Moderate - based on clinical studies and material science research",
      considerations: [
        "Patient allergies or sensitivities may contraindicate certain materials",
        "Longevity expectations should be discussed with patients",
        "Cost considerations may influence material selection"
      ]
    },
    [QUESTION_TYPES.EPIDEMIOLOGY]: {
      directAnswer: "Current epidemiological data shows varying prevalence rates across different populations and age groups.",
      explanation: "Global statistics indicate differences based on geographical location, socioeconomic status, and access to dental care.",
      evidenceLevel: "Strong - based on population studies and health surveys",
      considerations: [
        "Data collection methods influence reported prevalence rates",
        "Consider demographic trends when interpreting statistics",
        "Preventive strategies should target high-risk populations"
      ]
    }
  };
  
  // Select the appropriate answer template based on question type
  const answerTemplate = answers[questionType] || answers[QUESTION_TYPES.CLINICAL];
  
  // Create the answer object
  return {
    question,
    questionType,
    directAnswer: answerTemplate.directAnswer,
    explanation: answerTemplate.explanation,
    evidenceLevel: answerTemplate.evidenceLevel,
    considerations: answerTemplate.considerations,
    sources: searchResults.articles ? searchResults.articles.slice(0, 3).map(article => ({
      title: article.title,
      authors: article.authors,
      publication: article.journal,
      year: article.year,
      url: article.url
    })) : [],
    guidelines: searchResults.guidelines ? [searchResults.guidelines].flat().map(guideline => ({
      organization: guideline.organization,
      title: guideline.title,
      year: guideline.year,
      recommendation: guideline.recommendation
    })) : [],
    generatedAt: new Date().toISOString(),
    disclaimer: "This information is provided for educational purposes only and should not replace professional dental advice."
  };
}

/**
 * Extract key topics from a question and its answer
 * @param {string} question - The original question
 * @param {Object} answerData - The answer data
 * @returns {Object} - Extracted topics
 */
function extractKeyTopics(question, answerData) {
  // In a real implementation, this would use NLP to extract entities
  // For demonstration, we'll use simple pattern matching
  
  const questionLower = question.toLowerCase();
  
  // Common dental conditions
  const conditions = [
    'caries', 'cavity', 'decay', 'periodontal disease', 'gingivitis', 
    'periodontitis', 'pulpitis', 'abscess', 'lesion', 'malocclusion',
    'tmj', 'bruxism', 'erosion', 'fluorosis'
  ];
  
  // Common dental procedures
  const procedures = [
    'filling', 'restoration', 'crown', 'bridge', 'implant', 
    'extraction', 'root canal', 'scaling', 'whitening', 'orthodontics',
    'veneer', 'denture', 'sealant'
  ];
  
  // Common dental materials
  const materials = [
    'amalgam', 'composite', 'ceramic', 'porcelain', 'zirconia',
    'resin', 'glass ionomer', 'gold', 'titanium', 'acrylic'
  ];
  
  // Find matches in the question
  const extractedTopics = {
    conditions: conditions.filter(condition => questionLower.includes(condition)),
    procedures: procedures.filter(procedure => questionLower.includes(procedure)),
    materials: materials.filter(material => questionLower.includes(material))
  };
  
  return extractedTopics;
}

/**
 * Generate a hash for a question for caching purposes
 * @param {string} question - The question to hash
 * @returns {string} - Hash string
 */
function generateQuestionHash(question) {
  // Simple hash function for demonstration
  // In production, use a proper hashing algorithm
  
  let hash = 0;
  for (let i = 0; i < question.length; i++) {
    const char = question.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  return hash.toString();
}

/**
 * Generate a random aspect of a dental topic for contradiction examples
 * @param {string} topic - The dental topic
 * @returns {string} - Random aspect
 */
function generateRandomAspect(topic) {
  const aspects = [
    'treatment frequency',
    'optimal timing',
    'efficacy in specific populations',
    'long-term outcomes',
    'combination therapies',
    'risk factors',
    'diagnostic criteria',
    'preventive approaches'
  ];
  
  return aspects[Math.floor(Math.random() * aspects.length)];
}

/**
 * Determine the recommendation strength based on evidence level
 * @param {Object} evidenceLevel - The evidence level object
 * @returns {string} - Recommendation strength
 */
function determineRecommendationStrength(evidenceLevel) {
  // Map evidence levels to recommendation strengths
  const strengthMap = {
    '1a': 'Strong recommendation',
    '1b': 'Strong recommendation',
    '2a': 'Moderate recommendation',
    '2b': 'Moderate recommendation',
    '3a': 'Conditional recommendation',
    '3b': 'Conditional recommendation',
    '4': 'Weak recommendation',
    '5': 'Expert opinion only'
  };
  
  return strengthMap[evidenceLevel.level] || 'Insufficient evidence for recommendation';
}; 