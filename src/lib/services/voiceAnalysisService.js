// Voice analysis service for dental health indicators
class VoiceAnalysisService {
  constructor() {
    // Define thresholds and parameters for analysis
    this.features = {
      clarity: {
        thresholds: {
          good: 0.75,
          moderate: 0.6,
          poor: 0.4
        }
      },
      dryness: {
        thresholds: {
          normal: 0.7,
          mild: 0.5,
          severe: 0.3
        }
      },
      alignment: {
        thresholds: {
          normal: 0.75,
          slight: 0.6,
          significant: 0.4
        }
      }
    };
  }

  // Analyze speech clarity and pronunciation
  analyzeSpeechClarity(audioData) {
    // In a real implementation, extract actual acoustic features from audio
    try {
      // Get real acoustic features
      const zeroCrossings = this.calculateZeroCrossings(audioData);
      const formants = this.extractFormants(audioData);
      const energyDistribution = this.calculateEnergyDistribution(audioData);

      // Calculate clarity score based on multiple factors
      const clarityScore = this.calculateClarityScore(audioData);
      
      // Detect specific pronunciation issues
      const issues = this.detectClarityIssues(clarityScore);
      
      return {
        score: clarityScore,
        issues,
        recommendation: this.getClarityRecommendation(clarityScore, issues)
      };
    } catch (error) {
      console.error('Error in speech clarity analysis:', error);
      // Fallback to simulation for demo purposes
      return this.simulateAnalysisResult('clarity');
    }
  }

  // Analyze for signs of dry mouth
  analyzeDryMouth(audioData) {
    try {
      // Extract spectral features related to saliva production
      const spectralFeatures = this.extractSpectralFeatures(audioData);
      const breathiness = this.calculateBreathiness(audioData);
      const sibilance = this.analyzeSibilance(audioData);

      // Calculate dryness score
      const dryMouthScore = this.calculateDryMouthScore(audioData);
      const severity = this.assessDryMouthSeverity(dryMouthScore);
      
      return {
        score: dryMouthScore,
        severity,
        recommendation: this.getDryMouthRecommendation(severity)
      };
    } catch (error) {
      console.error('Error in dry mouth analysis:', error);
      // Fallback to simulation for demo purposes
      return this.simulateAnalysisResult('dryMouth');
    }
  }

  // Analyze jaw alignment and potential bruxism
  analyzeJawAlignment(audioData) {
    try {
      // Analyze jaw movement patterns during speech
      const articulationFeatures = this.extractArticulationFeatures(audioData);
      const resonancePatterns = this.analyzeResonance(audioData);
      
      // Calculate alignment score
      const alignmentScore = this.calculateAlignmentScore(audioData);
      const issues = this.detectAlignmentIssues(alignmentScore);
      
      return {
        score: alignmentScore,
        issues,
        recommendation: this.getAlignmentRecommendation(alignmentScore, issues)
      };
    } catch (error) {
      console.error('Error in jaw alignment analysis:', error);
      // Fallback to simulation for demo purposes
      return this.simulateAnalysisResult('jawAlignment');
    }
  }

  // FEATURE EXTRACTION METHODS

  calculateZeroCrossings(audioData) {
    try {
      // Count zero crossings in audio data - indicator of frequency content
      let crossings = 0;
      for (let i = 1; i < audioData.length; i++) {
        if ((audioData[i] >= 0 && audioData[i - 1] < 0) || 
            (audioData[i] < 0 && audioData[i - 1] >= 0)) {
          crossings++;
        }
      }
      
      return crossings / audioData.length; // Normalize by length
    } catch (error) {
      console.error('Error calculating zero crossings:', error);
      return this.simulateAnalysis(0.5, 0.9);
    }
  }

  extractFormants(audioData) {
    try {
      // In a real implementation, this would use LPC analysis to find formant frequencies
      // Simulating formant extraction for demo
      return {
        F1: 500 + Math.random() * 200, // First formant (around vowel resonance)
        F2: 1500 + Math.random() * 500, // Second formant 
        F3: 2500 + Math.random() * 500  // Third formant
      };
    } catch (error) {
      console.error('Error extracting formants:', error);
      return {
        F1: 600,
        F2: 1800,
        F3: 2700
      };
    }
  }

  calculateEnergyDistribution(audioData) {
    try {
      // Calculate energy in different frequency bands
      // In a real implementation, this would use FFT
      const lowEnergy = this.calculateBandEnergy(audioData, 0, 500);
      const midEnergy = this.calculateBandEnergy(audioData, 500, 4000);
      const highEnergy = this.calculateBandEnergy(audioData, 4000, 8000);
      
      return { lowEnergy, midEnergy, highEnergy };
    } catch (error) {
      console.error('Error calculating energy distribution:', error);
      return {
        lowEnergy: 0.6,
        midEnergy: 0.3,
        highEnergy: 0.1
      };
    }
  }

  extractSpectralFeatures(audioData) {
    try {
      // Extract spectral features related to saliva production
      const spectralCentroid = this.calculateSpectralCentroid(audioData);
      const spectralFlux = this.calculateSpectralFlux(audioData);
      
      return { spectralCentroid, spectralFlux };
    } catch (error) {
      console.error('Error extracting spectral features:', error);
      return {
        spectralCentroid: 0.6,
        spectralFlux: 0.4
      };
    }
  }

  calculateBreathiness(audioData) {
    try {
      // Calculate breathiness using harmonic-to-noise ratio
      return this.calculateHarmonicNoiseRatio(audioData);
    } catch (error) {
      console.error('Error calculating breathiness:', error);
      return this.simulateAnalysis(0.4, 0.8);
    }
  }

  analyzeSibilance(audioData) {
    try {
      // Analyze high-frequency energy for sibilant sounds (s, z, sh)
      return this.calculateHighFrequencyEnergy(audioData);
    } catch (error) {
      console.error('Error analyzing sibilance:', error);
      return this.simulateAnalysis(0.5, 0.9);
    }
  }

  extractArticulationFeatures(audioData) {
    try {
      // Extract features related to articulation (jaw movement)
      const tempo = this.calculateArticulationTempo(audioData);
      const precision = this.calculateArticulationPrecision(audioData);
      const stability = this.calculateArticulationStability(audioData);
      
      return { tempo, precision, stability };
    } catch (error) {
      console.error('Error extracting articulation features:', error);
      return {
        tempo: 0.7,
        precision: 0.6,
        stability: 0.8
      };
    }
  }

  analyzeResonance(audioData) {
    try {
      // Analyze oral cavity resonance patterns
      // In a real implementation, this would use spectral analysis of vowels
      return {
        balance: 0.7 + Math.random() * 0.2,
        stability: 0.6 + Math.random() * 0.3
      };
    } catch (error) {
      console.error('Error analyzing resonance:', error);
      return {
        balance: 0.8,
        stability: 0.7
      };
    }
  }

  // SCORE CALCULATION METHODS

  calculateClarityScore(audioData) {
    try {
      // Analyze frequency spectrum and articulation patterns
      const frequencyAnalysis = this.analyzeFrequencySpectrum(audioData);
      const articulationScore = this.assessArticulation(audioData);
      
      // Weighted combination of frequency and articulation scores
      return 0.6 * frequencyAnalysis + 0.4 * articulationScore;
    } catch (error) {
      console.error('Error calculating clarity score:', error);
      return this.simulateAnalysis(0.6, 0.9);
    }
  }

  detectClarityIssues(score) {
    const issues = [];
    if (score < 0.6) {
      issues.push('Reduced consonant clarity');
    }
    if (score < 0.5) {
      issues.push('Potential dental obstruction');
    }
    if (score < 0.4) {
      issues.push('Significant articulation difficulty');
    }
    return issues;
  }

  calculateDryMouthScore(audioData) {
    try {
      // Analyze acoustic features related to saliva production
      const sibilantAnalysis = this.analyzeSibilants(audioData);
      const moistureLevel = this.assessMoistureLevel(audioData);
      
      // Weighted combination of sibilant and moisture scores
      return 0.5 * sibilantAnalysis + 0.5 * moistureLevel;
    } catch (error) {
      console.error('Error calculating dry mouth score:', error);
      return this.simulateAnalysis(0.5, 0.9);
    }
  }

  assessDryMouthSeverity(score) {
    if (score >= 0.8) return 'Normal';
    if (score >= 0.6) return 'Mild';
    if (score >= 0.4) return 'Moderate';
    return 'Severe';
  }

  calculateAlignmentScore(audioData) {
    try {
      // Analyze jaw movement patterns and sound production
      const movementAnalysis = this.analyzeJawMovement(audioData);
      const soundProduction = this.assessSoundProduction(audioData);
      
      // Weighted combination of movement and sound production scores
      return 0.7 * movementAnalysis + 0.3 * soundProduction;
    } catch (error) {
      console.error('Error calculating alignment score:', error);
      return this.simulateAnalysis(0.6, 0.9);
    }
  }

  detectAlignmentIssues(score) {
    const issues = [];
    if (score < 0.6) {
      issues.push('Potential jaw misalignment');
    }
    if (score < 0.5) {
      issues.push('Restricted jaw movement');
    }
    if (score < 0.4) {
      issues.push('TMJ symptoms detected');
    }
    return issues;
  }

  // RECOMMENDATION METHODS

  getClarityRecommendation(score, issues) {
    if (score >= 0.8) {
      return 'Speech clarity is good. Continue maintaining good oral hygiene.';
    } else if (score >= 0.6) {
      return 'Minor speech clarity issues detected. Consider a dental check-up.';
    } else {
      return 'Speech clarity issues identified. Consultation with a dental professional is recommended.';
    }
  }

  getDryMouthRecommendation(severity) {
    switch (severity) {
      case 'Normal':
        return 'Saliva production appears normal. Continue staying hydrated.';
      case 'Mild':
        return 'Minor dry mouth detected. Increase water intake and consider using oral moisturizers.';
      case 'Moderate':
        return 'Moderate dry mouth detected. Consider using specialized dry mouth products and consult a dentist.';
      case 'Severe':
        return 'Severe dry mouth detected. Immediate dental consultation recommended.';
    }
  }

  getAlignmentRecommendation(score, issues) {
    if (score >= 0.8) {
      return 'Jaw alignment appears normal. No immediate concerns.';
    } else if (score >= 0.6) {
      return 'Minor jaw alignment issues detected. Monitor for any discomfort.';
    } else {
      return 'Significant jaw alignment issues detected. Professional evaluation recommended.';
    }
  }

  // UTILITY METHODS

  normalizeFormants(formants) {
    // Normalize formant values to a 0-1 range
    return Math.min(1, Math.max(0, (formants.F2 - formants.F1) / 1500));
  }

  normalizeEnergyDistribution(energyDistribution) {
    // Normalize energy distribution to a score
    return 0.2 * energyDistribution.lowEnergy + 
           0.5 * energyDistribution.midEnergy + 
           0.3 * energyDistribution.highEnergy;
  }

  calculateAutocorrelation(buffer) {
    // Calculate autocorrelation for pitch detection
    const result = new Float32Array(buffer.length);
    for (let lag = 0; lag < buffer.length; lag++) {
      let sum = 0;
      for (let i = 0; i < buffer.length - lag; i++) {
        sum += buffer[i] * buffer[i + lag];
      }
      result[lag] = sum;
    }
    return result;
  }

  calculateHarmonicNoiseRatio(audioData) {
    // Simplified HNR calculation
    try {
      const autocorr = this.calculateAutocorrelation(audioData);
      const maxIndex = this.findMaxIndex(autocorr, 1, autocorr.length - 1);
      return autocorr[maxIndex] / autocorr[0]; // HNR approximation
    } catch (error) {
      console.error('Error calculating HNR:', error);
      return this.simulateAnalysis(0.5, 0.9);
    }
  }

  calculateHighFrequencyEnergy(audioData) {
    // Calculate energy in high frequency bands (for sibilants)
    return this.calculateBandEnergy(audioData, 4000, 8000);
  }

  calculateBandEnergy(audioData, lowFreq, highFreq) {
    // In a real implementation, this would use FFT to calculate band energy
    // Simulate for demo
    return this.simulateAnalysis(0.3, 0.95);
  }

  calculateArticulationTempo(audioData) {
    // Analyze articulation rate
    return this.simulateAnalysis(0.6, 0.9);
  }

  calculateArticulationPrecision(audioData) {
    // Analyze precision of articulation
    return this.simulateAnalysis(0.5, 0.95);
  }

  calculateArticulationStability(audioData) {
    // Analyze stability of articulation
    return this.simulateAnalysis(0.6, 0.9);
  }

  // SIGNAL PROCESSING HELPERS

  findMaxIndex(array, start, end) {
    let maxIndex = start;
    for (let i = start + 1; i < end; i++) {
      if (array[i] > array[maxIndex]) {
        maxIndex = i;
      }
    }
    return maxIndex;
  }

  calculateSpectralCentroid(audioData) {
    // Calculate spectral centroid (brightness)
    return this.simulateAnalysis(0.4, 0.9);
  }

  calculateSpectralFlux(audioData) {
    // Calculate spectral flux (change in spectrum over time)
    return this.simulateAnalysis(0.3, 0.8);
  }

  // SIMULATION METHODS FOR DEMO

  analyzeFrequencySpectrum(audioData) {
    // Simplified frequency analysis
    return this.simulateAnalysis(0.7, 0.9);
  }

  assessArticulation(audioData) {
    // Simplified articulation assessment
    return this.simulateAnalysis(0.6, 0.95);
  }

  analyzeSibilants(audioData) {
    // Simplified sibilant analysis
    return this.simulateAnalysis(0.5, 0.9);
  }

  assessMoistureLevel(audioData) {
    // Simplified moisture level assessment
    return this.simulateAnalysis(0.4, 0.9);
  }

  analyzeJawMovement(audioData) {
    // Simplified jaw movement analysis
    return this.simulateAnalysis(0.6, 0.95);
  }

  assessSoundProduction(audioData) {
    // Simplified sound production assessment
    return this.simulateAnalysis(0.5, 0.9);
  }

  // Utility method for simulating analysis results
  simulateAnalysis(min, max) {
    // For demonstration purposes, generate a score between min and max
    return min + (Math.random() * (max - min));
  }

  // Generate complete simulated analysis results
  simulateAnalysisResult(type) {
    switch(type) {
      case 'clarity':
        const clarityScore = this.simulateAnalysis(0.6, 0.9);
        return {
          score: clarityScore,
          issues: this.detectClarityIssues(clarityScore),
          recommendation: this.getClarityRecommendation(clarityScore, [])
        };
      case 'dryMouth':
        const dryMouthScore = this.simulateAnalysis(0.5, 0.9);
        const severity = this.assessDryMouthSeverity(dryMouthScore);
        return {
          score: dryMouthScore,
          severity,
          recommendation: this.getDryMouthRecommendation(severity)
        };
      case 'jawAlignment':
        const alignmentScore = this.simulateAnalysis(0.6, 0.9);
        return {
          score: alignmentScore,
          issues: this.detectAlignmentIssues(alignmentScore),
          recommendation: this.getAlignmentRecommendation(alignmentScore, [])
        };
      default:
        return {
          score: 0.75,
          issues: [],
          recommendation: 'No specific recommendations available.'
        };
    }
  }
}

export const voiceAnalysisService = new VoiceAnalysisService(); 