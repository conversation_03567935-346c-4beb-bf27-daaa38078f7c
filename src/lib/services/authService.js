import { supabase } from '../supabase';
import { handleApiError } from '../utils/errorHandler';

export const signUp = async ({ email, password, username, firstName, lastName }) => {
  try {
    // Generate default username if not provided
    const defaultUsername = `user_${Date.now().toString(36)}`;
    const finalUsername = username || defaultUsername;

    // Register user
    const { data: authData, error: signUpError } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          username: finalUsername,
          first_name: firstName,
          last_name: lastName
        }
      }
    });

    if (signUpError) throw signUpError;

    // Create user profile
    const { error: profileError } = await supabase
      .from('user_profiles')
      .insert([{
        user_id: authData.user.id,
        username: finalUsername,
        first_name: firstName,
        last_name: lastName
      }]);

    if (profileError) throw profileError;

    return authData;
  } catch (error) {
    console.error('Sign up error:', error);
    throw new Error(handleApiError(error));
  }
};

export const signIn = async ({ email, password }) => {
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Sign in error:', error);
    throw new Error(handleApiError(error));
  }
};

export const signOut = async () => {
  try {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
  } catch (error) {
    console.error('Sign out error:', error);
    throw new Error(handleApiError(error));
  }
};

export const resetPassword = async (email) => {
  try {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/reset-password`
    });
    if (error) throw error;
  } catch (error) {
    console.error('Password reset error:', error);
    throw new Error(handleApiError(error));
  }
};

export const updatePassword = async (newPassword) => {
  try {
    const { error } = await supabase.auth.updateUser({
      password: newPassword
    });
    if (error) throw error;
  } catch (error) {
    console.error('Password update error:', error);
    throw new Error(handleApiError(error));
  }
};