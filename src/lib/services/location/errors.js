export class GeolocationError extends Error {
  constructor(code, message) {
    super(message);
    this.name = 'GeolocationError';
    this.code = code;
  }
}

export const handleGeolocationError = (error) => {
  switch (error.code) {
    case 1: // PERMISSION_DENIED
      throw new GeolocationError('PERMISSION_DENIED', error.message);
    case 2: // POSITION_UNAVAILABLE
      throw new GeolocationError('POSITION_UNAVAILABLE', error.message);
    case 3: // TIMEOUT
      throw new GeolocationError('TIMEOUT', error.message);
    default:
      throw new GeolocationError('DEFAULT', error.message);
  }
};