export const GEOLOCATION_CONFIG = {
  enableHighAccuracy: true,
  timeout: 10000,
  maximumAge: 0
};

export const DISTANCE_UNITS = {
  MILES: {
    name: 'miles',
    radius: 3959, // Earth's radius in miles
    convert: (km) => km * 0.621371
  },
  KILOMETERS: {
    name: 'kilometers',
    radius: 6371, // Earth's radius in kilometers
    convert: (miles) => miles * 1.60934
  }
};

export const ERROR_MESSAGES = {
  PERMISSION_DENIED: 'Location access was denied. Please enable location services or use ZIP code search.',
  POSITION_UNAVAILABLE: 'Unable to determine your location. Please try ZIP code search instead.',
  TIMEOUT: 'Location request timed out. Please try again or use ZIP code search.',
  UNSUPPORTED: 'Location services are not supported in your browser. Please use ZIP code search.',
  INVALID_COORDS: 'Invalid coordinates received. Please try ZIP code search instead.',
  INVALID_ZIP: 'Please enter a valid 5-digit ZIP code.',
  ZIP_NOT_FOUND: 'Could not find location for this ZIP code. Please try another.',
  DEFAULT: 'An error occurred while getting your location. Please try ZIP code search.'
};