import { GEOLOCATION_CONFIG, ERROR_MESSAGES } from './config';
import { handleGeolocationError } from './errors';
import { validateCoordinates } from './validation';

import { DISTANCE_UNITS } from './config';

export const getCurrentLocation = () => {
  return new Promise((resolve, reject) => {
    if (!navigator.geolocation) {
      console.error('Geolocation not supported');
      reject(new Error(ERROR_MESSAGES.UNSUPPORTED));
      return;
    }

    const timeoutId = setTimeout(() => {
      console.error('Location request timed out');
      reject(new Error(ERROR_MESSAGES.TIMEOUT));
    }, GEOLOCATION_CONFIG.timeout + 1000);

    navigator.geolocation.getCurrentPosition(
      (position) => {
        clearTimeout(timeoutId);
        console.log('Location retrieved successfully');
        
        try {
          const coords = validateCoordinates({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude
          });
          resolve(coords);
        } catch (error) {
          reject(new Error(ERROR_MESSAGES.INVALID_COORDS));
        }
      },
      (error) => {
        clearTimeout(timeoutId);
        console.error('Geolocation error:', error);
        reject(handleGeolocationError(error));
      },
      GEOLOCATION_CONFIG
    );
  });
};

export const getLocationFromZip = async (zipCode) => {
  if (!/^\d{5}$/.test(zipCode)) {
    throw new Error(ERROR_MESSAGES.INVALID_ZIP);
  }

  try {
    const response = await fetch(
      `https://api.zippopotam.us/us/${zipCode}`
    );

    if (!response.ok) {
      throw new Error(ERROR_MESSAGES.ZIP_NOT_FOUND);
    }

    const data = await response.json();
    const place = data.places[0];

    return validateCoordinates({
      latitude: parseFloat(place.latitude),
      longitude: parseFloat(place.longitude)
    });
  } catch (error) {
    console.error('ZIP code lookup error:', error);
    throw new Error(ERROR_MESSAGES.ZIP_NOT_FOUND);
  }
};

export const calculateDistance = (lat1, lon1, lat2, lon2, unit = DISTANCE_UNITS.MILES) => {
  const lat1Rad = toRadians(lat1);
  const lat2Rad = toRadians(lat2);
  const deltaLat = toRadians(lat2 - lat1);
  const deltaLon = toRadians(lon2 - lon1);

  const a = 
    Math.sin(deltaLat/2) * Math.sin(deltaLat/2) +
    Math.cos(lat1Rad) * Math.cos(lat2Rad) * 
    Math.sin(deltaLon/2) * Math.sin(deltaLon/2);
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  const distance = unit.radius * c;

  return Math.round(distance * 10) / 10; // Round to 1 decimal place
};

const toRadians = (degrees) => {
  return degrees * (Math.PI / 180);
};