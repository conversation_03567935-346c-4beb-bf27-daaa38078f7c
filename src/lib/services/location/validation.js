/**
 * Validates coordinates object to ensure it contains valid latitude and longitude
 * @param {Object} coords - The coordinates object to validate
 * @param {number} coords.latitude - Latitude value between -90 and 90
 * @param {number} coords.longitude - Longitude value between -180 and 180
 * @returns {Object} Validated coordinates with fixed precision
 */
export const validateCoordinates = (coords) => {
  if (!coords || typeof coords !== 'object') {
    throw new Error('Invalid coordinates object');
  }

  const { latitude, longitude } = coords;

  if (typeof latitude !== 'number' || typeof longitude !== 'number') {
    throw new Error('Latitude and longitude must be numbers');
  }

  if (latitude < -90 || latitude > 90) {
    throw new Error('Latitude must be between -90 and 90 degrees');
  }

  if (longitude < -180 || longitude > 180) {
    throw new Error('Longitude must be between -180 and 180 degrees');
  }

  return {
    latitude: Number(latitude.toFixed(6)),
    longitude: Number(longitude.toFixed(6))
  };
};