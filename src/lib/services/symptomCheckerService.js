import { supabase } from '../supabase';
import { encryptData } from './encryption';
import { uploadImage } from './imageService';
import { handleApiError } from '../utils/errorHandler';

const SYMPTOMS = {
  CAVITY: ['tooth pain', 'sensitivity', 'visible holes'],
  GUM_DISEASE: ['bleeding gums', 'swollen gums', 'receding gums'],
  SENSITIVITY: ['pain with hot/cold', 'sharp pain', 'temporary pain']
};

export const analyzeSymptoms = async (symptoms, image, userId) => {
  let imagePath = null;
  
  if (image) {
    imagePath = await uploadImage(image, userId);
  }

  // Encrypt sensitive data
  const encryptedData = encryptData({
    symptoms,
    userId,
    timestamp: new Date().toISOString()
  });

  // Store analysis in database
  const { data, error } = await supabase
    .from('symptom_analyses')
    .insert({
      user_id: userId,
      symptoms: encryptedData,
      image_path: imagePath,
      created_at: new Date().toISOString()
    })
    .select();

  if (error) throw new Error(handleApiError(error));
  return data;
};

export const getSymptomHistory = async (userId) => {
  const { data, error } = await supabase
    .from('symptom_analyses')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false })
    .limit(50);

  if (error) throw new Error(handleApiError(error));
  return data;
};