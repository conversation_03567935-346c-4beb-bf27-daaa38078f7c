import { supabase } from '../supabase';

export const uploadSTLFile = async (file, userId) => {
  try {
    // Validate file
    if (file.size > 50 * 1024 * 1024) { // 50MB limit
      throw new Error('File size must be less than 50MB');
    }
    
    if (!file.name.toLowerCase().endsWith('.stl')) {
      throw new Error('Only STL files are supported');
    }

    // Create unique filename
    const filename = `${userId}/${Date.now()}-${file.name}`;

    // Upload to Supabase storage
    const { data, error: uploadError } = await supabase.storage
      .from('dental-scans')
      .upload(filename, file, {
        cacheControl: '3600',
        upsert: false,
        contentType: 'model/stl'
      });

    if (uploadError) throw uploadError;

    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from('dental-scans')
      .getPublicUrl(filename);

    // Log upload in database
    await supabase
      .from('stl_files')
      .insert({
        user_id: userId,
        filename,
        original_name: file.name,
        file_size: file.size,
        public_url: publicUrl
      });

    return publicUrl;
  } catch (err) {
    console.error('STL upload error:', err);
    throw err;
  }
};

export const getSTLFiles = async (userId) => {
  try {
    const { data, error } = await supabase
      .from('stl_files')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  } catch (err) {
    console.error('Error fetching STL files:', err);
    throw err;
  }
};

export const deleteSTLFile = async (fileId, userId) => {
  try {
    // Get file info
    const { data: file, error: fetchError } = await supabase
      .from('stl_files')
      .select('filename')
      .eq('id', fileId)
      .eq('user_id', userId)
      .single();

    if (fetchError) throw fetchError;
    if (!file) throw new Error('File not found');

    // Delete from storage
    const { error: storageError } = await supabase.storage
      .from('dental-scans')
      .remove([file.filename]);

    if (storageError) throw storageError;

    // Delete from database
    const { error: dbError } = await supabase
      .from('stl_files')
      .delete()
      .eq('id', fileId)
      .eq('user_id', userId);

    if (dbError) throw dbError;
  } catch (err) {
    console.error('Error deleting STL file:', err);
    throw err;
  }
};