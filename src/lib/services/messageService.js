import { supabase } from '../supabase';
import { encryptMessage, decryptMessage } from '../utils/encryption';
import { handleApiError } from '../utils/errorHandler';

// Rate limiting configuration
const RATE_LIMITS = {
  messagesPerMinute: 60,
  messagesPerHour: 1000
};

// Message sending queue for offline support
let messageQueue = [];
let isProcessingQueue = false;

// Process queued messages when coming online
if (typeof window !== 'undefined') {
  window.addEventListener('online', processMessageQueue);
}

async function processMessageQueue() {
  if (isProcessingQueue || messageQueue.length === 0) return;
  
  isProcessingQueue = true;
  
  while (messageQueue.length > 0) {
    const message = messageQueue[0];
    try {
      await sendMessage(message);
      messageQueue.shift(); // Remove successfully sent message
    } catch (error) {
      console.error('Failed to send queued message:', error);
      break; // Stop processing on error
    }
  }
  
  isProcessingQueue = false;
}

export const createConversation = async (type, participants, metadata = {}) => {
  try {
    // Generate encryption key for the conversation
    const encryptionKey = await window.crypto.subtle.generateKey(
      { name: 'AES-GCM', length: 256 },
      true,
      ['encrypt', 'decrypt']
    );

    // Create conversation
    const { data: conversation, error: conversationError } = await supabase
      .from('conversations')
      .insert({
        type,
        metadata,
        encrypted_key: encryptionKey
      })
      .select()
      .single();

    if (conversationError) throw conversationError;

    // Add participants
    const participantInserts = participants.map(userId => ({
      conversation_id: conversation.id,
      user_id: userId,
      role: userId === supabase.auth.user()?.id ? 'admin' : 'member'
    }));

    const { error: participantError } = await supabase
      .from('conversation_participants')
      .insert(participantInserts);

    if (participantError) throw participantError;

    return conversation;
  } catch (error) {
    throw new Error(handleApiError(error));
  }
};

export const sendMessage = async ({ conversationId, content, contentType = 'text', parentId = null }) => {
  try {
    // Check rate limits
    const recentMessages = await getRecentMessages(conversationId);
    if (recentMessages.length >= RATE_LIMITS.messagesPerMinute) {
      throw new Error('Rate limit exceeded. Please wait before sending more messages.');
    }

    // Encrypt message content
    const encryptedContent = await encryptMessage(content);

    const message = {
      conversation_id: conversationId,
      sender_id: supabase.auth.user()?.id,
      encrypted_content: encryptedContent,
      content_type: contentType,
      parent_id: parentId,
      metadata: {
        client_timestamp: new Date().toISOString()
      }
    };

    // If offline, queue message
    if (!navigator.onLine) {
      messageQueue.push(message);
      return { queued: true, message };
    }

    const { data, error } = await supabase
      .from('messages')
      .insert(message)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    throw new Error(handleApiError(error));
  }
};

export const getMessages = async (conversationId, limit = 50, before = null) => {
  try {
    let query = supabase
      .from('messages')
      .select(`
        *,
        sender:sender_id(id, username),
        reactions:message_reactions(*)
      `)
      .eq('conversation_id', conversationId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (before) {
      query = query.lt('created_at', before);
    }

    const { data, error } = await query;
    if (error) throw error;

    // Decrypt messages
    const decryptedMessages = await Promise.all(
      data.map(async message => ({
        ...message,
        content: await decryptMessage(message.encrypted_content)
      }))
    );

    return decryptedMessages;
  } catch (error) {
    throw new Error(handleApiError(error));
  }
};

export const editMessage = async (messageId, newContent) => {
  try {
    const message = await getMessage(messageId);
    
    // Check if message is editable (within 5 minutes)
    const editWindow = 5 * 60 * 1000; // 5 minutes in milliseconds
    const messageAge = Date.now() - new Date(message.created_at).getTime();
    
    if (messageAge > editWindow) {
      throw new Error('Message can no longer be edited');
    }

    // Encrypt new content
    const encryptedContent = await encryptMessage(newContent);

    const { data, error } = await supabase
      .from('messages')
      .update({
        encrypted_content: encryptedContent,
        edited_at: new Date().toISOString()
      })
      .eq('id', messageId)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    throw new Error(handleApiError(error));
  }
};

export const deleteMessage = async (messageId) => {
  try {
    const { error } = await supabase
      .from('messages')
      .update({
        deleted_at: new Date().toISOString(),
        encrypted_content: null
      })
      .eq('id', messageId);

    if (error) throw error;
  } catch (error) {
    throw new Error(handleApiError(error));
  }
};

export const addReaction = async (messageId, reaction) => {
  try {
    const { error } = await supabase
      .from('message_reactions')
      .insert({
        message_id: messageId,
        user_id: supabase.auth.user()?.id,
        reaction
      });

    if (error) throw error;
  } catch (error) {
    throw new Error(handleApiError(error));
  }
};

export const removeReaction = async (messageId, reaction) => {
  try {
    const { error } = await supabase
      .from('message_reactions')
      .delete()
      .match({
        message_id: messageId,
        user_id: supabase.auth.user()?.id,
        reaction
      });

    if (error) throw error;
  } catch (error) {
    throw new Error(handleApiError(error));
  }
};

// Helper functions
const getRecentMessages = async (conversationId) => {
  const oneMinuteAgo = new Date(Date.now() - 60000).toISOString();
  
  const { data, error } = await supabase
    .from('messages')
    .select('id, created_at')
    .eq('conversation_id', conversationId)
    .eq('sender_id', supabase.auth.user()?.id)
    .gte('created_at', oneMinuteAgo);

  if (error) throw error;
  return data || [];
};

const getMessage = async (messageId) => {
  const { data, error } = await supabase
    .from('messages')
    .select('*')
    .eq('id', messageId)
    .single();

  if (error) throw error;
  return data;
};