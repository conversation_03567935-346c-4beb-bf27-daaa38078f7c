import { Loader } from '@googlemaps/js-api-loader';
import { supabase } from '../supabase';
import { handleApiError } from '../utils/errorHandler';
import { config } from '../config';

let googleMapsPromise = null;
let loadAttempts = 0;
const MAX_LOAD_ATTEMPTS = 3;

export const loadGoogleMapsScript = () => {
  if (googleMapsPromise) {
    console.log('🔄 Returning existing Google Maps promise');
    return googleMapsPromise;
  }

  // Debug logging for environment variables
  console.log('🔍 Environment Check:', {
    fromEnv: !!import.meta.env.VITE_GOOGLE_MAPS_API_KEY,
    envKeyLength: import.meta.env.VITE_GOOGLE_MAPS_API_KEY?.length || 0,
    fromConfig: !!config?.google?.apiKey,
    configKeyLength: config?.google?.apiKey?.length || 0,
    isDevelopment: import.meta.env.DEV,
    mode: import.meta.env.MODE
  });

  // Try to get API key from multiple sources
  const apiKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY || config?.google?.apiKey;
  
  if (!apiKey) {
    const error = new Error('❌ Google Maps API key is missing from both env and config');
    console.error('API Key Error:', error);
    return Promise.reject(error);
  }

  console.log('🔑 Using API key:', apiKey.substring(0, 8) + '...');
  console.log('🚀 Creating Google Maps loader...');

  // Create loader with detailed configuration
  const loader = new Loader({
    apiKey,
    version: 'weekly',
    libraries: config.google.libraries || ['places', 'geometry'],
    language: config.google.language || 'en',
    region: 'US',
    authReferrerPolicy: 'origin',
    id: 'google-maps-script',
    retries: 3
  });

  // Create and monitor the loading promise
  googleMapsPromise = new Promise((resolve, reject) => {
    console.log('📡 Starting Google Maps load process... Attempt: ' + (++loadAttempts));
    
    // Check if already loaded
    if (window.google?.maps) {
      console.log('✅ Google Maps already loaded in window');
      resolve(window.google);
      return;
    }

    // Check for existing script
    const existingScript = document.getElementById('google-maps-script');
    if (existingScript) {
      console.log('⚠️ Found existing Google Maps script, removing it to try again');
      existingScript.remove();
    }

    loader.load()
      .then(() => {
        console.log('🔍 Loader completed. Checking Google object:', {
          googleExists: !!window.google,
          mapsExists: !!window.google?.maps,
          placesExists: !!window.google?.maps?.places
        });

        if (!window.google?.maps) {
          throw new Error('Google Maps failed to load properly - window.google.maps is undefined');
        }

        console.log('✅ Google Maps loaded successfully');
        resolve(window.google);
      })
      .catch(err => {
        console.error('❌ Detailed loader error:', {
          error: err,
          message: err.message,
          stack: err.stack,
          type: err.constructor.name
        });
        
        // Automatically retry on failure (up to max attempts)
        if (loadAttempts < MAX_LOAD_ATTEMPTS) {
          console.log(`🔄 Retrying Google Maps load (${loadAttempts}/${MAX_LOAD_ATTEMPTS})...`);
          googleMapsPromise = null; // Clear the promise to allow retry
          
          // Try again after a brief delay
          setTimeout(() => {
            loadGoogleMapsScript().then(resolve).catch(reject);
          }, 1000);
        } else {
          console.error(`❌ Maximum load attempts (${MAX_LOAD_ATTEMPTS}) reached, giving up.`);
          googleMapsPromise = null;
          reject(err);
        }
      });
  });

  return googleMapsPromise;
};

export const createMap = async (element, options = {}) => {
  if (!element) {
    throw new Error('Map element is required');
  }

  try {
    console.log('🎯 Creating map with element:', element);
    const google = await loadGoogleMapsScript();

    const defaultOptions = {
      zoom: 4,
      center: { lat: 39.8283, lng: -98.5795 }, // Center of US
      styles: [
        {
          featureType: 'all',
          elementType: 'geometry',
          stylers: [{ color: '#242f3e' }]
        },
        {
          featureType: 'all',
          elementType: 'labels.text.stroke',
          stylers: [{ color: '#242f3e' }]
        },
        {
          featureType: 'all',
          elementType: 'labels.text.fill',
          stylers: [{ color: '#746855' }]
        }
      ],
      mapTypeControl: false,
      streetViewControl: false,
      fullscreenControl: true,
      gestureHandling: 'cooperative',
      disableDefaultUI: false,
      zoomControl: true,
      scaleControl: true,
      rotateControl: false
    };

    console.log('🗺️ Creating map with options:', { ...defaultOptions, ...options });

    const map = new google.maps.Map(element, {
      ...defaultOptions,
      ...options
    });

    if (!map) {
      throw new Error('Failed to create map instance');
    }

    console.log('✅ Map created successfully');

    // Log successful map creation
    try {
      await supabase
        .from('user_activity')
        .insert([{
          action_type: 'map_loaded',
          action_data: {
            success: true,
            timestamp: new Date().toISOString()
          }
        }]);
      console.log('📝 Map creation logged to Supabase');
    } catch (logError) {
      console.warn('⚠️ Failed to log map creation:', logError);
    }

    return map;
  } catch (error) {
    console.error('❌ Error creating map:', error);
    const handled = handleApiError(error);
    throw new Error(`Failed to create map: ${handled}`);
  }
};

export const geocodeAddress = async (address) => {
  if (!address) {
    throw new Error('Address is required for geocoding');
  }

  try {
    console.log('🔍 Geocoding address:', address);
    const google = await loadGoogleMapsScript();
    
    const geocoder = new google.maps.Geocoder();
    const response = await new Promise((resolve, reject) => {
      geocoder.geocode({ address }, (results, status) => {
        if (status === 'OK') {
          console.log('✅ Geocoding successful:', results);
          resolve(results);
        } else {
          console.error('❌ Geocoding failed with status:', status);
          reject(new Error(`Geocoding failed: ${status}`));
        }
      });
    });

    return response;
  } catch (error) {
    console.error('❌ Geocoding error:', error);
    const handled = handleApiError(error);
    throw new Error(`Geocoding failed: ${handled}`);
  }
};