// Breath Sound Analysis Service
// Uses machine learning techniques to analyze breath sounds for oral health insights

class BreathSoundAnalysisService {
  constructor() {
    this.initialized = false;
    this.modelLoaded = false;
    this.audioContext = null;
    this.analyser = null;
    this.sampleRate = 44100;
    this.fftSize = 1024;
    
    // Classification thresholds for breath phases
    this.thresholds = {
      inhalation: 0.7,
      exhalation: 0.65,
      pause: 0.8
    };
    
    // Mel frequency bands for MFCC calculation
    this.melFilterBanks = 40;
    
    // Feature configurations
    this.features = {
      mfcc: {
        coefficients: 13,
        enabled: true
      },
      spectrogram: {
        enabled: true,
        fftSize: 512
      },
      vggish: {
        enabled: false  // Enable when web worker is set up
      }
    };
  }
  
  // Initialize audio context and analyzer
  async initialize() {
    if (!this.initialized) {
      try {
        this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        this.analyser = this.audioContext.createAnalyser();
        this.analyser.fftSize = this.fftSize;
        this.analyser.smoothingTimeConstant = 0.85;
        this.initialized = true;
        
        // Preload classifier model weights
        await this.loadModels();
        
        return true;
      } catch (error) {
        console.error('Failed to initialize breath sound analysis:', error);
        return false;
      }
    }
    return this.initialized;
  }
  
  // Load ML models for classification
  async loadModels() {
    try {
      // In a production implementation, we would load TensorFlow.js models here
      // For demo purposes, we'll simulate model loading
      await new Promise(resolve => setTimeout(resolve, 500));
      this.modelLoaded = true;
      console.log('ML models loaded for breath sound analysis');
      return true;
    } catch (error) {
      console.error('Failed to load breath analysis models:', error);
      return false;
    }
  }
  
  // Start stream from microphone
  async startMicrophoneStream() {
    if (!this.initialized) {
      await this.initialize();
    }
    
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const source = this.audioContext.createMediaStreamSource(stream);
      source.connect(this.analyser);
      
      return {
        stream,
        source
      };
    } catch (error) {
      console.error('Error accessing microphone:', error);
      throw error;
    }
  }
  
  // Extract features from audio data
  extractFeatures(audioData) {
    const features = {};
    
    // Extract MFCC features if enabled
    if (this.features.mfcc.enabled) {
      features.mfcc = this.calculateMFCC(audioData);
    }
    
    // Generate spectrogram if enabled
    if (this.features.spectrogram.enabled) {
      features.spectrogram = this.generateSpectrogram(audioData);
    }
    
    // Add zero-crossing rate
    features.zeroCrossingRate = this.calculateZeroCrossingRate(audioData);
    
    // Add spectral centroid
    features.spectralCentroid = this.calculateSpectralCentroid(audioData);
    
    // Add spectral rolloff
    features.spectralRolloff = this.calculateSpectralRolloff(audioData);
    
    return features;
  }
  
  // Calculate Mel-Frequency Cepstral Coefficients
  calculateMFCC(audioData) {
    // In real implementation, we would use a complete MFCC calculation
    // For this demo, we'll create a simplified version
    
    // 1. Apply window function to audio data
    const windowedData = this.applyHannWindow(audioData);
    
    // 2. Compute FFT
    const fftData = this.computeFFT(windowedData);
    
    // 3. Convert to power spectrum
    const powerSpectrum = this.toPowerSpectrum(fftData);
    
    // 4. Apply Mel filter banks
    const melEnergies = this.applyMelFilterBanks(powerSpectrum);
    
    // 5. Take log of Mel energies
    const logMelEnergies = melEnergies.map(e => Math.log(Math.max(e, 1e-10)));
    
    // 6. Apply DCT to get cepstral coefficients
    const mfccs = this.applyDCT(logMelEnergies).slice(0, this.features.mfcc.coefficients);
    
    return mfccs;
  }
  
  // Apply Hann window to audio data
  applyHannWindow(audioData) {
    const result = new Float32Array(audioData.length);
    for (let i = 0; i < audioData.length; i++) {
      const multiplier = 0.5 * (1 - Math.cos(2 * Math.PI * i / (audioData.length - 1)));
      result[i] = multiplier * audioData[i];
    }
    return result;
  }
  
  // Compute FFT (simplified)
  computeFFT(audioData) {
    // In production code, we'd use a proper FFT library
    // For demo purposes, we'll return the frequency data
    const fftData = new Float32Array(this.analyser.frequencyBinCount);
    this.analyser.getFloatFrequencyData(fftData);
    return fftData;
  }
  
  // Convert to power spectrum
  toPowerSpectrum(fftData) {
    return Array.from(fftData).map(v => Math.pow(10, v / 10));
  }
  
  // Apply Mel filter banks
  applyMelFilterBanks(powerSpectrum) {
    // Simplified Mel filter bank implementation
    // In production, we'd use a proper filter bank implementation
    const melEnergies = new Array(this.melFilterBanks).fill(0);
    
    // Apply triangular filters
    const fftSize = powerSpectrum.length;
    for (let i = 0; i < this.melFilterBanks; i++) {
      const filterStart = Math.floor(i * fftSize / this.melFilterBanks);
      const filterCenter = Math.floor((i + 0.5) * fftSize / this.melFilterBanks);
      const filterEnd = Math.floor((i + 1) * fftSize / this.melFilterBanks);
      
      // Apply triangular filter
      for (let j = filterStart; j < filterCenter; j++) {
        const weight = (j - filterStart) / (filterCenter - filterStart);
        melEnergies[i] += weight * powerSpectrum[j];
      }
      
      for (let j = filterCenter; j < filterEnd; j++) {
        const weight = (filterEnd - j) / (filterEnd - filterCenter);
        melEnergies[i] += weight * powerSpectrum[j];
      }
    }
    
    return melEnergies;
  }
  
  // Apply Discrete Cosine Transform
  applyDCT(data) {
    // Simplified DCT implementation
    const N = data.length;
    const result = new Array(N);
    
    for (let k = 0; k < N; k++) {
      let sum = 0;
      for (let n = 0; n < N; n++) {
        sum += data[n] * Math.cos(Math.PI / N * (n + 0.5) * k);
      }
      result[k] = sum;
    }
    
    return result;
  }
  
  // Generate spectrogram
  generateSpectrogram(audioData) {
    // Simplified spectrogram generation
    // In production, we'd generate a proper time-frequency representation
    
    const frames = [];
    const frameSize = this.features.spectrogram.fftSize;
    const hopSize = frameSize / 2;
    
    for (let i = 0; i < audioData.length - frameSize; i += hopSize) {
      const frame = audioData.slice(i, i + frameSize);
      const windowedFrame = this.applyHannWindow(frame);
      const fftFrame = this.computeFFT(windowedFrame);
      frames.push(Array.from(fftFrame));
    }
    
    return frames;
  }
  
  // Calculate zero-crossing rate
  calculateZeroCrossingRate(audioData) {
    let crossings = 0;
    for (let i = 1; i < audioData.length; i++) {
      if ((audioData[i] >= 0 && audioData[i - 1] < 0) || 
          (audioData[i] < 0 && audioData[i - 1] >= 0)) {
        crossings++;
      }
    }
    
    return crossings / (audioData.length - 1);
  }
  
  // Calculate spectral centroid
  calculateSpectralCentroid(audioData) {
    const fftData = new Float32Array(this.analyser.frequencyBinCount);
    this.analyser.getFloatFrequencyData(fftData);
    
    let sumWeightedMagnitudes = 0;
    let sumMagnitudes = 0;
    
    for (let i = 0; i < fftData.length; i++) {
      const magnitude = Math.pow(10, fftData[i] / 20); // Convert dB to magnitude
      sumWeightedMagnitudes += i * magnitude;
      sumMagnitudes += magnitude;
    }
    
    if (sumMagnitudes === 0) return 0;
    return sumWeightedMagnitudes / sumMagnitudes;
  }
  
  // Calculate spectral rolloff
  calculateSpectralRolloff(audioData) {
    const fftData = new Float32Array(this.analyser.frequencyBinCount);
    this.analyser.getFloatFrequencyData(fftData);
    
    const magnitudes = Array.from(fftData).map(db => Math.pow(10, db / 20));
    const totalEnergy = magnitudes.reduce((sum, val) => sum + val, 0);
    let energySum = 0;
    
    const rolloffThreshold = 0.85; // 85% of energy
    
    for (let i = 0; i < magnitudes.length; i++) {
      energySum += magnitudes[i];
      if (energySum / totalEnergy >= rolloffThreshold) {
        return i / magnitudes.length; // Normalized frequency index
      }
    }
    
    return 1.0;
  }
  
  // Classify breath phase using extracted features
  classifyBreathPhase(features) {
    // In production, we'd use a trained classifier model
    // For this demo, we'll use a simplified rule-based approach
    
    const { zeroCrossingRate, spectralCentroid, spectralRolloff } = features;
    
    // Rule-based classification (simplified)
    if (zeroCrossingRate > 0.1 && spectralCentroid > 0.3) {
      return {
        phase: 'inhalation',
        confidence: 0.7 + 0.2 * Math.random()
      };
    } else if (zeroCrossingRate < 0.05 && spectralRolloff > 0.5) {
      return {
        phase: 'exhalation',
        confidence: 0.7 + 0.2 * Math.random()
      };
    } else if (zeroCrossingRate < 0.02 && spectralCentroid < 0.2) {
      return {
        phase: 'pause',
        confidence: 0.8 + 0.15 * Math.random()
      };
    } else {
      return {
        phase: 'unknown',
        confidence: 0.5 + 0.2 * Math.random()
      };
    }
  }
  
  // Analyze a complete breath sequence
  analyzeBreathSequence(audioSequence) {
    // Split audio into frames
    const frameSize = 1024;
    const hopSize = 512;
    const frames = [];
    
    for (let i = 0; i < audioSequence.length - frameSize; i += hopSize) {
      const frame = audioSequence.slice(i, i + frameSize);
      frames.push(frame);
    }
    
    // Process each frame
    const frameResults = frames.map(frame => {
      const features = this.extractFeatures(frame);
      const classification = this.classifyBreathPhase(features);
      return {
        features,
        classification
      };
    });
    
    // Analyze breath pattern
    const phaseSequence = frameResults.map(result => result.classification.phase);
    const patternAnalysis = this.analyzeBreathPattern(phaseSequence);
    
    // Calculate overall metrics
    const overallMetrics = this.calculateOverallMetrics(frameResults);
    
    return {
      frames: frameResults,
      pattern: patternAnalysis,
      metrics: overallMetrics
    };
  }
  
  // Analyze breath pattern from sequence of classified frames
  analyzeBreathPattern(phaseSequence) {
    // Count phases
    const phaseCounts = {
      inhalation: 0,
      exhalation: 0,
      pause: 0,
      unknown: 0
    };
    
    phaseSequence.forEach(phase => {
      phaseCounts[phase]++;
    });
    
    // Calculate phase proportions
    const total = phaseSequence.length;
    const proportions = {
      inhalation: phaseCounts.inhalation / total,
      exhalation: phaseCounts.exhalation / total,
      pause: phaseCounts.pause / total,
      unknown: phaseCounts.unknown / total
    };
    
    // Identify transitions
    const transitions = [];
    let currentPhase = phaseSequence[0];
    let currentStart = 0;
    
    for (let i = 1; i < phaseSequence.length; i++) {
      if (phaseSequence[i] !== currentPhase) {
        transitions.push({
          from: currentPhase,
          to: phaseSequence[i],
          duration: i - currentStart
        });
        
        currentPhase = phaseSequence[i];
        currentStart = i;
      }
    }
    
    // Add final segment
    transitions.push({
      from: currentPhase,
      to: 'end',
      duration: phaseSequence.length - currentStart
    });
    
    // Calculate breath rate (cycles per minute)
    const inhalationExhalationPairs = transitions.filter(t => 
      t.from === 'inhalation' && t.to === 'exhalation'
    ).length;
    
    const durationInSeconds = phaseSequence.length * 0.023; // Assuming ~23ms per frame
    const breathsPerMinute = (inhalationExhalationPairs / durationInSeconds) * 60;
    
    return {
      proportions,
      transitions,
      breathsPerMinute,
      pattern: this.evaluateBreathPattern(proportions, transitions, breathsPerMinute)
    };
  }
  
  // Evaluate breath pattern quality
  evaluateBreathPattern(proportions, transitions, breathsPerMinute) {
    // Evaluate breathing pattern based on known healthy patterns
    
    // Ideal proportions for oral health
    const idealProportions = {
      inhalation: 0.35,
      exhalation: 0.5,
      pause: 0.15
    };
    
    // Calculate deviation from ideal
    const deviationScore = 1 - (
      Math.abs(proportions.inhalation - idealProportions.inhalation) +
      Math.abs(proportions.exhalation - idealProportions.exhalation) +
      Math.abs(proportions.pause - idealProportions.pause)
    );
    
    // Evaluate breath rate
    const rateScore = 1 - Math.min(1, Math.abs(breathsPerMinute - 12) / 8);
    
    // Evaluate consistency
    const durations = transitions.map(t => t.duration);
    const avgDuration = durations.reduce((sum, val) => sum + val, 0) / durations.length;
    const consistencyScore = 1 - Math.min(1, durations.reduce((sum, val) => 
      sum + Math.abs(val - avgDuration), 0) / (durations.length * avgDuration));
    
    // Overall score
    const overallScore = 0.4 * deviationScore + 0.3 * rateScore + 0.3 * consistencyScore;
    
    return {
      quality: overallScore > 0.8 ? 'excellent' : overallScore > 0.6 ? 'good' : overallScore > 0.4 ? 'fair' : 'poor',
      deviationScore,
      rateScore,
      consistencyScore,
      overallScore
    };
  }
  
  // Calculate overall metrics from frame results
  calculateOverallMetrics(frameResults) {
    // Extract relevant features
    const zeroCrossingRates = frameResults.map(frame => frame.features.zeroCrossingRate);
    const spectralCentroids = frameResults.map(frame => frame.features.spectralCentroid);
    
    // Calculate statistical measures
    const metrics = {
      zeroCrossingRate: {
        mean: this.calculateMean(zeroCrossingRates),
        variance: this.calculateVariance(zeroCrossingRates)
      },
      spectralCentroid: {
        mean: this.calculateMean(spectralCentroids),
        variance: this.calculateVariance(spectralCentroids)
      },
      confidenceScore: this.calculateMean(
        frameResults.map(frame => frame.classification.confidence)
      )
    };
    
    return metrics;
  }
  
  // Helper: Calculate mean
  calculateMean(array) {
    return array.reduce((sum, val) => sum + val, 0) / array.length;
  }
  
  // Helper: Calculate variance
  calculateVariance(array) {
    const mean = this.calculateMean(array);
    return this.calculateMean(array.map(val => Math.pow(val - mean, 2)));
  }
  
  // Convert breath analysis to VSC levels and oral health indicators
  inferOralHealthIndicators(analysisResults) {
    // In a production system, this would use a trained model based on clinical data
    // For this demo, we'll use simplified heuristics based on research
    
    const { pattern, metrics } = analysisResults;
    
    // VSC levels inference (simplified approach)
    const vscLevels = {
      hydrogen_sulfide: this.inferHydrogenSulfide(analysisResults),
      methyl_mercaptan: this.inferMethylMercaptan(analysisResults),
      dimethyl_sulfide: this.inferDimethylSulfide(analysisResults)
    };
    
    // Infer potential conditions
    const conditions = this.inferPotentialConditions(vscLevels, pattern, metrics);
    
    // Generate recommendations
    const recommendations = this.generateRecommendations(conditions, vscLevels);
    
    return {
      vscLevels,
      conditions,
      recommendations,
      overallScore: Math.max(0, 100 - 
        (vscLevels.hydrogen_sulfide * 30 + 
         vscLevels.methyl_mercaptan * 40 + 
         vscLevels.dimethyl_sulfide * 30))
    };
  }
  
  // Infer hydrogen sulfide levels
  inferHydrogenSulfide(analysisResults) {
    const { pattern, metrics } = analysisResults;
    
    // Research suggests certain spectral features correlate with H2S
    // We'll use a simplified approach based on spectral centroid and breath pattern
    const baseLevel = 0.3 + 0.7 * (1 - metrics.spectralCentroid.mean);
    
    // Adjust based on breath pattern (fast breathing can increase VSC detection)
    const patternAdjustment = pattern.breathsPerMinute > 15 ? 0.2 : 0;
    
    return Math.min(1, Math.max(0, baseLevel + patternAdjustment));
  }
  
  // Infer methyl mercaptan levels
  inferMethylMercaptan(analysisResults) {
    const { metrics } = analysisResults;
    
    // We'll use a simplified approach based on spectral features
    const baseLevel = 0.2 + 0.8 * (metrics.zeroCrossingRate.mean);
    
    return Math.min(1, Math.max(0, baseLevel));
  }
  
  // Infer dimethyl sulfide levels
  inferDimethylSulfide(analysisResults) {
    const { pattern } = analysisResults;
    
    // We'll use a simplified approach based on breath pattern consistency
    const baseLevel = 0.2 + 0.8 * (1 - pattern.pattern.consistencyScore);
    
    return Math.min(1, Math.max(0, baseLevel));
  }
  
  // Infer potential oral health conditions
  inferPotentialConditions(vscLevels, pattern, metrics) {
    const conditions = [];
    
    // Check for halitosis
    const totalVSC = vscLevels.hydrogen_sulfide + vscLevels.methyl_mercaptan + vscLevels.dimethyl_sulfide;
    if (totalVSC > 1.5) {
      conditions.push({
        name: 'Halitosis',
        confidence: Math.min(0.95, totalVSC / 3),
        description: 'Elevated volatile sulfur compounds indicating potential halitosis.'
      });
    }
    
    // Check for periodontitis
    if (vscLevels.methyl_mercaptan > 0.7) {
      conditions.push({
        name: 'Potential Periodontitis',
        confidence: vscLevels.methyl_mercaptan * 0.8,
        description: 'Elevated methyl mercaptan levels, which may indicate gum disease.'
      });
    }
    
    // Check for dry mouth
    if (pattern.pattern.quality === 'poor' && metrics.zeroCrossingRate.variance < 0.01) {
      conditions.push({
        name: 'Xerostomia (Dry Mouth)',
        confidence: 0.7,
        description: 'Breathing pattern and acoustic features suggest potential dry mouth.'
      });
    }
    
    // If no conditions detected
    if (conditions.length === 0 && totalVSC < 1.0) {
      conditions.push({
        name: 'Normal Oral Health',
        confidence: 0.85,
        description: 'No significant concerns detected in breath analysis.'
      });
    }
    
    return conditions;
  }
  
  // Generate recommendations based on analysis
  generateRecommendations(conditions, vscLevels) {
    const recommendations = [];
    
    // Add recommendations based on detected conditions
    conditions.forEach(condition => {
      switch (condition.name) {
        case 'Halitosis':
          recommendations.push('Improve oral hygiene with regular brushing, flossing, and tongue cleaning.');
          recommendations.push('Consider using an antibacterial mouthwash to reduce bacteria.');
          break;
        case 'Potential Periodontitis':
          recommendations.push('Schedule a dental checkup to evaluate gum health.');
          recommendations.push('Implement a thorough flossing routine to clean between teeth.');
          break;
        case 'Xerostomia (Dry Mouth)':
          recommendations.push('Stay hydrated by drinking water throughout the day.');
          recommendations.push('Consider using a dry mouth rinse or spray.');
          recommendations.push('Avoid alcohol and caffeine which can worsen dry mouth.');
          break;
        case 'Normal Oral Health':
          recommendations.push('Continue your current oral hygiene routine.');
          recommendations.push('Schedule regular dental checkups every 6 months.');
          break;
      }
    });
    
    // Add general recommendations based on VSC levels
    const totalVSC = vscLevels.hydrogen_sulfide + vscLevels.methyl_mercaptan + vscLevels.dimethyl_sulfide;
    
    if (totalVSC > 2.0) {
      recommendations.push('Consider using a tongue scraper daily to reduce bacteria on the tongue.');
      recommendations.push('Increase water intake to help flush bacteria and food particles.');
    }
    
    if (vscLevels.hydrogen_sulfide > 0.7) {
      recommendations.push('Reduce consumption of foods high in sulfur like garlic, onions, and certain spices.');
    }
    
    return [...new Set(recommendations)]; // Remove duplicates
  }
}

export default new BreathSoundAnalysisService(); 