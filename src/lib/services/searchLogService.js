import { supabase } from '../supabase';
import { handleApiError } from '../utils/errorHandler';

export const logSearch = async (status, coords = null, userId = null) => {
  try {
    const logEntry = {
      status,
      user_id: userId,
      ...(coords && {
        latitude: coords.latitude,
        longitude: coords.longitude
      })
    };

    const { error } = await supabase
      .from('search_logs')
      .insert([logEntry]);

    if (error) throw error;
  } catch (error) {
    console.error('Error logging search:', error);
    throw handleApiError(error);
  }
};