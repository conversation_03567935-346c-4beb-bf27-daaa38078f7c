import { supabase } from '../supabase';
import { handleApiError } from '../utils/errorHandler';

// Subscription manager to prevent duplicate subscriptions
const subscriptionManager = {
  subscriptions: new Map(),
  add(channel, subscription) {
    if (this.subscriptions.has(channel)) {
      this.subscriptions.get(channel).unsubscribe();
    }
    this.subscriptions.set(channel, subscription);
  },
  remove(channel) {
    if (this.subscriptions.has(channel)) {
      this.subscriptions.get(channel).unsubscribe();
      this.subscriptions.delete(channel);
    }
  },
  clear() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.subscriptions.clear();
  }
};

// Real-time subscription setup
export const setupRealtimeSubscriptions = (userId, callbacks = {}) => {
  if (!userId) return;

  // User profile changes
  const profileSub = supabase
    .channel('profile_changes')
    .on('postgres_changes', {
      event: '*',
      schema: 'public',
      table: 'user_profiles',
      filter: `user_id=eq.${userId}`
    }, payload => {
      callbacks.onProfileUpdate?.(payload.new);
    })
    .subscribe();

  subscriptionManager.add('profile', profileSub);

  // Chat history updates
  const chatSub = supabase
    .channel('chat_updates')
    .on('postgres_changes', {
      event: '*',
      schema: 'public',
      table: 'users_chat_history',
      filter: `user_id=eq.${userId}`
    }, payload => {
      callbacks.onChatUpdate?.(payload.new);
    })
    .subscribe();

  subscriptionManager.add('chat', chatSub);

  // Progress updates
  const progressSub = supabase
    .channel('progress_updates')
    .on('postgres_changes', {
      event: '*',
      schema: 'public',
      table: 'predental_progress',
      filter: `user_id=eq.${userId}`
    }, payload => {
      callbacks.onProgressUpdate?.(payload.new);
    })
    .subscribe();

  subscriptionManager.add('progress', progressSub);

  return () => subscriptionManager.clear();
};

// Optimized data fetching with caching
const cache = new Map();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

export const fetchWithCache = async (key, fetcher) => {
  const cached = cache.get(key);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.data;
  }

  try {
    const data = await fetcher();
    cache.set(key, {
      data,
      timestamp: Date.now()
    });
    return data;
  } catch (error) {
    throw new Error(handleApiError(error));
  }
};

// Batch operations for better performance
export const batchOperation = async (operations) => {
  try {
    return await supabase.rpc('batch_operations', { operations });
  } catch (error) {
    throw new Error(handleApiError(error));
  }
};

// Offline support utilities
export const initOfflineSupport = () => {
  const pendingOperations = [];

  const addPendingOperation = (operation) => {
    pendingOperations.push(operation);
    localStorage.setItem('pendingOperations', JSON.stringify(pendingOperations));
  };

  const processPendingOperations = async () => {
    const operations = JSON.parse(localStorage.getItem('pendingOperations') || '[]');
    for (const operation of operations) {
      try {
        await batchOperation([operation]);
      } catch (error) {
        console.error('Error processing pending operation:', error);
      }
    }
    localStorage.removeItem('pendingOperations');
  };

  window.addEventListener('online', processPendingOperations);

  return {
    addPendingOperation,
    processPendingOperations
  };
};

// Performance monitoring
export const monitorPerformance = async (metric) => {
  try {
    await supabase
      .from('performance_metrics')
      .insert([{
        metric_name: metric.name,
        value: metric.value,
        metadata: metric.metadata
      }]);
  } catch (error) {
    console.error('Error logging performance metric:', error);
  }
};