import { supabase } from '../supabase';

// Monitor article update health
export async function checkArticleUpdateHealth() {
  try {
    // Get latest metrics
    const { data: metrics } = await supabase
      .from('article_metrics')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(24); // Last 24 hours

    if (!metrics?.length) return true;

    // Calculate health score
    const healthScore = calculateHealthScore(metrics);
    
    // Log health status
    await supabase
      .from('system_health')
      .insert([{
        component: 'article_updates',
        health_score: healthScore,
        status: getHealthStatus(healthScore),
        created_at: new Date().toISOString()
      }]);

    return healthScore >= 0.8; // Consider healthy if score >= 80%
  } catch (error) {
    console.error('Error checking article update health:', error);
    return false;
  }
}

function calculateHealthScore(metrics) {
  const scores = metrics.map(metric => {
    const successRate = metric.successful_requests / metric.total_requests;
    const errorPenalty = metric.error_count * 0.1;
    return Math.max(0, successRate - errorPenalty);
  });

  return scores.reduce((sum, score) => sum + score, 0) / scores.length;
}

function getHealthStatus(score) {
  if (score >= 0.9) return 'excellent';
  if (score >= 0.8) return 'good';
  if (score >= 0.6) return 'fair';
  return 'poor';
}

// Monitor rate limits
export async function checkRateLimits() {
  const { data: metrics } = await supabase
    .from('article_metrics')
    .select('total_requests, created_at')
    .gte('created_at', new Date(Date.now() - 3600000).toISOString()); // Last hour

  if (!metrics?.length) return true;

  const totalRequests = metrics.reduce((sum, m) => sum + m.total_requests, 0);
  return totalRequests < 500; // Hour limit
}

// Get system status report
export async function getSystemStatus() {
  const [articleHealth, withinLimits] = await Promise.all([
    checkArticleUpdateHealth(),
    checkRateLimits()
  ]);

  return {
    articleUpdates: {
      healthy: articleHealth,
      withinLimits
    },
    lastCheck: new Date().toISOString()
  };
}