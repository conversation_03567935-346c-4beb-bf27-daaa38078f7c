import { supabase } from '../supabase';
import { handleApiError } from '../utils/errorHandler';
import { v4 as uuidv4 } from 'uuid';

const MAX_HISTORY_ITEMS = 50;
const MAX_RETRIES = 3;
const RETRY_DELAY = 1000;

async function withRetry(operation) {
  let lastError;
  for (let i = 0; i < MAX_RETRIES; i++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      if (i < MAX_RETRIES - 1) {
        await new Promise(resolve => setTimeout(resolve, RETRY_DELAY * Math.pow(2, i)));
      }
    }
  }
  throw lastError;
}

export const loadChatHistory = async (userId) => {
  try {
    return await withRetry(async () => {
      const { data, error } = await supabase
        .from('users_chat_history')
        .select(`
          id,
          message_type,
          content,
          metadata,
          created_at,
          image_analysis (
            image_url,
            analysis_results,
            processing_status
          )`)
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(MAX_HISTORY_ITEMS);
  
      if (error && error.code !== 'PGRST116') throw error;
      return data;
    });
  } catch (err) {
    console.error('Error loading chat history:', err);
    throw err;
  }
};

export const saveChatMessage = async (userId, content, messageType, imageUrl = null, parentMessageId = null) => {
  try {
    return await withRetry(async () => {
      // Start transaction
      const { data: message, error: messageError } = await supabase
        .from('users_chat_history')
        .insert([{
          user_id: userId,
          content,
          message_type: messageType,
          parent_message_id: parentMessageId,
          metadata: {
            has_image: !!imageUrl
          }
        }])
        .select()
        .single();

      if (messageError) throw messageError;

      // If there's an image, save the image analysis record
      if (imageUrl) {
        const { error: imageError } = await supabase
          .from('image_analysis')
          .insert([{
            user_id: userId,
            chat_message_id: message.id,
            image_url: imageUrl
          }]);

        if (imageError) throw imageError;
      }

      return message;
    });
  } catch (err) {
    console.error('Error saving chat message:', err);
    throw new Error(handleApiError(err));
  }
};

export const startConversation = async (userId) => {
  try {
    const sessionId = uuidv4();
    const { data, error } = await supabase
      .from('conversation_logs')
      .insert([{
        user_id: userId,
        session_id: sessionId,
        metadata: {
          user_agent: navigator.userAgent,
          platform: navigator.platform
        }
      }])
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (err) {
    console.error('Error starting conversation:', err);
    throw new Error(handleApiError(err));
  }
};

export const endConversation = async (conversationId) => {
  try {
    const { error } = await supabase
      .from('conversation_logs')
      .update({ ended_at: new Date().toISOString() })
      .eq('id', conversationId);

    if (error) throw error;
  } catch (err) {
    console.error('Error ending conversation:', err);
    throw new Error(handleApiError(err));
  }
};