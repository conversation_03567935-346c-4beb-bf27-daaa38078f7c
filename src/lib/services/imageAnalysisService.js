import { supabase } from '../supabase';
import { handleApiError } from '../utils/errorHandler';
import { getDentalAdvice } from '../api/openai';

export const analyzeImage = async (imageUrl, userId) => {
  try {
    // Create analysis record
    const { data: analysis, error: createError } = await supabase
      .from('image_analysis')
      .insert({
        user_id: userId,
        image_url: imageUrl,
        processing_status: 'processing',
        metadata: {
          started_at: new Date().toISOString()
        }
      })
      .select()
      .single();

    if (createError) throw createError;

    try {
      // Get image analysis from OpenAI
      const analysisPrompt = `Please analyze this dental image in detail and provide:
1. Technical Image Details
2. Visual Content Analysis
3. Dental Observations
4. Potential Areas of Concern
5. Recommendations`;

      const aiAnalysis = await getDentalAdvice(analysisPrompt, imageUrl);

      // Update analysis record with results
      const { error: updateError } = await supabase
        .from('image_analysis')
        .update({
          analysis_results: {
            content: aiAnalysis,
            completed_at: new Date().toISOString()
          },
          processing_status: 'completed'
        })
        .eq('id', analysis.id);

      if (updateError) throw updateError;

      return {
        id: analysis.id,
        results: aiAnalysis,
        status: 'completed'
      };
    } catch (analysisError) {
      // Update record with error
      await supabase
        .from('image_analysis')
        .update({
          processing_status: 'failed',
          error_details: analysisError.message
        })
        .eq('id', analysis.id);

      throw analysisError;
    }
  } catch (error) {
    console.error('Image analysis error:', error);
    throw new Error(handleApiError(error));
  }
};

export const getImageAnalysisStatus = async (analysisId) => {
  try {
    const { data, error } = await supabase
      .from('image_analysis')
      .select('*')
      .eq('id', analysisId)
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error getting analysis status:', error);
    throw new Error(handleApiError(error));
  }
};