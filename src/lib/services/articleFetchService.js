import { supabase as browserSupabase } from '../supabase.js';
import { XMLParser } from 'fast-xml-parser';
import { JSD<PERSON> } from 'jsdom';
import { encode } from 'html-entities';

// Use global supabase client if available (for Node.js scripts), otherwise use browser client
const supabase = global.supabase || browserSupabase;

const TRUSTED_SOURCES = {
  ADA: {
    name: 'American Dental Association',
    feedUrl: 'https://www.ada.org/publications/ada-news/feeds/adanews',
    type: 'rss'
  },
  DENTAL_IQ: {
    name: 'DentistryIQ',
    feedUrl: 'https://www.dentistryiq.com/rss/all-content.feed',
    type: 'rss'
  },
  DENTAL_ECONOMICS: {
    name: 'Dental Economics',
    feedUrl: 'https://www.dentaleconomics.com/rss/all-content.feed',
    type: 'rss'
  },
  COLGATE_ORAL_HEALTH: {
    name: 'Colgate Oral Health Network',
    feedUrl: 'https://www.colgateprofessional.com/news/rss',
    type: 'rss'
  },
  DENTAL_TRIBUNE: {
    name: 'Dental Tribune',
    feedUrl: 'https://www.dental-tribune.com/feed/',
    type: 'rss'
  },
  JADA: {
    name: 'Journal of the American Dental Association',
    feedUrl: 'https://jada.ada.org/current.rss',
    type: 'rss'
  },
  ORAL_HEALTH_GROUP: {
    name: 'Oral Health Group',
    feedUrl: 'https://www.oralhealthgroup.com/feed/',
    type: 'rss'
  },
  DENTAL_PRODUCTS_REPORT: {
    name: 'Dental Products Report',
    feedUrl: 'https://www.dentalproductsreport.com/rss',
    type: 'rss'
  }
};

// Extract items from RSS feed based on feed structure
const extractItems = (feed) => {
  if (feed.rss?.channel?.item) {
    return Array.isArray(feed.rss.channel.item) ? feed.rss.channel.item : [feed.rss.channel.item];
  }
  if (feed.feed?.entry) {
    return Array.isArray(feed.feed.entry) ? feed.feed.entry : [feed.feed.entry];
  }
  if (feed.rdf?.item) {
    return Array.isArray(feed.rdf.item) ? feed.rdf.item : [feed.rdf.item];
  }
  return [];
};

// Extract content from various possible fields and clean it
const extractContent = (item) => {
  const content = item.description || 
                 item['content:encoded'] || 
                 item.content?.['#text'] ||
                 item.content ||
                 item.summary ||
                 '';
                 
  // Clean the content
  const dom = new JSDOM(content);
  const text = dom.window.document.body.textContent || '';
  return text.trim();
};

// Extract and clean title
const extractTitle = (item) => {
  const title = item.title?.['#text'] || item.title || '';
  const dom = new JSDOM(title);
  return dom.window.document.body.textContent?.trim() || '';
};

// Extract and clean link
const extractLink = (item) => {
  const link = item.link?.['#text'] || item.link || '';
  return link.trim();
};

// Validate article data with detailed error messages
const validateArticle = (article) => {
  const errors = [];
  
  if (!article.title?.trim()) {
    errors.push('Article must have a non-empty title');
  }
  
  if (!article.content?.trim()) {
    errors.push('Article must have non-empty content');
  }
  
  if (!article.source?.trim()) {
    errors.push('Article must have a source');
  }
  
  if (!article.link?.trim()) {
    errors.push('Article must have a valid link');
  }
  
  // Content should be at least 100 characters
  if (article.content?.trim().length < 100) {
    errors.push('Article content is too short (minimum 100 characters)');
  }
  
  if (errors.length > 0) {
    throw new Error(errors.join('; '));
  }
};

// Retry configuration
const RETRY_CONFIG = {
  maxRetries: 3,
  initialDelay: 1000, // 1 second
  maxDelay: 5000 // 5 seconds
};

// Sleep function for delays
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Exponential backoff delay
const getRetryDelay = (attempt) => {
  const delay = Math.min(
    RETRY_CONFIG.maxDelay,
    RETRY_CONFIG.initialDelay * Math.pow(2, attempt)
  );
  return delay * (0.75 + Math.random() * 0.5); // Add jitter
};

// Fetch with retry
const fetchWithRetry = async (url, options, attempt = 1) => {
  try {
    const response = await fetch(url, options);
    if (response.ok) {
      return response;
    }
    
    throw new Error(`HTTP error! status: ${response.status}`);
  } catch (error) {
    if (attempt >= RETRY_CONFIG.maxRetries) {
      throw error;
    }
    
    const delay = getRetryDelay(attempt);
    console.log(`Retry attempt ${attempt} for ${url} after ${delay}ms`);
    await sleep(delay);
    
    return fetchWithRetry(url, options, attempt + 1);
  }
};

// Fetch articles from RSS feed
const fetchRssFeed = async (source) => {
  try {
    console.log(`Fetching from ${source.name}...`);
    const response = await fetchWithRetry(source.feedUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; SmiloBot/1.0; +https://smilo.dental)',
        'Accept': 'application/rss+xml, application/xml, text/xml, application/atom+xml'
      }
    });
    
    const xmlData = await response.text();
    
    // Validate XML data
    if (!xmlData.trim() || !xmlData.includes('<?xml')) {
      throw new Error('Invalid XML data received');
    }
    
    const parser = new XMLParser({
      ignoreAttributes: false,
      attributeNamePrefix: "@_",
      parseAttributeValue: true,
      trimValues: true
    });
    
    const feed = parser.parse(xmlData);
    const items = extractItems(feed);
    
    if (!items.length) {
      console.warn(`No items found in feed from ${source.name}`);
      return [];
    }
    
    // Process and validate each item
    const articles = items.map(item => {
      const article = {
        title: extractTitle(item),
        content: extractContent(item),
        summary: extractSummary(extractContent(item)),
        source: source.name,
        sourceType: 'rss',
        sourceUrl: source.feedUrl,
        link: extractLink(item),
        pubDate: item.pubDate || item.published || item.date || new Date().toISOString(),
        authors: item.author?.name || item.author || item.creator || '',
        tags: extractTags(item.category || item.tags)
      };
      
      try {
        validateArticle(article);
        return article;
      } catch (error) {
        console.warn(`Skipping invalid article from ${source.name}:`, error.message);
        return null;
      }
    });
    
    // Filter out invalid articles
    return articles.filter(article => article !== null);
  } catch (error) {
    console.error(`Error fetching RSS feed from ${source.name}:`, error);
    await logError('rss_fetch', error, { source: source.name });
    return [];
  }
};

// Extract summary from content
const extractSummary = (content) => {
  const dom = new JSDOM(content);
  const text = dom.window.document.body.textContent || '';
  return text.slice(0, 300) + (text.length > 300 ? '...' : '');
};

// Extract and normalize tags
const extractTags = (categories) => {
  if (!categories) return [];
  const tags = Array.isArray(categories) ? categories : [categories];
  return tags.map(tag => tag.toLowerCase().trim());
};

// Check for duplicate articles
const isDuplicate = async (article) => {
  const { data } = await supabase
    .from('dental_articles')
    .select('id')
    .eq('title', article.title)
    .eq('source', article.source)
    .single();
  
  return !!data;
};

// Store article in Supabase
const storeArticle = async (article) => {
  try {
    validateArticle(article);
    
    const duplicate = await isDuplicate(article);
    if (duplicate) {
      return { status: 'skipped', reason: 'duplicate' };
    }
    
    const articleData = {
      title: article.title,
      content: article.content,
      summary: article.summary,
      source: article.source,
      source_type: article.sourceType || 'unknown',
      source_url: article.sourceUrl || '',
      link: article.link,
      pub_date: article.pubDate ? new Date(article.pubDate).toISOString() : new Date().toISOString(),
      authors: article.authors || '',
      tags: article.tags || [],
      status: 'published',
      inserted_at: new Date().toISOString()
    };
    
    const { data, error } = await supabase
      .from('dental_articles')
      .insert(articleData)
      .select('id')
      .single();
      
    if (error) {
      throw new Error(`Failed to store article: ${error.message}`);
    }
    
    return { 
      status: 'success', 
      id: data.id,
      title: article.title
    };
  } catch (error) {
    console.error(`Error storing article "${article.title}":`, error.message);
    await logError('article_storage', error, { title: article.title });
    return { 
      status: 'error', 
      title: article.title,
      error: error.message 
    };
  }
};

// Log errors to system_logs
const logError = async (component, error, metadata = {}) => {
  try {
    await supabase
      .from('system_logs')
      .insert({
        component,
        level: 'error',
        message: error.message,
        metadata
      });
  } catch (e) {
    console.error('Failed to log error:', e);
  }
};

// Update metrics
const updateMetrics = async (results) => {
  try {
    const successful = results.filter(r => r.status === 'success').length;
    const failed = results.filter(r => r.status === 'error').length;
    const skipped = results.filter(r => r.status === 'skipped').length;
    
    await supabase
      .from('article_metrics')
      .insert({
        total_requests: results.length,
        successful_requests: successful,
        failed_requests: failed,
        error_count: failed
      });
  } catch (error) {
    console.error('Failed to update metrics:', error);
  }
};

// Main function to fetch and store articles
export const fetchAndStoreArticles = async () => {
  console.log('Starting article fetch process...');
  const results = [];
  
  for (const source of Object.values(TRUSTED_SOURCES)) {
    console.log(`Fetching articles from ${source.name}...`);
    const articles = await fetchRssFeed(source);
    
    for (const article of articles) {
      const result = await storeArticle(article);
      results.push(result);
    }
  }
  
  await updateMetrics(results);
  
  const summary = {
    total: results.length,
    successful: results.filter(r => r.status === 'success').length,
    skipped: results.filter(r => r.status === 'skipped').length,
    failed: results.filter(r => r.status === 'error').length
  };
  
  console.log('Article fetch process completed:', summary);
  return summary;
};

// Function to clean up old articles
export const cleanupOldArticles = async (maxArticles = 1000) => {
  try {
    // Get total count
    const { count, error: countError } = await supabase
      .from('dental_articles')
      .select('*', { count: 'exact', head: true });
      
    if (countError) {
      console.error('Error getting article count:', countError);
      return { deleted: 0, remaining: 0 };
    }
      
    if (!count || count <= maxArticles) {
      return { deleted: 0, remaining: count || 0 };
    }
    
    // Get IDs of oldest articles to delete
    const toDelete = count - maxArticles;
    const { data: oldestArticles, error: fetchError } = await supabase
      .from('dental_articles')
      .select('id')
      .order('inserted_at', { ascending: true })
      .limit(toDelete);
      
    if (fetchError || !oldestArticles) {
      console.error('Error fetching oldest articles:', fetchError);
      return { deleted: 0, remaining: count };
    }
    
    // Delete oldest articles
    const ids = oldestArticles.map(article => article.id);
    const { error: deleteError } = await supabase
      .from('dental_articles')
      .delete()
      .in('id', ids);
    
    if (deleteError) {
      console.error('Error deleting old articles:', deleteError);
      return { deleted: 0, remaining: count };
    }
    
    return {
      deleted: ids.length,
      remaining: count - ids.length
    };
  } catch (error) {
    console.error('Error in cleanupOldArticles:', error);
    return { deleted: 0, remaining: 0 };
  }
};

// Export functions for use in other modules
export {
  TRUSTED_SOURCES,
  fetchRssFeed,
  storeArticle,
  isDuplicate,
  validateArticle,
  updateMetrics
}; 