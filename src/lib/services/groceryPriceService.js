// groceryPriceService.js
// Service to fetch grocery prices from local stores

// Check if we're in a browser environment
const isBrowser = typeof window !== 'undefined';

// Use window._env or default to 'demo-key' if environment variables aren't available
const API_KEY = (isBrowser && window._env && window._env.GROCERY_API_KEY) || 'demo-key';

// API for getting real grocery store data (using Overpass API for OpenStreetMap data)
const OVERPASS_API = 'https://overpass-api.de/api/interpreter';

/**
 * Get local grocery stores based on user's zip code or coordinates
 * @param {string} zipCode - User's zip code
 * @param {number} radiusMiles - Search radius in miles (default: 5)
 * @param {Object} coordinates - Optional lat/lng coordinates to use instead of zip
 * @returns {Promise<Array>} - Array of local grocery stores
 */
export const getLocalGroceryStores = async (zipCode, radiusMiles = 5, coordinates = null) => {
  try {
    // Convert miles to meters for the API
    const radiusMeters = radiusMiles * 1609.34;
    
    let lat, lng;
    
    if (coordinates) {
      // Use provided coordinates
      lat = coordinates.latitude;
      lng = coordinates.longitude;
    } else {
      // Get coordinates from ZIP code using Nominatim
      try {
        const geocodeResponse = await fetch(
          `https://nominatim.openstreetmap.org/search?format=json&postalcode=${zipCode}&country=US&limit=1`,
          {
            headers: {
              'Accept': 'application/json',
              'User-Agent': 'SmiloApp/1.0 (https://smilo.dental)'
            }
          }
        );
        
        const geocodeData = await geocodeResponse.json();
        
        if (geocodeData.length === 0) {
          throw new Error('Could not find coordinates for ZIP code');
        }
        
        lat = parseFloat(geocodeData[0].lat);
        lng = parseFloat(geocodeData[0].lon);
      } catch (geocodeError) {
        console.error('Error geocoding ZIP code:', geocodeError);
        return getStoresByBrand(radiusMiles);
      }
    }
    
    // Use Overpass API to find grocery stores (supermarkets, grocers, etc.)
    const overpassQuery = `
      [out:json];
      (
        node["shop"="supermarket"](around:${radiusMeters},${lat},${lng});
        node["shop"="grocery"](around:${radiusMeters},${lat},${lng});
        node["shop"="convenience"](around:${radiusMeters},${lat},${lng});
        node["shop"="organic"](around:${radiusMeters},${lat},${lng});
      );
      out body;
    `;
    
    const response = await fetch(OVERPASS_API, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: `data=${encodeURIComponent(overpassQuery)}`
    });
    
    if (!response.ok) {
      throw new Error('Failed to fetch stores from Overpass API');
    }
    
    const data = await response.json();
    
    // If no results found, use brand-based stores
    if (!data.elements || data.elements.length === 0) {
      return getStoresByBrand(radiusMiles);
    }
    
    // Process and format the results
    const stores = data.elements.map(store => {
      const storeLat = store.lat;
      const storeLng = store.lon;
      
      // Calculate distance in miles
      const distance = calculateDistance(lat, lng, storeLat, storeLng);
      const distanceMiles = (distance * 0.621371).toFixed(1); // Convert km to miles
      
      // Determine if this matches any known brand
      const storeName = store.tags.name || getGenericStoreName(store.tags.shop);
      const brand = detectStoreBrand(storeName);
      const storeType = brand ? brand : store.tags.shop || 'supermarket';
      
      return {
        id: `store-${store.id}`,
        name: storeName,
        type: storeType,
        brand: brand || null,
        distance: `${distanceMiles} miles`,
        distanceValue: parseFloat(distanceMiles),
        coordinates: {
          latitude: storeLat,
          longitude: storeLng
        },
        address: formatAddress(store.tags),
        searchUrl: brand ? getStoreSearchUrl(brand) : null
      };
    });
    
    // Sort by distance
    return stores.sort((a, b) => a.distanceValue - b.distanceValue);
  } catch (error) {
    console.error('Error fetching local grocery stores:', error);
    return getStoresByBrand(radiusMiles);
  }
};

/**
 * Detect store brand based on name
 * @param {string} storeName - Name of the store
 * @returns {string|null} - Brand name if recognized, null otherwise
 */
function detectStoreBrand(storeName) {
  if (!storeName) return null;
  
  const name = storeName.toLowerCase();
  
  // Major supermarket chains
  if (name.includes('walmart')) return 'walmart';
  if (name.includes('target')) return 'target';
  if (name.includes('kroger')) return 'kroger';
  if (name.includes('safeway')) return 'safeway';
  if (name.includes('publix')) return 'publix';
  if (name.includes('aldi')) return 'aldi';
  if (name.includes('trader joe')) return 'traderjoes';
  if (name.includes('whole foods')) return 'wholefoods';
  if (name.includes('costco')) return 'costco';
  if (name.includes('sam\'s club') || name.includes('sams club')) return 'samsclub';
  if (name.includes('meijer')) return 'meijer';
  if (name.includes('wegmans')) return 'wegmans';
  if (name.includes('albertsons')) return 'albertsons';
  if (name.includes('food lion')) return 'foodlion';
  if (name.includes('hy-vee') || name.includes('hyvee')) return 'hyvee';
  if (name.includes('giant')) return 'giant';
  if (name.includes('stop & shop') || name.includes('stop and shop')) return 'stopandshop';
  
  return null;
}

/**
 * Get search URL for a specific store brand
 * @param {string} brand - Store brand
 * @returns {string} - Search URL template for the store
 */
function getStoreSearchUrl(brand) {
  switch (brand) {
    case 'walmart':
      return 'https://www.walmart.com/search?q={searchTerm}';
    case 'target':
      return 'https://www.target.com/s?searchTerm={searchTerm}';
    case 'kroger':
      return 'https://www.kroger.com/search?query={searchTerm}';
    case 'safeway':
      return 'https://www.safeway.com/shop/search-results.html?q={searchTerm}';
    case 'publix':
      return 'https://www.publix.com/search?searchTerm={searchTerm}';
    case 'aldi':
      return 'https://www.aldi.us/en/search/?text={searchTerm}';
    case 'traderjoes':
      return 'https://www.traderjoes.com/home/<USER>';
    case 'wholefoods':
      return 'https://www.wholefoodsmarket.com/search?text={searchTerm}';
    case 'costco':
      return 'https://www.costco.com/CatalogSearch?keyword={searchTerm}';
    case 'samsclub':
      return 'https://www.samsclub.com/s/{searchTerm}';
    default:
      return null;
  }
}

/**
 * Get popular grocery store brands with reasonable distances
 * @param {number} radiusMiles - Search radius in miles
 * @returns {Array} - Array of brand-based stores
 */
function getStoresByBrand(radiusMiles = 5) {
  // Generate brand-based store data with distances within the requested radius
  return [
    { 
      id: 'walmart-1', 
      name: 'Walmart Supercenter', 
      brand: 'walmart',
      type: 'supermarket',
      distance: `${(radiusMiles * 0.3).toFixed(1)} miles`, 
      distanceValue: radiusMiles * 0.3,
      searchUrl: 'https://www.walmart.com/search?q={searchTerm}'
    },
    { 
      id: 'target-1', 
      name: 'Target', 
      brand: 'target',
      type: 'supermarket',
      distance: `${(radiusMiles * 0.4).toFixed(1)} miles`, 
      distanceValue: radiusMiles * 0.4,
      searchUrl: 'https://www.target.com/s?searchTerm={searchTerm}'
    },
    { 
      id: 'kroger-1', 
      name: 'Kroger', 
      brand: 'kroger',
      type: 'supermarket',
      distance: `${(radiusMiles * 0.6).toFixed(1)} miles`, 
      distanceValue: radiusMiles * 0.6,
      searchUrl: 'https://www.kroger.com/search?query={searchTerm}'
    },
    { 
      id: 'wholefoods-1', 
      name: 'Whole Foods Market', 
      brand: 'wholefoods',
      type: 'organic',
      distance: `${(radiusMiles * 0.8).toFixed(1)} miles`, 
      distanceValue: radiusMiles * 0.8,
      searchUrl: 'https://www.wholefoodsmarket.com/search?text={searchTerm}'
    }
  ];
}

/**
 * Helper function to calculate distance between two points using Haversine formula
 * @param {number} lat1 - Latitude of point 1
 * @param {number} lng1 - Longitude of point 1
 * @param {number} lat2 - Latitude of point 2
 * @param {number} lng2 - Longitude of point 2
 * @returns {number} - Distance in kilometers
 */
function calculateDistance(lat1, lng1, lat2, lng2) {
  const R = 6371; // Radius of the Earth in km
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLng = (lng2 - lng1) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLng/2) * Math.sin(dLng/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
}

/**
 * Format address from OSM tags
 * @param {Object} tags - OSM tags
 * @returns {string} - Formatted address
 */
function formatAddress(tags) {
  const parts = [];
  
  if (tags['addr:housenumber'] && tags['addr:street']) {
    parts.push(`${tags['addr:housenumber']} ${tags['addr:street']}`);
  }
  
  if (tags['addr:city']) {
    parts.push(tags['addr:city']);
  }
  
  if (tags['addr:state']) {
    parts.push(tags['addr:state']);
  }
  
  return parts.length > 0 ? parts.join(', ') : 'Address unavailable';
}

/**
 * Generate a generic store name based on type
 * @param {string} shopType - OSM shop type tag
 * @returns {string} - Generic store name
 */
function getGenericStoreName(shopType) {
  switch (shopType) {
    case 'supermarket':
      return 'Local Supermarket';
    case 'grocery':
      return 'Grocery Store';
    case 'convenience':
      return 'Convenience Store';
    case 'organic':
      return 'Organic Market';
    default:
      return 'Local Food Store';
  }
}

/**
 * Get price range for an ingredient based on estimated prices
 * @param {string} ingredient - Ingredient name
 * @param {string} storeId - Optional store ID to check specific store
 * @returns {Promise<Object>} - Price information for the ingredient with range
 */
export const getIngredientPriceRange = async (ingredient, storeId = null) => {
  try {
    // Get the base price range for this ingredient
    const priceRange = getEstimatedPriceRange(ingredient);
    const storePrices = {};
    
    // If a specific store is provided, get just that store's data
    const stores = storeId 
      ? [{ id: storeId }] 
      : [
          { id: 'walmart-1', priceFactor: 0.9, brand: 'walmart' },
          { id: 'target-1', priceFactor: 1.0, brand: 'target' },
          { id: 'kroger-1', priceFactor: 1.05, brand: 'kroger' },
          { id: 'wholefoods-1', priceFactor: 1.3, brand: 'wholefoods' }
        ];
    
    stores.forEach(store => {
      const priceFactor = store.priceFactor || 1.0;
      const lowerPrice = (priceRange.min * priceFactor).toFixed(2);
      const upperPrice = (priceRange.max * priceFactor).toFixed(2);
      
      // For display, we can show a range or a single price if they're close
      const priceDisplay = lowerPrice === upperPrice 
        ? `$${lowerPrice}` 
        : `$${lowerPrice} - $${upperPrice}`;
      
      // Is it SNAP eligible?
      const snapEligible = determineSnapEligibility(ingredient);
      
      // Is it dental-friendly?
      const dentalBenefits = getDentalBenefits(ingredient);
      
      storePrices[store.id] = {
        priceRange: { min: parseFloat(lowerPrice), max: parseFloat(upperPrice) },
        price: priceDisplay,
        inStock: Math.random() > 0.1, // 90% chance of being in stock
        snapEligible,
        dentalBenefits,
        brand: store.brand || null,
        searchUrl: store.brand ? 
          getStoreSearchUrl(store.brand).replace('{searchTerm}', encodeURIComponent(ingredient)) : 
          null
      };
    });
    
    return {
      ingredient,
      stores: storePrices,
      note: "Price may vary slightly by store"
    };
  } catch (error) {
    console.error('Error getting ingredient price range:', error);
    return { ingredient, stores: {} };
  }
};

/**
 * Get total price range for a recipe at a specific store
 * @param {Array} ingredients - List of recipe ingredients
 * @param {string} storeId - Store ID to check
 * @returns {Promise<Object>} - Price information for the recipe with range
 */
export const getRecipePriceRange = async (ingredients, storeId) => {
  try {
    const pricePromises = ingredients.map(ingredient => 
      getIngredientPriceRange(ingredient, storeId)
    );
    
    const prices = await Promise.all(pricePromises);
    
    let totalMinPrice = 0;
    let totalMaxPrice = 0;
    let itemsInStock = 0;
    let snapEligibleCount = 0;
    let dentalFriendlyCount = 0;
    let searchUrl = null;
    let brand = null;
    
    // Calculate total price from all ingredients
    prices.forEach(item => {
      if (item.stores[storeId]) {
        const storeData = item.stores[storeId];
        totalMinPrice += storeData.priceRange.min;
        totalMaxPrice += storeData.priceRange.max;
        
        if (storeData.inStock) {
            itemsInStock++;
          }
        
        if (storeData.snapEligible) {
          snapEligibleCount++;
        }
        
        if (storeData.dentalBenefits.isFriendly) {
          dentalFriendlyCount++;
        }
        
        // Store the search URL and brand from any item (they should all be the same for this store)
        if (!searchUrl && storeData.searchUrl) {
          searchUrl = storeData.searchUrl;
        }
        
        if (!brand && storeData.brand) {
          brand = storeData.brand;
        }
      }
    });
    
    // Create the price display
    const priceRangeText = totalMinPrice === totalMaxPrice
      ? `$${totalMinPrice.toFixed(2)}`
      : `$${totalMinPrice.toFixed(2)} - $${totalMaxPrice.toFixed(2)}`;
    
    // Get the store name
    const storeName = getStoreNameById(storeId);
    
    return {
      totalPriceRange: { min: totalMinPrice, max: totalMaxPrice },
      totalPrice: priceRangeText,
      inStockPercentage: `${Math.round((itemsInStock / ingredients.length) * 100)}%`,
      snapEligiblePercentage: `${Math.round((snapEligibleCount / ingredients.length) * 100)}%`,
      dentalFriendlyPercentage: `${Math.round((dentalFriendlyCount / ingredients.length) * 100)}%`,
      storeName,
      brand,
      searchUrl,
      note: "Prices are estimates and may vary"
    };
  } catch (error) {
    console.error('Error calculating recipe price range:', error);
    return { totalPrice: 'Price unavailable', inStockPercentage: '0%' };
  }
};

/**
 * Get estimated price range for an ingredient
 * @param {string} ingredient - Ingredient name
 * @returns {Object} - Estimated price range { min, max }
 */
function getEstimatedPriceRange(ingredient) {
  // This function estimates a realistic price range for common ingredients
  // based on USDA data and manual price seeding
  const lowerIngredient = ingredient.toLowerCase();
  
  // Set default price range with 10% variation
  let basePrice = 2.99;
  let variation = 0.1;
  
  // Dairy products
  if (lowerIngredient.includes('milk')) { basePrice = 3.49; variation = 0.15; }
  if (lowerIngredient.includes('yogurt')) { basePrice = 4.29; variation = 0.2; }
  if (lowerIngredient.includes('cheese')) { basePrice = 4.99; variation = 0.25; }
  
  // Fruits
  if (lowerIngredient.includes('apple')) { basePrice = 1.49; variation = 0.3; }
  if (lowerIngredient.includes('banana')) { basePrice = 0.59; variation = 0.1; }
  if (lowerIngredient.includes('berries')) { basePrice = 3.99; variation = 0.4; }
  
  // Vegetables
  if (lowerIngredient.includes('broccoli')) { basePrice = 2.49; variation = 0.2; }
  if (lowerIngredient.includes('spinach') || lowerIngredient.includes('kale')) { basePrice = 3.29; variation = 0.15; }
  if (lowerIngredient.includes('carrot')) { basePrice = 1.79; variation = 0.2; }
  if (lowerIngredient.includes('pepper')) { basePrice = 1.99; variation = 0.25; }
  if (lowerIngredient.includes('cucumber')) { basePrice = 1.69; variation = 0.15; }
  if (lowerIngredient.includes('avocado')) { basePrice = 1.49; variation = 0.3; }
  if (lowerIngredient.includes('tomato')) { basePrice = 2.49; variation = 0.2; }
  
  // Proteins
  if (lowerIngredient.includes('salmon')) { basePrice = 12.99; variation = 0.3; }
  if (lowerIngredient.includes('sardine')) { basePrice = 3.99; variation = 0.15; }
  if (lowerIngredient.includes('nut')) { basePrice = 6.99; variation = 0.2; }
  if (lowerIngredient.includes('seed')) { basePrice = 4.99; variation = 0.15; }
  
  // Staples
  if (lowerIngredient.includes('oat')) { basePrice = 3.99; variation = 0.1; }
  if (lowerIngredient.includes('bread')) { basePrice = 3.49; variation = 0.2; }
  if (lowerIngredient.includes('flour')) { basePrice = 4.29; variation = 0.1; }
  if (lowerIngredient.includes('olive oil')) { basePrice = 8.99; variation = 0.25; }
  if (lowerIngredient.includes('hummus')) { basePrice = 3.99; variation = 0.15; }
  
  // Calculate min and max prices with variation
  const min = basePrice * (1 - variation);
  const max = basePrice * (1 + variation);
  
  return { min, max, basePrice };
}

/**
 * Determine if an ingredient is SNAP/EBT eligible
 * @param {string} ingredient - Ingredient name
 * @returns {boolean} - Whether the ingredient is SNAP eligible
 */
function determineSnapEligibility(ingredient) {
  // Most food items are SNAP eligible, with exceptions
  const lowerIngredient = ingredient.toLowerCase();
  
  // Items that are not SNAP eligible
  const nonSnapItems = [
    'alcohol', 'wine', 'beer', 'liquor', 
    'tobacco', 'cigarette', 'cigar', 
    'vitamin', 'supplement', 
    'medicine', 'medication',
    'pet food', 'dog food', 'cat food',
    'prepared food', 'hot food',
    'cleaning', 'soap', 'detergent',
    'paper product', 'toilet paper',
    'cosmetic', 'makeup'
  ];
  
  // Check if the ingredient contains any non-SNAP keywords
  for (const nonSnapItem of nonSnapItems) {
    if (lowerIngredient.includes(nonSnapItem)) {
      return false;
    }
  }
  
  // By default, most food items are SNAP eligible
  return true;
}

/**
 * Get dental benefits information for an ingredient
 * @param {string} ingredient - Ingredient name
 * @returns {Object} - Dental benefits information
 */
function getDentalBenefits(ingredient) {
  const lowerIngredient = ingredient.toLowerCase();
  
  // Dental-friendly foods
  const dentalFriendlyFoods = [
    { terms: ['cheese', 'milk', 'yogurt', 'dairy'], benefit: 'Rich in calcium and phosphates that help remineralize teeth' },
    { terms: ['leafy green', 'spinach', 'kale'], benefit: 'High in calcium which strengthens tooth enamel' },
    { terms: ['water', 'tea', 'green tea'], benefit: 'Helps wash away food particles and bacteria' },
    { terms: ['nut', 'seed', 'almond', 'walnut'], benefit: 'Contains proteins and minerals that strengthen teeth' },
    { terms: ['apple', 'pear', 'celery', 'carrot'], benefit: 'Fibrous texture helps clean teeth and stimulate gums' },
    { terms: ['onion', 'garlic'], benefit: 'Contains antibacterial compounds that reduce harmful bacteria' },
    { terms: ['fish', 'salmon', 'sardine'], benefit: 'Rich in vitamin D and calcium for stronger teeth' },
    { terms: ['broccoli', 'cauliflower'], benefit: 'Contains fiber that helps clean teeth and vitamin C for gum health' }
  ];
  
  // Dental-unfriendly foods
  const dentalUnfriendlyFoods = [
    { terms: ['candy', 'chocolate', 'sweet', 'sugar', 'caramel'], reason: 'High in sugar that feeds cavity-causing bacteria' },
    { terms: ['soda', 'soft drink', 'pop', 'cola'], reason: 'Acidic and sugary, can erode enamel and cause cavities' },
    { terms: ['citrus', 'lemon', 'lime', 'orange', 'grapefruit'], reason: 'Acidic foods can erode tooth enamel' },
    { terms: ['dried fruit', 'raisin'], reason: 'Sticky and high in sugar, can cling to teeth' },
    { terms: ['potato chip', 'chip', 'cracker'], reason: 'Can get stuck between teeth and convert to sugar' },
    { terms: ['bread', 'white bread'], reason: 'Starches break down into simple sugars' },
    { terms: ['alcohol', 'wine', 'beer'], reason: 'Can dry out the mouth and reduce saliva' },
    { terms: ['coffee', 'tea', 'wine'], reason: 'Can stain teeth and some contain acids' }
  ];
  
  // Check if the ingredient matches any dental-friendly foods
  for (const food of dentalFriendlyFoods) {
    for (const term of food.terms) {
      if (lowerIngredient.includes(term)) {
        return {
          isFriendly: true,
          benefit: food.benefit
        };
      }
    }
  }
  
  // Check if the ingredient matches any dental-unfriendly foods
  for (const food of dentalUnfriendlyFoods) {
    for (const term of food.terms) {
      if (lowerIngredient.includes(term)) {
        return {
          isFriendly: false,
          reason: food.reason
        };
      }
    }
  }
  
  // Default: assume neutral
  return {
    isFriendly: true,
    benefit: 'Part of a balanced diet'
  };
}

/**
 * Get store name by ID
 * @param {string} storeId - Store ID
 * @returns {string} - Store name
 */
function getStoreNameById(storeId) {
  // Brand store mapping
  const storeMap = {
    'walmart-1': 'Walmart Supercenter',
    'target-1': 'Target',
    'kroger-1': 'Kroger',
    'wholefoods-1': 'Whole Foods Market'
  };
  
  // If it's a real store ID (from OSM), extract from the ID
  if (storeId && storeId.startsWith('store-')) {
    return 'Local Grocery Store';
  }
  
  return storeMap[storeId] || 'Local Store';
}

export default {
  getLocalGroceryStores,
  getIngredientPriceRange,
  getRecipePriceRange,
  // Legacy API compatibility
  getIngredientPrice: getIngredientPriceRange,
  getRecipePrice: getRecipePriceRange
}; 