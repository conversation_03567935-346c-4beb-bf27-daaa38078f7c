import { supabase } from '../supabase';
import { handleApiError } from '../utils/errorHandler';

/**
 * Save a user's search query
 * @param {string} query - The search query
 * @param {string} category - The category of search (e.g., 'articles', 'providers')
 * @param {string} userId - The user's ID
 * @returns {Promise<void>}
 */
export const logSearchQuery = async (query, category, userId) => {
  if (!userId) return; // Only save for authenticated users
  
  try {
    const { error } = await supabase
      .from('user_search_history')
      .insert([{
        user_id: userId,
        query,
        category,
        timestamp: new Date().toISOString()
      }]);

    if (error) throw error;
  } catch (error) {
    console.error('Error saving search query:', error);
    throw handleApiError(error);
  }
};

/**
 * Get a user's search history
 * @param {string} userId - The user's ID
 * @param {string} category - Optional category filter
 * @param {number} limit - Maximum number of results to return
 * @returns {Promise<Array>} - Array of search history items
 */
export const getUserSearchHistory = async (userId, category = null, limit = 10) => {
  if (!userId) return []; // Return empty array for unauthenticated users
  
  try {
    let query = supabase
      .from('user_search_history')
      .select('*')
      .eq('user_id', userId)
      .order('timestamp', { ascending: false })
      .limit(limit);
    
    // Apply category filter if provided
    if (category) {
      query = query.eq('category', category);
    }
    
    const { data, error } = await query;

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error fetching search history:', error);
    throw handleApiError(error);
  }
};

/**
 * Clear a user's search history
 * @param {string} userId - The user's ID
 * @param {string} category - Optional category to clear (if omitted, clears all)
 * @returns {Promise<void>}
 */
export const clearSearchHistory = async (userId, category = null) => {
  if (!userId) return; // Only proceed for authenticated users
  
  try {
    let query = supabase
      .from('user_search_history')
      .delete()
      .eq('user_id', userId);
    
    // Apply category filter if provided
    if (category) {
      query = query.eq('category', category);
    }
    
    const { error } = await query;

    if (error) throw error;
  } catch (error) {
    console.error('Error clearing search history:', error);
    throw handleApiError(error);
  }
};

/**
 * Save a page visit by the user
 * @param {string} path - The page path
 * @param {string} title - The page title
 * @param {string} userId - The user's ID
 * @returns {Promise<void>}
 */
export const logPageVisit = async (path, title, userId) => {
  if (!userId) return; // Only save for authenticated users
  
  try {
    const { error } = await supabase
      .from('user_page_history')
      .insert([{
        user_id: userId,
        path,
        title,
        timestamp: new Date().toISOString()
      }]);

    if (error) throw error;
  } catch (error) {
    console.error('Error saving page visit:', error);
    throw handleApiError(error);
  }
};

/**
 * Get a user's page history
 * @param {string} userId - The user's ID
 * @param {number} limit - Maximum number of results to return
 * @returns {Promise<Array>} - Array of page history items
 */
export const getUserPageHistory = async (userId, limit = 10) => {
  if (!userId) return []; // Return empty array for unauthenticated users
  
  try {
    const { data, error } = await supabase
      .from('user_page_history')
      .select('*')
      .eq('user_id', userId)
      .order('timestamp', { ascending: false })
      .limit(limit);

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error fetching page history:', error);
    throw handleApiError(error);
  }
}; 