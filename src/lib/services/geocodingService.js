// Geocoding service using multiple providers for reliability
const NOMINATIM_API = 'https://nominatim.openstreetmap.org';
const ZIPPOPOTAM_API = 'https://api.zippopotam.us/us';

// Cache for geocoding results
const GEOCODING_CACHE = new Map();

export async function getCoordinatesFromZip(zipCode) {
  // Check cache first
  const cacheKey = `zip_${zipCode}`;
  if (GEOCODING_CACHE.has(cacheKey)) {
    return GEOCODING_CACHE.get(cacheKey);
  }

  try {
    // Try Zippopotam first
    const response = await fetch(`${ZIPPOPOTAM_API}/${zipCode}`);
    
    if (response.ok) {
      const data = await response.json();
      const coords = {
        latitude: parseFloat(data.places[0].latitude),
        longitude: parseFloat(data.places[0].longitude)
      };
      
      GEOCODING_CACHE.set(cacheKey, coords);
      return coords;
    }

    // Fallback to Nominatim if Zippopotam fails
    const nominatimResponse = await fetch(
      `${NOMINATIM_API}/search?postalcode=${zipCode}&country=USA&format=json`
    );

    if (!nominatimResponse.ok) {
      throw new Error('Invalid ZIP code');
    }

    const places = await nominatimResponse.json();
    if (!places.length) {
      throw new Error('Location not found');
    }

    const coords = {
      latitude: parseFloat(places[0].lat),
      longitude: parseFloat(places[0].lon)
    };

    GEOCODING_CACHE.set(cacheKey, coords);
    return coords;
  } catch (error) {
    console.error('Geocoding error:', error);
    throw new Error('Could not find coordinates for ZIP code');
  }
}