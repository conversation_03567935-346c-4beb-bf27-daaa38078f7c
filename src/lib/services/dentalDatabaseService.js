import { supabase } from '../supabase';
import { config } from '../config';

// Major dental databases and APIs
const DENTAL_DATABASES = {
  PUBMED: {
    name: 'PubMed',
    baseUrl: 'https://eutils.ncbi.nlm.nih.gov/entrez/eutils',
    description: 'Medical and dental research database from the US National Library of Medicine'
  },
  ADA: {
    name: 'American Dental Association',
    baseUrl: 'https://api.ada.org/v1',
    description: 'Clinical guidelines and standards from the American Dental Association'
  },
  NIH_ORAL_HEALTH: {
    name: 'NIH Oral Health Database',
    baseUrl: 'https://www.nidcr.nih.gov/api',
    description: 'National Institute of Dental and Craniofacial Research database'
  },
  WHO_ORAL_HEALTH: {
    name: 'WHO Oral Health Database',
    baseUrl: 'https://www.who.int/data/gho/info/athena-api',
    description: 'World Health Organization global oral health database'
  },
  COCHRANE_ORAL_HEALTH: {
    name: 'Cochrane Oral Health',
    baseUrl: 'https://oralhealth.cochrane.org/api',
    description: 'Systematic reviews for evidence-based dentistry'
  },
  DENTAL_IMAGES_DB: {
    name: 'Dental Image Database',
    baseUrl: 'https://dentalimages-api.org/v1',
    description: 'Repository of dental radiographs and clinical images'
  }
};

// Cache duration in days
const CACHE_DURATION = {
  RESEARCH: 7,    // Research data cached for 7 days
  CLINICAL: 30,   // Clinical guidelines cached for 30 days
  IMAGES: 90      // Image references cached for 90 days
};

/**
 * Search PubMed for dental research articles
 * @param {string} query - The search query
 * @param {number} limit - Maximum number of results
 * @returns {Promise<Array>} - Array of research articles
 */
export const searchPubMedArticles = async (query, limit = 5) => {
  try {
    // Check cache first
    const { data: cachedData } = await supabase
      .from('external_api_cache')
      .select('*')
      .eq('api_name', 'pubmed')
      .eq('query', query.toLowerCase())
      .order('created_at', { ascending: false })
      .limit(1);
    
    // Return cached data if found and not expired
    if (cachedData && cachedData.length > 0) {
      const cacheAge = new Date() - new Date(cachedData[0].created_at);
      const cacheAgeInDays = cacheAge / (1000 * 60 * 60 * 24);
      
      if (cacheAgeInDays < CACHE_DURATION.RESEARCH) {
        console.log('Using cached PubMed data');
        return cachedData[0].data;
      }
    }
    
    // In a real implementation, we would use the PubMed API
    // For demonstration, we'll return sample data
    const sampleArticles = [
      {
        pmid: '34567890',
        title: `Recent advances in dental imaging analysis: ${query}`,
        authors: ['Smith J', 'Johnson A', 'Williams B'],
        journal: 'Journal of Dental Research',
        year: new Date().getFullYear() - 1,
        abstract: `This study examines the latest techniques in ${query} with a focus on clinical applications and diagnostic accuracy.`,
        url: 'https://pubmed.ncbi.nlm.nih.gov/34567890/'
      },
      {
        pmid: '36789012',
        title: `A systematic review of ${query} in dental practice`,
        authors: ['Brown R', 'Davis M', 'Miller K'],
        journal: 'International Journal of Dentistry',
        year: new Date().getFullYear(),
        abstract: `This systematic review evaluates the effectiveness of ${query} in various clinical scenarios and provides evidence-based recommendations.`,
        url: 'https://pubmed.ncbi.nlm.nih.gov/36789012/'
      },
      {
        pmid: '35678901',
        title: `Clinical outcomes of ${query} interventions: A meta-analysis`,
        authors: ['Wilson T', 'Anderson P', 'Thomas S'],
        journal: 'Journal of the American Dental Association',
        year: new Date().getFullYear() - 2,
        abstract: `This meta-analysis compiles data from 32 clinical trials to evaluate the efficacy of ${query} in improving patient outcomes.`,
        url: 'https://pubmed.ncbi.nlm.nih.gov/35678901/'
      }
    ];
    
    // Cache the results
    await supabase
      .from('external_api_cache')
      .insert({
        api_name: 'pubmed',
        query: query.toLowerCase(),
        data: sampleArticles.slice(0, limit),
        created_at: new Date().toISOString()
      });
    
    return sampleArticles.slice(0, limit);
  } catch (error) {
    console.error('Error searching PubMed:', error);
    return [];
  }
};

/**
 * Fetch clinical guidelines from the ADA API
 * @param {string} condition - The dental condition
 * @returns {Promise<Object>} - Clinical guidelines
 */
export const fetchADAGuidelines = async (condition) => {
  try {
    // Check cache first
    const { data: cachedData } = await supabase
      .from('external_api_cache')
      .select('*')
      .eq('api_name', 'ada_guidelines')
      .eq('query', condition.toLowerCase())
      .order('created_at', { ascending: false })
      .limit(1);
    
    // Return cached data if found and not expired
    if (cachedData && cachedData.length > 0) {
      const cacheAge = new Date() - new Date(cachedData[0].created_at);
      const cacheAgeInDays = cacheAge / (1000 * 60 * 60 * 24);
      
      if (cacheAgeInDays < CACHE_DURATION.CLINICAL) {
        console.log('Using cached ADA guidelines');
        return cachedData[0].data;
      }
    }
    
    // In a real implementation, we would call the ADA API
    // For demonstration, we'll return sample data
    const guidelineData = generateGuidelineData(condition);
    
    // Cache the results
    await supabase
      .from('external_api_cache')
      .insert({
        api_name: 'ada_guidelines',
        query: condition.toLowerCase(),
        data: guidelineData,
        created_at: new Date().toISOString()
      });
    
    return guidelineData;
  } catch (error) {
    console.error('Error fetching ADA guidelines:', error);
    return null;
  }
};

/**
 * Search dental image database for reference images
 * @param {string} condition - The dental condition or feature
 * @param {number} limit - Maximum number of results
 * @returns {Promise<Array>} - Array of reference images
 */
export const searchDentalImages = async (condition, limit = 3) => {
  try {
    // Check cache first
    const { data: cachedData } = await supabase
      .from('external_api_cache')
      .select('*')
      .eq('api_name', 'dental_images')
      .eq('query', condition.toLowerCase())
      .order('created_at', { ascending: false })
      .limit(1);
    
    // Return cached data if found and not expired
    if (cachedData && cachedData.length > 0) {
      const cacheAge = new Date() - new Date(cachedData[0].created_at);
      const cacheAgeInDays = cacheAge / (1000 * 60 * 60 * 24);
      
      if (cacheAgeInDays < CACHE_DURATION.IMAGES) {
        console.log('Using cached dental images');
        return cachedData[0].data;
      }
    }
    
    // In a real implementation, we would call an actual dental image API
    // For demonstration, we'll return sample data
    const sampleImages = [
      {
        id: 'img123456',
        title: `Reference image for ${condition}`,
        type: 'radiograph',
        description: `Standard radiographic view showing typical features of ${condition}`,
        url: 'https://example.com/dental-images/radiograph1.jpg'
      },
      {
        id: 'img234567',
        title: `Clinical photograph of ${condition}`,
        type: 'intraoral',
        description: `Intraoral clinical photograph showing ${condition} in a patient`,
        url: 'https://example.com/dental-images/intraoral1.jpg'
      },
      {
        id: 'img345678',
        title: `Advanced case of ${condition}`,
        type: 'radiograph',
        description: `Radiographic image showing advanced stage of ${condition} with typical features`,
        url: 'https://example.com/dental-images/radiograph2.jpg'
      }
    ];
    
    // Cache the results
    await supabase
      .from('external_api_cache')
      .insert({
        api_name: 'dental_images',
        query: condition.toLowerCase(),
        data: sampleImages.slice(0, limit),
        created_at: new Date().toISOString()
      });
    
    return sampleImages.slice(0, limit);
  } catch (error) {
    console.error('Error searching dental images:', error);
    return [];
  }
};

/**
 * Search WHO oral health statistics
 * @param {string} condition - The dental condition
 * @returns {Promise<Object>} - Statistical data
 */
export const fetchWHOOralHealthStats = async (condition) => {
  try {
    // Check cache first
    const { data: cachedData } = await supabase
      .from('external_api_cache')
      .select('*')
      .eq('api_name', 'who_stats')
      .eq('query', condition.toLowerCase())
      .order('created_at', { ascending: false })
      .limit(1);
    
    // Return cached data if found and not expired
    if (cachedData && cachedData.length > 0) {
      const cacheAge = new Date() - new Date(cachedData[0].created_at);
      const cacheAgeInDays = cacheAge / (1000 * 60 * 60 * 24);
      
      if (cacheAgeInDays < CACHE_DURATION.CLINICAL) {
        console.log('Using cached WHO statistics');
        return cachedData[0].data;
      }
    }
    
    // In a real implementation, we would call the WHO API
    // For demonstration, we'll return sample data
    const statsData = {
      condition: condition,
      globalPrevalence: Math.round(Math.random() * 45 + 5) + '%',
      regionData: [
        { region: 'North America', prevalence: Math.round(Math.random() * 40 + 10) + '%' },
        { region: 'Europe', prevalence: Math.round(Math.random() * 40 + 10) + '%' },
        { region: 'Asia', prevalence: Math.round(Math.random() * 40 + 10) + '%' },
        { region: 'Africa', prevalence: Math.round(Math.random() * 40 + 10) + '%' }
      ],
      riskFactors: [
        'Poor oral hygiene',
        'Diet high in sugars',
        'Tobacco use',
        'Limited access to dental care'
      ],
      source: 'World Health Organization Global Oral Health Database'
    };
    
    // Cache the results
    await supabase
      .from('external_api_cache')
      .insert({
        api_name: 'who_stats',
        query: condition.toLowerCase(),
        data: statsData,
        created_at: new Date().toISOString()
      });
    
    return statsData;
  } catch (error) {
    console.error('Error fetching WHO statistics:', error);
    return null;
  }
};

/**
 * Search Cochrane Oral Health reviews
 * @param {string} topic - The dental topic
 * @param {number} limit - Maximum number of results
 * @returns {Promise<Array>} - Array of systematic reviews
 */
export const searchCochraneReviews = async (topic, limit = 2) => {
  try {
    // Check cache first
    const { data: cachedData } = await supabase
      .from('external_api_cache')
      .select('*')
      .eq('api_name', 'cochrane_reviews')
      .eq('query', topic.toLowerCase())
      .order('created_at', { ascending: false })
      .limit(1);
    
    // Return cached data if found and not expired
    if (cachedData && cachedData.length > 0) {
      const cacheAge = new Date() - new Date(cachedData[0].created_at);
      const cacheAgeInDays = cacheAge / (1000 * 60 * 60 * 24);
      
      if (cacheAgeInDays < CACHE_DURATION.RESEARCH) {
        console.log('Using cached Cochrane reviews');
        return cachedData[0].data;
      }
    }
    
    // In a real implementation, we would call the Cochrane API
    // For demonstration, we'll return sample data
    const sampleReviews = [
      {
        id: 'CD012345',
        title: `Interventions for ${topic}: a Cochrane systematic review`,
        authors: ['Adams A', 'Brown B', 'Clark C'],
        published: `${new Date().getFullYear() - 1} Apr 15`,
        conclusionSummary: `There is moderate-quality evidence that interventions for ${topic} are effective in improving clinical outcomes, but more research is needed.`,
        url: 'https://www.cochranelibrary.com/cdsr/doi/10.1002/14651858.CD012345'
      },
      {
        id: 'CD123456',
        title: `Comparison of treatment modalities for ${topic}`,
        authors: ['Davis D', 'Evans E', 'Fisher F'],
        published: `${new Date().getFullYear() - 2} Sep 3`,
        conclusionSummary: `There is high-quality evidence supporting the use of specific interventions for ${topic}, particularly in adult populations.`,
        url: 'https://www.cochranelibrary.com/cdsr/doi/10.1002/14651858.CD123456'
      }
    ];
    
    // Cache the results
    await supabase
      .from('external_api_cache')
      .insert({
        api_name: 'cochrane_reviews',
        query: topic.toLowerCase(),
        data: sampleReviews.slice(0, limit),
        created_at: new Date().toISOString()
      });
    
    return sampleReviews.slice(0, limit);
  } catch (error) {
    console.error('Error searching Cochrane reviews:', error);
    return [];
  }
};

/**
 * Comprehensive search across multiple dental databases
 * @param {string} query - The search query
 * @returns {Promise<Object>} - Combined results from various sources
 */
export const comprehensiveDentalSearch = async (query) => {
  try {
    // Normalize the query
    const normalizedQuery = query.toLowerCase().trim();
    
    // Determine if this is a condition, procedure, or general query
    const queryType = categorizeQuery(normalizedQuery);
    
    // Gather data from relevant sources in parallel
    const [articles, guidelines, images, stats, reviews] = await Promise.all([
      searchPubMedArticles(normalizedQuery, 3),
      queryType === 'condition' ? fetchADAGuidelines(normalizedQuery) : null,
      searchDentalImages(normalizedQuery, 2),
      queryType === 'condition' ? fetchWHOOralHealthStats(normalizedQuery) : null,
      queryType !== 'general' ? searchCochraneReviews(normalizedQuery, 1) : null
    ]);
    
    // Compile the results
    return {
      query: query,
      queryType: queryType,
      sources: {
        research: articles,
        guidelines: guidelines,
        images: images,
        statistics: stats,
        evidenceReviews: reviews
      },
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error('Error performing comprehensive dental search:', error);
    return {
      query: query,
      error: 'Failed to complete comprehensive search',
      timestamp: new Date().toISOString()
    };
  }
};

/**
 * Categorize a query as a condition, procedure, or general question
 * @param {string} query - The search query
 * @returns {string} - The query type (condition, procedure, or general)
 */
function categorizeQuery(query) {
  // Lists of common conditions and procedures for classification
  const conditions = [
    'caries', 'cavity', 'decay', 'gingivitis', 'periodontitis', 'pulpitis',
    'abscess', 'lesion', 'ulcer', 'tumor', 'cancer', 'malocclusion', 'fracture'
  ];
  
  const procedures = [
    'filling', 'restoration', 'root canal', 'extraction', 'implant',
    'crown', 'bridge', 'veneer', 'scaling', 'cleaning', 'whitening'
  ];
  
  // Check if query contains condition-related terms
  if (conditions.some(condition => query.includes(condition))) {
    return 'condition';
  }
  
  // Check if query contains procedure-related terms
  if (procedures.some(procedure => query.includes(procedure))) {
    return 'procedure';
  }
  
  // Default to general query
  return 'general';
}

/**
 * Generate sample ADA guidelines for a dental condition
 * @param {string} condition - The dental condition
 * @returns {Object} - Guidelines data
 */
function generateGuidelineData(condition) {
  return {
    condition: condition,
    recommendedDiagnostics: [
      'Clinical examination',
      'Radiographic assessment',
      'Periodontal probing when appropriate'
    ],
    treatmentOptions: [
      {
        option: 'Conservative management',
        description: `Initial approach for mild to moderate ${condition}`,
        evidenceLevel: 'Strong'
      },
      {
        option: 'Interventional treatment',
        description: `Recommended for advanced ${condition} or when conservative management fails`,
        evidenceLevel: 'Moderate'
      }
    ],
    preventionStrategies: [
      'Regular dental check-ups',
      'Proper oral hygiene practices',
      'Diet modification when appropriate'
    ],
    followUp: 'Recommend follow-up evaluation within 3-6 months',
    source: 'American Dental Association Clinical Practice Guidelines'
  };
}

/**
 * Initialize the external API cache table
 */
export const initExternalAPICache = async () => {
  try {
    const { error } = await supabase.rpc('create_table_if_not_exists', {
      table_name: 'external_api_cache',
      columns: `
        id uuid primary key default uuid_generate_v4(),
        api_name text not null,
        query text not null,
        data jsonb not null,
        created_at timestamp with time zone default now()
      `
    });
    
    if (error) throw error;
    console.log('External API cache table initialized');
  } catch (error) {
    console.error('Error initializing external API cache:', error);
  }
}; 