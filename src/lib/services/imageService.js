import { supabase } from '../supabase';
import { v4 as uuidv4 } from 'uuid';
import { validateImage } from '../utils/validation';
import { handleApiError } from '../utils/errorHandler';

const BUCKET_NAME = 'dental-images';
const MAX_RETRIES = 3;
const RETRY_DELAY = 1000;

async function withRetry(operation) {
  let lastError;
  for (let i = 0; i < MAX_RETRIES; i++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      if (i < MAX_RETRIES - 1) {
        await new Promise(resolve => setTimeout(resolve, RETRY_DELAY * Math.pow(2, i)));
      }
    }
  }
  throw lastError;
}

export const uploadImage = async (imageFile, userId) => {
  try {
    // Validate image
    const validatedFile = validateImage(imageFile);

    // Generate unique filename
    const fileExt = imageFile.name.split('.').pop().toLowerCase();
    const fileName = `${userId}/${uuidv4()}.${fileExt}`;

    // Upload to Supabase storage
    const { data, error: uploadError } = await withRetry(async () => {
      return supabase.storage
        .from(BUCKET_NAME)
        .upload(fileName, validatedFile, {
          cacheControl: '3600',
          upsert: false,
          contentType: imageFile.type
        });
    });

    if (uploadError) throw uploadError;

    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from(BUCKET_NAME)
      .getPublicUrl(fileName);

    // Create database record
    const { error: dbError } = await supabase
      .from('image_analysis')
      .insert({
        user_id: userId,
        image_url: publicUrl,
        processing_status: 'pending',
        metadata: {
          original_name: imageFile.name,
          size: imageFile.size,
          type: imageFile.type
        }
      });

    if (dbError) throw dbError;

    return publicUrl;
  } catch (error) {
    console.error('Image upload error:', error);
    throw new Error(handleApiError(error));
  }
};

export const getImageAnalysis = async (imageId) => {
  try {
    const { data, error } = await supabase
      .from('image_analysis')
      .select('*')
      .eq('id', imageId)
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error fetching image analysis:', error);
    throw new Error(handleApiError(error));
  }
};

export const deleteImage = async (imageUrl, userId) => {
  try {
    // Extract filename from URL
    const fileName = imageUrl.split('/').pop();
    const filePath = `${userId}/${fileName}`;

    // Delete from storage
    const { error: storageError } = await supabase.storage
      .from(BUCKET_NAME)
      .remove([filePath]);

    if (storageError) throw storageError;

    // Delete database record
    const { error: dbError } = await supabase
      .from('image_analysis')
      .delete()
      .eq('image_url', imageUrl)
      .eq('user_id', userId);

    if (dbError) throw dbError;
  } catch (error) {
    console.error('Error deleting image:', error);
    throw new Error(handleApiError(error));
  }
};