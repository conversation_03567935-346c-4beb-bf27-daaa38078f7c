import { supabase } from '../supabase';
import { isOnline } from '../utils/network';
import { config } from '../config';

// System status check
export const checkSystemStatus = async () => {
  const checks = {
    server: false,
    database: false,
    storage: false,
    ai: false
  };

  try {
    // Check server
    const serverResponse = await fetch('/');
    checks.server = serverResponse.ok;

    // Check database connection
    const { data, error } = await supabase
      .from('system_settings')
      .select('maintenance_mode')
      .single();
    checks.database = !error;

    // Check storage
    const { data: storageData } = await supabase.storage
      .from('dental-images')
      .list();
    checks.storage = true;

    // Check AI services
    const aiConfigured = config.openai.apiKey && config.deepseek.apiKey;
    checks.ai = aiConfigured;

    return {
      status: Object.values(checks).every(Boolean) ? 'operational' : 'issues',
      checks,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error('System check error:', error);
    return {
      status: 'error',
      checks,
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
};

// Browser diagnostics
export const runBrowserDiagnostics = () => {
  return {
    online: isOnline(),
    userAgent: navigator.userAgent,
    cookies: navigator.cookieEnabled,
    localStorage: (() => {
      try {
        localStorage.setItem('test', 'test');
        localStorage.removeItem('test');
        return true;
      } catch (e) {
        return false;
      }
    })(),
    ssl: window.location.protocol === 'https:',
    resolution: {
      width: window.innerWidth,
      height: window.innerHeight
    }
  };
};

// Clear browser cache
export const clearBrowserCache = async () => {
  try {
    // Clear localStorage
    localStorage.clear();
    
    // Clear sessionStorage
    sessionStorage.clear();
    
    // Clear service worker cache if exists
    if ('serviceWorker' in navigator) {
      const registrations = await navigator.serviceWorker.getRegistrations();
      for (const registration of registrations) {
        await registration.unregister();
      }
    }
    
    // Clear cache storage
    if ('caches' in window) {
      const cacheNames = await caches.keys();
      await Promise.all(cacheNames.map(name => caches.delete(name)));
    }
    
    return true;
  } catch (error) {
    console.error('Cache clear error:', error);
    return false;
  }
};

// Fix common issues
export const fixCommonIssues = async () => {
  const fixes = {
    cache: false,
    connection: false,
    permissions: false
  };

  try {
    // Clear cache
    fixes.cache = await clearBrowserCache();

    // Check connection
    fixes.connection = await new Promise(resolve => {
      const timeout = setTimeout(() => resolve(false), 5000);
      fetch('/').then(() => {
        clearTimeout(timeout);
        resolve(true);
      }).catch(() => {
        clearTimeout(timeout);
        resolve(false);
      });
    });

    // Check permissions
    fixes.permissions = await checkPermissions();

    return {
      success: Object.values(fixes).some(Boolean),
      fixes,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error('Fix attempt error:', error);
    return {
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
};

// Check permissions
const checkPermissions = async () => {
  const permissions = {
    storage: false,
    notifications: false,
    camera: false
  };

  try {
    if ('permissions' in navigator) {
      const storagePermission = await navigator.permissions.query({
        name: 'persistent-storage'
      });
      permissions.storage = storagePermission.state === 'granted';

      const notificationPermission = await navigator.permissions.query({
        name: 'notifications'
      });
      permissions.notifications = notificationPermission.state === 'granted';

      const cameraPermission = await navigator.permissions.query({
        name: 'camera'
      });
      permissions.camera = cameraPermission.state === 'granted';
    }

    return permissions;
  } catch (error) {
    console.error('Permission check error:', error);
    return permissions;
  }
};