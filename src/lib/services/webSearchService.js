import { supabase } from '../supabase';

// List of reputable dental information sources
const REPUTABLE_DENTAL_SOURCES = [
  'ada.org', // American Dental Association
  'aapd.org', // American Academy of Pediatric Dentistry
  'nidcr.nih.gov', // National Institute of Dental and Craniofacial Research
  'mouthhealthy.org', // ADA consumer website
  'cdc.gov/oralhealth', // CDC Oral Health
  'perio.org', // American Academy of Periodontology
  'aae.org', // American Association of Endodontists
  'aaoms.org', // American Association of Oral and Maxillofacial Surgeons
  'adha.org', // American Dental Hygienists Association
  'jada.ada.org', // Journal of the American Dental Association
  'pubmed.ncbi.nlm.nih.gov', // PubMed (for dental research)
  'cochranelibrary.com', // Cochrane Library (for evidence-based dentistry)
  'who.int/health-topics/oral-health', // World Health Organization
  'fdiworlddental.org', // World Dental Federation
  'dentalhealth.org', // Oral Health Foundation
];

/**
 * Search for dental information from reputable sources
 * @param {string} query - The search query
 * @param {number} limit - Maximum number of results to return
 * @returns {Promise<Array>} - Array of search results
 */
export const searchDentalInformation = async (query, limit = 5) => {
  try {
    console.log(`Searching for dental information: "${query}"`);
    
    // First, check if we have cached this query in Supabase
    const { data: cachedResults, error: cacheError } = await supabase
      .from('search_cache')
      .select('*')
      .eq('query', query.toLowerCase().trim())
      .order('created_at', { ascending: false })
      .limit(1);
    
    // If we have recent cached results (less than 7 days old), use them
    if (!cacheError && cachedResults && cachedResults.length > 0) {
      const cachedResult = cachedResults[0];
      const cacheAge = new Date() - new Date(cachedResult.created_at);
      const cacheAgeInDays = cacheAge / (1000 * 60 * 60 * 24);
      
      if (cacheAgeInDays < 7) {
        console.log('Using cached search results');
        return cachedResult.results;
      }
    }
    
    // If no valid cache, perform a new search
    // Note: In a real implementation, you would integrate with a search API
    // such as Google Custom Search API, Bing Web Search API, or similar
    
    // For demonstration purposes, we'll simulate a search response
    // In a real implementation, replace this with an actual API call
    const simulatedResults = await simulateWebSearch(query, limit);
    
    // Cache the results in Supabase for future use
    const { error: insertError } = await supabase
      .from('search_cache')
      .insert({
        query: query.toLowerCase().trim(),
        results: simulatedResults,
        created_at: new Date().toISOString()
      });
    
    if (insertError) {
      console.error('Error caching search results:', insertError);
    }
    
    return simulatedResults;
  } catch (error) {
    console.error('Error searching for dental information:', error);
    throw new Error('Failed to search for dental information');
  }
};

/**
 * Simulate a web search for dental information
 * In a real implementation, replace this with an actual API call
 * @param {string} query - The search query
 * @param {number} limit - Maximum number of results to return
 * @returns {Promise<Array>} - Array of simulated search results
 */
const simulateWebSearch = async (query, limit) => {
  // In a real implementation, you would call an actual search API here
  // For demonstration, we'll return simulated results based on the query
  
  // Wait a short time to simulate network latency
  await new Promise(resolve => setTimeout(resolve, 500));
  
  const lowerQuery = query.toLowerCase();
  let results = [];
  
  // Generate simulated results based on query keywords
  if (lowerQuery.includes('caries') || lowerQuery.includes('cavity') || lowerQuery.includes('decay')) {
    results = [
      {
        title: 'Dental Caries (Tooth Decay) - NIH',
        url: 'https://www.nidcr.nih.gov/health-info/tooth-decay',
        snippet: 'Tooth decay (dental caries) is damage to a tooth that can happen when decay-causing bacteria in your mouth make acids that attack the tooth\'s surface...',
        source: 'nidcr.nih.gov'
      },
      {
        title: 'Tooth Decay - American Dental Association',
        url: 'https://www.mouthhealthy.org/all-topics-a-z/tooth-decay',
        snippet: 'Tooth decay is the destruction of your tooth enamel, the hard, outer layer of your teeth. It can be a problem for children, teens and adults...',
        source: 'mouthhealthy.org'
      }
    ];
  } else if (lowerQuery.includes('gum') || lowerQuery.includes('periodont')) {
    results = [
      {
        title: 'Gum Disease - American Academy of Periodontology',
        url: 'https://www.perio.org/for-patients/periodontal-disease/',
        snippet: 'Periodontal disease is an inflammatory disease that affects the soft and hard structures that support the teeth...',
        source: 'perio.org'
      },
      {
        title: 'Periodontal (Gum) Disease - NIH',
        url: 'https://www.nidcr.nih.gov/health-info/gum-disease',
        snippet: 'Periodontal (gum) disease is an infection of the tissues that hold your teeth in place. It\'s typically caused by poor brushing and flossing habits...',
        source: 'nidcr.nih.gov'
      }
    ];
  }
  
  // Add generic dental health results if we don't have specific matches
  if (results.length < limit) {
    results.push(
      {
        title: 'Oral Health - CDC',
        url: 'https://www.cdc.gov/oralhealth/index.html',
        snippet: "Oral health is an essential part of your overall health. CDC's Division of Oral Health works to improve oral health and reduce oral health disparities...",
        source: 'cdc.gov'
      },
      {
        title: 'Mouth Healthy - American Dental Association',
        url: 'https://www.mouthhealthy.org/',
        snippet: "The American Dental Association's consumer website offers information on dental care and oral health for patients and caregivers...",
        source: 'mouthhealthy.org'
      }
    );
  }
  
  // Limit results to requested number
  return results.slice(0, limit);
};

/**
 * Fetch the latest dental research articles
 * @param {string} topic - The dental topic to search for
 * @param {number} limit - Maximum number of articles to return
 * @returns {Promise<Array>} - Array of research articles
 */
export const getLatestDentalResearch = async (topic, limit = 3) => {
  try {
    // First, check if we have cached this query in Supabase
    const { data: cachedResults, error: cacheError } = await supabase
      .from('research_cache')
      .select('*')
      .eq('topic', topic.toLowerCase().trim())
      .order('created_at', { ascending: false })
      .limit(1);
    
    // If we have recent cached results (less than 30 days old), use them
    if (!cacheError && cachedResults && cachedResults.length > 0) {
      const cachedResult = cachedResults[0];
      const cacheAge = new Date() - new Date(cachedResult.created_at);
      const cacheAgeInDays = cacheAge / (1000 * 60 * 60 * 24);
      
      if (cacheAgeInDays < 30) {
        console.log('Using cached research results');
        return cachedResult.articles;
      }
    }
    
    // In a real implementation, you would call PubMed API or similar
    // For demonstration, we'll return simulated results
    const simulatedArticles = simulateDentalResearch(topic, limit);
    
    // Cache the results in Supabase for future use
    const { error: insertError } = await supabase
      .from('research_cache')
      .insert({
        topic: topic.toLowerCase().trim(),
        articles: simulatedArticles,
        created_at: new Date().toISOString()
      });
    
    if (insertError) {
      console.error('Error caching research results:', insertError);
    }
    
    return simulatedArticles;
  } catch (error) {
    console.error('Error fetching dental research:', error);
    throw new Error('Failed to fetch dental research');
  }
};

/**
 * Simulate fetching dental research articles
 * In a real implementation, replace this with an actual API call
 * @param {string} topic - The dental topic to search for
 * @param {number} limit - Maximum number of articles to return
 * @returns {Array} - Array of simulated research articles
 */
const simulateDentalResearch = (topic, limit) => {
  const currentYear = new Date().getFullYear();
  const lowerTopic = topic.toLowerCase();
  
  // Sample research articles based on topic
  const articles = [
    {
      title: `Recent Advances in ${topic} Treatment: A Systematic Review`,
      authors: 'Smith J, Johnson A, Williams B',
      journal: 'Journal of Dental Research',
      year: currentYear - 1,
      doi: `10.1177/00220345${currentYear - 1}1234567`,
      url: 'https://pubmed.ncbi.nlm.nih.gov/'
    },
    {
      title: `Clinical Outcomes of ${topic} Interventions: Meta-Analysis`,
      authors: 'Brown R, Davis M, Miller K',
      journal: 'Journal of the American Dental Association',
      year: currentYear - 2,
      doi: `10.1016/j.adaj.${currentYear - 2}.08.023`,
      url: 'https://jada.ada.org/'
    },
    {
      title: `Evidence-Based Approaches to ${topic} Management`,
      authors: 'Wilson T, Anderson P, Thomas S',
      journal: 'International Journal of Dental Hygiene',
      year: currentYear,
      doi: `10.1111/idh.${currentYear}.12345`,
      url: 'https://onlinelibrary.wiley.com/'
    }
  ];
  
  return articles.slice(0, limit);
}; 