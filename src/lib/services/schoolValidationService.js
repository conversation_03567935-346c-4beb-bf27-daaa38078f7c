import { ACCREDITED_SCHOOLS } from '../constants/accreditedSchools';

// Normalize school names for comparison
const normalizeSchoolName = (name) => {
  return name.toLowerCase()
    .replace(/university|college|school|of|dentistry|dental|medicine/g, '')
    .replace(/[^a-z0-9]/g, '')
    .trim();
};

// Check if a school matches an accredited school
const isAccreditedMatch = (schoolName, accreditedSchool) => {
  const normalizedSchool = normalizeSchoolName(schoolName);
  const normalizedAccredited = normalizeSchoolName(accreditedSchool.name);
  
  return normalizedSchool.includes(normalizedAccredited) ||
         normalizedAccredited.includes(normalizedSchool);
};

// Validate and enrich school data
export const validateSchools = (schools) => {
  return schools.map(school => {
    const accreditedMatch = ACCREDITED_SCHOOLS.find(accredited => 
      isAccreditedMatch(school.name, accredited)
    );

    if (!accreditedMatch) {
      return { ...school, accredited: false };
    }

    return {
      ...school,
      accredited: true,
      accreditationInfo: {
        id: accreditedMatch.accreditationId,
        website: accreditedMatch.website,
        programs: accreditedMatch.programs,
        description: accreditedMatch.description
      }
    };
  });
};

// Get default accredited schools for a region
export const getDefaultSchools = (region = 'FL') => {
  return ACCREDITED_SCHOOLS
    .filter(school => school.location.endsWith(region))
    .map(school => ({
      ...school,
      accredited: true,
      accreditationInfo: {
        id: school.accreditationId,
        website: school.website,
        programs: school.programs,
        description: school.description
      }
    }));
};