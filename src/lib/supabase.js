import { createClient } from '@supabase/supabase-js';
import { getGeolocationData, formatLocation } from './api/geolocation';

// Check for Node.js environment vs browser environment
const isNode = typeof window === 'undefined';

// Load environment variables appropriately
let supabaseUrl, supabaseAnonKey;

if (isNode) {
  // Node.js environment - use process.env
  console.log('Running in Node.js environment');
  supabaseUrl = process.env.VITE_SUPABASE_URL;
  supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;
} else {
  // Browser environment - use import.meta.env
  console.log('Environment variables check:');
  supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
  supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
  
  // Log status of environment variables (without exposing actual keys)
  console.log('VITE_SUPABASE_URL:', supabaseUrl ? 'Present' : 'Missing');
  console.log('VITE_SUPABASE_ANON_KEY:', supabase<PERSON><PERSON>Key ? 'Present' : 'Missing');
}

// Create Supabase client with better error handling
let supabase;

try {
  if (!supabaseUrl || !supabaseAnonKey) {
    console.error('Missing Supabase configuration. Auth functionality will be limited.');
    
    // Create a mock client that won't crash the app but will show proper errors
    supabase = createMockSupabaseClient();
  } else {
    // Create the real client with all required options
    supabase = createClient(supabaseUrl, supabaseAnonKey, {
      auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: true
      },
      realtime: {
        timeout: 30000 // Increase timeout for better reliability
      },
      db: {
        schema: 'public'
      }
    });
    console.log('Supabase client created successfully');
  }
} catch (error) {
  console.error('Error creating Supabase client:', error);
  // Provide a mock client as fallback
  supabase = createMockSupabaseClient();
}

// Helper function to create a mock Supabase client that won't crash the app
function createMockSupabaseClient() {
  console.warn('Using mock Supabase client with limited functionality');
  return {
    from: () => ({
      select: () => ({
        eq: () => ({
          single: () => Promise.resolve({ data: null, error: new Error('Supabase client not configured') }),
          limit: () => Promise.resolve({ data: [], error: new Error('Supabase client not configured') })
        }),
        limit: () => Promise.resolve({ data: [], error: new Error('Supabase client not configured') })
      }),
      insert: () => Promise.resolve({ data: null, error: new Error('Supabase client not configured') }),
      update: () => Promise.resolve({ data: null, error: new Error('Supabase client not configured') }),
      delete: () => Promise.resolve({ data: null, error: new Error('Supabase client not configured') })
    }),
    auth: {
      onAuthStateChange: (callback) => {
        console.log('Auth state change handler registered (mock)');
        return { data: { subscription: { unsubscribe: () => {} } } };
      },
      getSession: () => Promise.resolve({ data: { session: null }, error: null }),
      getUser: () => Promise.resolve({ data: { user: null }, error: null }),
      signInWithPassword: () => Promise.resolve({ 
        data: null, 
        error: new Error('Authentication unavailable - Supabase not configured') 
      }),
      signUp: () => Promise.resolve({ 
        data: null, 
        error: new Error('Authentication unavailable - Supabase not configured') 
      }),
      signOut: () => Promise.resolve({ error: null })
    }
  };
}

export { supabase };

// Utility function to get client IP and user agent
export const getClientInfo = async () => {
  try {
    // Get IP address
    const ipResponse = await fetch('https://api.ipify.org?format=json');

    if (!ipResponse.ok) {
      throw new Error(`Failed to get IP: ${ipResponse.status}`);
    }

    const { ip } = await ipResponse.json();

    // Get detailed geolocation data using our new module
    const geoData = await getGeolocationData(ip);

    // Gather other client information
    const clientInfo = {
      ip_address: ip,
      user_agent: navigator.userAgent,
      platform: navigator.platform,
      language: navigator.language,
      screen_resolution: `${window.screen.width}x${window.screen.height}`,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      geo_data: geoData,
      location_string: formatLocation(geoData)
    };

    return clientInfo;
  } catch (error) {
    console.error('Error getting client info:', error);
    // Return partial data if we can at least get the IP
    try {
      const ipResponse = await fetch('https://api.ipify.org?format=json');
      if (ipResponse.ok) {
        const { ip } = await ipResponse.json();
        return {
          ip_address: ip,
          user_agent: navigator.userAgent,
          error: error.message
        };
      }
    } catch (e) {
      console.error('Failed to get fallback IP:', e);
    }
    return null;
  }
};

// Test connection with retry logic
const testConnection = async () => {
  let retryCount = 0;
  const maxRetries = 3;

  // Return early if we're using the mock client
  if (!supabaseUrl || !supabaseAnonKey) {
    console.log('Skipping Supabase connection test due to missing credentials');
    return;
  }

  while (retryCount < maxRetries) {
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .select('id')
        .limit(1);

      if (error) {
        console.warn(`Supabase query error (attempt ${retryCount + 1}/${maxRetries}):`, error.message);
        // Continue rather than throwing - less aggressive
        retryCount++;
        await new Promise(res => setTimeout(res, 1000 * retryCount));
        continue;
      }

      console.log('✅ Supabase connection successful');
      return;
    } catch (err) {
      retryCount++;
      console.error(`🚨 Supabase connection test failed (attempt ${retryCount}/${maxRetries}):`, err.message);
      await new Promise(res => setTimeout(res, 1000 * retryCount)); // Exponential backoff
    }
  }
  console.error('❌ Supabase connection failed after maximum retries, but app will continue loading.');
};

// Run connection test but don't block app rendering
testConnection().catch(error => {
  console.error('Unhandled error in Supabase connection test:', error);
  // Don't throw error to avoid blocking app
});

// Global error handler for Supabase
supabase.handleError = (error, context = '') => {
  // Log the error
  console.warn(`Supabase error in ${context}:`, error);

  // For authentication errors, inform the user
  if (error?.status === 401 || error?.code === 'PGRST116') {
    console.log('Authentication error - user not authenticated');
    return { message: 'Please log in to continue.' };
  }

  // For permission errors, inform the user
  if (error?.status === 403 || error?.code === 'PGRST102') {
    console.log('Permission error - user not authorized');
    return { message: 'You do not have permission to perform this action.' };
  }

  // For connectivity errors, show a generic message
  if (error?.code === 'NETWORK_ERROR' || error?.message?.includes('Failed to fetch')) {
    console.log('Network error - cannot connect to Supabase');
    return { message: 'Network error. Please check your internet connection.' };
  }

  // For database errors, don't crash the app
  return { message: 'An error occurred while connecting to the database. The app will continue to function in offline mode.' };
};

// Ensure NDA notifications table exists
export const ensureNDANotificationsTable = async () => {
  try {
    // First, check if table exists
    const { data, error } = await supabase
      .from('nda_notifications')
      .select('id')
      .limit(1);

    if (error) {
      console.warn('NDA notifications table check failed, but continuing:', error);
    } else {
      console.log('✅ nda_notifications table accessible');
    }

    return !error;
  } catch (err) {
    console.warn('Error checking NDA notifications table:', err);
    return false;
  }
};
