// Browser-compatible polyfill for Node.js util module
// This provides the most commonly used util functions

// Simple implementation of util.inherits
export function inherits(ctor, superCtor) {
  if (ctor === undefined || ctor === null) {
    throw new TypeError('The constructor to "inherits" must not be null or undefined');
  }
  if (superCtor === undefined || superCtor === null) {
    throw new TypeError('The super constructor to "inherits" must not be null or undefined');
  }
  if (superCtor.prototype === undefined) {
    throw new TypeError('The super constructor to "inherits" must have a prototype');
  }
  ctor.super_ = superCtor;
  Object.setPrototypeOf(ctor.prototype, superCtor.prototype);
}

// Simple implementation of util.inspect
export function inspect(obj, options) {
  return JSON.stringify(obj, null, 2);
}

// Simple implementation of util.format
export function format(format, ...args) {
  if (typeof format !== 'string') {
    const objects = [format, ...args];
    return objects.map(obj => inspect(obj)).join(' ');
  }

  let i = 0;
  return format.replace(/%[sdjifoO%]/g, match => {
    if (match === '%%') return '%';
    if (i >= args.length) return match;
    const arg = args[i++];
    switch (match) {
      case '%s': return String(arg);
      case '%d': return Number(arg).toString();
      case '%i': return parseInt(arg).toString();
      case '%f': return parseFloat(arg).toString();
      case '%j': return JSON.stringify(arg);
      case '%o': case '%O': return inspect(arg);
      default: return match;
    }
  });
}

// Simple implementation of util.promisify
export function promisify(original) {
  return function(...args) {
    return new Promise((resolve, reject) => {
      original.call(this, ...args, (err, ...values) => {
        if (err) {
          return reject(err);
        }
        if (values.length === 1) {
          resolve(values[0]);
        } else {
          resolve(values);
        }
      });
    });
  };
}

// Simple implementation of util.types
export const types = {
  isDate: obj => obj instanceof Date,
  isRegExp: obj => obj instanceof RegExp,
  isArray: Array.isArray,
  isBoolean: obj => typeof obj === 'boolean',
  isNull: obj => obj === null,
  isNullOrUndefined: obj => obj === null || obj === undefined,
  isNumber: obj => typeof obj === 'number',
  isString: obj => typeof obj === 'string',
  isSymbol: obj => typeof obj === 'symbol',
  isUndefined: obj => obj === undefined,
  isObject: obj => obj !== null && typeof obj === 'object',
  isFunction: obj => typeof obj === 'function',
  isPrimitive: obj => {
    return obj === null || 
           typeof obj === 'boolean' ||
           typeof obj === 'number' ||
           typeof obj === 'string' ||
           typeof obj === 'symbol' ||
           typeof obj === 'undefined';
  }
};

// Default export for compatibility with both import styles
export default {
  inherits,
  inspect,
  format,
  promisify,
  types
};
