-- Create tables for AI analysis storage

-- Dental Articles Table
CREATE TABLE IF NOT EXISTS dental_articles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    summary TEXT,
    source TEXT NOT NULL,
    source_type TEXT NOT NULL,
    source_url TEXT,
    link TEXT NOT NULL,
    pub_date TIMESTAMP WITH TIME ZONE NOT NULL,
    authors TEXT,
    tags TEXT[],
    status TEXT NOT NULL DEFAULT 'published',
    inserted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for dental articles
CREATE INDEX IF NOT EXISTS idx_dental_articles_source ON dental_articles(source);
CREATE INDEX IF NOT EXISTS idx_dental_articles_pub_date ON dental_articles(pub_date);
CREATE INDEX IF NOT EXISTS idx_dental_articles_status ON dental_articles(status);

-- Add trigger for dental_articles updated_at
CREATE OR REPLACE FUNCTION update_dental_articles_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS update_dental_articles_updated_at ON dental_articles;
CREATE TRIGGER update_dental_articles_updated_at
    BEFORE UPDATE ON dental_articles
    FOR EACH ROW
    EXECUTE FUNCTION update_dental_articles_updated_at();

-- Enable RLS for dental_articles
ALTER TABLE dental_articles ENABLE ROW LEVEL SECURITY;

-- Create policies for dental_articles
DROP POLICY IF EXISTS "Anyone can view dental articles" ON dental_articles;
CREATE POLICY "Anyone can view dental articles"
    ON dental_articles FOR SELECT
    USING (status = 'published');

DROP POLICY IF EXISTS "System can manage dental articles" ON dental_articles;
CREATE POLICY "System can manage dental articles"
    ON dental_articles FOR ALL
    USING (auth.uid() IN (SELECT id FROM auth.users WHERE is_admin = true));

-- Voice Analysis Table
CREATE TABLE voice_analyses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id),
    features JSONB NOT NULL,
    analysis JSONB NOT NULL,
    confidence_score FLOAT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Breath Analysis Table
CREATE TABLE breath_analyses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id),
    features JSONB NOT NULL,
    analysis JSONB NOT NULL,
    vsc_levels JSONB,
    confidence_score FLOAT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Oral Scan Analysis Table
CREATE TABLE oral_scans (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id),
    image_url TEXT NOT NULL,
    features JSONB NOT NULL,
    analysis JSONB NOT NULL,
    confidence_score FLOAT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Symptom Analysis Table
CREATE TABLE symptom_analyses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id),
    symptoms JSONB NOT NULL,
    analysis JSONB NOT NULL,
    confidence_score FLOAT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI Training Data Table
CREATE TABLE ai_training_data (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    model_type TEXT NOT NULL,
    data JSONB NOT NULL,
    validation_score FLOAT,
    is_validated BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI Model Versions Table
CREATE TABLE ai_model_versions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    model_type TEXT NOT NULL,
    version TEXT NOT NULL,
    performance_metrics JSONB,
    is_active BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better query performance
CREATE INDEX idx_voice_analyses_user ON voice_analyses(user_id);
CREATE INDEX idx_breath_analyses_user ON breath_analyses(user_id);
CREATE INDEX idx_oral_scans_user ON oral_scans(user_id);
CREATE INDEX idx_symptom_analyses_user ON symptom_analyses(user_id);
CREATE INDEX idx_ai_training_data_model ON ai_training_data(model_type);
CREATE INDEX idx_ai_model_versions_active ON ai_model_versions(model_type) WHERE is_active = true;

-- Add triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_voice_analyses_updated_at
    BEFORE UPDATE ON voice_analyses
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_breath_analyses_updated_at
    BEFORE UPDATE ON breath_analyses
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_oral_scans_updated_at
    BEFORE UPDATE ON oral_scans
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_symptom_analyses_updated_at
    BEFORE UPDATE ON symptom_analyses
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ai_training_data_updated_at
    BEFORE UPDATE ON ai_training_data
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ai_model_versions_updated_at
    BEFORE UPDATE ON ai_model_versions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add RLS policies
ALTER TABLE voice_analyses ENABLE ROW LEVEL SECURITY;
ALTER TABLE breath_analyses ENABLE ROW LEVEL SECURITY;
ALTER TABLE oral_scans ENABLE ROW LEVEL SECURITY;
ALTER TABLE symptom_analyses ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_training_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_model_versions ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can view their own analyses"
    ON voice_analyses FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "Users can view their own analyses"
    ON breath_analyses FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "Users can view their own analyses"
    ON oral_scans FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "Users can view their own analyses"
    ON symptom_analyses FOR SELECT
    USING (auth.uid() = user_id);

-- Only allow admins to manage AI training data and model versions
CREATE POLICY "Only admins can manage AI training data"
    ON ai_training_data FOR ALL
    USING (auth.uid() IN (SELECT id FROM auth.users WHERE is_admin = true));

CREATE POLICY "Only admins can manage AI model versions"
    ON ai_model_versions FOR ALL
    USING (auth.uid() IN (SELECT id FROM auth.users WHERE is_admin = true)); 