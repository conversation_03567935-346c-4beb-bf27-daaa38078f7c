-- User Activity Tracking Schema
-- This table stores user activity for analytics and personalization

-- Create the user_activity table
CREATE TABLE public.user_activity (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  activity_type TEXT NOT NULL, -- e.g., 'search', 'click', 'view'
  activity_action TEXT, -- specific action, e.g., 'location', 'dental_school'
  activity_details JSONB, -- flexible details storage
  timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  
  -- <PERSON><PERSON><PERSON>
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  
  -- Add an index for faster queries
  CONSTRAINT valid_activity_type CHECK (activity_type IS NOT NULL AND activity_type != '')
);

-- Index for faster queries by user_id
CREATE INDEX user_activity_user_id_idx ON public.user_activity (user_id);

-- Index for faster queries by activity_type
CREATE INDEX user_activity_type_idx ON public.user_activity (activity_type);

-- Index for faster timestamp-based queries
CREATE INDEX user_activity_timestamp_idx ON public.user_activity (timestamp DESC);

-- Enable RLS (Row Level Security)
ALTER TABLE public.user_activity ENABLE ROW LEVEL SECURITY;

-- Create policies
-- Users can insert their own activity records
CREATE POLICY insert_own_activity ON public.user_activity 
  FOR INSERT TO authenticated 
  WITH CHECK (auth.uid() = user_id);

-- Users can read their own activity records
CREATE POLICY read_own_activity ON public.user_activity 
  FOR SELECT TO authenticated 
  USING (auth.uid() = user_id);

-- Admin users can read all activity records
CREATE POLICY admin_read_all_activity ON public.user_activity
  FOR SELECT TO authenticated
  USING (auth.uid() IN (SELECT id FROM auth.users WHERE is_admin = true));

-- Function to update the "updated_at" column
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add trigger to automatically update the updated_at column
CREATE TRIGGER update_user_activity_timestamp
BEFORE UPDATE ON public.user_activity
FOR EACH ROW
EXECUTE PROCEDURE update_modified_column(); 