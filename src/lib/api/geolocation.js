/**
 * Geolocation API utilities for IP-based location lookup
 * This module provides functions to retrieve geolocation data based on IP addresses
 */

// Simple in-memory cache with expiration
const geoCache = new Map();
const CACHE_EXPIRY = 24 * 60 * 60 * 1000; // 24 hours

/**
 * Fetch geolocation data for an IP address with retries and caching
 * @param {string} ipAddress - The IP address to look up
 * @param {number} [retries=2] - Number of retry attempts
 * @returns {Promise<Object|null>} - Geolocation data or null if failed
 */
export const getGeolocationData = async (ipAddress, retries = 2) => {
  // Return from cache if available and not expired
  const cacheKey = `geo_${ipAddress}`;
  const cachedData = geoCache.get(cacheKey);
  
  if (cachedData) {
    const { data, timestamp } = cachedData;
    if (Date.now() - timestamp < CACHE_EXPIRY) {
      console.log('Returning cached geolocation data for', ipAddress);
      return data;
    } else {
      // Expired cache entry
      geoCache.delete(cacheKey);
    }
  }
  
  // Fetch fresh data with retries
  for (let attempt = 0; attempt <= retries; attempt++) {
    try {
      // Use ipapi.co service which provides good free tier
      const response = await fetch(`https://ipapi.co/${ipAddress}/json/`);
      
      if (!response.ok) {
        throw new Error(`API response: ${response.status}`);
      }
      
      const data = await response.json();
      
      // Check for error response from API
      if (data.error) {
        throw new Error(`API error: ${data.reason}`);
      }
      
      // Cache the result
      geoCache.set(cacheKey, {
        data,
        timestamp: Date.now()
      });
      
      return data;
    } catch (error) {
      console.error(`Geolocation attempt ${attempt + 1}/${retries + 1} failed:`, error);
      
      if (attempt === retries) {
        console.error('All geolocation attempts failed');
        return null;
      }
      
      // Wait before retry with exponential backoff
      await new Promise(resolve => setTimeout(resolve, 500 * Math.pow(2, attempt)));
    }
  }
  
  return null;
};

/**
 * Format geolocation data into a human-readable string
 * @param {Object} geoData - Geolocation data
 * @returns {string} - Formatted location string
 */
export const formatLocation = (geoData) => {
  if (!geoData) return 'Unknown location';
  
  const parts = [
    geoData.city,
    geoData.region,
    geoData.country_name
  ].filter(Boolean);
  
  return parts.join(', ') || 'Unknown location';
};

/**
 * Clear the geolocation cache
 * @param {string} [ipAddress] - Specific IP to clear, or all if not provided
 */
export const clearGeoCache = (ipAddress) => {
  if (ipAddress) {
    geoCache.delete(`geo_${ipAddress}`);
  } else {
    geoCache.clear();
  }
};

export default {
  getGeolocationData,
  formatLocation,
  clearGeoCache
}; 