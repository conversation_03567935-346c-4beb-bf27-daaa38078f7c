// Simple test script to verify OpenAI API configuration
import OpenAI from 'openai';
import dotenv from 'dotenv';

// Load environment variables from both possible locations
dotenv.config({ path: '.env' });
dotenv.config({ path: '.env.local' });

// Get API key from environment variables
const apiKey = process.env.OPENAI_API_KEY || process.env.VITE_OPENAI_API_KEY;

console.log('Starting OpenAI API test...');

// Check if API key is available
if (!apiKey) {
  console.error('❌ Error: OpenAI API key is missing.');
  console.error('Environment variables available:', Object.keys(process.env)
    .filter(key => key.includes('OPENAI') || key.includes('API'))
    .join(', '));
  process.exit(1);
}

console.log('✅ API key found (first 4 chars):', apiKey.substring(0, 4) + '...');

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: api<PERSON><PERSON>
});

// Test a simple API call
async function testOpenAI() {
  try {
    console.log('Making test API call...');
    const response = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [{ role: 'user', content: 'Hello, are you working?' }],
      max_tokens: 10
    });
    
    console.log('✅ API call successful!');
    console.log('Response:', response.choices[0].message.content);
    return true;
  } catch (error) {
    console.error('❌ API call failed:', error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    }
    return false;
  }
}

// Run the test
testOpenAI().then(success => {
  if (success) {
    console.log('✅ OpenAI API test completed successfully!');
  } else {
    console.error('❌ OpenAI API test failed.');
  }
});

export default { testOpenAI }; 