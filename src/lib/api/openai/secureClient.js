import { SMILO_ASSISTANT_ID } from './config';
import { handleApiError } from '../../utils/errorHandler';

// Base URL for the OpenAI proxy
const API_BASE_URL = '/api/openai';

// Security headers for API requests
const getSecurityHeaders = () => ({
  'Content-Type': 'application/json',
  'X-Requested-With': 'XMLHttpRequest',
  'Cache-Control': 'no-cache, no-store, must-revalidate'
});

// Improved safe JSON parsing helper with detailed error handling
const safeParseJSON = async (response) => {
  // Get response as text first
  let text;

  try {
    text = await response.text();
    // Remove logging of response content for security
    console.log(`Response status: ${response.status}, Content type: ${response.headers.get('content-type')}`);
  } catch (textError) {
    console.error(`Failed to read response text`);
    throw new Error(`Failed to read response: ${response.status} ${response.statusText}`);
  }

  // Check for empty response
  if (!text || text.trim() === '') {
    console.error(`Empty response from server with status ${response.status}`);
    if (!response.ok) {
      throw new Error(`Server error (${response.status}): ${response.statusText}`);
    }
    // Return empty object for valid empty responses
    return {};
  }

  // Try to parse as JSON
  try {
    return JSON.parse(text);
  } catch (parseError) {
    console.error(`JSON parsing error`);
    
    if (!response.ok) {
      throw new Error(`Server error (${response.status}): ${response.statusText}`);
    }

    // If we can't parse the response as JSON but the response is OK,
    // return a simple object with the text to avoid breaking the application
    if (response.ok) {
      return { text: text, _parseFailed: true };
    }

    throw new Error('Invalid JSON response from server');
  }
};

// Add request sanitizer to prevent sending sensitive data
const sanitizeRequest = (data) => {
  // Create a deep copy to avoid modifying the original
  const sanitized = JSON.parse(JSON.stringify(data));
  
  // Remove any potential sensitive fields
  if (sanitized.api_key) delete sanitized.api_key;
  if (sanitized.apiKey) delete sanitized.apiKey;
  if (sanitized.authentication) delete sanitized.authentication;
  if (sanitized.auth) delete sanitized.auth;
  if (sanitized.password) delete sanitized.password;
  if (sanitized.secret) delete sanitized.secret;
  
  return sanitized;
};

// Retry helper for transient errors
const withRetry = async (fn, maxRetries = 2, delay = 1000) => {
  let lastError;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      console.warn(`Attempt ${attempt + 1}/${maxRetries + 1} failed: ${error.message}`);

      // Don't retry for client errors (4xx)
      if (error.status && error.status >= 400 && error.status < 500) {
        throw error;
      }

      if (attempt < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  throw lastError;
};

// Create a thread using the proxy
export const createThread = async () => {
  return withRetry(async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/assistants/create-thread`, {
        method: 'POST',
        headers: getSecurityHeaders(),
        body: JSON.stringify({}),
        credentials: 'same-origin'
      });

      const data = await safeParseJSON(response);

      if (!response.ok) {
        throw new Error(data.error || `Server error: ${response.status} ${response.statusText}`);
      }

      return data;
    } catch (error) {
      console.error('Create thread error');
      throw new Error(handleApiError(error));
    }
  });
};

// Create a message using the proxy
export const createMessage = async (threadId, content) => {
  return withRetry(async () => {
    try {
      // Validate inputs
      if (!threadId) throw new Error('Thread ID is required');
      if (!content) throw new Error('Content is required');
      
      const response = await fetch(`${API_BASE_URL}/assistants/create-message`, {
        method: 'POST',
        headers: getSecurityHeaders(),
        body: JSON.stringify(sanitizeRequest({
          thread_id: threadId,
          content,
        })),
        credentials: 'same-origin'
      });

      const data = await safeParseJSON(response);

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create message');
      }

      return data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  });
};

// Run the assistant using the proxy
export const runAssistant = async (threadId) => {
  return withRetry(async () => {
    try {
      // Validate input
      if (!threadId) throw new Error('Thread ID is required');

      const response = await fetch(`${API_BASE_URL}/assistants/run-assistant`, {
        method: 'POST',
        headers: getSecurityHeaders(),
        body: JSON.stringify(sanitizeRequest({
          thread_id: threadId,
          assistant_id: SMILO_ASSISTANT_ID,
        })),
        credentials: 'same-origin'
      });

      const data = await safeParseJSON(response);

      if (!response.ok) {
        throw new Error(data.error || 'Failed to run assistant');
      }

      return data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  });
};

// Get run status using the proxy
export const getRunStatus = async (threadId, runId) => {
  return withRetry(async () => {
    try {
      // Validate inputs
      if (!threadId) throw new Error('Thread ID is required');
      if (!runId) throw new Error('Run ID is required');
      
      const response = await fetch(`${API_BASE_URL}/assistants/get-run-status`, {
        method: 'POST',
        headers: getSecurityHeaders(),
        body: JSON.stringify(sanitizeRequest({
          thread_id: threadId,
          run_id: runId,
        })),
        credentials: 'same-origin'
      });

      const data = await safeParseJSON(response);

      if (!response.ok) {
        throw new Error(data.error || 'Failed to get run status');
      }

      return data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  });
};

// List messages using the proxy
export const listMessages = async (threadId) => {
  return withRetry(async () => {
    try {
      // Validate input
      if (!threadId) throw new Error('Thread ID is required');
      
      const response = await fetch(`${API_BASE_URL}/assistants/list-messages`, {
        method: 'POST',
        headers: getSecurityHeaders(),
        body: JSON.stringify(sanitizeRequest({
          thread_id: threadId,
        })),
        credentials: 'same-origin'
      });

      const data = await safeParseJSON(response);

      if (!response.ok) {
        throw new Error(data.error || 'Failed to list messages');
      }

      return data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  });
};

// Simple chat completion using the proxy
export const chatCompletion = async (messages, model = 'gpt-3.5-turbo', maxTokens = 500) => {
  return withRetry(async () => {
    try {
      // Validate input
      if (!messages || !Array.isArray(messages) || messages.length === 0) {
        throw new Error('Valid messages array is required');
      }
      
      console.log('Making chat completion request:', {
        endpoint: `${API_BASE_URL}/chat`,
        model,
        maxTokens,
        messageCount: messages?.length
      });

      const response = await fetch(`${API_BASE_URL}/chat`, {
        method: 'POST',
        headers: getSecurityHeaders(),
        body: JSON.stringify(sanitizeRequest({
          messages,
          model,
          max_tokens: maxTokens,
        })),
        credentials: 'same-origin'
      });

      console.log(`Chat API response status: ${response.status}`);
      const data = await safeParseJSON(response);

      if (!response.ok) {
        console.error('Chat API error:', data);
        throw new Error(data.error || `Failed to generate chat completion: ${response.status}`);
      }

      // Check if we got a valid response structure
      if (!data.choices || !Array.isArray(data.choices) || data.choices.length === 0) {
        console.error('Invalid chat completion response structure:', data);
        throw new Error('Invalid response structure from OpenAI API');
      }

      return data;
    } catch (error) {
      console.error('Chat completion error:', error);
      // Try to provide a more helpful error message
      if (error.message.includes('Failed to fetch') || error.message.includes('NetworkError')) {
        throw new Error('Network error connecting to AI service. Please check your connection.');
      }
      throw new Error(handleApiError(error));
    }
  });
};

// Vision model completion using the proxy
export const visionCompletion = async (messages, maxTokens = 1000, model = null) => {
  return withRetry(async () => {
    try {
      // Validate input
      if (!messages || !Array.isArray(messages) || messages.length === 0) {
        throw new Error('Valid messages array is required');
      }
      
      // Use the model from constants if not specified
      const visionModel = model || 'gpt-4o';

      console.log('Calling vision API with:', {
        messageCount: messages?.length,
        endpoint: `${API_BASE_URL}/vision`,
        maxTokens,
        model: visionModel
      });

      const response = await fetch(`${API_BASE_URL}/vision`, {
        method: 'POST',
        headers: getSecurityHeaders(),
        body: JSON.stringify(sanitizeRequest({
          messages,
          max_tokens: maxTokens,
          model: visionModel
        })),
        credentials: 'same-origin'
      });

      console.log('Vision API response status:', response.status);

      const data = await safeParseJSON(response);

      if (!response.ok) {
        console.error('Vision API response not OK:', {
          status: response.status,
          statusText: response.statusText,
          error: data.error || 'Unknown error',
          code: data.code || 'unknown_error'
        });

        // Check for model not found error and try fallback
        if (data.code === 'model_not_found' ||
            (data.details && data.details.includes('not found')) ||
            response.status === 404) {
          console.log('Model not found, will try fallback in server implementation');
        }

        throw new Error(data.error || `API error: ${response.status} ${response.statusText}`);
      }

      console.log('Vision API response received successfully');
      return data;
    } catch (error) {
      console.error('Vision completion error:', error);

      // Provide more specific error messages
      if (error.message.includes('not found') || error.message.includes('404')) {
        throw new Error('The vision model is not available. The server will try to use a fallback model.');
      }

      if (error.message.includes('too large') || error.message.includes('413')) {
        throw new Error('The image is too large. Please try a smaller image or reduce its resolution.');
      }

      throw new Error(handleApiError(error));
    }
  }, 3, 1000); // Increase retries to 3 with 1 second delay
};