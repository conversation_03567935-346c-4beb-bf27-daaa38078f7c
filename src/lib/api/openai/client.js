// This file now acts as a proxy to the secure client implementation
// to maintain backward compatibility with existing code

import { 
  createThread,
  createMessage,
  runAssistant,
  getRunStatus,
  listMessages,
  chatCompletion
} from './secureClient';

// Re-export the secure client functions
export {
  createThread,
  createMessage,
  runAssistant,
  getRunStatus,
  listMessages,
  chatCompletion
};