import {
  createThread,
  createMessage,
  runAssistant,
  getRunStatus,
  listMessages,
  chatCompletion,
  visionCompletion
} from './secureClient';

// Fallback function using the direct /fallback endpoint
const useFallbackEndpoint = async (question) => {
  try {
    console.log('Using emergency fallback endpoint');

    // Add retry logic for the fallback endpoint
    let retryCount = 0;
    const maxRetries = 2;
    let lastError = null;

    while (retryCount <= maxRetries) {
      try {
        console.log(`Fallback attempt ${retryCount + 1}/${maxRetries + 1}`);

        const response = await fetch('/api/openai/fallback', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            prompt: question || "Please provide general dental health information"
          }),
        });

        console.log(`Fallback response status: ${response.status}`);

        if (!response.ok) {
          throw new Error(`Fallback API error: ${response.status} ${response.statusText}`);
        }

        // Get the response as text first to ensure it's valid
        const text = await response.text();
        console.log(`Fallback response received (${text.length} chars)`);

        if (!text) {
          throw new Error('Empty response from fallback API');
        }

        try {
          const data = JSON.parse(text);
          return data.text || "I apologize, but I'm having trouble processing your request right now.";
        } catch (parseError) {
          console.error('JSON parse error in fallback:', parseError);
          // If we can't parse the JSON but have text, return a generic response
          if (text.length > 0) {
            return "I apologize, but I'm having trouble processing your request right now.";
          }
          throw parseError; // Re-throw to trigger retry
        }
      } catch (attemptError) {
        lastError = attemptError;
        console.error(`Fallback attempt ${retryCount + 1} failed:`, attemptError);

        // Don't retry for client errors (4xx)
        if (attemptError.message.includes('4') && !attemptError.message.includes('429')) {
          break;
        }

        retryCount++;
        if (retryCount <= maxRetries) {
          // Wait before retrying with exponential backoff
          const backoffTime = Math.pow(2, retryCount) * 500;
          console.log(`Waiting ${backoffTime}ms before retry...`);
          await new Promise(resolve => setTimeout(resolve, backoffTime));
        }
      }
    }

    throw lastError || new Error('All fallback attempts failed');
  } catch (fallbackError) {
    console.error('Emergency fallback failed after all retries:', fallbackError);
    return "I'm sorry, but our AI service is currently unavailable. Please try again later.";
  }
};

// Client-side image validation - returns null if valid, error message if invalid
const validateImage = (imageUrl) => {
  if (!imageUrl) return "No image provided";

  if (typeof imageUrl !== 'string') {
    return "Invalid image format - expected a string";
  }

  if (!imageUrl.startsWith('data:image/')) {
    return "Invalid image format - expected a data URL";
  }

  // Check if image is too large
  const approximateSizeInMB = (imageUrl.length * 0.75) / 1024 / 1024; // Rough estimate for base64
  if (approximateSizeInMB > 20) {
    return `Image is too large (approximately ${approximateSizeInMB.toFixed(1)}MB). Maximum size is 20MB.`;
  }

  return null; // No error
};

export const getDentalAdvice = async (question, imageUrl = null, conversationContext = []) => {
  try {
    // If image is provided, use a simpler direct approach with vision API
    if (imageUrl) {
      console.log('Processing dental image analysis request');

      // Validate image URL format
      const imageError = validateImage(imageUrl);
      if (imageError) {
        console.error('Image validation error:', imageError);
        return `I'm sorry, but there was an issue with your image: ${imageError}. Please try a different image, ensure it's in JPG or PNG format, and keep the size under 20MB.`;
      }

      // Optimize image size if needed by reducing quality
      let optimizedImageUrl = imageUrl;
      const approximateSizeInMB = (imageUrl.length * 0.75) / 1024 / 1024;
      if (approximateSizeInMB > 10) {
        console.log(`Image is large (${approximateSizeInMB.toFixed(1)}MB), attempting optimization...`);
        try {
          // Create a temporary image element to resize
          const img = new Image();
          img.src = imageUrl;
          await new Promise(resolve => {
            img.onload = resolve;
            img.onerror = resolve; // Continue even if there's an error
          });

          // Create a canvas to resize the image
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          const MAX_WIDTH = 1200;
          const MAX_HEIGHT = 1200;

          let width = img.width;
          let height = img.height;

          // Calculate new dimensions
          if (width > height) {
            if (width > MAX_WIDTH) {
              height *= MAX_WIDTH / width;
              width = MAX_WIDTH;
            }
          } else {
            if (height > MAX_HEIGHT) {
              width *= MAX_HEIGHT / height;
              height = MAX_HEIGHT;
            }
          }

          canvas.width = width;
          canvas.height = height;

          // Draw the image
          ctx.drawImage(img, 0, 0, width, height);

          // Get data URL with reduced quality
          const quality = 0.7; // 70% quality
          optimizedImageUrl = canvas.toDataURL('image/jpeg', quality);
          console.log('Image optimization complete');
        } catch (optimizationError) {
          console.error('Failed to optimize image:', optimizationError);
          // Continue with original image
        }
      }

      const messages = [
        {
          role: "system",
          content: "You are SMILO, an advanced dental analysis assistant with expertise in clinical dentistry, oral pathology, and dental radiology. Provide a comprehensive dental assessment of the image and answer the user's question. Always include a disclaimer about not being professional medical advice."
        },
        {
          role: "user",
          content: [
            { type: "text", text: question || "Please analyze this dental image" },
            { type: "image_url", image_url: { url: optimizedImageUrl } }
          ]
        }
      ];

      console.log('Calling vision API for dental image analysis');
      try {
        const completion = await visionCompletion(messages);
        console.log('Vision API response received successfully');

        if (!completion || !completion.choices || !completion.choices[0]?.message?.content) {
          console.error('Invalid vision API response structure:', completion);
          return "I'm sorry, but I encountered an issue analyzing your dental image. This could be due to server load or an issue with the image. Please try again later or consider uploading a different image with better lighting and focus.";
        }

        return completion.choices[0].message.content;
      } catch (visionError) {
        console.error('Vision API error:', visionError);

        // Check for specific error messages
        if (visionError.message && visionError.message.includes('API key')) {
          return "I'm sorry, but our dental image analysis service is currently unavailable due to an API configuration issue. Our team has been notified and is working to restore service as quickly as possible.";
        }

        return "I'm sorry, but I encountered an issue analyzing your dental image. This could be due to server load or an issue with the image. Please try again later or consider uploading a different image with better lighting and focus.";
      }
    }

    // If we have conversation context, use direct chat completion
    if (conversationContext && conversationContext.length > 0) {
      try {
        const systemPrompt = {
          role: "system",
          content: "You are SMILO, a dental AI assistant. Provide helpful information about dental health, but always include a disclaimer that this is not professional medical advice and users should consult a dentist for proper diagnosis and treatment."
        };

        // Make sure we have the system prompt
        const messages = conversationContext.some(msg => msg.role === 'system')
          ? [...conversationContext, { role: "user", content: question }]
          : [systemPrompt, ...conversationContext, { role: "user", content: question }];

        const completion = await chatCompletion(messages);

        if (!completion || !completion.choices || !completion.choices[0]?.message?.content) {
          console.error('Invalid chat completion response structure:', completion);
          // Try the fallback
          return await useFallbackEndpoint(question);
        }

        return completion.choices[0].message.content;
      } catch (chatError) {
        console.error('Chat completion error:', chatError);
        // Try the fallback
        return await useFallbackEndpoint(question);
      }
    }

    // Use a direct chat completion as fallback if assistants API isn't working
    // This ensures users will always get some response
    try {
      console.log('Using direct chat completion as fallback');
      const messages = [
        {
          role: "system",
          content: "You are SMILO, a dental AI assistant. Provide helpful information about dental health, but always include a disclaimer that this is not professional medical advice and users should consult a dentist for proper diagnosis and treatment."
        },
        {
          role: "user",
          content: question || "Please provide general dental health advice"
        }
      ];

      const completion = await chatCompletion(messages);
      if (completion?.choices?.[0]?.message?.content) {
        return completion.choices[0].message.content;
      }

      throw new Error('Invalid response from chat completion API');
    } catch (fallbackError) {
      console.error('Fallback chat completion error:', fallbackError);
      // Try the emergency fallback endpoint
      return await useFallbackEndpoint(question);
    }
  } catch (error) {
    console.error('AI service error:', error);
    // Use the emergency fallback as a last resort
    return await useFallbackEndpoint(question);
  }
};