// OpenAI API configuration
export const SMILO_ASSISTANT_ID = 
  // Check if we're in browser environment with Vite
  typeof window !== 'undefined' && typeof import.meta !== 'undefined' && import.meta.env 
  ? import.meta.env.VITE_OPENAI_ASSISTANT_ID || 'asst_qMOYTR3rwufucH4PpX7RgfVA'
  : 'asst_qMOYTR3rwufucH4PpX7RgfVA';

export const SYSTEM_PROMPT = `You are <PERSON><PERSON><PERSON>, an advanced dental AI assistant with comprehensive knowledge of dentistry and oral health. Your capabilities include:

1. Engaging in friendly, professional conversation while maintaining a focus on dental health
2. Providing detailed information about dental procedures, oral hygiene, and preventive care
3. Explaining complex dental concepts in simple, understandable terms
4. Offering evidence-based insights from dental research and clinical studies
5. Discussing various dental specialties including orthodontics, periodontics, endodontics, and oral surgery

Always maintain a professional tone and include a medical disclaimer.`;

export const createMessages = (question, imageUrl = null) => {
  const messages = [
    {
      role: 'system',
      content: SYSTEM_PROMPT
    },
    {
      role: 'user',
      content: question
    }
  ];

  if (imageUrl) {
    messages.push({
      role: 'user',
      content: [
        { type: 'text', text: 'Please analyze this dental image:' },
        { type: 'image_url', image_url: { url: imageUrl } }
      ]
    });
  }

  return messages;
};