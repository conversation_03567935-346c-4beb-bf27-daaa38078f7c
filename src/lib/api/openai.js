// This file has been updated to use the secure server-side API proxy

import { chatCompletion, visionCompletion } from './openai/secureClient';
import { API_CONFIG, DENTAL_KNOWLEDGE } from '../constants';
import { searchDentalInformation, getLatestDentalResearch } from '../services/webSearchService';
import { getDentalKnowledge, recordUserQuery } from '../services/knowledgeBaseService';

// Re-export from the legacy location for backward compatibility
export { getDentalAdvice } from '../openai';

export const generateDentalResponse = async (userQuery, options = {}) => {
  try {
    // Create context by combining knowledge sources
    let context = "";
    
    // Get information from knowledge base
    const knowledgeData = await getDentalKnowledge(userQuery);
    if (knowledgeData && knowledgeData.length > 0) {
      context += "DENTAL KNOWLEDGE:\n" + knowledgeData.join("\n\n") + "\n\n";
    } else {
      // Fallback to built-in knowledge
      context += "DENTAL KNOWLEDGE:\n" + DENTAL_KNOWLEDGE + "\n\n";
    }
    
    // Add web search results if enabled
    if (options.includeWebSearch) {
      const webResults = await searchDentalInformation(userQuery);
      if (webResults && webResults.length > 0) {
        context += "WEB SEARCH RESULTS:\n" + webResults.join("\n\n") + "\n\n";
      }
      
      // Add latest research if enabled
      if (options.includeResearch) {
        const researchResults = await getLatestDentalResearch(userQuery);
        if (researchResults && researchResults.length > 0) {
          context += "RECENT DENTAL RESEARCH:\n" + researchResults.join("\n\n") + "\n\n";
        }
      }
    }
    
    // Record the query for analytics
    recordUserQuery(userQuery);
    
    // Build the messages array
    const messages = [
      {
        role: "system",
        content: `You are a dental assistant AI. You provide helpful, accurate information about dental health and procedures.
        
        Use the following information to help answer the user's question. If the information doesn't contain an answer to their question, provide general dental knowledge but be honest about limitations.
        
        ${context}
        
        Always include a disclaimer that you are providing general information and not professional medical advice. Recommend that the user consult with a dentist for personalized advice and treatment.`
      },
      {
        role: "user",
        content: userQuery
      }
    ];
    
    // Get model response using the secure proxy
    const completion = await chatCompletion(
      messages,
      API_CONFIG.openai.models.chat,
      API_CONFIG.openai.maxTokens.chat
    );
    
    return completion.choices[0].message.content;
  } catch (error) {
    console.error("Error generating dental response:", error);
    return "I apologize, but I'm having trouble generating a response at the moment. Please try again later.";
  }
};