// Configuration constants
export const config = {
  openai: {
    // Try to get API key from both server environment and Vite environment variables
    // Check if process is defined before accessing process.env (for browser compatibility)
    // Also check for both OPENAI_API_KEY and VITE_OPENAI_API_KEY
    apiKey: (typeof process !== 'undefined' && process.env ? 
            (process.env.OPENAI_API_KEY || process.env.VITE_OPENAI_API_KEY) : undefined) || 
            (typeof import.meta !== 'undefined' && import.meta.env ? 
            (import.meta.env.VITE_OPENAI_API_KEY || import.meta.env.OPENAI_API_KEY) : undefined) || '',
    // NOTE: When the OpenAI API key is missing, the application will run in demo mode
    // To use real OpenAI API, add your key to .env as VITE_OPENAI_API_KEY=your-key-here
  },
  google: {
    // Try to get API key from both server environment and Vite environment variables
    // Check if process is defined before accessing process.env (for browser compatibility)
    // Also check for both GOOGLE_MAPS_API_KEY and VITE_GOOGLE_MAPS_API_KEY
    apiKey: (typeof process !== 'undefined' && process.env ? 
            (process.env.GOOGLE_MAPS_API_KEY || process.env.VITE_GOOGLE_MAPS_API_KEY || process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY) : undefined) || 
            (typeof import.meta !== 'undefined' && import.meta.env ? 
            (import.meta.env.VITE_GOOGLE_MAPS_API_KEY || import.meta.env.GOOGLE_MAPS_API_KEY || import.meta.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY) : undefined) || '',
    libraries: ['places', 'geometry'],
    version: 'weekly',
    language: 'en',
  }
};

// Debugging - Check if the API keys are being accessed correctly
console.log("Google Maps API Key:", config.google.apiKey ? "Loaded (OK)" : "Missing (X)");
console.log("OpenAI API Key:", config.openai.apiKey ? "Loaded (OK)" : "Missing (X) (running in demo mode)");

// Check if the API key is missing
if (!config.google.apiKey) {
  console.error("(X) Google Maps API key is missing! Check your .env file.");
} else {
  console.log("(OK) Google Maps API key loaded successfully.");
}

// Export API keys separately for easier consumption by server modules
export const OPENAI_API_KEY = config.openai.apiKey;
export const GOOGLE_MAPS_API_KEY = config.google.apiKey;