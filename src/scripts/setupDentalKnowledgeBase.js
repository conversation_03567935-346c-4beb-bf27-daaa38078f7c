import { createClient } from '@supabase/supabase-js';
import { DENTAL_KNOWLEDGE } from '../lib/constants';

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Set up the necessary tables for the dental knowledge base
 */
const setupDentalKnowledgeBase = async () => {
  try {
    console.log('Setting up dental knowledge base...');
    
    // Create tables if they don't exist
    // Note: In a real implementation, you would use Supabase migrations
    // This is a simplified approach for demonstration purposes
    
    // 1. Create dental_knowledge table
    console.log('Creating dental_knowledge table...');
    await supabase.rpc('create_table_if_not_exists', {
      table_name: 'dental_knowledge',
      columns: `
        id uuid primary key default uuid_generate_v4(),
        topic text not null,
        content jsonb not null,
        source text not null,
        access_count integer default 0,
        created_at timestamp with time zone default now(),
        updated_at timestamp with time zone default now()
      `
    });
    
    // 2. Create search_cache table
    console.log('Creating search_cache table...');
    await supabase.rpc('create_table_if_not_exists', {
      table_name: 'search_cache',
      columns: `
        id uuid primary key default uuid_generate_v4(),
        query text not null,
        results jsonb not null,
        created_at timestamp with time zone default now()
      `
    });
    
    // 3. Create research_cache table
    console.log('Creating research_cache table...');
    await supabase.rpc('create_table_if_not_exists', {
      table_name: 'research_cache',
      columns: `
        id uuid primary key default uuid_generate_v4(),
        topic text not null,
        articles jsonb not null,
        created_at timestamp with time zone default now()
      `
    });
    
    // 4. Create user_queries table
    console.log('Creating user_queries table...');
    await supabase.rpc('create_table_if_not_exists', {
      table_name: 'user_queries',
      columns: `
        id uuid primary key default uuid_generate_v4(),
        query text not null,
        user_id uuid,
        created_at timestamp with time zone default now()
      `
    });
    
    // Seed the dental_knowledge table with initial data
    console.log('Seeding dental_knowledge table...');
    await seedDentalKnowledge();
    
    console.log('Dental knowledge base setup complete!');
  } catch (error) {
    console.error('Error setting up dental knowledge base:', error);
  }
};

/**
 * Seed the dental_knowledge table with initial data
 */
const seedDentalKnowledge = async () => {
  try {
    // Convert DENTAL_KNOWLEDGE object to array of records
    const knowledgeRecords = [];
    
    // Add caries knowledge
    knowledgeRecords.push({
      topic: 'caries',
      content: DENTAL_KNOWLEDGE.commonConditions.caries,
      source: 'local'
    });
    
    // Add periodontal knowledge
    knowledgeRecords.push({
      topic: 'periodontal',
      content: DENTAL_KNOWLEDGE.commonConditions.periodontal,
      source: 'local'
    });
    
    // Add endodontic knowledge
    knowledgeRecords.push({
      topic: 'endodontic',
      content: DENTAL_KNOWLEDGE.commonConditions.endodontic,
      source: 'local'
    });
    
    // Add occlusal knowledge
    knowledgeRecords.push({
      topic: 'occlusal',
      content: DENTAL_KNOWLEDGE.commonConditions.occlusal,
      source: 'local'
    });
    
    // Add restorations knowledge
    knowledgeRecords.push({
      topic: 'restorations',
      content: DENTAL_KNOWLEDGE.restorations,
      source: 'local'
    });
    
    // Add radiographic findings knowledge
    knowledgeRecords.push({
      topic: 'radiographic',
      content: DENTAL_KNOWLEDGE.radiographicFindings,
      source: 'local'
    });
    
    // Add preventive measures knowledge
    knowledgeRecords.push({
      topic: 'preventive',
      content: DENTAL_KNOWLEDGE.preventiveMeasures,
      source: 'local'
    });
    
    // Add emergency conditions knowledge
    knowledgeRecords.push({
      topic: 'emergency',
      content: { emergencyConditions: DENTAL_KNOWLEDGE.emergencyConditions },
      source: 'local'
    });
    
    // Add tooth numbering systems knowledge
    knowledgeRecords.push({
      topic: 'tooth numbering',
      content: DENTAL_KNOWLEDGE.toothNumberingSystems,
      source: 'local'
    });
    
    // Insert records into dental_knowledge table
    for (const record of knowledgeRecords) {
      // Check if record already exists
      const { data: existingData } = await supabase
        .from('dental_knowledge')
        .select('id')
        .eq('topic', record.topic)
        .limit(1);
      
      if (existingData && existingData.length > 0) {
        console.log(`Knowledge for topic '${record.topic}' already exists, updating...`);
        
        // Update existing record
        await supabase
          .from('dental_knowledge')
          .update({
            content: record.content,
            source: record.source,
            updated_at: new Date().toISOString()
          })
          .eq('id', existingData[0].id);
      } else {
        console.log(`Adding knowledge for topic '${record.topic}'...`);
        
        // Insert new record
        await supabase
          .from('dental_knowledge')
          .insert({
            topic: record.topic,
            content: record.content,
            source: record.source,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });
      }
    }
    
    console.log(`Seeded ${knowledgeRecords.length} dental knowledge records`);
  } catch (error) {
    console.error('Error seeding dental knowledge:', error);
  }
};

// Run the setup function
setupDentalKnowledgeBase(); 