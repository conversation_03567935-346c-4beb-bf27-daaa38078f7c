#!/usr/bin/env node

import { fetchAndStoreArticles, cleanupOldArticles } from '../lib/services/articleFetchService.js';
import { supabase } from '../lib/supabase.js';

// Maximum execution time (in milliseconds) - 15 minutes
const MAX_EXECUTION_TIME = 15 * 60 * 1000;

console.log('========================================');
console.log(`Starting scheduled article fetch at ${new Date().toISOString()}`);
console.log('========================================');

// Set a timeout to terminate the script if it runs too long
const timeoutId = setTimeout(() => {
  console.error('Script execution exceeded maximum time limit. Terminating...');
  process.exit(1);
}, MAX_EXECUTION_TIME);

// Clear timeout when done to avoid hanging the process
timeoutId.unref();

const runArticleFetch = async () => {
  try {
    // Check Supabase connection first
    const { error: connectionError } = await supabase.from('dental_articles').select('count').limit(1);
    if (connectionError) {
      throw new Error(`Supabase connection failed: ${connectionError.message}`);
    }
    
    console.log('✅ Supabase connection confirmed');
    console.log('📚 Fetching articles from reputable dental sources...');
    
    // Fetch and store new articles
    const metrics = await fetchAndStoreArticles();
    console.log('📊 Article fetch metrics:');
    console.log(`- Sources checked: ${metrics.sources}`);
    console.log(`- Articles fetched: ${metrics.fetched}`);
    console.log(`- Articles stored: ${metrics.stored}`);
    console.log(`- Articles skipped: ${metrics.skipped}`);
    console.log(`- Failed articles: ${metrics.failed}`);
    console.log(`- Duration: ${metrics.durationMs}ms`);
    
    // Clean up old articles
    console.log('🧹 Cleaning up old articles...');
    const cleanupMetrics = await cleanupOldArticles();
    console.log(`✅ Cleanup completed: ${cleanupMetrics.deleted} old articles removed, ${cleanupMetrics.remaining} articles kept`);
    
    console.log('========================================');
    console.log(`Article fetch completed successfully at ${new Date().toISOString()}`);
    console.log('========================================');
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error in scheduled article fetch:');
    console.error(error);
    
    // Log error to Supabase if connection is working
    try {
      await supabase.from('system_logs').insert({
        component: 'article_fetcher_script',
        level: 'error',
        message: `Script execution error: ${error.message}`,
        metadata: { 
          error: error.message,
          stack: error.stack
        }
      });
    } catch (logError) {
      console.error('Failed to log error to Supabase:', logError.message);
    }
    
    process.exit(1);
  }
};

// Run the script
runArticleFetch(); 