#!/usr/bin/env node

import { config } from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';

// Get the path to the script and project root
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = dirname(dirname(__dirname));

// Load environment variables from .env file
config({ path: join(rootDir, '.env') });

// Manually load environment variables if dotenv doesn't work
try {
  if (!process.env.VITE_SUPABASE_URL) {
    const envContent = fs.readFileSync(join(rootDir, '.env'), 'utf8');
    const envLines = envContent.split('\n');
    
    envLines.forEach(line => {
      const match = line.match(/^(VITE_[A-Z_]+)=(.+)$/);
      if (match) {
        const [, key, value] = match;
        process.env[key] = value.trim();
      }
    });
  }
} catch (err) {
  console.warn('Error reading .env file manually:', err.message);
}

// Check loaded env variables
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('Environment Variables:');
console.log('VITE_SUPABASE_URL:', supabaseUrl ? 'Present' : 'Missing');
console.log('VITE_SUPABASE_ANON_KEY:', supabaseKey ? 'Present' : 'Missing');

// Only import TRUSTED_SOURCES first to avoid any module loading issues
import { TRUSTED_SOURCES, fetchRssFeed } from '../lib/services/articleFetchService.js';

// Test one RSS feed to check functionality
async function testFetch() {
  console.log('🔍 Testing article fetch functionality...');
  
  // Get the first source from the list
  const sourceName = Object.keys(TRUSTED_SOURCES)[0];
  const source = TRUSTED_SOURCES[sourceName];
  
  console.log(`Testing fetch from: ${source.name}`);
  
  try {
    const articles = await fetchRssFeed(source);
    
    if (articles.length > 0) {
      console.log('✅ Successfully fetched articles:');
      console.log(`Found ${articles.length} articles`);
      
      // Display the first article as a sample
      console.log('\nSample article:');
      console.log('Title:', articles[0].title);
      console.log('Summary:', articles[0].summary.slice(0, 150) + '...');
      console.log('Source:', articles[0].source);
      console.log('Link:', articles[0].link);
      
      console.log('\n✅ RSS fetching is working properly!');
    } else {
      console.log('⚠️ No articles found. The feed might be empty or the parsing failed.');
    }
  } catch (error) {
    console.error('❌ Error testing article fetch:', error);
  }
}

// Run the test
testFetch().catch(error => {
  console.error('Unhandled error in test:', error);
  process.exit(1);
}); 