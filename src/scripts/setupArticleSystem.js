#!/usr/bin/env node

import { config } from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = dirname(dirname(__dirname));
config({ path: join(rootDir, '.env') });

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_SERVICE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase credentials in environment variables');
  console.error('Please ensure VITE_SUPABASE_URL and VITE_SUPABASE_SERVICE_KEY are set');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

const ensureDentalArticlesTable = async () => {
  console.log('Checking dental_articles table...');
  
  // Check if table exists by trying to select from it
  const { error } = await supabase
    .from('dental_articles')
    .select('id')
    .limit(1);
  
  if (!error) {
    console.log('✅ dental_articles table already exists');
    return;
  }
  
  if (error.code === '42P01') {
    console.log('⚠️ dental_articles table does not exist. Creating it...');
    
    // Create the table using SQL
    const { error: createError } = await supabase.rpc('exec_sql', {
      sql_query: `
        CREATE TABLE dental_articles (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          title TEXT NOT NULL,
          content TEXT NOT NULL,
          summary TEXT,
          source TEXT NOT NULL,
          source_type TEXT,
          source_url TEXT,
          link TEXT,
          pub_date TIMESTAMP WITH TIME ZONE,
          inserted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          authors TEXT,
          tags TEXT[],
          image_url TEXT,
          is_featured BOOLEAN DEFAULT FALSE,
          is_published BOOLEAN DEFAULT TRUE,
          view_count INTEGER DEFAULT 0,
          quality_score FLOAT DEFAULT 0.0
        );
        
        CREATE INDEX dental_articles_source_idx ON dental_articles(source);
        CREATE INDEX dental_articles_inserted_at_idx ON dental_articles(inserted_at);
        CREATE INDEX dental_articles_tags_idx ON dental_articles USING GIN(tags);
      `
    });
    
    if (createError) {
      console.error('❌ Failed to create dental_articles table:', createError);
      return;
    }
    
    console.log('✅ dental_articles table created successfully');
  } else {
    console.error('❌ Error checking dental_articles table:', error);
  }
};

const ensureArticleMetricsTable = async () => {
  console.log('Checking article_metrics table...');
  
  // Check if table exists
  const { error } = await supabase
    .from('article_metrics')
    .select('id')
    .limit(1);
  
  if (!error) {
    console.log('✅ article_metrics table already exists');
    return;
  }
  
  if (error.code === '42P01') {
    console.log('⚠️ article_metrics table does not exist. Creating it...');
    
    // Create the table using SQL
    const { error: createError } = await supabase.rpc('exec_sql', {
      sql_query: `
        CREATE TABLE article_metrics (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          sources INTEGER DEFAULT 0,
          fetched INTEGER DEFAULT 0,
          stored INTEGER DEFAULT 0,
          skipped INTEGER DEFAULT 0,
          failed INTEGER DEFAULT 0,
          successful_requests INTEGER DEFAULT 0,
          total_requests INTEGER DEFAULT 0,
          error_count INTEGER DEFAULT 0,
          duration_ms INTEGER DEFAULT 0,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        CREATE INDEX article_metrics_created_at_idx ON article_metrics(created_at);
      `
    });
    
    if (createError) {
      console.error('❌ Failed to create article_metrics table:', createError);
      return;
    }
    
    console.log('✅ article_metrics table created successfully');
  } else {
    console.error('❌ Error checking article_metrics table:', error);
  }
};

const ensureSystemHealthTable = async () => {
  console.log('Checking system_health table...');
  
  // Check if table exists
  const { error } = await supabase
    .from('system_health')
    .select('id')
    .limit(1);
  
  if (!error) {
    console.log('✅ system_health table already exists');
    return;
  }
  
  if (error.code === '42P01') {
    console.log('⚠️ system_health table does not exist. Creating it...');
    
    // Create the table using SQL
    const { error: createError } = await supabase.rpc('exec_sql', {
      sql_query: `
        CREATE TABLE system_health (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          component TEXT NOT NULL,
          health_score FLOAT,
          status TEXT,
          message TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        CREATE INDEX system_health_component_idx ON system_health(component);
        CREATE INDEX system_health_created_at_idx ON system_health(created_at);
      `
    });
    
    if (createError) {
      console.error('❌ Failed to create system_health table:', createError);
      return;
    }
    
    console.log('✅ system_health table created successfully');
  } else {
    console.error('❌ Error checking system_health table:', error);
  }
};

// Main setup function
const setup = async () => {
  console.log('===================================');
  console.log('🚀 Setting up dental article system');
  console.log('===================================');
  
  try {
    // Check Supabase connection
    const { error: connectionError } = await supabase.from('_dummy_query_').select('*').limit(1);
    if (connectionError && connectionError.code !== '42P01') {
      throw new Error(`Supabase connection error: ${connectionError.message}`);
    }
    
    console.log('✅ Connected to Supabase');
    
    // Ensure tables exist
    await ensureDentalArticlesTable();
    await ensureArticleMetricsTable();
    await ensureSystemHealthTable();
    
    console.log('===================================');
    console.log('✅ Article system setup complete!');
    console.log('===================================');
    
    // Add sample article if table is empty
    const { count, error: countError } = await supabase
      .from('dental_articles')
      .select('*', { count: 'exact', head: true });
    
    if (countError) {
      console.error('❌ Error checking article count:', countError);
    } else if (count === 0) {
      console.log('ℹ️ No articles found. Adding a sample article...');
      
      const { error: insertError } = await supabase
        .from('dental_articles')
        .insert([{
          title: 'Welcome to Smilo Dental Resources',
          content: 'This is a sample article to help you get started with the dental resources system. Real articles will be fetched automatically from trusted dental sources.',
          summary: 'Welcome to the Smilo Dental Resources system',
          source: 'Smilo Dental',
          source_type: 'internal',
          is_featured: true,
          tags: ['welcome', 'introduction']
        }]);
      
      if (insertError) {
        console.error('❌ Error adding sample article:', insertError);
      } else {
        console.log('✅ Sample article added successfully');
      }
    }
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Setup failed:', error);
    process.exit(1);
  }
};

// Run setup
setup(); 