import { useState, useCallback } from 'react';
import { analyzeSymptoms, getSymptomHistory } from '../lib/services/symptomCheckerService';
import { getDentalAdvice } from '../lib/openai';
import { handleApiError } from '../lib/utils/errorHandler';
import { validate<PERSON><PERSON><PERSON>ey } from '../lib/utils/validation';
import { config } from '../lib/config';

export const useSymptomChecker = (user) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [result, setResult] = useState(null);
  const [history, setHistory] = useState([]);
  const [currentImage, setCurrentImage] = useState(null);
  const [analysisHistory, setAnalysisHistory] = useState([]);

  const checkSymptoms = async (symptoms, imageData = null) => {
    try {
      setLoading(true);
      setError(null);
      setResult(null); // Clear previous results
      
      console.log('Checking symptoms:', { 
        hasSymptoms: !!symptoms, 
        hasImage: !!imageData 
      });
      
      // Get AI analysis with user ID for personalization and tracking
      const analysis = await getDentalAdvice(symptoms, imageData, user?.id);
      
      if (!analysis) {
        throw new Error('No response received from the AI service');
      }
      
      console.log('Received analysis:', analysis.substring(0, 100) + '...');
      
      // Update history and current result
      const newAnalysis = {
        symptoms: symptoms,
        hasImage: !!imageData,
        imageData: imageData,
        response: analysis,
        timestamp: new Date().toISOString()
      };
      
      setAnalysisHistory(prev => [newAnalysis, ...prev].slice(0, 6));
      setResult(analysis);
      
      if (imageData) {
        setCurrentImage(imageData);
      }
      
      return analysis;
    } catch (err) {
      const errorMessage = handleApiError(err);
      console.error('Symptom checker error:', errorMessage);
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  };

  const clearResults = useCallback(() => {
    setResult(null);
    setError(null);
  }, []);

  const loadHistory = useCallback(async () => {
    if (!user?.id) return;
    
    try {
      const data = await getSymptomHistory(user.id);
      setHistory(data);
    } catch (err) {
      console.error('Error loading symptom history:', err);
    }
  }, [user]);

  return {
    checkSymptoms,
    loading,
    error,
    result,
    history,
    loadHistory,
    clearResults,
    currentImage,
    analysisHistory,
    setError,
    clearError: () => setError(null)
  };
};