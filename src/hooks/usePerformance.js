import { useState, useEffect } from 'react';

/**
 * Hook to detect performance capabilities of the device and network
 * Returns various indicators that can be used to optimize the experience
 */
export function usePerformance() {
  // Network status
  const [networkStatus, setNetworkStatus] = useState({
    isOnline: typeof navigator !== 'undefined' ? navigator.onLine : true,
    saveData: typeof navigator !== 'undefined' && 'connection' in navigator ? 
              navigator.connection?.saveData : false,
    effectiveType: typeof navigator !== 'undefined' && 'connection' in navigator ? 
                  navigator.connection?.effectiveType : '4g',
    downlink: typeof navigator !== 'undefined' && 'connection' in navigator ?
              navigator.connection?.downlink : 10,
    rtt: typeof navigator !== 'undefined' && 'connection' in navigator ?
          navigator.connection?.rtt : 50,
  });
  
  // Battery status
  const [batteryStatus, setBatteryStatus] = useState({
    isLow: false,
    level: 1.0,
    charging: true,
  });
  
  // Device performance
  const [devicePerformance, setDevicePerformance] = useState({
    isLowEnd: false,
    hasReducedMotion: typeof window !== 'undefined' ? 
                      window.matchMedia('(prefers-reduced-motion: reduce)').matches : false,
    score: 0, // Performance score from 0-100, higher is better
  });
  
  // Calculate overall performance indicator (0-100)
  const [performanceScore, setPerformanceScore] = useState(100);
  
  // Check if we should reduce animations and visual effects
  const [reduceAnimations, setReduceAnimations] = useState(false);
  
  // Check if user enabled data saver mode
  useEffect(() => {
    if (typeof navigator !== 'undefined' && 'connection' in navigator) {
      const connection = navigator.connection;
      
      const updateNetworkInfo = () => {
        setNetworkStatus({
          isOnline: navigator.onLine,
          saveData: connection.saveData,
          effectiveType: connection.effectiveType,
          downlink: connection.downlink,
          rtt: connection.rtt,
        });
      };
      
      updateNetworkInfo();
      connection.addEventListener('change', updateNetworkInfo);
      
      return () => {
        connection.removeEventListener('change', updateNetworkInfo);
      };
    }
    
    // Handle online/offline events
    const handleOnline = () => setNetworkStatus(prev => ({...prev, isOnline: true}));
    const handleOffline = () => setNetworkStatus(prev => ({...prev, isOnline: false}));
    
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);
  
  // Check battery status if available
  useEffect(() => {
    if (typeof navigator !== 'undefined' && 'getBattery' in navigator) {
      navigator.getBattery().then(battery => {
        const updateBatteryInfo = () => {
          setBatteryStatus({
            isLow: battery.level < 0.2 && !battery.charging,
            level: battery.level,
            charging: battery.charging,
          });
        };
        
        updateBatteryInfo();
        
        battery.addEventListener('levelchange', updateBatteryInfo);
        battery.addEventListener('chargingchange', updateBatteryInfo);
        
        return () => {
          battery.removeEventListener('levelchange', updateBatteryInfo);
          battery.removeEventListener('chargingchange', updateBatteryInfo);
        };
      }).catch(err => {
        console.log('Battery API not available:', err);
      });
    }
  }, []);
  
  // Check device performance
  useEffect(() => {
    // Simple heuristic to estimate device performance
    const estimatePerformance = () => {
      // Check device memory (if available)
      const memory = navigator.deviceMemory || 4; // Default to 4GB if not available
      
      // Check processor cores (if available)
      const cores = navigator.hardwareConcurrency || 4; // Default to 4 cores if not available
      
      // Check frame rate
      let frameRate = 60;
      if ('requestVideoFrameCallback' in HTMLVideoElement.prototype) {
        // This API is only available on high-end devices
        frameRate = 60;
      } else if (window.screen.width * window.screen.height > 2000000) {
        // If high resolution screen but no advanced APIs, estimate lower frame rate
        frameRate = 30;
      }
      
      // Check if reduced motion is preferred
      const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
      
      // Calculate a simple performance score (0-100)
      const memoryScore = Math.min(100, (memory / 8) * 100);
      const coreScore = Math.min(100, (cores / 8) * 100);
      const frameScore = Math.min(100, (frameRate / 60) * 100);
      const motionScore = prefersReducedMotion ? 50 : 100;
      
      const combinedScore = Math.round((memoryScore + coreScore + frameScore + motionScore) / 4);
      
      setDevicePerformance({
        isLowEnd: combinedScore < 60,
        hasReducedMotion: prefersReducedMotion,
        score: combinedScore,
      });
      
      return combinedScore;
    };
    
    const performanceScore = estimatePerformance();
    setPerformanceScore(performanceScore);
    
    // Add listener for reduced motion preference changes
    const reducedMotionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    const handleReducedMotionChange = () => {
      setDevicePerformance(prev => ({
        ...prev,
        hasReducedMotion: reducedMotionQuery.matches,
      }));
      estimatePerformance();
    };
    
    reducedMotionQuery.addEventListener('change', handleReducedMotionChange);
    return () => {
      reducedMotionQuery.removeEventListener('change', handleReducedMotionChange);
    };
  }, []);
  
  // Decide whether to reduce animations based on all factors
  useEffect(() => {
    // Reduce animations if any of these conditions are true
    const shouldReduceAnimations = 
      devicePerformance.isLowEnd || 
      devicePerformance.hasReducedMotion || 
      batteryStatus.isLow ||
      networkStatus.saveData || 
      networkStatus.effectiveType === 'slow-2g' || 
      networkStatus.effectiveType === '2g' || 
      performanceScore < 50;
    
    setReduceAnimations(shouldReduceAnimations);
    
    // Apply CSS class to the document for easy styling
    if (typeof document !== 'undefined') {
      if (shouldReduceAnimations) {
        document.documentElement.classList.add('reduce-animations');
      } else {
        document.documentElement.classList.remove('reduce-animations');
      }
    }
  }, [devicePerformance, batteryStatus, networkStatus, performanceScore]);
  
  return {
    networkStatus,
    batteryStatus,
    devicePerformance,
    performanceScore,
    reduceAnimations,
    // Helper classifications
    isSlowNetwork: networkStatus.effectiveType === 'slow-2g' || 
                  networkStatus.effectiveType === '2g' || 
                  networkStatus.downlink < 1.5,
    isLowPowerMode: batteryStatus.isLow,
    isLowEndDevice: devicePerformance.isLowEnd,
  };
}

export default usePerformance; 