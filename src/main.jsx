// Safari compatibility polyfill - add at the top of the file
(function() {
  // Check for Safari
  const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
  
  if (isSafari) {
    // Ensure global window objects exist 
    window.globalThis = window;
    
    // Polyfill for structuredClone if needed (Safari < 15.4)
    if (typeof structuredClone !== 'function') {
      window.structuredClone = function(obj) {
        return JSON.parse(JSON.stringify(obj));
      };
    }
    
    // Force recalculation of styles to prevent blank screen
    setTimeout(() => {
      document.body && document.body.getBoundingClientRect();
    }, 100);

    // Apply class immediately to prevent FOUC (Flash of Unstyled Content)
    document.documentElement.classList.add('safari');
    
    // Check for simplified mode in URL
    if (window.location.search.includes('simplified=true')) {
      document.documentElement.classList.add('simplified-mode');
    }
  }
})();

import React from 'react';
import ReactDOM from 'react-dom/client';
import { <PERSON>rowserRouter } from 'react-router-dom';
import App from './App';
import './index.css';
import './styles/modal.css';
import './styles/reduced-animations.css';
// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/autoplay';
import 'swiper/css/effect-fade';
import 'swiper/css/a11y';
import './styles/swiper-overrides.css';
import { initDatabase } from './lib/database/initDatabase';
import { applySecurityHeaders } from './middleware/security';
// Import our styles for recipe carousel
import './styles/recipeCarousel.css';
// Import Safari-specific fixes
import './styles/safari-fixes.css';
// Import responsive utilities
import { initResponsivenessFixes } from './utils/ensureResponsiveness';
import { initMetaTags } from './utils/metaTagUtils';
import { applyDeviceClasses } from './utils/deviceDetection';
import { applyPerformanceOptimizations } from './utils/performanceOptimizer';
import { initViewportHeightFix } from './utils/viewportFix';
import { applySafariOptimizations } from './utils/safariOptimizer';
import { applySimplifiedMode } from './utils/simplifiedMode';
// Import new runtime optimizations
import { initializeRuntimeOptimizations } from './utils/runtimeOptimizations';

// Enhanced performance initialization
const initializePerformanceOptimizations = async () => {
  if (typeof window === 'undefined') return;

  console.log('🚀 Starting comprehensive performance initialization...');

  // Initialize runtime optimizations first for immediate benefits
  initializeRuntimeOptimizations();

  // Check if simplified mode is needed
  const isSimplified = applySimplifiedMode();
  
  if (isSimplified) {
    console.log('Running in simplified mode for better performance');
  } else {
    // Only apply Safari optimizations in full mode
    // (simplified mode already includes these optimizations)
    applySafariOptimizations();
  }

  // Setup viewport and other meta tags
  initMetaTags();
  
  // Initialize viewport height fix
  initViewportHeightFix();
  
  // Apply device-specific classes
  applyDeviceClasses();
  
  // Apply performance optimizations
  applyPerformanceOptimizations();
  
  // Initialize responsive behavior
  initResponsivenessFixes();

  console.log('✅ Performance optimizations completed');
  return isSimplified;
};

// Initialize simplified mode for browsers that need it
if (typeof window !== 'undefined') {
  // Start performance optimizations immediately
  initializePerformanceOptimizations().then(isSimplified => {
    // Store simplified mode state for use in rendering
    window._smiloSimplifiedMode = isSimplified;
  }).catch(err => {
    console.error('Performance optimization error:', err);
    // Continue with default mode if optimizations fail
    window._smiloSimplifiedMode = false;
  });
}

// Use a safe way to create root that works in both browser and SSR context
const rootElement = typeof document !== 'undefined' ? document.getElementById('root') : null;

if (rootElement) {
  try {
    // For Safari, add a tiny delay to ensure proper initialization
    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
    
    if (isSafari) {
      console.log('Safari detected, using enhanced compatibility mode');
      // Add Safari flag to body immediately
      document.body.classList.add('safari-optimized');
      
      // Apply more aggressive optimizations for Safari
      document.body.classList.add('reduced-animations');
      
      // Check if we're using simplified mode
      const isSimplified = document.documentElement.classList.contains('simplified-mode') || window._smiloSimplifiedMode;
      
      // Specially fixed timeout for Safari to properly initialize
      setTimeout(() => {
        ReactDOM.createRoot(rootElement).render(
          <React.StrictMode>
            <App isSimplified={isSimplified} />
          </React.StrictMode>
        );
        
        // Force a repaint after render to fix common Safari rendering issues
        setTimeout(() => {
          document.body.style.opacity = '0.99';
          setTimeout(() => {
            document.body.style.opacity = '1';
          }, 0);
        }, 300);
      }, 100);
    } else {
      // For non-Safari browsers, check for simplified mode and render immediately
      const isSimplified = window._smiloSimplifiedMode || false;
      
      ReactDOM.createRoot(rootElement).render(
        <React.StrictMode>
          <App isSimplified={isSimplified} />
        </React.StrictMode>
      );
    }
  } catch (error) {
    console.error('Failed to render App:', error);
    rootElement.innerHTML = `
      <div style="text-align: center; padding: 2rem; color: white;">
        <h1>Loading Smilo Dental</h1>
        <p>Please wait while the application initializes...</p>
        <button onclick="window.location.reload()">Reload if stuck</button>
      </div>
    `;
  }
}