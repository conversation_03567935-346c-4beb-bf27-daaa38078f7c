// Minimal server.js - temporary workaround for path-to-regexp errors
import express from 'express';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import { config, OPENAI_API_KEY, GOOGLE_MAPS_API_KEY } from './src/lib/config.js';

// Load environment variables
dotenv.config();

// Create Express app
const app = express();
const PORT = process.env.PORT || 3000;

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Add middleware to handle path-to-regexp errors
app.use((req, res, next) => {
  try {
    // Only validate internal routes, skip external URLs
    if (!req.url.startsWith('http')) {
      // Clean and normalize the path
      const cleanPath = req.path.replace(/\/+/g, '/').replace(/\/$/, '');
      const pathSegments = cleanPath.split('/').filter(Boolean);
      
      // Check for malformed parameters
      const hasInvalidParams = pathSegments.some(segment => {
        // Allow valid parameter patterns only
        if (segment.startsWith(':')) {
          return !segment.match(/^:[a-zA-Z][a-zA-Z0-9_]*$/);
        }
        // Check for partial colons that might confuse path-to-regexp
        return segment.includes(':') && !segment.startsWith(':');
      });
      
      if (hasInvalidParams) {
        return res.status(400).json({
          error: 'Invalid route parameter',
          message: 'The request contains an invalid route parameter'
        });
      }
    }
    next();
  } catch (error) {
    if (error.message && error.message.includes('Missing parameter name')) {
      return res.status(400).json({
        error: 'Invalid route parameter',
        message: 'The request contains an invalid route parameter'
      });
    }
    next(error);
  }
});

// Log API key status on server startup
console.log('==== Main Server API Keys ====');
console.log('OpenAI API Key:', OPENAI_API_KEY ? 'Loaded (OK)' : 'Missing (X)');
console.log('Google Maps API Key:', GOOGLE_MAPS_API_KEY ? 'Loaded (OK)' : 'Missing (X)');

// Basic middleware
app.use(express.json());
app.use(express.static(path.join(__dirname, 'dist')));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ 
    status: 'OK', 
    message: 'Minimal server is running',
    mode: 'minimal'
  });
});

// Root endpoint with maintenance page
app.get('/', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Smilo Dental - Maintenance Mode</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            background-color: #0f172a;
            color: white;
            text-align: center;
            padding: 50px 20px;
            margin: 0;
          }
          .container {
            max-width: 600px;
            margin: 0 auto;
          }
          h1 {
            font-size: 2rem;
            margin-bottom: 1rem;
          }
          p {
            font-size: 1.1rem;
            line-height: 1.5;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>Smilo Dental</h1>
          <p>We're currently performing maintenance on our servers.</p>
          <p>Our team is working to restore full functionality soon.</p>
          <p>API Status: ${OPENAI_API_KEY ? 'API Keys Loaded' : 'Running in Demo Mode'}</p>
        </div>
      </body>
    </html>
  `);
});

// Simple API endpoint for testing
app.get('/api/status', (req, res) => {
  res.json({
    status: 'online',
    mode: 'minimal',
    apiKeysLoaded: {
      openai: !!OPENAI_API_KEY,
      googleMaps: !!GOOGLE_MAPS_API_KEY
    }
  });
});

// Catch-all route for SPA
app.get('*', (req, res) => {
  // Serve the maintenance page for all other routes
  res.redirect('/');
});

// Global error handler
app.use((err, req, res, next) => {
  console.error('Server error:', {
    message: err.message,
    stack: process.env.NODE_ENV === 'development' ? err.stack : undefined,
    url: req.url,
    path: req.path
  });

  // Check for path-to-regexp errors
  if (err.message && (
    err.message.includes('Missing parameter name') ||
    err.message.includes('pathToRegexpError') ||
    err.message.includes('path-to-regexp')
  )) {
    return res.status(400).json({
      error: 'Invalid route parameter',
      message: 'The request contains an invalid route parameter',
      details: process.env.NODE_ENV === 'development' ? err.message : undefined
    });
  }

  res.status(500).json({
    error: 'Server error',
    message: 'An unexpected error occurred',
    details: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

// Start the server
app.listen(PORT, () => {
  console.log(`Minimal server running on port ${PORT}`);
  console.log('Health check endpoint available at /health');
}); 