-- Create table for user page visits
CREATE TABLE IF NOT EXISTS user_page_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  path TEXT NOT NULL,
  title TEXT NOT NULL,
  timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create index for faster queries
CREATE INDEX IF NOT EXISTS user_page_history_timestamp_idx ON user_page_history (timestamp DESC);

-- Enable Row Level Security
ALTER TABLE user_page_history ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only view their own page history
CREATE POLICY "Users can view own page history" 
  ON user_page_history FOR SELECT 
  USING (auth.uid() = user_id);

-- Policy: Users can insert their own page history
CREATE POLICY "Users can insert own page history" 
  ON user_page_history FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

-- Create table for user search history
CREATE TABLE IF NOT EXISTS user_search_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  query TEXT NOT NULL,
  category TEXT NOT NULL,
  timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create index for faster queries
CREATE INDEX IF NOT EXISTS user_search_history_timestamp_idx ON user_search_history (timestamp DESC);

-- Enable Row Level Security
ALTER TABLE user_search_history ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only view their own search history
CREATE POLICY "Users can view own search history" 
  ON user_search_history FOR SELECT 
  USING (auth.uid() = user_id);

-- Policy: Users can insert their own search history
CREATE POLICY "Users can insert own search history" 
  ON user_search_history FOR INSERT 
  WITH CHECK (auth.uid() = user_id); 