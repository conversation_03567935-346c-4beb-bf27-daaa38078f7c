import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔍 Checking environment configuration...');

// Check if .env file exists
const envPath = path.join(__dirname, '..', '.env');
if (!fs.existsSync(envPath)) {
  console.warn('⚠️  .env file not found. Some features may not work properly.');
  console.log('📋 Please create a .env file with the required environment variables.');
} else {
  console.log('✅ .env file found');
}

// Check for critical environment variables
const criticalVars = [
  'VITE_SUPABASE_URL',
  'VITE_SUPABASE_ANON_KEY',
  'VITE_GOOGLE_MAPS_API_KEY',
  'OPENAI_API_KEY'
];

let missingVars = [];

try {
  const envContent = fs.readFileSync(envPath, 'utf8');
  criticalVars.forEach(varName => {
    if (!envContent.includes(varName + '=')) {
      missingVars.push(varName);
    }
  });

  if (missingVars.length > 0) {
    console.warn('⚠️  Missing environment variables:', missingVars.join(', '));
    console.log('💡 Add these variables to your .env file for full functionality.');
  } else {
    console.log('✅ All critical environment variables are present');
  }
} catch (error) {
  console.log('ℹ️  Could not read .env file, but that might be okay');
}

console.log('🎉 Environment check completed'); 