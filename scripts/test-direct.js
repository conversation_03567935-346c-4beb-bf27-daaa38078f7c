#!/usr/bin/env node

import { config } from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { createClient } from '@supabase/supabase-js';
import { XMLParser } from 'fast-xml-parser';
import { JSDOM } from 'jsdom';

// Load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = dirname(__dirname);
config({ path: join(rootDir, '.env') });

// Initialize Supabase client directly from environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('Environment Variables:');
console.log('VITE_SUPABASE_URL:', supabaseUrl || 'Missing');
console.log('VITE_SUPABASE_ANON_KEY:', supabaseKey ? 'Present' : 'Missing');

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase credentials in environment variables');
  process.exit(1);
}

// Create Supabase client directly
const supabase = createClient(supabaseUrl, supabaseKey);

// Define a test source
const testSource = {
  name: 'Dental Tribune',
  feedUrl: 'https://www.dental-tribune.com/feed/',
  type: 'rss'
};

// Helper functions (simplified)
const extractItems = (feed) => {
  if (feed.rss?.channel?.item) {
    return Array.isArray(feed.rss.channel.item) ? feed.rss.channel.item : [feed.rss.channel.item];
  }
  return [];
};

const extractContent = (item) => {
  const content = item.description || '';
  const dom = new JSDOM(content);
  return dom.window.document.body.textContent?.trim() || '';
};

const extractTitle = (item) => {
  const title = item.title || '';
  const dom = new JSDOM(title);
  return dom.window.document.body.textContent?.trim() || '';
};

// Test function to fetch and save an article
const testFetchAndSave = async () => {
  console.log('===================================');
  console.log('🔍 Testing Direct Article Fetch and Save');
  console.log('===================================');
  
  try {
    console.log(`Fetching from ${testSource.name}...`);
    
    // Fetch the RSS feed
    const response = await fetch(testSource.feedUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; SmiloBot/1.0; +https://smilo.dental)',
        'Accept': 'application/rss+xml, application/xml, text/xml'
      }
    });
    
    const xmlData = await response.text();
    
    // Parse the XML data
    const parser = new XMLParser({
      ignoreAttributes: false,
      attributeNamePrefix: "@_",
      parseAttributeValue: true,
      trimValues: true
    });
    
    const feed = parser.parse(xmlData);
    const items = extractItems(feed);
    
    if (items.length === 0) {
      console.log('No items found in feed');
      return;
    }
    
    // Process the first article
    const firstItem = items[0];
    const article = {
      title: extractTitle(firstItem),
      content: extractContent(firstItem),
      summary: extractContent(firstItem).substring(0, 200),
      source: testSource.name,
      source_type: 'rss',
      tags: ['dental', 'news'],
      status: 'published',
      is_featured: false
    };
    
    console.log('Article prepared:');
    console.log('Title:', article.title);
    console.log('Summary:', article.summary);
    
    // Test Supabase connection
    console.log('\nTesting Supabase connection...');
    const { data: connectionTest, error: connectionError } = await supabase.from('dental_articles').select('id').limit(1);
    
    if (connectionError) {
      console.error('❌ Failed to connect to Supabase:', connectionError);
      return;
    }
    
    console.log('✅ Successfully connected to Supabase');
    
    // Insert the article
    console.log('\nInserting article...');
    const { data, error } = await supabase
      .from('dental_articles')
      .insert([article])
      .select();
    
    if (error) {
      console.error('❌ Error inserting article:', error);
      return;
    }
    
    console.log('✅ Article successfully saved!');
    console.log('Article ID:', data[0].id);
    
  } catch (error) {
    console.error('❌ Error in test:', error);
  }
};

// Run the test
testFetchAndSave().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
}); 