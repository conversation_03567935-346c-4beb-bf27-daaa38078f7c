#!/usr/bin/env node

import { config } from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { createClient } from '@supabase/supabase-js';
import fetch from 'node-fetch';

// Load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = dirname(__dirname);
config({ path: join(rootDir, '.env') });

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase credentials in environment variables');
  console.error('Please ensure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Function to directly query the database
const directQuery = async (query) => {
  try {
    const url = `${supabaseUrl}/rest/v1/rpc/exec_sql`;
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${supabaseKey}`,
        'apikey': supabaseKey
      },
      body: JSON.stringify({ sql_query: query })
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Direct query failed with status ${response.status}:`, errorText);
      return null;
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error making direct query:', error);
    return null;
  }
};

// Function to get database schema
const getTableSchema = async () => {
  console.log('Checking table schema...');
  
  try {
    // First try with Supabase API
    const { data, error } = await supabase
      .from('dental_articles')
      .select('*')
      .limit(1);
    
    if (error) {
      console.log('Error fetching with Supabase API:', error.message);
    } else {
      console.log('Table schema keys:', Object.keys(data[0] || {}));
      return Object.keys(data[0] || {});
    }
  } catch (err) {
    console.error('Error checking schema with API:', err);
  }
  
  // Try with direct SQL
  try {
    const result = await directQuery('SELECT column_name FROM information_schema.columns WHERE table_name = \'dental_articles\'');
    if (result) {
      const columns = result.map(row => row.column_name);
      console.log('Table schema from SQL:', columns);
      return columns;
    }
  } catch (err) {
    console.error('Error checking schema with SQL:', err);
  }
  
  return [];
};

// Function to fix the table structure
const fixTableStructure = async () => {
  console.log('Attempting to fix dental_articles table structure...');
  
  // Try to add missing columns if they don't exist
  const missingColumns = [
    "ALTER TABLE dental_articles ADD COLUMN IF NOT EXISTS is_featured BOOLEAN DEFAULT FALSE",
    "ALTER TABLE dental_articles ADD COLUMN IF NOT EXISTS source TEXT DEFAULT 'manual' NOT NULL",
    "ALTER TABLE dental_articles ADD COLUMN IF NOT EXISTS source_type TEXT DEFAULT 'internal'",
    "ALTER TABLE dental_articles ADD COLUMN IF NOT EXISTS tags TEXT[] DEFAULT '{\"manual\"}'",
    "ALTER TABLE dental_articles ALTER COLUMN content DROP NOT NULL",
    "ALTER TABLE dental_articles ALTER COLUMN source DROP NOT NULL"
  ];
  
  for (const sql of missingColumns) {
    console.log(`Executing: ${sql}`);
    const result = await directQuery(sql);
    if (result === null) {
      console.log('⚠️ SQL execution failed or not available with current permissions');
    } else {
      console.log('✅ SQL executed successfully');
    }
  }
};

// Function to try inserting a simplified article
const insertSimpleArticle = async () => {
  console.log('Attempting to insert a simplified article...');
  
  const schema = await getTableSchema();
  const hasColumns = {
    title: schema.includes('title'),
    content: schema.includes('content'),
    source: schema.includes('source'),
    is_featured: schema.includes('is_featured')
  };
  
  console.log('Column availability:', hasColumns);
  
  // Create an object with only columns that exist
  const article = {};
  if (hasColumns.title) article.title = 'Test Article';
  if (hasColumns.content) article.content = 'Test content';
  if (hasColumns.source) article.source = 'manual';
  if (hasColumns.is_featured) article.is_featured = false;
  
  // Try inserting
  const { data, error } = await supabase
    .from('dental_articles')
    .insert([article])
    .select();
  
  if (error) {
    console.error('❌ Error inserting article:', error);
    
    // Try direct SQL insert if the API fails
    console.log('Attempting direct SQL insert...');
    const columns = Object.keys(article).join(', ');
    const values = Object.values(article).map(v => 
      typeof v === 'string' ? `'${v.replace(/'/g, "''")}'` : v
    ).join(', ');
    
    const sql = `INSERT INTO dental_articles (${columns}) VALUES (${values}) RETURNING *`;
    const result = await directQuery(sql);
    
    if (result === null) {
      console.log('⚠️ Direct SQL insert failed or not available');
    } else {
      console.log('✅ Direct SQL insert succeeded:', result);
    }
  } else {
    console.log('✅ Article inserted successfully via API:', data);
  }
};

// Function to try recreating the table
const recreateTable = async () => {
  console.log('Attempting to recreate dental_articles table as a last resort...');
  
  // Check if we have permission to drop and recreate
  const canRecreate = await directQuery('SELECT 1');
  if (canRecreate === null) {
    console.log('⚠️ You do not have permissions to recreate the table');
    return;
  }
  
  // Backup existing data if possible
  const { data: existingData, error: backupError } = await supabase
    .from('dental_articles')
    .select('*');
  
  if (backupError) {
    console.log('⚠️ Could not backup existing data:', backupError.message);
  } else {
    console.log(`Backed up ${existingData.length} existing articles`);
  }
  
  // Drop and recreate
  const dropResult = await directQuery('DROP TABLE IF EXISTS dental_articles');
  if (dropResult === null) {
    console.log('⚠️ Could not drop table');
    return;
  }
  
  const createResult = await directQuery(`
    CREATE TABLE dental_articles (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      title TEXT,
      content TEXT,
      summary TEXT,
      source TEXT DEFAULT 'manual',
      source_type TEXT DEFAULT 'internal',
      source_url TEXT,
      link TEXT,
      pub_date TIMESTAMP WITH TIME ZONE,
      inserted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      authors TEXT,
      tags TEXT[],
      image_url TEXT,
      is_featured BOOLEAN DEFAULT FALSE,
      is_published BOOLEAN DEFAULT TRUE,
      view_count INTEGER DEFAULT 0,
      quality_score FLOAT DEFAULT 0.0
    )
  `);
  
  if (createResult === null) {
    console.log('⚠️ Could not recreate table');
  } else {
    console.log('✅ Table recreated successfully');
    
    // Restore data if we had a backup
    if (existingData && existingData.length > 0) {
      console.log('Attempting to restore data...');
      
      // We need to handle restoring data carefully as the schema might have changed
      for (const article of existingData) {
        const { error: restoreError } = await supabase
          .from('dental_articles')
          .insert([article]);
        
        if (restoreError) {
          console.log(`⚠️ Could not restore article ${article.id}: ${restoreError.message}`);
        }
      }
      
      console.log('Data restoration attempted');
    }
  }
};

// Create a useful view
const createView = async () => {
  console.log('Attempting to create a useful view...');
  
  const viewResult = await directQuery(`
    CREATE OR REPLACE VIEW simple_articles AS
    SELECT id, title, COALESCE(source, 'unknown') as source, inserted_at
    FROM dental_articles
    ORDER BY inserted_at DESC
  `);
  
  if (viewResult === null) {
    console.log('⚠️ Could not create view');
  } else {
    console.log('✅ View created successfully');
  }
};

// Main function
const main = async () => {
  console.log('===================================');
  console.log('🔧 Database Fix Utility');
  console.log('===================================');
  
  // Get current schema
  const schema = await getTableSchema();
  
  if (schema.length === 0) {
    console.log('⚠️ Could not determine table schema');
  } else {
    console.log(`Table has ${schema.length} columns`);
    
    // Check for missing columns
    const missingColumns = [];
    ['is_featured', 'source', 'source_type', 'tags'].forEach(col => {
      if (!schema.includes(col)) {
        missingColumns.push(col);
      }
    });
    
    if (missingColumns.length > 0) {
      console.log(`Missing columns: ${missingColumns.join(', ')}`);
    } else {
      console.log('✅ All required columns exist');
    }
  }
  
  // Try to fix table structure
  await fixTableStructure();
  
  // Try inserting a simple article
  await insertSimpleArticle();
  
  // Only recreate as a last resort if explicitly confirmed
  const shouldRecreate = process.argv.includes('--recreate');
  if (shouldRecreate) {
    await recreateTable();
  } else {
    console.log('Skipping table recreation (use --recreate flag to enable)');
  }
  
  // Try to create a useful view
  await createView();
  
  console.log('===================================');
  console.log('Fix utility completed');
  console.log('===================================');
};

// Run the main function
main().catch(error => {
  console.error('Failed to run script:', error);
  process.exit(1);
}); 