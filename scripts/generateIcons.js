#!/usr/bin/env node

/**
 * <PERSON>ript to generate PNG icons from SVG files for PWA
 * This runs as part of the build process to ensure icons are available
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import sharp from 'sharp';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Icon sizes to generate
const ICON_SIZES = [192, 512];
const SOURCE_DIR = path.resolve(__dirname, '../public/images');
const DEST_DIR = path.resolve(__dirname, '../public/images');

/**
 * Ensure destination directory exists
 */
if (!fs.existsSync(DEST_DIR)) {
  fs.mkdirSync(DEST_DIR, { recursive: true });
}

/**
 * Convert SVG to PNG using sharp
 * @param {string} svgPath - Path to SVG file
 * @param {string} outputPath - Path for output PNG file
 * @param {number} size - Desired size of the PNG
 */
async function convertSvgToPng(svgPath, outputPath, size) {
  try {
    console.log(`Converting ${svgPath} to ${outputPath} at ${size}px...`);
    
    await sharp(svgPath)
      .resize(size, size)
      .png()
      .toFile(outputPath);
      
    console.log(`✅ Successfully created ${outputPath}`);
  } catch (error) {
    console.error(`❌ Error converting SVG to PNG: ${error.message}`);
    
    // If SVG conversion failed, create a fallback PNG
    try {
      console.log(`Creating fallback icon for ${size}px...`);
      
      // Create a simple colored square with text
      const svgBuffer = Buffer.from(`
        <svg xmlns="http://www.w3.org/2000/svg" width="${size}" height="${size}" viewBox="0 0 ${size} ${size}">
          <rect width="${size}" height="${size}" fill="#0d6efd" rx="${size/10}" ry="${size/10}" />
          <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="${size/4}px" font-weight="bold" 
            fill="#ffffff" text-anchor="middle" dominant-baseline="middle">SD</text>
        </svg>
      `);
      
      await sharp(svgBuffer)
        .resize(size, size)
        .png()
        .toFile(outputPath);
        
      console.log(`✅ Created fallback icon ${outputPath}`);
    } catch (fallbackError) {
      console.error(`❌ Failed to create fallback icon: ${fallbackError.message}`);
    }
  }
}

/**
 * Generate PNG icons for all sizes
 */
async function generateIcons() {
  console.log('🚀 Generating PWA icons...');
  
  let generatedCount = 0;
  let existingCount = 0;
  let errorCount = 0;
  
  for (const size of ICON_SIZES) {
    try {
      const svgPath = path.join(SOURCE_DIR, `icon-${size}x${size}.svg`);
      const pngPath = path.join(DEST_DIR, `icon-${size}x${size}.png`);
      
      // Check if SVG exists
      if (fs.existsSync(svgPath)) {
        // Check if PNG already exists and force regeneration
        if (!fs.existsSync(pngPath)) {
          await convertSvgToPng(svgPath, pngPath, size);
          generatedCount++;
        } else {
          console.log(`ℹ️ PNG icon already exists: ${pngPath}`);
          existingCount++;
        }
      } else {
        console.warn(`⚠️ SVG icon not found: ${svgPath}`);
        // Create a fallback PNG directly
        const pngPath = path.join(DEST_DIR, `icon-${size}x${size}.png`);
        
        if (!fs.existsSync(pngPath)) {
          try {
            // Create a simple colored square with text
            const svgBuffer = Buffer.from(`
              <svg xmlns="http://www.w3.org/2000/svg" width="${size}" height="${size}" viewBox="0 0 ${size} ${size}">
                <rect width="${size}" height="${size}" fill="#0d6efd" rx="${size/10}" ry="${size/10}" />
                <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="${size/4}px" font-weight="bold" 
                  fill="#ffffff" text-anchor="middle" dominant-baseline="middle">SD</text>
              </svg>
            `);
            
            await sharp(svgBuffer)
              .resize(size, size)
              .png()
              .toFile(pngPath);
              
            console.log(`✅ Created fallback icon ${pngPath}`);
            generatedCount++;
          } catch (fallbackError) {
            console.error(`❌ Failed to create fallback icon: ${fallbackError.message}`);
            errorCount++;
          }
        }
      }
    } catch (error) {
      console.error(`❌ Error processing icon size ${size}:`, error);
      errorCount++;
    }
  }
  
  console.log('\n📊 Icon Generation Summary:');
  console.log(`✅ Generated: ${generatedCount}`);
  console.log(`ℹ️ Existing: ${existingCount}`);
  console.log(`❌ Errors: ${errorCount}`);
  console.log('\n');
}

// Run the icon generation
generateIcons().catch(error => {
  console.error('❌ Fatal error generating icons:', error);
  process.exit(1);
}); 