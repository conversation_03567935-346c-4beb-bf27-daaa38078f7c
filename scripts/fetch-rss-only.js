#!/usr/bin/env node

import { XMLParser } from 'fast-xml-parser';
import { J<PERSON><PERSON> } from 'jsdom';

// Define trusted sources for RSS feeds
const TRUSTED_SOURCES = {
  ADA: {
    name: 'American Dental Association',
    feedUrl: 'https://www.ada.org/publications/ada-news/feeds/adanews',
    type: 'rss'
  },
  DENTAL_IQ: {
    name: 'DentistryIQ',
    feedUrl: 'https://www.dentistryiq.com/rss/all-content.feed',
    type: 'rss'
  },
  DENTAL_ECONOMICS: {
    name: 'Dental Economics',
    feedUrl: 'https://www.dentaleconomics.com/rss/all-content.feed',
    type: 'rss'
  },
  COLGATE_ORAL_HEALTH: {
    name: 'Colgate Oral Health Network',
    feedUrl: 'https://www.colgateprofessional.com/news/rss',
    type: 'rss'
  },
  DENTAL_TRIBUNE: {
    name: 'Dental Tribune',
    feedUrl: 'https://www.dental-tribune.com/feed/',
    type: 'rss'
  }
};

// Helper function to extract items from an RSS feed
const extractItems = (feed) => {
  if (feed.rss?.channel?.item) {
    return Array.isArray(feed.rss.channel.item) ? feed.rss.channel.item : [feed.rss.channel.item];
  }
  if (feed.feed?.entry) {
    return Array.isArray(feed.feed.entry) ? feed.feed.entry : [feed.feed.entry];
  }
  if (feed.rdf?.item) {
    return Array.isArray(feed.rdf.item) ? feed.rdf.item : [feed.rdf.item];
  }
  return [];
};

// Extract and clean content
const extractContent = (item) => {
  const content = item.description || 
                 item['content:encoded'] || 
                 item.content?.['#text'] ||
                 item.content ||
                 item.summary ||
                 '';
                 
  // Clean the content
  const dom = new JSDOM(content);
  const text = dom.window.document.body.textContent || '';
  return text.trim();
};

// Extract and clean title
const extractTitle = (item) => {
  const title = item.title?.['#text'] || item.title || '';
  const dom = new JSDOM(title);
  return dom.window.document.body.textContent?.trim() || '';
};

// Extract and clean link
const extractLink = (item) => {
  const link = item.link?.['#text'] || item.link || '';
  return link.trim();
};

// Extract summary from content
const extractSummary = (content) => {
  const dom = new JSDOM(content);
  const text = dom.window.document.body.textContent || '';
  return text.slice(0, 300) + (text.length > 300 ? '...' : '');
};

// Extract and normalize tags
const extractTags = (categories) => {
  if (!categories) return [];
  const tags = Array.isArray(categories) ? categories : [categories];
  return tags.map(tag => tag.toLowerCase().trim());
};

// Simple delay function for retries
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Fetch with retry logic
const fetchWithRetry = async (url, options, maxRetries = 3) => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const response = await fetch(url, options);
      if (response.ok) {
        return response;
      }
      
      throw new Error(`HTTP error! status: ${response.status}`);
    } catch (error) {
      if (attempt === maxRetries) {
        throw error;
      }
      
      const delay = 1000 * Math.pow(2, attempt);
      console.log(`Retry attempt ${attempt} for ${url} after ${delay}ms`);
      await sleep(delay);
    }
  }
};

// Fetch articles from an RSS feed
const fetchRssFeed = async (source) => {
  try {
    console.log(`Fetching from ${source.name}...`);
    const response = await fetchWithRetry(source.feedUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; SmiloBot/1.0; +https://smilo.dental)',
        'Accept': 'application/rss+xml, application/xml, text/xml, application/atom+xml'
      }
    });
    
    const xmlData = await response.text();
    
    // Validate XML data
    if (!xmlData.trim() || !xmlData.includes('<?xml')) {
      throw new Error('Invalid XML data received');
    }
    
    const parser = new XMLParser({
      ignoreAttributes: false,
      attributeNamePrefix: "@_",
      parseAttributeValue: true,
      trimValues: true
    });
    
    const feed = parser.parse(xmlData);
    const items = extractItems(feed);
    
    if (!items.length) {
      console.warn(`No items found in feed from ${source.name}`);
      return [];
    }
    
    // Process each item to create articles
    const articles = items.map(item => {
      const content = extractContent(item);
      return {
        title: extractTitle(item),
        content: content,
        summary: extractSummary(content),
        source: source.name,
        source_type: 'rss',
        source_url: source.feedUrl,
        link: extractLink(item),
        tags: extractTags(item.category || item.tags)
      };
    });
    
    // Filter out invalid articles
    return articles.filter(article => 
      article.title && article.title.trim() && 
      article.content && article.content.trim()
    );
  } catch (error) {
    console.error(`Error fetching RSS feed from ${source.name}:`, error);
    return [];
  }
};

// Test function to fetch an article from the first source
const testFetch = async () => {
  console.log('===================================');
  console.log('🔍 Testing RSS Fetching Only');
  console.log('===================================');
  
  // Get the first source from the list
  const sourceName = Object.keys(TRUSTED_SOURCES)[0];
  const source = TRUSTED_SOURCES[sourceName];
  
  console.log(`Testing fetch from: ${source.name}`);
  
  try {
    const articles = await fetchRssFeed(source);
    
    if (articles.length > 0) {
      console.log('✅ Successfully fetched articles');
      console.log(`Found ${articles.length} articles`);
      
      // Display the first article details
      console.log('\nSample article:');
      console.log('Title:', articles[0].title);
      console.log('Summary:', articles[0].summary?.slice(0, 150) + '...');
      console.log('Source:', articles[0].source);
      console.log('Source Type:', articles[0].source_type);
      console.log('Link:', articles[0].link);
      console.log('Tags:', articles[0].tags);
      
      // Save the article to a local file for reference
      console.log('\nSaving article to article-sample.json...');
      const fs = await import('fs/promises');
      await fs.writeFile('article-sample.json', JSON.stringify(articles[0], null, 2));
      
      console.log('✅ Article sample saved to article-sample.json');
      console.log('\n✅ RSS fetching is working properly!');
    } else {
      console.log('⚠️ No articles found. The feed might be empty or the parsing failed.');
    }
  } catch (error) {
    console.error('❌ Error testing article fetch:', error);
  }
};

// Run the test
testFetch().catch(error => {
  console.error('Unhandled error in test:', error);
  process.exit(1);
}); 