#!/usr/bin/env node

import { config } from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = dirname(__dirname);
config({ path: join(rootDir, '.env') });

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('Checking environment variables:');
console.log('VITE_SUPABASE_URL:', supabaseUrl ? 'Present' : 'Missing');
console.log('VITE_SUPABASE_ANON_KEY:', supabaseKey ? 'Present' : 'Missing');

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase credentials in environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Function to insert a test article
const insertTestArticle = async () => {
  console.log('Attempting to insert a test article...');
  
  try {
    // Create a basic article
    const article = {
      title: 'Updated Article System Test',
      content: 'This is a test article created by the update-article-system script.',
      summary: 'Test article for system update',
      source: 'system',
      source_type: 'internal',
      is_featured: false,
      tags: ['test', 'system'],
      status: 'published'
    };
    
    // Insert the article
    const { data: insertedArticle, error: insertError } = await supabase
      .from('dental_articles')
      .insert([article])
      .select();
    
    if (insertError) {
      console.error('❌ Error inserting test article:', insertError);
      return;
    }
    
    console.log('✅ Test article inserted successfully');
    console.log('Article ID:', insertedArticle[0].id);
  } catch (error) {
    console.error('❌ An error occurred:', error);
  }
};

// Function to test article retrieval
const testArticleRetrieval = async () => {
  console.log('\nTesting article retrieval...');
  
  try {
    // Try to get articles with created_at ordering
    const { data, error } = await supabase
      .from('dental_articles')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(5);
    
    if (error) {
      console.error('❌ Error retrieving articles:', error);
      return;
    }
    
    console.log(`✅ Retrieved ${data.length} articles successfully`);
    
    if (data.length > 0) {
      console.log('\nArticle details:');
      data.forEach((article, index) => {
        console.log(`\n${index + 1}. Title: ${article.title}`);
        console.log(`   ID: ${article.id}`);
        console.log(`   Source: ${article.source}`);
        console.log(`   Created: ${new Date(article.created_at).toLocaleString()}`);
      });
    }
  } catch (error) {
    console.error('❌ An error occurred during retrieval:', error);
  }
};

// Main function
const main = async () => {
  console.log('===================================');
  console.log('🔧 Article System Update Utility');
  console.log('===================================');
  
  await insertTestArticle();
  await testArticleRetrieval();
  
  console.log('\n===================================');
  console.log('✅ Article system update complete!');
  console.log('===================================');
};

// Run the main function
main().catch(error => {
  console.error('Failed to run script:', error);
  process.exit(1);
}); 