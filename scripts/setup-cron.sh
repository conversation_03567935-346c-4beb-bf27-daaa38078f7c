#!/bin/bash

# Get the absolute path of the project directory
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
SCRIPT_PATH="$PROJECT_DIR/scripts/fetchArticlesNode.js"
LOG_DIR="$PROJECT_DIR/logs"
LOG_FILE="$LOG_DIR/article-fetch.log"

echo "Setting up hourly article fetching cron job..."
echo "Project directory: $PROJECT_DIR"

# Ensure logs directory exists
mkdir -p "$LOG_DIR"
touch "$LOG_FILE"
chmod 644 "$LOG_FILE"

# Determine Node.js executable path
NODE_PATH=$(which node)
if [ -z "$NODE_PATH" ]; then
  echo "Error: Node.js not found in PATH. Please install Node.js."
  exit 1
fi

echo "Using Node.js at: $NODE_PATH"

# Create a wrapper script that includes environment variables
WRAPPER_SCRIPT="$PROJECT_DIR/scripts/fetch-articles-wrapper.sh"
cat > "$WRAPPER_SCRIPT" << EOL
#!/bin/bash
cd "$PROJECT_DIR" || exit 1

# Load environment variables from .env file if it exists
if [ -f "$PROJECT_DIR/.env" ]; then
  set -a
  source "$PROJECT_DIR/.env"
  set +a
fi

# Run the script
$NODE_PATH --experimental-modules "$SCRIPT_PATH" >> "$LOG_FILE" 2>&1
EOL

# Make wrapper script executable
chmod +x "$WRAPPER_SCRIPT"

# Create the cron job command - run every hour at minute 0
CRON_CMD="0 * * * * $WRAPPER_SCRIPT"

# Check if the cron job already exists
EXISTING_CRON=$(crontab -l 2>/dev/null | grep -F "$WRAPPER_SCRIPT")

if [ -z "$EXISTING_CRON" ]; then
    # Add the new cron job
    (crontab -l 2>/dev/null; echo "$CRON_CMD") | crontab -
    echo "✅ Cron job added successfully!"
else
    echo "ℹ️ Cron job already exists."
fi

# Ensure permissions on the fetchArticles.js script
chmod +x "$SCRIPT_PATH"

echo "✨ Setup complete! Articles will be fetched every hour."
echo "📝 Logs will be written to: $LOG_FILE"

# Run once immediately to test
echo "🚀 Running initial fetch to test setup..."
$WRAPPER_SCRIPT
echo "Initial fetch completed. Check $LOG_FILE for details." 