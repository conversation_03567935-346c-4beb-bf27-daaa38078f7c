#!/usr/bin/env node

import { config } from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { createClient } from '@supabase/supabase-js';
import fs from 'fs';

// Get the path to the script and project root
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = dirname(__dirname);

// Load environment variables from .env file
config({ path: join(rootDir, '.env') });

// Manually load environment variables if dotenv doesn't work
try {
  if (!process.env.VITE_SUPABASE_URL) {
    const envContent = fs.readFileSync(join(rootDir, '.env'), 'utf8');
    const envLines = envContent.split('\n');
    
    envLines.forEach(line => {
      const match = line.match(/^(VITE_[A-Z_]+)=(.+)$/);
      if (match) {
        const [, key, value] = match;
        process.env[key] = value.trim();
      }
    });
  }
} catch (err) {
  console.warn('Error reading .env file manually:', err.message);
}

// Check if env variables are loaded
console.log('Checking environment variables:');
console.log('VITE_SUPABASE_URL:', process.env.VITE_SUPABASE_URL ? 'Present' : 'Missing');
console.log('VITE_SUPABASE_ANON_KEY:', process.env.VITE_SUPABASE_ANON_KEY ? 'Present' : 'Missing');

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase credentials in environment variables');
  console.error('Please ensure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set in .env file');
  process.exit(1);
}

console.log('Creating Supabase client with:');
console.log('URL:', supabaseUrl);
console.log('Key:', supabaseKey ? '[Key available]' : 'Missing');

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

// Now we can import our service module with the global supabase instance available
global.supabase = supabase;

// Import the article service modules
import { fetchAndStoreArticles, cleanupOldArticles } from '../src/lib/services/articleFetchService.js';

// Maximum execution time (in milliseconds) - 15 minutes
const MAX_EXECUTION_TIME = 15 * 60 * 1000;

console.log('========================================');
console.log(`Starting scheduled article fetch at ${new Date().toISOString()}`);
console.log('========================================');

// Set a timeout to terminate the script if it runs too long
const timeoutId = setTimeout(() => {
  console.error('Script execution exceeded maximum time limit. Terminating...');
  process.exit(1);
}, MAX_EXECUTION_TIME);

// Clear timeout when done to avoid hanging the process
timeoutId.unref();

const runArticleFetch = async () => {
  try {
    // Check Supabase connection first
    const { error: connectionError } = await supabase.from('dental_articles').select('count').limit(1);
    if (connectionError && connectionError.code !== '42P01') {
      throw new Error(`Supabase connection failed: ${connectionError.message}`);
    }
    
    if (connectionError && connectionError.code === '42P01') {
      console.log('⚠️ dental_articles table does not exist. Please run setup-article-system first.');
      process.exit(1);
    }
    
    console.log('✅ Supabase connection confirmed');
    console.log('📚 Fetching articles from reputable dental sources...');
    
    // Fetch and store new articles
    const startTime = Date.now();
    const results = await fetchAndStoreArticles();
    const duration = Date.now() - startTime;
    
    const metrics = {
      sources: Object.keys(results.sources || {}).length,
      fetched: results.total || 0,
      stored: results.successful || 0,
      skipped: results.skipped || 0,
      failed: results.failed || 0,
      durationMs: duration
    };
    
    console.log('📊 Article fetch metrics:');
    console.log(`- Sources checked: ${metrics.sources}`);
    console.log(`- Articles fetched: ${metrics.fetched}`);
    console.log(`- Articles stored: ${metrics.stored}`);
    console.log(`- Articles skipped: ${metrics.skipped}`);
    console.log(`- Failed articles: ${metrics.failed}`);
    console.log(`- Duration: ${metrics.durationMs}ms`);
    
    // Clean up old articles
    console.log('🧹 Cleaning up old articles...');
    const cleanupMetrics = await cleanupOldArticles();
    console.log(`✅ Cleanup completed: ${cleanupMetrics.deleted} old articles removed, ${cleanupMetrics.remaining} articles kept`);
    
    // Log metrics to Supabase
    await supabase.from('article_metrics').insert({
      sources: metrics.sources,
      fetched: metrics.fetched,
      stored: metrics.stored,
      skipped: metrics.skipped,
      failed: metrics.failed,
      successful_requests: metrics.stored,
      total_requests: metrics.fetched,
      error_count: metrics.failed,
      duration_ms: metrics.durationMs
    });
    
    console.log('========================================');
    console.log(`Article fetch completed successfully at ${new Date().toISOString()}`);
    console.log('========================================');
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error in scheduled article fetch:');
    console.error(error);
    
    // Log error to Supabase if connection is working
    try {
      await supabase.from('system_logs').insert({
        component: 'article_fetcher_script',
        level: 'error',
        message: `Script execution error: ${error.message}`,
        metadata: { 
          error: error.message,
          stack: error.stack
        }
      });
    } catch (logError) {
      console.error('Failed to log error to Supabase:', logError.message);
    }
    
    process.exit(1);
  }
};

// Run the script
runArticleFetch(); 