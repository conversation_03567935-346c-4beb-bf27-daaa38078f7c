#!/usr/bin/env node

import { config } from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { createClient } from '@supabase/supabase-js';
import fs from 'fs';

// Get the path to the script and project root
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = dirname(__dirname);

// Load environment variables from .env file
config({ path: join(rootDir, '.env') });

// Manually load environment variables if dotenv doesn't work
try {
  if (!process.env.VITE_SUPABASE_URL) {
    const envContent = fs.readFileSync(join(rootDir, '.env'), 'utf8');
    const envLines = envContent.split('\n');
    
    envLines.forEach(line => {
      const match = line.match(/^(VITE_[A-Z_]+)=(.+)$/);
      if (match) {
        const [, key, value] = match;
        process.env[key] = value.trim();
      }
    });
  }
} catch (err) {
  console.warn('Error reading .env file manually:', err.message);
}

// Check if env variables are loaded
console.log('Checking environment variables:');
console.log('VITE_SUPABASE_URL:', process.env.VITE_SUPABASE_URL ? 'Present' : 'Missing');
console.log('VITE_SUPABASE_ANON_KEY:', process.env.VITE_SUPABASE_ANON_KEY ? 'Present' : 'Missing');

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase credentials in environment variables');
  console.error('Please ensure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set in .env file');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

async function addBasicArticle() {
  console.log('Attempting to add a basic article without problematic fields...');
  
  try {
    // First, try without the problematic fields
    const basicArticle = {
      title: 'Welcome to Smilo Dental Resources',
      content: 'This is a sample article to help you get started with the dental resources system. Real articles will be fetched automatically from trusted dental sources.',
      summary: 'Welcome to the Smilo Dental Resources system',
      source: 'Smilo Dental',
      source_type: 'internal'
    };
    
    const { data, error } = await supabase
      .from('dental_articles')
      .insert([basicArticle])
      .select();
    
    if (error) {
      console.error('❌ Error adding basic article:', error);
    } else {
      console.log('✅ Successfully added basic article:', data);
      return data[0];
    }
  } catch (err) {
    console.error('❌ Error in basic article insertion:', err);
  }
  
  return null;
}

async function viewTableStructure() {
  console.log('Checking table structure...');
  
  try {
    // Try to get table definition
    const { data, error } = await supabase
      .from('dental_articles')
      .select()
      .limit(1);
    
    if (error) {
      console.error('❌ Error querying table:', error);
    } else {
      if (data && data.length > 0) {
        console.log('Table structure based on returned data:');
        console.log(Object.keys(data[0]));
        return Object.keys(data[0]);
      } else {
        console.log('No records found in the table');
      }
    }
  } catch (err) {
    console.error('❌ Error checking table structure:', err);
  }
  
  return [];
}

async function modifyArticleSystem() {
  try {
    // First, check the structure
    const columns = await viewTableStructure();
    
    // Create a basic article if needed
    if (columns.length === 0) {
      console.log('No columns detected, trying to create a basic article');
      const article = await addBasicArticle();
      if (article) {
        // Re-check the structure
        await viewTableStructure();
      }
    }
    
    // Option 1: Just run the original function with the is_featured field removed
    console.log('Trying to add a sample article with specific fields...');
    
    // Test with specific fields only
    const { error: insertError } = await supabase
      .from('dental_articles')
      .insert([{
        title: 'Sample Article for Testing',
        content: 'This is a test article content',
        summary: 'Test summary',
        source: 'Test',
        source_type: 'test'
      }]);
    
    if (insertError) {
      console.error('❌ Could not add sample article with reduced fields:', insertError);
    } else {
      console.log('✅ Successfully added sample article with reduced fields!');
    }
    
    // Now update the setup script to avoid using problematic fields
    console.log('\nIMPORTANT NEXT STEPS:');
    console.log('1. Update src/scripts/setupArticleSystem.js to remove the is_featured field from the sample article');
    console.log('2. Update src/lib/services/articleFetchService.js to handle missing fields');
    console.log('3. Run "npm run setup-article-system" again');
    console.log('4. Then run "npm run test-article-fetch"');
    
  } catch (error) {
    console.error('❌ Error in modifyArticleSystem:', error);
  }
}

// Run the fix
modifyArticleSystem()
  .then(() => console.log('Done'))
  .catch(err => console.error('Unhandled error:', err))
  .finally(() => setTimeout(() => process.exit(), 1000)); 