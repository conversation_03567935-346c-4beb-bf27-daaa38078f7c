#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to apply security fixes for vulnerabilities identified in April 2025
 * 
 * This script:
 * 1. Updates package.json with patched dependency versions
 * 2. Creates the middleware security enhancement file if it doesn't exist
 * 3. Updates the server.js file to include the new middleware
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Colors for console output
const colors = {
  reset: "\x1b[0m",
  bright: "\x1b[1m",
  red: "\x1b[31m",
  green: "\x1b[32m",
  yellow: "\x1b[33m",
  blue: "\x1b[34m",
  cyan: "\x1b[36m"
};

// Root directory of the project
const rootDir = path.resolve(__dirname, '..');

// Security document path
const securityDocPath = path.join(rootDir, 'SECURITY_UPDATE_2025.md');

console.log(`${colors.bright}${colors.blue}=== Smilo Dental Security Fix Script ===${colors.reset}\n`);
console.log(`${colors.cyan}This script will apply security fixes for the vulnerabilities identified in April 2025.${colors.reset}\n`);

// Check if user wants to proceed
rl.question(`${colors.yellow}Do you want to proceed with applying security fixes? (y/n) ${colors.reset}`, (answer) => {
  if (answer.toLowerCase() !== 'y') {
    console.log(`\n${colors.red}Security fix application cancelled.${colors.reset}`);
    rl.close();
    return;
  }

  console.log(`\n${colors.green}Starting security fix application...${colors.reset}\n`);

  try {
    // Step 1: Add resolutions to package.json
    updatePackageJson();
    
    // Step 2: Create or update middleware security file
    createOrUpdateMiddlewareSecurity();
    
    // Step 3: Update server.js file
    updateServerJs();
    
    // Step 4: Install dependencies
    installDependencies();

    // Success message
    console.log(`\n${colors.bright}${colors.green}Security fixes successfully applied!${colors.reset}\n`);
    console.log(`${colors.cyan}Please check the following files:${colors.reset}`);
    console.log(`- package.json (updated dependency resolutions)`);
    console.log(`- src/middleware/middlewareSecurity.js (new security middleware)`);
    console.log(`- src/server/server.js (updated to use new middleware)`);
    console.log(`- ${securityDocPath} (documentation of fixes)\n`);
    
    console.log(`${colors.yellow}For more information about the security vulnerabilities and fixes, see:${colors.reset}`);
    console.log(`${securityDocPath}\n`);

    rl.close();
  } catch (error) {
    console.error(`\n${colors.red}Error applying security fixes: ${error.message}${colors.reset}\n`);
    rl.close();
    process.exit(1);
  }
});

/**
 * Update package.json with security resolutions
 */
function updatePackageJson() {
  console.log(`${colors.bright}Updating package.json with security resolutions...${colors.reset}`);
  
  const packageJsonPath = path.join(rootDir, 'package.json');
  
  if (!fs.existsSync(packageJsonPath)) {
    throw new Error('package.json not found at the project root.');
  }
  
  let packageJson;
  try {
    packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  } catch (error) {
    throw new Error(`Failed to parse package.json: ${error.message}`);
  }
  
  // Add or update resolutions
  packageJson.resolutions = {
    ...(packageJson.resolutions || {}),
    "next": "^15.2.3",
    "elliptic": "^6.5.4",
    "@babel/traverse": "^7.26.10",
    "@xmldom/xmldom": "^0.8.10",
    "path-to-regexp": "^6.2.1",
    "cross-spawn": "^7.0.3",
    "http-proxy-middleware": "^2.0.6",
    "image-size": "^1.1.1"
  };
  
  // Write updated package.json
  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
  console.log(`${colors.green}Successfully updated package.json with security resolutions.${colors.reset}`);
}

/**
 * Create or update the middleware security file
 */
function createOrUpdateMiddlewareSecurity() {
  console.log(`${colors.bright}Creating/updating middleware security file...${colors.reset}`);
  
  const middlewareDir = path.join(rootDir, 'src', 'middleware');
  const securityFilePath = path.join(middlewareDir, 'middlewareSecurity.js');
  
  // Create middleware directory if it doesn't exist
  if (!fs.existsSync(middlewareDir)) {
    fs.mkdirSync(middlewareDir, { recursive: true });
  }
  
  // Content for the middleware security file
  const securityFileContent = `/**
 * Middleware Security Enhancements
 * 
 * This middleware adds protection against known vulnerabilities,
 * specifically targeting the Next.js middleware authorization bypass vulnerability (CVE-2025-29927)
 * and other security concerns.
 */

/**
 * Block the x-middleware-subrequest header to prevent Next.js middleware bypass
 * This mitigates the CVE-2025-29927 vulnerability
 * @param {Request} req - The request object
 * @param {Response} res - The response object
 * @param {Function} next - The next middleware function
 */
export function protectAgainstMiddlewareBypass(req, res, next) {
  // Remove the x-middleware-subrequest header if present
  // This prevents exploitation of CVE-2025-29927
  if (req.headers && req.headers['x-middleware-subrequest']) {
    delete req.headers['x-middleware-subrequest'];
    
    // Log potential attack attempt (consider sending to security monitoring)
    console.warn('Security alert: Potentially malicious request with x-middleware-subrequest header blocked');
  }
  
  next();
}

/**
 * Implement rate limiting on sensitive endpoints
 * @param {Request} req - The request object
 * @param {Response} res - The response object
 * @param {Function} next - The next middleware function
 */
export function sensitiveEndpointRateLimiting(req, res, next) {
  // This is a placeholder for more sophisticated rate limiting
  // In a real implementation, you would use a library like express-rate-limit
  // and potentially integrate with Redis or another store for distributed rate limiting
  
  // Example of paths that should be rate limited
  const sensitiveEndpoints = [
    '/api/auth',
    '/api/admin',
    '/api/user',
    '/login',
    '/signup'
  ];
  
  // Check if the current path should be rate limited
  const shouldRateLimit = sensitiveEndpoints.some(endpoint => 
    req.path.startsWith(endpoint)
  );
  
  if (shouldRateLimit) {
    // Apply specific rate limiting logic or delegate to express-rate-limit
    // For now, we just add a header to indicate rate limiting is active
    res.setHeader('X-Rate-Limit-Protected', 'true');
  }
  
  next();
}

/**
 * Sanitize input parameters to prevent injection attacks
 * @param {Request} req - The request object
 * @param {Response} res - The response object
 * @param {Function} next - The next middleware function
 */
export function sanitizeInputParameters(req, res, next) {
  // Basic sanitization for query parameters
  if (req.query) {
    Object.keys(req.query).forEach(key => {
      if (typeof req.query[key] === 'string') {
        // Remove potentially dangerous characters
        req.query[key] = req.query[key]
          .replace(/[<>]/g, '') // Remove angle brackets
          .replace(/javascript:/gi, '') // Remove javascript: protocol
          .replace(/on\\w+=/gi, ''); // Remove event handlers
      }
    });
  }
  
  // Similar sanitization for request body if it exists and is an object
  if (req.body && typeof req.body === 'object') {
    Object.keys(req.body).forEach(key => {
      if (typeof req.body[key] === 'string') {
        req.body[key] = req.body[key]
          .replace(/[<>]/g, '')
          .replace(/javascript:/gi, '')
          .replace(/on\\w+=/gi, '');
      }
    });
  }
  
  next();
}

/**
 * Add additional authorization validation beyond middleware
 * This provides defense in depth against middleware bypass vulnerabilities
 * @param {Request} req - The request object
 * @param {Response} res - The response object
 * @param {Function} next - The next middleware function
 */
export function additionalAuthorizationValidation(req, res, next) {
  // Protected routes that require additional validation
  const protectedRoutes = [
    '/api/admin',
    '/api/user/profile',
    '/api/settings'
  ];
  
  // Check if the current path is a protected route
  const isProtectedRoute = protectedRoutes.some(route => 
    req.path.startsWith(route)
  );
  
  if (isProtectedRoute) {
    // In a real implementation, you would validate the user session here
    // regardless of any middleware authentication
    
    // Example validation logic (implement your actual validation)
    const hasValidToken = validateAuthToken(req);
    
    if (!hasValidToken) {
      return res.status(401).json({ 
        error: 'Unauthorized access',
        message: 'Additional authentication required'
      });
    }
  }
  
  next();
}

/**
 * Dummy auth token validation function - replace with your actual implementation
 * @param {Request} req - The request object
 * @returns {boolean} Whether the token is valid
 */
function validateAuthToken(req) {
  // This is a placeholder for actual token validation logic
  // In a real implementation, you would validate the session token,
  // check user permissions, etc.
  
  // Example: Check for Authorization header
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return false;
  }
  
  // Extract and validate token (implement your actual validation)
  const token = authHeader.split(' ')[1];
  return token && token.length > 10; // Dummy validation
}

/**
 * Apply all middleware security enhancements
 * @param {Request} req - The request object
 * @param {Response} res - The response object
 * @param {Function} next - The next middleware function
 */
export function applyAllSecurityEnhancements(req, res, next) {
  // Apply all security enhancements in sequence
  protectAgainstMiddlewareBypass(req, res, () => {
    sensitiveEndpointRateLimiting(req, res, () => {
      sanitizeInputParameters(req, res, () => {
        additionalAuthorizationValidation(req, res, next);
      });
    });
  });
}

export default {
  protectAgainstMiddlewareBypass,
  sensitiveEndpointRateLimiting,
  sanitizeInputParameters,
  additionalAuthorizationValidation,
  applyAllSecurityEnhancements
};`;
  
  // Write the security middleware file
  fs.writeFileSync(securityFilePath, securityFileContent);
  console.log(`${colors.green}Successfully created/updated middleware security file at ${securityFilePath}.${colors.reset}`);
}

/**
 * Update server.js file to use the new middleware
 */
function updateServerJs() {
  console.log(`${colors.bright}Updating server.js to use new security middleware...${colors.reset}`);
  
  const serverJsPath = path.join(rootDir, 'src', 'server', 'server.js');
  
  if (!fs.existsSync(serverJsPath)) {
    throw new Error('server.js not found at src/server/server.js.');
  }
  
  let serverJsContent = fs.readFileSync(serverJsPath, 'utf8');
  
  // Check if the middleware is already imported
  if (!serverJsContent.includes('middlewareSecurity.js')) {
    // Add import for the new middleware
    const importStatement = `import { 
  protectAgainstMiddlewareBypass, 
  sensitiveEndpointRateLimiting,
  sanitizeInputParameters,
  additionalAuthorizationValidation 
} from '../middleware/middlewareSecurity.js';`;
    
    // Find the last import statement
    const lastImportIndex = serverJsContent.lastIndexOf('import ');
    const endOfLastImportIndex = serverJsContent.indexOf(';', lastImportIndex) + 1;
    
    // Insert the new import after the last import
    serverJsContent = 
      serverJsContent.substring(0, endOfLastImportIndex) + 
      '\n' + importStatement + 
      serverJsContent.substring(endOfLastImportIndex);
    
    // Add middleware usage
    
    // 1. Add protection against middleware bypass
    serverJsContent = serverJsContent.replace(
      /(app\.use\(cors\(\{[\s\S]+?\}\)\));\n/,
      '$1;\n\n// Apply protection against Next.js middleware bypass vulnerability (CVE-2025-29927)\napp.use(protectAgainstMiddlewareBypass);\n'
    );
    
    // 2. Add additional security middleware for API routes
    serverJsContent = serverJsContent.replace(
      /(app\.use\('\/api'[\s\S]+?next\(\);[\s\S]+?\});\n/,
      '$1;\n\n// Additional security enhancements for sensitive endpoints\napp.use(\'/api\', sensitiveEndpointRateLimiting);\napp.use(\'/api\', sanitizeInputParameters);\n\n// Add secondary authorization validation as defense-in-depth\napp.use(\'/api/admin\', additionalAuthorizationValidation);\napp.use(\'/api/user\', additionalAuthorizationValidation);\n'
    );
    
    // Write the updated server.js file
    fs.writeFileSync(serverJsPath, serverJsContent);
    console.log(`${colors.green}Successfully updated server.js to use new security middleware.${colors.reset}`);
  } else {
    console.log(`${colors.yellow}server.js already imports middlewareSecurity.js. No changes made.${colors.reset}`);
  }
}

/**
 * Install dependencies
 */
function installDependencies() {
  console.log(`${colors.bright}Installing dependencies...${colors.reset}`);
  
  try {
    // Using --legacy-peer-deps to avoid peer dependency issues
    execSync('npm install --legacy-peer-deps', { cwd: rootDir, stdio: 'inherit' });
    console.log(`${colors.green}Successfully installed dependencies.${colors.reset}`);
  } catch (error) {
    console.warn(`${colors.yellow}Warning: Failed to install dependencies. You may need to run npm install manually.${colors.reset}`);
  }
} 