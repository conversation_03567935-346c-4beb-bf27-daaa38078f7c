-- Run this in the Supabase SQL Editor to complete the table structure

-- Add all missing columns from the expected schema
ALTER TABLE dental_articles ADD COLUMN IF NOT EXISTS source_url TEXT DEFAULT NULL;
ALTER TABLE dental_articles ADD COLUMN IF NOT EXISTS link TEXT DEFAULT NULL;
ALTER TABLE dental_articles ADD COLUMN IF NOT EXISTS pub_date TIMESTAMP WITH TIME ZONE DEFAULT NULL;
ALTER TABLE dental_articles ADD COLUMN IF NOT EXISTS inserted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
ALTER TABLE dental_articles ADD COLUMN IF NOT EXISTS authors TEXT DEFAULT NULL;
ALTER TABLE dental_articles ADD COLUMN IF NOT EXISTS image_url TEXT DEFAULT NULL;
ALTER TABLE dental_articles ADD COLUMN IF NOT EXISTS is_published BOOLEAN DEFAULT TRUE;
ALTER TABLE dental_articles ADD COLUMN IF NOT EXISTS view_count INTEGER DEFAULT 0;
ALTER TABLE dental_articles ADD COLUMN IF NOT EXISTS quality_score FLOAT DEFAULT 0.0;

-- Map existing columns to expected columns
UPDATE dental_articles 
SET 
  inserted_at = created_at,
  authors = NULL,
  pub_date = publication_date;

-- Refresh the schema cache
NOTIFY pgrst, 'reload schema';

-- Display current structure
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'dental_articles' 
ORDER BY ordinal_position; 