import os
import random
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from PIL import Image
import numpy as np

def visualize_sample_images(dataset_path, num_samples=5):
    """
    Visualize sample images from the dataset with their annotations.
    
    Args:
        dataset_path: Path to the dataset
        num_samples: Number of samples to visualize
    """
    # Choose a random split (train, test, or valid)
    split_options = ['train', 'test', 'valid']
    split = random.choice(split_options)
    split_path = os.path.join(dataset_path, split)
    
    # Load annotations
    annotations_file = os.path.join(split_path, '_annotations.csv')
    annotations = pd.read_csv(annotations_file)
    
    # Get unique filenames
    unique_files = annotations['filename'].unique()
    
    # Randomly select files to visualize
    files_to_visualize = random.sample(list(unique_files), min(num_samples, len(unique_files)))
    
    # Create a figure with subplots
    fig, axes = plt.subplots(1, len(files_to_visualize), figsize=(5*len(files_to_visualize), 5))
    if len(files_to_visualize) == 1:
        axes = [axes]
    
    # Define colors for different classes
    class_colors = {
        'Implant': 'red',
        'Caries': 'yellow',
        'Fillings': 'blue',
        'RootCanal': 'green',
        'Periapical': 'purple',
        'Crown': 'orange',
        'Impacted': 'cyan'
    }
    
    # Plot each image with annotations
    for i, filename in enumerate(files_to_visualize):
        # Load image
        img_path = os.path.join(split_path, filename)
        img = Image.open(img_path)
        
        # Get annotations for this image
        img_annotations = annotations[annotations['filename'] == filename]
        
        # Display image
        axes[i].imshow(img, cmap='gray')
        axes[i].set_title(f"Filename: {filename}\nSplit: {split}")
        
        # Add bounding boxes
        for _, row in img_annotations.iterrows():
            cls = row['class']
            xmin, ymin, xmax, ymax = row['xmin'], row['ymin'], row['xmax'], row['ymax']
            
            rect = patches.Rectangle(
                (xmin, ymin), xmax - xmin, ymax - ymin,
                linewidth=2, edgecolor=class_colors.get(cls, 'white'),
                facecolor='none', label=cls
            )
            axes[i].add_patch(rect)
            axes[i].text(xmin, ymin-5, cls, color=class_colors.get(cls, 'white'), fontsize=10, 
                         bbox=dict(facecolor='black', alpha=0.5))
        
        axes[i].axis('off')
    
    plt.tight_layout()
    plt.savefig('sample_dental_images.png', dpi=300)
    print(f"Visualization saved as sample_dental_images.png")
    plt.show()

if __name__ == "__main__":
    # Get dataset path from the download script result
    dataset_path = "/Users/<USER>/.cache/kagglehub/datasets/imtkaggleteam/dental-radiography/versions/1"
    
    if not os.path.exists(dataset_path):
        print("Dataset not found. Please run download_dental_radiography.py first.")
    else:
        print(f"Visualizing samples from {dataset_path}")
        visualize_sample_images(dataset_path) 