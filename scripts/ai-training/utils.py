"""
Utility functions for dental radiography AI training.
"""

import os
import sys
import random
import numpy as np
import torch
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from pathlib import Path
import json
from datetime import datetime
import shutil
import cv2

import config


def set_seeds(seed=42):
    """
    Set random seeds for reproducibility.
    
    Args:
        seed: Random seed
    """
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)  # For multi-GPU
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    os.environ['PYTHONHASHSEED'] = str(seed)
    
    print(f"Random seed set to {seed}")


def log_hyperparameters(args, model, logger):
    """
    Log hyperparameters to experiment tracking system.
    
    Args:
        args: Command-line arguments
        model: PyTorch Lightning module
        logger: Logger (e.g., WandB logger)
    """
    # Convert args to dict
    hparams = {k: v for k, v in vars(args).items()}
    
    # Add model hyperparameters
    hparams.update(model.hparams)
    
    # Log hyperparameters
    logger.log_hyperparams(hparams)


def export_model(model, output_path, format="torchscript"):
    """
    Export trained model to various formats.
    
    Args:
        model: Trained PyTorch Lightning module
        output_path: Path to save the exported model
        format: Export format (torchscript, onnx)
    
    Returns:
        Path to the exported model
    """
    # Create directory if it doesn't exist
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # Get the PyTorch model from the Lightning module
    pytorch_model = model.model
    pytorch_model.eval()
    
    # Create a dummy input tensor
    dummy_input = torch.rand(1, 3, config.IMAGE_SIZE[0], config.IMAGE_SIZE[1])
    
    if format == "torchscript":
        # Export to TorchScript
        try:
            script_model = torch.jit.trace(pytorch_model, dummy_input)
            script_model.save(str(output_path))
            print(f"Model exported to TorchScript format: {output_path}")
        except Exception as e:
            print(f"Error exporting to TorchScript: {e}")
            # Fallback to saving the PyTorch model
            torch.save(pytorch_model.state_dict(), str(output_path))
            print(f"Saved PyTorch model state dict instead: {output_path}")
    
    elif format == "onnx":
        # Export to ONNX
        try:
            import onnx
            import onnxruntime
            
            # Export PyTorch model to ONNX
            torch.onnx.export(
                pytorch_model,
                dummy_input,
                str(output_path),
                export_params=True,
                opset_version=11,
                do_constant_folding=True,
                input_names=["input"],
                output_names=["output"],
                dynamic_axes={"input": {0: "batch_size"}, "output": {0: "batch_size"}}
            )
            
            # Verify the model
            onnx_model = onnx.load(str(output_path))
            onnx.checker.check_model(onnx_model)
            print(f"Model exported to ONNX format: {output_path}")
            
            # Verify with ONNX Runtime
            ort_session = onnxruntime.InferenceSession(str(output_path))
            ort_inputs = {ort_session.get_inputs()[0].name: dummy_input.numpy()}
            ort_session.run(None, ort_inputs)
            print("ONNX model verified with ONNX Runtime")
            
        except Exception as e:
            print(f"Error exporting to ONNX: {e}")
            print("Make sure you have onnx and onnxruntime installed:")
            print("pip install onnx onnxruntime")
            # Fallback to saving the PyTorch model
            torch.save(pytorch_model.state_dict(), str(output_path))
            print(f"Saved PyTorch model state dict instead: {output_path}")
    
    else:
        # Save PyTorch model
        torch.save(pytorch_model.state_dict(), str(output_path))
        print(f"Model saved in PyTorch format: {output_path}")
    
    return output_path


def create_inference_model(model):
    """
    Create a simplified model for inference.
    
    Args:
        model: Trained PyTorch Lightning module
    
    Returns:
        Dictionary with model and preprocessing details
    """
    # Extract the PyTorch model
    pytorch_model = model.model
    pytorch_model.eval()
    
    # Create a dictionary with model and preprocessing details
    inference_model = {
        "model": pytorch_model,
        "config": {
            "image_size": config.IMAGE_SIZE,
            "normalize_mean": config.NORMALIZE_MEAN,
            "normalize_std": config.NORMALIZE_STD,
            "classes": config.CLASSES,
            "class_to_idx": config.CLASS_TO_IDX,
            "date_created": datetime.now().isoformat(),
            "backbone": model.backbone_name,
            "pretrained": model.hparams.get("pretrained", True)
        }
    }
    
    return inference_model


def load_inference_model(model_path):
    """
    Load a model for inference.
    
    Args:
        model_path: Path to the saved model
    
    Returns:
        Loaded model
    """
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    if str(model_path).endswith('.pt'):
        # Load PyTorch model
        try:
            # First try with weights_only=False for PyTorch 2.6 compatibility
            inference_model = torch.load(model_path, map_location=device, weights_only=False)
            # If this is a raw model (not our custom format)
            if not isinstance(inference_model, dict):
                # Create a compatible inference model structure
                print("Converting pretrained model to inference format...")
                model = inference_model
                model_config = {
                    "backbone": "resnet50",  # Default for pretrained model
                    "num_classes": 91,       # COCO dataset classes
                    "class_names": None      # Will be handled by the visualization code
                }
                return model, model_config
            
            model = inference_model["model"]
            model_config = inference_model["config"]
            print(f"Loaded PyTorch model from {model_path}")
            print(f"Model config: {model_config}")
        except Exception as e:
            print(f"Error loading model: {e}")
            print("Trying alternative loading method...")
            # If we use a pretrained model directly
            inference_model = torch.load(model_path, map_location=device)
            model = inference_model
            model_config = {
                "backbone": "resnet50",  # Default for pretrained model
                "num_classes": 91,       # COCO dataset classes
                "class_names": None      # Will be handled by the visualization code
            }
            print(f"Loaded model using alternative method")
    
    elif str(model_path).endswith('.onnx'):
        # Load ONNX model
        try:
            import onnxruntime
            model = onnxruntime.InferenceSession(str(model_path))
            model_config = {"format": "onnx"}
            print(f"Loaded ONNX model from {model_path}")
        except ImportError:
            print("Error: onnxruntime is required to load ONNX models")
            print("Install with: pip install onnxruntime")
            return None
    
    else:
        print(f"Unsupported model format: {model_path}")
        return None
    
    return model, model_config


def predict_image(model, image_path, confidence_threshold=0.5):
    """
    Run inference on a single image.
    
    Args:
        model: Loaded model
        image_path: Path to the image
        confidence_threshold: Confidence threshold for predictions
    
    Returns:
        Predictions (boxes, labels, scores)
    """
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model.to(device)
    model.eval()
    
    # Load and preprocess image
    image = cv2.imread(str(image_path))
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    # Resize image
    image = cv2.resize(image, (config.IMAGE_SIZE[1], config.IMAGE_SIZE[0]))
    
    # Convert to tensor and normalize
    image_tensor = torch.tensor(image, dtype=torch.float32) / 255.0
    image_tensor = image_tensor.permute(2, 0, 1)
    
    # Normalize
    mean = torch.tensor(config.NORMALIZE_MEAN).view(3, 1, 1)
    std = torch.tensor(config.NORMALIZE_STD).view(3, 1, 1)
    image_tensor = (image_tensor - mean) / std
    
    # Add batch dimension
    image_tensor = image_tensor.unsqueeze(0).to(device)
    
    # Run inference
    with torch.no_grad():
        predictions = model(image_tensor)
    
    # Extract results
    if isinstance(predictions, list):
        # This is the case with Faster R-CNN which returns a list of dicts
        predictions = predictions[0]
    
    # Get boxes, labels, and scores
    boxes = predictions["boxes"].cpu().numpy()
    labels = predictions["labels"].cpu().numpy()
    scores = predictions["scores"].cpu().numpy()
    
    # Filter by confidence threshold
    keep = scores >= confidence_threshold
    boxes = boxes[keep]
    labels = labels[keep]
    scores = scores[keep]
    
    return boxes, labels, scores, image


def visualize_predictions(image, boxes, labels, scores, output_path=None):
    """
    Visualize predictions on an image.
    
    Args:
        image: Input image (numpy array)
        boxes: Predicted bounding boxes
        labels: Predicted class labels
        scores: Prediction scores
        output_path: Path to save the visualization
    
    Returns:
        Matplotlib figure
    """
    # Create figure and axes
    fig, ax = plt.subplots(1, figsize=(12, 12))
    ax.imshow(image)
    
    # Draw bounding boxes
    for box, label_idx, score in zip(boxes, labels, scores):
        # Get coordinates
        x_min, y_min, x_max, y_max = box
        
        # Get class name
        if label_idx == 0:
            class_name = "background"
        else:
            class_name = list(config.CLASS_TO_IDX.keys())[list(config.CLASS_TO_IDX.values()).index(label_idx - 1)]
        
        # Create rectangle patch
        rect = patches.Rectangle(
            (x_min, y_min), x_max - x_min, y_max - y_min,
            linewidth=2, edgecolor='red', facecolor='none'
        )
        ax.add_patch(rect)
        
        # Add label
        ax.text(
            x_min, y_min - 5, f"{class_name} ({score:.2f})",
            bbox=dict(facecolor='red', alpha=0.5), fontsize=12, color='white'
        )
    
    plt.axis('off')
    plt.tight_layout()
    
    # Save if output path is provided
    if output_path:
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"Visualization saved to {output_path}")
    
    return fig


def create_training_report(trainer, model, data_module, results_dir):
    """
    Create a training report with metrics and visualizations.
    
    Args:
        trainer: PyTorch Lightning trainer
        model: Trained model
        data_module: Data module
        results_dir: Directory to save the report
    """
    # Create report directory
    report_dir = Path(results_dir) / "report"
    os.makedirs(report_dir, exist_ok=True)
    
    # Get test metrics
    test_metrics = trainer.test(model, data_module)[0]
    
    # Save metrics to JSON
    with open(report_dir / "metrics.json", "w") as f:
        json.dump(test_metrics, f, indent=4)
    
    # Save model summary
    with open(report_dir / "model_summary.txt", "w") as f:
        # Redirect stdout to file
        orig_stdout = sys.stdout
        sys.stdout = f
        
        # Print model summary
        print(f"Model: {model.__class__.__name__}")
        print(f"Backbone: {model.backbone_name}")
        print(f"Number of classes: {model.num_classes}")
        print(f"Number of parameters: {sum(p.numel() for p in model.parameters())}")
        print(f"Number of trainable parameters: {sum(p.numel() for p in model.parameters() if p.requires_grad)}")
        
        # Restore stdout
        sys.stdout = orig_stdout
    
    # Create visualizations
    create_visualizations(model, data_module, report_dir)
    
    print(f"Training report saved to {report_dir}")


def create_visualizations(model, data_module, output_dir):
    """
    Create visualizations from the test dataset.
    
    Args:
        model: Trained model
        data_module: Data module
        output_dir: Directory to save the visualizations
    """
    # Create visualizations directory
    vis_dir = Path(output_dir) / "visualizations"
    os.makedirs(vis_dir, exist_ok=True)
    
    # Get test dataloader
    test_loader = data_module.test_dataloader()
    
    # Get device
    device = next(model.parameters()).device
    
    # Set model to eval mode
    model.eval()
    
    # Get a batch of test data
    batch = next(iter(test_loader))
    images, targets = batch
    
    # Run inference on batch
    with torch.no_grad():
        model.model.eval()
        predictions = model.model(images.to(device))
    
    # Visualize a few test examples
    num_examples = min(5, len(images))
    
    for i in range(num_examples):
        image = images[i].permute(1, 2, 0).cpu().numpy()
        
        # Denormalize
        mean = np.array(config.NORMALIZE_MEAN)
        std = np.array(config.NORMALIZE_STD)
        image = std * image + mean
        image = np.clip(image, 0, 1)
        image = (image * 255).astype(np.uint8)
        
        # Get ground truth
        gt_boxes = targets[i]["boxes"].cpu().numpy()
        gt_labels = targets[i]["labels"].cpu().numpy()
        
        # Get predictions
        pred = predictions[i]
        pred_boxes = pred["boxes"].cpu().numpy()
        pred_labels = pred["labels"].cpu().numpy()
        pred_scores = pred["scores"].cpu().numpy()
        
        # Filter predictions by score
        score_threshold = 0.5
        keep = pred_scores >= score_threshold
        pred_boxes = pred_boxes[keep]
        pred_labels = pred_labels[keep]
        pred_scores = pred_scores[keep]
        
        # Create figure with two subplots (GT and predictions)
        fig, axes = plt.subplots(1, 2, figsize=(20, 10))
        
        # Plot ground truth
        axes[0].imshow(image)
        axes[0].set_title("Ground Truth", fontsize=14)
        
        for box, label_idx in zip(gt_boxes, gt_labels):
            x_min, y_min, x_max, y_max = box
            rect = patches.Rectangle(
                (x_min, y_min), x_max - x_min, y_max - y_min,
                linewidth=2, edgecolor='green', facecolor='none'
            )
            axes[0].add_patch(rect)
            
            # Get class name
            class_name = list(config.CLASS_TO_IDX.keys())[list(config.CLASS_TO_IDX.values()).index(label_idx)]
            axes[0].text(
                x_min, y_min - 5, class_name,
                bbox=dict(facecolor='green', alpha=0.5), fontsize=10, color='white'
            )
        
        # Plot predictions
        axes[1].imshow(image)
        axes[1].set_title("Predictions", fontsize=14)
        
        for box, label_idx, score in zip(pred_boxes, pred_labels, pred_scores):
            x_min, y_min, x_max, y_max = box
            rect = patches.Rectangle(
                (x_min, y_min), x_max - x_min, y_max - y_min,
                linewidth=2, edgecolor='red', facecolor='none'
            )
            axes[1].add_patch(rect)
            
            # Get class name
            if label_idx == 0:
                class_name = "background"
            else:
                class_name = list(config.CLASS_TO_IDX.keys())[list(config.CLASS_TO_IDX.values()).index(label_idx - 1)]
            
            axes[1].text(
                x_min, y_min - 5, f"{class_name} ({score:.2f})",
                bbox=dict(facecolor='red', alpha=0.5), fontsize=10, color='white'
            )
        
        for ax in axes:
            ax.axis('off')
        
        plt.tight_layout()
        plt.savefig(vis_dir / f"example_{i}.png", dpi=300, bbox_inches='tight')
        plt.close(fig)
    
    print(f"Visualizations saved to {vis_dir}")


def backup_code(output_dir):
    """
    Backup all Python code to the output directory.
    
    Args:
        output_dir: Directory to save the code backup
    """
    # Create backup directory
    backup_dir = Path(output_dir) / "code_backup"
    os.makedirs(backup_dir, exist_ok=True)
    
    # Find all Python files in the current directory
    py_files = list(Path('.').glob('*.py'))
    
    # Copy files to backup directory
    for py_file in py_files:
        shutil.copy2(py_file, backup_dir)
    
    print(f"Code backup saved to {backup_dir}")


def get_flops_and_params(model):
    """
    Calculate FLOPs and parameters of a model.
    
    Args:
        model: PyTorch model
    
    Returns:
        Dictionary with FLOPs and parameters
    """
    # Try to import ptflops
    try:
        from ptflops import get_model_complexity_info
        
        # Get FLOPs and parameters
        macs, params = get_model_complexity_info(
            model,
            tuple(config.IMAGE_SIZE),
            as_strings=True,
            print_per_layer_stat=False,
            verbose=False
        )
        
        return {
            "flops": macs,
            "params": params
        }
    
    except ImportError:
        print("ptflops not installed. Install with: pip install ptflops")
        
        # Calculate parameters manually
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        return {
            "flops": "N/A",
            "params": f"{total_params / 1e6:.2f}M",
            "trainable_params": f"{trainable_params / 1e6:.2f}M"
        } 