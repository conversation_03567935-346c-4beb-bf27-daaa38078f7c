"""
Data module for the dental radiography dataset.
Handles loading, preprocessing, augmentation, and batching of dental images.
"""

import os
import cv2
import numpy as np
import pandas as pd
import torch
from PIL import Image
from torch.utils.data import Dataset, DataLoader
import albumentations as A
from albumentations.pytorch import ToTensorV2
import pytorch_lightning as pl
from sklearn.model_selection import train_test_split
from collections import Counter
import matplotlib.pyplot as plt
import matplotlib.patches as patches

import config


class DentalRadiographyDataset(Dataset):
    """Dataset class for dental radiography images with bounding box annotations"""
    
    def __init__(self, data_dir, annotation_file, transform=None, mode="train"):
        self.data_dir = data_dir
        self.transform = transform
        self.mode = mode
        
        # Load annotations
        self.annotations = pd.read_csv(os.path.join(data_dir, annotation_file))
        
        # Get unique filenames
        self.unique_filenames = self.annotations['filename'].unique()
        
        # Track class distribution
        self.class_counts = Counter(self.annotations['class'])
        print(f"Class distribution in {mode} set: {dict(self.class_counts)}")
    
    def __len__(self):
        return len(self.unique_filenames)
    
    def __getitem__(self, idx):
        # Get image filename
        filename = self.unique_filenames[idx]
        img_path = os.path.join(self.data_dir, filename)
        
        # Get annotations for this image
        img_anns = self.annotations[self.annotations['filename'] == filename]
        
        # Load image
        image = cv2.imread(img_path)
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # Get bounding boxes and class labels
        boxes = []
        labels = []
        
        for _, row in img_anns.iterrows():
            # Get bounding box coordinates
            x_min = row['xmin']
            y_min = row['ymin']
            x_max = row['xmax']
            y_max = row['ymax']
            
            # Normalize bbox coordinates if transform will resize the image
            # This ensures coordinates are properly scaled when transforms are applied
            if self.transform:
                orig_height, orig_width = image.shape[:2]
                # Store coordinates in Pascal VOC format [x_min, y_min, x_max, y_max]
                boxes.append([x_min, y_min, x_max, y_max])
            else:
                boxes.append([x_min, y_min, x_max, y_max])
            
            # Get class label
            class_name = row['class']
            label_idx = config.CLASS_TO_IDX[class_name]
            labels.append(label_idx)
        
        # Convert to numpy arrays
        boxes = np.array(boxes, dtype=np.float32)
        labels = np.array(labels, dtype=np.int64)
        
        # Create target dict
        target = {
            'boxes': boxes,
            'labels': labels,
            'image_id': torch.tensor([idx]),
            'area': (boxes[:, 3] - boxes[:, 1]) * (boxes[:, 2] - boxes[:, 0]),
            'iscrowd': torch.zeros((len(boxes),), dtype=torch.int64)
        }
        
        # Apply transformations
        if self.transform:
            # Albumentations supports bounding box transformations
            transformed = self.transform(image=image, bboxes=boxes, labels=labels)
            image = transformed['image']
            
            # Update transformed boxes and labels
            boxes = torch.tensor(transformed['bboxes'], dtype=torch.float32)
            labels = torch.tensor(transformed['labels'], dtype=torch.int64)
            
            # Update target with transformed data
            if len(boxes) > 0:
                target['boxes'] = boxes
                target['labels'] = labels
                target['area'] = (boxes[:, 3] - boxes[:, 1]) * (boxes[:, 2] - boxes[:, 0])
        else:
            # Convert image to tensor
            image = torch.tensor(image, dtype=torch.float32).permute(2, 0, 1)
            target['boxes'] = torch.tensor(boxes, dtype=torch.float32)
            target['labels'] = torch.tensor(labels, dtype=torch.int64)
        
        return image, target
    
    def visualize_sample(self, idx, figsize=(12, 12)):
        """Visualize a sample from the dataset with bounding boxes"""
        image, target = self[idx]
        
        # Convert image tensor to numpy for visualization
        if isinstance(image, torch.Tensor):
            image = image.permute(1, 2, 0).numpy()
            # Denormalize if necessary
            if image.max() <= 1.0:
                image = (image * 255).astype(np.uint8)
        
        # Create figure and axes
        fig, ax = plt.subplots(1, figsize=figsize)
        ax.imshow(image)
        
        # Draw bounding boxes
        boxes = target['boxes']
        labels = target['labels']
        
        for i, (box, label_idx) in enumerate(zip(boxes, labels)):
            # Get coordinates
            x_min, y_min, x_max, y_max = box
            
            # Get class name
            class_name = list(config.CLASS_TO_IDX.keys())[list(config.CLASS_TO_IDX.values()).index(label_idx.item() if isinstance(label_idx, torch.Tensor) else label_idx)]
            
            # Create rectangle patch
            rect = patches.Rectangle(
                (x_min, y_min), x_max - x_min, y_max - y_min,
                linewidth=2, edgecolor='r', facecolor='none'
            )
            ax.add_patch(rect)
            
            # Add label
            ax.text(
                x_min, y_min - 5, class_name,
                bbox=dict(facecolor='red', alpha=0.5), fontsize=12, color='white'
            )
        
        plt.axis('off')
        plt.tight_layout()
        return fig


def get_transforms(aug_intensity='medium'):
    """
    Get data augmentation transforms based on the specified intensity
    """
    # Base normalization and conversion to tensor for all splits
    base_transform = [
        A.Normalize(mean=config.NORMALIZE_MEAN, std=config.NORMALIZE_STD),
        ToTensorV2()
    ]
    
    # No augmentations for validation and test
    val_transforms = A.Compose(
        [
            A.Resize(height=config.IMAGE_SIZE[0], width=config.IMAGE_SIZE[1]),
            *base_transform
        ],
        bbox_params=A.BboxParams(format='pascal_voc', label_fields=['labels'])
    )
    
    # Training augmentations based on intensity
    if aug_intensity == 'none':
        train_transforms = val_transforms
    else:
        # Light augmentations
        light_augs = [
            A.HorizontalFlip(p=0.5),
            A.RandomBrightnessContrast(p=0.2),
        ]
        
        # Medium augmentations (light + more)
        medium_augs = light_augs + [
            A.ShiftScaleRotate(shift_limit=0.05, scale_limit=0.1, rotate_limit=10, p=0.3),
            A.RGBShift(r_shift_limit=15, g_shift_limit=15, b_shift_limit=15, p=0.3),
            A.GaussNoise(var_limit=(10.0, 50.0), p=0.2),
        ]
        
        # Heavy augmentations (medium + more)
        heavy_augs = medium_augs + [
            A.RandomGamma(gamma_limit=(80, 120), p=0.2),
            A.GaussianBlur(blur_limit=(3, 5), p=0.2),
            A.GridDistortion(p=0.2),
            A.CoarseDropout(max_holes=8, max_height=32, max_width=32, p=0.2),
        ]
        
        aug_dict = {
            'light': light_augs,
            'medium': medium_augs,
            'heavy': heavy_augs
        }
        
        selected_augs = aug_dict.get(aug_intensity, medium_augs)
        
        train_transforms = A.Compose(
            [
                A.Resize(height=config.IMAGE_SIZE[0], width=config.IMAGE_SIZE[1]),
                *selected_augs,
                *base_transform
            ],
            bbox_params=A.BboxParams(format='pascal_voc', label_fields=['labels'])
        )
    
    return train_transforms, val_transforms


class DentalRadiographyDataModule(pl.LightningDataModule):
    """PyTorch Lightning data module for the dental radiography dataset"""
    
    def __init__(
        self,
        data_path=config.DATASET_PATH,
        train_dir=config.TRAIN_DIR,
        val_dir=config.VALID_DIR,
        test_dir=config.TEST_DIR,
        batch_size=config.BATCH_SIZE,
        num_workers=config.NUM_WORKERS,
        aug_intensity=config.AUG_INTENSITY
    ):
        super().__init__()
        self.data_path = data_path
        self.train_dir = train_dir
        self.val_dir = val_dir
        self.test_dir = test_dir
        self.batch_size = batch_size
        self.num_workers = num_workers
        self.aug_intensity = aug_intensity
        
        # Get transforms
        self.train_transforms, self.val_transforms = get_transforms(aug_intensity)
        
        # Class weights for handling imbalance
        self.class_weights = None
    
    def prepare_data(self):
        """
        Download or verify data. This method is called once per node.
        """
        # Dataset should already be downloaded, just verify it exists
        if not os.path.exists(self.data_path):
            raise FileNotFoundError(
                f"Dataset not found at {self.data_path}. "
                "Please run download_dental_radiography.py first."
            )
    
    def setup(self, stage=None):
        """
        Setup datasets. This method is called on every GPU in distributed training.
        """
        if stage == 'fit' or stage is None:
            # Create training dataset
            self.train_dataset = DentalRadiographyDataset(
                data_dir=self.train_dir,
                annotation_file="_annotations.csv",
                transform=self.train_transforms,
                mode="train"
            )
            
            # Create validation dataset
            self.val_dataset = DentalRadiographyDataset(
                data_dir=self.val_dir,
                annotation_file="_annotations.csv",
                transform=self.val_transforms,
                mode="val"
            )
            
            # Calculate class weights for imbalanced dataset
            if config.USE_CLASS_WEIGHTS:
                class_counts = self.train_dataset.class_counts
                total_samples = sum(class_counts.values())
                num_classes = len(class_counts)
                
                # Compute inverse frequency weights
                weights = {cls: total_samples / (num_classes * count) 
                          for cls, count in class_counts.items()}
                
                # Convert to tensor
                class_weights = torch.zeros(num_classes)
                for cls, idx in config.CLASS_TO_IDX.items():
                    class_weights[idx] = weights.get(cls, 1.0)
                
                self.class_weights = class_weights
                print(f"Class weights: {class_weights}")
        
        if stage == 'test' or stage is None:
            # Create test dataset
            self.test_dataset = DentalRadiographyDataset(
                data_dir=self.test_dir,
                annotation_file="_annotations.csv",
                transform=self.val_transforms,
                mode="test"
            )
    
    def train_dataloader(self):
        """Get train dataloader"""
        return DataLoader(
            self.train_dataset,
            batch_size=self.batch_size,
            shuffle=True,
            num_workers=self.num_workers,
            pin_memory=True,
            collate_fn=self.collate_fn
        )
    
    def val_dataloader(self):
        """Get validation dataloader"""
        return DataLoader(
            self.val_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=self.num_workers,
            pin_memory=True,
            collate_fn=self.collate_fn
        )
    
    def test_dataloader(self):
        """Get test dataloader"""
        return DataLoader(
            self.test_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=self.num_workers,
            pin_memory=True,
            collate_fn=self.collate_fn
        )
    
    def get_class_weights(self):
        """Get class weights for handling imbalance"""
        return self.class_weights
    
    @staticmethod
    def collate_fn(batch):
        """
        Custom collate function for batches with variable number of objects.
        Required for object detection tasks.
        """
        images = []
        targets = []
        
        for img, target in batch:
            images.append(img)
            targets.append(target)
        
        # Stack images into a batch
        images = torch.stack(images, dim=0)
        
        return images, targets 