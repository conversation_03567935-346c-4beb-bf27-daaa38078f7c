#!/usr/bin/env python3
"""
Dental Health Advisor

This script combines dental image analysis with nutritional advice
to provide comprehensive dental health recommendations.
"""

import os
import sys
import torch
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import argparse
import json
import cv2
from pathlib import Path
from PIL import Image
import warnings
warnings.filterwarnings("ignore")

def parse_args():
    parser = argparse.ArgumentParser(description="Dental Health Advisor")
    parser.add_argument("--detection_model", type=str, default=None,
                      help="Path to the dental radiography detection model")
    parser.add_argument("--classification_model", type=str, default=None,
                      help="Path to the cavity classification model") 
    parser.add_argument("--nutrition_model", type=str, default=None,
                      help="Path to the food cavity risk model")
    parser.add_argument("--cancer_model", type=str, default=None,
                      help="Path to the oral cancer detection model")
    parser.add_argument("--face_model", type=str, default=None,
                      help="Path to the facial analysis model")
    parser.add_argument("--thermal_model", type=str, default=None,
                      help="Path to the thermal analysis model")
    parser.add_argument("--dental_image", type=str, default=None,
                      help="Path to dental image for analysis")
    parser.add_argument("--oral_image", type=str, default=None,
                      help="Path to oral image for cancer analysis")
    parser.add_argument("--face_image", type=str, default=None,
                      help="Path to facial image for feature analysis")
    parser.add_argument("--thermal_image", type=str, default=None,
                      help="Path to thermal image for inflammation analysis")
    parser.add_argument("--nutrition_data", type=str, default=None,
                      help="Path to nutrition data CSV or food name")
    parser.add_argument("--output_dir", type=str, default="./dental_advisor_output",
                      help="Directory to save results")
    parser.add_argument("--detection_threshold", type=float, default=0.5,
                      help="Detection confidence threshold")
    parser.add_argument("--show", action="store_true", default=False,
                      help="Show visualizations instead of saving them")
    return parser.parse_args()

def import_functions():
    """Dynamically import functions from other scripts"""
    functions = {}
    
    # Try to import combined_predict functions
    try:
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        from combined_predict import load_detection_model, run_detection, visualize_detection_results
        functions["load_detection_model"] = load_detection_model
        functions["run_detection"] = run_detection
        functions["visualize_detection_results"] = visualize_detection_results
    except ImportError as e:
        print(f"Warning: Could not import detection functions: {e}")
    
    # Try to import food_cavity_predict functions
    try:
        from food_cavity_predict import load_model, load_input_data, predict_cavity_risk, visualize_predictions
        functions["load_nutrition_model"] = load_model
        functions["load_nutrition_data"] = load_input_data
        functions["predict_cavity_risk"] = predict_cavity_risk
        functions["visualize_nutrition_predictions"] = visualize_predictions
    except ImportError as e:
        print(f"Warning: Could not import nutrition functions: {e}")
    
    # Try to import thermal_predict functions
    try:
        from thermal_predict import load_model as load_thermal_model, process_image, make_prediction, visualize_prediction
        functions["load_thermal_model"] = load_thermal_model
        functions["process_thermal_image"] = process_image
        functions["predict_thermal"] = make_prediction
        functions["visualize_thermal_prediction"] = visualize_prediction
    except ImportError as e:
        print(f"Warning: Could not import thermal imaging functions: {e}")
    
    return functions

def process_dental_image(dental_image, detection_model, classification_model, threshold, output_dir, functions):
    """Process dental image with detection and classification models"""
    if not os.path.exists(dental_image):
        print(f"Dental image not found: {dental_image}")
        return None
    
    results = {"dental_image": dental_image}
    
    # Run detection if model is provided
    if detection_model and "load_detection_model" in functions:
        print("Running dental condition detection...")
        detection_model_obj = functions["load_detection_model"](detection_model)
        img = cv2.imread(dental_image)
        detections = functions["run_detection"](img, detection_model_obj, threshold)
        
        if detections:
            results["detections"] = detections
            
            # Save detection visualization
            detection_img = functions["visualize_detection_results"](img.copy(), detections)
            detection_path = os.path.join(output_dir, "detection_results.jpg")
            cv2.imwrite(detection_path, detection_img)
            results["detection_visualization"] = detection_path
            
            # Count conditions
            condition_counts = {}
            for detection in detections:
                condition = detection["label"]
                condition_counts[condition] = condition_counts.get(condition, 0) + 1
            results["condition_counts"] = condition_counts
    
    return results

def process_nutrition_data(nutrition_data, nutrition_model, output_dir, functions):
    """Process nutrition data with the food cavity risk model"""
    if not nutrition_data:
        return None
    
    results = {"nutrition_data": nutrition_data}
    
    # Run nutrition analysis if model is provided
    if nutrition_model and "load_nutrition_model" in functions:
        print("Running food cavity risk analysis...")
        
        # Set device
        device = torch.device("cuda" if torch.cuda.is_available() else 
                             "mps" if torch.backends.mps.is_available() else 
                             "cpu")
        
        # Load model
        model, feature_names, class_names, scaler = functions["load_nutrition_model"](nutrition_model, device)
        
        # Load input data
        X, food_names = functions["load_nutrition_data"](nutrition_data, feature_names)
        
        # Make predictions
        nutrition_results = functions["predict_cavity_risk"](model, X, scaler, device, feature_names, class_names)
        
        # Create visualizations
        functions["visualize_nutrition_predictions"](nutrition_results, food_names, output_dir, False)
        
        # Add results
        results["nutrition_results"] = []
        for food, result in zip(food_names, nutrition_results):
            results["nutrition_results"].append({
                "food": food,
                "risk_level": result["risk_name"],
                "high_risk_probability": result["probabilities"]["High Risk"]
            })
        
        # Count high risk foods
        high_risk_foods = [food for food, result in zip(food_names, nutrition_results) 
                         if result["risk_name"] == "High Risk"]
        results["high_risk_foods"] = high_risk_foods
        results["high_risk_count"] = len(high_risk_foods)
    
    return results

def process_thermal_image(thermal_image, thermal_model, output_dir, functions):
    """Process thermal image with the thermal analysis model"""
    if not thermal_image or not os.path.exists(thermal_image):
        print(f"Thermal image not found: {thermal_image}")
        return None
    
    results = {"thermal_image": thermal_image}
    
    # Run thermal analysis if model is provided
    if thermal_model and "load_thermal_model" in functions:
        print("Running thermal imaging analysis...")
        
        # Set device
        device = torch.device("cuda" if torch.cuda.is_available() else 
                             "mps" if torch.backends.mps.is_available() else 
                             "cpu")
        
        # Load model
        model = functions["load_thermal_model"](thermal_model, device)
        
        # Process image
        image, input_tensor, features = functions["process_thermal_image"](thermal_image, 256)
        
        # Make prediction
        threshold = 0.5
        result = functions["predict_thermal"](model, input_tensor, features, device, threshold)
        
        # Visualize prediction
        visualization_path = os.path.join(output_dir, "thermal_analysis.jpg")
        functions["visualize_thermal_prediction"](image, result, visualization_path, False, True)
        
        # Add results
        results["inflammation_detected"] = result["predicted_class"] == "Abnormal"
        results["severity"] = result["severity"]
        results["temperature_features"] = {
            "max_temp": result["features"]["max_temp"],
            "min_temp": result["features"]["min_temp"],
            "avg_temp": result["features"]["avg_temp"],
            "temp_range": result["features"]["temp_range"],
            "hotspot_count": result["features"]["hotspot_count"]
        }
        results["visualization_path"] = visualization_path
    
    return results

def generate_recommendations(dental_results, nutrition_results, cancer_results, face_results, thermal_results, output_path):
    """Generate comprehensive dental health recommendations"""
    recommendations = []
    risk_level = "low"
    
    # Analyze dental results
    if dental_results and "condition_counts" in dental_results:
        condition_counts = dental_results["condition_counts"]
        
        if "cavity" in condition_counts and condition_counts["cavity"] > 0:
            recommendations.append(f"⚠️ {condition_counts['cavity']} cavities detected. Immediate dental visit recommended.")
            risk_level = "high"
        
        if "impacted_tooth" in condition_counts and condition_counts["impacted_tooth"] > 0:
            recommendations.append(f"⚠️ {condition_counts['impacted_tooth']} impacted teeth detected. Consultation with oral surgeon recommended.")
            risk_level = "high"
            
        if "filling" in condition_counts and condition_counts["filling"] > 0:
            recommendations.append(f"ℹ️ {condition_counts['filling']} dental fillings detected. Regular check-ups to monitor condition.")
            risk_level = max(risk_level, "medium")
            
        if "implant" in condition_counts and condition_counts["implant"] > 0:
            recommendations.append(f"ℹ️ {condition_counts['implant']} dental implants detected. Continue proper oral hygiene.")
            risk_level = max(risk_level, "medium")
    
    # Analyze nutrition results
    if nutrition_results and "high_risk_foods" in nutrition_results:
        high_risk_foods = nutrition_results["high_risk_foods"]
        
        if high_risk_foods:
            recommendations.append(f"⚠️ {len(high_risk_foods)} high-risk foods detected in your diet.")
            recommendations.append("Consider reducing consumption of: " + ", ".join(high_risk_foods[:5]))
            
            if len(high_risk_foods) > 3:
                risk_level = max(risk_level, "medium")
            if len(high_risk_foods) > 5:
                risk_level = "high"
                
        # Add general nutrition advice
        recommendations.append("💡 Dietary recommendations:")
        recommendations.append("- Limit sugary foods and drinks")
        recommendations.append("- Choose water over acidic or sugary beverages")
        recommendations.append("- Include calcium-rich foods for tooth strength")
    
    # Analyze thermal results
    if thermal_results and "inflammation_detected" in thermal_results:
        if thermal_results["inflammation_detected"]:
            severity = thermal_results["severity"]
            if severity == "HIGH":
                recommendations.append("⚠️ Significant thermal abnormalities detected, indicating potential serious inflammation.")
                recommendations.append("   Immediate dental consultation recommended.")
                risk_level = "high"
            elif severity == "MEDIUM":
                recommendations.append("⚠️ Moderate thermal abnormalities detected, suggesting inflammation or infection.")
                recommendations.append("   Dental evaluation recommended within 1-2 weeks.")
                risk_level = max(risk_level, "medium")
            else:
                recommendations.append("ℹ️ Minor thermal abnormalities detected. Monitor for changes in symptoms.")
                risk_level = max(risk_level, "low")
            
            # Add specific recommendations based on temperature features
            if "temperature_features" in thermal_results:
                features = thermal_results["temperature_features"]
                if features["hotspot_count"] > 2:
                    recommendations.append("   - Multiple hotspots detected, suggesting multiple areas of inflammation.")
                if features["temp_range"] > 2.0:
                    recommendations.append("   - Large temperature variation detected, which may indicate acute inflammation.")
        else:
            recommendations.append("✅ No significant thermal abnormalities detected.")
    
    # Add general dental health recommendations
    recommendations.append("\n💡 General dental health recommendations:")
    recommendations.append("- Brush teeth twice daily with fluoride toothpaste")
    recommendations.append("- Floss daily to remove food particles between teeth")
    recommendations.append("- Use an antimicrobial mouthwash")
    recommendations.append("- Schedule regular dental check-ups every 6 months")
    
    # Determine overall risk level
    risk_descriptions = {
        "low": "Your overall dental health risk appears to be LOW based on the available information.",
        "medium": "Your overall dental health risk appears to be MEDIUM. Some areas need attention.",
        "high": "Your overall dental health risk appears to be HIGH. Prompt dental care is recommended."
    }
    
    # Write recommendations to file
    with open(output_path, "w") as f:
        f.write("# Dental Health Advisor Recommendations\n\n")
        f.write(f"## Overall Assessment: {risk_descriptions[risk_level]}\n\n")
        
        for rec in recommendations:
            f.write(f"{rec}\n")
    
    return {
        "risk_level": risk_level,
        "recommendations": recommendations
    }

def create_summary_report(dental_results, nutrition_results, cancer_results, face_results, thermal_results, recommendation_results, output_dir):
    """Create a visual summary report combining all analyses"""
    plt.figure(figsize=(12, 10))
    plt.subplots_adjust(hspace=0.4)
    
    # Title
    plt.suptitle("Dental Health Advisor - Summary Report", fontsize=18, y=0.98)
    
    # Risk level color
    risk_colors = {"low": "green", "medium": "orange", "high": "red"}
    risk_level = recommendation_results["risk_level"]
    
    # Add risk level indicator
    ax1 = plt.subplot(3, 2, 1)
    ax1.axis('off')
    ax1.text(0.5, 0.5, f"Overall Risk Level: {risk_level.upper()}", 
             fontsize=16, ha='center', va='center',
             bbox=dict(boxstyle="round,pad=0.5", facecolor=risk_colors[risk_level], alpha=0.3))
    
    # Dental conditions (if available)
    ax2 = plt.subplot(3, 2, 2)
    if dental_results and "condition_counts" in dental_results:
        conditions = list(dental_results["condition_counts"].keys())
        counts = list(dental_results["condition_counts"].values())
        
        colors = ['red' if c == 'cavity' else 'orange' if c == 'impacted_tooth' else 'blue' 
                 for c in conditions]
        
        ax2.bar(conditions, counts, color=colors)
        ax2.set_title("Detected Dental Conditions")
        ax2.set_ylabel("Count")
        plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45, ha='right')
    else:
        ax2.axis('off')
        ax2.text(0.5, 0.5, "No dental image analysis available", 
                ha='center', va='center', fontsize=12)
    
    # Nutrition risk (if available)
    ax3 = plt.subplot(3, 2, 3)
    if nutrition_results and "nutrition_results" in nutrition_results:
        results = nutrition_results["nutrition_results"]
        
        # Get risk counts
        risk_counts = {"Low Risk": 0, "Medium Risk": 0, "High Risk": 0}
        for item in results:
            risk_level = item["risk_level"]
            risk_counts[risk_level] += 1
        
        labels = list(risk_counts.keys())
        sizes = list(risk_counts.values())
        colors = ['green', 'orange', 'red']
        
        if sum(sizes) > 0:
            ax3.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%',
                   startangle=90)
            ax3.axis('equal')
            ax3.set_title("Food Cavity Risk Distribution")
        else:
            ax3.axis('off')
            ax3.text(0.5, 0.5, "No nutrition data available", 
                    ha='center', va='center', fontsize=12)
    else:
        ax3.axis('off')
        ax3.text(0.5, 0.5, "No nutrition analysis available", 
                ha='center', va='center', fontsize=12)
    
    # Key recommendations
    ax4 = plt.subplot(3, 2, (4, 6))
    ax4.axis('off')
    
    recommendations = recommendation_results.get("recommendations", 
                                             ["No specific recommendations available"])
    
    # Filter out general recommendations
    key_recs = [r for r in recommendations if r.startswith(("⚠️", "ℹ️", "💡")) and not r.startswith("\n")]
    
    if len(key_recs) > 8:
        key_recs = key_recs[:8]
        
    text = "Key Recommendations:\n\n" + "\n".join(key_recs[:8])
    ax4.text(0.05, 0.95, text, va='top', fontsize=12, 
            bbox=dict(boxstyle="round,pad=0.5", facecolor='lightgray', alpha=0.2))
    
    # Dental image with detections
    ax5 = plt.subplot(3, 2, 5)
    if dental_results and "detection_visualization" in dental_results:
        img_path = dental_results["detection_visualization"]
        img = plt.imread(img_path)
        ax5.imshow(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
        ax5.set_title("Dental Analysis")
        ax5.axis('off')
    else:
        ax5.axis('off')
        ax5.text(0.5, 0.5, "No dental image visualization available", 
                ha='center', va='center', fontsize=12)
    
    # Add thermal analysis chart if available
    if thermal_results and "inflammation_detected" in thermal_results:
        ax_thermal = plt.subplot(3, 3, 7)
        
        features = thermal_results["temperature_features"]
        feature_names = ["Max Temp", "Min Temp", "Avg Temp", "Temp Range", "Hotspots"]
        feature_values = [
            features["max_temp"], 
            features["min_temp"], 
            features["avg_temp"], 
            features["temp_range"], 
            features["hotspot_count"]
        ]
        
        # Normalize values for better visualization
        normalized_values = [
            min(10, features["max_temp"]) / 10,  # Assuming max temp around 10°C above baseline
            min(5, features["min_temp"]) / 5,    # Assuming min temp around 5°C below baseline
            min(5, features["avg_temp"]) / 5,    # Assuming avg temp around 5°C
            min(5, features["temp_range"]) / 5,  # Assuming temp range around 5°C
            min(5, features["hotspot_count"]) / 5  # Normalized to 5 hotspots
        ]
        
        ax_thermal.bar(feature_names, normalized_values, 
                     color="red" if thermal_results["inflammation_detected"] else "green")
        ax_thermal.set_title(f"Thermal Analysis: {thermal_results['severity']} Severity")
        plt.setp(ax_thermal.xaxis.get_majorticklabels(), rotation=45, ha='right')
        
        # Add text annotation for key findings
        if thermal_results["inflammation_detected"]:
            ax_thermal.text(0.5, -0.3, f"Inflammation detected with {thermal_results['severity']} severity", 
                          ha='center', transform=ax_thermal.transAxes)
    
    # Save the figure
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "summary_report.png"), dpi=300, bbox_inches='tight')
    plt.close()

def main():
    """Main function to run the dental health advisor"""
    args = parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Import functions from other modules
    functions = import_functions()
    
    # Process dental image
    dental_results = process_dental_image(
        args.dental_image, 
        args.detection_model, 
        args.classification_model, 
        args.detection_threshold, 
        args.output_dir, 
        functions
    )
    
    # Process nutrition data
    nutrition_results = process_nutrition_data(
        args.nutrition_data,
        args.nutrition_model,
        args.output_dir,
        functions
    )
    
    # Process oral cancer image
    cancer_results = process_oral_cancer_image(
        args.oral_image,
        args.cancer_model,
        args.output_dir,
        functions
    ) if "process_oral_cancer_image" in globals() else None
    
    # Process facial image
    face_results = process_facial_image(
        args.face_image,
        args.face_model,
        args.output_dir,
        functions
    ) if "process_facial_image" in globals() else None
    
    # Process thermal image
    thermal_results = process_thermal_image(
        args.thermal_image,
        args.thermal_model,
        args.output_dir,
        functions
    )
    
    # Generate recommendations
    recommendations_path = os.path.join(args.output_dir, "recommendations.md")
    recommendation_results = generate_recommendations(
        dental_results, 
        nutrition_results,
        cancer_results,
        face_results,
        thermal_results,
        recommendations_path
    )
    
    # Create summary report
    create_summary_report(
        dental_results, 
        nutrition_results,
        cancer_results,
        face_results,
        thermal_results,
        recommendation_results, 
        args.output_dir
    )
    
    # Save all results to JSON
    results = {
        "dental_analysis": dental_results,
        "nutrition_analysis": nutrition_results,
        "oral_cancer_analysis": cancer_results,
        "facial_analysis": face_results,
        "thermal_analysis": thermal_results,
        "recommendations": recommendation_results
    }
    
    # Custom JSON encoder to handle numpy types, etc.
    class NpEncoder(json.JSONEncoder):
        def default(self, obj):
            if isinstance(obj, (np.integer, np.floating, np.bool_)):
                return obj.item()
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, Path):
                return str(obj)
            return super(NpEncoder, self).default(obj)
    
    # Save results to JSON
    with open(os.path.join(args.output_dir, "full_results.json"), "w") as f:
        json.dump(results, f, cls=NpEncoder, indent=2)
    
    print(f"\nDental health analysis complete. Results saved to {args.output_dir}")
    print(f"Recommendations available at {recommendations_path}")

if __name__ == "__main__":
    main() 