# Multi-Dataset Dental AI Training

This directory contains scripts for training AI models on multiple dental datasets, providing comprehensive dental health analysis capabilities.

## Available Datasets

### Dental Radiography Dataset
- **Location**: `/Users/<USER>/.cache/kagglehub/datasets/imtkaggleteam/dental-radiography/versions/1`
- **Description**: X-ray images with annotations for various dental conditions including implants, impacted teeth, cavities, and fillings.
- **Size**: 1,000+ annotated radiography images
- **Format**: COCO/YOLO annotations

### Cavity Classification Dataset
- **Location**: `/Users/<USER>/.cache/kagglehub/datasets/aqibrehmanpirzada/cavity-and-non-cavity-classification/versions/1/Dentalfinaldata`
- **Description**: A binary classification dataset of dental images showing presence or absence of cavities.
- **Size**: 2,062 images (1,074 cavity, 988 no cavity)
- **Format**: Images organized in cavity/no_cavity folders

### Food Nutrition Dataset
- **ID**: `utsavdey1410/food-nutrition-dataset`
- **Description**: Nutritional information for various food items, used to assess cavity risk.
- **Size**: Varies depending on version
- **Format**: CSV with nutritional information

### Oral Cancer Prediction Dataset
- **ID**: `ankushpanday2/oral-cancer-prediction-dataset`
- **Description**: Images of oral conditions for cancer detection and classification.
- **Size**: Varied collection of cancer and non-cancer oral images
- **Format**: Images organized in cancer/non-cancer categories

### Tufts Face Database
- **ID**: `kpvisionlab/tufts-face-database`
- **Description**: Collection of facial images for facial analysis related to dental health
- **Size**: Over 10,000 images of 112 subjects with varied poses and expressions
- **Format**: Images organized in subdirectories with metadata

### Infrared Thermography Temperature Dataset
- **ID**: `joebeachcapital/infrared-thermography-temperature`
- **Description**: Collection of thermal images for detecting inflammation and thermal abnormalities in dental tissues
- **Size**: Varies depending on version
- **Format**: Thermal images with optional temperature readings

## Training Scripts

### Cavity Classifier

The `cavity_classifier.py` script trains a model for cavity detection in dental images:

```bash
python3 cavity_classifier.py \
  --data_path /Users/<USER>/.cache/kagglehub/datasets/aqibrehmanpirzada/cavity-and-non-cavity-classification/versions/1/Dentalfinaldata \
  --output_dir cavity_classifier_output \
  --batch_size 32 \
  --epochs 20 \
  --learning_rate 0.001 \
  --model resnet18
```

Options:
- `--data_path`: Path to the cavity classification dataset
- `--output_dir`: Output directory for model and results
- `--batch_size`: Training batch size
- `--epochs`: Number of training epochs
- `--learning_rate`: Learning rate
- `--model`: Model architecture (resnet18, mobilenet, efficientnet)

### Food Cavity Risk Classifier

The `food_cavity_classifier.py` script analyzes nutritional data of food items to determine their potential for causing dental cavities:

```bash
python3 food_cavity_classifier.py \
  --dataset "utsavdey1410/food-nutrition-dataset" \
  --output_dir food_cavity_classifier_output \
  --epochs 50 \
  --batch_size 64 \
  --learning_rate 0.001 \
  --hidden_size 128
```

Options:
- `--dataset`: The Kaggle dataset identifier (default: "utsavdey1410/food-nutrition-dataset")
- `--output_dir`: Output directory for model and results (default: "./food_cavity_classifier_output")
- `--batch_size`: Training batch size (default: 64)
- `--epochs`: Number of training epochs (default: 50)
- `--learning_rate`: Learning rate (default: 0.001)
- `--hidden_size`: Hidden layer size in the neural network (default: 128)
- `--device`: Device to use (cuda, mps, cpu, or None for auto-detection)

The model automatically downloads the nutrition dataset using `kagglehub` and processes it to extract relevant features for cavity risk prediction. The script creates a classification model that focuses on nutritional components that influence cavity formation, such as sugar content, carbohydrates, and acidity indicators.

### Oral Cancer Classifier

The `oral_cancer_classifier.py` script trains a deep learning model to detect oral cancer from images:

```bash
python3 oral_cancer_classifier.py \
  --dataset "ankushpanday2/oral-cancer-prediction-dataset" \
  --output_dir oral_cancer_classifier_output \
  --batch_size 32 \
  --epochs 30 \
  --learning_rate 0.0001 \
  --model resnet50 \
  --image_size 224 \
  --augmentation
```

Options:
- `--dataset`: Kaggle dataset identifier (default: "ankushpanday2/oral-cancer-prediction-dataset") 
- `--output_dir`: Output directory for model and results (default: "./oral_cancer_classifier_output")
- `--batch_size`: Training batch size (default: 32)
- `--epochs`: Number of training epochs (default: 30)
- `--learning_rate`: Learning rate (default: 0.0001)
- `--model`: Model architecture (choices: resnet18, resnet50, densenet121, efficientnet_b0; default: resnet50)
- `--image_size`: Image size for training (default: 224)
- `--device`: Device to use (cuda, mps, cpu, or None for auto-detection)
- `--augmentation`: Use data augmentation (flag, enabled by default)

The model automatically downloads the oral cancer dataset using `kagglehub` and processes it to detect cancer in oral images. It uses transfer learning with pre-trained neural network architectures and applies extensive data augmentation to improve performance.

### Facial Analysis for Dental Health

The `face_analysis.py` script trains a model to analyze facial features related to dental health conditions:

```bash
python3 face_analysis.py \
  --dataset "kpvisionlab/tufts-face-database" \
  --output_dir facial_analysis_output \
  --batch_size 16 \
  --epochs 25 \
  --learning_rate 0.0001 \
  --model resnet50 \
  --image_size 256 \
  --features "asymmetry,jaw_alignment,smile_analysis"
```

Options:
- `--dataset`: Kaggle dataset identifier (default: "kpvisionlab/tufts-face-database")
- `--output_dir`: Output directory for model and results (default: "./facial_analysis_output")
- `--batch_size`: Training batch size (default: 16)
- `--epochs`: Number of training epochs (default: 25)
- `--learning_rate`: Learning rate (default: 0.0001)
- `--model`: Model architecture (choices: resnet18, resnet50, densenet121; default: resnet50)
- `--image_size`: Image size for training (default: 256)
- `--device`: Device to use (cuda, mps, cpu, or None for auto-detection)
- `--features`: Features to analyze (default: ["asymmetry", "jaw_alignment", "smile_analysis"])

The script trains a model to identify facial features that can indicate dental health issues, such as facial asymmetry (which can indicate jaw problems), jaw alignment issues, and smile characteristics. The model automatically downloads the Tufts Face Database using `kagglehub` and processes the images for facial feature analysis.

### Thermal Imaging Analysis

The `thermal_analysis.py` script trains a model to detect inflammation, infection, and other thermal abnormalities in dental tissues:

```bash
python3 thermal_analysis.py \
  --dataset "joebeachcapital/infrared-thermography-temperature" \
  --output_dir thermal_analysis_output \
  --batch_size 16 \
  --epochs 30 \
  --learning_rate 0.0001 \
  --model resnet50 \
  --temperature_threshold 1.5
```

Options:
- `--dataset`: Kaggle dataset identifier (default: "joebeachcapital/infrared-thermography-temperature")
- `--output_dir`: Output directory for model and results (default: "./thermal_analysis_output")
- `--batch_size`: Training batch size (default: 16)
- `--epochs`: Number of training epochs (default: 30)
- `--learning_rate`: Learning rate (default: 0.0001)
- `--model`: Model architecture (choices: resnet18, resnet50, densenet121; default: resnet50)
- `--image_size`: Image size for training (default: 256)
- `--device`: Device to use (cuda, mps, cpu, or None for auto-detection)
- `--temperature_threshold`: Temperature difference threshold for abnormality in °C (default: 1.5)

The script trains a model to detect thermal abnormalities in dental tissues using infrared thermography images. It can process temperature data when available or analyze thermal patterns in the images. The model automatically downloads the dataset using `kagglehub` and processes the images for thermal analysis.

## Prediction Scripts

### Simple Cavity Prediction

The `simple_predict.py` script makes predictions on new dental images:

```bash
python3 simple_predict.py \
  --model_path cavity_classifier_output/cavity_classifier_model.pt \
  --input path/to/image.jpg \
  --output_dir predictions
```

Options:
- `--model_path`: Path to the trained model file
- `--input`: Path to input image or directory of images
- `--output_dir`: Directory to save results
- `--show`: Show results instead of saving them
- `--model_info`: Path to model information JSON

### Food Cavity Risk Prediction

The `food_cavity_predict.py` script allows you to use the trained model to analyze food items and assess their cavity risk:

```bash
python3 food_cavity_predict.py \
  --model_path food_cavity_classifier_output/model_best.pt \
  --input path/to/nutrition_data.csv \
  --output_dir food_predictions
```

You can also analyze a single food item by providing its name instead of a CSV file, which will prompt for manual entry of nutritional values:

```bash
python3 food_cavity_predict.py \
  --model_path food_cavity_classifier_output/model_best.pt \
  --input "Chocolate Cake" \
  --output_dir food_predictions
```

Options:
- `--model_path`: Path to the trained model file (required)
- `--input`: Path to input CSV file with food nutritional data or a single food name (required)
- `--output_dir`: Directory to save prediction results (default: "./food_cavity_predictions")
- `--device`: Device to use (cuda, mps, cpu, or None for auto-detection)
- `--show`: Show visualizations instead of saving them

### Oral Cancer Prediction

The `oral_cancer_predict.py` script analyzes dental images to detect signs of oral cancer:

```bash
python3 oral_cancer_predict.py \
  --model_path oral_cancer_classifier_output/model_best.pt \
  --input path/to/oral_image.jpg \
  --output_dir oral_cancer_predictions \
  --threshold 0.5
```

You can also analyze a directory of images:

```bash
python3 oral_cancer_predict.py \
  --model_path oral_cancer_classifier_output/model_best.pt \
  --input path/to/images_directory \
  --output_dir oral_cancer_predictions
```

Options:
- `--model_path`: Path to the trained model file (required)
- `--input`: Path to input image or directory of images (required)
- `--output_dir`: Directory to save prediction results (default: "./oral_cancer_predictions")
- `--device`: Device to use (cuda, mps, cpu, or None for auto-detection)
- `--threshold`: Threshold for positive classification (default: 0.5)
- `--show`: Show visualizations instead of saving them

The script provides detailed visualizations with risk levels (HIGH/MEDIUM/LOW) and generates a comprehensive report in JSON format. When processing multiple images, it produces summary charts showing the distribution of predictions and cancer probabilities across all analyzed images.

### Facial Feature Analysis

The `face_predict.py` script analyzes facial images to identify features related to dental health:

```bash
python3 face_predict.py \
  --model_path facial_analysis_output/model_best.pt \
  --input path/to/face_image.jpg \
  --output_dir facial_analysis_predictions \
  --detailed
```

You can also analyze a directory of facial images:

```bash
python3 face_predict.py \
  --model_path facial_analysis_output/model_best.pt \
  --input path/to/images_directory \
  --output_dir facial_analysis_predictions \
  --threshold 0.5
```

Options:
- `--model_path`: Path to the trained model file (required)
- `--input`: Path to input image or directory of images (required)
- `--output_dir`: Directory to save prediction results (default: "./facial_analysis_predictions")
- `--device`: Device to use (cuda, mps, cpu, or None for auto-detection)
- `--threshold`: Threshold for issue classification (default: 0.5)
- `--show`: Show visualizations instead of saving them
- `--detailed`: Show detailed facial feature analysis with landmarks

The script detects faces in the images, extracts facial landmarks, and analyzes features related to dental health such as asymmetry, jaw alignment, and smile characteristics. It provides detailed visualizations with risk levels (HIGH/MEDIUM/LOW) and generates a comprehensive report in JSON format.

### Thermal Imaging Analysis

The `thermal_predict.py` script analyzes thermal images to detect inflammation and abnormalities in dental tissues:

```bash
python3 thermal_predict.py \
  --model_path thermal_analysis_output/model_best.pt \
  --input path/to/thermal_image.jpg \
  --output_dir thermal_predictions \
  --detailed
```

You can also analyze a directory of thermal images:

```bash
python3 thermal_predict.py \
  --model_path thermal_analysis_output/model_best.pt \
  --input path/to/images_directory \
  --output_dir thermal_predictions \
  --threshold 0.5
```

Options:
- `--model_path`: Path to the trained model file (required)
- `--input`: Path to input image or directory of images (required)
- `--output_dir`: Directory to save prediction results (default: "./thermal_predictions")
- `--device`: Device to use (cuda, mps, cpu, or None for auto-detection)
- `--threshold`: Threshold for abnormality classification (default: 0.5)
- `--show`: Show visualizations instead of saving them
- `--detailed`: Show detailed thermal analysis with region breakdown and hotspot detection

The script provides detailed visualizations with severity levels (HIGH/MEDIUM/LOW) based on thermal pattern analysis. It generates a comprehensive report in JSON format, including temperature distribution, hotspot detection, and dental implications. When processing multiple images, it produces summary charts showing distribution of temperature patterns and abnormalities across all analyzed images.

## Test Scripts

The repository includes test scripts to demonstrate the functionality of each model:

### Test Food Cavity Script

```bash
python3 test_food_cavity.py --download_samples --show
```

### Test Oral Cancer Script

```bash
python3 test_oral_cancer.py --download_samples --show
```

### Test Facial Analysis Script

```bash
python3 test_face_analysis.py --download_samples --detailed --show
```

These test scripts will download sample data if needed and demonstrate the prediction capabilities of each model.

### Thermal Imaging Test

The `test_thermal.py` script demonstrates the thermal imaging analysis functionality:

```bash
python3 test_thermal.py \
  --model_path thermal_analysis_output/model_best.pt \
  --download_samples \
  --output_dir thermal_test_results \
  --detailed
```

Options:
- `--model_path`: Path to the trained model file (default: "thermal_analysis_output/model_best.pt")
- `--download_samples`: Download sample images if no existing model is found
- `--output_dir`: Directory to save test results (default: "./thermal_test_results")
- `--show`: Show visualizations instead of saving them
- `--detailed`: Show detailed thermal analysis

The test script can download sample thermal images from the dataset or create synthetic test images if needed. It demonstrates how to use the thermal analysis model for dental applications and shows how to interpret the results.

## Integrated Dental Health Advisor

The `dental_health_advisor.py` script provides a comprehensive dental health analysis by combining:
1. Analysis of dental radiography images for condition detection
2. Assessment of cavity presence/absence
3. Nutritional analysis of food items for cavity risk
4. Oral cancer risk assessment
5. Facial feature analysis for dental health
6. Thermal imaging analysis for dental health

```bash
python3 dental_health_advisor.py \
  --cavity_model cavity_classifier_output/cavity_classifier_model.pt \
  --food_model food_cavity_classifier_output/model_best.pt \
  --cancer_model oral_cancer_classifier_output/model_best.pt \
  --face_model facial_analysis_output/model_best.pt \
  --thermal_model thermal_analysis_output/model_best.pt \
  --inputs path/to/inputs_directory \
  --output_dir advisory_results
```

Options:
- `--cavity_model`: Path to the cavity classification model
- `--food_model`: Path to the food cavity risk model
- `--cancer_model`: Path to the oral cancer detection model
- `--face_model`: Path to the facial analysis model
- `--thermal_model`: Path to the thermal imaging model
- `--inputs`: Path to input directory containing dental images and nutritional data
- `--output_dir`: Directory to save results (default: "./dental_advisor_output")
- `--detection_threshold`: Detection confidence threshold (default: 0.5)
- `--show`: Show visualizations instead of saving them

The dental health advisor generates:
1. **Comprehensive Recommendations**: Personalized dental health recommendations based on all analyses.
2. **Summary Report**: A visual dashboard combining key metrics from all analyses.
3. **Complete Results**: A JSON file with detailed results from all analyses.

## Facial Analysis Features

The facial analysis module examines several dental-health related features:

1. **Facial Asymmetry**: Analyzes facial symmetry which can indicate jaw issues or bite problems
2. **Jaw Alignment**: Evaluates proper alignment of the jaw and potential TMJ issues
3. **Smile Analysis**: Assesses smile characteristics including teeth visibility, lip curvature, and symmetry

Each feature is scored on a scale of 0-1, where higher values indicate potential issues. The system combines these scores with model predictions to determine an overall risk level for dental-related facial issues.

## Troubleshooting

- **Dataset Download Issues**: If Kaggle datasets fail to download, ensure you have the Kaggle API configured correctly and try running with a direct path to a local copy of the dataset instead.
- **Memory Issues**: If you encounter memory errors, try reducing batch size or using a simpler model architecture.
- **CUDA Out of Memory**: For training on large datasets, consider using a smaller batch size or enabling gradient accumulation.
- **Face Detection Issues**: For facial analysis, ensure that images have clear, well-lit faces. The `face_recognition` library requires proper lighting and frontal or near-frontal face orientation.

## Additional Information

- Models are saved with their training metrics and configuration for easy reference
- All prediction scripts can process both single images and directories of images
- Visualization capabilities help interpret model predictions and results
- Facial analysis requires additional dependencies: `face_recognition` and `dlib` 