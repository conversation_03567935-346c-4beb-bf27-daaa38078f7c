#!/usr/bin/env python3
"""
Dental Radiography Inference Script for Smilo

This script demonstrates how to use a trained dental radiography model
for inference on new images.
"""

import os
import sys
import argparse
import logging
from pathlib import Path
import json
import time

import numpy as np
import torch
import torchvision
from torchvision import transforms
import matplotlib.pyplot as plt
from PIL import Image, ImageDraw, ImageFont
import cv2

# Import the dental dataset manager
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from dental_dataset_manager import DentalDatasetManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("dental_radiography_inference.log")
    ]
)
logger = logging.getLogger(__name__)


def load_model(model_path, num_classes, device="cuda"):
    """
    Load a trained dental radiography model.
    
    Args:
        model_path: Path to the model file
        num_classes: Number of classes
        device: Device to load the model on
        
    Returns:
        Loaded model
    """
    # Create model architecture
    model = torchvision.models.detection.fasterrcnn_resnet50_fpn(weights=None)
    
    # Replace the classifier with a new one for our number of classes
    in_features = model.roi_heads.box_predictor.cls_score.in_features
    model.roi_heads.box_predictor = torchvision.models.detection.faster_rcnn.FastRCNNPredictor(
        in_features, num_classes
    )
    
    # Load weights
    model.load_state_dict(torch.load(model_path, map_location=device))
    model.to(device)
    model.eval()
    
    return model


def preprocess_image(image_path, transform=None):
    """
    Preprocess an image for inference.
    
    Args:
        image_path: Path to the image
        transform: Transforms to apply
        
    Returns:
        Preprocessed image tensor
    """
    # Read image
    image = cv2.imread(str(image_path))
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    # Apply preprocessing
    if transform:
        image_tensor = transform(image)
    else:
        # Default transform
        transform = transforms.Compose([
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        image_tensor = transform(image)
    
    return image_tensor, image


def run_inference(model, image_tensor, device="cuda", confidence_threshold=0.5):
    """
    Run inference on an image.
    
    Args:
        model: Trained model
        image_tensor: Preprocessed image tensor
        device: Device to run inference on
        confidence_threshold: Confidence threshold for predictions
        
    Returns:
        Prediction results
    """
    # Move image to device
    image_tensor = image_tensor.to(device)
    
    # Run inference
    with torch.no_grad():
        prediction = model([image_tensor])[0]
    
    # Filter predictions by confidence
    keep = prediction['scores'] > confidence_threshold
    filtered_prediction = {
        'boxes': prediction['boxes'][keep],
        'labels': prediction['labels'][keep],
        'scores': prediction['scores'][keep]
    }
    
    return filtered_prediction


def visualize_prediction(image, prediction, class_names, output_path=None):
    """
    Visualize prediction on an image.
    
    Args:
        image: Original image (numpy array)
        prediction: Prediction results
        class_names: List of class names
        output_path: Path to save the visualization
        
    Returns:
        Visualization image
    """
    # Convert to PIL Image for drawing
    image_pil = Image.fromarray(image)
    draw = ImageDraw.Draw(image_pil)
    
    # Define colors for different classes
    colors = [
        (255, 0, 0),    # Red
        (0, 255, 0),    # Green
        (0, 0, 255),    # Blue
        (255, 255, 0),  # Yellow
        (255, 0, 255),  # Magenta
        (0, 255, 255),  # Cyan
    ]
    
    # Draw bounding boxes and labels
    for box, label, score in zip(prediction['boxes'].cpu().numpy(), 
                               prediction['labels'].cpu().numpy(), 
                               prediction['scores'].cpu().numpy()):
        # Get coordinates
        x1, y1, x2, y2 = box
        
        # Get class name and color
        class_idx = int(label)
        class_name = class_names[class_idx - 1] if class_idx > 0 and class_idx <= len(class_names) else f"Class {class_idx}"
        color = colors[class_idx % len(colors)]
        
        # Draw rectangle
        draw.rectangle([x1, y1, x2, y2], outline=color, width=3)
        
        # Draw label
        label_text = f"{class_name} ({score:.2f})"
        draw.text((x1, y1 - 10), label_text, fill=color)
    
    # Save if output path is provided
    if output_path:
        image_pil.save(output_path)
    
    return image_pil


def generate_report(prediction, class_names, output_path=None):
    """
    Generate a report from the prediction results.
    
    Args:
        prediction: Prediction results
        class_names: List of class names
        output_path: Path to save the report
        
    Returns:
        Report as a dictionary
    """
    # Count detections by class
    class_counts = {}
    for label in prediction['labels'].cpu().numpy():
        class_idx = int(label)
        class_name = class_names[class_idx - 1] if class_idx > 0 and class_idx <= len(class_names) else f"Class {class_idx}"
        class_counts[class_name] = class_counts.get(class_name, 0) + 1
    
    # Create report
    report = {
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        "num_detections": len(prediction['labels']),
        "class_counts": class_counts,
        "detections": []
    }
    
    # Add details for each detection
    for box, label, score in zip(prediction['boxes'].cpu().numpy(), 
                               prediction['labels'].cpu().numpy(), 
                               prediction['scores'].cpu().numpy()):
        class_idx = int(label)
        class_name = class_names[class_idx - 1] if class_idx > 0 and class_idx <= len(class_names) else f"Class {class_idx}"
        
        detection = {
            "class": class_name,
            "confidence": float(score),
            "bounding_box": box.tolist()
        }
        
        report["detections"].append(detection)
    
    # Save if output path is provided
    if output_path:
        with open(output_path, 'w') as f:
            json.dump(report, f, indent=2)
    
    return report


def main():
    """Main function to run dental radiography inference."""
    parser = argparse.ArgumentParser(description="Dental Radiography Inference")
    
    # Input options
    parser.add_argument("--model", required=True, 
                        help="Path to the trained model")
    parser.add_argument("--image", required=True, 
                        help="Path to the input image")
    parser.add_argument("--classes", nargs='+', default=["Implant", "Cavity", "Fillings", "Impacted Tooth"],
                        help="List of class names")
    
    # Inference options
    parser.add_argument("--confidence", type=float, default=0.5, 
                        help="Confidence threshold for predictions")
    parser.add_argument("--device", default="cuda" if torch.cuda.is_available() else "cpu", 
                        help="Device to run inference on (cuda or cpu)")
    
    # Output options
    parser.add_argument("--output-dir", default="./dental_radiography_inference", 
                        help="Directory to save output")
    
    args = parser.parse_args()
    
    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Load model
    logger.info(f"Loading model from: {args.model}")
    num_classes = len(args.classes) + 1  # +1 for background
    model = load_model(args.model, num_classes, device=args.device)
    
    # Preprocess image
    logger.info(f"Processing image: {args.image}")
    image_tensor, original_image = preprocess_image(args.image)
    
    # Run inference
    logger.info("Running inference...")
    prediction = run_inference(model, image_tensor, device=args.device, 
                              confidence_threshold=args.confidence)
    
    # Visualize prediction
    logger.info("Generating visualization...")
    output_image_path = output_dir / "prediction.png"
    visualization = visualize_prediction(original_image, prediction, args.classes, 
                                        output_path=output_image_path)
    
    # Generate report
    logger.info("Generating report...")
    output_report_path = output_dir / "report.json"
    report = generate_report(prediction, args.classes, output_path=output_report_path)
    
    # Print summary
    print("\nDental Radiography Analysis Results:")
    print(f"Total detections: {report['num_detections']}")
    print("\nDetections by class:")
    for class_name, count in report['class_counts'].items():
        print(f"- {class_name}: {count}")
    
    print(f"\nVisualization saved to: {output_image_path}")
    print(f"Report saved to: {output_report_path}")


if __name__ == "__main__":
    main()
