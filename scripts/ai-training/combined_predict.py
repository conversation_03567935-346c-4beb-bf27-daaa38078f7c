#!/usr/bin/env python3
"""
Combined prediction script for dental analysis.
This script uses both the dental radiography detection model and the cavity classification model
to provide comprehensive dental analysis.
"""

import os
import sys
import torch
import torchvision
import torchvision.transforms as transforms
from PIL import Image, ImageDraw, ImageFont
import matplotlib.pyplot as plt
import argparse
import json
from pathlib import Path
import numpy as np
import cv2

def parse_args():
    parser = argparse.ArgumentParser(description="Combined dental analysis prediction")
    parser.add_argument("--detection_model", type=str, required=True, 
                      help="Path to dental radiography detection model")
    parser.add_argument("--classification_model", type=str, required=True,
                      help="Path to cavity classification model")
    parser.add_argument("--input", type=str, required=True,
                      help="Path to input image or directory of images")
    parser.add_argument("--output_dir", type=str, default="./combined_predictions",
                      help="Directory to save results")
    parser.add_argument("--detection_threshold", type=float, default=0.5,
                      help="Detection threshold for radiography model")
    parser.add_argument("--show", action="store_true", default=False,
                      help="Show results instead of saving them")
    parser.add_argument("--rankings_csv", type=str, default=None,
                      help="Path to dental rankings CSV for metadata (optional)")
    return parser.parse_args()

def load_detection_model(model_path):
    """Load the dental radiography detection model"""
    print(f"Loading detection model from {model_path}")
    
    # Initialize model architecture (Faster R-CNN with ResNet-50 backbone)
    model = torchvision.models.detection.fasterrcnn_resnet50_fpn(pretrained=False)
    
    # Update the box predictor for dental classes
    num_classes = 5  # Background + 4 dental conditions
    in_features = model.roi_heads.box_predictor.cls_score.in_features
    model.roi_heads.box_predictor = torchvision.models.detection.faster_rcnn.FastRCNNPredictor(in_features, num_classes)
    
    # Load the state dict
    try:
        model.load_state_dict(torch.load(model_path, map_location=torch.device('cpu')))
    except Exception as e:
        print(f"Error loading detection model: {e}")
        # Try alternative loading method
        checkpoint = torch.load(model_path, map_location=torch.device('cpu'))
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
        elif 'model' in checkpoint:
            model.load_state_dict(checkpoint['model'])
    
    model.eval()
    return model

def load_classification_model(model_path):
    """Load the cavity classification model"""
    print(f"Loading classification model from {model_path}")
    
    # Default values
    model_type = 'resnet18'
    num_classes = 2
    class_names = ['cavity', 'no_cavity']
    
    # Check for model_info.json
    model_info_path = os.path.dirname(model_path) + '/model_info.json'
    if os.path.exists(model_info_path):
        with open(model_info_path, 'r') as f:
            model_info = json.load(f)
        model_type = model_info.get('model_type', model_type)
        num_classes = model_info.get('num_classes', num_classes)
        class_names = model_info.get('class_names', class_names)
    
    # Initialize model architecture
    if model_type == 'resnet18':
        model = torchvision.models.resnet18(weights=None)
        model.fc = torch.nn.Linear(model.fc.in_features, num_classes)
    elif model_type == 'mobilenet':
        model = torchvision.models.mobilenet_v2(weights=None)
        model.classifier[1] = torch.nn.Linear(model.classifier[1].in_features, num_classes)
    elif model_type == 'efficientnet':
        model = torchvision.models.efficientnet_b0(weights=None)
        model.classifier[1] = torch.nn.Linear(model.classifier[1].in_features, num_classes)
    
    # Load the state dict
    try:
        model.load_state_dict(torch.load(model_path, map_location=torch.device('cpu')))
    except Exception as e:
        print(f"Error loading classification model: {e}")
        # Try alternative loading method
        checkpoint = torch.load(model_path, map_location=torch.device('cpu'))
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
        elif 'model' in checkpoint:
            model.load_state_dict(checkpoint['model'])
    
    model.eval()
    return model, class_names

def load_rankings(csv_path):
    """Load dental rankings data if available"""
    if not csv_path or not os.path.exists(csv_path):
        return None
    
    import pandas as pd
    try:
        rankings = pd.read_csv(csv_path)
        print(f"Loaded {len(rankings)} dental school rankings")
        return rankings
    except Exception as e:
        print(f"Error loading rankings data: {e}")
        return None

def process_image_for_detection(image_path):
    """Process an image for the detection model"""
    img = Image.open(image_path).convert('RGB')
    img_tensor = transforms.functional.to_tensor(img)
    return img, img_tensor

def process_image_for_classification(image_path):
    """Process an image for the classification model"""
    transform = transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.ToTensor(),
        transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
    ])
    img = Image.open(image_path).convert('RGB')
    img_tensor = transform(img)
    return img_tensor

def detect_dental_conditions(model, img_tensor, threshold, device):
    """Run detection on the image"""
    dental_classes = {
        0: "background",
        1: "implant",
        2: "cavity",
        3: "filling",
        4: "impacted_tooth"
    }
    
    with torch.no_grad():
        img_tensor = img_tensor.to(device)
        prediction = model([img_tensor])
    
    # Extract results
    boxes = prediction[0]['boxes'].cpu().numpy()
    scores = prediction[0]['scores'].cpu().numpy()
    labels = prediction[0]['labels'].cpu().numpy()
    
    # Filter by threshold
    mask = scores >= threshold
    boxes = boxes[mask]
    scores = scores[mask]
    labels = labels[mask]
    
    # Create result objects
    detections = []
    for box, score, label in zip(boxes, scores, labels):
        detections.append({
            'box': box.tolist(),
            'score': float(score),
            'label': int(label),
            'class_name': dental_classes.get(int(label), f"class_{label}")
        })
    
    return detections

def classify_cavity(model, img_tensor, class_names, device):
    """Run cavity classification on the image"""
    with torch.no_grad():
        img_tensor = img_tensor.unsqueeze(0).to(device)
        outputs = model(img_tensor)
        _, preds = torch.max(outputs, 1)
        probabilities = torch.nn.functional.softmax(outputs, dim=1)
        confidence = probabilities[0][preds[0]].item()
    
    classification = {
        'class_id': preds[0].item(),
        'class_name': class_names[preds[0]],
        'confidence': float(confidence)
    }
    
    return classification

def visualize_combined_results(img, detections, classification, output_path=None, show=False):
    """Create a visualization of combined results"""
    # Convert PIL image to opencv format for drawing
    img_cv = np.array(img)
    img_cv = img_cv[:, :, ::-1].copy()  # RGB to BGR
    
    # Draw detection boxes
    for detection in detections:
        x1, y1, x2, y2 = [int(coord) for coord in detection['box']]
        label = detection['class_name']
        score = detection['score']
        
        # Choose color based on class
        if label == 'cavity':
            color = (0, 0, 255)  # Red for cavity
        elif label == 'filling':
            color = (0, 255, 0)  # Green for filling
        elif label == 'implant':
            color = (255, 0, 0)  # Blue for implant
        else:
            color = (255, 255, 0)  # Cyan for other classes
        
        # Draw bounding box
        cv2.rectangle(img_cv, (x1, y1), (x2, y2), color, 2)
        
        # Add label text
        text = f"{label}: {score:.2f}"
        cv2.putText(img_cv, text, (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
    
    # Add classification result
    class_name = classification['class_name']
    confidence = classification['confidence']
    
    # Position the text in the top-left corner
    if class_name == 'cavity':
        text_color = (0, 0, 255)  # Red for cavity
    else:
        text_color = (0, 255, 0)  # Green for no cavity
    
    text = f"Overall classification: {class_name} ({confidence:.2f})"
    cv2.putText(img_cv, text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, text_color, 2)
    
    # Convert back to RGB for Matplotlib
    img_rgb = cv2.cvtColor(img_cv, cv2.COLOR_BGR2RGB)
    
    # Display the result
    plt.figure(figsize=(12, 12))
    plt.imshow(img_rgb)
    plt.axis('off')
    
    if output_path:
        plt.savefig(output_path, bbox_inches='tight')
        plt.close()
        print(f"Visualization saved to {output_path}")
    
    if show:
        plt.show()
    else:
        plt.close()
    
    return img_rgb

def main():
    args = parse_args()
    
    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Set device
    device = torch.device("cuda" if torch.cuda.is_available() else 
                         "mps" if torch.backends.mps.is_available() else 
                         "cpu")
    print(f"Using device: {device}")
    
    # Load models
    detection_model = load_detection_model(args.detection_model)
    detection_model = detection_model.to(device)
    
    classification_model, class_names = load_classification_model(args.classification_model)
    classification_model = classification_model.to(device)
    
    # Load rankings data if provided
    rankings = load_rankings(args.rankings_csv)
    
    # Process input path
    input_path = Path(args.input)
    
    if input_path.is_file():
        # Process a single image
        print(f"Processing image: {input_path}")
        
        # Load and process image
        img, detection_tensor = process_image_for_detection(input_path)
        classification_tensor = process_image_for_classification(input_path)
        
        # Run inference with both models
        detections = detect_dental_conditions(detection_model, detection_tensor, args.detection_threshold, device)
        classification = classify_cavity(classification_model, classification_tensor, class_names, device)
        
        # Create visualization
        output_filename = f"{input_path.stem}_combined{input_path.suffix}"
        output_path = output_dir / output_filename if not args.show else None
        
        result_img = visualize_combined_results(img, detections, classification, output_path, args.show)
        
        # Save results to JSON
        results = {
            'filename': input_path.name,
            'detections': detections,
            'classification': classification
        }
        
        result_json_path = output_dir / f"{input_path.stem}_results.json"
        with open(result_json_path, 'w') as f:
            json.dump(results, f, indent=4)
        
        # Print summary
        detection_summary = ", ".join([f"{d['class_name']} ({d['score']:.2f})" for d in detections])
        print(f"Detections: {detection_summary if detection_summary else 'None'}")
        print(f"Classification: {classification['class_name']} with {classification['confidence']:.2%} confidence")
        
    elif input_path.is_dir():
        # Process a directory of images
        print(f"Processing directory: {input_path}")
        image_extensions = ['.jpg', '.jpeg', '.png']
        image_paths = [f for f in input_path.glob('*') if f.suffix.lower() in image_extensions]
        
        if not image_paths:
            print(f"No images found in {input_path}")
            return
        
        print(f"Found {len(image_paths)} images")
        
        # Track overall results
        all_results = []
        
        for img_path in image_paths:
            print(f"Processing {img_path.name}...")
            
            # Load and process image
            img, detection_tensor = process_image_for_detection(img_path)
            classification_tensor = process_image_for_classification(img_path)
            
            # Run inference with both models
            detections = detect_dental_conditions(detection_model, detection_tensor, args.detection_threshold, device)
            classification = classify_cavity(classification_model, classification_tensor, class_names, device)
            
            # Create visualization
            output_filename = f"{img_path.stem}_combined{img_path.suffix}"
            output_path = output_dir / output_filename if not args.show else None
            
            result_img = visualize_combined_results(img, detections, classification, output_path, args.show)
            
            # Add to results
            results = {
                'filename': img_path.name,
                'detections': detections,
                'classification': classification
            }
            
            # Save individual JSON result
            result_json_path = output_dir / f"{img_path.stem}_results.json"
            with open(result_json_path, 'w') as f:
                json.dump(results, f, indent=4)
            
            all_results.append(results)
        
        # Save summary of all results
        summary_path = output_dir / "all_results.json"
        with open(summary_path, 'w') as f:
            json.dump(all_results, f, indent=4)
        
        # Generate summary statistics
        total_images = len(all_results)
        cavity_images = sum(1 for r in all_results if r['classification']['class_name'] == 'cavity')
        
        detection_counts = {}
        for result in all_results:
            for detection in result['detections']:
                class_name = detection['class_name']
                detection_counts[class_name] = detection_counts.get(class_name, 0) + 1
        
        print(f"\nSummary statistics:")
        print(f"Total images processed: {total_images}")
        print(f"Images classified as having cavity: {cavity_images} ({cavity_images/total_images:.1%})")
        print("Detections by class:")
        for class_name, count in detection_counts.items():
            print(f"  - {class_name}: {count} detections")
        
        print(f"Results saved to {output_dir}")
    
    else:
        print(f"Input path not found: {input_path}")

if __name__ == "__main__":
    main() 