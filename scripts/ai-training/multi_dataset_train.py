#!/usr/bin/env python3
"""
Multi-dataset training script for dental AI.
This script integrates multiple dental datasets to create a comprehensive
dental analysis AI system for the Smilo Dental project.
"""

import os
import sys
import argparse
import torch
import torchvision
import pandas as pd
import numpy as np
from pathlib import Path
import pytorch_lightning as pl
from pytorch_lightning.callbacks import ModelCheckpoint, EarlyStopping, LearningRateMonitor
from pytorch_lightning.loggers import TensorBoardLogger
from torch.utils.data import DataLoader, Dataset, ConcatDataset
from torchvision import transforms
from PIL import Image
import json

# Import utilities from existing training pipeline
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from data_module import DentalRadiographyDataModule
from model import DentalObjectDetectionModel
from utils import set_seeds, export_model, create_inference_model

class CavityClassificationDataset(Dataset):
    """Dataset for cavity classification"""
    def __init__(self, root_dir, transform=None):
        self.root_dir = Path(root_dir)
        self.transform = transform
        self.classes = ['cavity', 'no_cavity']
        self.class_to_idx = {cls: idx for idx, cls in enumerate(self.classes)}
        
        # Collect all image paths and labels
        self.samples = []
        for class_name in self.classes:
            class_dir = self.root_dir / class_name
            for img_path in class_dir.glob('*.jpg'):
                self.samples.append((str(img_path), self.class_to_idx[class_name]))
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        img_path, label = self.samples[idx]
        image = Image.open(img_path).convert('RGB')
        
        if self.transform:
            image = self.transform(image)
        
        # Create detection target format compatible with object detection
        # For classification dataset, we'll create a dummy box covering most of the image
        h, w = image.shape[1], image.shape[2]
        boxes = torch.tensor([[0.1 * w, 0.1 * h, 0.9 * w, 0.9 * h]], dtype=torch.float32)
        labels = torch.tensor([label + 1], dtype=torch.int64)  # +1 because 0 is background in detection
        
        target = {
            'boxes': boxes,
            'labels': labels,
            'image_id': torch.tensor([idx]),
            'area': (boxes[:, 3] - boxes[:, 1]) * (boxes[:, 2] - boxes[:, 0]),
            'iscrowd': torch.zeros((1,), dtype=torch.int64)
        }
        
        return image, target

def load_dental_rankings(csv_path):
    """Load dental school rankings as metadata"""
    try:
        rankings = pd.read_csv(csv_path)
        print(f"Loaded dental rankings data with {len(rankings)} institutions")
        return rankings
    except Exception as e:
        print(f"Error loading dental rankings: {e}")
        return None

def parse_args():
    """Parse command-line arguments"""
    parser = argparse.ArgumentParser(description="Multi-dataset dental AI training")
    
    # Dataset paths
    parser.add_argument("--radiography_path", type=str, 
                       default="/Users/<USER>/.cache/kagglehub/datasets/imtkaggleteam/dental-radiography/versions/1",
                       help="Path to dental radiography dataset")
    parser.add_argument("--cavity_path", type=str, 
                       default="/Users/<USER>/.cache/kagglehub/datasets/aqibrehmanpirzada/cavity-and-non-cavity-classification/versions/1/Dentalfinaldata",
                       help="Path to cavity classification dataset")
    parser.add_argument("--rankings_path", type=str, 
                       default="/Users/<USER>/.cache/kagglehub/datasets/nehaprabhavalkar/indian-universities-rankings-2020/versions/1/dental.csv",
                       help="Path to dental school rankings CSV")
    
    # Training parameters
    parser.add_argument("--batch_size", type=int, default=8, help="Batch size")
    parser.add_argument("--epochs", type=int, default=30, help="Number of epochs")
    parser.add_argument("--learning_rate", type=float, default=3e-4, help="Learning rate")
    parser.add_argument("--backbone", type=str, default="resnet50", help="Model backbone")
    parser.add_argument("--num_workers", type=int, default=4, help="Number of data loading workers")
    parser.add_argument("--output_dir", type=str, default="./multi_dataset_output", help="Output directory")
    parser.add_argument("--seed", type=int, default=42, help="Random seed")
    parser.add_argument("--use_gpu", action="store_true", default=True, help="Use GPU if available")
    parser.add_argument("--precision", type=str, default="16-mixed", choices=["16", "32", "16-mixed"],
                       help="Floating point precision")
    
    return parser.parse_args()

def prepare_multi_dataset(args):
    """Prepare and combine multiple datasets"""
    print("Preparing multi-dataset training...")
    
    # 1. Create dental radiography data module (object detection)
    print("Setting up dental radiography dataset...")
    radiography_data = DentalRadiographyDataModule(
        data_path=args.radiography_path,
        batch_size=args.batch_size,
        num_workers=args.num_workers,
        aug_intensity="medium"
    )
    radiography_data.prepare_data()
    radiography_data.setup()
    
    # 2. Create cavity classification dataset
    print("Setting up cavity classification dataset...")
    transform = transforms.Compose([
        transforms.Resize((512, 512)),
        transforms.ToTensor(),
        transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
    ])
    
    cavity_dataset = CavityClassificationDataset(
        root_dir=args.cavity_path,
        transform=transform
    )
    
    print(f"Loaded cavity dataset with {len(cavity_dataset)} images")
    
    # 3. Load dental rankings as metadata
    rankings_data = load_dental_rankings(args.rankings_path)
    
    return radiography_data, cavity_dataset, rankings_data

def main():
    """Main training function"""
    # Parse arguments
    args = parse_args()
    
    # Set random seeds
    set_seeds(args.seed)
    
    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Prepare datasets
    radiography_data, cavity_dataset, rankings_data = prepare_multi_dataset(args)
    
    # Get class names from both datasets
    all_classes = radiography_data.classes + ['Cavity', 'No Cavity']
    print(f"Combined classes: {all_classes}")
    
    # Save dataset configuration
    dataset_config = {
        "radiography_classes": radiography_data.classes,
        "cavity_classes": ["Cavity", "No Cavity"],
        "all_classes": all_classes,
        "rankings_metadata": args.rankings_path
    }
    
    with open(output_dir / "dataset_config.json", "w") as f:
        json.dump(dataset_config, f, indent=4)
    
    # Initialize model with combined classes
    num_classes = len(all_classes) + 1  # +1 for background
    model = DentalObjectDetectionModel(
        num_classes=num_classes,
        backbone_name=args.backbone,
        pretrained=True,
        learning_rate=args.learning_rate
    )
    
    # Setup callbacks
    callbacks = [
        ModelCheckpoint(
            dirpath=output_dir / "checkpoints",
            filename="{epoch:02d}-{val_loss:.4f}",
            monitor="val_loss",
            mode="min",
            save_top_k=3,
            save_last=True
        ),
        EarlyStopping(
            monitor="val_loss",
            patience=10,
            mode="min"
        ),
        LearningRateMonitor(logging_interval="epoch")
    ]
    
    # Setup logger
    logger = TensorBoardLogger(output_dir / "logs", name="multi_dataset")
    
    # Determine device
    if args.use_gpu and torch.cuda.is_available():
        accelerator = "gpu"
        devices = torch.cuda.device_count()
        print(f"Using GPU training with {devices} device(s)")
    elif torch.backends.mps.is_available():
        accelerator = "mps"
        devices = 1
        print("Using Apple Metal (MPS) for training")
    else:
        accelerator = "cpu"
        devices = 1
        print("Using CPU for training")
    
    # Initialize trainer
    trainer = pl.Trainer(
        max_epochs=args.epochs,
        callbacks=callbacks,
        logger=logger,
        accelerator=accelerator,
        devices=devices,
        precision=args.precision,
        log_every_n_steps=10,
        num_sanity_val_steps=0  # Skip validation to avoid hangs
    )
    
    # Train model using radiography data
    print("\n==== Training on dental radiography dataset ====")
    trainer.fit(model, datamodule=radiography_data)
    
    # Save intermediate model
    torch.save(model.state_dict(), output_dir / "radiography_trained_model.pt")
    
    # Create and save inference model
    inference_model = {
        "model": model,
        "config": {
            "backbone": args.backbone,
            "num_classes": num_classes,
            "class_names": all_classes
        }
    }
    
    torch.save(inference_model, output_dir / "multi_dataset_model.pt")
    print(f"Multi-dataset model saved at {output_dir / 'multi_dataset_model.pt'}")
    
    # Print instructions
    print("\n=== Training completed ===")
    print("To use this model for inference:")
    print(f"python3 predict.py --model_path {output_dir / 'multi_dataset_model.pt'} --input your_image.jpg")

if __name__ == "__main__":
    main() 