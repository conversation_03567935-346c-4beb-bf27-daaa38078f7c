#!/usr/bin/env python3
"""
Oral Cancer Prediction

This script loads a trained oral cancer model and makes predictions
on new oral images to assess the risk of oral cancer.
"""

import os
import sys
import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
import argparse
import json
from pathlib import Path
from PIL import Image
import cv2
from torchvision import transforms, models
from sklearn.metrics import confusion_matrix, classification_report, accuracy_score

def parse_args():
    parser = argparse.ArgumentParser(description="Oral Cancer Prediction")
    parser.add_argument("--model_path", type=str, required=True,
                      help="Path to the trained model file")
    parser.add_argument("--input", type=str, required=True,
                      help="Path to input image or directory of images")
    parser.add_argument("--output_dir", type=str, default="./oral_cancer_predictions",
                      help="Directory to save prediction results")
    parser.add_argument("--device", type=str, default=None,
                      help="Device to use (cuda, mps, cpu, or None for auto-detection)")
    parser.add_argument("--threshold", type=float, default=0.5,
                      help="Threshold for positive classification")
    parser.add_argument("--show", action="store_true", default=False,
                      help="Show visualizations instead of saving them")
    return parser.parse_args()

def load_model(model_path, device):
    """Load the trained oral cancer model"""
    print(f"Loading model from {model_path}")
    
    try:
        checkpoint = torch.load(model_path, map_location=device)
        
        # Extract model information
        model_name = checkpoint.get('model_name', 'resnet50')
        model_image_size = checkpoint.get('image_size', 224)
        
        # Create model based on architecture from checkpoint
        if model_name == "resnet18":
            model = models.resnet18(weights=None)
            model.fc = nn.Linear(model.fc.in_features, 2)
        elif model_name == "resnet50":
            model = models.resnet50(weights=None)
            model.fc = nn.Linear(model.fc.in_features, 2)
        elif model_name == "densenet121":
            model = models.densenet121(weights=None)
            model.classifier = nn.Linear(model.classifier.in_features, 2)
        elif model_name == "efficientnet_b0":
            model = models.efficientnet_b0(weights=None)
            model.classifier[1] = nn.Linear(model.classifier[1].in_features, 2)
        else:
            model = models.resnet50(weights=None)
            model.fc = nn.Linear(model.fc.in_features, 2)
        
        # Load model state
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
        else:
            model.load_state_dict(checkpoint)
        
        model = model.to(device)
        model.eval()
        
        return model, model_image_size
        
    except Exception as e:
        print(f"Error loading model: {e}")
        sys.exit(1)

def process_image(image_path, image_size):
    """Process an image for model prediction"""
    try:
        # Load and transform image
        transform = transforms.Compose([
            transforms.Resize((image_size, image_size)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        image = Image.open(image_path).convert('RGB')
        transformed_image = transform(image)
        
        # Create batch dimension
        input_tensor = transformed_image.unsqueeze(0)
        
        return input_tensor, image
    
    except Exception as e:
        print(f"Error processing image {image_path}: {e}")
        return None, None

def make_prediction(model, input_tensor, device, threshold=0.5):
    """Make prediction with the model"""
    try:
        input_tensor = input_tensor.to(device)
        
        with torch.no_grad():
            # Forward pass
            outputs = model(input_tensor)
            probabilities = torch.softmax(outputs, dim=1)
            
            # Get prediction
            cancer_probability = probabilities[0, 1].item()
            predicted_class = 1 if cancer_probability >= threshold else 0
            
            result = {
                'predicted_class': predicted_class,
                'class_name': 'Cancer' if predicted_class == 1 else 'Non-Cancer',
                'cancer_probability': cancer_probability,
                'non_cancer_probability': probabilities[0, 0].item()
            }
            
            return result
    
    except Exception as e:
        print(f"Error making prediction: {e}")
        return None

def visualize_prediction(image, result, output_path=None, show=False):
    """Visualize prediction result on the image"""
    # Convert PIL image to numpy array for OpenCV
    if isinstance(image, Image.Image):
        image = np.array(image)
        image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
    
    # Create a copy of the image
    vis_image = image.copy()
    
    # Get prediction information
    predicted_class = result['predicted_class']
    cancer_probability = result['cancer_probability']
    
    # Create text labels
    class_name = "Cancer" if predicted_class == 1 else "Non-Cancer"
    color = (0, 0, 255) if predicted_class == 1 else (0, 255, 0)  # Red for cancer, green for non-cancer
    
    # Add overlay and text
    h, w = vis_image.shape[:2]
    
    # Add semi-transparent overlay at the bottom
    overlay = vis_image.copy()
    cv2.rectangle(overlay, (0, h-60), (w, h), (0, 0, 0), -1)
    cv2.addWeighted(overlay, 0.7, vis_image, 0.3, 0, vis_image)
    
    # Add prediction text
    font = cv2.FONT_HERSHEY_SIMPLEX
    text = f"Prediction: {class_name} ({cancer_probability:.2%})"
    cv2.putText(vis_image, text, (10, h-25), font, 0.8, color, 2)
    
    # Add risk level
    risk_level = "HIGH RISK" if cancer_probability > 0.7 else "MEDIUM RISK" if cancer_probability > 0.3 else "LOW RISK"
    risk_color = (0, 0, 255) if cancer_probability > 0.7 else (0, 165, 255) if cancer_probability > 0.3 else (0, 255, 0)
    cv2.putText(vis_image, risk_level, (10, h-60+25), font, 0.8, risk_color, 2)
    
    # Save or show the visualization
    if output_path:
        cv2.imwrite(output_path, vis_image)
    
    if show:
        plt.figure(figsize=(10, 8))
        plt.imshow(cv2.cvtColor(vis_image, cv2.COLOR_BGR2RGB))
        plt.axis('off')
        plt.tight_layout()
        plt.show()
    
    return vis_image

def process_directory(input_path, model, image_size, device, output_dir, threshold, show):
    """Process all images in a directory"""
    results = []
    
    # Get all image files in directory
    image_files = []
    for root, _, files in os.walk(input_path):
        for file in files:
            if file.lower().endswith(('.png', '.jpg', '.jpeg')):
                image_files.append(os.path.join(root, file))
    
    print(f"Found {len(image_files)} images in {input_path}")
    
    # Process each image
    for img_path in image_files:
        print(f"Processing {img_path}...")
        
        # Process image
        input_tensor, image = process_image(img_path, image_size)
        if input_tensor is None:
            continue
        
        # Make prediction
        prediction = make_prediction(model, input_tensor, device, threshold)
        if prediction is None:
            continue
        
        # Add file info to prediction
        prediction['file_path'] = img_path
        prediction['file_name'] = os.path.basename(img_path)
        results.append(prediction)
        
        # Save visualization
        if output_dir:
            base_name = os.path.splitext(os.path.basename(img_path))[0]
            vis_path = os.path.join(output_dir, f"{base_name}_prediction.jpg")
            visualize_prediction(image, prediction, vis_path, show)
    
    return results

def create_summary_report(results, output_dir, show=False):
    """Create summary report of predictions"""
    if not results:
        print("No results to summarize")
        return
    
    # Count predictions
    total = len(results)
    cancer_count = sum(1 for r in results if r['predicted_class'] == 1)
    non_cancer_count = total - cancer_count
    
    # Create pie chart
    plt.figure(figsize=(10, 6))
    plt.pie([cancer_count, non_cancer_count], 
            labels=['Cancer', 'Non-Cancer'],
            autopct='%1.1f%%',
            colors=['red', 'green'],
            explode=(0.1, 0))
    plt.title('Prediction Distribution')
    
    if output_dir:
        plt.savefig(os.path.join(output_dir, "prediction_distribution.png"))
    
    if show:
        plt.show()
    else:
        plt.close()
    
    # Create bar chart of probabilities
    plt.figure(figsize=(12, 6))
    
    # Sort by cancer probability
    sorted_results = sorted(results, key=lambda x: x['cancer_probability'], reverse=True)
    
    # Limit to top 20 for readability
    if len(sorted_results) > 20:
        sorted_results = sorted_results[:20]
    
    names = [r.get('file_name', f"Image {i+1}") for i, r in enumerate(sorted_results)]
    cancer_probs = [r['cancer_probability'] for r in sorted_results]
    non_cancer_probs = [r['non_cancer_probability'] for r in sorted_results]
    
    # Create stacked bars
    indices = range(len(names))
    plt.bar(indices, non_cancer_probs, label='Non-Cancer')
    plt.bar(indices, cancer_probs, bottom=non_cancer_probs, color='red', label='Cancer')
    
    plt.xlabel('Images')
    plt.ylabel('Probability')
    plt.title('Cancer Probability by Image')
    plt.xticks(indices, names, rotation=45, ha='right')
    plt.legend()
    plt.tight_layout()
    
    if output_dir:
        plt.savefig(os.path.join(output_dir, "cancer_probabilities.png"))
    
    if show:
        plt.show()
    else:
        plt.close()
    
    # Create detailed report
    report = {
        'total_images': total,
        'cancer_count': cancer_count,
        'non_cancer_count': non_cancer_count,
        'cancer_percentage': cancer_count / total * 100 if total > 0 else 0,
        'high_risk_count': sum(1 for r in results if r['cancer_probability'] > 0.7),
        'medium_risk_count': sum(1 for r in results if 0.3 < r['cancer_probability'] <= 0.7),
        'low_risk_count': sum(1 for r in results if r['cancer_probability'] <= 0.3),
        'average_cancer_probability': sum(r['cancer_probability'] for r in results) / total if total > 0 else 0,
        'image_details': results
    }
    
    # Save report
    if output_dir:
        with open(os.path.join(output_dir, "prediction_report.json"), 'w') as f:
            json.dump(report, f, indent=4)
    
    # Print summary
    print("\nPrediction Summary:")
    print(f"Total images: {total}")
    print(f"Cancer detected: {cancer_count} ({cancer_count/total*100:.1f}%)")
    print(f"Non-cancer: {non_cancer_count} ({non_cancer_count/total*100:.1f}%)")
    print(f"High risk: {report['high_risk_count']} ({report['high_risk_count']/total*100:.1f}%)")
    print(f"Medium risk: {report['medium_risk_count']} ({report['medium_risk_count']/total*100:.1f}%)")
    print(f"Low risk: {report['low_risk_count']} ({report['low_risk_count']/total*100:.1f}%)")
    
    return report

def main():
    args = parse_args()
    
    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Set device
    if args.device:
        device = torch.device(args.device)
    else:
        device = torch.device("cuda" if torch.cuda.is_available() else 
                             "mps" if torch.backends.mps.is_available() else 
                             "cpu")
    print(f"Using device: {device}")
    
    # Load model
    model, image_size = load_model(args.model_path, device)
    
    # Check if input is a directory or a single file
    if os.path.isdir(args.input):
        # Process directory
        results = process_directory(args.input, model, image_size, device, 
                                   args.output_dir, args.threshold, args.show)
        
        # Create summary report
        create_summary_report(results, args.output_dir, args.show)
    else:
        # Process single image
        print(f"Processing single image: {args.input}")
        
        # Process image
        input_tensor, image = process_image(args.input, image_size)
        if input_tensor is None:
            sys.exit(1)
        
        # Make prediction
        prediction = make_prediction(model, input_tensor, device, args.threshold)
        if prediction is None:
            sys.exit(1)
        
        # Add file info to prediction
        prediction['file_path'] = args.input
        prediction['file_name'] = os.path.basename(args.input)
        
        # Print result
        print("\nPrediction Result:")
        print(f"Class: {prediction['class_name']}")
        print(f"Cancer Probability: {prediction['cancer_probability']:.2%}")
        print(f"Non-Cancer Probability: {prediction['non_cancer_probability']:.2%}")
        
        # Save or show visualization
        if args.output_dir:
            base_name = os.path.splitext(os.path.basename(args.input))[0]
            vis_path = os.path.join(args.output_dir, f"{base_name}_prediction.jpg")
            visualize_prediction(image, prediction, vis_path, args.show)
            
            # Save prediction to JSON
            with open(os.path.join(args.output_dir, f"{base_name}_prediction.json"), 'w') as f:
                json.dump(prediction, f, indent=4)
        
        print(f"\nResults saved to {args.output_dir}")

if __name__ == "__main__":
    main() 