#!/usr/bin/env python3
"""
Model evaluation script for dental AI models.
This script evaluates the performance of trained models on different datasets.
"""

import os
import torch
import torchvision
import torchvision.transforms as transforms
from PIL import Image
import matplotlib.pyplot as plt
import argparse
import json
from pathlib import Path
import numpy as np
from sklearn.metrics import confusion_matrix, classification_report, accuracy_score
from tqdm import tqdm
import pandas as pd
import cv2
from torchvision.models.detection.faster_rcnn import FastRCNNPredictor

def parse_args():
    parser = argparse.ArgumentParser(description="Dental AI Model Evaluation")
    parser.add_argument("--detection_model", type=str, default=None, 
                      help="Path to dental radiography detection model")
    parser.add_argument("--classification_model", type=str, default=None,
                      help="Path to cavity classification model")
    parser.add_argument("--radiography_dataset", type=str, default=None,
                      help="Path to dental radiography dataset test folder")
    parser.add_argument("--cavity_dataset", type=str, default=None,
                      help="Path to cavity classification dataset root")
    parser.add_argument("--output_dir", type=str, default="./evaluation_results",
                      help="Directory to save evaluation results")
    parser.add_argument("--detection_threshold", type=float, default=0.5,
                      help="Detection threshold for radiography model")
    parser.add_argument("--batch_size", type=int, default=16,
                      help="Batch size for evaluation")
    return parser.parse_args()

def load_detection_model(model_path):
    """Load the dental radiography detection model"""
    if not model_path or not os.path.exists(model_path):
        return None
    
    print(f"Loading detection model from {model_path}")
    
    # Initialize model architecture (Faster R-CNN with ResNet-50 backbone)
    model = torchvision.models.detection.fasterrcnn_resnet50_fpn(pretrained=False)
    
    # Update the box predictor for dental classes
    num_classes = 5  # Background + 4 dental conditions
    in_features = model.roi_heads.box_predictor.cls_score.in_features
    model.roi_heads.box_predictor = FastRCNNPredictor(in_features, num_classes)
    
    # Load the state dict
    try:
        model.load_state_dict(torch.load(model_path, map_location=torch.device('cpu')))
    except Exception as e:
        print(f"Error loading detection model: {e}")
        try:
            # Try alternative loading method
            checkpoint = torch.load(model_path, map_location=torch.device('cpu'))
            if 'model_state_dict' in checkpoint:
                model.load_state_dict(checkpoint['model_state_dict'])
            elif 'model' in checkpoint:
                model.load_state_dict(checkpoint['model'])
        except Exception as e:
            print(f"Failed to load model: {e}")
            return None
    
    model.eval()
    return model

def load_classification_model(model_path):
    """Load the cavity classification model"""
    if not model_path or not os.path.exists(model_path):
        return None, None
    
    print(f"Loading classification model from {model_path}")
    
    # Default values
    model_type = 'resnet18'
    num_classes = 2
    class_names = ['cavity', 'no_cavity']
    
    # Check for model_info.json
    model_info_path = os.path.dirname(model_path) + '/model_info.json'
    if os.path.exists(model_info_path):
        with open(model_info_path, 'r') as f:
            model_info = json.load(f)
        model_type = model_info.get('model_type', model_type)
        num_classes = model_info.get('num_classes', num_classes)
        class_names = model_info.get('class_names', class_names)
    
    # Initialize model architecture
    if model_type == 'resnet18':
        model = torchvision.models.resnet18(weights=None)
        model.fc = torch.nn.Linear(model.fc.in_features, num_classes)
    elif model_type == 'mobilenet':
        model = torchvision.models.mobilenet_v2(weights=None)
        model.classifier[1] = torch.nn.Linear(model.classifier[1].in_features, num_classes)
    elif model_type == 'efficientnet':
        model = torchvision.models.efficientnet_b0(weights=None)
        model.classifier[1] = torch.nn.Linear(model.classifier[1].in_features, num_classes)
    
    # Load the state dict
    try:
        model.load_state_dict(torch.load(model_path, map_location=torch.device('cpu')))
    except Exception as e:
        print(f"Error loading classification model: {e}")
        try:
            # Try alternative loading method
            checkpoint = torch.load(model_path, map_location=torch.device('cpu'))
            if 'model_state_dict' in checkpoint:
                model.load_state_dict(checkpoint['model_state_dict'])
            elif 'model' in checkpoint:
                model.load_state_dict(checkpoint['model'])
        except Exception as e:
            print(f"Failed to load model: {e}")
            return None, None
    
    model.eval()
    return model, class_names

def create_cavity_dataset_loaders(cavity_dataset_root, batch_size=16):
    """Create dataloaders for cavity classification dataset evaluation"""
    if not cavity_dataset_root or not os.path.exists(cavity_dataset_root):
        return None, None, None
    
    transform = transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.ToTensor(),
        transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
    ])
    
    # Try to find cavity and no_cavity directories
    cavity_dir = os.path.join(cavity_dataset_root, 'cavity')
    no_cavity_dir = os.path.join(cavity_dataset_root, 'no_cavity')
    
    if not os.path.exists(cavity_dir) or not os.path.exists(no_cavity_dir):
        print(f"Could not find cavity/no_cavity directories in {cavity_dataset_root}")
        return None, None, None
    
    # Load the dataset
    print(f"Loading cavity classification dataset from {cavity_dataset_root}")
    try:
        dataset = torchvision.datasets.ImageFolder(root=cavity_dataset_root, transform=transform)
        
        # Split into train, validation, test (70%, 15%, 15%)
        total_size = len(dataset)
        train_size = int(0.7 * total_size)
        val_size = int(0.15 * total_size)
        test_size = total_size - train_size - val_size
        
        train_dataset, val_dataset, test_dataset = torch.utils.data.random_split(
            dataset, [train_size, val_size, test_size]
        )
        
        # Create dataloaders
        test_loader = torch.utils.data.DataLoader(
            test_dataset, batch_size=batch_size, shuffle=False, num_workers=2
        )
        
        # Return class information
        class_to_idx = dataset.class_to_idx
        idx_to_class = {v: k for k, v in class_to_idx.items()}
        
        print(f"Cavity classification dataset: {total_size} images, {test_size} test images")
        print(f"Classes: {class_to_idx}")
        
        return test_loader, idx_to_class, test_dataset
    except Exception as e:
        print(f"Error creating cavity dataset loaders: {e}")
        return None, None, None

def load_radiography_annotations(radiography_dataset_path):
    """Load annotations for the dental radiography dataset"""
    if not radiography_dataset_path or not os.path.exists(radiography_dataset_path):
        return None, None
    
    # Look for annotations.json in the test folder
    test_dir = os.path.join(radiography_dataset_path, 'test')
    if os.path.exists(test_dir):
        annotations_path = os.path.join(test_dir, 'annotations.json')
    else:
        # If no test folder, try directly
        annotations_path = os.path.join(radiography_dataset_path, 'annotations.json')
    
    if not os.path.exists(annotations_path):
        print(f"Could not find annotations at {annotations_path}")
        return None, None
    
    try:
        with open(annotations_path, 'r') as f:
            annotations = json.load(f)
        
        # Get class mapping
        categories = {cat['id']: cat['name'] for cat in annotations['categories']}
        
        # Group annotations by image
        image_annotations = {}
        for ann in annotations['annotations']:
            image_id = ann['image_id']
            if image_id not in image_annotations:
                image_annotations[image_id] = []
            image_annotations[image_id].append({
                'bbox': ann['bbox'],
                'category_id': ann['category_id'],
                'category_name': categories[ann['category_id']]
            })
        
        # Map image IDs to file paths
        image_paths = {}
        for img in annotations['images']:
            image_paths[img['id']] = os.path.join(os.path.dirname(annotations_path), img['file_name'])
        
        print(f"Loaded {len(annotations['images'])} images with annotations")
        print(f"Categories: {categories}")
        
        return image_annotations, image_paths
    except Exception as e:
        print(f"Error loading radiography annotations: {e}")
        return None, None

def evaluate_classification_model(model, test_loader, idx_to_class, device, output_dir):
    """Evaluate cavity classification model"""
    if model is None or test_loader is None:
        return
    
    model = model.to(device)
    
    # Track predictions and ground truth
    all_preds = []
    all_labels = []
    
    # Evaluate on test set
    print("Evaluating cavity classification model...")
    with torch.no_grad():
        for images, labels in tqdm(test_loader):
            images = images.to(device)
            outputs = model(images)
            _, preds = torch.max(outputs, 1)
            
            all_preds.extend(preds.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
    
    # Calculate metrics
    accuracy = accuracy_score(all_labels, all_preds)
    conf_matrix = confusion_matrix(all_labels, all_preds)
    class_report = classification_report(all_labels, all_preds, target_names=[idx_to_class[i] for i in range(len(idx_to_class))])
    
    # Save results
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    
    report = {
        'accuracy': float(accuracy),
        'confusion_matrix': conf_matrix.tolist(),
        'classification_report': class_report
    }
    
    with open(os.path.join(output_dir, 'classification_results.json'), 'w') as f:
        json.dump(report, f, indent=4)
    
    # Print summary
    print(f"Classification Model Evaluation:")
    print(f"Accuracy: {accuracy:.4f}")
    print(f"Confusion Matrix:")
    print(conf_matrix)
    print(f"Classification Report:")
    print(class_report)
    
    # Plot confusion matrix
    plt.figure(figsize=(8, 6))
    plt.imshow(conf_matrix, interpolation='nearest', cmap=plt.cm.Blues)
    plt.title('Confusion Matrix')
    plt.colorbar()
    
    classes = [idx_to_class[i] for i in range(len(idx_to_class))]
    tick_marks = np.arange(len(classes))
    plt.xticks(tick_marks, classes, rotation=45)
    plt.yticks(tick_marks, classes)
    
    # Add text annotations
    thresh = conf_matrix.max() / 2.0
    for i in range(conf_matrix.shape[0]):
        for j in range(conf_matrix.shape[1]):
            plt.text(j, i, str(conf_matrix[i, j]),
                    horizontalalignment="center",
                    color="white" if conf_matrix[i, j] > thresh else "black")
    
    plt.tight_layout()
    plt.ylabel('True label')
    plt.xlabel('Predicted label')
    plt.savefig(os.path.join(output_dir, 'confusion_matrix.png'))
    
    return report

def evaluate_detection_model(model, image_annotations, image_paths, device, threshold, output_dir):
    """Evaluate dental radiography detection model"""
    if model is None or image_annotations is None or image_paths is None:
        return
    
    model = model.to(device)
    
    # Define dental classes
    dental_classes = {
        1: "implant",
        2: "cavity",
        3: "filling",
        4: "impacted_tooth"
    }
    
    # Initialize metrics
    true_positives = {class_id: 0 for class_id in dental_classes}
    false_positives = {class_id: 0 for class_id in dental_classes}
    false_negatives = {class_id: 0 for class_id in dental_classes}
    
    # Track IoU values
    iou_values = {class_id: [] for class_id in dental_classes}
    
    # Create output directory for visualizations
    vis_dir = os.path.join(output_dir, 'detection_visualizations')
    Path(vis_dir).mkdir(parents=True, exist_ok=True)
    
    # Calculate intersection over union
    def compute_iou(box1, box2):
        # Convert from [x, y, w, h] to [x1, y1, x2, y2]
        b1_x1, b1_y1, b1_w, b1_h = box1
        b1_x2, b1_y2 = b1_x1 + b1_w, b1_y1 + b1_h
        
        b2_x1, b2_y1, b2_w, b2_h = box2
        b2_x2, b2_y2 = b2_x1 + b2_w, b2_y1 + b2_h
        
        # Calculate intersection area
        x_left = max(b1_x1, b2_x1)
        y_top = max(b1_y1, b2_y1)
        x_right = min(b1_x2, b2_x2)
        y_bottom = min(b1_y2, b2_y2)
        
        if x_right < x_left or y_bottom < y_top:
            return 0.0
        
        intersection_area = (x_right - x_left) * (y_bottom - y_top)
        
        # Calculate union area
        b1_area = b1_w * b1_h
        b2_area = b2_w * b2_h
        union_area = b1_area + b2_area - intersection_area
        
        iou = intersection_area / union_area if union_area > 0 else 0
        return iou
    
    # Evaluate on test images
    print("Evaluating detection model...")
    
    # Select a subset of images for visualization
    vis_image_ids = list(image_annotations.keys())[:10]  # First 10 images
    
    for image_id, annotations in tqdm(image_annotations.items()):
        if image_id not in image_paths:
            continue
        
        image_path = image_paths[image_id]
        if not os.path.exists(image_path):
            continue
        
        # Load and preprocess image
        image = Image.open(image_path).convert("RGB")
        img_tensor = transforms.functional.to_tensor(image)
        
        # Get ground truth boxes and classes
        gt_boxes = []
        gt_classes = []
        for ann in annotations:
            gt_boxes.append(ann['bbox'])
            gt_classes.append(ann['category_id'])
        
        # Run model inference
        with torch.no_grad():
            img_tensor = img_tensor.to(device)
            predictions = model([img_tensor])
        
        # Extract model predictions
        pred_boxes = predictions[0]['boxes'].cpu().numpy()
        pred_scores = predictions[0]['scores'].cpu().numpy()
        pred_labels = predictions[0]['labels'].cpu().numpy()
        
        # Filter predictions by threshold
        mask = pred_scores >= threshold
        pred_boxes = pred_boxes[mask]
        pred_scores = pred_scores[mask]
        pred_labels = pred_labels[mask]
        
        # Convert detection format from [x1, y1, x2, y2] to [x, y, w, h]
        pred_boxes_xywh = []
        for box in pred_boxes:
            x1, y1, x2, y2 = box
            pred_boxes_xywh.append([x1, y1, x2 - x1, y2 - y1])
        
        # Match predictions to ground truth
        pred_matched = [False] * len(pred_boxes_xywh)
        gt_matched = [False] * len(gt_boxes)
        
        # For each ground truth box, find the best matching prediction
        for gt_idx, (gt_box, gt_class) in enumerate(zip(gt_boxes, gt_classes)):
            best_iou = 0.5  # IoU threshold
            best_pred_idx = -1
            
            for pred_idx, (pred_box, pred_label) in enumerate(zip(pred_boxes_xywh, pred_labels)):
                if pred_matched[pred_idx] or pred_label != gt_class:
                    continue
                
                iou = compute_iou(gt_box, pred_box)
                if iou > best_iou:
                    best_iou = iou
                    best_pred_idx = pred_idx
            
            if best_pred_idx >= 0:
                # Found a match
                pred_matched[best_pred_idx] = True
                gt_matched[gt_idx] = True
                true_positives[gt_class] += 1
                iou_values[gt_class].append(best_iou)
            else:
                # No match found
                false_negatives[gt_class] += 1
        
        # Count false positives
        for pred_idx, (pred_matched_flag, pred_label) in enumerate(zip(pred_matched, pred_labels)):
            if not pred_matched_flag:
                false_positives[pred_label] += 1
        
        # Create visualization for sampled images
        if image_id in vis_image_ids:
            # Create a visualization
            img_cv = np.array(image)
            img_cv = img_cv[:, :, ::-1].copy()  # RGB to BGR
            
            # Draw ground truth boxes (green)
            for gt_box, gt_class, matched in zip(gt_boxes, gt_classes, gt_matched):
                x, y, w, h = [int(c) for c in gt_box]
                color = (0, 255, 0) if matched else (0, 255, 255)  # Green if matched, yellow if missed
                cv2.rectangle(img_cv, (x, y), (x + w, y + h), color, 2)
                label = dental_classes.get(gt_class, f"class_{gt_class}")
                cv2.putText(img_cv, f"GT: {label}", (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
            
            # Draw prediction boxes (blue if TP, red if FP)
            for pred_box, pred_label, pred_score, matched in zip(pred_boxes, pred_labels, pred_scores, pred_matched):
                x1, y1, x2, y2 = [int(c) for c in pred_box]
                color = (255, 0, 0) if matched else (0, 0, 255)  # Blue if true positive, red if false positive
                cv2.rectangle(img_cv, (x1, y1), (x2, y2), color, 2)
                label = dental_classes.get(pred_label, f"class_{pred_label}")
                cv2.putText(img_cv, f"Pred: {label} ({pred_score:.2f})", (x1, y2 + 20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
            
            # Convert back to RGB for saving
            img_rgb = cv2.cvtColor(img_cv, cv2.COLOR_BGR2RGB)
            
            # Save visualization
            vis_path = os.path.join(vis_dir, f"detection_{image_id}.png")
            plt.figure(figsize=(12, 12))
            plt.imshow(img_rgb)
            plt.axis('off')
            plt.title(f"Image ID: {image_id}")
            plt.savefig(vis_path, bbox_inches='tight')
            plt.close()
    
    # Calculate metrics
    precision = {}
    recall = {}
    f1_score = {}
    
    for class_id in dental_classes:
        tp = true_positives[class_id]
        fp = false_positives[class_id]
        fn = false_negatives[class_id]
        
        precision[class_id] = tp / (tp + fp) if (tp + fp) > 0 else 0
        recall[class_id] = tp / (tp + fn) if (tp + fn) > 0 else 0
        f1_score[class_id] = 2 * precision[class_id] * recall[class_id] / (precision[class_id] + recall[class_id]) if (precision[class_id] + recall[class_id]) > 0 else 0
    
    # Calculate mean IoU
    mean_iou = {}
    for class_id in dental_classes:
        if iou_values[class_id]:
            mean_iou[class_id] = sum(iou_values[class_id]) / len(iou_values[class_id])
        else:
            mean_iou[class_id] = 0
    
    # Save results
    report = {
        'threshold': threshold,
        'true_positives': true_positives,
        'false_positives': false_positives,
        'false_negatives': false_negatives,
        'precision': precision,
        'recall': recall,
        'f1_score': f1_score,
        'mean_iou': mean_iou
    }
    
    with open(os.path.join(output_dir, 'detection_results.json'), 'w') as f:
        json.dump(report, f, indent=4)
    
    # Print summary
    print(f"Detection Model Evaluation (threshold: {threshold}):")
    print("Class\tPrecision\tRecall\tF1-Score\tMean IoU")
    for class_id, name in dental_classes.items():
        print(f"{name}\t{precision[class_id]:.4f}\t{recall[class_id]:.4f}\t{f1_score[class_id]:.4f}\t{mean_iou[class_id]:.4f}")
    
    # Calculate mean across classes
    mean_precision = sum(precision.values()) / len(precision)
    mean_recall = sum(recall.values()) / len(recall)
    mean_f1 = sum(f1_score.values()) / len(f1_score)
    overall_mean_iou = sum(mean_iou.values()) / len(mean_iou)
    
    print(f"Mean\t{mean_precision:.4f}\t{mean_recall:.4f}\t{mean_f1:.4f}\t{overall_mean_iou:.4f}")
    
    # Create performance visualization
    plt.figure(figsize=(12, 8))
    x = list(dental_classes.values())
    width = 0.2
    
    plt.bar([i - width for i in range(len(x))], [precision[i+1] for i in range(len(x))], width=width, label='Precision')
    plt.bar([i for i in range(len(x))], [recall[i+1] for i in range(len(x))], width=width, label='Recall')
    plt.bar([i + width for i in range(len(x))], [f1_score[i+1] for i in range(len(x))], width=width, label='F1-Score')
    
    plt.xlabel('Class')
    plt.ylabel('Score')
    plt.title('Detection Performance by Class')
    plt.xticks(range(len(x)), x)
    plt.legend()
    plt.savefig(os.path.join(output_dir, 'detection_performance.png'))
    plt.close()
    
    return report

def main():
    args = parse_args()
    
    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Set device
    device = torch.device("cuda" if torch.cuda.is_available() else 
                         "mps" if torch.backends.mps.is_available() else 
                         "cpu")
    print(f"Using device: {device}")
    
    # Load models
    detection_model = load_detection_model(args.detection_model)
    classification_model, class_names = load_classification_model(args.classification_model)
    
    # Check if at least one model is loaded
    if detection_model is None and classification_model is None:
        print("No models were loaded successfully. Exiting.")
        return
    
    # Start time tracking
    import time
    start_time = time.time()
    
    # Evaluate cavity classification model
    if classification_model is not None and args.cavity_dataset:
        test_loader, idx_to_class, test_dataset = create_cavity_dataset_loaders(
            args.cavity_dataset, args.batch_size
        )
        
        if test_loader is not None:
            classification_report = evaluate_classification_model(
                classification_model, test_loader, idx_to_class, device, output_dir
            )
    
    # Evaluate dental radiography detection model
    if detection_model is not None and args.radiography_dataset:
        image_annotations, image_paths = load_radiography_annotations(args.radiography_dataset)
        
        if image_annotations is not None and image_paths is not None:
            detection_report = evaluate_detection_model(
                detection_model, image_annotations, image_paths, device, 
                args.detection_threshold, output_dir
            )
    
    # Generate combined report
    combined_results = {
        'evaluation_time': time.time() - start_time,
        'models_evaluated': {
            'detection_model': args.detection_model is not None,
            'classification_model': args.classification_model is not None
        },
        'datasets_used': {
            'radiography_dataset': args.radiography_dataset,
            'cavity_dataset': args.cavity_dataset
        }
    }
    
    with open(os.path.join(output_dir, 'evaluation_summary.json'), 'w') as f:
        json.dump(combined_results, f, indent=4)
    
    print(f"\nEvaluation completed! Results saved to {output_dir}")
    print(f"Total evaluation time: {time.time() - start_time:.2f} seconds")

if __name__ == "__main__":
    main() 