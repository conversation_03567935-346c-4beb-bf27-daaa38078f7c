import kagglehub
import os
import sys

def download_dental_dataset():
    """
    Downloads the dental radiography dataset from <PERSON><PERSON> and returns the path.
    This dataset will be used for AI training on dental images.
    """
    try:
        # Download latest version
        path = kagglehub.dataset_download("imtkaggleteam/dental-radiography")
        print("Path to dataset files:", path)
        return path
    except Exception as e:
        print(f"Error downloading dataset: {e}")
        return None

if __name__ == "__main__":
    download_dental_dataset() 