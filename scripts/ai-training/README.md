# Advanced Dental Radiography AI Training Pipeline

This directory contains a comprehensive, production-grade AI training pipeline for dental radiography image analysis. The system is designed to detect and classify various dental conditions like implants, caries, fillings, and other features in dental X-ray images.

## Table of Contents

- [System Overview](#system-overview)
- [Requirements](#requirements)
- [Dataset](#dataset)
- [Getting Started](#getting-started)
- [Training](#training)
- [Evaluation](#evaluation)
- [Prediction](#prediction)
- [Model Architecture](#model-architecture)
- [Experiment Tracking](#experiment-tracking)
- [Best Practices](#best-practices)
- [Performance Optimization](#performance-optimization)
- [Troubleshooting](#troubleshooting)

## System Overview

This AI system implements a state-of-the-art object detection model based on Faster R-CNN architecture with various backbone options (ResNet50, EfficientNet, DenseNet). The pipeline includes:

- Data downloading and preparation
- Data augmentation and preprocessing
- Model training with various backbones
- Hyperparameter optimization
- Evaluation and visualization
- Model export and deployment
- Inference on new images

## Requirements

To install all required dependencies:

```bash
# Basic requirements
python3 -m pip install -r requirements.txt

# Training requirements
python3 -m pip install -r requirements_training.txt
```

The system requires:
- Python 3.8+
- PyTorch 2.0+
- CUDA-capable GPU for efficient training

## Dataset

This pipeline uses the "Dental Radiography" dataset from Kaggle, which contains labeled dental X-ray images with bounding box annotations for various dental conditions:

- Implants
- Caries (cavities)
- Fillings
- Root canals
- Periapical lesions
- Crowns
- Impacted teeth

### Dataset Structure

The dataset is organized into train/valid/test splits with the following structure:
- `train/`: Contains training images (~1000+ images)
- `valid/`: Contains validation images (~120+ images)
- `test/`: Contains test images (~70+ images)

Each split includes an `_annotations.csv` file with bounding box annotations.

### Download the Dataset

```bash
python3 download_dental_radiography.py
```

### Visualize the Dataset

```bash
python3 visualize_dental_data.py
```

## Getting Started

1. Download the dataset:
   ```bash
   python3 download_dental_radiography.py
   ```

2. Visualize sample images:
   ```bash
   python3 visualize_dental_data.py
   ```

3. Train a model:
   ```bash
   python3 train.py --backbone resnet50 --batch_size 16 --epochs 100
   ```

4. Run inference:
   ```bash
   python3 predict.py --model_path ./results/inference_model.pt --input /path/to/image.jpg
   ```

## Training

The training script (`train.py`) supports numerous parameters for customization:

```bash
python3 train.py \
  --backbone resnet50 \
  --batch_size 16 \
  --learning_rate 3e-4 \
  --epochs 100 \
  --aug_intensity medium \
  --optimizer AdamW \
  --scheduler ReduceLROnPlateau \
  --use_wandb  # Enable Weights & Biases tracking
```

### Key Training Parameters

- `--backbone`: Model backbone (resnet50, efficientnet_b3, densenet121)
- `--batch_size`: Training batch size
- `--learning_rate`: Initial learning rate
- `--epochs`: Number of training epochs
- `--aug_intensity`: Data augmentation intensity (none, light, medium, heavy)
- `--optimizer`: Optimizer to use (Adam, AdamW, SGD)
- `--scheduler`: Learning rate scheduler
- `--pretrained`: Use pretrained weights
- `--feature_extract`: Only update the new layers params
- `--use_ema`: Use Exponential Moving Average for model weights

## Evaluation

The system automatically evaluates model performance using Mean Average Precision (mAP) metrics:

- mAP@50: Mean Average Precision with IoU threshold of 0.5
- mAP@50:95: Mean Average Precision averaged over IoU thresholds from 0.5 to 0.95

Results are saved in the `results/` directory, including:
- Metrics in JSON format
- Visualizations of predictions
- Model checkpoint files

## Prediction

To run inference on new images:

```bash
python3 predict.py \
  --model_path ./results/inference_model.pt \
  --input /path/to/image.jpg \
  --confidence 0.5 \
  --output_dir predictions \
  --save_json
```

Or to process a directory of images:

```bash
python3 predict.py \
  --model_path ./results/inference_model.pt \
  --input /path/to/images/ \
  --output_dir predictions
```

## Model Architecture

The system implements a Faster R-CNN architecture with customizable backbones:

- **Backbones**: 
  - ResNet50/101 (default)
  - EfficientNet-B0/B3
  - DenseNet121/169

- **Detection Head**:
  - Region Proposal Network (RPN)
  - ROI Align pooling
  - Box regression and classification heads

- **Features**:
  - Class-weighted loss functions
  - Gradient clipping
  - Learning rate scheduling
  - Early stopping
  - Mixed precision training
  - Exponential Moving Average (EMA)

## Experiment Tracking

The system supports experiment tracking with:

1. **TensorBoard** (default):
   ```
   tensorboard --logdir logs/
   ```

2. **Weights & Biases**:
   ```bash
   python3 train.py --use_wandb --wandb_project dental-radiography
   ```

## Best Practices

This pipeline implements several best practices for deep learning:

- **Reproducibility**: Fixed random seeds for deterministic results
- **Scalability**: Data loader workers and GPU support
- **Monitoring**: Comprehensive logging and visualization
- **Modularity**: Clean separation of data, model, and training code
- **Evaluation**: Standard object detection metrics (mAP)
- **Deployment**: Model export to TorchScript and ONNX formats

## Performance Optimization

- **Mixed Precision Training**: Use `--precision 16` to enable FP16 training
- **Efficient Data Loading**: Set `--num_workers` based on your CPU cores
- **Gradient Accumulation**: For larger effective batch sizes
- **EMA**: Use `--use_ema` to enable Exponential Moving Average of weights

## Troubleshooting

- **Out of Memory Errors**: Reduce batch size or use gradient accumulation
- **Slow Training**: Ensure CUDA is properly set up, increase workers
- **Poor Performance**: Try different backbones, adjust learning rate
- **Overfitting**: Increase augmentation intensity, add regularization

For more detailed information, refer to the comments in individual Python modules.

## License

This project is for educational and research purposes only. The dental radiography dataset is provided by Kaggle and subject to its terms of use. 