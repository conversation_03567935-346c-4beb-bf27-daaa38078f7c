#!/usr/bin/env python3
"""
Facial Analysis Prediction for Dental Health

This script loads a trained facial analysis model and makes predictions on
new facial images to identify dental-related features and potential issues.
"""

import os
import sys
import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
import argparse
import json
from pathlib import Path
from PIL import Image
import cv2
from torchvision import transforms, models
import face_recognition
import time
import dlib

def parse_args():
    parser = argparse.ArgumentParser(description="Facial Analysis Prediction for Dental Health")
    parser.add_argument("--model_path", type=str, required=True,
                      help="Path to the trained model file")
    parser.add_argument("--input", type=str, required=True,
                      help="Path to input image or directory of images")
    parser.add_argument("--output_dir", type=str, default="./facial_analysis_predictions",
                      help="Directory to save prediction results")
    parser.add_argument("--device", type=str, default=None,
                      help="Device to use (cuda, mps, cpu, or None for auto-detection)")
    parser.add_argument("--threshold", type=float, default=0.5,
                      help="Threshold for issue classification")
    parser.add_argument("--show", action="store_true", default=False,
                      help="Show visualizations instead of saving them")
    parser.add_argument("--detailed", action="store_true", default=False,
                      help="Show detailed facial feature analysis")
    return parser.parse_args()

def load_model(model_path, device):
    """Load the trained facial analysis model"""
    print(f"Loading model from {model_path}")
    
    try:
        checkpoint = torch.load(model_path, map_location=device)
        
        # Extract model information
        model_name = checkpoint.get('model_name', 'resnet50')
        image_size = checkpoint.get('image_size', 256)
        
        # Create model based on architecture from checkpoint
        if model_name == "resnet18":
            model = models.resnet18(weights=None)
            model.fc = nn.Linear(model.fc.in_features, 2)
        elif model_name == "resnet50":
            model = models.resnet50(weights=None)
            model.fc = nn.Linear(model.fc.in_features, 2)
        elif model_name == "densenet121":
            model = models.densenet121(weights=None)
            model.classifier = nn.Linear(model.classifier.in_features, 2)
        else:
            model = models.resnet50(weights=None)
            model.fc = nn.Linear(model.fc.in_features, 2)
        
        # Load model state
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
        else:
            model.load_state_dict(checkpoint)
        
        model = model.to(device)
        model.eval()
        
        return model, image_size
        
    except Exception as e:
        print(f"Error loading model: {e}")
        sys.exit(1)

def analyze_facial_features(image):
    """
    Analyze facial features related to dental health
    
    Parameters:
    - image: Face image to analyze (numpy array)
    
    Returns:
    - Dictionary of facial features with scores (0-1 scale) and face location
    """
    # Detect faces
    face_locations = face_recognition.face_locations(image)
    if not face_locations:
        return None, None
    
    # Choose the most prominent face (largest area)
    primary_face = max(face_locations, key=lambda rect: (rect[2] - rect[0]) * (rect[1] - rect[3]))
    
    features = {
        'asymmetry': 0.0,
        'jaw_alignment': 0.0,
        'smile_analysis': 0.0
    }
    
    try:
        # Extract face landmarks
        face_landmarks = face_recognition.face_landmarks(image)
        
        if len(face_landmarks) > 0:
            landmarks = face_landmarks[0]
            
            # Calculate facial asymmetry
            left_eye = np.mean(landmarks['left_eye'], axis=0)
            right_eye = np.mean(landmarks['right_eye'], axis=0)
            nose_tip = landmarks['nose_tip'][0]
            
            # Basic asymmetry: difference between midpoint of eyes and nose tip
            eye_midpoint = [(left_eye[0] + right_eye[0]) / 2, (left_eye[1] + right_eye[1]) / 2]
            horizontal_asymmetry = abs(eye_midpoint[0] - nose_tip[0]) / (right_eye[0] - left_eye[0])
            features['asymmetry'] = min(horizontal_asymmetry, 1.0)
            
            # Jaw alignment based on chin and jaw points
            if 'chin' in landmarks:
                chin_points = landmarks['chin']
                if len(chin_points) >= 3:
                    # Calculate angle of the jaw line
                    jaw_angle_left = np.arctan2(chin_points[0][1] - chin_points[2][1], 
                                               chin_points[0][0] - chin_points[2][0])
                    jaw_angle_right = np.arctan2(chin_points[-1][1] - chin_points[-3][1], 
                                                chin_points[-1][0] - chin_points[-3][0])
                    
                    # Jaw misalignment score based on angle difference
                    angle_diff = abs(jaw_angle_left - jaw_angle_right)
                    features['jaw_alignment'] = min(angle_diff / np.pi, 1.0)
            
            # Smile analysis
            if 'top_lip' in landmarks and 'bottom_lip' in landmarks:
                top_lip = landmarks['top_lip']
                bottom_lip = landmarks['bottom_lip']
                
                # Calculate lip curvature and symmetry
                left_corner = top_lip[0]
                right_corner = top_lip[6]
                center_point = top_lip[3]
                
                # Check smile symmetry
                left_dist = np.sqrt((left_corner[0] - center_point[0])**2 + (left_corner[1] - center_point[1])**2)
                right_dist = np.sqrt((right_corner[0] - center_point[0])**2 + (right_corner[1] - center_point[1])**2)
                
                smile_asymmetry = abs(left_dist - right_dist) / max(left_dist, right_dist)
                features['smile_analysis'] = min(smile_asymmetry, 1.0)
        
        # Return both features and the location of the primary face        
        return features, primary_face
        
    except Exception as e:
        print(f"Error analyzing facial features: {e}")
        return features, primary_face

def process_image(image_path, image_size):
    """Process an image for model prediction"""
    try:
        # Load image
        image = Image.open(image_path).convert('RGB')
        np_image = np.array(image)
        
        # Analyze facial features
        features, face_location = analyze_facial_features(np_image)
        
        if face_location is None:
            print(f"No face detected in {image_path}")
            return None, None, None
        
        # Transform image for model
        transform = transforms.Compose([
            transforms.Resize((image_size, image_size)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        transformed_image = transform(image)
        input_tensor = transformed_image.unsqueeze(0)  # Add batch dimension
        
        return input_tensor, image, features
    
    except Exception as e:
        print(f"Error processing image {image_path}: {e}")
        return None, None, None

def make_prediction(model, input_tensor, features, device, threshold=0.5):
    """Make prediction with the model"""
    try:
        input_tensor = input_tensor.to(device)
        
        with torch.no_grad():
            # Forward pass
            outputs = model(input_tensor)
            probabilities = torch.softmax(outputs, dim=1)
            
            # Get prediction
            issue_probability = probabilities[0, 1].item()
            predicted_class = 1 if issue_probability >= threshold else 0
            
            # Combine model prediction with facial feature analysis
            result = {
                'predicted_class': predicted_class,
                'class_name': 'Potential Issue' if predicted_class == 1 else 'Normal',
                'issue_probability': issue_probability,
                'normal_probability': probabilities[0, 0].item(),
                'facial_features': features
            }
            
            # Add risk level based on combined model prediction and feature scores
            feature_sum = sum(features.values()) / len(features)
            combined_score = (issue_probability + feature_sum) / 2
            
            if combined_score > 0.7:
                result['risk_level'] = 'HIGH'
            elif combined_score > 0.4:
                result['risk_level'] = 'MEDIUM'
            else:
                result['risk_level'] = 'LOW'
            
            return result
    
    except Exception as e:
        print(f"Error making prediction: {e}")
        return None

def visualize_prediction(image, result, output_path=None, show=False, detailed=False):
    """Visualize prediction result on the image"""
    # Convert PIL image to numpy array for OpenCV if needed
    if isinstance(image, Image.Image):
        image = np.array(image)
        image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
    
    # Create a copy of the image
    vis_image = image.copy()
    
    # Get prediction information
    predicted_class = result['predicted_class']
    issue_probability = result['issue_probability']
    risk_level = result['risk_level']
    facial_features = result['facial_features']
    
    # Determine colors based on risk level
    if risk_level == 'HIGH':
        risk_color = (0, 0, 255)  # Red for high risk
    elif risk_level == 'MEDIUM':
        risk_color = (0, 165, 255)  # Orange for medium risk
    else:
        risk_color = (0, 255, 0)  # Green for low risk
    
    # Add facial landmarks and analysis if detailed visualization is requested
    if detailed:
        # Get face landmarks
        face_landmarks = face_recognition.face_landmarks(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        
        if face_landmarks:
            landmarks = face_landmarks[0]
            
            # Draw facial landmarks
            for feature, points in landmarks.items():
                points = np.array(points, dtype=np.int32)
                cv2.polylines(vis_image, [points], 
                             closed=('eye' in feature or 'lip' in feature or feature == 'chin'), 
                             color=(255, 255, 0), thickness=1)
            
            # Draw asymmetry line
            if 'left_eye' in landmarks and 'right_eye' in landmarks and 'nose_tip' in landmarks:
                left_eye = np.mean(landmarks['left_eye'], axis=0).astype(int)
                right_eye = np.mean(landmarks['right_eye'], axis=0).astype(int)
                nose_tip = landmarks['nose_tip'][0]
                
                # Draw eye midpoint line
                eye_midpoint = [(left_eye[0] + right_eye[0]) // 2, (left_eye[1] + right_eye[1]) // 2]
                cv2.line(vis_image, tuple(eye_midpoint), tuple(nose_tip), (0, 255, 255), 1)
                
            # Draw jaw alignment lines
            if 'chin' in landmarks:
                chin_points = landmarks['chin']
                if len(chin_points) >= 3:
                    # Draw left jaw line
                    cv2.line(vis_image, tuple(chin_points[0]), tuple(chin_points[2]), (255, 0, 255), 2)
                    # Draw right jaw line
                    cv2.line(vis_image, tuple(chin_points[-1]), tuple(chin_points[-3]), (255, 0, 255), 2)
    
    # Add information overlay at the bottom
    h, w = vis_image.shape[:2]
    overlay_height = 120 if detailed else 80
    
    # Create semi-transparent overlay
    overlay = vis_image.copy()
    cv2.rectangle(overlay, (0, h-overlay_height), (w, h), (0, 0, 0), -1)
    cv2.addWeighted(overlay, 0.7, vis_image, 0.3, 0, vis_image)
    
    # Add prediction text
    font = cv2.FONT_HERSHEY_SIMPLEX
    font_scale = 0.6
    text_color = (255, 255, 255)
    
    # Add main prediction
    class_text = f"Prediction: {result['class_name']} ({issue_probability:.2%})"
    cv2.putText(vis_image, class_text, (10, h-overlay_height+25), font, 0.7, risk_color, 2)
    
    # Add risk level
    risk_text = f"Risk Level: {risk_level}"
    cv2.putText(vis_image, risk_text, (10, h-overlay_height+50), font, 0.7, risk_color, 2)
    
    # Add detailed feature scores if requested
    if detailed:
        feature_y = h-overlay_height+75
        cv2.putText(vis_image, f"Facial Asymmetry: {facial_features['asymmetry']:.2f}", 
                   (10, feature_y), font, font_scale, text_color, 1)
        cv2.putText(vis_image, f"Jaw Alignment: {facial_features['jaw_alignment']:.2f}", 
                   (10, feature_y+20), font, font_scale, text_color, 1)
        cv2.putText(vis_image, f"Smile Analysis: {facial_features['smile_analysis']:.2f}", 
                   (10, feature_y+40), font, font_scale, text_color, 1)
    
    # Save or show the visualization
    if output_path:
        cv2.imwrite(output_path, vis_image)
    
    if show:
        plt.figure(figsize=(12, 10))
        plt.imshow(cv2.cvtColor(vis_image, cv2.COLOR_BGR2RGB))
        plt.axis('off')
        plt.tight_layout()
        plt.show()
    
    return vis_image

def process_directory(input_path, model, image_size, device, output_dir, threshold, show, detailed):
    """Process all images in a directory"""
    results = []
    
    # Get all image files in directory
    image_files = []
    for root, _, files in os.walk(input_path):
        for file in files:
            if file.lower().endswith(('.png', '.jpg', '.jpeg')):
                image_files.append(os.path.join(root, file))
    
    print(f"Found {len(image_files)} images in {input_path}")
    
    # Process each image
    for img_path in image_files:
        print(f"Processing {img_path}...")
        
        # Process image
        input_tensor, image, features = process_image(img_path, image_size)
        if input_tensor is None:
            continue
        
        # Make prediction
        prediction = make_prediction(model, input_tensor, features, device, threshold)
        if prediction is None:
            continue
        
        # Add file info to prediction
        prediction['file_path'] = img_path
        prediction['file_name'] = os.path.basename(img_path)
        results.append(prediction)
        
        # Save visualization
        if output_dir:
            base_name = os.path.splitext(os.path.basename(img_path))[0]
            vis_path = os.path.join(output_dir, f"{base_name}_prediction.jpg")
            visualize_prediction(image, prediction, vis_path, show, detailed)
    
    return results

def create_summary_report(results, output_dir, show=False):
    """Create summary report of predictions"""
    if not results:
        print("No results to summarize")
        return
    
    # Count predictions
    total = len(results)
    issue_count = sum(1 for r in results if r['predicted_class'] == 1)
    normal_count = total - issue_count
    
    # Count risk levels
    high_risk = sum(1 for r in results if r['risk_level'] == 'HIGH')
    medium_risk = sum(1 for r in results if r['risk_level'] == 'MEDIUM')
    low_risk = sum(1 for r in results if r['risk_level'] == 'LOW')
    
    # Create prediction distribution pie chart
    plt.figure(figsize=(10, 8))
    
    # Create first subplot for class distribution
    plt.subplot(2, 1, 1)
    plt.pie([issue_count, normal_count], 
            labels=['Potential Issue', 'Normal'],
            autopct='%1.1f%%',
            colors=['red', 'green'],
            explode=(0.1, 0))
    plt.title('Prediction Distribution')
    
    # Create second subplot for risk level distribution
    plt.subplot(2, 1, 2)
    plt.pie([high_risk, medium_risk, low_risk], 
           labels=['High Risk', 'Medium Risk', 'Low Risk'],
           autopct='%1.1f%%',
           colors=['red', 'orange', 'green'],
           explode=(0.1, 0, 0))
    plt.title('Risk Level Distribution')
    
    plt.tight_layout()
    
    if output_dir:
        plt.savefig(os.path.join(output_dir, "prediction_distribution.png"))
    
    if show:
        plt.show()
    else:
        plt.close()
    
    # Create feature correlation heatmap
    plt.figure(figsize=(10, 8))
    
    # Extract feature values
    asymmetry = [r['facial_features']['asymmetry'] for r in results]
    jaw_alignment = [r['facial_features']['jaw_alignment'] for r in results]
    smile_analysis = [r['facial_features']['smile_analysis'] for r in results]
    issue_probs = [r['issue_probability'] for r in results]
    
    # Create correlation matrix
    corr_data = np.array([asymmetry, jaw_alignment, smile_analysis, issue_probs])
    corr = np.corrcoef(corr_data)
    
    # Plot heatmap
    feature_names = ['Facial Asymmetry', 'Jaw Alignment', 'Smile Analysis', 'Issue Probability']
    sns_heatmap = plt.imshow(corr, cmap='coolwarm', vmin=-1, vmax=1)
    plt.colorbar(sns_heatmap)
    plt.xticks(np.arange(len(feature_names)), feature_names, rotation=45, ha='right')
    plt.yticks(np.arange(len(feature_names)), feature_names)
    plt.title('Feature Correlation')
    
    for i in range(len(feature_names)):
        for j in range(len(feature_names)):
            plt.text(j, i, f'{corr[i, j]:.2f}', 
                    ha="center", va="center", 
                    color="white" if abs(corr[i, j]) > 0.5 else "black")
    
    plt.tight_layout()
    
    if output_dir:
        plt.savefig(os.path.join(output_dir, "feature_correlation.png"))
    
    if show:
        plt.show()
    else:
        plt.close()
    
    # Create bar chart of issue probabilities
    plt.figure(figsize=(12, 6))
    
    # Sort by issue probability
    sorted_results = sorted(results, key=lambda x: x['issue_probability'], reverse=True)
    
    # Limit to top 20 for readability
    if len(sorted_results) > 20:
        sorted_results = sorted_results[:20]
    
    names = [r.get('file_name', f"Image {i+1}") for i, r in enumerate(sorted_results)]
    issue_probs = [r['issue_probability'] for r in sorted_results]
    normal_probs = [r['normal_probability'] for r in sorted_results]
    
    # Create stacked bars
    indices = range(len(names))
    plt.bar(indices, normal_probs, label='Normal')
    plt.bar(indices, issue_probs, bottom=normal_probs, color='red', label='Potential Issue')
    
    plt.xlabel('Images')
    plt.ylabel('Probability')
    plt.title('Issue Probability by Image')
    plt.xticks(indices, names, rotation=45, ha='right')
    plt.legend()
    plt.tight_layout()
    
    if output_dir:
        plt.savefig(os.path.join(output_dir, "issue_probabilities.png"))
    
    if show:
        plt.show()
    else:
        plt.close()
    
    # Create detailed report
    report = {
        'total_images': total,
        'issue_count': issue_count,
        'normal_count': normal_count,
        'issue_percentage': issue_count / total * 100 if total > 0 else 0,
        'high_risk_count': high_risk,
        'medium_risk_count': medium_risk,
        'low_risk_count': low_risk,
        'risk_distribution': {
            'high': high_risk / total * 100 if total > 0 else 0,
            'medium': medium_risk / total * 100 if total > 0 else 0,
            'low': low_risk / total * 100 if total > 0 else 0
        },
        'average_issue_probability': sum(r['issue_probability'] for r in results) / total if total > 0 else 0,
        'average_features': {
            'asymmetry': sum(r['facial_features']['asymmetry'] for r in results) / total if total > 0 else 0,
            'jaw_alignment': sum(r['facial_features']['jaw_alignment'] for r in results) / total if total > 0 else 0,
            'smile_analysis': sum(r['facial_features']['smile_analysis'] for r in results) / total if total > 0 else 0
        },
        'image_details': results
    }
    
    # Save report
    if output_dir:
        with open(os.path.join(output_dir, "prediction_report.json"), 'w') as f:
            json.dump(report, f, indent=4)
    
    # Print summary
    print("\nPrediction Summary:")
    print(f"Total images: {total}")
    print(f"Potential issues detected: {issue_count} ({issue_count/total*100:.1f}%)")
    print(f"Normal: {normal_count} ({normal_count/total*100:.1f}%)")
    print(f"Risk levels: High: {high_risk} ({high_risk/total*100:.1f}%), "
          f"Medium: {medium_risk} ({medium_risk/total*100:.1f}%), "
          f"Low: {low_risk} ({low_risk/total*100:.1f}%)")
    
    return report

def main():
    args = parse_args()
    
    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Set device
    if args.device:
        device = torch.device(args.device)
    else:
        device = torch.device("cuda" if torch.cuda.is_available() else 
                             "mps" if torch.backends.mps.is_available() else 
                             "cpu")
    print(f"Using device: {device}")
    
    # Load model
    model, image_size = load_model(args.model_path, device)
    
    # Check if input is a directory or a single file
    if os.path.isdir(args.input):
        # Process directory
        results = process_directory(args.input, model, image_size, device, 
                                   args.output_dir, args.threshold, args.show, args.detailed)
        
        # Create summary report
        create_summary_report(results, args.output_dir, args.show)
    else:
        # Process single image
        print(f"Processing single image: {args.input}")
        
        # Process image
        input_tensor, image, features = process_image(args.input, image_size)
        if input_tensor is None:
            print("No face detected or error processing image.")
            sys.exit(1)
        
        # Make prediction
        prediction = make_prediction(model, input_tensor, features, device, args.threshold)
        if prediction is None:
            print("Error making prediction.")
            sys.exit(1)
        
        # Add file info to prediction
        prediction['file_path'] = args.input
        prediction['file_name'] = os.path.basename(args.input)
        
        # Print result
        print("\nPrediction Result:")
        print(f"Class: {prediction['class_name']}")
        print(f"Issue Probability: {prediction['issue_probability']:.2%}")
        print(f"Risk Level: {prediction['risk_level']}")
        print("\nFacial Features:")
        for feature, score in prediction['facial_features'].items():
            print(f"- {feature.replace('_', ' ').title()}: {score:.2f}")
        
        # Save or show visualization
        if args.output_dir:
            base_name = os.path.splitext(os.path.basename(args.input))[0]
            vis_path = os.path.join(args.output_dir, f"{base_name}_prediction.jpg")
            visualize_prediction(image, prediction, vis_path, args.show, args.detailed)
            
            # Save prediction to JSON
            with open(os.path.join(args.output_dir, f"{base_name}_prediction.json"), 'w') as f:
                json.dump(prediction, f, indent=4)
        
        print(f"\nResults saved to {args.output_dir}")

if __name__ == "__main__":
    main() 