#!/usr/bin/env python3
"""
Dental Radiography AI Trainer for Smilo

This script demonstrates how to use the enhanced dental dataset manager
to train AI models for dental radiography analysis.
"""

import os
import sys
import argparse
import logging
from pathlib import Path
import json
import time
from datetime import datetime

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import torchvision
from torchvision import transforms
import matplotlib.pyplot as plt
from tqdm import tqdm
import pandas as pd
import cv2

# Import the dental dataset manager
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from dental_dataset_manager import DentalDatasetManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("dental_radiography_trainer.log")
    ]
)
logger = logging.getLogger(__name__)

class DentalRadiographyDataset(Dataset):
    """Dataset class for dental radiography images with annotations."""
    
    def __init__(self, data_dir, transform=None, target_transform=None):
        """
        Initialize the dataset.
        
        Args:
            data_dir: Directory containing images and annotations
            transform: Transforms to apply to images
            target_transform: Transforms to apply to targets
        """
        self.data_dir = Path(data_dir)
        self.transform = transform
        self.target_transform = target_transform
        
        self.images_dir = self.data_dir / "images"
        self.annotations_dir = self.data_dir / "annotations"
        
        # Get all image files
        self.image_files = sorted([f for f in os.listdir(self.images_dir) 
                                  if f.lower().endswith(('.jpg', '.jpeg', '.png'))])
        
        # Load class names from dataset info if available
        dataset_info_path = self.data_dir.parent / "dataset_info.json"
        if dataset_info_path.exists():
            with open(dataset_info_path, 'r') as f:
                dataset_info = json.load(f)
                self.classes = dataset_info.get("classes", ["Implant", "Cavity", "Fillings", "Impacted Tooth"])
        else:
            self.classes = ["Implant", "Cavity", "Fillings", "Impacted Tooth"]
        
        self.class_to_idx = {cls: idx for idx, cls in enumerate(self.classes)}
    
    def __len__(self):
        return len(self.image_files)
    
    def __getitem__(self, idx):
        """
        Get a sample from the dataset.
        
        Args:
            idx: Index of the sample
            
        Returns:
            Tuple of (image, target)
        """
        img_name = self.image_files[idx]
        img_path = os.path.join(self.images_dir, img_name)
        
        # Load image
        image = cv2.imread(img_path)
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # Load annotations if available
        annotation_path = os.path.join(self.annotations_dir, img_name.replace('.jpg', '_annotations.csv'))
        
        if os.path.exists(annotation_path):
            # Read annotations
            annotations = pd.read_csv(annotation_path)
            
            # Create target dictionary for object detection
            boxes = []
            labels = []
            
            for _, row in annotations.iterrows():
                # Get bounding box coordinates
                x_min = row.get('x_min', row.get('xmin'))
                y_min = row.get('y_min', row.get('ymin'))
                x_max = row.get('x_max', row.get('xmax'))
                y_max = row.get('y_max', row.get('ymax'))
                
                # Get class label
                class_name = row.get('class', row.get('label'))
                class_idx = self.class_to_idx.get(class_name, 0)
                
                # Add to lists
                boxes.append([x_min, y_min, x_max, y_max])
                labels.append(class_idx)
            
            # Convert to tensors
            if boxes:
                boxes = torch.as_tensor(boxes, dtype=torch.float32)
                labels = torch.as_tensor(labels, dtype=torch.int64)
            else:
                boxes = torch.zeros((0, 4), dtype=torch.float32)
                labels = torch.zeros(0, dtype=torch.int64)
            
            target = {
                'boxes': boxes,
                'labels': labels,
                'image_id': torch.tensor([idx]),
                'area': (boxes[:, 3] - boxes[:, 1]) * (boxes[:, 2] - boxes[:, 0]) if len(boxes) > 0 else torch.zeros(0),
                'iscrowd': torch.zeros((len(boxes),), dtype=torch.int64)
            }
        else:
            # If no annotations, create empty target
            target = {
                'boxes': torch.zeros((0, 4), dtype=torch.float32),
                'labels': torch.zeros(0, dtype=torch.int64),
                'image_id': torch.tensor([idx]),
                'area': torch.zeros(0),
                'iscrowd': torch.zeros(0, dtype=torch.int64)
            }
        
        # Apply transforms
        if self.transform:
            image = self.transform(image)
        
        if self.target_transform:
            target = self.target_transform(target)
        
        return image, target


def get_model(num_classes):
    """
    Create a Faster R-CNN model for dental radiography analysis.
    
    Args:
        num_classes: Number of classes to detect
        
    Returns:
        Faster R-CNN model
    """
    # Load a pre-trained model
    model = torchvision.models.detection.fasterrcnn_resnet50_fpn(weights="DEFAULT")
    
    # Replace the classifier with a new one for our number of classes
    in_features = model.roi_heads.box_predictor.cls_score.in_features
    model.roi_heads.box_predictor = torchvision.models.detection.faster_rcnn.FastRCNNPredictor(
        in_features, num_classes
    )
    
    return model


def train_model(model, data_loaders, optimizer, num_epochs=10, device="cuda"):
    """
    Train the model.
    
    Args:
        model: Model to train
        data_loaders: Dictionary of data loaders for train and val sets
        optimizer: Optimizer to use
        num_epochs: Number of epochs to train for
        device: Device to train on
        
    Returns:
        Trained model and training history
    """
    # Move model to device
    model.to(device)
    
    # Initialize history
    history = {
        'train_loss': [],
        'val_loss': []
    }
    
    # Training loop
    for epoch in range(num_epochs):
        logger.info(f"Epoch {epoch+1}/{num_epochs}")
        
        # Training phase
        model.train()
        train_loss = 0.0
        
        for images, targets in tqdm(data_loaders['train'], desc="Training"):
            # Move data to device
            images = [img.to(device) for img in images]
            targets = [{k: v.to(device) for k, v in t.items()} for t in targets]
            
            # Zero the parameter gradients
            optimizer.zero_grad()
            
            # Forward pass
            loss_dict = model(images, targets)
            losses = sum(loss for loss in loss_dict.values())
            
            # Backward pass and optimize
            losses.backward()
            optimizer.step()
            
            train_loss += losses.item()
        
        avg_train_loss = train_loss / len(data_loaders['train'])
        history['train_loss'].append(avg_train_loss)
        
        # Validation phase
        model.eval()
        val_loss = 0.0
        
        with torch.no_grad():
            for images, targets in tqdm(data_loaders['val'], desc="Validation"):
                # Move data to device
                images = [img.to(device) for img in images]
                targets = [{k: v.to(device) for k, v in t.items()} for t in targets]
                
                # Forward pass
                loss_dict = model(images, targets)
                losses = sum(loss for loss in loss_dict.values())
                
                val_loss += losses.item()
        
        avg_val_loss = val_loss / len(data_loaders['val'])
        history['val_loss'].append(avg_val_loss)
        
        logger.info(f"Train Loss: {avg_train_loss:.4f}, Val Loss: {avg_val_loss:.4f}")
    
    return model, history


def evaluate_model(model, data_loader, device="cuda"):
    """
    Evaluate the model on a test set.
    
    Args:
        model: Model to evaluate
        data_loader: Data loader for the test set
        device: Device to evaluate on
        
    Returns:
        Dictionary of evaluation metrics
    """
    model.eval()
    
    # Initialize metrics
    metrics = {
        'detections': [],
        'ground_truth': []
    }
    
    with torch.no_grad():
        for images, targets in tqdm(data_loader, desc="Evaluating"):
            # Move data to device
            images = [img.to(device) for img in images]
            
            # Get predictions
            predictions = model(images)
            
            # Store predictions and ground truth
            for pred, target in zip(predictions, targets):
                metrics['detections'].append(pred)
                metrics['ground_truth'].append(target)
    
    # Calculate mAP and other metrics
    # This is a simplified version - in practice, you would use a proper evaluation function
    logger.info("Evaluation complete")
    
    return metrics


def visualize_predictions(model, data_loader, class_names, num_images=5, device="cuda"):
    """
    Visualize model predictions on sample images.
    
    Args:
        model: Trained model
        data_loader: Data loader
        class_names: List of class names
        num_images: Number of images to visualize
        device: Device to run inference on
        
    Returns:
        Figure with visualizations
    """
    model.eval()
    
    # Get sample images
    images, targets = next(iter(data_loader))
    images = images[:num_images]
    targets = targets[:num_images]
    
    # Make predictions
    with torch.no_grad():
        images_tensor = [img.to(device) for img in images]
        predictions = model(images_tensor)
    
    # Create figure
    fig, axes = plt.subplots(num_images, 2, figsize=(12, 3*num_images))
    
    for i, (img, target, pred) in enumerate(zip(images, targets, predictions)):
        # Convert tensor to numpy array
        img_np = img.permute(1, 2, 0).cpu().numpy()
        img_np = (img_np * 255).astype(np.uint8)
        
        # Plot ground truth
        axes[i, 0].imshow(img_np)
        axes[i, 0].set_title("Ground Truth")
        
        for box, label in zip(target['boxes'].cpu().numpy(), target['labels'].cpu().numpy()):
            x1, y1, x2, y2 = box
            rect = plt.Rectangle((x1, y1), x2-x1, y2-y1, fill=False, edgecolor='green', linewidth=2)
            axes[i, 0].add_patch(rect)
            axes[i, 0].text(x1, y1, class_names[label], bbox=dict(facecolor='green', alpha=0.5))
        
        # Plot predictions
        axes[i, 1].imshow(img_np)
        axes[i, 1].set_title("Predictions")
        
        for box, label, score in zip(pred['boxes'].cpu().numpy(), 
                                    pred['labels'].cpu().numpy(), 
                                    pred['scores'].cpu().numpy()):
            if score > 0.5:  # Only show predictions with confidence > 0.5
                x1, y1, x2, y2 = box
                rect = plt.Rectangle((x1, y1), x2-x1, y2-y1, fill=False, edgecolor='red', linewidth=2)
                axes[i, 1].add_patch(rect)
                axes[i, 1].text(x1, y1, f"{class_names[label]} ({score:.2f})", 
                               bbox=dict(facecolor='red', alpha=0.5))
    
    plt.tight_layout()
    return fig


def main():
    """Main function to train and evaluate dental radiography models."""
    parser = argparse.ArgumentParser(description="Dental Radiography AI Trainer")
    
    # Dataset options
    parser.add_argument("--dataset", default="radiography", 
                        help="Dataset ID or alias (default: radiography)")
    parser.add_argument("--download", action="store_true", 
                        help="Download the dataset if not already downloaded")
    parser.add_argument("--preprocess", action="store_true", 
                        help="Preprocess the dataset if not already preprocessed")
    
    # Training options
    parser.add_argument("--epochs", type=int, default=10, 
                        help="Number of epochs to train for")
    parser.add_argument("--batch-size", type=int, default=4, 
                        help="Batch size for training")
    parser.add_argument("--learning-rate", type=float, default=0.005, 
                        help="Learning rate")
    parser.add_argument("--device", default="cuda" if torch.cuda.is_available() else "cpu", 
                        help="Device to train on (cuda or cpu)")
    
    # Output options
    parser.add_argument("--output-dir", default="./dental_radiography_output", 
                        help="Directory to save output")
    
    args = parser.parse_args()
    
    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Initialize dataset manager
    manager = DentalDatasetManager()
    
    # Download dataset if requested
    if args.download:
        logger.info(f"Downloading dataset: {args.dataset}")
        dataset_path = manager.download_dataset(args.dataset)
        if not dataset_path:
            logger.error(f"Failed to download dataset: {args.dataset}")
            return
    
    # Preprocess dataset if requested
    if args.preprocess:
        logger.info(f"Preprocessing dataset: {args.dataset}")
        dataset_path = manager.preprocess_dataset(
            args.dataset,
            preprocessing_steps=["normalization", "contrast_enhancement"]
        )
        if not dataset_path:
            logger.error(f"Failed to preprocess dataset: {args.dataset}")
            return
    
    # Get dataset path
    dataset_path = manager.get_dataset_path(args.dataset, processed=True)
    if not dataset_path:
        logger.error(f"Dataset not found: {args.dataset}")
        return
    
    logger.info(f"Using dataset at: {dataset_path}")
    
    # Load dataset info
    with open(dataset_path / "dataset_info.json", 'r') as f:
        dataset_info = json.load(f)
    
    class_names = dataset_info.get("classes", ["Implant", "Cavity", "Fillings", "Impacted Tooth"])
    num_classes = len(class_names) + 1  # +1 for background
    
    logger.info(f"Classes: {class_names}")
    
    # Define transforms
    transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    # Create datasets
    train_dataset = DentalRadiographyDataset(dataset_path / "train", transform=transform)
    val_dataset = DentalRadiographyDataset(dataset_path / "val", transform=transform)
    test_dataset = DentalRadiographyDataset(dataset_path / "test", transform=transform)
    
    logger.info(f"Train dataset size: {len(train_dataset)}")
    logger.info(f"Validation dataset size: {len(val_dataset)}")
    logger.info(f"Test dataset size: {len(test_dataset)}")
    
    # Create data loaders
    data_loaders = {
        'train': DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True, 
                           collate_fn=lambda x: tuple(zip(*x))),
        'val': DataLoader(val_dataset, batch_size=args.batch_size, 
                         collate_fn=lambda x: tuple(zip(*x))),
        'test': DataLoader(test_dataset, batch_size=args.batch_size, 
                          collate_fn=lambda x: tuple(zip(*x)))
    }
    
    # Create model
    logger.info("Creating model...")
    model = get_model(num_classes)
    
    # Define optimizer
    optimizer = optim.SGD(model.parameters(), lr=args.learning_rate, momentum=0.9, weight_decay=0.0005)
    
    # Train model
    logger.info("Starting training...")
    start_time = time.time()
    model, history = train_model(
        model, 
        data_loaders, 
        optimizer, 
        num_epochs=args.epochs, 
        device=args.device
    )
    training_time = time.time() - start_time
    logger.info(f"Training completed in {training_time:.2f} seconds")
    
    # Save model
    model_path = output_dir / "dental_radiography_model.pth"
    torch.save(model.state_dict(), model_path)
    logger.info(f"Model saved to: {model_path}")
    
    # Save training history
    history_path = output_dir / "training_history.json"
    with open(history_path, 'w') as f:
        json.dump({k: [float(v) for v in vals] for k, vals in history.items()}, f, indent=2)
    logger.info(f"Training history saved to: {history_path}")
    
    # Plot training history
    plt.figure(figsize=(10, 5))
    plt.plot(history['train_loss'], label='Train Loss')
    plt.plot(history['val_loss'], label='Validation Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Training and Validation Loss')
    plt.legend()
    plt.grid(True)
    plt.savefig(output_dir / "training_history.png")
    
    # Evaluate model
    logger.info("Evaluating model...")
    metrics = evaluate_model(model, data_loaders['test'], device=args.device)
    
    # Visualize predictions
    logger.info("Generating visualizations...")
    fig = visualize_predictions(model, data_loaders['test'], class_names, num_images=5, device=args.device)
    fig.savefig(output_dir / "predictions.png")
    
    logger.info(f"All outputs saved to: {output_dir}")
    logger.info("Done!")


if __name__ == "__main__":
    main()
