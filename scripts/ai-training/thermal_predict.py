#!/usr/bin/env python3
"""
Thermal Imaging Prediction for Dental Applications

This script loads a trained thermal imaging model and analyzes new thermal images
to detect inflammation, infection, and other thermal abnormalities in dental tissues.
"""

import os
import sys
import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import argparse
import json
from pathlib import Path
from PIL import Image
import cv2
import time
from torchvision import transforms, models
import torch.nn as nn

def parse_args():
    parser = argparse.ArgumentParser(description="Thermal Imaging Prediction for Dental Applications")
    parser.add_argument("--model_path", type=str, required=True,
                      help="Path to the trained model file")
    parser.add_argument("--input", type=str, required=True,
                      help="Path to input image or directory of images")
    parser.add_argument("--output_dir", type=str, default="./thermal_predictions",
                      help="Directory to save prediction results")
    parser.add_argument("--device", type=str, default=None,
                      help="Device to use (cuda, mps, cpu, or None for auto-detection)")
    parser.add_argument("--threshold", type=float, default=0.5,
                      help="Threshold for abnormality classification")
    parser.add_argument("--show", action="store_true", default=False,
                      help="Show visualizations instead of saving them")
    parser.add_argument("--detailed", action="store_true", default=False,
                      help="Show detailed thermal analysis")
    return parser.parse_args()

def load_model(model_path, device):
    """Load the trained thermal analysis model"""
    print(f"Loading model from {model_path}")
    
    try:
        checkpoint = torch.load(model_path, map_location=device)
        
        # Extract model information
        model_name = checkpoint.get('model_name', 'resnet50')
        image_size = checkpoint.get('image_size', 256)
        
        # Create model based on architecture from checkpoint
        if model_name == "resnet18":
            model = models.resnet18(weights=None)
            model.fc = nn.Linear(model.fc.in_features, 2)
        elif model_name == "resnet50":
            model = models.resnet50(weights=None)
            model.fc = nn.Linear(model.fc.in_features, 2)
        elif model_name == "densenet121":
            model = models.densenet121(weights=None)
            model.classifier = nn.Linear(model.classifier.in_features, 2)
        else:
            model = models.resnet50(weights=None)
            model.fc = nn.Linear(model.fc.in_features, 2)
        
        # Load model state
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
        else:
            model.load_state_dict(checkpoint)
        
        model = model.to(device)
        model.eval()
        
        return model, image_size
        
    except Exception as e:
        print(f"Error loading model: {e}")
        sys.exit(1)

def extract_thermal_features(image):
    """
    Extract thermal features from an image
    
    Parameters:
    - image: Image as numpy array or path to image
    
    Returns:
    - Dictionary of thermal features
    """
    features = {
        'max_temp': 0.0,
        'min_temp': 0.0,
        'avg_temp': 0.0,
        'temp_range': 0.0,
        'hotspot_count': 0,
        'region_temps': {}
    }
    
    try:
        # Load image if path is provided
        if isinstance(image, str):
            image = cv2.imread(image)
            
        if image is None:
            return features
            
        # Convert to grayscale if not already
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image
            
        # Normalize to 0-255 if needed
        if gray.max() > 0:
            norm = 255 * (gray - gray.min()) / (gray.max() - gray.min())
            norm = norm.astype(np.uint8)
        else:
            norm = gray
        
        # Extract thermal features
        features['max_temp'] = float(gray.max())
        features['min_temp'] = float(gray.min())
        features['avg_temp'] = float(gray.mean())
        features['temp_range'] = float(gray.max() - gray.min())
        
        # Count hotspots (areas above certain threshold)
        threshold = features['avg_temp'] + features['temp_range'] * 0.2  # 20% above average
        _, thresh = cv2.threshold(gray, threshold, 255, cv2.THRESH_BINARY)
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        features['hotspot_count'] = len(contours)
        
        # Analyze temperature in different regions (simulate dental regions)
        h, w = gray.shape
        regions = {
            'upper_left': gray[0:h//2, 0:w//2],
            'upper_right': gray[0:h//2, w//2:w],
            'lower_left': gray[h//2:h, 0:w//2],
            'lower_right': gray[h//2:h, w//2:w]
        }
        
        for region_name, region_data in regions.items():
            if region_data.size > 0:
                features['region_temps'][region_name] = {
                    'max': float(region_data.max()),
                    'min': float(region_data.min()),
                    'avg': float(region_data.mean())
                }
        
    except Exception as e:
        print(f"Error extracting thermal features: {e}")
    
    return features

def process_image(image_path, image_size):
    """Process an image for model prediction"""
    try:
        # Load image
        image = Image.open(image_path).convert('RGB')
        np_image = np.array(image)
        
        # Extract thermal features
        features = extract_thermal_features(np_image)
        
        # Transform image for model
        transform = transforms.Compose([
            transforms.Resize((image_size, image_size)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        transformed_image = transform(image)
        input_tensor = transformed_image.unsqueeze(0)  # Add batch dimension
        
        return input_tensor, image, features
    
    except Exception as e:
        print(f"Error processing image {image_path}: {e}")
        return None, None, None

def make_prediction(model, input_tensor, features, device, threshold=0.5):
    """Make prediction with the model"""
    try:
        input_tensor = input_tensor.to(device)
        
        with torch.no_grad():
            # Forward pass
            outputs = model(input_tensor)
            probabilities = torch.softmax(outputs, dim=1)
            
            # Get prediction
            abnormal_probability = probabilities[0, 1].item()
            predicted_class = 1 if abnormal_probability >= threshold else 0
            
            # Calculate severity based on thermal features and model prediction
            # Get the temperatures from features
            temp_range = features['temp_range']
            hotspot_count = features['hotspot_count']
            
            # Normalized hotspot count (max reasonable number is around 10)
            norm_hotspot = min(hotspot_count / 10.0, 1.0)
            
            # Combined score for severity
            # Weight model prediction higher than feature-based scores
            severity_score = (0.6 * abnormal_probability + 
                             0.2 * norm_hotspot + 
                             0.2 * (temp_range / 255.0))
            
            # Determine severity level
            if severity_score > 0.7:
                severity = "HIGH"
            elif severity_score > 0.4:
                severity = "MEDIUM"
            else:
                severity = "LOW"
            
            # Create result dictionary
            result = {
                'predicted_class': predicted_class,
                'class_name': 'Thermal Abnormality' if predicted_class == 1 else 'Normal',
                'abnormal_probability': abnormal_probability,
                'normal_probability': probabilities[0, 0].item(),
                'severity': severity,
                'severity_score': severity_score,
                'thermal_features': features
            }
            
            return result
    
    except Exception as e:
        print(f"Error making prediction: {e}")
        return None

def visualize_prediction(image, result, output_path=None, show=False, detailed=False):
    """Visualize prediction result on the image"""
    # Convert PIL image to numpy array for OpenCV if needed
    if isinstance(image, Image.Image):
        image = np.array(image)
        
    # Create a copy of the image for visualization
    vis_image = image.copy()
    
    # Get prediction information
    predicted_class = result['predicted_class']
    abnormal_probability = result['abnormal_probability']
    severity = result['severity']
    features = result['thermal_features']
    
    # Apply thermal colormap
    if len(vis_image.shape) == 3:
        gray = cv2.cvtColor(vis_image, cv2.COLOR_RGB2GRAY)
    else:
        gray = vis_image.copy()
        vis_image = cv2.cvtColor(gray, cv2.COLOR_GRAY2RGB)
    
    # Normalize and apply colormap
    if gray.max() > gray.min():
        norm = 255 * (gray - gray.min()) / (gray.max() - gray.min())
        norm = norm.astype(np.uint8)
        thermal = cv2.applyColorMap(norm, cv2.COLORMAP_JET)
    else:
        thermal = cv2.applyColorMap(gray, cv2.COLORMAP_JET)
    
    # Blend original with thermal map
    alpha = 0.7
    vis_image = cv2.addWeighted(vis_image, 1-alpha, thermal, alpha, 0)
    
    # Determine colors based on severity
    if severity == 'HIGH':
        severity_color = (0, 0, 255)  # Red for high severity (BGR)
    elif severity == 'MEDIUM':
        severity_color = (0, 165, 255)  # Orange for medium severity
    else:
        severity_color = (0, 255, 0)  # Green for low severity
    
    # Add detailed analysis if requested
    if detailed:
        # Draw contours for hotspots
        threshold = features['avg_temp'] + features['temp_range'] * 0.2  # 20% above average
        _, thresh = cv2.threshold(gray, int(threshold), 255, cv2.THRESH_BINARY)
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        cv2.drawContours(vis_image, contours, -1, (0, 255, 255), 2)
        
        # Draw regions for temperature analysis
        h, w = vis_image.shape[:2]
        # Draw lines dividing the image into four quadrants
        cv2.line(vis_image, (w//2, 0), (w//2, h), (255, 255, 255), 1)
        cv2.line(vis_image, (0, h//2), (w, h//2), (255, 255, 255), 1)
        
        # Label regions
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.5
        cv2.putText(vis_image, "Upper Left", (10, 20), font, font_scale, (255, 255, 255), 1)
        cv2.putText(vis_image, "Upper Right", (w//2 + 10, 20), font, font_scale, (255, 255, 255), 1)
        cv2.putText(vis_image, "Lower Left", (10, h//2 + 20), font, font_scale, (255, 255, 255), 1)
        cv2.putText(vis_image, "Lower Right", (w//2 + 10, h//2 + 20), font, font_scale, (255, 255, 255), 1)
    
    # Add information overlay at the bottom
    h, w = vis_image.shape[:2]
    overlay_height = 120 if detailed else 80
    
    # Create semi-transparent overlay
    overlay = vis_image.copy()
    cv2.rectangle(overlay, (0, h-overlay_height), (w, h), (0, 0, 0), -1)
    cv2.addWeighted(overlay, 0.7, vis_image, 0.3, 0, vis_image)
    
    # Add prediction text
    font = cv2.FONT_HERSHEY_SIMPLEX
    font_scale = 0.6
    text_color = (255, 255, 255)
    
    # Add main prediction
    class_text = f"Prediction: {result['class_name']} ({abnormal_probability:.2%})"
    cv2.putText(vis_image, class_text, (10, h-overlay_height+25), font, 0.7, severity_color, 2)
    
    # Add severity level
    severity_text = f"Severity: {severity}"
    cv2.putText(vis_image, severity_text, (10, h-overlay_height+50), font, 0.7, severity_color, 2)
    
    # Add detailed feature scores if requested
    if detailed:
        feature_y = h-overlay_height+75
        cv2.putText(vis_image, f"Hotspots: {features['hotspot_count']}", 
                   (10, feature_y), font, font_scale, text_color, 1)
        cv2.putText(vis_image, f"Temp Range: {features['temp_range']:.1f}", 
                   (10, feature_y+20), font, font_scale, text_color, 1)
        cv2.putText(vis_image, f"Max Temp: {features['max_temp']:.1f}", 
                   (10, feature_y+40), font, font_scale, text_color, 1)
    
    # Save or show the visualization
    if output_path:
        cv2.imwrite(output_path, vis_image)
    
    if show:
        plt.figure(figsize=(12, 10))
        plt.imshow(cv2.cvtColor(vis_image, cv2.COLOR_BGR2RGB))
        plt.axis('off')
        plt.tight_layout()
        plt.show()
    
    return vis_image

def process_directory(input_path, model, image_size, device, output_dir, threshold, show, detailed):
    """Process all images in a directory"""
    results = []
    
    # Get all image files in directory
    image_files = []
    for root, _, files in os.walk(input_path):
        for file in files:
            if file.lower().endswith(('.png', '.jpg', '.jpeg', '.tiff')):
                image_files.append(os.path.join(root, file))
    
    print(f"Found {len(image_files)} images in {input_path}")
    
    # Process each image
    for img_path in image_files:
        print(f"Processing {img_path}...")
        
        # Process image
        input_tensor, image, features = process_image(img_path, image_size)
        if input_tensor is None:
            continue
        
        # Make prediction
        prediction = make_prediction(model, input_tensor, features, device, threshold)
        if prediction is None:
            continue
        
        # Add file info to prediction
        prediction['file_path'] = img_path
        prediction['file_name'] = os.path.basename(img_path)
        results.append(prediction)
        
        # Save visualization
        if output_dir:
            base_name = os.path.splitext(os.path.basename(img_path))[0]
            vis_path = os.path.join(output_dir, f"{base_name}_prediction.jpg")
            visualize_prediction(image, prediction, vis_path, show, detailed)
    
    return results

def create_summary_report(results, output_dir, show=False):
    """Create summary report of predictions"""
    if not results:
        print("No results to summarize")
        return
    
    # Count predictions
    total = len(results)
    abnormal_count = sum(1 for r in results if r['predicted_class'] == 1)
    normal_count = total - abnormal_count
    
    # Count severity levels
    high_severity = sum(1 for r in results if r['severity'] == 'HIGH')
    medium_severity = sum(1 for r in results if r['severity'] == 'MEDIUM')
    low_severity = sum(1 for r in results if r['severity'] == 'LOW')
    
    # Create prediction distribution pie chart
    plt.figure(figsize=(15, 5))
    
    # Create first subplot for class distribution
    plt.subplot(1, 3, 1)
    plt.pie([abnormal_count, normal_count], 
            labels=['Thermal Abnormality', 'Normal'],
            autopct='%1.1f%%',
            colors=['red', 'green'],
            explode=(0.1, 0))
    plt.title('Prediction Distribution')
    
    # Create second subplot for severity level distribution
    plt.subplot(1, 3, 2)
    plt.pie([high_severity, medium_severity, low_severity], 
           labels=['High Severity', 'Medium Severity', 'Low Severity'],
           autopct='%1.1f%%',
           colors=['red', 'orange', 'green'],
           explode=(0.1, 0, 0))
    plt.title('Severity Level Distribution')
    
    # Create third subplot for hotspot distribution
    hotspot_counts = [r['thermal_features']['hotspot_count'] for r in results]
    plt.subplot(1, 3, 3)
    plt.hist(hotspot_counts, bins=10, color='blue', alpha=0.7)
    plt.xlabel('Number of Hotspots')
    plt.ylabel('Count')
    plt.title('Hotspot Distribution')
    
    plt.tight_layout()
    
    if output_dir:
        plt.savefig(os.path.join(output_dir, "prediction_distribution.png"))
    
    if show:
        plt.show()
    else:
        plt.close()
    
    # Create temperature heatmap
    plt.figure(figsize=(12, 8))
    
    # Extract temperature data for regions
    region_data = {}
    for r in results:
        if 'file_name' in r and 'thermal_features' in r and 'region_temps' in r['thermal_features']:
            for region, temps in r['thermal_features']['region_temps'].items():
                if region not in region_data:
                    region_data[region] = []
                region_data[region].append(temps['avg'])
    
    # Create heatmap data
    if region_data and all(len(region_data[region]) == len(results) for region in region_data):
        heatmap_data = np.array([region_data[region] for region in sorted(region_data.keys())])
        plt.imshow(heatmap_data, aspect='auto', cmap='hot')
        plt.colorbar(label='Average Temperature')
        plt.xticks(range(len(results)), [r['file_name'] for r in results], rotation=90)
        plt.yticks(range(len(region_data)), sorted(region_data.keys()))
        plt.title('Region Temperatures Across Images')
    else:
        plt.text(0.5, 0.5, "Insufficient region data for heatmap", 
                ha='center', va='center', fontsize=12)
    
    plt.tight_layout()
    
    if output_dir:
        plt.savefig(os.path.join(output_dir, "temperature_heatmap.png"))
    
    if show:
        plt.show()
    else:
        plt.close()
    
    # Create detailed report
    report = {
        'total_images': total,
        'abnormal_count': abnormal_count,
        'normal_count': normal_count,
        'abnormal_percentage': abnormal_count / total * 100 if total > 0 else 0,
        'high_severity_count': high_severity,
        'medium_severity_count': medium_severity,
        'low_severity_count': low_severity,
        'severity_distribution': {
            'high': high_severity / total * 100 if total > 0 else 0,
            'medium': medium_severity / total * 100 if total > 0 else 0,
            'low': low_severity / total * 100 if total > 0 else 0
        },
        'average_features': {
            'hotspot_count': sum(r['thermal_features']['hotspot_count'] for r in results) / total if total > 0 else 0,
            'temp_range': sum(r['thermal_features']['temp_range'] for r in results) / total if total > 0 else 0,
            'max_temp': sum(r['thermal_features']['max_temp'] for r in results) / total if total > 0 else 0,
            'min_temp': sum(r['thermal_features']['min_temp'] for r in results) / total if total > 0 else 0,
            'avg_temp': sum(r['thermal_features']['avg_temp'] for r in results) / total if total > 0 else 0
        },
        'dental_implications': get_dental_implications(results),
        'image_details': results
    }
    
    # Save report
    if output_dir:
        with open(os.path.join(output_dir, "thermal_prediction_report.json"), 'w') as f:
            json.dump(report, f, indent=4)
    
    # Print summary
    print("\nPrediction Summary:")
    print(f"Total images: {total}")
    print(f"Thermal abnormalities detected: {abnormal_count} ({abnormal_count/total*100:.1f}%)")
    print(f"Normal: {normal_count} ({normal_count/total*100:.1f}%)")
    print(f"Severity levels: High: {high_severity} ({high_severity/total*100:.1f}%), "
          f"Medium: {medium_severity} ({medium_severity/total*100:.1f}%), "
          f"Low: {low_severity} ({low_severity/total*100:.1f}%)")
    print("\nDental Implications:")
    for implication in report['dental_implications']:
        print(f"- {implication}")
    
    return report

def get_dental_implications(results):
    """Generate dental health implications based on thermal analysis results"""
    implications = []
    
    # Count abnormalities
    abnormal_count = sum(1 for r in results if r['predicted_class'] == 1)
    high_severity = sum(1 for r in results if r['severity'] == 'HIGH')
    
    # Check if any significant findings
    if abnormal_count > 0:
        implications.append("Thermal abnormalities detected, potentially indicating dental inflammation.")
        
        # Check for severity distribution
        if high_severity > 0:
            implications.append(f"High severity thermal patterns detected in {high_severity} images, suggesting active infection or acute inflammation.")
        
        # Check common regions with abnormalities
        region_abnormalities = {}
        for r in results:
            if r['predicted_class'] == 1 and 'thermal_features' in r and 'region_temps' in r['thermal_features']:
                # Find region with highest temperature
                regions = r['thermal_features']['region_temps']
                if regions:
                    max_region = max(regions.items(), key=lambda x: x[1]['avg'])[0]
                    region_abnormalities[max_region] = region_abnormalities.get(max_region, 0) + 1
        
        # Report most common abnormal regions
        if region_abnormalities:
            most_common = max(region_abnormalities.items(), key=lambda x: x[1])
            implications.append(f"Most frequent thermal abnormalities detected in the {most_common[0].replace('_', ' ')} region, which may indicate localized dental issues in that area.")
        
        # Temperature pattern analysis
        hotspot_avg = sum(r['thermal_features']['hotspot_count'] for r in results) / len(results)
        if hotspot_avg > 3:
            implications.append(f"Multiple thermal hotspots detected (avg: {hotspot_avg:.1f}), suggesting possible multiple sites of inflammation.")
        
        # Provide general recommendations
        implications.append("Recommended action: Correlate thermal findings with clinical examination and radiographic assessment.")
        if high_severity > 0:
            implications.append("Urgent dental evaluation recommended for areas with high thermal severity.")
    else:
        implications.append("No significant thermal abnormalities detected in the analyzed images.")
    
    return implications

def main():
    args = parse_args()
    
    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Set device
    if args.device:
        device = torch.device(args.device)
    else:
        device = torch.device("cuda" if torch.cuda.is_available() else 
                             "mps" if torch.backends.mps.is_available() else 
                             "cpu")
    print(f"Using device: {device}")
    
    # Load model
    model, image_size = load_model(args.model_path, device)
    
    # Check if input is a directory or a single file
    if os.path.isdir(args.input):
        # Process directory
        results = process_directory(args.input, model, image_size, device, 
                                   args.output_dir, args.threshold, args.show, args.detailed)
        
        # Create summary report
        create_summary_report(results, args.output_dir, args.show)
    else:
        # Process single image
        print(f"Processing single image: {args.input}")
        
        # Process image
        input_tensor, image, features = process_image(args.input, image_size)
        if input_tensor is None:
            print("Error processing image.")
            sys.exit(1)
        
        # Make prediction
        prediction = make_prediction(model, input_tensor, features, device, args.threshold)
        if prediction is None:
            print("Error making prediction.")
            sys.exit(1)
        
        # Add file info to prediction
        prediction['file_path'] = args.input
        prediction['file_name'] = os.path.basename(args.input)
        
        # Print result
        print("\nPrediction Result:")
        print(f"Class: {prediction['class_name']}")
        print(f"Abnormality Probability: {prediction['abnormal_probability']:.2%}")
        print(f"Severity: {prediction['severity']}")
        print("\nThermal Features:")
        print(f"- Hotspots: {features['hotspot_count']}")
        print(f"- Temperature Range: {features['temp_range']:.1f}")
        print(f"- Average Temperature: {features['avg_temp']:.1f}")
        
        # Save or show visualization
        if args.output_dir:
            base_name = os.path.splitext(os.path.basename(args.input))[0]
            vis_path = os.path.join(args.output_dir, f"{base_name}_prediction.jpg")
            visualize_prediction(image, prediction, vis_path, args.show, args.detailed)
            
            # Save prediction to JSON
            with open(os.path.join(args.output_dir, f"{base_name}_prediction.json"), 'w') as f:
                json.dump(prediction, f, indent=4)
            
            # Generate dental implications
            implications = get_dental_implications([prediction])
            print("\nDental Implications:")
            for implication in implications:
                print(f"- {implication}")
        
        print(f"\nResults saved to {args.output_dir}")

if __name__ == "__main__":
    main() 