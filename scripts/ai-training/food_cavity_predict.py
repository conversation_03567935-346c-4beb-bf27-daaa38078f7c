#!/usr/bin/env python3
"""
Food Cavity Risk Prediction

This script loads a trained food cavity risk model and makes predictions
on new food items to assess their cavity-causing potential.
"""

import os
import sys
import torch
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import argparse
from pathlib import Path
import json
from sklearn.preprocessing import StandardScaler

def parse_args():
    parser = argparse.ArgumentParser(description="Food Cavity Risk Prediction")
    parser.add_argument("--model_path", type=str, required=True,
                      help="Path to the trained model file")
    parser.add_argument("--input", type=str, required=True,
                      help="Path to input CSV file with food nutritional data or a single food name")
    parser.add_argument("--output_dir", type=str, default="./food_cavity_predictions",
                      help="Directory to save prediction results")
    parser.add_argument("--device", type=str, default=None,
                      help="Device to use (cuda, mps, cpu, or None for auto-detection)")
    parser.add_argument("--show", action="store_true", default=False,
                      help="Show visualizations instead of saving them")
    return parser.parse_args()

def load_model(model_path, device):
    """Load the trained food cavity risk model"""
    print(f"Loading model from {model_path}")
    
    try:
        checkpoint = torch.load(model_path, map_location=device)
        
        # Extract model information
        feature_names = checkpoint.get('feature_names', [])
        class_names = checkpoint.get('class_names', ['Low Risk', 'Medium Risk', 'High Risk'])
        scaler = checkpoint.get('scaler', None)
        
        # If model info is not in checkpoint, try to load from model_info.json
        if not feature_names or not scaler:
            model_info_path = os.path.join(os.path.dirname(model_path), 'model_info.json')
            if os.path.exists(model_info_path):
                with open(model_info_path, 'r') as f:
                    model_info = json.load(f)
                feature_names = model_info.get('input_features', feature_names)
                input_size = model_info.get('input_size', len(feature_names))
                hidden_size = model_info.get('hidden_size', 128)
                class_names = model_info.get('class_names', class_names)
            else:
                # Use default values if no info is found
                input_size = len(feature_names) if feature_names else 10
                hidden_size = 128
        else:
            input_size = len(feature_names)
            hidden_size = 128
        
        # Recreate the model architecture
        from food_cavity_classifier import CavityRiskClassifier
        model = CavityRiskClassifier(input_size, hidden_size, len(class_names))
        
        # Load the state dict
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
        else:
            model.load_state_dict(checkpoint)
        
        model = model.to(device)
        model.eval()
        
        return model, feature_names, class_names, scaler
    
    except Exception as e:
        print(f"Error loading model: {e}")
        # Handle case where direct import doesn't work
        class CavityRiskClassifier(torch.nn.Module):
            def __init__(self, input_size, hidden_size=128, num_classes=3):
                super(CavityRiskClassifier, self).__init__()
                self.network = torch.nn.Sequential(
                    torch.nn.Linear(input_size, hidden_size),
                    torch.nn.ReLU(),
                    torch.nn.Dropout(0.3),
                    torch.nn.Linear(hidden_size, hidden_size // 2),
                    torch.nn.ReLU(),
                    torch.nn.Dropout(0.2),
                    torch.nn.Linear(hidden_size // 2, num_classes)
                )
                
            def forward(self, x):
                return self.network(x)
        
        # Try loading again with local class definition
        checkpoint = torch.load(model_path, map_location=device)
        
        feature_names = checkpoint.get('feature_names', [])
        class_names = checkpoint.get('class_names', ['Low Risk', 'Medium Risk', 'High Risk'])
        scaler = checkpoint.get('scaler', None)
        
        # If model info is not in checkpoint, try to load from model_info.json
        if not feature_names or not scaler:
            model_info_path = os.path.join(os.path.dirname(model_path), 'model_info.json')
            if os.path.exists(model_info_path):
                with open(model_info_path, 'r') as f:
                    model_info = json.load(f)
                feature_names = model_info.get('input_features', feature_names)
                input_size = model_info.get('input_size', len(feature_names))
                hidden_size = model_info.get('hidden_size', 128)
                class_names = model_info.get('class_names', class_names)
            else:
                # Use default values if no info is found
                input_size = len(feature_names) if feature_names else 10
                hidden_size = 128
        else:
            input_size = len(feature_names)
            hidden_size = 128
            
        model = CavityRiskClassifier(input_size, hidden_size, len(class_names))
        
        # Load the state dict
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
        else:
            model.load_state_dict(checkpoint)
        
        model = model.to(device)
        model.eval()
        
        return model, feature_names, class_names, scaler

def load_input_data(input_path, feature_names):
    """Load and prepare input data for prediction"""
    # Check if input is a file or a food name
    if os.path.exists(input_path) and input_path.endswith(('.csv', '.xlsx')):
        # Load from file
        print(f"Loading data from file: {input_path}")
        if input_path.endswith('.csv'):
            df = pd.read_csv(input_path)
        else:
            df = pd.read_excel(input_path)
        
        # Check if the dataframe contains the required features
        missing_features = [feat for feat in feature_names if not any(feat.lower() in col.lower() for col in df.columns)]
        if missing_features:
            print(f"Warning: Some features are missing from input data: {missing_features}")
            print("Using available features and filling missing ones with zeros.")
            
        # Prepare features
        X = pd.DataFrame()
        for feat in feature_names:
            # Find matching column
            matching_cols = [col for col in df.columns if feat.lower() in col.lower()]
            if matching_cols:
                X[feat] = df[matching_cols[0]]
            else:
                X[feat] = 0  # Fill missing features with zeros
                
        # Add food names if available
        food_names = None
        name_cols = [col for col in df.columns if 'name' in col.lower() or 'food' in col.lower() or 'item' in col.lower()]
        if name_cols:
            food_names = df[name_cols[0]].tolist()
        else:
            # Use row numbers as names
            food_names = [f"Food {i+1}" for i in range(len(df))]
            
        return X, food_names
    
    else:
        # Treat input as a food name for manual entry
        print(f"No file found at {input_path}. Treating as food name: {input_path}")
        
        # Create a dialog to enter nutritional information
        print(f"\nPlease enter nutritional information for {input_path}:")
        
        values = {}
        for feat in feature_names:
            # Simplify feature name for user entry
            simple_name = feat.split('_')[-1] if '_' in feat else feat
            try:
                val = float(input(f"{simple_name} content (g/100g or enter for 0): ") or 0)
                values[feat] = val
            except ValueError:
                print(f"Invalid input. Using 0 for {simple_name}.")
                values[feat] = 0
        
        X = pd.DataFrame([values])
        food_names = [input_path]
        
        return X, food_names

def predict_cavity_risk(model, X, scaler, device, feature_names, class_names):
    """Make predictions using the loaded model"""
    # Ensure X has the right features in the right order
    if scaler:
        X_scaled = scaler.transform(X)
    else:
        # If no scaler is provided, standardize manually
        X_mean = X.mean()
        X_std = X.std()
        X_std[X_std == 0] = 1  # Avoid division by zero
        X_scaled = (X - X_mean) / X_std
    
    # Convert to tensor
    X_tensor = torch.FloatTensor(X_scaled).to(device)
    
    # Make predictions
    with torch.no_grad():
        outputs = model(X_tensor)
        probabilities = torch.softmax(outputs, dim=1)
        _, predicted = torch.max(outputs, 1)
    
    # Create results
    results = []
    for i in range(len(X)):
        risk_class = predicted[i].item()
        probs = probabilities[i].cpu().numpy()
        
        result = {
            'risk_class': risk_class,
            'risk_name': class_names[risk_class],
            'probabilities': {class_names[j]: float(probs[j]) for j in range(len(class_names))},
            'features': {feat: float(X.iloc[i][j]) for j, feat in enumerate(X.columns)}
        }
        results.append(result)
    
    return results

def visualize_predictions(results, food_names, output_dir, show=False):
    """Create visualizations of prediction results"""
    # Ensure output directory exists
    os.makedirs(output_dir, exist_ok=True)
    
    # Risk class colors
    colors = ['green', 'yellow', 'red']
    
    # 1. Bar chart of risk probabilities for each food
    plt.figure(figsize=(12, 6))
    
    # Limit to top 10 foods if there are many
    if len(food_names) > 10:
        results = results[:10]
        food_names = food_names[:10]
        print(f"Showing visualizations for first 10 foods only.")
    
    # Extract probabilities for each risk class
    low_probs = [r['probabilities']['Low Risk'] for r in results]
    med_probs = [r['probabilities']['Medium Risk'] for r in results]
    high_probs = [r['probabilities']['High Risk'] for r in results]
    
    # Create stacked bar chart
    bar_width = 0.6
    indices = np.arange(len(food_names))
    
    p1 = plt.bar(indices, low_probs, bar_width, color='green', label='Low Risk')
    p2 = plt.bar(indices, med_probs, bar_width, bottom=low_probs, color='orange', label='Medium Risk')
    p3 = plt.bar(indices, high_probs, bar_width, bottom=[i+j for i,j in zip(low_probs, med_probs)], 
                color='red', label='High Risk')
    
    plt.xlabel('Food Item')
    plt.ylabel('Probability')
    plt.title('Cavity Risk Probability by Food Item')
    plt.xticks(indices, food_names, rotation=45, ha='right')
    plt.legend()
    plt.tight_layout()
    
    if show:
        plt.show()
    else:
        plt.savefig(os.path.join(output_dir, 'risk_probabilities.png'))
        plt.close()
    
    # 2. Feature importance for the highest risk foods
    if len(results) > 0:
        # Get the food with highest cavity risk
        highest_risk_idx = np.argmax([r['probabilities']['High Risk'] for r in results])
        highest_risk_food = food_names[highest_risk_idx]
        feature_values = results[highest_risk_idx]['features']
        
        # Create a bar chart of feature values
        plt.figure(figsize=(10, 8))
        feature_items = sorted(feature_values.items(), key=lambda x: x[1], reverse=True)
        feature_names = [item[0] for item in feature_items]
        feature_vals = [item[1] for item in feature_items]
        
        colors = ['red' if val > np.mean(feature_vals) else 'blue' for val in feature_vals]
        
        plt.barh(feature_names, feature_vals, color=colors)
        plt.xlabel('Value')
        plt.title(f'Nutritional Profile - {highest_risk_food}')
        plt.tight_layout()
        
        if show:
            plt.show()
        else:
            plt.savefig(os.path.join(output_dir, 'highest_risk_features.png'))
            plt.close()
    
    # 3. Summary table
    plt.figure(figsize=(10, 6))
    plt.axis('off')
    
    table_data = []
    table_colors = []
    
    for i, (food, result) in enumerate(zip(food_names, results)):
        risk_name = result['risk_name']
        high_prob = result['probabilities']['High Risk']
        
        table_data.append([i+1, food, risk_name, f"{high_prob:.2%}"])
        
        # Color based on risk level
        if risk_name == 'Low Risk':
            row_color = ['white', 'white', 'lightgreen', 'lightgreen']
        elif risk_name == 'Medium Risk':
            row_color = ['white', 'white', 'khaki', 'khaki']
        else:
            row_color = ['white', 'white', 'salmon', 'salmon']
            
        table_colors.append(row_color)
    
    table = plt.table(
        cellText=table_data,
        colLabels=['#', 'Food Item', 'Risk Level', 'High Risk Probability'],
        cellColours=table_colors,
        loc='center',
        cellLoc='center'
    )
    
    table.auto_set_font_size(False)
    table.set_fontsize(12)
    table.scale(1.2, 1.5)
    plt.title('Cavity Risk Summary', fontsize=16, pad=20)
    
    if show:
        plt.show()
    else:
        plt.savefig(os.path.join(output_dir, 'risk_summary_table.png'))
        plt.close()
    
    return

def main():
    args = parse_args()
    
    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Set device
    if args.device:
        device = torch.device(args.device)
    else:
        device = torch.device("cuda" if torch.cuda.is_available() else 
                             "mps" if torch.backends.mps.is_available() else 
                             "cpu")
    print(f"Using device: {device}")
    
    # Load model
    model, feature_names, class_names, scaler = load_model(args.model_path, device)
    
    # Load input data
    X, food_names = load_input_data(args.input, feature_names)
    
    # Make predictions
    results = predict_cavity_risk(model, X, scaler, device, feature_names, class_names)
    
    # Display results
    print("\nCavity Risk Prediction Results:")
    print("-" * 50)
    for food, result in zip(food_names, results):
        risk_name = result['risk_name']
        high_prob = result['probabilities']['High Risk'] * 100
        
        risk_color = "\033[92m" if risk_name == 'Low Risk' else "\033[93m" if risk_name == 'Medium Risk' else "\033[91m"
        reset_color = "\033[0m"
        
        print(f"Food: {food}")
        print(f"Risk Level: {risk_color}{risk_name}{reset_color}")
        print(f"High Risk Probability: {risk_color}{high_prob:.2f}%{reset_color}")
        print("-" * 50)
    
    # Create visualizations
    visualize_predictions(results, food_names, output_dir, args.show)
    
    # Save results to JSON
    results_with_names = []
    for food, result in zip(food_names, results):
        result_copy = result.copy()
        result_copy['food_name'] = food
        results_with_names.append(result_copy)
    
    with open(os.path.join(output_dir, 'prediction_results.json'), 'w') as f:
        json.dump(results_with_names, f, indent=4)
    
    # Generate dental recommendations based on results
    high_risk_foods = [food for food, result in zip(food_names, results) 
                     if result['risk_name'] == 'High Risk']
    
    with open(os.path.join(output_dir, 'dental_recommendations.txt'), 'w') as f:
        f.write("Dental Health Recommendations\n")
        f.write("============================\n\n")
        
        if high_risk_foods:
            f.write("High Cavity Risk Foods:\n")
            for food in high_risk_foods:
                f.write(f"- {food}\n")
            
            f.write("\nRecommendations for High-Risk Foods:\n")
            f.write("1. Consume these foods in moderation\n")
            f.write("2. Rinse mouth with water after consumption\n")
            f.write("3. Brush teeth or chew sugar-free gum after eating\n")
            f.write("4. Consider consuming with meals rather than as snacks\n")
        else:
            f.write("No high-risk foods identified in this analysis.\n")
        
        f.write("\nGeneral Dental Health Tips:\n")
        f.write("1. Brush teeth twice daily with fluoride toothpaste\n")
        f.write("2. Floss daily to remove food particles between teeth\n")
        f.write("3. Limit snacking between meals\n")
        f.write("4. Drink water throughout the day, especially after eating\n")
        f.write("5. Visit your dentist regularly for check-ups\n")
    
    print(f"\nResults and visualizations saved to {output_dir}")
    print(f"Dental recommendations saved to {output_dir}/dental_recommendations.txt")

if __name__ == "__main__":
    main() 