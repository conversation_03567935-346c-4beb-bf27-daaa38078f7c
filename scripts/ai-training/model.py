"""
Model architecture for dental radiography object detection.
Implements a Faster R-CNN architecture with various backbones for detecting dental conditions.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision
from torchvision.models.detection import FasterRCNN
from torchvision.models.detection.rpn import AnchorGenerator
from torchvision.models.detection.faster_rcnn import FastRCNNPredictor
import timm
import pytorch_lightning as pl
from torchmetrics.detection.mean_ap import MeanAveragePrecision
from torch.optim.lr_scheduler import ReduceLROnPlateau, StepLR, CosineAnnealingLR
from torch.optim.swa_utils import AveragedModel, update_bn

import config


class DentalObjectDetectionModel(pl.LightningModule):
    """
    Dental radiography object detection model based on Faster R-CNN.
    """
    
    def __init__(
        self,
        num_classes=config.NUM_CLASSES + 1,  # +1 for background
        backbone_name=config.BACKBONE,
        pretrained=config.PRETRAINED,
        feature_extract=config.FEATURE_EXTRACT,
        learning_rate=config.LEARNING_RATE,
        weight_decay=config.WEIGHT_DECAY,
        momentum=config.MOMENTUM,
        use_swa=config.USE_EMA,
        class_weights=None,
        **kwargs
    ):
        super().__init__()
        self.save_hyperparameters()
        
        self.num_classes = num_classes
        self.backbone_name = backbone_name
        self.learning_rate = learning_rate
        self.weight_decay = weight_decay
        self.momentum = momentum
        self.use_swa = use_swa
        self.class_weights = class_weights
        
        # Build model
        self.model = self._create_model(
            backbone_name=backbone_name,
            pretrained=pretrained,
            feature_extract=feature_extract,
            num_classes=num_classes
        )
        
        # Setup SWA model if enabled
        if self.use_swa:
            self.swa_model = AveragedModel(self.model)
        
        # Setup metrics
        self.train_map = MeanAveragePrecision()
        self.val_map = MeanAveragePrecision()
        self.test_map = MeanAveragePrecision()
        
        # Track best validation score
        self.best_val_map = 0.0
    
    def _create_model(self, backbone_name, pretrained, feature_extract, num_classes):
        """
        Create the Faster R-CNN model with the specified backbone.
        
        Args:
            backbone_name: Name of the backbone (resnet50, efficientnet_b3, etc.)
            pretrained: Whether to use pretrained weights
            feature_extract: If True, only update the new layers params
            num_classes: Number of classes (including background)
        
        Returns:
            Faster R-CNN model
        """
        # Select backbone
        if backbone_name.startswith('resnet'):
            # ResNet backbone
            if backbone_name == 'resnet50':
                backbone = torchvision.models.resnet50(weights='DEFAULT' if pretrained else None)
                backbone_out_channels = 2048
            elif backbone_name == 'resnet101':
                backbone = torchvision.models.resnet101(weights='DEFAULT' if pretrained else None)
                backbone_out_channels = 2048
            else:
                raise ValueError(f"Unsupported ResNet backbone: {backbone_name}")
            
            # Remove the last two layers (avgpool and fc)
            backbone = nn.Sequential(*list(backbone.children())[:-2])
            # Add out_channels attribute
            backbone.out_channels = backbone_out_channels
            
        elif backbone_name.startswith('efficientnet'):
            # EfficientNet backbone from timm
            backbone = timm.create_model(
                backbone_name, 
                pretrained=pretrained, 
                features_only=True,
                out_indices=(4,)  # Use the last feature map
            )
            backbone_out_channels = backbone.feature_info.channels()[-1]
            # Add out_channels attribute
            backbone.out_channels = backbone_out_channels
            
        elif backbone_name.startswith('densenet'):
            # DenseNet backbone
            if backbone_name == 'densenet121':
                backbone = torchvision.models.densenet121(weights='DEFAULT' if pretrained else None).features
                backbone_out_channels = 1024
            elif backbone_name == 'densenet169':
                backbone = torchvision.models.densenet169(weights='DEFAULT' if pretrained else None).features
                backbone_out_channels = 1664
            else:
                raise ValueError(f"Unsupported DenseNet backbone: {backbone_name}")
            
            # Add out_channels attribute
            backbone.out_channels = backbone_out_channels
            
        else:
            raise ValueError(f"Unsupported backbone: {backbone_name}")
        
        # Freeze backbone parameters if feature_extract is True
        if feature_extract:
            for param in backbone.parameters():
                param.requires_grad = False
        
        # Create anchor generator with appropriate sizes and aspect ratios for dental objects
        anchor_generator = AnchorGenerator(
            sizes=((32, 64, 128, 256, 512),),
            aspect_ratios=((0.5, 1.0, 2.0),)
        )
        
        # Create ROI pooler
        roi_pooler = torchvision.ops.MultiScaleRoIAlign(
            featmap_names=['0'],  # We have a single feature map
            output_size=7,
            sampling_ratio=2
        )
        
        # Create Faster R-CNN model
        model = FasterRCNN(
            backbone=backbone,
            num_classes=num_classes,
            rpn_anchor_generator=anchor_generator,
            box_roi_pool=roi_pooler,
            min_size=config.IMAGE_SIZE[0],  # Minimum image size
            max_size=config.IMAGE_SIZE[1],  # Maximum image size
            rpn_score_thresh=0.05,  # RPN score threshold for object proposals
            rpn_nms_thresh=0.7,  # NMS threshold for RPN
            rpn_fg_iou_thresh=0.7,  # IoU threshold for positive RPN examples
            rpn_bg_iou_thresh=0.3,  # IoU threshold for negative RPN examples
            box_score_thresh=0.05,  # Box score threshold for predictions
            box_nms_thresh=0.5,  # NMS threshold for predictions
            box_detections_per_img=100,  # Maximum detections per image
            box_fg_iou_thresh=0.5,  # IoU threshold for positive box examples
            box_bg_iou_thresh=0.5,  # IoU threshold for negative box examples
        )
        
        # Replace the classifier with a new one for our number of classes
        in_features = model.roi_heads.box_predictor.cls_score.in_features
        model.roi_heads.box_predictor = FastRCNNPredictor(in_features, num_classes)
        
        return model
    
    def forward(self, x):
        """Forward pass"""
        self.model.eval()
        return self.model(x)
    
    def training_step(self, batch, batch_idx):
        """Training step"""
        images, targets = batch
        
        # Convert targets to the format expected by the model
        for i, target in enumerate(targets):
            # Ensure all required keys are present
            for key in ['boxes', 'labels']:
                if key not in target:
                    raise KeyError(f"Target is missing required key: {key}")
        
        # Forward pass
        loss_dict = self.model(images, targets)
        
        # Calculate total loss
        losses = sum(loss for loss in loss_dict.values())
        
        # Log losses
        for k, v in loss_dict.items():
            self.log(f"train/{k}", v, on_step=True, on_epoch=True, prog_bar=False, logger=True)
        
        self.log("train/loss", losses, on_step=True, on_epoch=True, prog_bar=True, logger=True)
        
        return losses
    
    def validation_step(self, batch, batch_idx):
        """Validation step"""
        images, targets = batch
        
        # Put model in eval mode
        self.model.eval()
        
        with torch.no_grad():
            # Get predictions
            outputs = self.model(images)
            
            # Calculate validation mAP
            self.val_map.update(outputs, targets)
            
            # For logging, compute a simple loss
            # Note: We're not using this for optimization, just logging
            # In val/test mode, the model returns a list of dicts with predictions
            val_loss = torch.tensor(0.0, device=self.device)
            
            # Log predictions (for a subset of batches)
            if batch_idx % 10 == 0:
                for i, (output, target) in enumerate(zip(outputs, targets)):
                    img_id = i if batch_idx == 0 else batch_idx * self.trainer.datamodule.batch_size + i
                    self._log_predictions(images[i], output, target, img_id, "val")
        
        self.log("val/loss", val_loss, on_step=False, on_epoch=True, prog_bar=True, logger=True)
        
    def on_validation_epoch_end(self):
        """End of validation epoch"""
        # Calculate and log mean average precision
        metrics = self.val_map.compute()
        map_50 = metrics['map_50']
        map_50_95 = metrics['map']
        
        self.log("val/mAP@50", map_50, on_epoch=True, prog_bar=True, logger=True)
        self.log("val/mAP@50:95", map_50_95, on_epoch=True, prog_bar=True, logger=True)
        
        # Track best model
        if map_50 > self.best_val_map:
            self.best_val_map = map_50
            self.log("val/best_mAP@50", map_50, on_epoch=True, logger=True)
        
        # Reset metric
        self.val_map.reset()
    
    def test_step(self, batch, batch_idx):
        """Test step"""
        images, targets = batch
        
        # Put model in eval mode
        self.model.eval()
        
        with torch.no_grad():
            # Use SWA model if available
            if self.use_swa and hasattr(self, 'swa_model'):
                outputs = self.swa_model(images)
            else:
                outputs = self.model(images)
            
            # Calculate test mAP
            self.test_map.update(outputs, targets)
            
            # Log predictions
            if batch_idx % 5 == 0:
                for i, (output, target) in enumerate(zip(outputs, targets)):
                    img_id = i if batch_idx == 0 else batch_idx * self.trainer.datamodule.batch_size + i
                    self._log_predictions(images[i], output, target, img_id, "test")
    
    def on_test_epoch_end(self):
        """End of test epoch"""
        # Calculate and log mean average precision
        metrics = self.test_map.compute()
        map_50 = metrics['map_50']
        map_50_95 = metrics['map']
        
        self.log("test/mAP@50", map_50, on_epoch=True, logger=True)
        self.log("test/mAP@50:95", map_50_95, on_epoch=True, logger=True)
        
        # Reset metric
        self.test_map.reset()
    
    def predict_step(self, batch, batch_idx, dataloader_idx=0):
        """Prediction step"""
        images, targets = batch
        
        # Use SWA model if available
        if self.use_swa and hasattr(self, 'swa_model'):
            self.swa_model.eval()
            with torch.no_grad():
                outputs = self.swa_model(images)
        else:
            self.model.eval()
            with torch.no_grad():
                outputs = self.model(images)
        
        return outputs
    
    def configure_optimizers(self):
        """Configure optimizers and learning rate schedulers"""
        # Select parameters to update
        params = [p for p in self.model.parameters() if p.requires_grad]
        
        # Select optimizer
        if config.OPTIMIZER == "AdamW":
            optimizer = torch.optim.AdamW(
                params,
                lr=self.learning_rate,
                weight_decay=self.weight_decay
            )
        elif config.OPTIMIZER == "Adam":
            optimizer = torch.optim.Adam(
                params,
                lr=self.learning_rate,
                weight_decay=self.weight_decay
            )
        elif config.OPTIMIZER == "SGD":
            optimizer = torch.optim.SGD(
                params,
                lr=self.learning_rate,
                momentum=self.momentum,
                weight_decay=self.weight_decay
            )
        else:
            raise ValueError(f"Unsupported optimizer: {config.OPTIMIZER}")
        
        # Select scheduler
        if config.SCHEDULER == "StepLR":
            scheduler = StepLR(
                optimizer,
                step_size=config.STEP_SIZE,
                gamma=config.GAMMA
            )
            return {
                "optimizer": optimizer,
                "lr_scheduler": {
                    "scheduler": scheduler,
                    "interval": "epoch",
                    "frequency": 1
                }
            }
        
        elif config.SCHEDULER == "CosineAnnealingLR":
            scheduler = CosineAnnealingLR(
                optimizer,
                T_max=config.EPOCHS,
                eta_min=1e-6
            )
            return {
                "optimizer": optimizer,
                "lr_scheduler": {
                    "scheduler": scheduler,
                    "interval": "epoch",
                    "frequency": 1
                }
            }
        
        elif config.SCHEDULER == "ReduceLROnPlateau":
            scheduler = ReduceLROnPlateau(
                optimizer,
                mode=config.PRIMARY_METRIC_MODE,
                factor=config.GAMMA,
                patience=5,
                min_lr=1e-6,
                verbose=True
            )
            return {
                "optimizer": optimizer,
                "lr_scheduler": {
                    "scheduler": scheduler,
                    "interval": "epoch",
                    "frequency": 1,
                    "monitor": "val/mAP@50"
                }
            }
        
        else:
            return optimizer
    
    def on_train_epoch_end(self):
        """End of training epoch"""
        # Update SWA model if enabled and after certain epochs
        if self.use_swa and self.current_epoch >= config.EPOCHS // 2:
            self.swa_model.update_parameters(self.model)
            
            # Update batch norm statistics periodically
            if self.current_epoch % 10 == 0 and self.current_epoch > 0:
                update_bn(self.trainer.datamodule.train_dataloader(), self.swa_model)
    
    def _log_predictions(self, image, prediction, target, img_id, stage):
        """Log predictions for visualization"""
        # Only log if using wandb and at certain intervals
        if not config.USE_WANDB or img_id % 20 != 0:
            return
        
        import wandb
        import matplotlib.pyplot as plt
        import matplotlib.patches as patches
        import numpy as np
        
        # Convert tensors to numpy
        image_np = image.permute(1, 2, 0).cpu().numpy()
        
        # Denormalize
        mean = np.array(config.NORMALIZE_MEAN)
        std = np.array(config.NORMALIZE_STD)
        image_np = std * image_np + mean
        image_np = np.clip(image_np, 0, 1)
        
        # Convert to uint8
        image_np = (image_np * 255).astype(np.uint8)
        
        # Create figure and axes
        fig, ax = plt.subplots(1, figsize=(12, 12))
        ax.imshow(image_np)
        
        # Draw ground truth boxes
        if target is not None:
            gt_boxes = target['boxes'].cpu().numpy()
            gt_labels = target['labels'].cpu().numpy()
            
            for box, label_idx in zip(gt_boxes, gt_labels):
                x_min, y_min, x_max, y_max = box
                rect = patches.Rectangle(
                    (x_min, y_min), x_max - x_min, y_max - y_min,
                    linewidth=2, edgecolor='green', facecolor='none'
                )
                ax.add_patch(rect)
                
                # Get class name
                class_name = list(config.CLASS_TO_IDX.keys())[list(config.CLASS_TO_IDX.values()).index(label_idx)]
                ax.text(
                    x_min, y_min - 5, f"GT: {class_name}",
                    bbox=dict(facecolor='green', alpha=0.5), fontsize=10, color='white'
                )
        
        # Draw prediction boxes
        if prediction is not None:
            pred_boxes = prediction['boxes'].cpu().numpy()
            pred_labels = prediction['labels'].cpu().numpy()
            pred_scores = prediction['scores'].cpu().numpy()
            
            # Filter by score threshold
            score_threshold = 0.5
            keep = pred_scores >= score_threshold
            pred_boxes = pred_boxes[keep]
            pred_labels = pred_labels[keep]
            pred_scores = pred_scores[keep]
            
            for box, label_idx, score in zip(pred_boxes, pred_labels, pred_scores):
                x_min, y_min, x_max, y_max = box
                rect = patches.Rectangle(
                    (x_min, y_min), x_max - x_min, y_max - y_min,
                    linewidth=2, edgecolor='red', facecolor='none'
                )
                ax.add_patch(rect)
                
                # Get class name
                if label_idx < len(config.CLASSES) + 1:  # +1 for background
                    if label_idx == 0:
                        class_name = "background"
                    else:
                        class_name = list(config.CLASS_TO_IDX.keys())[list(config.CLASS_TO_IDX.values()).index(label_idx - 1)]
                    
                    ax.text(
                        x_min, y_min - 5, f"Pred: {class_name} ({score:.2f})",
                        bbox=dict(facecolor='red', alpha=0.5), fontsize=10, color='white'
                    )
        
        plt.axis('off')
        plt.tight_layout()
        
        # Log to wandb
        wandb.log({
            f"{stage}/predictions/{img_id}": wandb.Image(fig)
        })
        
        plt.close(fig) 