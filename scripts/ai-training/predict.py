"""
Prediction script for dental radiography object detection.
Allows loading a trained model and running inference on new images.
"""

import os
import sys
import argparse
import glob
import torch
import numpy as np
import cv2
import matplotlib.pyplot as plt
from pathlib import Path
import json
import time

from utils import load_inference_model, predict_image, visualize_predictions
import config


def parse_args():
    """Parse command-line arguments"""
    parser = argparse.ArgumentParser(description="Run inference with trained dental radiography model")
    
    # Input arguments
    parser.add_argument("--model_path", type=str, required=True,
                       help="Path to the trained model")
    parser.add_argument("--input", type=str, required=True,
                       help="Path to input image or directory of images")
    parser.add_argument("--output_dir", type=str, default="predictions",
                       help="Directory to save predictions")
    
    # Inference settings
    parser.add_argument("--confidence", type=float, default=0.5,
                       help="Confidence threshold for detections")
    parser.add_argument("--device", type=str, default="cuda" if torch.cuda.is_available() else "cpu",
                       choices=["cuda", "cpu"],
                       help="Device to run inference on")
    parser.add_argument("--batch_size", type=int, default=1,
                       help="Batch size for inference (only for directory input)")
    
    # Output settings
    parser.add_argument("--save_json", action="store_true", default=False,
                       help="Save predictions as JSON")
    parser.add_argument("--save_visualizations", action="store_true", default=True,
                       help="Save visualization images")
    
    return parser.parse_args()


def process_single_image(model, image_path, confidence_threshold, output_dir, save_json, save_vis):
    """
    Process a single image with the model.
    
    Args:
        model: Loaded model
        image_path: Path to the image
        confidence_threshold: Confidence threshold for predictions
        output_dir: Directory to save outputs
        save_json: Whether to save predictions as JSON
        save_vis: Whether to save visualization images
    
    Returns:
        Predictions dictionary
    """
    print(f"Processing image: {image_path}")
    
    # Run prediction
    start_time = time.time()
    boxes, labels, scores, image = predict_image(model, image_path, confidence_threshold)
    inference_time = time.time() - start_time
    
    print(f"Found {len(boxes)} detections in {inference_time:.4f} seconds")
    
    # Create output paths
    image_name = os.path.splitext(os.path.basename(image_path))[0]
    os.makedirs(output_dir, exist_ok=True)
    
    # Create predictions dictionary
    predictions = {
        "image_path": str(image_path),
        "image_name": image_name,
        "inference_time": inference_time,
        "detections": []
    }
    
    # Add detections to predictions
    for box, label_idx, score in zip(boxes, labels, scores):
        # Get class name
        if label_idx == 0:
            class_name = "background"
        else:
            class_name = list(config.CLASS_TO_IDX.keys())[list(config.CLASS_TO_IDX.values()).index(label_idx - 1)]
        
        # Add detection to predictions
        predictions["detections"].append({
            "class": class_name,
            "class_id": int(label_idx),
            "confidence": float(score),
            "box": [float(x) for x in box]
        })
    
    # Save predictions as JSON
    if save_json:
        json_path = os.path.join(output_dir, f"{image_name}_predictions.json")
        with open(json_path, "w") as f:
            json.dump(predictions, f, indent=4)
        print(f"Predictions saved to {json_path}")
    
    # Save visualization
    if save_vis:
        vis_path = os.path.join(output_dir, f"{image_name}_visualization.png")
        fig = visualize_predictions(image, boxes, labels, scores, vis_path)
        plt.close(fig)
    
    return predictions


def process_directory(model, input_dir, confidence_threshold, output_dir, save_json, save_vis, batch_size=1):
    """
    Process all images in a directory with the model.
    
    Args:
        model: Loaded model
        input_dir: Path to directory containing images
        confidence_threshold: Confidence threshold for predictions
        output_dir: Directory to save outputs
        save_json: Whether to save predictions as JSON
        save_vis: Whether to save visualization images
        batch_size: Batch size for inference
    
    Returns:
        List of predictions dictionaries
    """
    # Find all images in the directory
    image_extensions = [".jpg", ".jpeg", ".png"]
    image_paths = []
    
    for ext in image_extensions:
        image_paths.extend(glob.glob(os.path.join(input_dir, f"*{ext}")))
        image_paths.extend(glob.glob(os.path.join(input_dir, f"*{ext.upper()}")))
    
    print(f"Found {len(image_paths)} images in {input_dir}")
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Process each image
    all_predictions = []
    
    for image_path in image_paths:
        try:
            predictions = process_single_image(
                model, image_path, confidence_threshold, output_dir, save_json, save_vis
            )
            all_predictions.append(predictions)
        except Exception as e:
            print(f"Error processing {image_path}: {e}")
    
    # Save all predictions to a single JSON file
    if save_json:
        all_json_path = os.path.join(output_dir, "all_predictions.json")
        with open(all_json_path, "w") as f:
            json.dump(all_predictions, f, indent=4)
        print(f"All predictions saved to {all_json_path}")
    
    # Create summary
    summary = {
        "total_images": len(image_paths),
        "processed_images": len(all_predictions),
        "total_detections": sum(len(p["detections"]) for p in all_predictions),
        "class_counts": {},
        "average_inference_time": np.mean([p["inference_time"] for p in all_predictions])
    }
    
    # Count detections by class
    for predictions in all_predictions:
        for detection in predictions["detections"]:
            class_name = detection["class"]
            summary["class_counts"][class_name] = summary["class_counts"].get(class_name, 0) + 1
    
    # Save summary
    summary_path = os.path.join(output_dir, "summary.json")
    with open(summary_path, "w") as f:
        json.dump(summary, f, indent=4)
    print(f"Summary saved to {summary_path}")
    
    # Print summary
    print("\nSummary:")
    print(f"Total images: {summary['total_images']}")
    print(f"Processed images: {summary['processed_images']}")
    print(f"Total detections: {summary['total_detections']}")
    print(f"Average inference time: {summary['average_inference_time']:.4f} seconds")
    print("Detections by class:")
    for cls, count in summary["class_counts"].items():
        print(f"  {cls}: {count}")
    
    return all_predictions


def main():
    """Main prediction function"""
    # Parse arguments
    args = parse_args()
    
    print(f"Loading model from {args.model_path}")
    
    # Load model
    model, model_config = load_inference_model(args.model_path)
    
    if model is None:
        print("Error loading model")
        sys.exit(1)
    
    # Move model to device
    if hasattr(model, 'to') and args.device == "cuda" and torch.cuda.is_available():
        model = model.to(args.device)
        print(f"Model loaded on {args.device}")
    else:
        print(f"Model loaded on CPU")
    
    # Process input
    if os.path.isfile(args.input):
        # Process single image
        process_single_image(
            model, args.input, args.confidence, args.output_dir, 
            args.save_json, args.save_visualizations
        )
    
    elif os.path.isdir(args.input):
        # Process directory of images
        process_directory(
            model, args.input, args.confidence, args.output_dir,
            args.save_json, args.save_visualizations, args.batch_size
        )
    
    else:
        print(f"Input path does not exist: {args.input}")
        sys.exit(1)
    
    print("Prediction completed successfully!")


if __name__ == "__main__":
    main() 