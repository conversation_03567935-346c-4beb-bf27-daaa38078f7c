#!/usr/bin/env python3
"""
Food Cavity Risk Classifier

This script analyzes nutritional data of food items to determine 
their potential for causing dental cavities.
"""

import os
import sys
import torch
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import argparse
import json
from pathlib import Path
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import confusion_matrix, classification_report
import kagglehub

class CavityRiskClassifier(torch.nn.Module):
    def __init__(self, input_size, hidden_size=128, num_classes=3):
        super(CavityRiskClassifier, self).__init__()
        self.network = torch.nn.Sequential(
            torch.nn.Linear(input_size, hidden_size),
            torch.nn.ReLU(),
            torch.nn.Dropout(0.3),
            torch.nn.Linear(hidden_size, hidden_size // 2),
            torch.nn.ReLU(),
            torch.nn.Dropout(0.2),
            torch.nn.Linear(hidden_size // 2, num_classes)
        )
        
    def forward(self, x):
        return self.network(x)

def parse_args():
    parser = argparse.ArgumentParser(description="Food Cavity Risk Classifier")
    parser.add_argument("--dataset", type=str, default="utsavdey1410/food-nutrition-dataset",
                      help="Kaggle dataset identifier")
    parser.add_argument("--output_dir", type=str, default="./food_cavity_classifier_output",
                      help="Output directory for model and results")
    parser.add_argument("--batch_size", type=int, default=64,
                      help="Training batch size")
    parser.add_argument("--epochs", type=int, default=50,
                      help="Number of training epochs")
    parser.add_argument("--learning_rate", type=float, default=0.001,
                      help="Learning rate")
    parser.add_argument("--hidden_size", type=int, default=128,
                      help="Hidden layer size")
    parser.add_argument("--device", type=str, default=None,
                      help="Device to use (cuda, mps, cpu, or None for auto-detection)")
    return parser.parse_args()

def download_and_prepare_dataset(dataset_id):
    """Download dataset using kagglehub and load the CSV"""
    print(f"Downloading dataset: {dataset_id}")
    
    try:
        # Download the dataset using kagglehub
        dataset_path = kagglehub.download(f"datasets/{dataset_id}")
        print(f"Dataset downloaded to: {dataset_path}")
        
        # Find CSV files in the dataset
        csv_files = []
        for root, dirs, files in os.walk(dataset_path):
            for file in files:
                if file.endswith('.csv'):
                    csv_files.append(os.path.join(root, file))
        
        if not csv_files:
            raise ValueError(f"No CSV files found in the dataset at {dataset_path}")
        
        # Load the first CSV file (assuming it's the main one)
        print(f"Loading data from: {csv_files[0]}")
        df = pd.read_csv(csv_files[0])
        
        return df, dataset_path
    
    except Exception as e:
        print(f"Error downloading or loading dataset: {e}")
        print("Attempting to use a sample dataset instead...")
        
        # Create a sample dataset
        np.random.seed(42)
        sample_data = {
            'Name': [f"Food{i}" for i in range(100)],
            'Calories': np.random.randint(50, 800, 100),
            'TotalFat': np.random.uniform(0, 30, 100),
            'SaturatedFat': np.random.uniform(0, 10, 100),
            'Sodium': np.random.uniform(0, 1000, 100),
            'Carbohydrates': np.random.uniform(0, 100, 100),
            'Sugars': np.random.uniform(0, 50, 100),
            'Fiber': np.random.uniform(0, 15, 100),
            'Protein': np.random.uniform(0, 30, 100),
            'Calcium': np.random.uniform(0, 500, 100),
            'VitaminC': np.random.uniform(0, 100, 100),
            'Iron': np.random.uniform(0, 100, 100),
        }
        
        df = pd.DataFrame(sample_data)
        return df, "sample_data"

def prepare_cavity_risk_features(df):
    """Process the dataset and prepare features for cavity risk prediction"""
    print("Preparing cavity risk features...")
    
    # Fill missing values
    df = df.fillna(0)
    
    # Select relevant features for cavity risk
    feature_columns = []
    
    # Look for sugar-related features
    sugar_cols = [col for col in df.columns if 'sugar' in col.lower()]
    feature_columns.extend(sugar_cols)
    
    # Look for carbohydrate-related features
    carb_cols = [col for col in df.columns if any(x in col.lower() for x in ['carb', 'carbohydrate'])]
    feature_columns.extend(carb_cols)
    
    # Look for acidity indicators
    acid_cols = [col for col in df.columns if any(x in col.lower() for x in ['acid', 'ph', 'vitamin c'])]
    feature_columns.extend(acid_cols)
    
    # Look for other relevant nutritional features
    other_cols = [col for col in df.columns if any(x in col.lower() for x in 
                                             ['calorie', 'protein', 'fat', 'fiber', 'sodium', 
                                              'calcium', 'phosphorus', 'iron'])]
    feature_columns.extend(other_cols)
    
    # Remove duplicates and filter out non-numeric columns
    feature_columns = list(set(feature_columns))
    numeric_cols = df[feature_columns].select_dtypes(include=['int64', 'float64']).columns.tolist()
    
    # If we have very few features, add more
    if len(numeric_cols) < 5:
        print("Too few specific features found, adding more generic nutritional columns...")
        numeric_cols = df.select_dtypes(include=['int64', 'float64']).columns.tolist()
        # Remove any columns that are likely IDs or years
        numeric_cols = [col for col in numeric_cols if not any(x in col.lower() for x in ['id', 'year', 'code'])]
    
    print(f"Selected {len(numeric_cols)} features: {numeric_cols}")
    
    # Create target variable based on sugar content
    # If sugar column exists, use it to create target
    sugar_col = None
    for col in sugar_cols:
        if df[col].nunique() > 1:  # Ensure column has variation
            sugar_col = col
            break
    
    if sugar_col:
        print(f"Using {sugar_col} to define cavity risk")
        # Create 3 classes: low, medium, high risk based on sugar content
        sugar_thresholds = [
            df[sugar_col].quantile(0.33),
            df[sugar_col].quantile(0.67)
        ]
        
        df['cavity_risk'] = pd.cut(
            df[sugar_col], 
            bins=[-float('inf'), sugar_thresholds[0], sugar_thresholds[1], float('inf')],
            labels=[0, 1, 2]  # 0=low, 1=medium, 2=high
        )
    else:
        print("No suitable sugar column found. Creating synthetic target...")
        # Create synthetic target based on combination of carbs, calories, etc.
        numeric_df = df[numeric_cols]
        # Normalize columns to 0-1 range for combining
        normalized_df = (numeric_df - numeric_df.min()) / (numeric_df.max() - numeric_df.min() + 1e-8)
        
        # Weight certain nutrients higher for cavity risk
        risk_weights = {}
        for col in normalized_df.columns:
            col_lower = col.lower()
            if 'sugar' in col_lower:
                risk_weights[col] = 3.0
            elif any(x in col_lower for x in ['carb', 'carbohydrate']):
                risk_weights[col] = 2.0
            elif 'calorie' in col_lower:
                risk_weights[col] = 1.5
            elif any(x in col_lower for x in ['acid', 'ph']):
                risk_weights[col] = 2.0
            elif 'calcium' in col_lower:
                risk_weights[col] = -1.0  # Calcium is protective
            elif 'fiber' in col_lower:
                risk_weights[col] = -0.5  # Fiber is somewhat protective
            else:
                risk_weights[col] = 1.0
        
        # Compute weighted risk score
        risk_score = pd.Series(0, index=df.index)
        for col in normalized_df.columns:
            risk_score += normalized_df[col] * risk_weights.get(col, 1.0)
        
        # Normalize risk score
        risk_score = (risk_score - risk_score.min()) / (risk_score.max() - risk_score.min() + 1e-8)
        
        # Create 3 classes
        df['cavity_risk'] = pd.cut(
            risk_score, 
            bins=[0, 0.33, 0.67, 1],
            labels=[0, 1, 2]  # 0=low, 1=medium, 2=high
        )
    
    # Ensure cavity_risk is numeric
    df['cavity_risk'] = df['cavity_risk'].astype(int)
    
    # Count class distribution
    class_counts = df['cavity_risk'].value_counts().sort_index()
    print("Class distribution:")
    for i, count in enumerate(class_counts):
        risk_level = "Low" if i == 0 else "Medium" if i == 1 else "High"
        print(f"  Class {i} ({risk_level} Risk): {count} items")
    
    return df, numeric_cols

def create_data_loaders(df, feature_columns, target_column, batch_size):
    """Create PyTorch dataloaders from DataFrame"""
    print("Creating data loaders...")
    
    # Get features and target
    X = df[feature_columns].values
    y = df[target_column].values
    
    # Split data into train, validation, and test sets
    X_train, X_temp, y_train, y_temp = train_test_split(X, y, test_size=0.3, random_state=42, stratify=y)
    X_val, X_test, y_val, y_test = train_test_split(X_temp, y_temp, test_size=0.5, random_state=42, stratify=y_temp)
    
    # Standardize features
    scaler = StandardScaler()
    X_train = scaler.fit_transform(X_train)
    X_val = scaler.transform(X_val)
    X_test = scaler.transform(X_test)
    
    # Create PyTorch tensors
    X_train_tensor = torch.FloatTensor(X_train)
    y_train_tensor = torch.LongTensor(y_train)
    X_val_tensor = torch.FloatTensor(X_val)
    y_val_tensor = torch.LongTensor(y_val)
    X_test_tensor = torch.FloatTensor(X_test)
    y_test_tensor = torch.LongTensor(y_test)
    
    # Create datasets
    train_dataset = torch.utils.data.TensorDataset(X_train_tensor, y_train_tensor)
    val_dataset = torch.utils.data.TensorDataset(X_val_tensor, y_val_tensor)
    test_dataset = torch.utils.data.TensorDataset(X_test_tensor, y_test_tensor)
    
    # Create dataloaders
    train_loader = torch.utils.data.DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = torch.utils.data.DataLoader(val_dataset, batch_size=batch_size)
    test_loader = torch.utils.data.DataLoader(test_dataset, batch_size=batch_size)
    
    print(f"Train: {len(train_dataset)} samples")
    print(f"Validation: {len(val_dataset)} samples")
    print(f"Test: {len(test_dataset)} samples")
    
    return train_loader, val_loader, test_loader, scaler

def plot_training_results(train_losses, val_losses, train_acc, val_acc, output_path):
    """Plot training and validation metrics"""
    plt.figure(figsize=(12, 5))
    
    # Plot losses
    plt.subplot(1, 2, 1)
    plt.plot(train_losses, label='Train Loss')
    plt.plot(val_losses, label='Validation Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Training and Validation Loss')
    plt.legend()
    
    # Plot accuracy
    plt.subplot(1, 2, 2)
    plt.plot(train_acc, label='Train Accuracy')
    plt.plot(val_acc, label='Validation Accuracy')
    plt.xlabel('Epoch')
    plt.ylabel('Accuracy')
    plt.title('Training and Validation Accuracy')
    plt.legend()
    
    plt.tight_layout()
    plt.savefig(output_path)
    plt.close()

def plot_confusion_matrix(y_true, y_pred, class_names, output_path):
    """Plot confusion matrix"""
    plt.figure(figsize=(10, 8))
    
    cm = confusion_matrix(y_true, y_pred)
    
    # Normalize confusion matrix
    cm_norm = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]
    
    sns.heatmap(cm_norm, annot=cm, fmt='d', cmap='Blues', 
                xticklabels=class_names, yticklabels=class_names)
    
    plt.xlabel('Predicted')
    plt.ylabel('True')
    plt.title('Confusion Matrix')
    plt.tight_layout()
    plt.savefig(output_path)
    plt.close()

def plot_feature_importance(model, feature_names, output_path):
    """Plot feature importance based on model weights"""
    # Get feature importance from the first layer weights
    weights = model.network[0].weight.data.cpu().numpy()
    
    # Average absolute weights across all outputs
    importance = np.abs(weights).mean(axis=0)
    
    # Sort features by importance
    sorted_idx = np.argsort(importance)
    
    plt.figure(figsize=(10, 8))
    bars = plt.barh(range(len(sorted_idx)), importance[sorted_idx], align='center')
    plt.yticks(range(len(sorted_idx)), [feature_names[i] for i in sorted_idx])
    plt.xlabel('Feature Importance')
    plt.title('Nutritional Feature Importance for Cavity Risk')
    
    # Color bars based on importance
    for i, bar in enumerate(bars):
        if i > len(sorted_idx) * 0.7:  # Top 30% features
            bar.set_color('red')
        elif i > len(sorted_idx) * 0.4:  # Next 30% features
            bar.set_color('orange')
        else:  # Bottom 40% features
            bar.set_color('blue')
    
    plt.tight_layout()
    plt.savefig(output_path)
    plt.close()

def main():
    args = parse_args()
    
    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Set device
    if args.device:
        device = torch.device(args.device)
    else:
        device = torch.device("cuda" if torch.cuda.is_available() else 
                             "mps" if torch.backends.mps.is_available() else 
                             "cpu")
    print(f"Using device: {device}")
    
    # Download and prepare dataset
    df, dataset_path = download_and_prepare_dataset(args.dataset)
    
    # Prepare features for cavity risk prediction
    df, feature_columns = prepare_cavity_risk_features(df)
    
    # Create data loaders
    train_loader, val_loader, test_loader, scaler = create_data_loaders(
        df, feature_columns, 'cavity_risk', args.batch_size
    )
    
    # Create model
    input_size = len(feature_columns)
    model = CavityRiskClassifier(input_size, args.hidden_size, num_classes=3)
    model = model.to(device)
    
    # Define loss function and optimizer
    criterion = torch.nn.CrossEntropyLoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=args.learning_rate)
    
    # Training metrics
    train_losses = []
    val_losses = []
    train_acc = []
    val_acc = []
    best_val_loss = float('inf')
    
    # Training loop
    print(f"Starting training for {args.epochs} epochs...")
    for epoch in range(args.epochs):
        # Training
        model.train()
        train_loss = 0
        train_correct = 0
        train_total = 0
        
        for inputs, targets in train_loader:
            inputs, targets = inputs.to(device), targets.to(device)
            
            # Forward pass
            outputs = model(inputs)
            loss = criterion(outputs, targets)
            
            # Backward pass and optimize
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            # Track metrics
            train_loss += loss.item() * inputs.size(0)
            _, predicted = torch.max(outputs, 1)
            train_total += targets.size(0)
            train_correct += (predicted == targets).sum().item()
        
        # Average training metrics
        train_loss = train_loss / train_total
        train_accuracy = train_correct / train_total
        train_losses.append(train_loss)
        train_acc.append(train_accuracy)
        
        # Validation
        model.eval()
        val_loss = 0
        val_correct = 0
        val_total = 0
        
        with torch.no_grad():
            for inputs, targets in val_loader:
                inputs, targets = inputs.to(device), targets.to(device)
                
                # Forward pass
                outputs = model(inputs)
                loss = criterion(outputs, targets)
                
                # Track metrics
                val_loss += loss.item() * inputs.size(0)
                _, predicted = torch.max(outputs, 1)
                val_total += targets.size(0)
                val_correct += (predicted == targets).sum().item()
        
        # Average validation metrics
        val_loss = val_loss / val_total
        val_accuracy = val_correct / val_total
        val_losses.append(val_loss)
        val_acc.append(val_accuracy)
        
        # Save best model
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            # Save model
            model_path = output_dir / "model_best.pt"
            # Save with additional information
            torch.save({
                'model_state_dict': model.state_dict(),
                'feature_names': feature_columns,
                'class_names': ['Low Risk', 'Medium Risk', 'High Risk'],
                'scaler': scaler,
                'input_size': input_size,
                'hidden_size': args.hidden_size,
                'epoch': epoch,
                'val_loss': val_loss,
                'val_accuracy': val_accuracy
            }, model_path)
            print(f"Saved best model at epoch {epoch+1}")
        
        # Print epoch results
        print(f"Epoch {epoch+1}/{args.epochs}: "
              f"Train Loss={train_loss:.4f}, Train Acc={train_accuracy:.4f}, "
              f"Val Loss={val_loss:.4f}, Val Acc={val_accuracy:.4f}")
    
    # Plot training results
    plot_training_results(
        train_losses, val_losses, train_acc, val_acc,
        output_dir / "training_results.png"
    )
    
    # Evaluate on test set
    model.eval()
    test_loss = 0
    test_correct = 0
    test_total = 0
    all_targets = []
    all_predictions = []
    
    with torch.no_grad():
        for inputs, targets in test_loader:
            inputs, targets = inputs.to(device), targets.to(device)
            
            # Forward pass
            outputs = model(inputs)
            loss = criterion(outputs, targets)
            
            # Track metrics
            test_loss += loss.item() * inputs.size(0)
            _, predicted = torch.max(outputs, 1)
            test_total += targets.size(0)
            test_correct += (predicted == targets).sum().item()
            
            # Save targets and predictions for confusion matrix
            all_targets.extend(targets.cpu().numpy())
            all_predictions.extend(predicted.cpu().numpy())
    
    # Calculate test metrics
    test_loss = test_loss / test_total
    test_accuracy = test_correct / test_total
    
    print(f"\nTest Results: Loss={test_loss:.4f}, Accuracy={test_accuracy:.4f}")
    
    # Plot confusion matrix
    plot_confusion_matrix(
        all_targets, all_predictions,
        ['Low Risk', 'Medium Risk', 'High Risk'],
        output_dir / "confusion_matrix.png"
    )
    
    # Plot feature importance
    plot_feature_importance(
        model, feature_columns,
        output_dir / "feature_importance.png"
    )
    
    # Generate classification report
    report = classification_report(
        all_targets, all_predictions,
        target_names=['Low Risk', 'Medium Risk', 'High Risk'],
        output_dict=True
    )
    
    # Save model information
    model_info = {
        'input_features': feature_columns,
        'input_size': input_size,
        'hidden_size': args.hidden_size,
        'class_names': ['Low Risk', 'Medium Risk', 'High Risk'],
        'test_accuracy': test_accuracy,
        'test_loss': test_loss,
        'classification_report': report,
        'dataset_path': dataset_path,
        'dataset_id': args.dataset
    }
    
    with open(output_dir / "model_info.json", "w") as f:
        json.dump(model_info, f, indent=4)
    
    print(f"\nTraining complete! Model and results saved to {output_dir}")

if __name__ == "__main__":
    main() 