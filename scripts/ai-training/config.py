"""
Configuration file for the dental radiography AI training pipeline.
This centralizes all hyperparameters and settings for reproducibility.
"""

import os
from datetime import datetime
from pathlib import Path

# Dataset paths
DATASET_PATH = "/Users/<USER>/.cache/kagglehub/datasets/imtkaggleteam/dental-radiography/versions/1"
TRAIN_DIR = os.path.join(DATASET_PATH, "train")
VALID_DIR = os.path.join(DATASET_PATH, "valid")
TEST_DIR = os.path.join(DATASET_PATH, "test")

# Data processing
IMAGE_SIZE = (512, 512)  # Height x Width
NORMALIZE_MEAN = [0.485, 0.456, 0.406]  # ImageNet normalization
NORMALIZE_STD = [0.229, 0.224, 0.225]
USE_CLASS_WEIGHTS = True  # For handling class imbalance
ANNOTATION_SUFFIX = "_annotations.csv"

# Available dental condition classes
CLASSES = [
    'Implant', 
    '<PERSON>avity',  # Caries
    'Fillings',
    'Impacted Tooth'
]

# Class to index mapping
CLASS_TO_IDX = {cls: idx for idx, cls in enumerate(CLASSES)}
NUM_CLASSES = len(CLASSES)

# Model architecture
BACKBONE = "resnet50"  # Options: resnet50, efficientnet_b3, densenet121
PRETRAINED = True  # Use ImageNet pretrained weights
FEATURE_EXTRACT = False  # If True, only update the reshaped layer params
DROPOUT_RATE = 0.3  # Dropout rate for regularization
FP16_PRECISION = True  # Use mixed precision training

# Training hyperparameters
BATCH_SIZE = 16
NUM_WORKERS = 4  # Number of data loading workers
LEARNING_RATE = 3e-4
WEIGHT_DECAY = 1e-5
MOMENTUM = 0.9
EPOCHS = 100
PATIENCE = 15  # Early stopping patience
GAMMA = 0.1  # Learning rate scheduler gamma
STEP_SIZE = 20  # Step size for scheduler
GRAD_CLIP_VAL = 1.0  # Gradient clipping value

# Optimizer settings
OPTIMIZER = "AdamW"  # Options: Adam, AdamW, SGD
SCHEDULER = "ReduceLROnPlateau"  # Options: StepLR, CosineAnnealingLR, ReduceLROnPlateau

# Evaluation metrics
METRICS = ["accuracy", "precision", "recall", "f1", "iou", "map"]
PRIMARY_METRIC = "map"  # Mean Average Precision for object detection
PRIMARY_METRIC_MODE = "max"  # Whether to maximize or minimize the primary metric

# Augmentations intensity
AUG_INTENSITY = "medium"  # Options: none, light, medium, heavy

# Experiment tracking
USE_WANDB = False  # Whether to use Weights & Biases for tracking
WANDB_PROJECT = "dental-radiography-analysis"
WANDB_ENTITY = "smilo-dental"

# Saving and loading
SAVE_TOP_K = 3  # Save top k best models
CHECKPOINT_DIR = "./checkpoints"
RESULTS_DIR = "./results"
LOGS_DIR = "./logs"

# Experiment naming
TIMESTAMP = datetime.now().strftime("%Y%m%d_%H%M%S")
RUN_NAME = f"dental_detection_{BACKBONE}_{TIMESTAMP}"

# Hardware acceleration
USE_GPU = True
PRECISION = "16-mixed" if FP16_PRECISION else 32

# Reproducibility
RANDOM_SEED = 42

# Advanced settings
MIXUP_ALPHA = 0.2  # Mixup augmentation alpha, 0 to disable
USE_EMA = True  # Use Exponential Moving Average of model weights
EMA_DECAY = 0.999
ENABLE_ANOMALY_DETECTION = False  # PyTorch anomaly detection mode

# Path management
def create_run_directories():
    """Create necessary directories for the training run"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    run_dir = Path(f"./runs/{timestamp}")
    
    for subdir in ["checkpoints", "logs", "results", "predictions"]:
        (run_dir / subdir).mkdir(parents=True, exist_ok=True)
    
    return run_dir 