# Dental Radiography AI for Smilo

This directory contains enhanced scripts for working with dental radiography datasets and training AI models for dental analysis. These tools leverage KaggleHub to access high-quality dental radiography datasets and provide a streamlined workflow for preprocessing, training, and inference.

## Overview

The dental radiography AI system consists of three main components:

1. **Dataset Manager**: A comprehensive tool for downloading, preprocessing, and managing dental datasets from Kaggle.
2. **Training Pipeline**: A script for training object detection models on dental radiography images.
3. **Inference Tool**: A script for running inference on new dental radiography images.

## Installation

### Prerequisites

- Python 3.8+
- PyTorch 1.10+
- CUDA-capable GPU (recommended)

### Required Packages

```bash
pip install kagglehub torch torchvision opencv-python pandas matplotlib pillow tqdm
```

### Kaggle Authentication

To use KaggleHub, you need to authenticate with your Kaggle credentials:

1. Go to your Kaggle account settings: https://www.kaggle.com/settings
2. Scroll down to the "API" section and click "Create New Token"
3. This will download a `kaggle.json` file
4. Place this file in `~/.kaggle/kaggle.json` (Linux/Mac) or `C:\Users\<USER>\.kaggle\kaggle.json` (Windows)
5. Set appropriate permissions: `chmod 600 ~/.kaggle/kaggle.json` (Linux/Mac)

## Dataset Manager

The `dental_dataset_manager.py` script provides a comprehensive interface for managing dental datasets:

```bash
# List available datasets
python dental_dataset_manager.py --list

# Download the dental radiography dataset
python dental_dataset_manager.py --dataset radiography --download

# Preprocess the dataset with specific steps
python dental_dataset_manager.py --dataset radiography --preprocess --preprocessing-steps normalization contrast_enhancement resize

# Show information about a dataset
python dental_dataset_manager.py --dataset radiography --info
```

### Available Datasets

The dataset manager supports the following dental datasets:

- `radiography`: Dental radiography images with annotations for various conditions
- `cavity`: Cavity classification dataset
- `oral_cancer`: Oral cancer prediction dataset
- `thermal`: Infrared thermography temperature dataset
- `facial`: Tufts face database for facial analysis

### Preprocessing Options

The dataset manager supports various preprocessing steps:

- `normalization`: Normalize pixel values
- `contrast_enhancement`: Enhance contrast using CLAHE
- `resize`: Resize images to a standard size
- `denoise`: Apply denoising
- `edge_enhancement`: Enhance edges for better feature detection

## Training Pipeline

The `dental_radiography_trainer.py` script provides a complete pipeline for training dental radiography AI models:

```bash
# Train a model with default settings
python dental_radiography_trainer.py --download --preprocess --epochs 10 --batch-size 4 --output-dir ./dental_model_output

# Train with specific settings
python dental_radiography_trainer.py --dataset radiography --epochs 20 --batch-size 8 --learning-rate 0.001 --device cuda
```

### Training Options

- `--dataset`: Dataset ID or alias (default: radiography)
- `--download`: Download the dataset if not already downloaded
- `--preprocess`: Preprocess the dataset if not already preprocessed
- `--epochs`: Number of epochs to train for (default: 10)
- `--batch-size`: Batch size for training (default: 4)
- `--learning-rate`: Learning rate (default: 0.005)
- `--device`: Device to train on (cuda or cpu)
- `--output-dir`: Directory to save output

## Inference Tool

The `dental_radiography_inference.py` script allows you to run inference on new dental radiography images:

```bash
# Run inference on a single image
python dental_radiography_inference.py --model ./dental_model_output/dental_radiography_model.pth --image path/to/dental_xray.jpg --output-dir ./inference_results

# Run with custom confidence threshold
python dental_radiography_inference.py --model ./dental_model_output/dental_radiography_model.pth --image path/to/dental_xray.jpg --confidence 0.7
```

### Inference Options

- `--model`: Path to the trained model (required)
- `--image`: Path to the input image (required)
- `--classes`: List of class names (default: Implant Cavity Fillings "Impacted Tooth")
- `--confidence`: Confidence threshold for predictions (default: 0.5)
- `--device`: Device to run inference on (cuda or cpu)
- `--output-dir`: Directory to save output

## Example Workflow

Here's a complete workflow for training and using a dental radiography AI model:

```bash
# 1. Download and preprocess the dataset
python dental_dataset_manager.py --dataset radiography --download --preprocess

# 2. Train the model
python dental_radiography_trainer.py --epochs 20 --batch-size 8 --output-dir ./dental_model_output

# 3. Run inference on new images
python dental_radiography_inference.py --model ./dental_model_output/dental_radiography_model.pth --image path/to/dental_xray.jpg --output-dir ./inference_results
```

## Integration with Smilo

These tools can be integrated with the Smilo platform in several ways:

1. **API Integration**: The inference script can be wrapped in an API endpoint to provide dental radiography analysis as a service.

2. **Web Interface**: The visualization and reporting components can be integrated into the Smilo web interface to provide interactive analysis of dental radiography images.

3. **Batch Processing**: The dataset manager and training pipeline can be used to periodically retrain models on new data.

## Benefits for Smilo

1. **Improved Diagnosis**: The dental radiography AI can help identify dental conditions that might be missed by human examination.

2. **Standardized Analysis**: Provides consistent analysis of dental radiography images across different providers.

3. **Educational Tool**: Can be used to educate patients about their dental conditions by highlighting areas of concern in radiography images.

4. **Research Platform**: The dataset manager and training pipeline provide a foundation for dental research using AI.

## Future Enhancements

1. **Multi-modal Analysis**: Integrate with other dental imaging modalities (e.g., intraoral cameras, 3D scans).

2. **Longitudinal Analysis**: Track changes in dental conditions over time.

3. **Treatment Planning**: Provide AI-assisted treatment planning based on radiography analysis.

4. **Mobile Integration**: Optimize models for mobile deployment to enable on-device analysis.

## Troubleshooting

### Common Issues

1. **KaggleHub Authentication**: If you encounter authentication issues, ensure your Kaggle API credentials are correctly set up.

2. **GPU Memory**: If you encounter GPU memory issues during training, try reducing the batch size.

3. **Dataset Not Found**: If a dataset is not found, check that you're using the correct dataset ID or alias.

### Getting Help

For additional help, please refer to:

- KaggleHub documentation: https://github.com/Kaggle/kagglehub
- PyTorch documentation: https://pytorch.org/docs/stable/index.html
- Smilo AI documentation: [Internal link to Smilo AI documentation]
