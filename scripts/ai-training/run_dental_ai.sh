#!/bin/bash

# Dental AI System Launcher
# This script provides a convenient way to run the various dental AI scripts

# Set default paths
MODEL_DIR="./models"
OUTPUT_DIR="./results"
RADIOGRAPHY_MODEL="$MODEL_DIR/inference_model.pt"
CAVITY_MODEL="$MODEL_DIR/cavity_classifier_model.pt"
FOOD_MODEL="$MODEL_DIR/food_cavity_model.pt"
ORAL_CANCER_MODEL="$MODEL_DIR/oral_cancer_model.pt"
FACE_MODEL="$MODEL_DIR/facial_analysis_model.pt"
THERMAL_MODEL="$MODEL_DIR/thermal_analysis_model.pt"

# Check if Python 3 is installed
if ! command -v python3 &> /dev/null; then
    echo "Error: Python 3 is required but not found"
    exit 1
fi

# Ensure required directories exist
mkdir -p "$MODEL_DIR"
mkdir -p "$OUTPUT_DIR"

# Show menu
show_menu() {
    clear
    echo "================================================"
    echo "        Smilo Dental AI System Launcher         "
    echo "================================================"
    echo "1. Train Cavity Classification Model"
    echo "2. Train Food Cavity Risk Model"
    echo "3. Train Oral Cancer Classification Model"
    echo "4. Train Facial Analysis Model"
    echo "5. Train Thermal Imaging Analysis Model"
    echo "-------------------------------------------------"
    echo "6. Predict Cavity from Dental Image"
    echo "7. Analyze Food Cavity Risk"
    echo "8. Analyze Facial Features for Dental Health"
    echo "9. Analyze Thermal Images for Dental Inflammation"
    echo "-------------------------------------------------"
    echo "10. Run Test Script (Oral Cancer Detection)"
    echo "11. Run Test Script (Facial Analysis)"
    echo "12. Run Test Script (Thermal Imaging Analysis)"
    echo "-------------------------------------------------"
    echo "13. Run Comprehensive Dental Analysis"
    echo "14. Exit"
    echo "================================================"
    echo ""
    read -p "Enter your choice [1-14]: " choice
}

train_cavity_model() {
    echo "Training Cavity Classification Model..."
    echo "----------------------------------------"
    
    # Ask for dataset path
    read -p "Enter path to cavity dataset (leave empty for default): " dataset_path
    if [ -z "$dataset_path" ]; then
        dataset_path="/Users/<USER>/.cache/kagglehub/datasets/aqibrehmanpirzada/cavity-and-non-cavity-classification/versions/1/Dentalfinaldata"
    fi
    
    # Ask for model type
    echo "Select model architecture:"
    echo "1. ResNet18 (faster, good accuracy)"
    echo "2. MobileNet (smaller size)"
    echo "3. EfficientNet (higher accuracy, slower)"
    read -p "Enter your choice [1-3]: " model_choice
    
    case $model_choice in
        1) model_type="resnet18" ;;
        2) model_type="mobilenet" ;;
        3) model_type="efficientnet" ;;
        *) model_type="resnet18" ;;
    esac
    
    # Ask for training parameters
    read -p "Enter batch size [32]: " batch_size
    batch_size=${batch_size:-32}
    
    read -p "Enter number of epochs [20]: " epochs
    epochs=${epochs:-20}
    
    read -p "Enter learning rate [0.001]: " learning_rate
    learning_rate=${learning_rate:-0.001}
    
    # Run training
    python3 cavity_classifier.py \
        --data_path "$dataset_path" \
        --output_dir "cavity_classifier_output" \
        --batch_size $batch_size \
        --epochs $epochs \
        --learning_rate $learning_rate \
        --model $model_type
    
    # Copy model to models directory
    if [ -f "cavity_classifier_output/cavity_classifier_model.pt" ]; then
        cp "cavity_classifier_output/cavity_classifier_model.pt" "$MODEL_DIR/"
        echo "Model saved to $MODEL_DIR/cavity_classifier_model.pt"
    fi
    
    read -p "Press Enter to continue..."
}

train_food_model() {
    echo "Training Food Cavity Risk Model..."
    echo "---------------------------------------"
    
    # Ask for dataset ID
    read -p "Enter Kaggle dataset ID [utsavdey1410/food-nutrition-dataset]: " dataset_id
    dataset_id=${dataset_id:-"utsavdey1410/food-nutrition-dataset"}
    
    # Ask for training parameters
    read -p "Enter batch size [64]: " batch_size
    batch_size=${batch_size:-64}
    
    read -p "Enter number of epochs [50]: " epochs
    epochs=${epochs:-50}
    
    read -p "Enter learning rate [0.001]: " learning_rate
    learning_rate=${learning_rate:-0.001}
    
    read -p "Enter hidden layer size [128]: " hidden_size
    hidden_size=${hidden_size:-128}
    
    # Run training
    python3 food_cavity_classifier.py \
        --dataset "$dataset_id" \
        --output_dir "food_cavity_classifier_output" \
        --batch_size $batch_size \
        --epochs $epochs \
        --learning_rate $learning_rate \
        --hidden_size $hidden_size
    
    # Copy model to models directory
    if [ -f "food_cavity_classifier_output/model_best.pt" ]; then
        cp "food_cavity_classifier_output/model_best.pt" "$MODEL_DIR/food_cavity_model.pt"
        echo "Model saved to $MODEL_DIR/food_cavity_model.pt"
    fi
    
    read -p "Press Enter to continue..."
}

train_oral_cancer_model() {
    echo "Training Oral Cancer Classification Model..."
    echo "-------------------------------------------"
    
    # Ask for dataset ID
    read -p "Enter Kaggle dataset ID [ankushpanday2/oral-cancer-prediction-dataset]: " dataset_id
    dataset_id=${dataset_id:-"ankushpanday2/oral-cancer-prediction-dataset"}
    
    # Ask for model type
    echo "Select model architecture:"
    echo "1. ResNet18 (faster, good accuracy)"
    echo "2. ResNet50 (better accuracy, slower)"
    echo "3. DenseNet121 (high accuracy)"
    echo "4. EfficientNet-B0 (balanced speed/accuracy)"
    read -p "Enter your choice [1-4]: " model_choice
    
    case $model_choice in
        1) model_type="resnet18" ;;
        2) model_type="resnet50" ;;
        3) model_type="densenet121" ;;
        4) model_type="efficientnet_b0" ;;
        *) model_type="resnet50" ;;
    esac
    
    # Ask for training parameters
    read -p "Enter batch size [32]: " batch_size
    batch_size=${batch_size:-32}
    
    read -p "Enter number of epochs [30]: " epochs
    epochs=${epochs:-30}
    
    read -p "Enter learning rate [0.0001]: " learning_rate
    learning_rate=${learning_rate:-0.0001}
    
    read -p "Enter image size [224]: " image_size
    image_size=${image_size:-224}
    
    # Run training
    python3 oral_cancer_classifier.py \
        --dataset "$dataset_id" \
        --output_dir "oral_cancer_classifier_output" \
        --batch_size $batch_size \
        --epochs $epochs \
        --learning_rate $learning_rate \
        --model $model_type \
        --image_size $image_size
    
    # Copy model to models directory
    if [ -f "oral_cancer_classifier_output/model_best.pt" ]; then
        cp "oral_cancer_classifier_output/model_best.pt" "$MODEL_DIR/oral_cancer_model.pt"
        echo "Model saved to $MODEL_DIR/oral_cancer_model.pt"
    fi
    
    read -p "Press Enter to continue..."
}

train_facial_analysis_model() {
    echo "Training Facial Analysis Model for Dental Health..."
    echo "--------------------------------------------------"
    
    # Ask for dataset ID
    read -p "Enter Kaggle dataset ID [kpvisionlab/tufts-face-database]: " dataset_id
    dataset_id=${dataset_id:-"kpvisionlab/tufts-face-database"}
    
    # Ask for model type
    echo "Select model architecture:"
    echo "1. ResNet18 (faster, good accuracy)"
    echo "2. ResNet50 (better accuracy, slower)"
    echo "3. DenseNet121 (high accuracy)"
    read -p "Enter your choice [1-3]: " model_choice
    
    case $model_choice in
        1) model_type="resnet18" ;;
        2) model_type="resnet50" ;;
        3) model_type="densenet121" ;;
        *) model_type="resnet50" ;;
    esac
    
    # Ask for training parameters
    read -p "Enter batch size [16]: " batch_size
    batch_size=${batch_size:-16}
    
    read -p "Enter number of epochs [25]: " epochs
    epochs=${epochs:-25}
    
    read -p "Enter learning rate [0.0001]: " learning_rate
    learning_rate=${learning_rate:-0.0001}
    
    read -p "Enter image size [256]: " image_size
    image_size=${image_size:-256}
    
    read -p "Enter features to analyze (comma-separated) [asymmetry,jaw_alignment,smile_analysis]: " features
    features=${features:-"asymmetry,jaw_alignment,smile_analysis"}
    
    # Run training
    python3 face_analysis.py \
        --dataset "$dataset_id" \
        --output_dir "facial_analysis_output" \
        --batch_size $batch_size \
        --epochs $epochs \
        --learning_rate $learning_rate \
        --model $model_type \
        --image_size $image_size \
        --features "$features"
    
    # Copy model to models directory
    if [ -f "facial_analysis_output/model_best.pt" ]; then
        cp "facial_analysis_output/model_best.pt" "$MODEL_DIR/facial_analysis_model.pt"
        echo "Model saved to $MODEL_DIR/facial_analysis_model.pt"
    fi
    
    read -p "Press Enter to continue..."
}

train_thermal_analysis_model() {
    echo ""
    echo "=== Training Thermal Imaging Analysis Model ==="
    echo ""
    
    # Prompt for dataset ID
    read -p "Enter Kaggle dataset ID [default: joebeachcapital/infrared-thermography-temperature]: " dataset_id
    dataset_id=${dataset_id:-"joebeachcapital/infrared-thermography-temperature"}
    
    # Prompt for model architecture
    read -p "Enter model architecture (resnet18, resnet50, densenet121) [default: resnet50]: " model_type
    model_type=${model_type:-"resnet50"}
    
    # Prompt for batch size
    read -p "Enter batch size [default: 16]: " batch_size
    batch_size=${batch_size:-16}
    
    # Prompt for epochs
    read -p "Enter number of epochs [default: 30]: " epochs
    epochs=${epochs:-30}
    
    # Prompt for learning rate
    read -p "Enter learning rate [default: 0.0001]: " learning_rate
    learning_rate=${learning_rate:-0.0001}
    
    # Prompt for temperature threshold
    read -p "Enter temperature difference threshold for abnormality (°C) [default: 1.5]: " temp_threshold
    temp_threshold=${temp_threshold:-1.5}
    
    # Create output directory
    mkdir -p thermal_analysis_output
    
    # Run the training script
    echo ""
    echo "Starting thermal analysis model training..."
    echo ""
    
    python3 thermal_analysis.py \
        --dataset "$dataset_id" \
        --output_dir thermal_analysis_output \
        --batch_size $batch_size \
        --epochs $epochs \
        --learning_rate $learning_rate \
        --model $model_type \
        --temperature_threshold $temp_threshold
    
    # Copy the best model to models directory
    if [ -f "thermal_analysis_output/model_best.pt" ]; then
        mkdir -p "$MODEL_DIR"
        cp thermal_analysis_output/model_best.pt "$THERMAL_MODEL"
        echo ""
        echo "Thermal analysis model saved to $THERMAL_MODEL"
    else
        echo ""
        echo "Error: Training did not produce a model file."
    fi
    
    # Return to menu
    echo ""
    read -p "Press Enter to return to the menu..." dummy
}

predict_cavity() {
    echo "Predict Cavity from Dental Image..."
    echo "---------------------------------------"
    
    # Ask for model path
    read -p "Enter path to cavity model [default]: " model_path
    if [ -z "$model_path" ]; then
        model_path="$CAVITY_MODEL"
    fi
    
    # Check if model exists
    if [ ! -f "$model_path" ]; then
        echo "Model not found at $model_path"
        echo "Please train a model first or specify correct path"
        read -p "Press Enter to continue..."
        return
    fi
    
    # Ask for input image
    read -p "Enter path to dental image or directory: " input_path
    if [ -z "$input_path" ]; then
        echo "Input path is required"
        read -p "Press Enter to continue..."
        return
    fi
    
    # Create output directory
    output_path="$OUTPUT_DIR/cavity_predictions_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$output_path"
    
    # Run prediction
    python3 simple_predict.py \
        --model_path "$model_path" \
        --input "$input_path" \
        --output_dir "$output_path"
    
    echo "Results saved to $output_path"
    read -p "Press Enter to continue..."
}

predict_food_risk() {
    echo "Predict Cavity Risk from Food Nutrition Data..."
    echo "-----------------------------------------------"
    
    # Ask for model path
    read -p "Enter path to food cavity model [default]: " model_path
    if [ -z "$model_path" ]; then
        model_path="$FOOD_MODEL"
    fi
    
    # Check if model exists
    if [ ! -f "$model_path" ]; then
        echo "Model not found at $model_path"
        echo "Please train a model first or specify correct path"
        read -p "Press Enter to continue..."
        return
    fi
    
    # Ask for input data
    read -p "Enter path to food nutrition CSV or food name: " input_path
    if [ -z "$input_path" ]; then
        echo "Input is required"
        read -p "Press Enter to continue..."
        return
    fi
    
    # Create output directory
    output_path="$OUTPUT_DIR/food_predictions_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$output_path"
    
    # Run prediction
    python3 food_cavity_predict.py \
        --model_path "$model_path" \
        --input "$input_path" \
        --output_dir "$output_path"
    
    echo "Results saved to $output_path"
    read -p "Press Enter to continue..."
}

predict_oral_cancer() {
    echo "Predict Oral Cancer from Images..."
    echo "----------------------------------"
    
    # Ask for model path
    read -p "Enter path to oral cancer model [default]: " model_path
    if [ -z "$model_path" ]; then
        model_path="$ORAL_CANCER_MODEL"
    fi
    
    # Check if model exists
    if [ ! -f "$model_path" ]; then
        echo "Model not found at $model_path"
        echo "Please train a model first or specify correct path"
        read -p "Press Enter to continue..."
        return
    fi
    
    # Ask for input image/directory
    read -p "Enter path to oral image or directory: " input_path
    if [ -z "$input_path" ]; then
        echo "Input path is required"
        read -p "Press Enter to continue..."
        return
    fi
    
    # Ask for threshold
    read -p "Enter threshold for positive classification [0.5]: " threshold
    threshold=${threshold:-0.5}
    
    # Create output directory
    output_path="$OUTPUT_DIR/oral_cancer_predictions_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$output_path"
    
    # Run prediction
    python3 oral_cancer_predict.py \
        --model_path "$model_path" \
        --input "$input_path" \
        --output_dir "$output_path" \
        --threshold $threshold
    
    echo "Results saved to $output_path"
    read -p "Press Enter to continue..."
}

analyze_facial_features() {
    echo "Analyze Facial Features for Dental Health..."
    echo "------------------------------------------"
    
    # Ask for model path
    read -p "Enter path to facial analysis model [default]: " model_path
    if [ -z "$model_path" ]; then
        model_path="$FACE_MODEL"
    fi
    
    # Check if model exists
    if [ ! -f "$model_path" ]; then
        echo "Model not found at $model_path"
        echo "Please train a model first or specify correct path"
        read -p "Press Enter to continue..."
        return
    fi
    
    # Ask for input image/directory
    read -p "Enter path to facial image or directory: " input_path
    if [ -z "$input_path" ]; then
        echo "Input path is required"
        read -p "Press Enter to continue..."
        return
    fi
    
    # Ask for threshold
    read -p "Enter threshold for issue classification [0.5]: " threshold
    threshold=${threshold:-0.5}
    
    # Ask for detailed analysis
    read -p "Show detailed facial feature analysis? (y/n) [y]: " detailed
    detailed=${detailed:-y}
    
    # Create output directory
    output_path="$OUTPUT_DIR/facial_analysis_predictions_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$output_path"
    
    # Prepare command
    cmd="python3 face_predict.py --model_path \"$model_path\" --input \"$input_path\" --output_dir \"$output_path\" --threshold $threshold"
    
    # Add detailed flag if selected
    if [[ "$detailed" =~ ^[Yy]$ ]]; then
        cmd="$cmd --detailed"
    fi
    
    # Run prediction
    eval $cmd
    
    echo "Results saved to $output_path"
    read -p "Press Enter to continue..."
}

analyze_thermal_images() {
    echo ""
    echo "=== Analyze Thermal Images for Dental Inflammation ==="
    echo ""
    
    # Check if model exists
    if [ ! -f "$THERMAL_MODEL" ]; then
        echo "Thermal analysis model not found at $THERMAL_MODEL"
        read -p "Do you want to train a model first? (y/n) [default: y]: " train_first
        train_first=${train_first:-"y"}
        
        if [ "$train_first" = "y" ] || [ "$train_first" = "Y" ]; then
            train_thermal_analysis_model
            return
        fi
        
        # Use a different model file
        read -p "Enter path to thermal analysis model: " THERMAL_MODEL
        
        if [ ! -f "$THERMAL_MODEL" ]; then
            echo "Model file not found. Returning to menu."
            read -p "Press Enter to continue..." dummy
            return
        fi
    fi
    
    # Prompt for input
    read -p "Enter path to input thermal image or directory: " input_path
    
    if [ ! -e "$input_path" ]; then
        echo "Input path does not exist. Returning to menu."
        read -p "Press Enter to continue..." dummy
        return
    fi
    
    # Prompt for threshold
    read -p "Enter threshold for abnormality classification [default: 0.5]: " threshold
    threshold=${threshold:-0.5}
    
    # Prompt for detailed analysis
    read -p "Enable detailed analysis? (y/n) [default: n]: " detailed
    if [ "$detailed" = "y" ] || [ "$detailed" = "Y" ]; then
        detailed_flag="--detailed"
    else
        detailed_flag=""
    fi
    
    # Create output directory
    mkdir -p "$OUTPUT_DIR/thermal_predictions"
    
    # Run the prediction script
    echo ""
    echo "Running thermal analysis..."
    echo ""
    
    python3 thermal_predict.py \
        --model_path "$THERMAL_MODEL" \
        --input "$input_path" \
        --output_dir "$OUTPUT_DIR/thermal_predictions" \
        --threshold $threshold \
        $detailed_flag
    
    # Return to menu
    echo ""
    read -p "Press Enter to return to the menu..." dummy
}

comprehensive_analysis() {
    echo "Comprehensive Dental Health Analysis..."
    echo "---------------------------------------"
    
    # Ask for model paths
    read -p "Enter path to radiography detection model [default/none]: " detection_model
    if [ "$detection_model" = "none" ]; then
        detection_model=""
    elif [ -z "$detection_model" ]; then
        detection_model="$RADIOGRAPHY_MODEL"
    fi
    
    read -p "Enter path to cavity classification model [default/none]: " cavity_model
    if [ "$cavity_model" = "none" ]; then
        cavity_model=""
    elif [ -z "$cavity_model" ]; then
        cavity_model="$CAVITY_MODEL"
    fi
    
    read -p "Enter path to food cavity risk model [default/none]: " nutrition_model
    if [ "$nutrition_model" = "none" ]; then
        nutrition_model=""
    elif [ -z "$nutrition_model" ]; then
        nutrition_model="$FOOD_MODEL"
    fi
    
    read -p "Enter path to oral cancer model [default/none]: " oral_cancer_model
    if [ "$oral_cancer_model" = "none" ]; then
        oral_cancer_model=""
    elif [ -z "$oral_cancer_model" ]; then
        oral_cancer_model="$ORAL_CANCER_MODEL"
    fi
    
    read -p "Enter path to facial analysis model [default/none]: " face_model
    if [ "$face_model" = "none" ]; then
        face_model=""
    elif [ -z "$face_model" ]; then
        face_model="$FACE_MODEL"
    fi
    
    # Check if at least one model is specified
    if [ -z "$detection_model" ] && [ -z "$cavity_model" ] && [ -z "$nutrition_model" ] && [ -z "$oral_cancer_model" ] && [ -z "$face_model" ]; then
        echo "At least one model must be specified"
        read -p "Press Enter to continue..."
        return
    fi
    
    # Ask for inputs
    read -p "Enter path to dental image [none]: " dental_image
    if [ "$dental_image" = "none" ]; then
        dental_image=""
    fi
    
    read -p "Enter path to oral image [none]: " oral_image
    if [ "$oral_image" = "none" ]; then
        oral_image=""
    fi
    
    read -p "Enter path to face image [none]: " face_image
    if [ "$face_image" = "none" ]; then
        face_image=""
    fi
    
    read -p "Enter path to nutrition data [none]: " nutrition_data
    if [ "$nutrition_data" = "none" ]; then
        nutrition_data=""
    fi
    
    # Check if at least one input is specified
    if [ -z "$dental_image" ] && [ -z "$oral_image" ] && [ -z "$face_image" ] && [ -z "$nutrition_data" ]; then
        echo "At least one input must be specified"
        read -p "Press Enter to continue..."
        return
    fi
    
    # Create output directory
    output_path="$OUTPUT_DIR/dental_advisor_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$output_path"
    
    # Prepare command
    cmd="python3 dental_health_advisor.py --output_dir \"$output_path\""
    
    # Add model paths if specified
    if [ ! -z "$detection_model" ]; then
        cmd="$cmd --detection_model \"$detection_model\""
    fi
    
    if [ ! -z "$cavity_model" ]; then
        cmd="$cmd --classification_model \"$cavity_model\""
    fi
    
    if [ ! -z "$nutrition_model" ]; then
        cmd="$cmd --nutrition_model \"$nutrition_model\""
    fi
    
    if [ ! -z "$oral_cancer_model" ]; then
        cmd="$cmd --oral_cancer_model \"$oral_cancer_model\""
    fi
    
    if [ ! -z "$face_model" ]; then
        cmd="$cmd --face_model \"$face_model\""
    fi
    
    # Add inputs if specified
    if [ ! -z "$dental_image" ]; then
        cmd="$cmd --dental_image \"$dental_image\""
    fi
    
    if [ ! -z "$oral_image" ]; then
        cmd="$cmd --oral_image \"$oral_image\""
    fi
    
    if [ ! -z "$face_image" ]; then
        cmd="$cmd --face_image \"$face_image\""
    fi
    
    if [ ! -z "$nutrition_data" ]; then
        cmd="$cmd --nutrition_data \"$nutrition_data\""
    fi
    
    # Run analysis
    eval $cmd
    
    echo "Results saved to $output_path"
    read -p "Press Enter to continue..."
}

run_test_script_oral_cancer() {
    echo "Running Oral Cancer Detection Test Script..."
    echo "-------------------------------------------"
    
    # Ask if they want to download sample images
    read -p "Download sample images? (y/n) [y]: " download_samples
    download_samples=${download_samples:-y}
    
    # Create output directory
    output_path="$OUTPUT_DIR/oral_cancer_test_$(date +%Y%m%d_%H%M%S)"
    
    # Prepare command
    cmd="python3 test_oral_cancer.py --output_dir \"$output_path\""
    
    # Add options based on input
    if [[ "$download_samples" =~ ^[Yy]$ ]]; then
        cmd="$cmd --download_samples"
    fi
    
    # Ask if they want to show visualizations
    read -p "Show visualizations? (y/n) [n]: " show_vis
    if [[ "$show_vis" =~ ^[Yy]$ ]]; then
        cmd="$cmd --show"
    fi
    
    # Ask for model path
    read -p "Enter path to oral cancer model [default]: " model_path
    if [ ! -z "$model_path" ]; then
        cmd="$cmd --model_path \"$model_path\""
    fi
    
    # Run test script
    eval $cmd
    
    echo "Test results saved to $output_path"
    read -p "Press Enter to continue..."
}

run_test_script_facial_analysis() {
    echo "Running Facial Analysis Test Script..."
    echo "-------------------------------------"
    
    # Ask if they want to download sample images
    read -p "Download sample images? (y/n) [y]: " download_samples
    download_samples=${download_samples:-y}
    
    # Create output directory
    output_path="$OUTPUT_DIR/facial_analysis_test_$(date +%Y%m%d_%H%M%S)"
    
    # Prepare command
    cmd="python3 test_face_analysis.py --output_dir \"$output_path\""
    
    # Add options based on input
    if [[ "$download_samples" =~ ^[Yy]$ ]]; then
        cmd="$cmd --download_samples"
    fi
    
    # Ask if they want to show visualizations
    read -p "Show visualizations? (y/n) [n]: " show_vis
    if [[ "$show_vis" =~ ^[Yy]$ ]]; then
        cmd="$cmd --show"
    fi
    
    # Ask if they want detailed analysis
    read -p "Show detailed facial feature analysis? (y/n) [y]: " detailed
    detailed=${detailed:-y}
    if [[ "$detailed" =~ ^[Yy]$ ]]; then
        cmd="$cmd --detailed"
    fi
    
    # Ask for model path
    read -p "Enter path to facial analysis model [default]: " model_path
    if [ ! -z "$model_path" ]; then
        cmd="$cmd --model_path \"$model_path\""
    fi
    
    # Run test script
    eval $cmd
    
    echo "Test results saved to $output_path"
    read -p "Press Enter to continue..."
}

run_thermal_test() {
    echo ""
    echo "=== Run Thermal Imaging Analysis Test ==="
    echo ""
    
    # Check if model exists
    if [ ! -f "$THERMAL_MODEL" ]; then
        echo "Thermal analysis model not found at $THERMAL_MODEL"
        echo "Test script will download sample images and generate expected results."
    fi
    
    # Prompt for download_samples
    read -p "Download sample thermal images? (y/n) [default: y]: " download_samples
    if [ "$download_samples" = "y" ] || [ "$download_samples" = "Y" ] || [ -z "$download_samples" ]; then
        download_flag="--download_samples"
    else
        download_flag=""
    fi
    
    # Prompt for detailed analysis
    read -p "Enable detailed analysis? (y/n) [default: n]: " detailed
    if [ "$detailed" = "y" ] || [ "$detailed" = "Y" ]; then
        detailed_flag="--detailed"
    else
        detailed_flag=""
    fi
    
    # Create output directory
    mkdir -p "$OUTPUT_DIR/thermal_test_results"
    
    # Run the test script
    echo ""
    echo "Running thermal imaging analysis test..."
    echo ""
    
    python3 test_thermal.py \
        --model_path "$THERMAL_MODEL" \
        --output_dir "$OUTPUT_DIR/thermal_test_results" \
        $download_flag \
        $detailed_flag
    
    # Return to menu
    echo ""
    read -p "Press Enter to return to the menu..." dummy
}

# Process choice
process_choice() {
    case $choice in
        1) train_cavity_model ;;
        2) train_food_model ;;
        3) train_oral_cancer_model ;;
        4) train_facial_analysis_model ;;
        5)
            train_thermal_analysis_model
            ;;
        6) predict_cavity ;;
        7) predict_food_risk ;;
        8) analyze_facial_features ;;
        9)
            analyze_thermal_images
            ;;
        10) run_test_script_oral_cancer ;;
        11) run_test_script_facial_analysis ;;
        12) run_thermal_test ;;
        13) comprehensive_analysis ;;
        14) echo "Exiting..."; exit 0 ;;
        *) echo "Invalid option. Please try again."
           read -p "Press Enter to continue..." dummy ;;
    esac
}

# Main loop
while true; do
    show_menu
    process_choice
done 