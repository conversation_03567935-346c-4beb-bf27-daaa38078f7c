#!/usr/bin/env python3
"""
Facial Analysis for Dental Health

This script analyzes facial images to detect features related to dental health,
such as facial asymmetry, jaw alignment, and signs of dental conditions.
Uses the Tufts Face Database for training and validation.
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import torchvision
from torchvision import transforms, models
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import argparse
import json
from pathlib import Path
from PIL import Image
import cv2
from sklearn.model_selection import train_test_split
from sklearn.metrics import confusion_matrix, classification_report, accuracy_score
import kagglehub
import time
import face_recognition
import dlib

# Set up argument parser
def parse_args():
    parser = argparse.ArgumentParser(description="Facial Analysis for Dental Health")
    parser.add_argument("--dataset", type=str, default="kpvisionlab/tufts-face-database",
                      help="Kaggle dataset identifier")
    parser.add_argument("--output_dir", type=str, default="./facial_analysis_output",
                      help="Output directory for model and results")
    parser.add_argument("--batch_size", type=int, default=16,
                      help="Training batch size")
    parser.add_argument("--epochs", type=int, default=25,
                      help="Number of training epochs")
    parser.add_argument("--learning_rate", type=float, default=0.0001,
                      help="Learning rate")
    parser.add_argument("--model", type=str, default="resnet50",
                      choices=["resnet18", "resnet50", "densenet121"],
                      help="Model architecture")
    parser.add_argument("--image_size", type=int, default=256,
                      help="Image size for training")
    parser.add_argument("--device", type=str, default=None,
                      help="Device to use (cuda, mps, cpu, or None for auto-detection)")
    parser.add_argument("--features", nargs='+', 
                      default=["asymmetry", "jaw_alignment", "smile_analysis"],
                      help="Features to analyze")
    return parser.parse_args()

# Custom dataset for facial images
class FacialDataset(Dataset):
    def __init__(self, image_paths, labels, transform=None):
        self.image_paths = image_paths
        self.labels = labels
        self.transform = transform
        
    def __len__(self):
        return len(self.image_paths)
    
    def __getitem__(self, idx):
        img_path = self.image_paths[idx]
        
        try:
            # Load image
            image = Image.open(img_path).convert('RGB')
            
            # Apply transformations
            if self.transform:
                image = self.transform(image)
                
            # Get label
            label = self.labels[idx]
            
            return image, label
        
        except Exception as e:
            print(f"Error loading image {img_path}: {e}")
            # Return a placeholder image and the label
            placeholder = torch.zeros((3, 256, 256))
            return placeholder, self.labels[idx]

# Function to download and prepare the dataset
def download_and_prepare_dataset(dataset_id):
    """Download dataset using kagglehub and organize it"""
    print(f"Downloading dataset: {dataset_id}")
    
    try:
        # Download the dataset using kagglehub
        dataset_path = kagglehub.dataset_download(dataset_id)
        print(f"Dataset downloaded to: {dataset_path}")
        
        # Find facial images and create labels
        image_paths = []
        labels = []  # We'll create synthetic labels based on facial features
        
        # Walk through the dataset directory
        for root, _, files in os.walk(dataset_path):
            for file in files:
                if file.lower().endswith(('.png', '.jpg', '.jpeg')):
                    img_path = os.path.join(root, file)
                    try:
                        # Attempt to detect face in the image
                        image = face_recognition.load_image_file(img_path)
                        face_locations = face_recognition.face_locations(image)
                        
                        # Only include images with faces
                        if len(face_locations) > 0:
                            image_paths.append(img_path)
                            
                            # Generate synthetic label based on facial features
                            # This is where we would analyze facial symmetry, jaw alignment, etc.
                            # For now, we'll use random labels for demonstration
                            features = analyze_facial_features(image, face_locations[0])
                            
                            # Create label based on features (0 for normal, 1 for potential dental issue)
                            # This is simplified - in reality would use more sophisticated analysis
                            label = 1 if (features['asymmetry'] > 0.2 or 
                                          features['jaw_alignment'] > 0.2 or
                                          features['smile_analysis'] > 0.2) else 0
                            
                            labels.append(label)
                    except Exception as e:
                        print(f"Error processing {img_path}: {e}")
                        continue
        
        print(f"Found {len(image_paths)} facial images")
        print(f"Labels distribution: {labels.count(1)} potential issues, {labels.count(0)} normal")
        
        return dataset_path, image_paths, labels
    except Exception as e:
        print(f"Error downloading or preparing dataset: {e}")
        sys.exit(1)

def analyze_facial_features(image, face_location):
    """
    Analyze facial features related to dental health
    
    Parameters:
    - image: Face image to analyze
    - face_location: Location of the face in the image (top, right, bottom, left)
    
    Returns:
    - Dictionary of facial features with scores (0-1 scale)
    """
    features = {
        'asymmetry': 0.0,
        'jaw_alignment': 0.0,
        'smile_analysis': 0.0
    }
    
    try:
        # Extract face landmarks
        face_landmarks = face_recognition.face_landmarks(image)
        
        if len(face_landmarks) > 0:
            landmarks = face_landmarks[0]
            
            # Calculate facial asymmetry
            left_eye = np.mean(landmarks['left_eye'], axis=0)
            right_eye = np.mean(landmarks['right_eye'], axis=0)
            nose_tip = landmarks['nose_tip'][0]
            
            # Basic asymmetry: difference between midpoint of eyes and nose tip
            eye_midpoint = [(left_eye[0] + right_eye[0]) / 2, (left_eye[1] + right_eye[1]) / 2]
            horizontal_asymmetry = abs(eye_midpoint[0] - nose_tip[0]) / (right_eye[0] - left_eye[0])
            features['asymmetry'] = min(horizontal_asymmetry, 1.0)
            
            # Jaw alignment based on chin and jaw points
            if 'chin' in landmarks:
                chin_points = landmarks['chin']
                jaw_points = []
                if len(chin_points) >= 3:
                    # Calculate angle of the jaw line
                    jaw_angle_left = np.arctan2(chin_points[0][1] - chin_points[2][1], 
                                               chin_points[0][0] - chin_points[2][0])
                    jaw_angle_right = np.arctan2(chin_points[-1][1] - chin_points[-3][1], 
                                                chin_points[-1][0] - chin_points[-3][0])
                    
                    # Jaw misalignment score based on angle difference
                    angle_diff = abs(jaw_angle_left - jaw_angle_right)
                    features['jaw_alignment'] = min(angle_diff / np.pi, 1.0)
            
            # Smile analysis
            if 'top_lip' in landmarks and 'bottom_lip' in landmarks:
                top_lip = landmarks['top_lip']
                bottom_lip = landmarks['bottom_lip']
                
                # Calculate lip curvature and symmetry
                # This is a simplified approach - real analysis would be more complex
                left_corner = top_lip[0]
                right_corner = top_lip[6]
                center_point = top_lip[3]
                
                # Check smile symmetry
                left_dist = np.sqrt((left_corner[0] - center_point[0])**2 + (left_corner[1] - center_point[1])**2)
                right_dist = np.sqrt((right_corner[0] - center_point[0])**2 + (right_corner[1] - center_point[1])**2)
                
                smile_asymmetry = abs(left_dist - right_dist) / max(left_dist, right_dist)
                features['smile_analysis'] = min(smile_asymmetry, 1.0)
                
    except Exception as e:
        print(f"Error analyzing facial features: {e}")
    
    return features

# Function to create data loaders
def create_data_loaders(image_paths, labels, image_size, batch_size):
    """Create PyTorch data loaders from image paths and labels"""
    print("Creating data loaders...")
    
    # Split data into train, validation, and test sets
    X_train, X_temp, y_train, y_temp = train_test_split(
        image_paths, labels, test_size=0.3, random_state=42, stratify=labels
    )
    X_val, X_test, y_val, y_test = train_test_split(
        X_temp, y_temp, test_size=0.5, random_state=42, stratify=y_temp
    )
    
    # Define transformations
    train_transform = transforms.Compose([
        transforms.Resize((image_size, image_size)),
        transforms.RandomHorizontalFlip(),
        transforms.RandomRotation(10),
        transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.1),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    val_transform = transforms.Compose([
        transforms.Resize((image_size, image_size)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    # Create datasets
    train_dataset = FacialDataset(X_train, y_train, transform=train_transform)
    val_dataset = FacialDataset(X_val, y_val, transform=val_transform)
    test_dataset = FacialDataset(X_test, y_test, transform=val_transform)
    
    # Create dataloaders
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=2)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, num_workers=2)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, num_workers=2)
    
    print(f"Train: {len(train_dataset)} samples")
    print(f"Validation: {len(val_dataset)} samples")
    print(f"Test: {len(test_dataset)} samples")
    
    return train_loader, val_loader, test_loader

# Function to create model
def create_model(model_name, num_classes=2):
    """Create a model with the specified architecture"""
    print(f"Creating model: {model_name}")
    
    if model_name == "resnet18":
        model = models.resnet18(weights=models.ResNet18_Weights.DEFAULT)
        model.fc = nn.Linear(model.fc.in_features, num_classes)
    elif model_name == "resnet50":
        model = models.resnet50(weights=models.ResNet50_Weights.DEFAULT)
        model.fc = nn.Linear(model.fc.in_features, num_classes)
    elif model_name == "densenet121":
        model = models.densenet121(weights=models.DenseNet121_Weights.DEFAULT)
        model.classifier = nn.Linear(model.classifier.in_features, num_classes)
    else:
        raise ValueError(f"Unsupported model: {model_name}")
    
    return model

# Function for training
def train_model(model, train_loader, val_loader, criterion, optimizer, device, epochs, output_dir):
    """Train the model and track metrics"""
    print(f"Training model for {epochs} epochs...")
    
    # Training metrics
    train_losses = []
    val_losses = []
    train_accs = []
    val_accs = []
    best_val_loss = float('inf')
    best_model_path = os.path.join(output_dir, "model_best.pt")
    
    # Training loop
    for epoch in range(epochs):
        epoch_start_time = time.time()
        
        # Training
        model.train()
        train_loss = 0
        train_correct = 0
        train_total = 0
        
        for inputs, targets in train_loader:
            inputs, targets = inputs.to(device), targets.to(device)
            
            # Forward pass
            outputs = model(inputs)
            loss = criterion(outputs, targets)
            
            # Backward pass and optimize
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            # Track metrics
            train_loss += loss.item() * inputs.size(0)
            _, predicted = torch.max(outputs, 1)
            train_total += targets.size(0)
            train_correct += (predicted == targets).sum().item()
        
        # Average training metrics
        train_loss = train_loss / train_total
        train_accuracy = train_correct / train_total
        train_losses.append(train_loss)
        train_accs.append(train_accuracy)
        
        # Validation
        model.eval()
        val_loss = 0
        val_correct = 0
        val_total = 0
        
        with torch.no_grad():
            for inputs, targets in val_loader:
                inputs, targets = inputs.to(device), targets.to(device)
                
                # Forward pass
                outputs = model(inputs)
                loss = criterion(outputs, targets)
                
                # Track metrics
                val_loss += loss.item() * inputs.size(0)
                _, predicted = torch.max(outputs, 1)
                val_total += targets.size(0)
                val_correct += (predicted == targets).sum().item()
        
        # Average validation metrics
        val_loss = val_loss / val_total
        val_accuracy = val_correct / val_total
        val_losses.append(val_loss)
        val_accs.append(val_accuracy)
        
        # Save best model
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            torch.save({
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'epoch': epoch,
                'val_loss': val_loss,
                'val_accuracy': val_accuracy,
                'model_name': args.model,
                'image_size': args.image_size
            }, best_model_path)
            print(f"Saved best model at epoch {epoch+1}")
        
        # Print epoch results
        epoch_time = time.time() - epoch_start_time
        print(f"Epoch {epoch+1}/{epochs}: "
              f"Train Loss={train_loss:.4f}, Train Acc={train_accuracy:.4f}, "
              f"Val Loss={val_loss:.4f}, Val Acc={val_accuracy:.4f}, "
              f"Time={epoch_time:.1f}s")
    
    # Plot and save training curves
    plot_training_results(train_losses, val_losses, train_accs, val_accs, os.path.join(output_dir, "training_curves.png"))
    
    return train_losses, val_losses, train_accs, val_accs

# Function to plot training results
def plot_training_results(train_losses, val_losses, train_accs, val_accs, output_path):
    """Plot training and validation metrics"""
    plt.figure(figsize=(12, 5))
    
    # Plot losses
    plt.subplot(1, 2, 1)
    plt.plot(train_losses, label='Train Loss')
    plt.plot(val_losses, label='Validation Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Training and Validation Loss')
    plt.legend()
    
    # Plot accuracy
    plt.subplot(1, 2, 2)
    plt.plot(train_accs, label='Train Accuracy')
    plt.plot(val_accs, label='Validation Accuracy')
    plt.xlabel('Epoch')
    plt.ylabel('Accuracy')
    plt.title('Training and Validation Accuracy')
    plt.legend()
    
    plt.tight_layout()
    plt.savefig(output_path)
    plt.close()

# Function to evaluate model on test set
def evaluate_model(model, test_loader, criterion, device, output_dir):
    """Evaluate the model on the test set"""
    print("Evaluating model on test set...")
    
    model.eval()
    test_loss = 0
    all_preds = []
    all_targets = []
    
    with torch.no_grad():
        for inputs, targets in test_loader:
            inputs, targets = inputs.to(device), targets.to(device)
            
            # Forward pass
            outputs = model(inputs)
            loss = criterion(outputs, targets)
            
            # Track metrics
            test_loss += loss.item() * inputs.size(0)
            _, predicted = torch.max(outputs, 1)
            
            # Store predictions and targets
            all_preds.extend(predicted.cpu().numpy())
            all_targets.extend(targets.cpu().numpy())
    
    # Calculate metrics
    test_loss = test_loss / len(test_loader.dataset)
    test_accuracy = accuracy_score(all_targets, all_preds)
    
    # Generate classification report
    class_names = ['Normal', 'Potential Issue']
    clf_report = classification_report(all_targets, all_preds, target_names=class_names, output_dict=True)
    
    # Plot confusion matrix
    cm = confusion_matrix(all_targets, all_preds)
    plot_confusion_matrix(cm, class_names, os.path.join(output_dir, "confusion_matrix.png"))
    
    # Print results
    print(f"Test Loss: {test_loss:.4f}")
    print(f"Test Accuracy: {test_accuracy:.4f}")
    print("\nClassification Report:")
    print(classification_report(all_targets, all_preds, target_names=class_names))
    
    # Save results to JSON
    results = {
        'test_loss': test_loss,
        'test_accuracy': test_accuracy,
        'classification_report': clf_report
    }
    
    with open(os.path.join(output_dir, "test_results.json"), "w") as f:
        json.dump(results, f, indent=4)
    
    return results

# Function to plot confusion matrix
def plot_confusion_matrix(cm, class_names, output_path):
    """Plot confusion matrix"""
    plt.figure(figsize=(10, 8))
    
    # Normalize confusion matrix
    cm_norm = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]
    
    sns.heatmap(cm_norm, annot=cm, fmt='d', cmap='Blues', 
               xticklabels=class_names, yticklabels=class_names)
    
    plt.xlabel('Predicted')
    plt.ylabel('True')
    plt.title('Confusion Matrix')
    plt.tight_layout()
    plt.savefig(output_path)
    plt.close()

def main():
    global args
    args = parse_args()
    
    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Set device
    if args.device:
        device = torch.device(args.device)
    else:
        device = torch.device("cuda" if torch.cuda.is_available() else 
                             "mps" if torch.backends.mps.is_available() else 
                             "cpu")
    print(f"Using device: {device}")
    
    # Download and prepare dataset
    dataset_path, image_paths, labels = download_and_prepare_dataset(args.dataset)
    
    # Create data loaders
    train_loader, val_loader, test_loader = create_data_loaders(
        image_paths, labels, args.image_size, args.batch_size
    )
    
    # Create model
    model = create_model(args.model)
    model = model.to(device)
    
    # Define loss function and optimizer
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=args.learning_rate)
    
    # Train model
    train_losses, val_losses, train_accs, val_accs = train_model(
        model, train_loader, val_loader, criterion, optimizer, device, args.epochs, output_dir
    )
    
    # Load best model for evaluation
    best_model_path = os.path.join(output_dir, "model_best.pt")
    checkpoint = torch.load(best_model_path)
    model.load_state_dict(checkpoint['model_state_dict'])
    
    # Evaluate model
    test_results = evaluate_model(model, test_loader, criterion, device, output_dir)
    
    # Save model info
    model_info = {
        'model_name': args.model,
        'image_size': args.image_size,
        'dataset_id': args.dataset,
        'dataset_path': dataset_path,
        'num_samples': len(image_paths),
        'potential_issue_samples': labels.count(1),
        'normal_samples': labels.count(0),
        'batch_size': args.batch_size,
        'epochs': args.epochs,
        'learning_rate': args.learning_rate,
        'features_analyzed': args.features,
        'test_accuracy': test_results['test_accuracy'],
        'class_names': ['Normal', 'Potential Issue'],
    }
    
    with open(os.path.join(output_dir, "model_info.json"), "w") as f:
        json.dump(model_info, f, indent=4)
    
    print(f"\nTraining complete! Model and results saved to {output_dir}")

if __name__ == "__main__":
    main() 