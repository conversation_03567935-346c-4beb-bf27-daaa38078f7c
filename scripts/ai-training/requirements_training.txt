# Core libraries
torch>=2.0.0
torchvision>=0.15.0
numpy>=1.20.0
pandas>=1.3.0
matplotlib>=3.4.0
Pillow>=8.2.0
scikit-learn>=1.0.0
kagglehub>=0.2.5

# Deep learning utilities
albumentations>=1.3.0
timm>=0.6.12  # PyTorch Image Models
pytorch-lightning>=2.0.0
torchmetrics>=0.11.0

# Experiment tracking
wandb>=0.15.0
tensorboard>=2.12.0

# Model explainability
captum>=0.6.0
grad-cam>=1.4.6

# Optimization and hyperparameter tuning
optuna>=3.1.0
ray[tune]>=2.3.0

# Serialization and data processing
pydicom>=2.3.0  # For DICOM medical image processing
h5py>=3.7.0
joblib>=1.1.0

# Evaluation metrics
sklearn>=0.0.0
scikit-image>=0.19.0 