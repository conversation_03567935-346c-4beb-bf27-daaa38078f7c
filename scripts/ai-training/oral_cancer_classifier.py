#!/usr/bin/env python3
"""
Oral Cancer Prediction Model

This script trains a deep learning model to predict oral cancer from images
using the Kaggle Oral Cancer Prediction Dataset.
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import torchvision
from torchvision import transforms, models
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import argparse
import json
from pathlib import Path
from PIL import Image
from sklearn.model_selection import train_test_split
from sklearn.metrics import confusion_matrix, classification_report, accuracy_score
import kagglehub
import time

# Set up argument parser
def parse_args():
    parser = argparse.ArgumentParser(description="Oral Cancer Prediction Model")
    parser.add_argument("--dataset", type=str, default="ankushpanday2/oral-cancer-prediction-dataset",
                      help="Kaggle dataset identifier")
    parser.add_argument("--output_dir", type=str, default="./oral_cancer_classifier_output",
                      help="Output directory for model and results")
    parser.add_argument("--batch_size", type=int, default=32,
                      help="Training batch size")
    parser.add_argument("--epochs", type=int, default=30,
                      help="Number of training epochs")
    parser.add_argument("--learning_rate", type=float, default=0.0001,
                      help="Learning rate")
    parser.add_argument("--model", type=str, default="resnet50",
                      choices=["resnet18", "resnet50", "densenet121", "efficientnet_b0"],
                      help="Model architecture")
    parser.add_argument("--image_size", type=int, default=224,
                      help="Image size for training")
    parser.add_argument("--device", type=str, default=None,
                      help="Device to use (cuda, mps, cpu, or None for auto-detection)")
    parser.add_argument("--augmentation", action="store_true", default=True,
                      help="Use data augmentation")
    return parser.parse_args()

# Custom dataset for oral cancer images
class OralCancerDataset(Dataset):
    def __init__(self, image_paths, labels, transform=None):
        self.image_paths = image_paths
        self.labels = labels
        self.transform = transform
        
    def __len__(self):
        return len(self.image_paths)
    
    def __getitem__(self, idx):
        img_path = self.image_paths[idx]
        
        try:
            # Load image
            image = Image.open(img_path).convert('RGB')
            
            # Apply transformations
            if self.transform:
                image = self.transform(image)
                
            # Get label
            label = self.labels[idx]
            
            return image, label
        
        except Exception as e:
            print(f"Error loading image {img_path}: {e}")
            # Return a placeholder image and the label
            placeholder = torch.zeros((3, 224, 224))
            return placeholder, self.labels[idx]

# Function to download and prepare the dataset
def download_and_prepare_dataset(dataset_id):
    """Download dataset using kagglehub and organize it"""
    print(f"Downloading dataset: {dataset_id}")
    
    try:
        # Download the dataset using kagglehub
        dataset_path = kagglehub.dataset_download(f"{dataset_id}")
        print(f"Dataset downloaded to: {dataset_path}")
        
        # Find image directories
        image_paths = []
        labels = []
        label_map = {'cancer': 1, 'non-cancer': 0}
        
        # Process directories assuming standard structure
        for class_name in ['cancer', 'non-cancer']:
            class_dir = os.path.join(dataset_path, class_name)
            if os.path.exists(class_dir) and os.path.isdir(class_dir):
                for img_file in os.listdir(class_dir):
                    if img_file.lower().endswith(('.png', '.jpg', '.jpeg')):
                        img_path = os.path.join(class_dir, img_file)
                        image_paths.append(img_path)
                        labels.append(label_map[class_name])
                        
        print(f"Found {len(image_paths)} images: {labels.count(1)} cancer, {labels.count(0)} non-cancer")
        
        # If no images are found, check for a different structure
        if not image_paths:
            print("Standard structure not found. Searching for images recursively...")
            for root, dirs, files in os.walk(dataset_path):
                for file in files:
                    if file.lower().endswith(('.png', '.jpg', '.jpeg')):
                        img_path = os.path.join(root, file)
                        # Try to infer label from directory name
                        if "cancer" in root.lower() and "non" not in root.lower():
                            label = 1  # cancer
                        elif "non" in root.lower() and "cancer" in root.lower():
                            label = 0  # non-cancer
                        else:
                            continue  # Skip if can't determine label
                        
                        image_paths.append(img_path)
                        labels.append(label)
            
            print(f"Found {len(image_paths)} images recursively: {labels.count(1)} cancer, {labels.count(0)} non-cancer")
        
        return dataset_path, image_paths, labels
    except Exception as e:
        print(f"Error downloading or preparing dataset: {e}")
        sys.exit(1)

# Function to create data loaders
def create_data_loaders(image_paths, labels, image_size, batch_size, augmentation=True):
    """Create PyTorch data loaders from image paths and labels"""
    print("Creating data loaders...")
    
    # Split data into train, validation, and test sets
    X_train, X_temp, y_train, y_temp = train_test_split(
        image_paths, labels, test_size=0.3, random_state=42, stratify=labels
    )
    X_val, X_test, y_val, y_test = train_test_split(
        X_temp, y_temp, test_size=0.5, random_state=42, stratify=y_temp
    )
    
    # Define transformations
    train_transform = transforms.Compose([
        transforms.Resize((image_size, image_size)),
        transforms.RandomHorizontalFlip(),
        transforms.RandomRotation(10),
        transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.1, hue=0.1),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ]) if augmentation else transforms.Compose([
        transforms.Resize((image_size, image_size)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    val_transform = transforms.Compose([
        transforms.Resize((image_size, image_size)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    # Create datasets
    train_dataset = OralCancerDataset(X_train, y_train, transform=train_transform)
    val_dataset = OralCancerDataset(X_val, y_val, transform=val_transform)
    test_dataset = OralCancerDataset(X_test, y_test, transform=val_transform)
    
    # Create dataloaders
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=2)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, num_workers=2)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, num_workers=2)
    
    print(f"Train: {len(train_dataset)} samples")
    print(f"Validation: {len(val_dataset)} samples")
    print(f"Test: {len(test_dataset)} samples")
    
    return train_loader, val_loader, test_loader

# Function to create model
def create_model(model_name, num_classes=2):
    """Create a model with the specified architecture"""
    print(f"Creating model: {model_name}")
    
    if model_name == "resnet18":
        model = models.resnet18(weights=models.ResNet18_Weights.DEFAULT)
        model.fc = nn.Linear(model.fc.in_features, num_classes)
    elif model_name == "resnet50":
        model = models.resnet50(weights=models.ResNet50_Weights.DEFAULT)
        model.fc = nn.Linear(model.fc.in_features, num_classes)
    elif model_name == "densenet121":
        model = models.densenet121(weights=models.DenseNet121_Weights.DEFAULT)
        model.classifier = nn.Linear(model.classifier.in_features, num_classes)
    elif model_name == "efficientnet_b0":
        model = models.efficientnet_b0(weights=models.EfficientNet_B0_Weights.DEFAULT)
        model.classifier[1] = nn.Linear(model.classifier[1].in_features, num_classes)
    else:
        raise ValueError(f"Unsupported model: {model_name}")
    
    return model

# Function for training
def train_model(model, train_loader, val_loader, criterion, optimizer, device, epochs, output_dir):
    """Train the model and track metrics"""
    print(f"Training model for {epochs} epochs...")
    
    # Training metrics
    train_losses = []
    val_losses = []
    train_accs = []
    val_accs = []
    best_val_loss = float('inf')
    best_model_path = os.path.join(output_dir, "model_best.pt")
    
    # Training loop
    for epoch in range(epochs):
        epoch_start_time = time.time()
        
        # Training
        model.train()
        train_loss = 0
        train_correct = 0
        train_total = 0
        
        for inputs, targets in train_loader:
            inputs, targets = inputs.to(device), targets.to(device)
            
            # Forward pass
            outputs = model(inputs)
            loss = criterion(outputs, targets)
            
            # Backward pass and optimize
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            # Track metrics
            train_loss += loss.item() * inputs.size(0)
            _, predicted = torch.max(outputs, 1)
            train_total += targets.size(0)
            train_correct += (predicted == targets).sum().item()
        
        # Average training metrics
        train_loss = train_loss / train_total
        train_accuracy = train_correct / train_total
        train_losses.append(train_loss)
        train_accs.append(train_accuracy)
        
        # Validation
        model.eval()
        val_loss = 0
        val_correct = 0
        val_total = 0
        
        with torch.no_grad():
            for inputs, targets in val_loader:
                inputs, targets = inputs.to(device), targets.to(device)
                
                # Forward pass
                outputs = model(inputs)
                loss = criterion(outputs, targets)
                
                # Track metrics
                val_loss += loss.item() * inputs.size(0)
                _, predicted = torch.max(outputs, 1)
                val_total += targets.size(0)
                val_correct += (predicted == targets).sum().item()
        
        # Average validation metrics
        val_loss = val_loss / val_total
        val_accuracy = val_correct / val_total
        val_losses.append(val_loss)
        val_accs.append(val_accuracy)
        
        # Save best model
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            torch.save({
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'epoch': epoch,
                'val_loss': val_loss,
                'val_accuracy': val_accuracy,
                'model_name': args.model,
                'image_size': args.image_size
            }, best_model_path)
            print(f"Saved best model at epoch {epoch+1}")
        
        # Print epoch results
        epoch_time = time.time() - epoch_start_time
        print(f"Epoch {epoch+1}/{epochs}: "
              f"Train Loss={train_loss:.4f}, Train Acc={train_accuracy:.4f}, "
              f"Val Loss={val_loss:.4f}, Val Acc={val_accuracy:.4f}, "
              f"Time={epoch_time:.1f}s")
    
    # Plot and save training curves
    plot_training_results(train_losses, val_losses, train_accs, val_accs, os.path.join(output_dir, "training_curves.png"))
    
    return train_losses, val_losses, train_accs, val_accs

# Function to plot training results
def plot_training_results(train_losses, val_losses, train_accs, val_accs, output_path):
    """Plot training and validation metrics"""
    plt.figure(figsize=(12, 5))
    
    # Plot losses
    plt.subplot(1, 2, 1)
    plt.plot(train_losses, label='Train Loss')
    plt.plot(val_losses, label='Validation Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Training and Validation Loss')
    plt.legend()
    
    # Plot accuracy
    plt.subplot(1, 2, 2)
    plt.plot(train_accs, label='Train Accuracy')
    plt.plot(val_accs, label='Validation Accuracy')
    plt.xlabel('Epoch')
    plt.ylabel('Accuracy')
    plt.title('Training and Validation Accuracy')
    plt.legend()
    
    plt.tight_layout()
    plt.savefig(output_path)
    plt.close()

# Function to evaluate model on test set
def evaluate_model(model, test_loader, criterion, device, output_dir):
    """Evaluate the model on the test set"""
    print("Evaluating model on test set...")
    
    model.eval()
    test_loss = 0
    all_preds = []
    all_targets = []
    
    with torch.no_grad():
        for inputs, targets in test_loader:
            inputs, targets = inputs.to(device), targets.to(device)
            
            # Forward pass
            outputs = model(inputs)
            loss = criterion(outputs, targets)
            
            # Track metrics
            test_loss += loss.item() * inputs.size(0)
            _, predicted = torch.max(outputs, 1)
            
            # Store predictions and targets
            all_preds.extend(predicted.cpu().numpy())
            all_targets.extend(targets.cpu().numpy())
    
    # Calculate metrics
    test_loss = test_loss / len(test_loader.dataset)
    test_accuracy = accuracy_score(all_targets, all_preds)
    
    # Generate classification report
    class_names = ['Non-Cancer', 'Cancer']
    clf_report = classification_report(all_targets, all_preds, target_names=class_names, output_dict=True)
    
    # Plot confusion matrix
    cm = confusion_matrix(all_targets, all_preds)
    plot_confusion_matrix(cm, class_names, os.path.join(output_dir, "confusion_matrix.png"))
    
    # Print results
    print(f"Test Loss: {test_loss:.4f}")
    print(f"Test Accuracy: {test_accuracy:.4f}")
    print("\nClassification Report:")
    print(classification_report(all_targets, all_preds, target_names=class_names))
    
    # Save results to JSON
    results = {
        'test_loss': test_loss,
        'test_accuracy': test_accuracy,
        'classification_report': clf_report
    }
    
    with open(os.path.join(output_dir, "test_results.json"), "w") as f:
        json.dump(results, f, indent=4)
    
    return results

# Function to plot confusion matrix
def plot_confusion_matrix(cm, class_names, output_path):
    """Plot confusion matrix"""
    plt.figure(figsize=(10, 8))
    
    # Normalize confusion matrix
    cm_norm = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]
    
    sns.heatmap(cm_norm, annot=cm, fmt='d', cmap='Blues', 
               xticklabels=class_names, yticklabels=class_names)
    
    plt.xlabel('Predicted')
    plt.ylabel('True')
    plt.title('Confusion Matrix')
    plt.tight_layout()
    plt.savefig(output_path)
    plt.close()

def main():
    global args
    args = parse_args()
    
    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Set device
    if args.device:
        device = torch.device(args.device)
    else:
        device = torch.device("cuda" if torch.cuda.is_available() else 
                             "mps" if torch.backends.mps.is_available() else 
                             "cpu")
    print(f"Using device: {device}")
    
    # Download and prepare dataset
    dataset_path, image_paths, labels = download_and_prepare_dataset(args.dataset)
    
    # Create data loaders
    train_loader, val_loader, test_loader = create_data_loaders(
        image_paths, labels, args.image_size, args.batch_size, args.augmentation
    )
    
    # Create model
    model = create_model(args.model)
    model = model.to(device)
    
    # Define loss function and optimizer
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=args.learning_rate)
    
    # Train model
    train_losses, val_losses, train_accs, val_accs = train_model(
        model, train_loader, val_loader, criterion, optimizer, device, args.epochs, output_dir
    )
    
    # Load best model for evaluation
    best_model_path = os.path.join(output_dir, "model_best.pt")
    checkpoint = torch.load(best_model_path)
    model.load_state_dict(checkpoint['model_state_dict'])
    
    # Evaluate model
    test_results = evaluate_model(model, test_loader, criterion, device, output_dir)
    
    # Save model info
    model_info = {
        'model_name': args.model,
        'image_size': args.image_size,
        'dataset_id': args.dataset,
        'dataset_path': dataset_path,
        'num_samples': len(image_paths),
        'cancer_samples': labels.count(1),
        'non_cancer_samples': labels.count(0),
        'batch_size': args.batch_size,
        'epochs': args.epochs,
        'learning_rate': args.learning_rate,
        'augmentation': args.augmentation,
        'test_accuracy': test_results['test_accuracy'],
        'class_names': ['Non-Cancer', 'Cancer'],
    }
    
    with open(os.path.join(output_dir, "model_info.json"), "w") as f:
        json.dump(model_info, f, indent=4)
    
    print(f"\nTraining complete! Model and results saved to {output_dir}")

if __name__ == "__main__":
    main() 