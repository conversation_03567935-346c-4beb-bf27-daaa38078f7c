"""
Main training script for dental radiography object detection model.
Handles model training, evaluation, and deployment.
"""

import os
import sys
import argparse
from datetime import datetime
import json
import torch
import numpy as np
import random
from pathlib import Path
import pytorch_lightning as pl
from pytorch_lightning.callbacks import (
    ModelCheckpoint,
    EarlyStopping,
    LearningRateMonitor,
    TQDMProgressBar,
)
from pytorch_lightning.loggers import TensorBoardLogger, WandbLogger

# Import local modules
import config
from data_module import DentalRadiographyDataModule
from model import DentalObjectDetectionModel
from utils import set_seeds, log_hyperparameters, export_model, create_inference_model


def parse_args():
    """Parse command-line arguments"""
    parser = argparse.ArgumentParser(description="Train dental radiography object detection model")
    
    # Data arguments
    parser.add_argument("--data_path", type=str, default=config.DATASET_PATH,
                        help="Path to the dataset")
    parser.add_argument("--batch_size", type=int, default=config.BATCH_SIZE,
                        help="Batch size for training and evaluation")
    parser.add_argument("--num_workers", type=int, default=config.NUM_WORKERS,
                        help="Number of data loading workers")
    parser.add_argument("--aug_intensity", type=str, default=config.AUG_INTENSITY,
                        choices=["none", "light", "medium", "heavy"],
                        help="Intensity of data augmentation")
    
    # Model arguments
    parser.add_argument("--backbone", type=str, default=config.BACKBONE,
                        help="Backbone architecture (resnet50, efficientnet_b3, densenet121)")
    parser.add_argument("--pretrained", action="store_true", default=config.PRETRAINED,
                        help="Use pretrained weights")
    parser.add_argument("--feature_extract", action="store_true", default=config.FEATURE_EXTRACT,
                        help="Only update the reshaped layer params")
    
    # Training arguments
    parser.add_argument("--learning_rate", type=float, default=config.LEARNING_RATE,
                        help="Initial learning rate")
    parser.add_argument("--weight_decay", type=float, default=config.WEIGHT_DECAY,
                        help="Weight decay")
    parser.add_argument("--momentum", type=float, default=config.MOMENTUM,
                        help="Momentum (for SGD)")
    parser.add_argument("--epochs", type=int, default=config.EPOCHS,
                        help="Number of epochs")
    parser.add_argument("--patience", type=int, default=config.PATIENCE,
                        help="Early stopping patience")
    parser.add_argument("--optimizer", type=str, default=config.OPTIMIZER,
                        choices=["Adam", "AdamW", "SGD"],
                        help="Optimizer to use")
    parser.add_argument("--scheduler", type=str, default=config.SCHEDULER,
                        choices=["StepLR", "CosineAnnealingLR", "ReduceLROnPlateau"],
                        help="Learning rate scheduler")
    parser.add_argument("--grad_clip_val", type=float, default=config.GRAD_CLIP_VAL,
                        help="Gradient clipping value")
    
    # Experiment tracking arguments
    parser.add_argument("--use_wandb", action="store_true", default=config.USE_WANDB,
                        help="Use Weights & Biases for tracking")
    parser.add_argument("--wandb_project", type=str, default=config.WANDB_PROJECT,
                        help="W&B project name")
    parser.add_argument("--wandb_entity", type=str, default=config.WANDB_ENTITY,
                        help="W&B entity name")
    parser.add_argument("--run_name", type=str, default=config.RUN_NAME,
                        help="Run name for experiment tracking")
    
    # Hardware arguments
    parser.add_argument("--use_gpu", action="store_true", default=config.USE_GPU,
                        help="Use GPU for training")
    parser.add_argument("--precision", type=str, default=config.PRECISION,
                        choices=["16", "32", "16-mixed"],
                        help="Floating point precision")
    
    # Reproducibility arguments
    parser.add_argument("--seed", type=int, default=config.RANDOM_SEED,
                        help="Random seed")
    
    # Advanced arguments
    parser.add_argument("--use_ema", action="store_true", default=config.USE_EMA,
                        help="Use Exponential Moving Average")
    parser.add_argument("--ema_decay", type=float, default=config.EMA_DECAY,
                        help="EMA decay rate")
    parser.add_argument("--mixup_alpha", type=float, default=config.MIXUP_ALPHA,
                        help="Mixup alpha (0 to disable)")
    
    # Export arguments
    parser.add_argument("--export_format", type=str, default="torchscript",
                        choices=["torchscript", "onnx", "none"],
                        help="Export format for the trained model")
    
    # Additional arguments for troubleshooting
    parser.add_argument("--skip_validation", action="store_true", default=False,
                      help="Skip validation checks to speed up training")
    
    return parser.parse_args()


def main():
    """Main training function"""
    # Parse arguments
    args = parse_args()
    
    # Create directories
    run_dir = config.create_run_directories()
    checkpoint_dir = run_dir / "checkpoints"
    logs_dir = run_dir / "logs"
    results_dir = run_dir / "results"
    
    print(f"Run directory: {run_dir}")
    
    # Set random seeds for reproducibility
    set_seeds(args.seed)
    
    # Initialize data module
    data_module = DentalRadiographyDataModule(
        data_path=args.data_path,
        batch_size=args.batch_size,
        num_workers=args.num_workers,
        aug_intensity=args.aug_intensity
    )
    
    # Setup data module
    data_module.prepare_data()
    data_module.setup()
    
    # Get class weights for handling imbalance
    class_weights = data_module.get_class_weights()
    
    # Initialize model
    model = DentalObjectDetectionModel(
        num_classes=config.NUM_CLASSES + 1,  # +1 for background
        backbone_name=args.backbone,
        pretrained=args.pretrained,
        feature_extract=args.feature_extract,
        learning_rate=args.learning_rate,
        weight_decay=args.weight_decay,
        momentum=args.momentum,
        use_swa=args.use_ema,
        class_weights=class_weights
    )
    
    # Setup callbacks
    callbacks = [
        ModelCheckpoint(
            dirpath=checkpoint_dir,
            filename="{epoch:02d}-{val/mAP@50:.4f}",
            monitor="val/mAP@50",
            mode="max",
            save_top_k=config.SAVE_TOP_K,
            save_last=True,
            verbose=True
        ),
        EarlyStopping(
            monitor="val/mAP@50",
            mode="max",
            patience=args.patience,
            verbose=True
        ),
        LearningRateMonitor(logging_interval="epoch"),
        TQDMProgressBar(refresh_rate=10)
    ]
    
    # Setup loggers
    loggers = [
        TensorBoardLogger(logs_dir, name="dental_detection")
    ]
    
    # Add WandB logger if requested
    if args.use_wandb:
        try:
            import wandb
            wandb_logger = WandbLogger(
                project=args.wandb_project,
                entity=args.wandb_entity,
                name=args.run_name,
                log_model=True
            )
            loggers.append(wandb_logger)
            
            # Log hyperparameters
            log_hyperparameters(args, model, wandb_logger)
            
        except ImportError:
            print("Could not import wandb. Running without WandB logging.")
            args.use_wandb = False
    
    # Determine number of GPUs
    if args.use_gpu and torch.cuda.is_available():
        accelerator = "gpu"
        devices = torch.cuda.device_count()
    else:
        accelerator = "cpu"
        devices = 1  # Use 1 CPU core by default
    
    # Initialize trainer
    trainer = pl.Trainer(
        max_epochs=args.epochs,
        callbacks=callbacks,
        logger=loggers,
        accelerator=accelerator,
        devices=devices,
        precision=args.precision,
        gradient_clip_val=args.grad_clip_val,
        log_every_n_steps=10,
        deterministic=True,
        num_sanity_val_steps=0 if args.skip_validation else 2  # Skip validation checks if requested
    )
    
    # Train model
    print("Starting training...")
    trainer.fit(model, datamodule=data_module)
    
    # Test model
    print("Evaluating on test set...")
    trainer.test(model, datamodule=data_module)
    
    # Export model
    if args.export_format != "none":
        export_path = results_dir / f"model_{args.export_format}.pt"
        export_model(model, export_path, format=args.export_format)
        print(f"Model exported to {export_path}")
    
    # Create and save inference model
    inference_model = create_inference_model(model)
    inference_path = results_dir / "inference_model.pt"
    torch.save(inference_model, inference_path)
    print(f"Inference model saved to {inference_path}")
    
    # Save model configs
    with open(results_dir / "model_config.json", "w") as f:
        config_dict = {k: v for k, v in vars(args).items()}
        json.dump(config_dict, f, indent=4)
    
    print("Training completed successfully!")
    
    # Close WandB
    if args.use_wandb:
        import wandb
        wandb.finish()


if __name__ == "__main__":
    main() 