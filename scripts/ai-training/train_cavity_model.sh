#!/bin/bash

# Set default paths
MODEL_DIR="./models"
OUTPUT_DIR="./results"
CAVITY_MODEL="$MODEL_DIR/cavity_classifier_model.pt"

# Check if Python 3 is installed
if ! command -v python3 &> /dev/null; then
    echo "Error: Python 3 is required but not found"
    exit 1
fi

# Ensure required directories exist
mkdir -p "$MODEL_DIR"
mkdir -p "$OUTPUT_DIR"

echo "Training Cavity Classification Model..."
echo "----------------------------------------"

# Ask for dataset path
read -p "Enter path to cavity dataset (leave empty for default): " dataset_path
if [ -z "$dataset_path" ]; then
    dataset_path="/Users/<USER>/.cache/kagglehub/datasets/aqibrehmanpirzada/cavity-and-non-cavity-classification/versions/1/Dentalfinaldata"
fi

# Ask for model type
echo "Select model architecture:"
echo "1. ResNet18 (faster, good accuracy)"
echo "2. MobileNet (smaller size)"
echo "3. EfficientNet (higher accuracy, slower)"
read -p "Enter your choice [1-3]: " model_choice

case $model_choice in
    1) model_type="resnet18" ;;
    2) model_type="mobilenet" ;;
    3) model_type="efficientnet" ;;
    *) model_type="resnet18" ;;
esac

# Ask for training parameters
read -p "Enter batch size [32]: " batch_size
batch_size=${batch_size:-32}

read -p "Enter number of epochs [20]: " epochs
epochs=${epochs:-20}

read -p "Enter learning rate [0.001]: " learning_rate
learning_rate=${learning_rate:-0.001}

# Run training
python3 cavity_classifier.py \
    --data_path "$dataset_path" \
    --output_dir "cavity_classifier_output" \
    --batch_size $batch_size \
    --epochs $epochs \
    --learning_rate $learning_rate \
    --model $model_type

# Copy model to models directory
if [ -f "cavity_classifier_output/cavity_classifier_model.pt" ]; then
    cp "cavity_classifier_output/cavity_classifier_model.pt" "$MODEL_DIR/"
    echo "Model saved to $MODEL_DIR/cavity_classifier_model.pt"
fi

echo "Training complete!" 