#!/usr/bin/env python3
"""
Dental Dataset Manager for Smilo AI

This module provides enhanced functionality for managing dental datasets from Kaggle,
with specific focus on dental radiography datasets for AI training and inference.
It handles downloading, preprocessing, and organizing the data in a format optimized
for Smilo's AI models.

Usage:
    python dental_dataset_manager.py --dataset imtkaggleteam/dental-radiography --preprocess
"""

import os
import sys
import logging
import argparse
import json
import shutil
import hashlib
from pathlib import Path
from datetime import datetime
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Union

# Third-party imports
try:
    import kagglehub
    import cv2
    from tqdm import tqdm
    import matplotlib.pyplot as plt
    from PIL import Image
except ImportError as e:
    print(f"Error: Required package not found: {e}")
    print("Please install required packages: pip install kagglehub opencv-python tqdm matplotlib pillow")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(os.path.dirname(__file__), "dataset_manager.log"))
    ]
)
logger = logging.getLogger(__name__)

# Constants
DEFAULT_DATASETS = {
    "radiography": "imtkaggleteam/dental-radiography",
    "cavity": "aqibrehmanpirzada/cavity-and-non-cavity-classification",
    "oral_cancer": "ankushpanday2/oral-cancer-prediction-dataset",
    "thermal": "joebeachcapital/infrared-thermography-temperature",
    "facial": "kpvisionlab/tufts-face-database"
}

DATASET_METADATA = {
    "imtkaggleteam/dental-radiography": {
        "description": "X-ray images with annotations for dental conditions",
        "classes": ["Implant", "Cavity", "Fillings", "Impacted Tooth"],
        "image_format": "jpg",
        "annotation_format": "csv",
        "recommended_preprocessing": ["normalization", "contrast_enhancement"]
    }
}

class DentalDatasetManager:
    """Manager for dental datasets used in Smilo AI training and inference."""
    
    def __init__(self, base_dir: Optional[str] = None, cache_dir: Optional[str] = None):
        """
        Initialize the dataset manager.
        
        Args:
            base_dir: Base directory for storing processed datasets
            cache_dir: Directory for caching downloaded datasets
        """
        # Set up directories
        self.base_dir = Path(base_dir) if base_dir else Path.home() / ".smilo" / "datasets"
        self.cache_dir = Path(cache_dir) if cache_dir else Path.home() / ".cache" / "smilo" / "datasets"
        self.processed_dir = self.base_dir / "processed"
        
        # Create directories if they don't exist
        self.base_dir.mkdir(parents=True, exist_ok=True)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.processed_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize dataset registry
        self.registry_path = self.base_dir / "dataset_registry.json"
        self.registry = self._load_registry()
        
        logger.info(f"Initialized DentalDatasetManager with base directory: {self.base_dir}")
    
    def _load_registry(self) -> Dict:
        """Load the dataset registry from disk or create a new one."""
        if self.registry_path.exists():
            try:
                with open(self.registry_path, 'r') as f:
                    return json.load(f)
            except json.JSONDecodeError:
                logger.warning(f"Error reading registry file. Creating new registry.")
                return {"datasets": {}}
        else:
            return {"datasets": {}}
    
    def _save_registry(self):
        """Save the dataset registry to disk."""
        with open(self.registry_path, 'w') as f:
            json.dump(self.registry, f, indent=2)
    
    def download_dataset(self, dataset_id: str, version: Optional[str] = None, 
                         force_download: bool = False) -> Path:
        """
        Download a dataset from Kaggle and return the path.
        
        Args:
            dataset_id: The Kaggle dataset identifier
            version: Specific version to download, None for latest
            force_download: Whether to force re-download even if cached
            
        Returns:
            Path to the downloaded dataset
        """
        # Resolve dataset alias if provided
        if dataset_id in DEFAULT_DATASETS:
            actual_id = DEFAULT_DATASETS[dataset_id]
            logger.info(f"Resolved dataset alias '{dataset_id}' to '{actual_id}'")
            dataset_id = actual_id
        
        try:
            # Generate a unique identifier for this dataset version
            dataset_name = dataset_id.split("/")[-1]
            version_str = version if version else "latest"
            dataset_dir_name = f"{dataset_name}_{version_str}"
            dataset_cache = self.cache_dir / dataset_dir_name
            
            # Check if dataset is already downloaded
            if dataset_cache.exists() and not force_download:
                logger.info(f"Using cached dataset: {dataset_cache}")
                return dataset_cache
            
            # Download latest or specific version
            logger.info(f"Downloading dataset: {dataset_id}" + (f" (version: {version})" if version else ""))
            path = kagglehub.dataset_download(dataset_id, version=version)
            
            # Update registry
            dataset_info = {
                "id": dataset_id,
                "name": dataset_name,
                "version": version,
                "download_date": datetime.now().isoformat(),
                "path": str(path),
                "processed": False
            }
            
            self.registry["datasets"][dataset_id] = dataset_info
            self._save_registry()
            
            logger.info(f"Dataset downloaded successfully to: {path}")
            return Path(path)
            
        except Exception as e:
            logger.error(f"Error downloading dataset: {e}")
            return None
    
    def preprocess_dataset(self, dataset_id: str, output_dir: Optional[str] = None, 
                          split_ratio: Tuple[float, float, float] = (0.7, 0.15, 0.15),
                          preprocessing_steps: List[str] = None) -> Path:
        """
        Preprocess a downloaded dataset for AI training.
        
        Args:
            dataset_id: The Kaggle dataset identifier or alias
            output_dir: Directory to save processed data, None for default
            split_ratio: Train/validation/test split ratio
            preprocessing_steps: List of preprocessing steps to apply
            
        Returns:
            Path to the processed dataset
        """
        # Resolve dataset alias if provided
        if dataset_id in DEFAULT_DATASETS:
            actual_id = DEFAULT_DATASETS[dataset_id]
            logger.info(f"Resolved dataset alias '{dataset_id}' to '{actual_id}'")
            dataset_id = actual_id
        
        # Check if dataset exists in registry
        if dataset_id not in self.registry["datasets"]:
            logger.warning(f"Dataset {dataset_id} not found in registry. Downloading first.")
            dataset_path = self.download_dataset(dataset_id)
            if not dataset_path:
                return None
        else:
            dataset_path = Path(self.registry["datasets"][dataset_id]["path"])
        
        # Set up output directory
        dataset_name = dataset_id.split("/")[-1]
        if output_dir:
            output_path = Path(output_dir)
        else:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = self.processed_dir / f"{dataset_name}_{timestamp}"
        
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Determine preprocessing steps
        if preprocessing_steps is None:
            if dataset_id in DATASET_METADATA:
                preprocessing_steps = DATASET_METADATA[dataset_id].get("recommended_preprocessing", [])
            else:
                preprocessing_steps = ["normalization"]
        
        logger.info(f"Preprocessing dataset {dataset_id} with steps: {preprocessing_steps}")
        
        # Process based on dataset type
        if dataset_id == "imtkaggleteam/dental-radiography":
            self._preprocess_dental_radiography(dataset_path, output_path, split_ratio, preprocessing_steps)
        else:
            logger.warning(f"No specific preprocessing pipeline for {dataset_id}. Using generic preprocessing.")
            self._preprocess_generic(dataset_path, output_path, split_ratio, preprocessing_steps)
        
        # Update registry
        self.registry["datasets"][dataset_id]["processed"] = True
        self.registry["datasets"][dataset_id]["processed_path"] = str(output_path)
        self.registry["datasets"][dataset_id]["preprocessing_steps"] = preprocessing_steps
        self.registry["datasets"][dataset_id]["split_ratio"] = list(split_ratio)
        self._save_registry()
        
        logger.info(f"Dataset preprocessing complete. Processed data saved to: {output_path}")
        return output_path
    
    def _preprocess_dental_radiography(self, dataset_path: Path, output_path: Path, 
                                      split_ratio: Tuple[float, float, float],
                                      preprocessing_steps: List[str]):
        """
        Preprocess the dental radiography dataset specifically.
        
        Args:
            dataset_path: Path to the downloaded dataset
            output_path: Path to save processed data
            split_ratio: Train/validation/test split ratio
            preprocessing_steps: List of preprocessing steps to apply
        """
        # Create output directories
        train_dir = output_path / "train"
        val_dir = output_path / "val"
        test_dir = output_path / "test"
        
        for directory in [train_dir, val_dir, test_dir]:
            directory.mkdir(exist_ok=True)
            (directory / "images").mkdir(exist_ok=True)
            (directory / "annotations").mkdir(exist_ok=True)
        
        # Find all images and annotations
        images = []
        annotations = []
        
        for root, _, files in os.walk(dataset_path):
            for file in files:
                if file.lower().endswith(('.jpg', '.jpeg', '.png')):
                    images.append(os.path.join(root, file))
                elif file.lower().endswith('.csv') and 'annotation' in file.lower():
                    annotations.append(os.path.join(root, file))
        
        logger.info(f"Found {len(images)} images and {len(annotations)} annotation files")
        
        # Create a mapping from image filename to annotation filename
        image_to_annotation = {}
        for annotation_path in annotations:
            annotation_filename = os.path.basename(annotation_path)
            image_filename = annotation_filename.replace('_annotations.csv', '.jpg')
            image_to_annotation[image_filename] = annotation_path
        
        # Shuffle and split the dataset
        np.random.seed(42)  # For reproducibility
        np.random.shuffle(images)
        
        n_train = int(len(images) * split_ratio[0])
        n_val = int(len(images) * split_ratio[1])
        
        train_images = images[:n_train]
        val_images = images[n_train:n_train + n_val]
        test_images = images[n_train + n_val:]
        
        # Process and copy the data
        for subset_name, subset_images, subset_dir in [
            ("train", train_images, train_dir),
            ("validation", val_images, val_dir),
            ("test", test_images, test_dir)
        ]:
            logger.info(f"Processing {len(subset_images)} images for {subset_name} set")
            
            for img_path in tqdm(subset_images, desc=f"Processing {subset_name} set"):
                img_filename = os.path.basename(img_path)
                
                # Read and preprocess the image
                img = cv2.imread(img_path)
                if img is None:
                    logger.warning(f"Could not read image: {img_path}")
                    continue
                
                # Apply preprocessing steps
                img = self._apply_preprocessing(img, preprocessing_steps)
                
                # Save the processed image
                output_img_path = subset_dir / "images" / img_filename
                cv2.imwrite(str(output_img_path), img)
                
                # Copy the annotation if available
                if img_filename in image_to_annotation:
                    annotation_path = image_to_annotation[img_filename]
                    annotation_filename = os.path.basename(annotation_path)
                    output_annotation_path = subset_dir / "annotations" / annotation_filename
                    shutil.copy2(annotation_path, output_annotation_path)
        
        # Create dataset info file
        dataset_info = {
            "name": "dental_radiography",
            "description": "Preprocessed dental radiography dataset",
            "preprocessing_steps": preprocessing_steps,
            "split_ratio": list(split_ratio),
            "num_train_images": len(train_images),
            "num_val_images": len(val_images),
            "num_test_images": len(test_images),
            "classes": DATASET_METADATA.get("imtkaggleteam/dental-radiography", {}).get("classes", []),
            "created_at": datetime.now().isoformat()
        }
        
        with open(output_path / "dataset_info.json", 'w') as f:
            json.dump(dataset_info, f, indent=2)
        
        # Create a visualization of sample images
        self._create_dataset_visualization(train_images[:9], output_path / "sample_images.png", preprocessing_steps)
    
    def _preprocess_generic(self, dataset_path: Path, output_path: Path, 
                           split_ratio: Tuple[float, float, float],
                           preprocessing_steps: List[str]):
        """
        Generic preprocessing for datasets without specific pipelines.
        
        Args:
            dataset_path: Path to the downloaded dataset
            output_path: Path to save processed data
            split_ratio: Train/validation/test split ratio
            preprocessing_steps: List of preprocessing steps to apply
        """
        # Create output directories
        train_dir = output_path / "train"
        val_dir = output_path / "val"
        test_dir = output_path / "test"
        
        for directory in [train_dir, val_dir, test_dir]:
            directory.mkdir(exist_ok=True)
        
        # Find all images
        images = []
        for root, _, files in os.walk(dataset_path):
            for file in files:
                if file.lower().endswith(('.jpg', '.jpeg', '.png')):
                    images.append(os.path.join(root, file))
        
        logger.info(f"Found {len(images)} images")
        
        # Shuffle and split the dataset
        np.random.seed(42)  # For reproducibility
        np.random.shuffle(images)
        
        n_train = int(len(images) * split_ratio[0])
        n_val = int(len(images) * split_ratio[1])
        
        train_images = images[:n_train]
        val_images = images[n_train:n_train + n_val]
        test_images = images[n_train + n_val:]
        
        # Process and copy the data
        for subset_name, subset_images, subset_dir in [
            ("train", train_images, train_dir),
            ("validation", val_images, val_dir),
            ("test", test_images, test_dir)
        ]:
            logger.info(f"Processing {len(subset_images)} images for {subset_name} set")
            
            for img_path in tqdm(subset_images, desc=f"Processing {subset_name} set"):
                img_filename = os.path.basename(img_path)
                
                # Read and preprocess the image
                img = cv2.imread(img_path)
                if img is None:
                    logger.warning(f"Could not read image: {img_path}")
                    continue
                
                # Apply preprocessing steps
                img = self._apply_preprocessing(img, preprocessing_steps)
                
                # Save the processed image
                output_img_path = subset_dir / img_filename
                cv2.imwrite(str(output_img_path), img)
        
        # Create dataset info file
        dataset_info = {
            "description": "Preprocessed generic dataset",
            "preprocessing_steps": preprocessing_steps,
            "split_ratio": list(split_ratio),
            "num_train_images": len(train_images),
            "num_val_images": len(val_images),
            "num_test_images": len(test_images),
            "created_at": datetime.now().isoformat()
        }
        
        with open(output_path / "dataset_info.json", 'w') as f:
            json.dump(dataset_info, f, indent=2)
        
        # Create a visualization of sample images
        self._create_dataset_visualization(train_images[:9], output_path / "sample_images.png", preprocessing_steps)
    
    def _apply_preprocessing(self, image: np.ndarray, preprocessing_steps: List[str]) -> np.ndarray:
        """
        Apply preprocessing steps to an image.
        
        Args:
            image: Input image as numpy array
            preprocessing_steps: List of preprocessing steps to apply
            
        Returns:
            Preprocessed image
        """
        processed_img = image.copy()
        
        for step in preprocessing_steps:
            if step == "normalization":
                # Normalize to 0-1 range
                processed_img = processed_img.astype(np.float32) / 255.0
                processed_img = (processed_img * 255).astype(np.uint8)
                
            elif step == "contrast_enhancement":
                # Apply CLAHE (Contrast Limited Adaptive Histogram Equalization)
                if len(processed_img.shape) == 3:
                    # Convert to grayscale for dental X-rays
                    gray = cv2.cvtColor(processed_img, cv2.COLOR_BGR2GRAY)
                else:
                    gray = processed_img
                
                clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
                enhanced = clahe.apply(gray)
                
                if len(processed_img.shape) == 3:
                    # Convert back to BGR
                    processed_img = cv2.cvtColor(enhanced, cv2.COLOR_GRAY2BGR)
                else:
                    processed_img = enhanced
                
            elif step == "resize":
                # Resize to standard dimensions (512x512 for dental images)
                processed_img = cv2.resize(processed_img, (512, 512), interpolation=cv2.INTER_AREA)
                
            elif step == "denoise":
                # Apply denoising
                processed_img = cv2.fastNlMeansDenoisingColored(processed_img, None, 10, 10, 7, 21)
                
            elif step == "edge_enhancement":
                # Enhance edges for better feature detection
                kernel = np.array([[-1, -1, -1], [-1, 9, -1], [-1, -1, -1]])
                processed_img = cv2.filter2D(processed_img, -1, kernel)
        
        return processed_img
    
    def _create_dataset_visualization(self, image_paths: List[str], output_path: Path, 
                                     preprocessing_steps: List[str]):
        """
        Create a visualization of sample images from the dataset.
        
        Args:
            image_paths: List of paths to sample images
            output_path: Path to save the visualization
            preprocessing_steps: List of preprocessing steps that were applied
        """
        n_images = min(9, len(image_paths))
        fig, axes = plt.subplots(3, 3, figsize=(12, 12))
        axes = axes.flatten()
        
        for i, img_path in enumerate(image_paths[:n_images]):
            # Read original image
            original = cv2.imread(img_path)
            original = cv2.cvtColor(original, cv2.COLOR_BGR2RGB)
            
            # Apply preprocessing
            processed = self._apply_preprocessing(original, preprocessing_steps)
            processed = cv2.cvtColor(processed, cv2.COLOR_BGR2RGB)
            
            # Display side by side
            combined = np.hstack((original, processed))
            axes[i].imshow(combined)
            axes[i].set_title(f"Sample {i+1}")
            axes[i].axis('off')
        
        # Hide any unused subplots
        for i in range(n_images, len(axes)):
            axes[i].axis('off')
        
        plt.suptitle(f"Sample Images (Original | Processed)\nPreprocessing: {', '.join(preprocessing_steps)}")
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close(fig)
    
    def get_dataset_info(self, dataset_id: str) -> Dict:
        """
        Get information about a dataset.
        
        Args:
            dataset_id: The Kaggle dataset identifier or alias
            
        Returns:
            Dictionary with dataset information
        """
        # Resolve dataset alias if provided
        if dataset_id in DEFAULT_DATASETS:
            actual_id = DEFAULT_DATASETS[dataset_id]
            dataset_id = actual_id
        
        if dataset_id in self.registry["datasets"]:
            return self.registry["datasets"][dataset_id]
        else:
            logger.warning(f"Dataset {dataset_id} not found in registry")
            return None
    
    def list_datasets(self) -> List[Dict]:
        """
        List all datasets in the registry.
        
        Returns:
            List of dataset information dictionaries
        """
        return list(self.registry["datasets"].values())
    
    def get_dataset_path(self, dataset_id: str, processed: bool = True) -> Optional[Path]:
        """
        Get the path to a dataset.
        
        Args:
            dataset_id: The Kaggle dataset identifier or alias
            processed: Whether to return the processed dataset path (if available)
            
        Returns:
            Path to the dataset
        """
        # Resolve dataset alias if provided
        if dataset_id in DEFAULT_DATASETS:
            actual_id = DEFAULT_DATASETS[dataset_id]
            dataset_id = actual_id
        
        if dataset_id not in self.registry["datasets"]:
            logger.warning(f"Dataset {dataset_id} not found in registry")
            return None
        
        dataset_info = self.registry["datasets"][dataset_id]
        
        if processed and dataset_info.get("processed", False):
            return Path(dataset_info["processed_path"])
        else:
            return Path(dataset_info["path"])
    
    def export_dataset_metadata(self, dataset_id: str, output_path: Optional[Path] = None) -> Path:
        """
        Export metadata about a dataset to a JSON file.
        
        Args:
            dataset_id: The Kaggle dataset identifier or alias
            output_path: Path to save the metadata, None for default
            
        Returns:
            Path to the metadata file
        """
        # Resolve dataset alias if provided
        if dataset_id in DEFAULT_DATASETS:
            actual_id = DEFAULT_DATASETS[dataset_id]
            dataset_id = actual_id
        
        if dataset_id not in self.registry["datasets"]:
            logger.warning(f"Dataset {dataset_id} not found in registry")
            return None
        
        dataset_info = self.registry["datasets"][dataset_id]
        
        # Add additional metadata if available
        if dataset_id in DATASET_METADATA:
            dataset_info.update(DATASET_METADATA[dataset_id])
        
        # Set output path
        if output_path is None:
            output_path = self.base_dir / f"{dataset_id.split('/')[-1]}_metadata.json"
        
        # Save metadata
        with open(output_path, 'w') as f:
            json.dump(dataset_info, f, indent=2)
        
        logger.info(f"Dataset metadata exported to: {output_path}")
        return output_path


def main():
    """Main function to run the dental dataset manager."""
    parser = argparse.ArgumentParser(description="Dental Dataset Manager for Smilo AI")
    
    # Dataset selection
    parser.add_argument("--dataset", default="radiography", 
                        help="Dataset ID or alias (default: radiography)")
    
    # Actions
    parser.add_argument("--download", action="store_true", 
                        help="Download the dataset")
    parser.add_argument("--preprocess", action="store_true", 
                        help="Preprocess the dataset")
    parser.add_argument("--info", action="store_true", 
                        help="Show dataset information")
    parser.add_argument("--list", action="store_true", 
                        help="List all datasets")
    
    # Options
    parser.add_argument("--force", action="store_true", 
                        help="Force re-download even if cached")
    parser.add_argument("--output-dir", 
                        help="Directory to save processed data")
    parser.add_argument("--preprocessing-steps", nargs="+", 
                        choices=["normalization", "contrast_enhancement", "resize", "denoise", "edge_enhancement"],
                        help="Preprocessing steps to apply")
    parser.add_argument("--split-ratio", nargs=3, type=float, default=[0.7, 0.15, 0.15],
                        help="Train/validation/test split ratio (default: 0.7 0.15 0.15)")
    
    args = parser.parse_args()
    
    # Initialize dataset manager
    manager = DentalDatasetManager()
    
    # Execute requested action
    if args.list:
        datasets = manager.list_datasets()
        if datasets:
            print("\nAvailable Datasets:")
            for dataset in datasets:
                print(f"- {dataset['id']} (Downloaded: {dataset['download_date']})")
                print(f"  Path: {dataset['path']}")
                if dataset.get('processed', False):
                    print(f"  Processed: Yes - {dataset['processed_path']}")
                else:
                    print(f"  Processed: No")
                print()
        else:
            print("No datasets in registry. Use --download to download a dataset.")
    
    elif args.info:
        info = manager.get_dataset_info(args.dataset)
        if info:
            print(f"\nDataset Information for {args.dataset}:")
            for key, value in info.items():
                print(f"- {key}: {value}")
        else:
            print(f"No information available for dataset: {args.dataset}")
    
    elif args.download:
        path = manager.download_dataset(args.dataset, force_download=args.force)
        if path:
            print(f"\nDataset downloaded successfully to: {path}")
        else:
            print(f"Failed to download dataset: {args.dataset}")
    
    elif args.preprocess:
        # Download first if needed
        if args.dataset not in manager.registry["datasets"]:
            print(f"Dataset {args.dataset} not found in registry. Downloading first...")
            path = manager.download_dataset(args.dataset)
            if not path:
                print(f"Failed to download dataset: {args.dataset}")
                return
        
        # Preprocess the dataset
        path = manager.preprocess_dataset(
            args.dataset,
            output_dir=args.output_dir,
            split_ratio=tuple(args.split_ratio),
            preprocessing_steps=args.preprocessing_steps
        )
        
        if path:
            print(f"\nDataset preprocessed successfully. Results saved to: {path}")
        else:
            print(f"Failed to preprocess dataset: {args.dataset}")
    
    else:
        # If no action specified, show help
        parser.print_help()


if __name__ == "__main__":
    main()
