#!/usr/bin/env python3
"""
Dental Radiography Mobile Inference Script for Smilo

This script provides a lightweight inference solution for mobile deployment.
It supports multiple model formats (TFLite, ONNX, CoreML, TorchScript) and
is optimized for mobile devices.
"""

import os
import sys
import argparse
import logging
from pathlib import Path
import json
import time
import numpy as np
from typing import Dict, List, Optional, Tuple, Union
import cv2
from PIL import Image, ImageDraw, ImageFont

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Default class names for dental radiography
DEFAULT_CLASSES = ["Implant", "<PERSON>avity", "Fillings", "Impacted Tooth"]

class MobileInferenceEngine:
    """Lightweight inference engine for mobile deployment."""
    
    def __init__(self, model_path: str, model_format: str = None, 
                class_names: List[str] = None, device: str = "cpu"):
        """
        Initialize the inference engine.
        
        Args:
            model_path: Path to the model file
            model_format: Format of the model (tflite, onnx, coreml, torchscript)
            class_names: List of class names
            device: Device to run inference on (cpu, gpu)
        """
        self.model_path = Path(model_path)
        self.class_names = class_names or DEFAULT_CLASSES
        self.device = device.lower()
        
        # Determine model format if not provided
        if model_format is None:
            extension = self.model_path.suffix.lower()
            if extension == '.tflite':
                model_format = 'tflite'
            elif extension == '.onnx':
                model_format = 'onnx'
            elif extension == '.mlmodel':
                model_format = 'coreml'
            elif extension == '.pt':
                model_format = 'torchscript'
            else:
                raise ValueError(f"Could not determine model format from extension: {extension}")
        
        self.model_format = model_format.lower()
        self.model = self._load_model()
        logger.info(f"Initialized {self.model_format} inference engine")
    
    def _load_model(self):
        """
        Load the model based on its format.
        
        Returns:
            Loaded model
        """
        if self.model_format == 'tflite':
            try:
                import tensorflow as tf
                # Load TFLite model
                interpreter = tf.lite.Interpreter(model_path=str(self.model_path))
                interpreter.allocate_tensors()
                return interpreter
            except ImportError:
                raise ImportError("TensorFlow not installed. Install with: pip install tensorflow")
        
        elif self.model_format == 'onnx':
            try:
                import onnxruntime as ort
                # Set up ONNX runtime session
                providers = ['CPUExecutionProvider']
                if self.device == 'gpu':
                    providers.insert(0, 'CUDAExecutionProvider')
                session = ort.InferenceSession(str(self.model_path), providers=providers)
                return session
            except ImportError:
                raise ImportError("ONNX Runtime not installed. Install with: pip install onnxruntime")
        
        elif self.model_format == 'coreml':
            try:
                import coremltools as ct
                # Load CoreML model
                model = ct.models.MLModel(str(self.model_path))
                return model
            except ImportError:
                raise ImportError("CoreML Tools not installed. Install with: pip install coremltools")
        
        elif self.model_format == 'torchscript':
            try:
                import torch
                # Load TorchScript model
                model = torch.jit.load(str(self.model_path))
                model.eval()
                if self.device == 'gpu' and torch.cuda.is_available():
                    model = model.to('cuda')
                return model
            except ImportError:
                raise ImportError("PyTorch not installed. Install with: pip install torch")
        
        else:
            raise ValueError(f"Unsupported model format: {self.model_format}")
    
    def preprocess_image(self, image_path: str) -> Tuple[np.ndarray, np.ndarray]:
        """
        Preprocess an image for inference.
        
        Args:
            image_path: Path to the image
            
        Returns:
            Tuple of (preprocessed image tensor, original image)
        """
        # Read image
        original_image = cv2.imread(str(image_path))
        if original_image is None:
            raise ValueError(f"Could not read image: {image_path}")
        
        original_image = cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB)
        
        # Get image dimensions
        height, width = original_image.shape[:2]
        
        # Resize to 224x224 (standard mobile input size)
        input_image = cv2.resize(original_image, (224, 224), interpolation=cv2.INTER_AREA)
        
        # Normalize pixel values
        input_tensor = input_image.astype(np.float32) / 255.0
        
        # Format based on model type
        if self.model_format == 'tflite':
            # TFLite expects [1, height, width, channels]
            input_tensor = np.expand_dims(input_tensor, axis=0)
        
        elif self.model_format == 'onnx':
            # ONNX expects [1, channels, height, width]
            input_tensor = input_tensor.transpose(2, 0, 1)  # HWC to CHW
            input_tensor = np.expand_dims(input_tensor, axis=0)
        
        elif self.model_format == 'coreml':
            # CoreML typically expects [1, height, width, channels] or PIL image
            input_tensor = np.expand_dims(input_tensor, axis=0)
        
        elif self.model_format == 'torchscript':
            # PyTorch expects [1, channels, height, width]
            input_tensor = input_tensor.transpose(2, 0, 1)  # HWC to CHW
            input_tensor = np.expand_dims(input_tensor, axis=0)
            
            try:
                import torch
                input_tensor = torch.from_numpy(input_tensor)
                if self.device == 'gpu' and torch.cuda.is_available():
                    input_tensor = input_tensor.to('cuda')
            except ImportError:
                raise ImportError("PyTorch not installed. Install with: pip install torch")
        
        return input_tensor, original_image
    
    def run_inference(self, input_tensor: np.ndarray, confidence_threshold: float = 0.5) -> Dict:
        """
        Run inference on an image.
        
        Args:
            input_tensor: Preprocessed image tensor
            confidence_threshold: Confidence threshold for predictions
            
        Returns:
            Dictionary with prediction results
        """
        if self.model_format == 'tflite':
            # Get input and output details
            interpreter = self.model
            input_details = interpreter.get_input_details()
            output_details = interpreter.get_output_details()
            
            # Set input tensor
            interpreter.set_tensor(input_details[0]['index'], input_tensor)
            
            # Run inference
            interpreter.invoke()
            
            # Get output tensors
            # Note: This assumes a specific output format - adjust based on your model
            boxes = interpreter.get_tensor(output_details[0]['index'])
            classes = interpreter.get_tensor(output_details[1]['index'])
            scores = interpreter.get_tensor(output_details[2]['index'])
            
            # Filter by confidence
            mask = scores > confidence_threshold
            filtered_boxes = boxes[mask]
            filtered_classes = classes[mask]
            filtered_scores = scores[mask]
            
            return {
                'boxes': filtered_boxes,
                'labels': filtered_classes,
                'scores': filtered_scores
            }
        
        elif self.model_format == 'onnx':
            # Get input and output names
            session = self.model
            input_name = session.get_inputs()[0].name
            
            # Run inference
            outputs = session.run(None, {input_name: input_tensor})
            
            # Process outputs
            # Note: This assumes a specific output format - adjust based on your model
            boxes = outputs[0]
            scores = outputs[1]
            classes = outputs[2]
            
            # Filter by confidence
            mask = scores > confidence_threshold
            filtered_boxes = boxes[mask]
            filtered_classes = classes[mask]
            filtered_scores = scores[mask]
            
            return {
                'boxes': filtered_boxes,
                'labels': filtered_classes,
                'scores': filtered_scores
            }
        
        elif self.model_format == 'coreml':
            # Run inference
            model = self.model
            
            # Convert to PIL Image for CoreML
            if isinstance(input_tensor, np.ndarray):
                # If it's a numpy array, convert to PIL
                if input_tensor.shape[0] == 1:  # If batch dimension
                    pil_image = Image.fromarray((input_tensor[0] * 255).astype(np.uint8))
                else:
                    pil_image = Image.fromarray((input_tensor * 255).astype(np.uint8))
                
                # Run prediction
                outputs = model.predict({'input': pil_image})
            else:
                # Assume it's already in the right format
                outputs = model.predict({'input': input_tensor})
            
            # Process outputs
            # Note: This assumes a specific output format - adjust based on your model
            boxes = outputs['boxes']
            scores = outputs['scores']
            classes = outputs['classes']
            
            # Filter by confidence
            mask = scores > confidence_threshold
            filtered_boxes = boxes[mask]
            filtered_classes = classes[mask]
            filtered_scores = scores[mask]
            
            return {
                'boxes': filtered_boxes,
                'labels': filtered_classes,
                'scores': filtered_scores
            }
        
        elif self.model_format == 'torchscript':
            try:
                import torch
                
                # Run inference
                model = self.model
                with torch.no_grad():
                    outputs = model(input_tensor)
                
                # Process outputs
                # Note: This assumes a specific output format - adjust based on your model
                if isinstance(outputs, dict):
                    boxes = outputs['boxes'].cpu().numpy()
                    scores = outputs['scores'].cpu().numpy()
                    classes = outputs['labels'].cpu().numpy()
                elif isinstance(outputs, list):
                    boxes = outputs[0]['boxes'].cpu().numpy()
                    scores = outputs[0]['scores'].cpu().numpy()
                    classes = outputs[0]['labels'].cpu().numpy()
                else:
                    raise ValueError("Unexpected output format from TorchScript model")
                
                # Filter by confidence
                mask = scores > confidence_threshold
                filtered_boxes = boxes[mask]
                filtered_classes = classes[mask]
                filtered_scores = scores[mask]
                
                return {
                    'boxes': filtered_boxes,
                    'labels': filtered_classes,
                    'scores': filtered_scores
                }
                
            except ImportError:
                raise ImportError("PyTorch not installed. Install with: pip install torch")
        
        else:
            raise ValueError(f"Unsupported model format: {self.model_format}")
    
    def visualize_prediction(self, image: np.ndarray, prediction: Dict, 
                           output_path: Optional[str] = None) -> np.ndarray:
        """
        Visualize prediction on an image.
        
        Args:
            image: Original image
            prediction: Prediction results
            output_path: Path to save the visualization
            
        Returns:
            Visualization image
        """
        # Convert to PIL Image for drawing
        image_pil = Image.fromarray(image)
        draw = ImageDraw.Draw(image_pil)
        
        # Define colors for different classes
        colors = [
            (255, 0, 0),    # Red
            (0, 255, 0),    # Green
            (0, 0, 255),    # Blue
            (255, 255, 0),  # Yellow
            (255, 0, 255),  # Magenta
            (0, 255, 255),  # Cyan
        ]
        
        # Get image dimensions
        width, height = image_pil.size
        
        # Draw bounding boxes and labels
        boxes = prediction['boxes']
        labels = prediction['labels']
        scores = prediction['scores']
        
        for i in range(len(boxes)):
            # Get box coordinates
            box = boxes[i]
            
            # Scale box coordinates if needed
            if box[0] <= 1.0 and box[1] <= 1.0 and box[2] <= 1.0 and box[3] <= 1.0:
                # Normalized coordinates (0-1)
                x1, y1, x2, y2 = box[0] * width, box[1] * height, box[2] * width, box[3] * height
            else:
                # Absolute coordinates
                x1, y1, x2, y2 = box[0], box[1], box[2], box[3]
            
            # Get class index and name
            class_idx = int(labels[i])
            class_name = self.class_names[class_idx - 1] if 0 < class_idx <= len(self.class_names) else f"Class {class_idx}"
            
            # Get score
            score = float(scores[i])
            
            # Get color
            color = colors[class_idx % len(colors)]
            
            # Draw rectangle
            draw.rectangle([x1, y1, x2, y2], outline=color, width=3)
            
            # Draw label
            label_text = f"{class_name} ({score:.2f})"
            draw.text((x1, y1 - 10), label_text, fill=color)
        
        # Convert back to numpy array
        result_image = np.array(image_pil)
        
        # Save if output path is provided
        if output_path:
            image_pil.save(output_path)
        
        return result_image
    
    def generate_report(self, prediction: Dict, image_path: str, 
                       output_path: Optional[str] = None) -> Dict:
        """
        Generate a report from the prediction results.
        
        Args:
            prediction: Prediction results
            image_path: Path to the original image
            output_path: Path to save the report
            
        Returns:
            Report as a dictionary
        """
        # Count detections by class
        class_counts = {}
        for label in prediction['labels']:
            class_idx = int(label)
            class_name = self.class_names[class_idx - 1] if 0 < class_idx <= len(self.class_names) else f"Class {class_idx}"
            class_counts[class_name] = class_counts.get(class_name, 0) + 1
        
        # Create report
        report = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "image_path": str(image_path),
            "model_path": str(self.model_path),
            "model_format": self.model_format,
            "num_detections": len(prediction['labels']),
            "class_counts": class_counts,
            "detections": []
        }
        
        # Add details for each detection
        boxes = prediction['boxes']
        labels = prediction['labels']
        scores = prediction['scores']
        
        for i in range(len(boxes)):
            class_idx = int(labels[i])
            class_name = self.class_names[class_idx - 1] if 0 < class_idx <= len(self.class_names) else f"Class {class_idx}"
            
            detection = {
                "class": class_name,
                "confidence": float(scores[i]),
                "bounding_box": boxes[i].tolist() if isinstance(boxes[i], np.ndarray) else boxes[i]
            }
            
            report["detections"].append(detection)
        
        # Save if output path is provided
        if output_path:
            with open(output_path, 'w') as f:
                json.dump(report, f, indent=2)
        
        return report


def main():
    """Main function to run dental radiography mobile inference."""
    parser = argparse.ArgumentParser(description="Dental Radiography Mobile Inference")
    
    # Input options
    parser.add_argument("--model", required=True, 
                        help="Path to the model file")
    parser.add_argument("--format", choices=['tflite', 'onnx', 'coreml', 'torchscript'],
                        help="Model format (if not specified, will be inferred from file extension)")
    parser.add_argument("--image", required=True, 
                        help="Path to the input image")
    parser.add_argument("--classes", nargs='+', default=DEFAULT_CLASSES,
                        help="List of class names")
    
    # Inference options
    parser.add_argument("--confidence", type=float, default=0.5, 
                        help="Confidence threshold for predictions")
    parser.add_argument("--device", default="cpu", choices=['cpu', 'gpu'],
                        help="Device to run inference on (cpu or gpu)")
    
    # Output options
    parser.add_argument("--output-dir", default="./dental_mobile_inference", 
                        help="Directory to save output")
    parser.add_argument("--no-visualization", action="store_true",
                        help="Disable visualization output")
    
    args = parser.parse_args()
    
    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    try:
        # Initialize inference engine
        logger.info(f"Initializing inference engine for model: {args.model}")
        engine = MobileInferenceEngine(
            model_path=args.model,
            model_format=args.format,
            class_names=args.classes,
            device=args.device
        )
        
        # Preprocess image
        logger.info(f"Processing image: {args.image}")
        input_tensor, original_image = engine.preprocess_image(args.image)
        
        # Run inference
        logger.info("Running inference...")
        start_time = time.time()
        prediction = engine.run_inference(input_tensor, confidence_threshold=args.confidence)
        inference_time = time.time() - start_time
        logger.info(f"Inference completed in {inference_time:.3f} seconds")
        
        # Generate visualization
        if not args.no_visualization:
            logger.info("Generating visualization...")
            output_image_path = output_dir / "prediction.png"
            visualization = engine.visualize_prediction(
                original_image, 
                prediction, 
                output_path=str(output_image_path)
            )
        
        # Generate report
        logger.info("Generating report...")
        output_report_path = output_dir / "report.json"
        report = engine.generate_report(
            prediction, 
            args.image, 
            output_path=str(output_report_path)
        )
        
        # Add inference time to report
        report["inference_time"] = inference_time
        with open(output_report_path, 'w') as f:
            json.dump(report, f, indent=2)
        
        # Print summary
        print("\nDental Radiography Analysis Results:")
        print(f"Total detections: {report['num_detections']}")
        print(f"Inference time: {inference_time:.3f} seconds")
        print("\nDetections by class:")
        for class_name, count in report['class_counts'].items():
            print(f"- {class_name}: {count}")
        
        if not args.no_visualization:
            print(f"\nVisualization saved to: {output_image_path}")
        print(f"Report saved to: {output_report_path}")
        
    except Exception as e:
        logger.error(f"Error during inference: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
