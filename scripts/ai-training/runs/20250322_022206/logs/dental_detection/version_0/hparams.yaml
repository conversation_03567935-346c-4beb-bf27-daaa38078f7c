backbone_name: resnet50
class_weights: !!python/object/apply:torch._utils._rebuild_tensor_v2
- !!python/object/apply:torch.storage._load_from_bytes
  - !!binary |
    gAKKCmz8nEb5IGqoUBkugAJN6QMugAJ9cQAoWBAAAABwcm90b2NvbF92ZXJzaW9ucQFN6QNYDQAA
    AGxpdHRsZV9lbmRpYW5xAohYCgAAAHR5cGVfc2l6ZXNxA31xBChYBQAAAHNob3J0cQVLAlgDAAAA
    aW50cQZLBFgEAAAAbG9uZ3EHSwR1dS6AAihYBwAAAHN0b3JhZ2VxAGN0b3JjaApGbG9hdFN0b3Jh
    Z2UKcQFYCgAAADQ0Mjg2OTIwNjRxAlgDAAAAY3B1cQNLBE50cQRRLoACXXEAWAoAAAA0NDI4Njky
    MDY0cQFhLgQAAAAAAAAALwmQPzkOX0DtE8Q+7ReWQA==
- 0
- !!python/tuple
  - 4
- !!python/tuple
  - 1
- false
- !!python/object/apply:collections.OrderedDict
  - []
feature_extract: false
learning_rate: 0.0003
momentum: 0.9
num_classes: 5
pretrained: true
use_swa: true
weight_decay: 1.0e-05
