#!/bin/bash

# Make the training script executable
chmod +x train_cavity_model.sh

# Show menu
show_menu() {
    clear
    echo "================================================"
    echo "        Smilo Dental AI System Launcher         "
    echo "================================================"
    echo "1. Train Cavity Classification Model"
    echo "2. Exit"
    echo "================================================"
    echo ""
    read -p "Enter your choice [1-2]: " choice
}

# Process choice
process_choice() {
    case $choice in
        1) ./train_cavity_model.sh ;;
        2) echo "Exiting..."; exit 0 ;;
        *) echo "Invalid option. Please try again."
           read -p "Press Enter to continue..." dummy ;;
    esac
}

# Main loop
while true; do
    show_menu
    process_choice
done 