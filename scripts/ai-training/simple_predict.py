#!/usr/bin/env python3
"""
Simple inference script for dental cavity classification.
This script loads a trained cavity classifier model and makes predictions on new images.
"""

import os
import torch
import torchvision
import torchvision.transforms as transforms
from PIL import Image
import matplotlib.pyplot as plt
import argparse
import json
from pathlib import Path
import numpy as np

def parse_args():
    parser = argparse.ArgumentParser(description="Dental cavity prediction")
    parser.add_argument("--model_path", type=str, required=True, 
                      help="Path to trained model file (cavity_classifier_model.pt)")
    parser.add_argument("--input", type=str, required=True,
                      help="Path to input image or directory of images")
    parser.add_argument("--output_dir", type=str, default="./predictions",
                      help="Directory to save results")
    parser.add_argument("--show", action="store_true", default=False,
                      help="Show results instead of saving them")
    parser.add_argument("--model_info", type=str, default=None,
                      help="Path to model_info.json (optional)")
    return parser.parse_args()

def load_model(model_path, model_info_path=None):
    """Load the trained model"""
    # Load model info if provided
    if model_info_path and os.path.exists(model_info_path):
        with open(model_info_path, 'r') as f:
            model_info = json.load(f)
        model_type = model_info.get('model_type', 'resnet18')
        num_classes = model_info.get('num_classes', 2)
        class_names = model_info.get('class_names', ['cavity', 'no_cavity'])
    else:
        # Default values
        model_type = 'resnet18'
        num_classes = 2
        class_names = ['cavity', 'no_cavity']
    
    # Initialize model architecture
    if model_type == 'resnet18':
        model = torchvision.models.resnet18(weights=None)
        model.fc = torch.nn.Linear(model.fc.in_features, num_classes)
    elif model_type == 'mobilenet':
        model = torchvision.models.mobilenet_v2(weights=None)
        model.classifier[1] = torch.nn.Linear(model.classifier[1].in_features, num_classes)
    elif model_type == 'efficientnet':
        model = torchvision.models.efficientnet_b0(weights=None)
        model.classifier[1] = torch.nn.Linear(model.classifier[1].in_features, num_classes)
    
    # Load the state dict
    model.load_state_dict(torch.load(model_path, map_location=torch.device('cpu')))
    model.eval()
    
    return model, class_names

def process_image(image_path, transform):
    """Process an image for model input"""
    img = Image.open(image_path).convert('RGB')
    img_tensor = transform(img)
    return img, img_tensor

def predict(model, img_tensor, class_names, device):
    """Make a prediction on the input image"""
    with torch.no_grad():
        img_tensor = img_tensor.unsqueeze(0).to(device)
        outputs = model(img_tensor)
        _, preds = torch.max(outputs, 1)
        probabilities = torch.nn.functional.softmax(outputs, dim=1)
        confidence = probabilities[0][preds[0]].item()
        
    prediction = {
        'class_id': preds[0].item(),
        'class_name': class_names[preds[0]],
        'confidence': confidence
    }
    
    return prediction

def visualize_prediction(img, prediction, output_path=None, show=False):
    """Visualize the prediction"""
    # Create figure
    plt.figure(figsize=(8, 8))
    plt.imshow(img)
    
    # Add prediction text
    title = f"Prediction: {prediction['class_name']} ({prediction['confidence']:.2%})"
    color = 'green' if prediction['class_name'] == 'no_cavity' else 'red'
    
    plt.title(title, color=color, fontsize=16)
    plt.axis('off')
    
    # Save or show
    if output_path:
        plt.savefig(output_path, bbox_inches='tight')
        plt.close()
        print(f"Prediction saved to {output_path}")
    
    if show:
        plt.show()
    else:
        plt.close()

def main():
    args = parse_args()
    
    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Set device
    device = torch.device("cuda" if torch.cuda.is_available() else 
                         "mps" if torch.backends.mps.is_available() else 
                         "cpu")
    print(f"Using device: {device}")
    
    # Load model
    model_info_path = args.model_info
    if not model_info_path and os.path.exists(os.path.dirname(args.model_path) + '/model_info.json'):
        model_info_path = os.path.dirname(args.model_path) + '/model_info.json'
    
    print(f"Loading model from {args.model_path}")
    model, class_names = load_model(args.model_path, model_info_path)
    model = model.to(device)
    
    # Define image transformations
    transform = transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.ToTensor(),
        transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
    ])
    
    # Process input path
    input_path = Path(args.input)
    
    if input_path.is_file():
        # Process single image
        print(f"Processing image: {input_path}")
        img, img_tensor = process_image(input_path, transform)
        prediction = predict(model, img_tensor, class_names, device)
        
        output_filename = f"{input_path.stem}_prediction{input_path.suffix}"
        output_path = output_dir / output_filename if not args.show else None
        
        visualize_prediction(img, prediction, output_path, args.show)
        
        # Print results
        print(f"Prediction: {prediction['class_name']} with {prediction['confidence']:.2%} confidence")
        
    elif input_path.is_dir():
        # Process directory of images
        print(f"Processing directory: {input_path}")
        image_extensions = ['.jpg', '.jpeg', '.png']
        image_paths = [f for f in input_path.glob('*') if f.suffix.lower() in image_extensions]
        
        if not image_paths:
            print(f"No images found in {input_path}")
            return
        
        print(f"Found {len(image_paths)} images")
        
        results = []
        for img_path in image_paths:
            print(f"Processing {img_path.name}...")
            img, img_tensor = process_image(img_path, transform)
            prediction = predict(model, img_tensor, class_names, device)
            
            output_filename = f"{img_path.stem}_prediction{img_path.suffix}"
            output_path = output_dir / output_filename if not args.show else None
            
            visualize_prediction(img, prediction, output_path, args.show)
            
            results.append({
                'filename': img_path.name,
                'prediction': prediction['class_name'],
                'confidence': prediction['confidence']
            })
        
        # Save results summary
        with open(output_dir / 'results_summary.json', 'w') as f:
            json.dump(results, f, indent=4)
        
        print(f"Processed {len(results)} images. Results saved to {output_dir}")
    
    else:
        print(f"Input path not found: {input_path}")

if __name__ == "__main__":
    main() 