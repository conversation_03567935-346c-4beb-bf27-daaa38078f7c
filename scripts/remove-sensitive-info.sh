#!/bin/bash

# <PERSON>ript to remove sensitive information from the repository
# This script should be run with caution as it modifies Git history

echo "WARNING: This script will modify Git history and remove sensitive information."
echo "Make sure you have a backup of your repository before proceeding."
echo "This operation is IRREVERSIBLE and will require a force push to remote repositories."
echo ""
read -p "Are you sure you want to continue? (y/n) " -n 1 -r
echo ""

if [[ ! $REPLY =~ ^[Yy]$ ]]
then
    echo "Operation cancelled."
    exit 1
fi

# Create a backup of the repository
echo "Creating backup of the repository..."
BACKUP_DIR="../$(basename $(pwd))_backup_$(date +%Y%m%d%H%M%S)"
mkdir -p "$BACKUP_DIR"
cp -r ./* "$BACKUP_DIR"
cp -r ./.git "$BACKUP_DIR"
echo "Backup created at $BACKUP_DIR"

# Remove sensitive files from Git history
echo "Removing sensitive files from Git history..."

# Remove .env files
git filter-branch --force --index-filter \
  "git rm --cached --ignore-unmatch .env .env.local .env.development.local .env.test.local .env.production.local" \
  --prune-empty --tag-name-filter cat -- --all

# Remove any other sensitive files
git filter-branch --force --index-filter \
  "git rm --cached --ignore-unmatch src/lib/api/openai/config.js src/lib/services/location/config.js" \
  --prune-empty --tag-name-filter cat -- --all

# Clean up
echo "Cleaning up..."
rm -rf .git/refs/original/
git reflog expire --expire=now --all
git gc --prune=now
git gc --aggressive --prune=now

echo ""
echo "Sensitive information has been removed from Git history."
echo "You will need to force push these changes to your remote repositories:"
echo "  git push origin --force --all"
echo "  git push origin --force --tags"
echo ""
echo "IMPORTANT: All collaborators will need to clone the repository again after this operation."
echo "The backup of your original repository is available at $BACKUP_DIR"
