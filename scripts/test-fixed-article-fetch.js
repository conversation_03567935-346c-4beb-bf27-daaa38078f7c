#!/usr/bin/env node

import { config } from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { createClient } from '@supabase/supabase-js';
import { TRUSTED_SOURCES, fetchRssFeed } from '../src/lib/services/articleFetchServiceFixed.js';

// Load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = dirname(__dirname);
config({ path: join(rootDir, '.env') });

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('Environment Variables:');
console.log('VITE_SUPABASE_URL:', supabaseUrl ? 'Present' : 'Missing');
console.log('VITE_SUPABASE_ANON_KEY:', supabaseKey ? 'Present' : 'Missing');

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase credentials in environment variables');
  process.exit(1);
}

// Create global supabase client for the article service to use
global.supabase = createClient(supabaseUrl, supabaseKey);

// Test function to fetch an article from the first source
const testFetch = async () => {
  console.log('===================================');
  console.log('🔍 Testing Fixed Article Fetch');
  console.log('===================================');
  
  // Get the first source from the list
  const sourceName = Object.keys(TRUSTED_SOURCES)[0];
  const source = TRUSTED_SOURCES[sourceName];
  
  console.log(`Testing fetch from: ${source.name}`);
  
  try {
    const articles = await fetchRssFeed(source);
    
    if (articles.length > 0) {
      console.log('✅ Successfully fetched articles');
      console.log(`Found ${articles.length} articles`);
      
      // Display the first article details
      console.log('\nSample article:');
      console.log('Title:', articles[0].title);
      console.log('Summary:', articles[0].summary?.slice(0, 150) + '...');
      console.log('Source:', articles[0].source);
      console.log('Source Type:', articles[0].source_type);
      console.log('Link:', articles[0].link);
      console.log('Tags:', articles[0].tags);
      
      // Try to insert the article
      console.log('\nAttempting to insert this article...');
      const { data, error } = await global.supabase
        .from('dental_articles')
        .insert([articles[0]])
        .select();
      
      if (error) {
        console.error('❌ Error inserting article:', error);
        
        if (error.message.includes('row-level security')) {
          console.log('\n⚠️ Row-level security is preventing inserts. Please update RLS policies in Supabase:');
          console.log('1. Go to Supabase Dashboard > Authentication > Policies');
          console.log('2. Find the "dental_articles" table');
          console.log('3. Add a policy to allow inserts with the following condition:');
          console.log('   - FOR INSERT WITH CHECK (true)');
        }
      } else {
        console.log('✅ Article successfully inserted!');
        console.log('Article ID:', data[0].id);
      }
      
      console.log('\n✅ RSS fetching is working properly!');
    } else {
      console.log('⚠️ No articles found. The feed might be empty or the parsing failed.');
    }
  } catch (error) {
    console.error('❌ Error testing article fetch:', error);
  }
};

// Run the test
testFetch().catch(error => {
  console.error('Unhandled error in test:', error);
  process.exit(1);
}); 