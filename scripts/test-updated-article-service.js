#!/usr/bin/env node

// Import required packages
import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';
import { fileURLToPath } from 'url';
import { dirname, resolve } from 'path';

// Setup for proper ES module file paths
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Initialize environment variables
dotenv.config();

// Set up Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

// Check if required environment variables are present
console.log('Environment variables check:');
console.log('- VITE_SUPABASE_URL:', supabaseUrl ? '✅ Present' : '❌ Missing');
console.log('- VITE_SUPABASE_ANON_KEY:', supabaseKey ? '✅ Present' : '❌ Missing');

if (!supabaseUrl || !supabaseKey) {
  console.error('Error: Missing required environment variables.');
  process.exit(1);
}

// Initialize global Supabase client
global.supabase = createClient(supabaseUrl, supabaseKey);
console.log('Supabase client initialized successfully.');

// Now that global.supabase is set, import the article service
const articleServiceModule = await import('../src/lib/services/articleFetchServiceUpdated.js');
const { TRUSTED_SOURCES, fetchRssFeed, fetchAndStoreArticles } = articleServiceModule;

// Test function
async function testUpdatedArticleService() {
  try {
    console.log('Testing Updated Article Fetching Service');
    console.log('======================================');
    
    // 1. Display available RSS sources
    console.log('Available RSS Sources:');
    Object.entries(TRUSTED_SOURCES).forEach(([key, source]) => {
      console.log(`- ${source.name} (${source.feedUrl})`);
    });
    console.log('');
    
    // 2. Test fetching from a single source first
    console.log('Testing fetch from a single source (Dental Tribune):');
    const articles = await fetchRssFeed(TRUSTED_SOURCES.DENTAL_TRIBUNE);
    console.log(`Fetched ${articles.length} articles from Dental Tribune`);
    
    if (articles.length > 0) {
      console.log('Sample article:');
      const sample = articles[0];
      console.log('- Title:', sample.title);
      console.log('- Summary:', sample.summary?.substring(0, 100) + '...');
      console.log('- Link:', sample.link);
      console.log('- Tags:', sample.tags?.join(', '));
      console.log('');
      
      // 3. Test storing a single article
      console.log('Testing storing a single article...');
      const result = await global.supabase
        .from('dental_articles')
        .insert([sample])
        .select();
      
      if (result.error) {
        console.error('Error storing sample article:', result.error);
      } else {
        console.log('✅ Successfully stored sample article with ID:', result.data[0].id);
      }
    }
    
    // 4. Test the full fetch and store process
    console.log('\nTesting full article fetch and store process:');
    console.log('This will fetch articles from all sources and store them in the database.');
    console.log('Starting in 3 seconds...');
    
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    const metrics = await fetchAndStoreArticles();
    
    console.log('\nFinal Results:');
    console.log(`- Sources processed: ${metrics.sources}`);
    console.log(`- Articles fetched: ${metrics.fetched}`);
    console.log(`- Articles stored: ${metrics.stored}`);
    console.log(`- Articles skipped (duplicates): ${metrics.skipped}`);
    console.log(`- Failures: ${metrics.failed}`);
    
    console.log('\nTest completed successfully!');
  } catch (error) {
    console.error('Test failed with error:', error);
    process.exit(1);
  }
}

// Run the test
testUpdatedArticleService(); 