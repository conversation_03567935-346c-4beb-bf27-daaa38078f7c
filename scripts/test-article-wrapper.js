#!/usr/bin/env node

import { config } from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = dirname(__dirname);
config({ path: join(rootDir, '.env') });

// Initialize Supabase client directly from environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('Environment Variables:');
console.log('VITE_SUPABASE_URL:', supabaseUrl || 'Missing');
console.log('VITE_SUPABASE_ANON_KEY:', supabaseKey ? 'Present' : 'Missing');

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase credentials in environment variables');
  process.exit(1);
}

// Create global Supabase client before importing the article service
global.supabase = createClient(supabaseUrl, supabaseKey);

// Now import the article fetch service
import { TRUSTED_SOURCES, fetchRssFeed, fetchAndStoreArticles } from '../src/lib/services/articleFetchServiceFixed.js';

// Test function
const testArticleFetch = async () => {
  console.log('===================================');
  console.log('🔍 Testing Article System with Wrapper');
  console.log('===================================');
  
  // Get the first source
  const firstSourceKey = Object.keys(TRUSTED_SOURCES)[0];
  const firstSource = TRUSTED_SOURCES[firstSourceKey];
  
  console.log(`Testing fetch from: ${firstSource.name}`);
  
  try {
    // Test fetch RSS
    const articles = await fetchRssFeed(firstSource);
    
    if (articles.length > 0) {
      console.log(`✅ Successfully fetched ${articles.length} articles`);
      
      // Display details of first article
      console.log('\nFirst article details:');
      console.log('Title:', articles[0].title);
      console.log('Summary:', articles[0].summary?.substring(0, 100) + '...');
      
      // Test inserting the article
      console.log('\nAttempting to insert article...');
      const { data, error } = await global.supabase
        .from('dental_articles')
        .insert([articles[0]])
        .select();
      
      if (error) {
        console.error('❌ Error inserting article:', error);
      } else {
        console.log('✅ Article successfully inserted!');
        console.log('Article ID:', data[0].id);
      }
    } else {
      console.log('⚠️ No articles found. The feed might be empty.');
    }
    
    // Now test the fetchAndStoreArticles function
    console.log('\nTesting fetchAndStoreArticles function...');
    const metrics = await fetchAndStoreArticles();
    
    console.log('Metrics from fetch and store operation:');
    console.log(`- Sources: ${metrics.sources}`);
    console.log(`- Articles fetched: ${metrics.fetched}`);
    console.log(`- Articles stored: ${metrics.stored}`);
    console.log(`- Articles skipped: ${metrics.skipped}`);
    console.log(`- Articles failed: ${metrics.failed}`);
    
    console.log('\n===================================');
    console.log('✅ Article system test completed!');
    console.log('===================================');
  } catch (error) {
    console.error('❌ Error testing article system:', error);
  }
};

// Run the test
testArticleFetch().catch(error => {
  console.error('Unhandled error in wrapper:', error);
  process.exit(1);
}); 