#!/usr/bin/env node

import { XMLParser } from 'fast-xml-parser';

// Define potential dental RSS feeds to test
const RSS_FEEDS_TO_TEST = [
  // Alternative feeds for ADA
  { name: "ADA News", url: "https://www.ada.org/rss/news" },
  { name: "ADA Publications", url: "https://jada.ada.org/current.rss" },
  
  // Alternative feeds for DentistryIQ
  { name: "DentistryIQ Alternative", url: "https://www.dentistryiq.com/rss/" },
  
  // Alternative feeds for Dental Economics
  { name: "Dental Economics Alternative", url: "https://www.dentaleconomics.com/rss/" },
  
  // Other dental sources
  { name: "Dental Tribune", url: "https://www.dental-tribune.com/feed/" },
  { name: "Dental Products Report", url: "https://www.dentalproductsreport.com/rss.xml" },
  { name: "Dentistry Today", url: "https://www.dentistrytoday.com/feed/" },
  { name: "The Probe", url: "https://www.the-probe.co.uk/feed/" },
  { name: "Oral Health", url: "https://www.oralhealthgroup.com/feed/" },
  { name: "Dental Review News", url: "https://www.dental-review.co.uk/feed/" },
  { name: "Modern Dental Network", url: "https://www.moderndentistrynetwork.com/rss/" },
  { name: "RDH Magazine", url: "https://www.rdhmag.com/rss/" },
  { name: "Dental Asia", url: "https://www.dentalasia.net/feed/" },
  { name: "Digital Esthetics", url: "https://www.digitalesthetics.com/rss/" }
];

// Fetch and check a single RSS feed
const testRssFeed = async (feed) => {
  try {
    console.log(`Testing: ${feed.name} (${feed.url})`);
    
    const response = await fetch(feed.url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; SmiloBot/1.0; +https://smilo.dental)',
        'Accept': 'application/rss+xml, application/xml, text/xml, application/atom+xml'
      },
      timeout: 10000 // 10 second timeout
    });
    
    if (!response.ok) {
      return {
        name: feed.name,
        url: feed.url,
        status: `Error: HTTP ${response.status}`,
        working: false
      };
    }
    
    const xmlData = await response.text();
    
    // Check if it looks like valid XML
    if (!xmlData.trim().startsWith('<?xml') && !xmlData.trim().startsWith('<rss') && !xmlData.trim().startsWith('<feed')) {
      return {
        name: feed.name,
        url: feed.url,
        status: 'Error: Not valid XML/RSS',
        working: false
      };
    }
    
    // Try to parse it
    const parser = new XMLParser({
      ignoreAttributes: false,
      attributeNamePrefix: "@_",
      parseAttributeValue: true,
      trimValues: true
    });
    
    const parsed = parser.parse(xmlData);
    
    // Check for RSS structure
    let itemCount = 0;
    if (parsed.rss?.channel?.item) {
      const items = Array.isArray(parsed.rss.channel.item) ? parsed.rss.channel.item : [parsed.rss.channel.item];
      itemCount = items.length;
    } else if (parsed.feed?.entry) {
      const items = Array.isArray(parsed.feed.entry) ? parsed.feed.entry : [parsed.feed.entry];
      itemCount = items.length;
    } else if (parsed.rdf?.item) {
      const items = Array.isArray(parsed.rdf.item) ? parsed.rdf.item : [parsed.rdf.item];
      itemCount = items.length;
    }
    
    return {
      name: feed.name,
      url: feed.url,
      status: `Success: Found ${itemCount} items`,
      working: true,
      itemCount
    };
  } catch (error) {
    return {
      name: feed.name,
      url: feed.url,
      status: `Error: ${error.message}`,
      working: false
    };
  }
};

// Test all feeds
const testAllFeeds = async () => {
  console.log('===================================');
  console.log('🔍 Testing Dental RSS Feeds');
  console.log('===================================');
  
  const results = [];
  
  for (const feed of RSS_FEEDS_TO_TEST) {
    const result = await testRssFeed(feed);
    results.push(result);
    
    // Wait a bit between requests to be polite
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // Display results
  console.log('\nResults:');
  console.log('===================================');
  
  const workingFeeds = results.filter(r => r.working);
  const failedFeeds = results.filter(r => !r.working);
  
  console.log(`✅ ${workingFeeds.length} working feeds found`);
  console.log(`❌ ${failedFeeds.length} feeds failed`);
  
  console.log('\nWorking feeds:');
  workingFeeds.forEach(feed => {
    console.log(`- ${feed.name}: ${feed.status} (${feed.url})`);
  });
  
  console.log('\nFailed feeds:');
  failedFeeds.forEach(feed => {
    console.log(`- ${feed.name}: ${feed.status} (${feed.url})`);
  });
  
  // Generate updated code for working feeds
  if (workingFeeds.length > 0) {
    console.log('\nRecommended TRUSTED_SOURCES configuration:');
    console.log('```javascript');
    console.log('const TRUSTED_SOURCES = {');
    
    workingFeeds.forEach((feed, index) => {
      const key = feed.name.toUpperCase().replace(/[^A-Z0-9]/g, '_');
      console.log(`  ${key}: {`);
      console.log(`    name: '${feed.name}',`);
      console.log(`    feedUrl: '${feed.url}',`);
      console.log(`    type: 'rss'`);
      console.log(`  }${index < workingFeeds.length - 1 ? ',' : ''}`);
    });
    
    console.log('};');
    console.log('```');
  }
  
  console.log('\n===================================');
};

// Run the tests
testAllFeeds().catch(error => {
  console.error('Error testing feeds:', error);
  process.exit(1);
}); 