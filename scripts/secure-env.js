/**
 * Secure Environment Variables Handler
 * 
 * This script provides a secure way to access environment variables
 * with additional validation and obfuscation.
 */

// Import required modules
const crypto = require('crypto');

/**
 * Get an environment variable with validation
 * @param {string} key - The environment variable key
 * @param {boolean} required - Whether the variable is required
 * @param {string} defaultValue - Default value if not required and not found
 * @returns {string} The environment variable value
 */
function getEnvVar(key, required = false, defaultValue = '') {
  const value = process.env[key] || '';
  
  if (required && !value) {
    console.error(`Required environment variable ${key} is not set!`);
    throw new Error(`Required environment variable ${key} is not set!`);
  }
  
  return value || defaultValue;
}

/**
 * Get an API key with obfuscation for logging
 * @param {string} key - The environment variable key for the API key
 * @param {boolean} required - Whether the API key is required
 * @returns {Object} Object containing the API key and an obfuscated version for logging
 */
function getApiKey(key, required = true) {
  const apiKey = getEnvVar(key, required);
  
  if (!apiKey) {
    return { 
      value: '', 
      obfuscated: '',
      isValid: false
    };
  }
  
  // Create an obfuscated version for logging (only show first 4 and last 4 chars)
  const obfuscated = apiKey.length > 8
    ? `${apiKey.substring(0, 4)}...${apiKey.substring(apiKey.length - 4)}`
    : '********';
  
  return {
    value: apiKey,
    obfuscated,
    isValid: true
  };
}

/**
 * Validate a Supabase URL
 * @param {string} url - The Supabase URL to validate
 * @returns {boolean} Whether the URL is valid
 */
function isValidSupabaseUrl(url) {
  if (!url) return false;
  
  try {
    const urlObj = new URL(url);
    return urlObj.protocol === 'https:' && 
           urlObj.hostname.includes('supabase') &&
           !urlObj.hostname.includes('localhost');
  } catch (e) {
    return false;
  }
}

/**
 * Validate a Supabase anon key
 * @param {string} key - The Supabase anon key to validate
 * @returns {boolean} Whether the key is valid
 */
function isValidSupabaseAnonKey(key) {
  if (!key) return false;
  
  // Supabase anon keys are JWT tokens
  return key.startsWith('eyJ') && key.includes('.');
}

/**
 * Get Supabase configuration with validation
 * @returns {Object} Object containing Supabase URL and anon key
 */
function getSupabaseConfig() {
  const url = getEnvVar('VITE_SUPABASE_URL', true);
  const anonKey = getEnvVar('VITE_SUPABASE_ANON_KEY', true);
  
  if (!isValidSupabaseUrl(url)) {
    console.error('Invalid Supabase URL format!');
  }
  
  if (!isValidSupabaseAnonKey(anonKey)) {
    console.error('Invalid Supabase anon key format!');
  }
  
  return {
    url,
    anonKey,
    isValid: isValidSupabaseUrl(url) && isValidSupabaseAnonKey(anonKey)
  };
}

/**
 * Generate a secure hash of a value for comparison
 * @param {string} value - The value to hash
 * @returns {string} The hashed value
 */
function generateSecureHash(value) {
  return crypto.createHash('sha256').update(value).digest('hex');
}

module.exports = {
  getEnvVar,
  getApiKey,
  getSupabaseConfig,
  generateSecureHash
};
