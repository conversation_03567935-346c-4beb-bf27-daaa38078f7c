#!/usr/bin/env node

import { config } from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = dirname(__dirname);
config({ path: join(rootDir, '.env') });

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase credentials in environment variables');
  console.error('Please ensure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Function to check if a column exists in a table
const columnExists = async (table, column) => {
  try {
    // Use a direct query to check if the column exists
    const { data, error } = await supabase
      .from(table)
      .select(column)
      .limit(1)
      .single();
    
    if (error && error.code === 'PGRST204') {
      // No rows returned but we only care about the column existence
      return false;
    } else if (error && error.message.includes(`Could not find the '${column}' column`)) {
      return false;
    }
    
    // If no error, or error not related to column missing, assume column exists
    return true;
  } catch (error) {
    console.error(`Error checking if column ${column} exists:`, error);
    return false;
  }
};

// Function to add a specific column using the Supabase API
const addColumn = async (table, columnDef) => {
  try {
    const { name, type, default_value } = columnDef;
    
    console.log(`Attempting to add ${name} column to ${table} table...`);
    
    // Check if the column already exists
    const exists = await columnExists(table, name);
    if (exists) {
      console.log(`✅ Column ${name} already exists in ${table} table`);
      return true;
    }
    
    // Direct SQL may not be available with anon key, so we'll first try to add a row with the column
    // This is a common workaround when we don't have direct SQL access
    const testObj = {};
    testObj[name] = default_value;
    
    const { error } = await supabase
      .from(table)
      .insert([testObj]);
    
    if (!error) {
      console.log(`✅ Successfully added ${name} column to ${table} table`);
      return true;
    } else {
      console.log(`⚠️ Could not add column through insert: ${error.message}`);
      console.log('This likely means you need admin access to modify the table structure');
      return false;
    }
  } catch (error) {
    console.error(`❌ Error adding ${columnDef.name} column:`, error);
    return false;
  }
};

// Main function
const main = async () => {
  console.log('Checking for missing columns in dental_articles table...');
  
  // List of columns that should be in the dental_articles table
  const requiredColumns = [
    { name: 'is_featured', type: 'BOOLEAN', default_value: false },
    { name: 'source', type: 'TEXT', default_value: 'manual' },
    { name: 'source_type', type: 'TEXT', default_value: 'internal' },
    { name: 'tags', type: 'TEXT[]', default_value: ['manual'] }
  ];
  
  // Check and add each column
  for (const column of requiredColumns) {
    const added = await addColumn('dental_articles', column);
    if (!added) {
      console.log(`⚠️ Could not add ${column.name} column. You may need admin access.`);
    }
  }
  
  // Try to insert a minimal record as a test
  console.log('Attempting to insert a basic article...');
  const { data, error } = await supabase
    .from('dental_articles')
    .insert([
      {
        title: 'Test Article',
        content: 'This is a test article added to verify the dental_articles table structure.',
        source: 'manual',
        is_featured: false
      }
    ])
    .select();
  
  if (error) {
    console.error('❌ Error inserting test article:', error);
    if (error.message.includes('not-null')) {
      console.log('💡 There might be NOT NULL constraints on some columns.');
    }
  } else {
    console.log('✅ Successfully inserted test article!');
    console.log(data);
  }
  
  console.log('Done.');
};

// Run the main function
main().catch(error => {
  console.error('Failed to run script:', error);
  process.exit(1);
}); 