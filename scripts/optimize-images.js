import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import sharp from 'sharp';

// Get current file directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Define paths
const publicDir = path.join(__dirname, '../public');
const imagesDir = path.join(publicDir, 'images');

// Create images directory if it doesn't exist
if (!fs.existsSync(imagesDir)) {
  fs.mkdirSync(imagesDir, { recursive: true });
}

// Function to optimize an image
async function optimizeImage(inputPath, outputDir, options = {}) {
  const filename = path.basename(inputPath);
  const name = path.parse(filename).name;
  
  try {
    // Load the image
    let image = sharp(inputPath);
    
    // Resize if dimensions are provided
    if (options.width || options.height) {
      image = image.resize({
        width: options.width,
        height: options.height,
        fit: options.fit || 'cover',
        position: options.position || 'center'
      });
    }
    
    // Convert to WebP with high compression
    const webpOutputPath = path.join(outputDir, `${name}.webp`);
    await image
      .webp({ quality: options.quality || 80 })
      .toFile(webpOutputPath);
    
    console.log(`Optimized: ${webpOutputPath}`);
    
    // Also create a small version for mobile if requested
    if (options.createMobile) {
      const mobileWidth = options.mobileWidth || Math.floor((options.width || 800) / 2);
      const mobileHeight = options.height ? Math.floor(options.height / 2) : null;
      
      const mobileOutputPath = path.join(outputDir, `${name}-mobile.webp`);
      await sharp(inputPath)
        .resize({
          width: mobileWidth,
          height: mobileHeight,
          fit: options.fit || 'cover',
          position: options.position || 'center'
        })
        .webp({ quality: options.quality || 80 })
        .toFile(mobileOutputPath);
      
      console.log(`Created mobile version: ${mobileOutputPath}`);
    }
    
    return true;
  } catch (error) {
    console.error(`Error optimizing ${filename}:`, error);
    return false;
  }
}

// Optimize the logo
async function optimizeLogo() {
  const logoPath = path.join(publicDir, 'images', 'smilo-logo1.jpg');
  
  // Check if the logo exists
  if (!fs.existsSync(logoPath)) {
    console.log('Logo not found at:', logoPath);
    return;
  }
  
  // Optimize for different sizes
  await optimizeImage(logoPath, imagesDir, {
    width: 80,
    height: 80,
    quality: 85,
    createMobile: true,
    mobileWidth: 48
  });
  
  // Create favicon sizes
  await optimizeImage(logoPath, imagesDir, {
    width: 32,
    height: 32,
    quality: 90
  });
  
  console.log('Logo optimization complete');
}

// Main function
async function main() {
  console.log('Starting image optimization...');
  
  // Optimize logo
  await optimizeLogo();
  
  // Find and optimize all images in the public directory
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif'];
  
  function findImages(dir, images = []) {
    const files = fs.readdirSync(dir);
    
    for (const file of files) {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        findImages(filePath, images);
      } else if (imageExtensions.includes(path.extname(file).toLowerCase())) {
        images.push(filePath);
      }
    }
    
    return images;
  }
  
  const images = findImages(publicDir).filter(img => !img.includes('node_modules'));
  console.log(`Found ${images.length} images to optimize`);
  
  // Optimize each image
  for (const image of images) {
    const outputDir = path.dirname(image);
    await optimizeImage(image, outputDir, {
      quality: 80
    });
  }
  
  console.log('Image optimization complete!');
}

main().catch(console.error);
