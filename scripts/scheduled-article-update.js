#!/usr/bin/env node

/**
 * Scheduled Article Update Script
 * 
 * This script is designed to be run on a schedule (e.g., daily) to fetch
 * and store new dental articles from various RSS feeds.
 * 
 * Usage:
 *   node scripts/scheduled-article-update.js
 *
 * Recommended schedule: Daily at off-peak hours
 */

// Import required packages
import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// Setup for proper ES module file paths
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Initialize environment variables
dotenv.config();

// Set up logging
const logDir = path.join(__dirname, '../logs');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

const logFile = path.join(logDir, `article-update-${new Date().toISOString().split('T')[0]}.log`);
const logStream = fs.createWriteStream(logFile, { flags: 'a' });

// Custom logger that writes to both console and log file
const logger = {
  log: (...args) => {
    const message = args.map(arg => 
      typeof arg === 'object' ? JSON.stringify(arg) : arg
    ).join(' ');
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] INFO: ${message}`;
    console.log(logMessage);
    logStream.write(logMessage + '\n');
  },
  error: (...args) => {
    const message = args.map(arg => 
      typeof arg === 'object' ? JSON.stringify(arg) : arg
    ).join(' ');
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ERROR: ${message}`;
    console.error(logMessage);
    logStream.write(logMessage + '\n');
  }
};

// Set up Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

// Check if required environment variables are present
logger.log('Environment variables check:');
logger.log('- VITE_SUPABASE_URL:', supabaseUrl ? '✅ Present' : '❌ Missing');
logger.log('- VITE_SUPABASE_ANON_KEY:', supabaseKey ? '✅ Present' : '❌ Missing');

if (!supabaseUrl || !supabaseKey) {
  logger.error('Error: Missing required environment variables.');
  process.exit(1);
}

// Initialize global Supabase client
global.supabase = createClient(supabaseUrl, supabaseKey);
logger.log('Supabase client initialized successfully.');

// Now that global.supabase is set, import the article service
const articleServiceModule = await import('../src/lib/services/articleFetchServiceUpdated.js');
const { fetchAndStoreArticles } = articleServiceModule;

// Main function
async function runScheduledUpdate() {
  try {
    logger.log('Starting scheduled article update...');
    
    // Track execution time
    const startTime = Date.now();
    
    // Run the fetch and store process
    const metrics = await fetchAndStoreArticles();
    
    // Calculate execution time
    const executionTime = ((Date.now() - startTime) / 1000).toFixed(2);
    
    // Log results
    logger.log('Article update completed in', executionTime, 'seconds');
    logger.log('Final Results:');
    logger.log(`- Sources processed: ${metrics.sources}`);
    logger.log(`- Articles fetched: ${metrics.fetched}`);
    logger.log(`- Articles stored: ${metrics.stored}`);
    logger.log(`- Articles skipped (duplicates): ${metrics.skipped}`);
    logger.log(`- Failures: ${metrics.failed}`);
    
    // Clean up old articles if needed
    if (metrics.stored > 0) {
      await cleanupOldArticles();
    }
    
    // Close the log stream
    logStream.end();
    
    logger.log('Scheduled update completed successfully!');
    process.exit(0);
  } catch (error) {
    logger.error('Scheduled update failed with error:', error);
    
    // Close the log stream
    logStream.end();
    
    process.exit(1);
  }
}

// Cleanup function to remove very old articles (optional)
async function cleanupOldArticles() {
  try {
    // Keep the 500 most recent articles, delete the rest
    logger.log('Checking for old articles to clean up...');
    
    // First, get the count of articles
    const { count, error: countError } = await global.supabase
      .from('dental_articles')
      .select('id', { count: 'exact', head: true });
    
    if (countError) {
      logger.error('Error counting articles:', countError);
      return;
    }
    
    // If we have more than 500 articles, delete the oldest ones
    if (count > 500) {
      logger.log(`Found ${count} articles, cleaning up oldest articles to keep 500...`);
      
      // Get the IDs of the 500 newest articles
      const { data: newestArticles, error: selectError } = await global.supabase
        .from('dental_articles')
        .select('id')
        .order('created_at', { ascending: false })
        .limit(500);
      
      if (selectError) {
        logger.error('Error selecting newest articles:', selectError);
        return;
      }
      
      // Create an array of IDs to keep
      const idsToKeep = newestArticles.map(article => article.id);
      
      // Delete articles not in the "keep" list
      const { error: deleteError } = await global.supabase
        .from('dental_articles')
        .delete()
        .not('id', 'in', `(${idsToKeep.join(',')})`);
      
      if (deleteError) {
        logger.error('Error deleting old articles:', deleteError);
        return;
      }
      
      logger.log(`Successfully cleaned up old articles, keeping the newest 500.`);
    } else {
      logger.log(`Currently have ${count} articles, no cleanup needed.`);
    }
  } catch (error) {
    logger.error('Error during article cleanup:', error);
  }
}

// Run the scheduled update
runScheduledUpdate(); 