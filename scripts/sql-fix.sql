-- Run this in the Supabase SQL Editor to fix the dental_articles table

-- First, check if the table exists and create it if needed
CREATE TABLE IF NOT EXISTS dental_articles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title TEXT,
  content TEXT,
  summary TEXT,
  source TEXT DEFAULT 'manual',
  source_type TEXT DEFAULT 'internal',
  source_url TEXT,
  link TEXT,
  pub_date TIMESTAMP WITH TIME ZONE,
  inserted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  authors TEXT,
  tags TEXT[],
  image_url TEXT,
  is_featured BOOLEAN DEFAULT FALSE,
  is_published BOOLEAN DEFAULT TRUE,
  view_count INTEGER DEFAULT 0,
  quality_score FLOAT DEFAULT 0.0
);

-- Add any missing columns that might be needed
ALTER TABLE dental_articles ADD COLUMN IF NOT EXISTS is_featured BOOLEAN DEFAULT FALSE;
ALTER TABLE dental_articles ADD COLUMN IF NOT EXISTS source TEXT DEFAULT 'manual';
ALTER TABLE dental_articles ADD COLUMN IF NOT EXISTS source_type TEXT DEFAULT 'internal';
ALTER TABLE dental_articles ADD COLUMN IF NOT EXISTS tags TEXT[] DEFAULT '{}'::TEXT[];

-- Make sure nullable fields are properly set
ALTER TABLE dental_articles ALTER COLUMN content DROP NOT NULL;
ALTER TABLE dental_articles ALTER COLUMN source DROP NOT NULL;

-- Create or recreate RLS policies
ALTER TABLE dental_articles ENABLE ROW LEVEL SECURITY;

-- Allow anonymous users to read dental articles
DROP POLICY IF EXISTS "Allow anonymous read access" ON dental_articles;
CREATE POLICY "Allow anonymous read access" ON dental_articles
  FOR SELECT USING (true);

-- Allow authenticated users to insert articles
DROP POLICY IF EXISTS "Allow authenticated insert" ON dental_articles;
CREATE POLICY "Allow authenticated insert" ON dental_articles
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Insert a test article
INSERT INTO dental_articles (title, content, summary, source, source_type, is_featured)
VALUES (
  'Test Article from SQL Editor',
  'This is a test article created directly from the SQL Editor.',
  'Test summary',
  'manual',
  'internal',
  FALSE
);

-- Refresh the schema cache if needed
NOTIFY pgrst, 'reload schema'; 