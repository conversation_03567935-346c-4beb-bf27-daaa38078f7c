#!/usr/bin/env node

import { config } from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { createClient } from '@supabase/supabase-js';
import fs from 'fs';

// Get the path to the script and project root
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = dirname(__dirname);

// Load environment variables from .env file
config({ path: join(rootDir, '.env') });

// Manually load environment variables if dotenv doesn't work
try {
  if (!process.env.VITE_SUPABASE_URL) {
    const envContent = fs.readFileSync(join(rootDir, '.env'), 'utf8');
    const envLines = envContent.split('\n');
    
    envLines.forEach(line => {
      const match = line.match(/^(VITE_[A-Z_]+)=(.+)$/);
      if (match) {
        const [, key, value] = match;
        process.env[key] = value.trim();
      }
    });
  }
} catch (err) {
  console.warn('Error reading .env file manually:', err.message);
}

// Check if env variables are loaded
console.log('Checking environment variables:');
console.log('VITE_SUPABASE_URL:', process.env.VITE_SUPABASE_URL ? 'Present' : 'Missing');
console.log('VITE_SUPABASE_ANON_KEY:', process.env.VITE_SUPABASE_ANON_KEY ? 'Present' : 'Missing');

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase credentials in environment variables');
  console.error('Please ensure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set in .env file');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

async function fixArticlesTable() {
  console.log('Fixing dental_articles table structure...');
  
  try {
    // Add missing columns using SQL RPC
    const { error } = await supabase.rpc('exec_sql', {
      sql_query: `
        -- Ensure UUID extension is available
        CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
        
        -- Add missing columns if they don't exist
        ALTER TABLE dental_articles 
        ADD COLUMN IF NOT EXISTS is_featured BOOLEAN DEFAULT FALSE,
        ADD COLUMN IF NOT EXISTS is_published BOOLEAN DEFAULT TRUE,
        ADD COLUMN IF NOT EXISTS view_count INTEGER DEFAULT 0,
        ADD COLUMN IF NOT EXISTS quality_score FLOAT DEFAULT 0.0;
      `
    });
    
    if (error) {
      console.error('❌ Error executing SQL:', error);
      
      // Try a different approach if rpc fails
      console.log('Trying alternative approach using separate ALTER TABLE statements...');
      
      // Try adding columns one by one
      const addColumns = async () => {
        try {
          // This is less efficient but more likely to work
          const columns = [
            { name: 'is_featured', type: 'boolean', default: 'false' },
            { name: 'is_published', type: 'boolean', default: 'true' },
            { name: 'view_count', type: 'integer', default: '0' },
            { name: 'quality_score', type: 'float', default: '0.0' }
          ];
          
          for (const col of columns) {
            try {
              const { error } = await supabase.rpc('exec_sql', {
                sql_query: `ALTER TABLE dental_articles ADD COLUMN IF NOT EXISTS ${col.name} ${col.type} DEFAULT ${col.default};`
              });
              
              if (!error) {
                console.log(`✅ Added column ${col.name} successfully`);
              } else {
                console.warn(`⚠️ Could not add column ${col.name}: ${error.message}`);
              }
            } catch (e) {
              console.warn(`⚠️ Error adding column ${col.name}: ${e.message}`);
            }
          }
        } catch (e) {
          console.error('❌ Alternative approach failed:', e);
        }
      };
      
      await addColumns();
    } else {
      console.log('✅ Successfully updated dental_articles table structure');
    }
    
    // Test adding a sample article
    const { error: insertError } = await supabase
      .from('dental_articles')
      .insert([{
        title: 'Welcome to Smilo Dental Resources',
        content: 'This is a sample article to help you get started with the dental resources system. Real articles will be fetched automatically from trusted dental sources.',
        summary: 'Welcome to the Smilo Dental Resources system',
        source: 'Smilo Dental',
        source_type: 'internal',
        is_featured: true,
        tags: ['welcome', 'introduction']
      }]);
      
    if (insertError) {
      console.error('❌ Error adding sample article:', insertError);
      console.log('Trying to debug table structure...');
      
      // Check table structure
      const { data, error: descError } = await supabase.rpc('exec_sql', {
        sql_query: `SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'dental_articles';`
      });
      
      if (descError) {
        console.error('❌ Could not inspect table structure:', descError);
      } else {
        console.log('Current table structure:');
        console.log(data);
      }
    } else {
      console.log('✅ Sample article added successfully');
    }
    
  } catch (error) {
    console.error('❌ Error fixing table:', error);
  }
}

// Run the fix
fixArticlesTable()
  .then(() => console.log('Done'))
  .catch(err => console.error('Unhandled error:', err))
  .finally(() => process.exit()); 