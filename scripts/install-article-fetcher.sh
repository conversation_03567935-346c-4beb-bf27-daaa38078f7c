#!/bin/bash

# <PERSON>ript to install and set up the article fetcher system
echo "========================================="
echo "Installing Dental Article Fetcher System"
echo "========================================="

# Get the absolute path of the project directory
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_DIR" || exit 1

echo "🔍 Checking prerequisites..."

# Check Node.js installation
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js before proceeding."
    exit 1
fi

NODE_VERSION=$(node -v)
echo "✅ Node.js found: $NODE_VERSION"

# Check npm installation
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm before proceeding."
    exit 1
fi

NPM_VERSION=$(npm -v)
echo "✅ npm found: $NPM_VERSION"

# Create logs directory
mkdir -p "$PROJECT_DIR/logs"
echo "✅ Created logs directory"

# Install required dependencies
echo "📦 Installing required dependencies..."
npm install --save axios xml2js

# Migrate the Supabase database
echo "🗄️ Setting up database schema..."
if command -v supabase &> /dev/null; then
    # If Supabase CLI is available, apply migrations
    echo "✅ Found Supabase CLI, applying migrations..."
    supabase migration up
else
    echo "ℹ️ Supabase CLI not found. You'll need to apply migrations manually."
    echo "   Please run the following SQL script in your Supabase dashboard:"
    echo "   $PROJECT_DIR/supabase/migrations/20250310125950_dental_articles.sql"
fi

# Set up cron job
echo "⏱️ Setting up cron job for hourly article fetching..."
bash "$PROJECT_DIR/scripts/setup-cron.sh"

echo "========================================="
echo "✨ Article Fetcher System Setup Complete!"
echo "========================================="
echo "Articles will now be automatically fetched every hour."
echo "The system will maintain up to 1000 recent dental articles."
echo "Logs can be found in $PROJECT_DIR/logs/article-fetch.log"
echo ""
echo "To fetch articles manually, run:"
echo "npm run fetch-articles"
echo "=========================================" 