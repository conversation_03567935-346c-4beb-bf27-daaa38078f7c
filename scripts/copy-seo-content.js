import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const sourceDir = path.join(__dirname, '../src/content');
const targetDir = path.join(__dirname, '../public/content');

// Create target directory if it doesn't exist
if (!fs.existsSync(targetDir)) {
  fs.mkdirSync(targetDir, { recursive: true });
}

// Copy content files recursively
try {
  fs.cpSync(sourceDir, targetDir, { 
    recursive: true,
    force: true 
  });
  console.log('SEO content copied successfully');
} catch (error) {
  console.error('Error copying SEO content:', error);
} 