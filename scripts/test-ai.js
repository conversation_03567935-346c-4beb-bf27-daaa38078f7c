// Simple script to test the AI functionality

import OpenAI from 'openai';
import 'dotenv/config';

async function testAI() {
  try {
    console.log('Testing AI functionality...');
    
    // Get API key from environment
    const apiKey = process.env.VITE_OPENAI_API_KEY;
    
    if (!apiKey) {
      console.error('Error: No API key found in environment variables.');
      console.log('Make sure VITE_OPENAI_API_KEY is set in your .env file.');
      return;
    }
    
    console.log('API key found. Initializing OpenAI client...');
    
    // Initialize the client
    const openai = new OpenAI({
      apiKey: apiKey,
      dangerouslyAllowBrowser: true
    });
    
    // Test the API with a simple request
    console.log('Sending test request to OpenAI...');
    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system", 
          content: "You are <PERSON><PERSON><PERSON>, a dental AI assistant. Provide concise answers to dental questions."
        },
        {
          role: "user",
          content: "What is a dental cavity?"
        }
      ],
    });
    
    // Log the result
    console.log('\nAI Test Result:');
    console.log('----------------');
    console.log(completion.choices[0].message.content);
    console.log('----------------');
    console.log('AI test completed successfully!');
    
  } catch (error) {
    console.error('Error testing AI:', error);
  }
}

testAI(); 