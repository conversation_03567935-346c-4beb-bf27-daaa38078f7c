#!/usr/bin/env node

import { config } from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { createClient } from '@supabase/supabase-js';
import fs from 'fs';

// Get the path to the script and project root
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = dirname(__dirname);

// Load environment variables from .env file
config({ path: join(rootDir, '.env') });

// Manually load environment variables if dotenv doesn't work
try {
  if (!process.env.VITE_SUPABASE_URL) {
    const envContent = fs.readFileSync(join(rootDir, '.env'), 'utf8');
    const envLines = envContent.split('\n');
    
    envLines.forEach(line => {
      const match = line.match(/^(VITE_[A-Z_]+)=(.+)$/);
      if (match) {
        const [, key, value] = match;
        process.env[key] = value.trim();
      }
    });
  }
} catch (err) {
  console.warn('Error reading .env file manually:', err.message);
}

// Check if env variables are loaded
console.log('Checking environment variables:');
console.log('VITE_SUPABASE_URL:', process.env.VITE_SUPABASE_URL ? 'Present' : 'Missing');
console.log('VITE_SUPABASE_ANON_KEY:', process.env.VITE_SUPABASE_ANON_KEY ? 'Present' : 'Missing');

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase credentials in environment variables');
  console.error('Please ensure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set in .env file');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

async function getDentalArticlesColumns() {
  try {
    // First get the column information by trying to insert a record with all fields
    // Even if it fails, we can see which columns exist from the error message
    const testArticle = {
      title: 'Test Article',
      content: 'Test content',
      summary: 'Test summary',
      source: 'Test',
      source_type: 'test',
      source_url: 'https://test.com',
      link: 'https://test.com/article',
      pub_date: new Date().toISOString(),
      authors: 'Test Author',
      tags: ['test'],
      image_url: 'https://test.com/image.jpg',
      is_featured: true,
      is_published: true,
      view_count: 0,
      quality_score: 0.0
    };
    
    // Try to insert and check the response
    const { error } = await supabase
      .from('dental_articles')
      .insert([testArticle]);
    
    if (error) {
      console.log('Error details:', error);
      
      // Extract missing columns from error message
      if (error.message && error.message.includes('column')) {
        const missingColumns = [];
        const missingColRegex = /column ['"]([^'"]+)['"]/g;
        let match;
        
        while ((match = missingColRegex.exec(error.message)) !== null) {
          missingColumns.push(match[1]);
        }
        
        if (missingColumns.length > 0) {
          console.log('Missing columns:', missingColumns);
          return { existingColumns: Object.keys(testArticle).filter(col => !missingColumns.includes(col)), missingColumns };
        }
      }
    } else {
      console.log('✅ All columns already exist!');
      // Clean up the test record
      return { existingColumns: Object.keys(testArticle), missingColumns: [] };
    }
  } catch (e) {
    console.error('Error checking columns:', e);
  }
  
  return { existingColumns: [], missingColumns: [] };
}

async function fixMissingColumns() {
  try {
    const { existingColumns, missingColumns } = await getDentalArticlesColumns();
    console.log('Existing columns:', existingColumns);
    
    // Now we need to manually create any missing columns without using SQL statements
    if (missingColumns && missingColumns.length > 0) {
      console.log('Adding missing columns:', missingColumns);
      
      // To add columns without SQL, we need to:
      // 1. Get existing records
      // 2. Update them with the new fields
      // This will implicitly create the columns
      
      // Get a record to update
      const { data: existingRecords } = await supabase
        .from('dental_articles')
        .select('id')
        .limit(1);
      
      if (existingRecords && existingRecords.length > 0) {
        const record = existingRecords[0];
        
        // Create an update object with default values for missing columns
        const updateObj = {};
        
        if (missingColumns.includes('is_featured')) {
          updateObj.is_featured = false;
        }
        
        if (missingColumns.includes('is_published')) {
          updateObj.is_published = true;
        }
        
        if (missingColumns.includes('view_count')) {
          updateObj.view_count = 0;
        }
        
        if (missingColumns.includes('quality_score')) {
          updateObj.quality_score = 0.0;
        }
        
        // Update the record to create the columns
        const { error: updateError } = await supabase
          .from('dental_articles')
          .update(updateObj)
          .eq('id', record.id);
          
        if (updateError) {
          console.error('❌ Error updating record to create columns:', updateError);
        } else {
          console.log('✅ Created missing columns by updating existing record');
        }
      } else {
        // No existing records, so create a new one with all required fields
        const { error: insertError } = await supabase
          .from('dental_articles')
          .insert([{
            title: 'Welcome to Smilo Dental Resources',
            content: 'This is a sample article to help you get started with the dental resources system. Real articles will be fetched automatically from trusted dental sources.',
            summary: 'Welcome to the Smilo Dental Resources system',
            source: 'Smilo Dental',
            source_type: 'internal',
            is_featured: true,
            is_published: true,
            view_count: 0,
            quality_score: 0.0,
            tags: ['welcome', 'introduction']
          }]);
          
        if (insertError) {
          console.error('❌ Error creating sample article with all fields:', insertError);
        } else {
          console.log('✅ Created sample article with all required fields');
        }
      }
    } else {
      // Already has all columns or couldn't determine
      // Try to add a sample article
      const { error: insertError } = await supabase
        .from('dental_articles')
        .insert([{
          title: 'Welcome to Smilo Dental Resources',
          content: 'This is a sample article to help you get started with the dental resources system. Real articles will be fetched automatically from trusted dental sources.',
          summary: 'Welcome to the Smilo Dental Resources system',
          source: 'Smilo Dental',
          source_type: 'internal',
          is_featured: true,
          is_published: true,
          view_count: 0,
          quality_score: 0.0,
          tags: ['welcome', 'introduction']
        }]);
        
      if (insertError) {
        console.error('❌ Error adding sample article:', insertError);
      } else {
        console.log('✅ Sample article added successfully');
      }
    }
  } catch (error) {
    console.error('❌ Error fixing columns:', error);
  }
}

// Run the fix
fixMissingColumns()
  .then(() => console.log('Done'))
  .catch(err => console.error('Unhandled error:', err))
  .finally(() => setTimeout(() => process.exit(), 1000)); 