#!/usr/bin/env node

import { config } from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = dirname(__dirname);
config({ path: join(rootDir, '.env') });

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('Environment Variables:');
console.log('VITE_SUPABASE_URL:', supabaseUrl || 'Missing');
console.log('VITE_SUPABASE_ANON_KEY:', supabaseKey ? 'Present' : 'Missing');

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase credentials in environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Function to check articles in the database
const checkArticles = async () => {
  try {
    console.log('Checking articles in the database...');
    
    // Get article count
    const { count, error: countError } = await supabase
      .from('dental_articles')
      .select('*', { count: 'exact', head: true });
    
    if (countError) {
      console.error('❌ Error checking article count:', countError);
      return;
    }
    
    console.log(`Found ${count} articles in the database.`);
    
    // Get the latest articles - try without ordering by inserted_at
    const { data, error } = await supabase
      .from('dental_articles')
      .select('*')
      .limit(5);
    
    if (error) {
      console.error('❌ Error fetching articles:', error);
      return;
    }
    
    if (data.length === 0) {
      console.log('No articles found in the database.');
    } else {
      console.log('\nArticles in database:');
      data.forEach((article, index) => {
        console.log(`\n${index + 1}. Title: ${article.title || 'No title'}`);
        console.log(`   ID: ${article.id || 'No ID'}`);
        console.log(`   Columns: ${Object.keys(article).join(', ')}`);
        
        // Display each column's value
        Object.entries(article).forEach(([key, value]) => {
          // Skip displaying large content fields
          if (key === 'content' && value && value.length > 100) {
            console.log(`   ${key}: ${value.substring(0, 100)}...`);
          } else {
            console.log(`   ${key}: ${value}`);
          }
        });
      });
    }
    
    // Check missing columns based on the schema defined in setupArticleSystem.js
    console.log('\nChecking for missing columns...');
    const expectedColumns = [
      'id', 'title', 'content', 'summary', 'source', 'source_type', 
      'source_url', 'link', 'pub_date', 'inserted_at', 'updated_at',
      'authors', 'tags', 'image_url', 'is_featured', 'is_published',
      'view_count', 'quality_score'
    ];
    
    if (data && data.length > 0) {
      const actualColumns = Object.keys(data[0]);
      const missingColumns = expectedColumns.filter(col => !actualColumns.includes(col));
      
      if (missingColumns.length > 0) {
        console.log(`⚠️ Missing expected columns: ${missingColumns.join(', ')}`);
      } else {
        console.log('✅ All expected columns exist');
      }
    }
  } catch (error) {
    console.error('❌ An error occurred:', error);
  }
};

// Run the main function
checkArticles().catch(error => {
  console.error('Failed to run script:', error);
  process.exit(1);
}); 