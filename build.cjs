// Simple build script to handle build process issues
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting enhanced build process...');

// Check if CI build is requested
const isCiBuild = process.argv.includes('--ci');
if (isCiBuild) {
  console.log('🔍 CI build detected');
}

// Create temporary backup of problematic files
const backupFiles = [
  'src/App.jsx',
  'src/main.jsx',
  'src/utils/isomorphicImports.js',
  'src/lib/utils/validation.js'
];

console.log('📦 Creating backups of sensitive files...');
const backups = {};
backupFiles.forEach(file => {
  try {
    if (fs.existsSync(file)) {
      const content = fs.readFileSync(file, 'utf8');
      backups[file] = content;
      console.log(`✅ Backed up ${file}`);
    }
  } catch (err) {
    console.error(`❌ Error backing up ${file}:`, err);
  }
});

try {
  // Modify App.jsx to remove client-side only code for build
  console.log('🔧 Patching App.jsx for build compatibility...');
  const appFile = 'src/App.jsx';
  if (fs.existsSync(appFile)) {
    let appContent = fs.readFileSync(appFile, 'utf8');

    // Only patch the file if it contains the pattern we're looking for
    if (appContent.includes('// Try to generate missing icons') ||
        appContent.includes('initViewportFix();')) {
      // Replace the client-side code block with a comment
      appContent = appContent.replace(
        /\/\/ Try to generate missing icons[\s\S]*?initViewportFix\(\);[\s\S]*?\}/,
        '// Browser initialization moved to main.jsx for build compatibility'
      );

      fs.writeFileSync(appFile, appContent);
      console.log('✅ App.jsx patched');
    } else {
      console.log('ℹ️ App.jsx already compatible, no patching needed');
    }
  }

  // Check and patch main.jsx to ensure browser-only code is properly handled
  console.log('🔧 Ensuring main.jsx is properly structured...');
  const mainFile = 'src/main.jsx';
  if (fs.existsSync(mainFile)) {
    let mainContent = fs.readFileSync(mainFile, 'utf8');

    // Make sure browser-only code is properly wrapped in window check
    if (!mainContent.includes('if (typeof window !== \'undefined\')')) {
      // Add the check around browser-specific code
      mainContent = mainContent.replace(
        // Match import statements for browser-specific modules
        /(import\s+.*viewportFix.*|import\s+.*generateIcons.*|import\s+.*mobileOptimization.*)/g,
        '// Dynamically import browser-specific utilities\n' +
        '// This approach prevents build errors by ensuring these imports\n' +
        '// are only evaluated at runtime in the browser\n' +
        'if (typeof window !== \'undefined\') {\n' +
        '  $1'
      );

      fs.writeFileSync(mainFile, mainContent);
      console.log('✅ main.jsx patched');
    } else {
      console.log('ℹ️ main.jsx already compatible');
    }
  }

  // Ensure Vite is installed before running build
  console.log('🔧 Verifying Vite installation...');
  try {
    execSync('npm list vite', { stdio: 'inherit' });
    console.log('✅ Vite is installed');
  } catch (error) {
    console.log('⚠️ Vite not found in the project dependencies, installing...');
    try {
      execSync('npm install vite@4.5.0 --save-dev', { stdio: 'inherit' });
      console.log('✅ Vite installed successfully');
    } catch (installError) {
      console.error('❌ Failed to install Vite:', installError);
      throw new Error('Vite installation failed');
    }
  }

  // Find the vite executable path
  let vitePath = './node_modules/.bin/vite';
  if (!fs.existsSync(vitePath)) {
    console.log('⚠️ Vite not found at expected path, searching for alternatives...');

    try {
      // Try to find vite globally
      const whichOutput = execSync('which vite', { encoding: 'utf8' }).trim();
      if (whichOutput) {
        vitePath = whichOutput;
        console.log(`✅ Found global Vite at: ${vitePath}`);
      }
    } catch (e) {
      console.log('ℹ️ No global Vite found, falling back to npx');
      vitePath = 'npx vite';
    }
  } else {
    console.log(`✅ Found local Vite at: ${vitePath}`);
  }

  // Run preliminary build steps
  console.log('🏗️ Running pre-build steps...');
  try {
    execSync('npm run generate-icons', { stdio: 'inherit' });
    console.log('✅ Icons generated');
  } catch (error) {
    console.error('⚠️ Icon generation failed, continuing build anyway:', error.message);
  }

  try {
    execSync('npm run copy-seo-content', { stdio: 'inherit' });
    console.log('✅ SEO content copied');
  } catch (error) {
    console.error('⚠️ SEO content copy failed, continuing build anyway:', error.message);
  }

  // Run the build command
  console.log('🏗️ Running Vite build command...');
  const buildEnv = {
    ...process.env,
    NODE_ENV: 'production',
    TAILWIND_CONFIG_FILE: 'tailwind.config.js'
    // TensorFlow has been removed from the project
  };

  if (isCiBuild) {
    buildEnv.VITE_CI_BUILD = 'true';
  }

  try {
    const viteBuildCmd = vitePath.includes('npx') ? `${vitePath} build` : `${vitePath} build`;
    execSync(viteBuildCmd, {
      stdio: 'inherit',
      env: buildEnv
    });
    console.log('✅ Vite build completed successfully!');
  } catch (buildError) {
    console.error('❌ Vite build failed:', buildError.message);
    throw new Error('Vite build command failed');
  }

  console.log('✅ Build process completed successfully!');
} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
} finally {
  // Restore original files
  console.log('🔄 Restoring original files...');
  Object.entries(backups).forEach(([file, content]) => {
    try {
      fs.writeFileSync(file, content);
      console.log(`✅ Restored ${file}`);
    } catch (err) {
      console.error(`❌ Error restoring ${file}:`, err);
    }
  });
}