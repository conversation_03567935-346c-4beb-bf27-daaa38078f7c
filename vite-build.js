// Direct Vite build script as a fallback mechanism
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting direct Vite build process...');

// Check if Vite exists
const vitePath = './node_modules/.bin/vite';
if (!fs.existsSync(vitePath)) {
  console.error('❌ Vite executable not found at', vitePath);
  console.log('Installing Vite 4.5.0...');
  try {
    execSync('npm install vite@4.5.0 --save-dev', { stdio: 'inherit' });
    console.log('✅ Vite installed successfully');
  } catch (error) {
    console.error('❌ Failed to install Vite:', error.message);
    process.exit(1);
  }
}

// Set necessary environment variables
process.env.NODE_ENV = 'production';
process.env.TAILWIND_CONFIG_FILE = 'tailwind.config.js';
process.env.VITE_SKIP_TF = 'true';

// Run Vite build
try {
  console.log('🏗️ Running Vite build...');
  execSync(`${vitePath} build`, {
    stdio: 'inherit',
    env: process.env
  });
  console.log('✅ Build completed successfully!');
} catch (error) {
  console.error('❌ Build failed:', error.message);
  
  // Try one last approach with npx
  console.log('⚠️ Attempting build with npx as a last resort...');
  try {
    execSync('npx vite build', {
      stdio: 'inherit',
      env: process.env
    });
    console.log('✅ Build with npx completed successfully!');
  } catch (npxError) {
    console.error('❌ Final build attempt failed:', npxError.message);
    process.exit(1);
  }
} 