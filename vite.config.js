import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';
import { visualizer } from 'rollup-plugin-visualizer';
import bundleOptimizerPlugin from './vite-plugin-bundle-optimizer.js';

export default defineConfig({
  plugins: [
    react({
      babel: {
        plugins: [
          // Remove prop-types in production for smaller bundle
          process.env.NODE_ENV === 'production' && ["babel-plugin-transform-remove-console", {
            "exclude": ["error", "warn"]
          }]
        ].filter(Boolean)
      }
    }),
    bundleOptimizerPlugin(),
    // Bundle analyzer - only in build mode
    process.env.ANALYZE && visualizer({
      filename: 'stats.html',
      open: true,
      gzipSize: true
    })
  ].filter(Boolean),
  
  // Enhanced alias configuration
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '~': path.resolve(__dirname, './public')
    }
  },

  // Environment variables configuration
  define: {
    __APP_VERSION__: JSON.stringify(process.env.npm_package_version || '1.0.0'),
    __BUILD_TIME__: JSON.stringify(new Date().toISOString()),
    global: 'globalThis',
  },

  // Enhanced server configuration for development
  server: {
    host: true,
    port: 5173,
    strictPort: false,
    hmr: {
      port: 24678,
      overlay: true
    },
    watch: {
      usePolling: false,
      ignored: ['**/node_modules/**', '**/dist/**']
    }
  },

  // Build optimization configuration
  build: {
    target: ['es2020', 'chrome79', 'firefox78', 'safari14', 'edge79'],
    minify: 'terser',
    cssMinify: 'lightningcss',
    sourcemap: false,
    assetsInlineLimit: 4096,
    
    rollupOptions: {
      output: {
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js',
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name.split('.');
          const extType = info[info.length - 1];
          if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(extType)) {
            return `assets/images/[name]-[hash].[ext]`;
          }
          if (/woff2?|eot|ttf|otf/i.test(extType)) {
            return `assets/fonts/[name]-[hash].[ext]`;
          }
          return `assets/[ext]/[name]-[hash].[ext]`;
        },
        
        // Advanced code splitting
        manualChunks: {
          // Core React libraries
          'react-vendor': ['react', 'react-dom', 'react-router-dom'],
          
          // UI and styling
          'ui-vendor': [
            'framer-motion', 
            'lucide-react', 
            '@heroicons/react',
            'react-icons'
          ],
          
          // Charts and data visualization
          'charts-vendor': [
            'chart.js', 
            'react-chartjs-2', 
            'recharts',
            'three',
            '@react-three/fiber',
            '@react-three/drei'
          ],
          
          // Utilities and helpers
          'utils-vendor': [
            'lodash', 
            'date-fns', 
            'axios',
            'js-cookie',
            'uuid'
          ],
          
          // Supabase and authentication
          'auth-vendor': [
            '@supabase/supabase-js',
            '@supabase/auth-ui-react',
            '@supabase/auth-ui-shared'
          ]
        }
      },
      
      // External dependencies that shouldn't be bundled
      external: [],
      
      // Input configuration
      input: {
        main: path.resolve(__dirname, 'index.html')
      }
    },
    
    // Performance optimization
    reportCompressedSize: true,
    chunkSizeWarningLimit: 1000
  },

  // Optimization settings
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      'framer-motion',
      '@supabase/supabase-js'
    ],
    exclude: [
      'three',
      '@react-three/fiber',
      '@react-three/drei'
    ]
  },

  // CSS processing
  css: {
    modules: false,
    preprocessorOptions: {
      scss: {
        additionalData: `@import "@/styles/variables.scss";`
      }
    },
    devSourcemap: true
  },

  // Enhanced preview configuration
  preview: {
    host: true,
    port: 4173,
    strictPort: false,
    open: true
  }
}); 