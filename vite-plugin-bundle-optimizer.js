// Enhanced Vite plugin for comprehensive bundle optimization
import { createFilter } from '@rollup/pluginutils';

/**
 * Advanced bundle optimizer plugin for Vite
 * - Removes console.log statements and dead code in production
 * - Optimizes imports for large libraries  
 * - Adds performance-critical optimizations
 * - Implements aggressive tree-shaking
 */
export default function bundleOptimizerPlugin(options = {}) {
  const defaultOptions = {
    jsFilter: /\.(js|jsx|ts|tsx)$/,
    exclude: /node_modules/,
    removeConsole: true,
    optimizeImports: true,
    deadCodeElimination: true,
    ...options
  };

  const jsFilter = createFilter(defaultOptions.jsFilter, defaultOptions.exclude);
  const isProduction = process.env.NODE_ENV === 'production';
  
  return {
    name: 'vite-plugin-bundle-optimizer',
    
    // Transform JavaScript files
    transform(code, id) {
      if (jsFilter(id)) {
        let transformedCode = code;
        
        // Remove console statements in production
        if (isProduction && defaultOptions.removeConsole) {
          // More comprehensive console removal
          transformedCode = transformedCode
            .replace(/console\.(log|info|debug|trace)\(.*?\);?/g, '')
            .replace(/console\.(log|info|debug|trace)\([\s\S]*?\);/g, '')
            // Keep error and warn
            .replace(/\/\*\s*console\s*\*\//g, '');
        }
        
        // Optimize imports for better tree-shaking
        if (defaultOptions.optimizeImports) {
          // Convert default imports to named imports where beneficial
          transformedCode = transformedCode
            .replace(/import\s+_\s+from\s+['"]lodash['"];?/g, "// Lodash optimized import")
            .replace(/import\s+\*\s+as\s+(\w+)\s+from\s+['"]lodash['"];?/g, "// Lodash optimized import");
        }
        
        // Dead code elimination patterns
        if (isProduction && defaultOptions.deadCodeElimination) {
          // Remove development-only code blocks
          transformedCode = transformedCode
            .replace(/if\s*\(\s*process\.env\.NODE_ENV\s*===\s*['"]development['"]\s*\)\s*\{[\s\S]*?\}/g, '')
            .replace(/if\s*\(\s*__DEV__\s*\)\s*\{[\s\S]*?\}/g, '')
            // Remove debug statements
            .replace(/debugger;?/g, '')
            // Remove empty blocks
            .replace(/\{\s*\}/g, '{}');
        }
        
        // Performance optimizations
        if (isProduction) {
          // Optimize React element creation
          transformedCode = transformedCode
            // Remove unnecessary whitespace in JSX
            .replace(/>\s+</g, '><')
            // Optimize string concatenation
            .replace(/\+\s*['"]\s*['"]\s*\+/g, '+');
        }
        
        return {
          code: transformedCode,
          map: null
        };
      }
    },
    
    // Generate bundle optimizations
    generateBundle(options, bundle) {
      if (isProduction) {
        // Remove empty chunks
        Object.keys(bundle).forEach(fileName => {
          const chunk = bundle[fileName];
          if (chunk.type === 'chunk' && chunk.code.trim().length < 100) {
            console.log(`Removing empty chunk: ${fileName}`);
            delete bundle[fileName];
          }
        });
      }
    },
    
    // Configure Rollup options for aggressive optimization
    config(config, { command }) {
      // Ensure optimized build configuration
      if (!config.build) config.build = {};
      if (!config.build.rollupOptions) config.build.rollupOptions = {};
      
      // Enable aggressive tree-shaking
      config.build.rollupOptions.treeshake = {
        moduleSideEffects: false,
        propertyReadSideEffects: false,
        tryCatchDeoptimization: false,
        // More aggressive tree-shaking options
        unknownGlobalSideEffects: false,
        correctVarValueBeforeDeclaration: false
      };
      
      // Optimize external dependencies
      if (command === 'build') {
        config.build.rollupOptions.external = config.build.rollupOptions.external || [];
        
        // Mark large libraries as external if they should be CDN loaded
        // (This is optional and depends on your CDN strategy)
        const potentialExternals = [
          // Uncomment if you want to load from CDN
          // 'react',
          // 'react-dom'
        ];
        
        config.build.rollupOptions.external.push(...potentialExternals);
      }
      
      return config;
    },
    
    // Build start hook for performance monitoring
    buildStart() {
      if (isProduction) {
        console.log('🚀 Starting optimized production build...');
      }
    },
    
    // Build end hook for performance reporting
    buildEnd() {
      if (isProduction) {
        console.log('✅ Bundle optimization completed');
      }
    }
  };
}
