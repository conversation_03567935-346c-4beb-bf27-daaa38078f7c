# Dental Article System Documentation

## Overview

The Dental Article System is designed to automatically fetch, process, and store dental articles from trusted RSS feeds. These articles are stored in the Supabase database and can be displayed on the Smilo website to provide valuable information to dental patients and professionals.

## System Components

1. **Article Fetching Service**: Retrieves articles from trusted RSS feeds, processes them, and stores them in the database.
2. **Database Schema**: Uses the `dental_articles` table in Supabase with proper row-level security (RLS) policies.
3. **Scheduled Updates**: Can be run manually or set up as a cron job to periodically update the article collection.
4. **Testing & Maintenance Scripts**: Various scripts to test, debug, and maintain the article system.

## Database Schema

The `dental_articles` table includes the following columns:

- `id`: Unique identifier (UUID)
- `created_at`: Timestamp when the record was created
- `title`: Article title
- `content`: Full article content
- `summary`: Short summary of the article
- `source`: Name of the source (e.g., "Dental Tribune")
- `source_type`: Type of source (e.g., "rss")
- `source_url`: URL of the source feed
- `link`: Direct link to the original article
- `tags`: Array of tags/categories
- `status`: Publication status (e.g., "published", "draft")
- `is_featured`: Boolean indicating featured status

## RLS Policies

The database is configured with the following RLS policies:

- Anonymous users can read all published articles
- Authenticated users with the appropriate role can manage articles

## Installation & Setup

1. Ensure the database schema is properly set up
2. Set the required environment variables:
   - `VITE_SUPABASE_URL`: Your Supabase project URL
   - `VITE_SUPABASE_ANON_KEY`: Your Supabase anonymous key

## Available Scripts

This system includes several scripts for different purposes:

- `npm run update-articles`: Run the scheduled article update process
- `npm run test-updated-article-service`: Test the updated article fetching service
- `npm run test-rss-feeds`: Test various RSS feeds to check their availability
- `npm run check-env`: Verify that environment variables are correctly loaded

## Trusted Sources

The system is configured to fetch articles from the following trusted sources:

1. Dental Tribune: `https://www.dental-tribune.com/feed/`
2. Dentistry Today: `https://www.dentistrytoday.com/feed/`
3. The Probe: `https://www.the-probe.co.uk/feed/`
4. Oral Health: `https://www.oralhealthgroup.com/feed/`
5. Dental Review News: `https://www.dental-review.co.uk/feed/`

## Automatic Updates

For automatic updates, you can set up a cron job to run the update script daily. For example:

```
# Run article update daily at 2:00 AM
0 2 * * * cd /path/to/project && npm run update-articles >> /path/to/logs/cron-log.txt 2>&1
```

## Troubleshooting

Common issues and their solutions:

1. **Missing Environment Variables**: Ensure your `.env` file contains the required Supabase credentials.
2. **Database Connection Issues**: Verify your Supabase project is active and the credentials are correct.
3. **RSS Feed Errors**: If feeds are not working, run the `test-rss-feeds` script to identify working alternatives.
4. **RLS Policy Issues**: Check that the appropriate RLS policies have been applied to the `dental_articles` table.

## Maintenance

The system automatically manages article retention:

- When new articles are added, the system will retain the 500 most recent articles.
- Older articles are automatically pruned to prevent database bloat.

## Developer Information

The main service implementation is located at:
`src/lib/services/articleFetchServiceUpdated.js`

For enhancements or modifications, check the existing code structure and follow the established patterns for consistency.

## Logs

Logs for the article update process are stored in the `logs` directory with filenames in the format `article-update-YYYY-MM-DD.log`. 