/*
  # Security Updates Migration

  1. Add search_path = public to all functions
    - Prevents SQL injection vulnerabilities
    - Ensures functions operate in the correct schema context
  
  2. Reduce OTP expiry time
    - Update OTP expiry from 900 seconds (15 minutes) to 300 seconds (5 minutes)
    - Improves security by reducing the window of opportunity for attacks
*/

-- Update function: update_conversation_last_message
CREATE OR REPLACE FUNCTION update_conversation_last_message()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE conversations
  SET last_message_at = NEW.created_at
  WHERE id = NEW.conversation_id;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = public;

-- Update function: update_last_read_at
CREATE OR REPLACE FUNCTION update_last_read_at()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE conversation_participants
  SET last_read_at = now()
  WHERE conversation_id = NEW.conversation_id
  AND user_id = auth.uid();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = public;

-- Update function: update_conversation_message_count
CREATE OR REPLACE FUNCTION update_conversation_message_count()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE conversation_logs
  SET message_count = message_count + 1
  WHERE id = (
    SELECT id FROM conversation_logs
    WHERE user_id = NEW.user_id
    AND ended_at IS NULL
    ORDER BY started_at DESC
    LIMIT 1
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = public;

-- Update function: update_session_last_active
CREATE OR REPLACE FUNCTION update_session_last_active()
RETURNS TRIGGER AS $$
BEGIN
  NEW.last_active = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = public;

-- Update function: update_modified_column
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = public;

-- Update function: update_article_cache
CREATE OR REPLACE FUNCTION update_article_cache()
RETURNS trigger AS $$
BEGIN
  -- Update the cache with latest published articles
  UPDATE article_cache
  SET 
    articles = (
      SELECT json_agg(
        json_build_object(
          'id', a.id,
          'title', a.title,
          'summary', a.summary,
          'source', a.source,
          'link', a.link,
          'tags', a.tags,
          'published_at', a.published_at
        )
      )
      FROM articles a
      WHERE a.status = 'published'
      ORDER BY a.published_at DESC
      LIMIT 50
    ),
    updated_at = NOW()
  WHERE id = 1;
  
  RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = public;

-- Update function: update_updated_at_column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = public;

-- Update function: handle_new_user
CREATE OR REPLACE FUNCTION handle_new_user() 
RETURNS trigger AS $$
BEGIN
  -- Create user profile
  INSERT INTO public.user_profiles (user_id, username, first_name, last_name)
  VALUES (
    NEW.id,
    COALESCE(NEW.raw_user_meta_data->>'username', 'user_' || substr(NEW.id::text, 1, 8)),
    NEW.raw_user_meta_data->>'first_name',
    NEW.raw_user_meta_data->>'last_name'
  );

  -- Create user preferences
  INSERT INTO public.user_preferences (user_id)
  VALUES (NEW.id);

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = public;

-- Create function: verify_admin (if it doesn't exist)
CREATE OR REPLACE FUNCTION verify_admin()
RETURNS BOOLEAN AS $$
DECLARE
  is_admin BOOLEAN;
BEGIN
  SELECT EXISTS (
    SELECT 1 FROM user_profiles
    WHERE user_id = auth.uid()
    AND role = 'admin'
  ) INTO is_admin;
  
  RETURN is_admin;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = public;

-- Create function: update_conversation_last_read (if it doesn't exist)
CREATE OR REPLACE FUNCTION update_conversation_last_read(conversation_id UUID)
RETURNS VOID AS $$
BEGIN
  UPDATE conversation_participants
  SET last_read_at = now()
  WHERE conversation_id = conversation_id
  AND user_id = auth.uid();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = public;
