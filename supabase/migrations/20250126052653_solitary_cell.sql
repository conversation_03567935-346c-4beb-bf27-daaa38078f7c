/*
  # Add Course Tracking Tables

  1. New Tables
    - `course_entries`
      - Stores individual course entries with grades and credits
    - `semester_records`
      - Groups courses by semester for GPA calculation
    - `gpa_calculations`
      - Stores calculated GPAs (overall, science, semester)

  2. Security
    - Enable RLS on all tables
    - Add policies for user access
*/

-- Create course_entries table
CREATE TABLE course_entries (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users NOT NULL,
  semester_id uuid,
  course_name text NOT NULL,
  course_type text NOT NULL,
  credits numeric(3,1) NOT NULL,
  grade text NOT NULL,
  grade_points numeric(3,2) NOT NULL,
  is_science_course boolean DEFAULT false,
  notes text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create semester_records table
CREATE TABLE semester_records (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users NOT NULL,
  semester text NOT NULL,
  year integer NOT NULL,
  total_credits numeric(4,1) NOT NULL DEFAULT 0,
  gpa numeric(3,2),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create gpa_calculations table
CREATE TABLE gpa_calculations (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users NOT NULL,
  overall_gpa numeric(3,2),
  science_gpa numeric(3,2),
  total_credits numeric(4,1),
  science_credits numeric(4,1),
  calculated_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE course_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE semester_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE gpa_calculations ENABLE ROW LEVEL SECURITY;

-- Create policies for course_entries
CREATE POLICY "Users can read own course entries"
  ON course_entries
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own course entries"
  ON course_entries
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own course entries"
  ON course_entries
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own course entries"
  ON course_entries
  FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);

-- Create policies for semester_records
CREATE POLICY "Users can read own semester records"
  ON semester_records
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own semester records"
  ON semester_records
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own semester records"
  ON semester_records
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

-- Create policies for gpa_calculations
CREATE POLICY "Users can read own GPA calculations"
  ON gpa_calculations
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own GPA calculations"
  ON gpa_calculations
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- Create indexes
CREATE INDEX course_entries_user_id_idx ON course_entries(user_id);
CREATE INDEX course_entries_semester_id_idx ON course_entries(semester_id);
CREATE INDEX semester_records_user_id_idx ON semester_records(user_id);
CREATE INDEX gpa_calculations_user_id_idx ON gpa_calculations(user_id);