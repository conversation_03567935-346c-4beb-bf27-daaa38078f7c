-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP FUNCTION IF EXISTS handle_new_user();

-- <PERSON>reate improved function to handle new user profiles
CREATE OR REPLACE FUNCTION handle_new_user() 
<PERSON><PERSON><PERSON><PERSON> trigger AS $$
DECLARE
  username_val text;
BEGIN
  -- Generate a unique username if not provided
  username_val := COALESCE(
    NEW.raw_user_meta_data->>'username',
    'user_' || substr(replace(NEW.id::text, '-', ''), 1, 8)
  );

  -- Create user profile with retry logic for username uniqueness
  FOR i IN 1..3 LOOP
    BEGIN
      INSERT INTO public.user_profiles (
        user_id,
        username,
        first_name,
        last_name,
        role
      ) VALUES (
        NEW.id,
        username_val,
        NEW.raw_user_meta_data->>'first_name',
        NEW.raw_user_meta_data->>'last_name',
        'user'
      );
      EXIT; -- Exit loop if insert succeeds
    EXCEPTION WHEN unique_violation THEN
      -- Append random string and try again
      username_val := username_val || '_' || substr(md5(random()::text), 1, 4);
      IF i = 3 THEN RAISE; END IF; -- Re-raise on final attempt
    END;
  END LOOP;

  -- Create user preferences
  INSERT INTO public.user_preferences (user_id)
  VALUES (NEW.id);

  -- Initialize predental progress
  INSERT INTO public.predental_progress (
    user_id,
    overall_gpa,
    science_gpa,
    total_credits,
    science_credits
  ) VALUES (
    NEW.id,
    0,
    0,
    0,
    0
  );

  RETURN NEW;
END;
$$ language plpgsql security definer;

-- Create trigger for new user handling
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user();