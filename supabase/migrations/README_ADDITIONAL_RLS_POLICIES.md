# Additional Row Level Security (RLS) Policies Documentation

This document outlines the Row Level Security (RLS) policies implemented for additional tables in the system.

## Overview

RLS policies have been implemented for the following tables:
- `reputable_sources`
- `chat_logs`
- `nda_notifications`
- `nda_acceptances`

Each table has policies that:
- Deny all access by default
- Allow SELECT only if a user is authenticated
- Allow INSERT/UPDATE/DELETE only if the record belongs to the current user (if applicable)
- Prevent unauthenticated access completely

## Helper Functions

The `is_admin()` helper function was created to simplify policy definitions:
- Checks if the current user has an admin role
- Used to grant administrative privileges across all tables

## Policies by Table

### 1. reputable_sources

| Operation | Policy Name | Description |
|-----------|-------------|-------------|
| SELECT | Authenticated users can read reputable sources | All authenticated users can view reputable sources |
| ALL | Admins can manage reputable sources | Only admins can create, update, or delete reputable sources |

### 2. chat_logs

| Operation | Policy Name | Description |
|-----------|-------------|-------------|
| SELECT | Users can read own chat logs | Users can only view their own chat logs |
| SELECT | Admins can read all chat logs | Admins can view all chat logs |
| INSERT | Users can insert own chat logs | Users can only create chat logs for themselves |
| UPDATE | Users can update own chat logs | Users can only update their own chat logs |
| DELETE | Users can delete own chat logs | Users can only delete their own chat logs |

### 3. nda_notifications

| Operation | Policy Name | Description |
|-----------|-------------|-------------|
| SELECT | Authenticated users can read nda notifications | Only admins can view NDA notifications |
| ALL | Admins can manage nda notifications | Only admins can create, update, or delete NDA notifications |

### 4. nda_acceptances

| Operation | Policy Name | Description |
|-----------|-------------|-------------|
| SELECT | Authenticated users can read own nda acceptances | Users can only view their own NDA acceptances (matched by email) |
| SELECT | Admins can read all nda acceptances | Admins can view all NDA acceptances |
| INSERT | Users can insert own nda acceptances | Users can only create NDA acceptances for themselves |
| ALL | Admins can manage all nda acceptances | Admins can create, update, or delete any NDA acceptance |

## Security Considerations

1. **Default Deny**: By enabling RLS on all tables without a public policy, access is denied by default to unauthenticated users.

2. **Principle of Least Privilege**: Users only have the permissions they need to perform their roles.

3. **Separation of Duties**: Different roles (admin, regular user) have different permissions.

4. **Defense in Depth**: Policies are enforced at the database level, providing an additional layer of security beyond application-level controls.

## Maintenance and Updates

When modifying the database schema or adding new tables, remember to:

1. Enable RLS on new tables
2. Define appropriate policies based on the table's purpose and ownership model
3. Test the policies to ensure they work as expected
4. Update this documentation to reflect the changes
