/*
  # Pre-dental tracking tables

  1. New Tables
    - `predental_courses`
      - `id` (uuid, primary key)
      - `user_id` (uuid, references auth.users)
      - `name` (text)
      - `type` (text)
      - `credits` (numeric)
      - `grade` (text)
      - `semester` (text)
      - `year` (integer)
      - `notes` (text)
      - `created_at` (timestamptz)
      - `updated_at` (timestamptz)

    - `predental_progress`
      - `id` (uuid, primary key)
      - `user_id` (uuid, references auth.users)
      - `school_id` (text)
      - `dat_scores` (jsonb)
      - `experience_hours` (jsonb)
      - `created_at` (timestamptz)
      - `updated_at` (timestamptz)

  2. Security
    - Enable RLS on all tables
    - Add policies for authenticated users
*/

-- Create predental_courses table
CREATE TABLE predental_courses (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users NOT NULL,
  name text NOT NULL,
  type text NOT NULL,
  credits numeric(3,1) NOT NULL,
  grade text NOT NULL,
  semester text NOT NULL,
  year integer NOT NULL,
  notes text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create predental_progress table
CREATE TABLE predental_progress (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users NOT NULL,
  school_id text NOT NULL,
  dat_scores jsonb DEFAULT '{}',
  experience_hours jsonb DEFAULT '{}',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE predental_courses ENABLE ROW LEVEL SECURITY;
ALTER TABLE predental_progress ENABLE ROW LEVEL SECURITY;

-- Create policies for predental_courses
CREATE POLICY "Users can read own courses"
  ON predental_courses
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own courses"
  ON predental_courses
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own courses"
  ON predental_courses
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete own courses"
  ON predental_courses
  FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);

-- Create policies for predental_progress
CREATE POLICY "Users can read own progress"
  ON predental_progress
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own progress"
  ON predental_progress
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own progress"
  ON predental_progress
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- Create indexes
CREATE INDEX predental_courses_user_id_idx ON predental_courses(user_id);
CREATE INDEX predental_progress_user_id_idx ON predental_progress(user_id);