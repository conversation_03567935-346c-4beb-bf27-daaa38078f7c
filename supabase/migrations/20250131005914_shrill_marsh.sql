/*
  # AI Chat System Schema

  1. New Tables
    - users_chat_history
      - Stores all chat messages and responses
      - Links to user profiles
      - Includes metadata and timestamps
    
    - image_analysis
      - Stores image analysis results
      - Links to chat history
      - Includes processing metadata
    
    - conversation_logs
      - Tracks conversation sessions
      - Stores metadata and analytics
      - Enables conversation context

  2. Security
    - Enable RLS on all tables
    - Add policies for user data access
    - Implement secure image handling

  3. Indexes
    - Optimize for frequent queries
    - Enable efficient filtering
*/

-- Create users_chat_history table
CREATE TABLE users_chat_history (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users NOT NULL,
  message_type text NOT NULL CHECK (message_type IN ('user', 'ai')),
  content text NOT NULL,
  metadata jsonb DEFAULT '{}',
  parent_message_id uuid REFERENCES users_chat_history(id),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create image_analysis table
CREATE TABLE image_analysis (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users NOT NULL,
  chat_message_id uuid REFERENCES users_chat_history(id),
  image_url text NOT NULL,
  analysis_results jsonb DEFAULT '{}',
  processing_status text NOT NULL DEFAULT 'pending' 
    CHECK (processing_status IN ('pending', 'processing', 'completed', 'failed')),
  error_details text,
  created_at timestamptz DEFAULT now(),
  completed_at timestamptz
);

-- Create conversation_logs table
CREATE TABLE conversation_logs (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users NOT NULL,
  session_id text NOT NULL,
  conversation_data jsonb DEFAULT '{}',
  metadata jsonb DEFAULT '{}',
  started_at timestamptz DEFAULT now(),
  ended_at timestamptz,
  message_count integer DEFAULT 0
);

-- Enable Row Level Security
ALTER TABLE users_chat_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE image_analysis ENABLE ROW LEVEL SECURITY;
ALTER TABLE conversation_logs ENABLE ROW LEVEL SECURITY;

-- Create policies for users_chat_history
CREATE POLICY "Users can read own chat history"
  ON users_chat_history
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own messages"
  ON users_chat_history
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- Create policies for image_analysis
CREATE POLICY "Users can read own image analysis"
  ON image_analysis
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own image analysis"
  ON image_analysis
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- Create policies for conversation_logs
CREATE POLICY "Users can read own conversation logs"
  ON conversation_logs
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own conversation logs"
  ON conversation_logs
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- Create indexes for performance
CREATE INDEX chat_history_user_id_idx ON users_chat_history(user_id);
CREATE INDEX chat_history_created_at_idx ON users_chat_history(created_at DESC);
CREATE INDEX chat_history_parent_msg_idx ON users_chat_history(parent_message_id);
CREATE INDEX image_analysis_user_id_idx ON image_analysis(user_id);
CREATE INDEX image_analysis_status_idx ON image_analysis(processing_status);
CREATE INDEX conversation_logs_user_id_idx ON conversation_logs(user_id);
CREATE INDEX conversation_logs_session_idx ON conversation_logs(session_id);

-- Create function to update message count
CREATE OR REPLACE FUNCTION update_conversation_message_count()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE conversation_logs
  SET message_count = message_count + 1
  WHERE id = (
    SELECT id FROM conversation_logs
    WHERE user_id = NEW.user_id
    AND ended_at IS NULL
    ORDER BY started_at DESC
    LIMIT 1
  );
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for message count
CREATE TRIGGER update_message_count_trigger
  AFTER INSERT ON users_chat_history
  FOR EACH ROW
  EXECUTE FUNCTION update_conversation_message_count();