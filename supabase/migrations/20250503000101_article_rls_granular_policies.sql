/*
  # Article System Granular RLS Policies

  This migration adds more granular Row Level Security (RLS) policies to the article system tables.
  Instead of using the ALL operation, we define separate policies for INSERT, UPDATE, and DELETE
  to provide more fine-grained control over permissions.
*/

-- Drop the previous "ALL" policies to replace them with granular ones
DROP POLICY IF EXISTS "Ad<PERSON> can manage article categories" ON article_categories;
DROP POLICY IF EXISTS "Authors can manage article category mappings" ON article_category_mapping;
DROP POLICY IF EXISTS "Ad<PERSON> can manage article category mappings" ON article_category_mapping;
DROP POLICY IF EXISTS "Ad<PERSON> can manage article metrics" ON article_metrics;
DROP POLICY IF EXISTS "Reviewers can manage their own reviews" ON article_reviews;
DROP POLICY IF EXISTS "Ad<PERSON> can manage all article reviews" ON article_reviews;
DROP POLICY IF EXISTS "Ad<PERSON> can manage article sources" ON article_sources;
DROP POLICY IF EXISTS "Authors can manage article tag mappings" ON article_tag_mapping;
DROP POLICY IF EXISTS "Ad<PERSON> can manage article tag mappings" ON article_tag_mapping;
DROP POLICY IF EXISTS "Ad<PERSON> can manage article tags" ON article_tags;

-- 1. article_categories granular policies
CREATE POLICY "Ad<PERSON> can insert article categories"
  ON article_categories
  FOR INSERT
  TO authenticated
  WITH CHECK (is_admin());

CREATE POLICY "Admins can update article categories"
  ON article_categories
  FOR UPDATE
  TO authenticated
  USING (is_admin())
  WITH CHECK (is_admin());

CREATE POLICY "Admins can delete article categories"
  ON article_categories
  FOR DELETE
  TO authenticated
  USING (is_admin());

-- 2. article_category_mapping granular policies
CREATE POLICY "Authors can insert article category mappings"
  ON article_category_mapping
  FOR INSERT
  TO authenticated
  WITH CHECK (is_article_author(article_id));

CREATE POLICY "Authors can update article category mappings"
  ON article_category_mapping
  FOR UPDATE
  TO authenticated
  USING (is_article_author(article_id))
  WITH CHECK (is_article_author(article_id));

CREATE POLICY "Authors can delete article category mappings"
  ON article_category_mapping
  FOR DELETE
  TO authenticated
  USING (is_article_author(article_id));

CREATE POLICY "Admins can insert article category mappings"
  ON article_category_mapping
  FOR INSERT
  TO authenticated
  WITH CHECK (is_admin());

CREATE POLICY "Admins can update article category mappings"
  ON article_category_mapping
  FOR UPDATE
  TO authenticated
  USING (is_admin())
  WITH CHECK (is_admin());

CREATE POLICY "Admins can delete article category mappings"
  ON article_category_mapping
  FOR DELETE
  TO authenticated
  USING (is_admin());

-- 3. article_metrics granular policies
CREATE POLICY "Admins can insert article metrics"
  ON article_metrics
  FOR INSERT
  TO authenticated
  WITH CHECK (is_admin());

CREATE POLICY "Admins can update article metrics"
  ON article_metrics
  FOR UPDATE
  TO authenticated
  USING (is_admin())
  WITH CHECK (is_admin());

CREATE POLICY "Admins can delete article metrics"
  ON article_metrics
  FOR DELETE
  TO authenticated
  USING (is_admin());

-- 4. article_reviews granular policies
CREATE POLICY "Reviewers can insert their own reviews"
  ON article_reviews
  FOR INSERT
  TO authenticated
  WITH CHECK (reviewer_id = auth.uid());

CREATE POLICY "Reviewers can update their own reviews"
  ON article_reviews
  FOR UPDATE
  TO authenticated
  USING (reviewer_id = auth.uid())
  WITH CHECK (reviewer_id = auth.uid());

CREATE POLICY "Reviewers can delete their own reviews"
  ON article_reviews
  FOR DELETE
  TO authenticated
  USING (reviewer_id = auth.uid());

CREATE POLICY "Admins can insert article reviews"
  ON article_reviews
  FOR INSERT
  TO authenticated
  WITH CHECK (is_admin());

CREATE POLICY "Admins can update article reviews"
  ON article_reviews
  FOR UPDATE
  TO authenticated
  USING (is_admin())
  WITH CHECK (is_admin());

CREATE POLICY "Admins can delete article reviews"
  ON article_reviews
  FOR DELETE
  TO authenticated
  USING (is_admin());

-- 5. article_sources granular policies
CREATE POLICY "Admins can insert article sources"
  ON article_sources
  FOR INSERT
  TO authenticated
  WITH CHECK (is_admin());

CREATE POLICY "Admins can update article sources"
  ON article_sources
  FOR UPDATE
  TO authenticated
  USING (is_admin())
  WITH CHECK (is_admin());

CREATE POLICY "Admins can delete article sources"
  ON article_sources
  FOR DELETE
  TO authenticated
  USING (is_admin());

-- 6. article_tag_mapping granular policies
CREATE POLICY "Authors can insert article tag mappings"
  ON article_tag_mapping
  FOR INSERT
  TO authenticated
  WITH CHECK (is_article_author(article_id));

CREATE POLICY "Authors can update article tag mappings"
  ON article_tag_mapping
  FOR UPDATE
  TO authenticated
  USING (is_article_author(article_id))
  WITH CHECK (is_article_author(article_id));

CREATE POLICY "Authors can delete article tag mappings"
  ON article_tag_mapping
  FOR DELETE
  TO authenticated
  USING (is_article_author(article_id));

CREATE POLICY "Admins can insert article tag mappings"
  ON article_tag_mapping
  FOR INSERT
  TO authenticated
  WITH CHECK (is_admin());

CREATE POLICY "Admins can update article tag mappings"
  ON article_tag_mapping
  FOR UPDATE
  TO authenticated
  USING (is_admin())
  WITH CHECK (is_admin());

CREATE POLICY "Admins can delete article tag mappings"
  ON article_tag_mapping
  FOR DELETE
  TO authenticated
  USING (is_admin());

-- 7. article_tags granular policies
CREATE POLICY "Admins can insert article tags"
  ON article_tags
  FOR INSERT
  TO authenticated
  WITH CHECK (is_admin());

CREATE POLICY "Admins can update article tags"
  ON article_tags
  FOR UPDATE
  TO authenticated
  USING (is_admin())
  WITH CHECK (is_admin());

CREATE POLICY "Admins can delete article tags"
  ON article_tags
  FOR DELETE
  TO authenticated
  USING (is_admin());
