-- Create chat folders table
CREATE TABLE chat_folders (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users NOT NULL,
  name text NOT NULL,
  description text,
  parent_id uuid REFERENCES chat_folders(id),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create chat_message_folders junction table
CREATE TABLE chat_message_folders (
  message_id uuid REFERENCES users_chat_history(id) ON DELETE CASCADE,
  folder_id uuid REFERENCES chat_folders(id) ON DELETE CASCADE,
  added_at timestamptz DEFAULT now(),
  PRIMARY KEY (message_id, folder_id)
);

-- Enable RLS
ALTER TABLE chat_folders ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_message_folders ENABLE ROW LEVEL SECURITY;

-- Create policies for chat_folders
CREATE POLICY "Users can read own folders"
  ON chat_folders
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create own folders"
  ON chat_folders
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own folders"
  ON chat_folders
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete own folders"
  ON chat_folders
  FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);

-- Create policies for chat_message_folders
CREATE POLICY "Users can manage own message folders"
  ON chat_message_folders
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM chat_folders
      WHERE id = folder_id
      AND user_id = auth.uid()
    )
  );

-- Create indexes
CREATE INDEX chat_folders_user_id_idx ON chat_folders(user_id);
CREATE INDEX chat_folders_parent_id_idx ON chat_folders(parent_id);
CREATE INDEX chat_message_folders_message_id_idx ON chat_message_folders(message_id);
CREATE INDEX chat_message_folders_folder_id_idx ON chat_message_folders(folder_id);

-- Create updated_at trigger function if it doesn't exist
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for chat_folders
CREATE TRIGGER update_chat_folders_updated_at
  BEFORE UPDATE ON chat_folders
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();