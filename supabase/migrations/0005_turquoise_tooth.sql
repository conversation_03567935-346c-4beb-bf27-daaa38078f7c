/*
  # System Settings Table and Policies

  1. New Tables
    - system_settings table for application configuration
  
  2. Security
    - Enable RLS on system_settings table
    - Add policies for reading and updating settings
*/

-- Create system_settings table
CREATE TABLE IF NOT EXISTS system_settings (
  id int PRIMARY KEY DEFAULT 1,
  maintenance_mode boolean DEFAULT false,
  updated_by uuid REFERENCES auth.users,
  updated_at timestamptz DEFAULT now(),
  CONSTRAINT single_row CHECK (id = 1)
);

-- Enable RLS
ALTER TABLE system_settings ENABLE ROW LEVEL SECURITY;

-- Add policies
CREATE POLICY "system_settings_read_policy"
  ON system_settings
  FOR SELECT
  TO authenticated
  USING (true);

-- Insert default settings
INSERT INTO system_settings (id, maintenance_mode, updated_at)
VALUES (1, false, now())
ON CONFLICT (id) DO NOTHING;