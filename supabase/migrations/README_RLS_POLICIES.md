# Row Level Security (RLS) Policies Documentation

This document outlines the Row Level Security (RLS) policies implemented for the article system tables.

## Overview

RLS policies have been implemented for the following tables:
- `article_categories`
- `article_category_mapping`
- `article_metrics`
- `article_reviews`
- `article_sources`
- `article_tag_mapping`
- `article_tags`

Each table has policies that:
- Allow authenticated users to SELECT rows
- Only allow INSERT/UPDATE/DELETE for users with appropriate ownership
- Deny access by default for unauthenticated users

## Helper Functions

Three helper functions were created to simplify policy definitions:

1. **is_admin()**
   - Checks if the current user has an admin role
   - Used to grant administrative privileges across all tables

2. **is_article_author(article_uuid UUID)**
   - Checks if the current user is the author of a specific article
   - Used for article-related junction tables (category and tag mappings)

3. **is_article_reviewer(article_uuid UUID)**
   - Checks if the current user is a reviewer for a specific article
   - Used for review-related permissions

## Policies by Table

### 1. article_categories

| Operation | Policy Name | Description |
|-----------|-------------|-------------|
| SELECT | Authenticated users can read article categories | All authenticated users can view categories |
| INSERT | Admins can insert article categories | Only admins can create new categories |
| UPDATE | Admins can update article categories | Only admins can modify existing categories |
| DELETE | Admins can delete article categories | Only admins can remove categories |

### 2. article_category_mapping

| Operation | Policy Name | Description |
|-----------|-------------|-------------|
| SELECT | Authenticated users can read article category mappings | All authenticated users can view mappings |
| INSERT | Authors can insert article category mappings | Article authors can add their articles to categories |
| INSERT | Admins can insert article category mappings | Admins can add any article to categories |
| UPDATE | Authors can update article category mappings | Article authors can modify category assignments |
| UPDATE | Admins can update article category mappings | Admins can modify any category assignment |
| DELETE | Authors can delete article category mappings | Article authors can remove their articles from categories |
| DELETE | Admins can delete article category mappings | Admins can remove any article from categories |

### 3. article_metrics

| Operation | Policy Name | Description |
|-----------|-------------|-------------|
| SELECT | Authenticated users can read article metrics | All authenticated users can view metrics |
| INSERT | Admins can insert article metrics | Only admins can create metrics records |
| UPDATE | Admins can update article metrics | Only admins can modify metrics |
| DELETE | Admins can delete article metrics | Only admins can delete metrics |

### 4. article_reviews

| Operation | Policy Name | Description |
|-----------|-------------|-------------|
| SELECT | Authenticated users can read article reviews | All authenticated users can view reviews |
| INSERT | Reviewers can insert their own reviews | Users can create reviews where they are the reviewer |
| INSERT | Admins can insert article reviews | Admins can create reviews for any reviewer |
| UPDATE | Reviewers can update their own reviews | Users can modify only their own reviews |
| UPDATE | Admins can update article reviews | Admins can modify any review |
| DELETE | Reviewers can delete their own reviews | Users can delete only their own reviews |
| DELETE | Admins can delete article reviews | Admins can delete any review |

### 5. article_sources

| Operation | Policy Name | Description |
|-----------|-------------|-------------|
| SELECT | Authenticated users can read article sources | All authenticated users can view sources |
| INSERT | Admins can insert article sources | Only admins can add new sources |
| UPDATE | Admins can update article sources | Only admins can modify sources |
| DELETE | Admins can delete article sources | Only admins can remove sources |

### 6. article_tag_mapping

| Operation | Policy Name | Description |
|-----------|-------------|-------------|
| SELECT | Authenticated users can read article tag mappings | All authenticated users can view tag mappings |
| INSERT | Authors can insert article tag mappings | Article authors can add tags to their articles |
| INSERT | Admins can insert article tag mappings | Admins can add tags to any article |
| UPDATE | Authors can update article tag mappings | Article authors can modify tags on their articles |
| UPDATE | Admins can update article tag mappings | Admins can modify tags on any article |
| DELETE | Authors can delete article tag mappings | Article authors can remove tags from their articles |
| DELETE | Admins can delete article tag mappings | Admins can remove tags from any article |

### 7. article_tags

| Operation | Policy Name | Description |
|-----------|-------------|-------------|
| SELECT | Authenticated users can read article tags | All authenticated users can view tags |
| INSERT | Admins can insert article tags | Only admins can create new tags |
| UPDATE | Admins can update article tags | Only admins can modify existing tags |
| DELETE | Admins can delete article tags | Only admins can remove tags |

## Security Considerations

1. **Default Deny**: By enabling RLS on all tables without a public policy, access is denied by default to unauthenticated users.

2. **Principle of Least Privilege**: Users only have the permissions they need to perform their roles.

3. **Separation of Duties**: Different roles (admin, author, reviewer) have different permissions.

4. **Defense in Depth**: Policies are enforced at the database level, providing an additional layer of security beyond application-level controls.

## Maintenance and Updates

When modifying the database schema or adding new tables related to the article system, remember to:

1. Enable RLS on new tables
2. Define appropriate policies based on the table's purpose and ownership model
3. Test the policies to ensure they work as expected
4. Update this documentation to reflect the changes
