/*
  # Dental Articles Schema Update

  1. Changes
    - Create dental articles tables with proper dependencies
    - Add full-text search capabilities
    - Set up RLS policies
    - Add indexes for performance
    - Handle existing tables gracefully

  2. Security
    - RLS enabled on all tables
    - Proper access policies
    - Secure default values
*/

-- Safely create tables with existence checks
DO $$ 
BEGIN
  -- Create dental_articles table if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM pg_tables WHERE tablename = 'dental_articles') THEN
    CREATE TABLE dental_articles (
      id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
      title text NOT NULL,
      content text NOT NULL,
      summary text NOT NULL,
      author_id uuid REFERENCES auth.users,
      status text NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'review', 'published', 'archived')),
      publication_date timestamptz,
      last_reviewed_at timestamptz,
      seo_metadata jsonb DEFAULT '{}',
      created_at timestamptz DEFAULT now(),
      updated_at timestamptz DEFAULT now(),
      search_vector tsvector GENERATED ALWAYS AS (
        setweight(to_tsvector('english', title), 'A') ||
        setweight(to_tsvector('english', summary), 'B') ||
        setweight(to_tsvector('english', content), 'C')
      ) STORED
    );
  END IF;

  -- Create article_categories table if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM pg_tables WHERE tablename = 'article_categories') THEN
    CREATE TABLE article_categories (
      id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
      name text NOT NULL UNIQUE,
      description text,
      parent_id uuid REFERENCES article_categories(id),
      created_at timestamptz DEFAULT now()
    );
  END IF;

  -- Create article_tags table if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM pg_tables WHERE tablename = 'article_tags') THEN
    CREATE TABLE article_tags (
      id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
      name text NOT NULL UNIQUE,
      description text,
      created_at timestamptz DEFAULT now()
    );
  END IF;

  -- Create article_reviews table if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM pg_tables WHERE tablename = 'article_reviews') THEN
    CREATE TABLE article_reviews (
      id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
      article_id uuid REFERENCES dental_articles ON DELETE CASCADE,
      reviewer_id uuid REFERENCES auth.users,
      status text NOT NULL CHECK (status IN ('pending', 'approved', 'rejected')),
      comments text,
      created_at timestamptz DEFAULT now(),
      updated_at timestamptz DEFAULT now()
    );
  END IF;

  -- Create junction tables if they don't exist
  IF NOT EXISTS (SELECT 1 FROM pg_tables WHERE tablename = 'article_category_mapping') THEN
    CREATE TABLE article_category_mapping (
      article_id uuid REFERENCES dental_articles ON DELETE CASCADE,
      category_id uuid REFERENCES article_categories ON DELETE CASCADE,
      PRIMARY KEY (article_id, category_id)
    );
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_tables WHERE tablename = 'article_tag_mapping') THEN
    CREATE TABLE article_tag_mapping (
      article_id uuid REFERENCES dental_articles ON DELETE CASCADE,
      tag_id uuid REFERENCES article_tags ON DELETE CASCADE,
      PRIMARY KEY (article_id, tag_id)
    );
  END IF;
END $$;

-- Enable Row Level Security
DO $$ 
BEGIN
  EXECUTE 'ALTER TABLE dental_articles ENABLE ROW LEVEL SECURITY';
  EXECUTE 'ALTER TABLE article_categories ENABLE ROW LEVEL SECURITY';
  EXECUTE 'ALTER TABLE article_tags ENABLE ROW LEVEL SECURITY';
  EXECUTE 'ALTER TABLE article_reviews ENABLE ROW LEVEL SECURITY';
  EXECUTE 'ALTER TABLE article_category_mapping ENABLE ROW LEVEL SECURITY';
  EXECUTE 'ALTER TABLE article_tag_mapping ENABLE ROW LEVEL SECURITY';
EXCEPTION WHEN undefined_table THEN NULL;
END $$;

-- Create policies if they don't exist
DO $$ 
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'dental_articles' AND policyname = 'Public can read published articles') THEN
    CREATE POLICY "Public can read published articles"
      ON dental_articles
      FOR SELECT
      USING (status = 'published');
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'dental_articles' AND policyname = 'Authors can CRUD their own articles') THEN
    CREATE POLICY "Authors can CRUD their own articles"
      ON dental_articles
      FOR ALL
      TO authenticated
      USING (author_id = auth.uid())
      WITH CHECK (author_id = auth.uid());
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'dental_articles' AND policyname = 'Reviewers can update articles under review') THEN
    CREATE POLICY "Reviewers can update articles under review"
      ON dental_articles
      FOR UPDATE
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM article_reviews
          WHERE article_id = dental_articles.id
          AND reviewer_id = auth.uid()
          AND status = 'pending'
        )
      );
  END IF;
END $$;

-- Create indexes if they don't exist
DO $$ 
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'dental_articles_search_idx') THEN
    CREATE INDEX dental_articles_search_idx ON dental_articles USING GIN (search_vector);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'dental_articles_status_idx') THEN
    CREATE INDEX dental_articles_status_idx ON dental_articles (status);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'dental_articles_publication_date_idx') THEN
    CREATE INDEX dental_articles_publication_date_idx ON dental_articles (publication_date DESC);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'article_category_mapping_article_idx') THEN
    CREATE INDEX article_category_mapping_article_idx ON article_category_mapping (article_id);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'article_tag_mapping_article_idx') THEN
    CREATE INDEX article_tag_mapping_article_idx ON article_tag_mapping (article_id);
  END IF;
END $$;

-- Create or replace timestamp update function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers if they don't exist
DO $$ 
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_dental_articles_updated_at') THEN
    CREATE TRIGGER update_dental_articles_updated_at
      BEFORE UPDATE ON dental_articles
      FOR EACH ROW
      EXECUTE FUNCTION update_updated_at_column();
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_article_reviews_updated_at') THEN
    CREATE TRIGGER update_article_reviews_updated_at
      BEFORE UPDATE ON article_reviews
      FOR EACH ROW
      EXECUTE FUNCTION update_updated_at_column();
  END IF;
END $$;

-- Insert initial data if tables are empty
DO $$ 
BEGIN
  -- Insert categories if none exist
  IF NOT EXISTS (SELECT 1 FROM article_categories) THEN
    INSERT INTO article_categories (name, description) VALUES
      ('Preventive Care', 'Articles about maintaining oral health and preventing dental issues'),
      ('Procedures', 'Detailed information about common dental procedures'),
      ('Oral Health', 'General oral health topics and best practices'),
      ('Research', 'Latest dental research and scientific findings'),
      ('Patient Education', 'Educational resources for dental patients')
    ON CONFLICT DO NOTHING;
  END IF;

  -- Insert tags if none exist
  IF NOT EXISTS (SELECT 1 FROM article_tags) THEN
    INSERT INTO article_tags (name, description) VALUES
      ('brushing', 'Proper brushing techniques and tips'),
      ('flossing', 'Flossing methods and importance'),
      ('cavities', 'Cavity prevention and treatment'),
      ('gum-health', 'Periodontal health information'),
      ('dental-hygiene', 'General dental hygiene practices')
    ON CONFLICT DO NOTHING;
  END IF;
END $$;