-- Create dental_articles table
CREATE TABLE IF NOT EXISTS public.dental_articles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    summary TEXT,
    source TEXT NOT NULL,
    source_type TEXT NOT NULL,
    source_url TEXT,
    link TEXT NOT NULL,
    pub_date TIMESTAMP WITH TIME ZONE,
    authors TEXT,
    tags TEXT[],
    image_url TEXT,
    is_featured BOOLEAN DEFAULT false,
    inserted_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    status TEXT DEFAULT 'published'
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_dental_articles_source ON public.dental_articles(source);
CREATE INDEX IF NOT EXISTS idx_dental_articles_source_type ON public.dental_articles(source_type);
CREATE INDEX IF NOT EXISTS idx_dental_articles_pub_date ON public.dental_articles(pub_date);
CREATE INDEX IF NOT EXISTS idx_dental_articles_inserted_at ON public.dental_articles(inserted_at);
CREATE INDEX IF NOT EXISTS idx_dental_articles_status ON public.dental_articles(status);

-- Setup RLS (Row Level Security)
ALTER TABLE public.dental_articles ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Allow public read access" 
    ON public.dental_articles 
    FOR SELECT 
    USING (status = 'published');

CREATE POLICY "Allow admins to manage all dental articles" 
    ON public.dental_articles 
    USING (
        auth.uid() IN (
            SELECT auth.uid() 
            FROM auth.users 
            WHERE auth.users.is_admin = true
        )
    );

-- Add function to update updated_at timestamp automatically
CREATE OR REPLACE FUNCTION update_dental_articles_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add trigger to automatically update updated_at
CREATE TRIGGER update_dental_articles_timestamp
BEFORE UPDATE ON public.dental_articles
FOR EACH ROW
EXECUTE FUNCTION update_dental_articles_timestamp();

-- Create system_logs table if it doesn't exist (for tracking metrics)
CREATE TABLE IF NOT EXISTS public.system_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    component TEXT NOT NULL,
    level TEXT NOT NULL,
    message TEXT NOT NULL,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add index for system_logs
CREATE INDEX IF NOT EXISTS idx_system_logs_component ON public.system_logs(component);
CREATE INDEX IF NOT EXISTS idx_system_logs_level ON public.system_logs(level);
CREATE INDEX IF NOT EXISTS idx_system_logs_created_at ON public.system_logs(created_at);

-- Enable RLS for system_logs
ALTER TABLE public.system_logs ENABLE ROW LEVEL SECURITY;

-- Create policy for system_logs
CREATE POLICY "Allow admins to view system logs" 
    ON public.system_logs 
    FOR SELECT 
    USING (
        auth.uid() IN (
            SELECT auth.uid() 
            FROM auth.users 
            WHERE auth.users.is_admin = true
        )
    );

CREATE POLICY "Allow service role to insert logs" 
    ON public.system_logs 
    FOR INSERT 
    WITH CHECK (true);

-- Add comment to dental_articles table for documentation
COMMENT ON TABLE public.dental_articles IS 'Dental research articles and papers from reputable sources'; 