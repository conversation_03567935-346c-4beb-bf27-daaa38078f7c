/*
  # Search Logs Schema Update

  1. Changes
    - Add indexes for performance optimization
    - Add timestamp index for efficient querying
    - Add composite index for user searches

  2. Security
    - Maintain existing RLS policies
    - Add additional policy for analytics access
*/

-- Add indexes to existing search_logs table
CREATE INDEX IF NOT EXISTS search_logs_created_at_idx 
  ON search_logs (created_at DESC);

CREATE INDEX IF NOT EXISTS search_logs_user_searches_idx 
  ON search_logs (user_id, created_at DESC);

CREATE INDEX IF NOT EXISTS search_logs_location_idx 
  ON search_logs (latitude, longitude)
  WHERE latitude IS NOT NULL AND longitude IS NOT NULL;

-- Add analytics access policy
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'public' 
    AND tablename = 'search_logs' 
    AND policyname = 'Analytics users can read all logs'
  ) THEN
    CREATE POLICY "Analytics users can read all logs"
      ON search_logs
      FOR SELECT
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM user_profiles
          WHERE user_id = auth.uid()
          AND role IN ('admin', 'analyst')
        )
      );
  END IF;
END $$;