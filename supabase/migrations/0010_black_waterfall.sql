/*
  # Article System Setup

  1. New Tables
    - `articles` - Stores dental articles
    - `article_sources` - Stores trusted article sources
  
  2. Functions
    - `update_article_cache()` - Updates the article cache
    
  3. Triggers
    - Auto-update cache when articles change
*/

-- Create articles table
CREATE TABLE IF NOT EXISTS articles (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  title text NOT NULL,
  summary text NOT NULL,
  content text NOT NULL,
  source text NOT NULL,
  link text NOT NULL,
  tags text[] DEFAULT '{}',
  status text NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
  published_at timestamptz,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create article sources table
CREATE TABLE IF NOT EXISTS article_sources (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  url text NOT NULL,
  is_trusted boolean DEFAULT false,
  created_at timestamptz DEFAULT now()
);

-- Create function to update article cache
CREATE OR REPLACE FUNCTION update_article_cache()
<PERSON><PERSON><PERSON><PERSON> trigger AS $$
BEGIN
  -- Update the cache with latest published articles
  UPDATE article_cache
  SET 
    articles = (
      SELECT json_agg(
        json_build_object(
          'id', a.id,
          'title', a.title,
          'summary', a.summary,
          'source', a.source,
          'link', a.link,
          'tags', a.tags,
          'published_at', a.published_at
        )
      )
      FROM articles a
      WHERE a.status = 'published'
      ORDER BY a.published_at DESC
      LIMIT 50
    ),
    updated_at = NOW()
  WHERE id = 1;
  
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update cache when articles change
CREATE TRIGGER update_article_cache_trigger
AFTER INSERT OR UPDATE OR DELETE ON articles
FOR EACH STATEMENT
EXECUTE FUNCTION update_article_cache();

-- Enable RLS
ALTER TABLE articles ENABLE ROW LEVEL SECURITY;
ALTER TABLE article_sources ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Anyone can read published articles"
  ON articles
  FOR SELECT
  USING (status = 'published');

CREATE POLICY "Only authenticated users can read trusted sources"
  ON article_sources
  FOR SELECT
  TO authenticated
  USING (true);

-- Insert some initial trusted sources
INSERT INTO article_sources (name, url, is_trusted) VALUES
  ('American Dental Association', 'https://www.ada.org', true),
  ('Journal of Clinical Periodontology', 'https://onlinelibrary.wiley.com/journal/1600051x', true),
  ('Journal of Dental Research', 'https://journals.sagepub.com/home/<USER>', true)
ON CONFLICT DO NOTHING;