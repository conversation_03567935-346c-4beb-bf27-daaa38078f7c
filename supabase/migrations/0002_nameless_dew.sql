-- Create secure storage for dental images
CREATE POLICY "Images are private"
ON storage.objects
FOR ALL
TO authenticated
USING (auth.uid() = (storage.foldername(name))[1]::uuid);

-- Create symptom analysis table
CREATE TABLE symptom_analyses (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users NOT NULL,
  symptoms text NOT NULL,
  image_path text,
  created_at timestamptz DEFAULT now(),
  analysis_result text
);

-- Enable RLS
ALTER TABLE symptom_analyses ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can read own analyses"
  ON symptom_analyses
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create own analyses"
  ON symptom_analyses
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);