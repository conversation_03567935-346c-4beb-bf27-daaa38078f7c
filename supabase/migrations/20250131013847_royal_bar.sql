-- Drop any existing duplicate tables
DO $$ 
BEGIN
  DROP TABLE IF EXISTS user_profiles CASCADE;
  DROP TABLE IF EXISTS user_preferences CASCADE;
  DROP TABLE IF EXISTS user_activity CASCADE;
  DROP TABLE IF EXISTS user_agreements CASCADE;
  DROP TABLE IF EXISTS users_chat_history CASCADE;
  DROP TABLE IF EXISTS image_analysis CASCADE;
  DROP TABLE IF EXISTS conversation_logs CASCADE;
EXCEPTION WHEN undefined_object THEN NULL;
END $$;

-- Create user_profiles table
CREATE TABLE user_profiles (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users NOT NULL,
  username text UNIQUE NOT NULL,
  first_name text,
  last_name text,
  role text NOT NULL DEFAULT 'user',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create user_preferences table
CREATE TABLE user_preferences (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users NOT NULL,
  notification_settings jsonb DEFAULT '{}',
  privacy_settings jsonb DEFAULT '{}',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create user_activity table
CREATE TABLE user_activity (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users NOT NULL,
  action_type text NOT NULL,
  action_data jsonb DEFAULT '{}',
  ip_address text,
  user_agent text,
  created_at timestamptz DEFAULT now()
);

-- Create user_agreements table
CREATE TABLE user_agreements (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users NOT NULL,
  agreement_type text NOT NULL,
  agreement_version text NOT NULL,
  accepted_at timestamptz DEFAULT now(),
  ip_address text,
  user_agent text
);

-- Create users_chat_history table
CREATE TABLE users_chat_history (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users NOT NULL,
  message_type text NOT NULL CHECK (message_type IN ('user', 'ai')),
  content text NOT NULL,
  metadata jsonb DEFAULT '{}',
  parent_message_id uuid REFERENCES users_chat_history(id),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create image_analysis table
CREATE TABLE image_analysis (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users NOT NULL,
  chat_message_id uuid REFERENCES users_chat_history(id),
  image_url text NOT NULL,
  analysis_results jsonb DEFAULT '{}',
  processing_status text NOT NULL DEFAULT 'pending' 
    CHECK (processing_status IN ('pending', 'processing', 'completed', 'failed')),
  error_details text,
  created_at timestamptz DEFAULT now(),
  completed_at timestamptz
);

-- Create conversation_logs table
CREATE TABLE conversation_logs (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users NOT NULL,
  session_id text NOT NULL,
  conversation_data jsonb DEFAULT '{}',
  metadata jsonb DEFAULT '{}',
  started_at timestamptz DEFAULT now(),
  ended_at timestamptz,
  message_count integer DEFAULT 0
);

-- Enable RLS
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_activity ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_agreements ENABLE ROW LEVEL SECURITY;
ALTER TABLE users_chat_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE image_analysis ENABLE ROW LEVEL SECURITY;
ALTER TABLE conversation_logs ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can read own profile"
  ON user_profiles FOR SELECT TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can update own profile"
  ON user_profiles FOR UPDATE TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can read own preferences"
  ON user_preferences FOR SELECT TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can update own preferences"
  ON user_preferences FOR UPDATE TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can read own activity"
  ON user_activity FOR SELECT TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own activity"
  ON user_activity FOR INSERT TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can read own agreements"
  ON user_agreements FOR SELECT TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own agreements"
  ON user_agreements FOR INSERT TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can read own chat history"
  ON users_chat_history FOR SELECT TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own messages"
  ON users_chat_history FOR INSERT TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can read own image analysis"
  ON image_analysis FOR SELECT TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own image analysis"
  ON image_analysis FOR INSERT TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can read own conversation logs"
  ON conversation_logs FOR SELECT TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own conversation logs"
  ON conversation_logs FOR INSERT TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- Create indexes
CREATE INDEX user_profiles_username_idx ON user_profiles(username);
CREATE INDEX user_profiles_user_id_idx ON user_profiles(user_id);
CREATE INDEX user_preferences_user_id_idx ON user_preferences(user_id);
CREATE INDEX user_activity_user_id_idx ON user_activity(user_id);
CREATE INDEX user_agreements_user_id_idx ON user_agreements(user_id);
CREATE INDEX chat_history_user_id_idx ON users_chat_history(user_id);
CREATE INDEX chat_history_created_at_idx ON users_chat_history(created_at DESC);
CREATE INDEX chat_history_parent_msg_idx ON users_chat_history(parent_message_id);
CREATE INDEX image_analysis_user_id_idx ON image_analysis(user_id);
CREATE INDEX image_analysis_status_idx ON image_analysis(processing_status);
CREATE INDEX conversation_logs_user_id_idx ON conversation_logs(user_id);
CREATE INDEX conversation_logs_session_idx ON conversation_logs(session_id);