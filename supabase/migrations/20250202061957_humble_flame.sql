/*
  # Dental Resources Database Schema

  1. New Tables
    - dental_articles
      - Core table for dental articles and resources
      - Full-text search enabled
      - Status tracking for content workflow
    - article_categories
      - Categories for organizing content
    - article_tags
      - Tagging system for better content discovery
    - article_sources
      - Tracking and validating content sources
    - article_reviews
      - Peer review system for content quality
    - article_metrics
      - Usage and engagement tracking

  2. Security
    - RLS enabled on all tables
    - Public read access for published content
    - Restricted write access for authorized users
*/

-- Create dental_articles table with full-text search
CREATE TABLE dental_articles (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  title text NOT NULL,
  content text NOT NULL,
  summary text NOT NULL,
  author_id uuid REFERENCES auth.users,
  status text NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'review', 'published', 'archived')),
  publication_date timestamptz,
  last_reviewed_at timestamptz,
  seo_metadata jsonb DEFAULT '{}',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  search_vector tsvector GENERATED ALWAYS AS (
    setweight(to_tsvector('english', title), 'A') ||
    setweight(to_tsvector('english', summary), 'B') ||
    setweight(to_tsvector('english', content), 'C')
  ) STORED
);

-- Create article_categories table
CREATE TABLE article_categories (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL UNIQUE,
  description text,
  parent_id uuid REFERENCES article_categories(id),
  created_at timestamptz DEFAULT now()
);

-- Create article_tags table
CREATE TABLE article_tags (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL UNIQUE,
  description text,
  created_at timestamptz DEFAULT now()
);

-- Create article_sources table
CREATE TABLE article_sources (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  url text NOT NULL,
  credibility_score numeric(3,2) CHECK (credibility_score >= 0 AND credibility_score <= 5),
  verified boolean DEFAULT false,
  created_at timestamptz DEFAULT now(),
  last_verified_at timestamptz
);

-- Create article_reviews table
CREATE TABLE article_reviews (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  article_id uuid REFERENCES dental_articles ON DELETE CASCADE,
  reviewer_id uuid REFERENCES auth.users,
  status text NOT NULL CHECK (status IN ('pending', 'approved', 'rejected')),
  comments text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create article_metrics table
CREATE TABLE article_metrics (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  article_id uuid REFERENCES dental_articles ON DELETE CASCADE,
  views integer DEFAULT 0,
  shares integer DEFAULT 0,
  likes integer DEFAULT 0,
  avg_time_spent interval,
  last_updated timestamptz DEFAULT now()
);

-- Create junction tables
CREATE TABLE article_category_mapping (
  article_id uuid REFERENCES dental_articles ON DELETE CASCADE,
  category_id uuid REFERENCES article_categories ON DELETE CASCADE,
  PRIMARY KEY (article_id, category_id)
);

CREATE TABLE article_tag_mapping (
  article_id uuid REFERENCES dental_articles ON DELETE CASCADE,
  tag_id uuid REFERENCES article_tags ON DELETE CASCADE,
  PRIMARY KEY (article_id, tag_id)
);

-- Enable Row Level Security
ALTER TABLE dental_articles ENABLE ROW LEVEL SECURITY;
ALTER TABLE article_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE article_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE article_sources ENABLE ROW LEVEL SECURITY;
ALTER TABLE article_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE article_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE article_category_mapping ENABLE ROW LEVEL SECURITY;
ALTER TABLE article_tag_mapping ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Public can read published articles"
  ON dental_articles
  FOR SELECT
  USING (status = 'published');

CREATE POLICY "Authors can CRUD their own articles"
  ON dental_articles
  FOR ALL
  TO authenticated
  USING (author_id = auth.uid())
  WITH CHECK (author_id = auth.uid());

CREATE POLICY "Reviewers can update articles under review"
  ON dental_articles
  FOR UPDATE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM article_reviews
      WHERE article_id = dental_articles.id
      AND reviewer_id = auth.uid()
      AND status = 'pending'
    )
  );

-- Create indexes
CREATE INDEX dental_articles_search_idx ON dental_articles USING GIN (search_vector);
CREATE INDEX dental_articles_status_idx ON dental_articles (status);
CREATE INDEX dental_articles_publication_date_idx ON dental_articles (publication_date DESC);
CREATE INDEX article_category_mapping_article_idx ON article_category_mapping (article_id);
CREATE INDEX article_tag_mapping_article_idx ON article_tag_mapping (article_id);
CREATE INDEX article_metrics_article_idx ON article_metrics (article_id);

-- Create function to update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers
CREATE TRIGGER update_dental_articles_updated_at
  BEFORE UPDATE ON dental_articles
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_article_reviews_updated_at
  BEFORE UPDATE ON article_reviews
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Insert initial categories
INSERT INTO article_categories (name, description) VALUES
  ('Preventive Care', 'Articles about maintaining oral health and preventing dental issues'),
  ('Procedures', 'Detailed information about common dental procedures'),
  ('Oral Health', 'General oral health topics and best practices'),
  ('Research', 'Latest dental research and scientific findings'),
  ('Patient Education', 'Educational resources for dental patients')
ON CONFLICT DO NOTHING;

-- Insert initial tags
INSERT INTO article_tags (name, description) VALUES
  ('brushing', 'Proper brushing techniques and tips'),
  ('flossing', 'Flossing methods and importance'),
  ('cavities', 'Cavity prevention and treatment'),
  ('gum-health', 'Periodontal health information'),
  ('dental-hygiene', 'General dental hygiene practices')
ON CONFLICT DO NOTHING;

-- Insert trusted sources
INSERT INTO article_sources (name, url, credibility_score, verified) VALUES
  ('American Dental Association', 'https://www.ada.org', 5.00, true),
  ('Journal of Dental Research', 'https://journals.sagepub.com/home/<USER>', 5.00, true),
  ('National Institute of Dental Research', 'https://www.nidcr.nih.gov', 5.00, true)
ON CONFLICT DO NOTHING;