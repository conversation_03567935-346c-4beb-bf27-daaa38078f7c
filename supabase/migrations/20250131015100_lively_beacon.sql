-- Create function to handle new user profiles
CREATE OR REPLACE FUNCTION handle_new_user() 
<PERSON><PERSON><PERSON><PERSON> trigger AS $$
BEGIN
  -- Create user profile
  INSERT INTO public.user_profiles (user_id, username, first_name, last_name)
  VALUES (
    NEW.id,
    COALESCE(NEW.raw_user_meta_data->>'username', 'user_' || substr(NEW.id::text, 1, 8)),
    NEW.raw_user_meta_data->>'first_name',
    NEW.raw_user_meta_data->>'last_name'
  );

  -- Create user preferences
  INSERT INTO public.user_preferences (user_id)
  VALUES (NEW.id);

  RETURN NEW;
END;
$$ language plpgsql security definer;

-- Create trigger for new user handling
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Add insert policies for initial user data
CREATE POLICY "Handle initial profile creation"
  ON public.user_profiles
  FOR INSERT
  TO service_role
  WITH CHECK (true);

CREATE POLICY "Handle initial preferences creation"
  ON public.user_preferences
  FOR INSERT
  TO service_role
  WITH CHECK (true);

-- Add policy for profile creation during signup
CREATE POLICY "Enable insert for authentication service"
  ON public.user_profiles
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);