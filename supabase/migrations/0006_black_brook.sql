/*
  # Create search logs table

  1. New Tables
    - `search_logs`
      - `id` (uuid, primary key)
      - `latitude` (numeric, nullable)
      - `longitude` (numeric, nullable)
      - `status` (text) - 'granted' or 'denied'
      - `created_at` (timestamp)
      - `user_id` (uuid, nullable) - Reference to auth.users
  2. Security
    - Enable R<PERSON> on `search_logs` table
    - Add policy for authenticated users to read their own logs
    - Add policy for inserting logs (both authenticated and anonymous users)
*/

CREATE TABLE search_logs (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  latitude numeric(10,6),
  longitude numeric(10,6),
  status text NOT NULL CHECK (status IN ('granted', 'denied')),
  created_at timestamptz DEFAULT now(),
  user_id uuid REFERENCES auth.users
);

ALTER TABLE search_logs ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read own search logs"
  ON search_logs
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Anyone can insert search logs"
  ON search_logs
  FOR INSERT
  TO anon, authenticated
  WIT<PERSON> CHECK (true);