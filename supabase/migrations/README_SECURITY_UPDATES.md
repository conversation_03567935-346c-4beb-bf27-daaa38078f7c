# Security Updates Documentation

This document outlines the security updates implemented in the database and authentication system.

## 1. SQL Function Security Updates

All SQL functions have been updated to include the `search_path = public` parameter to prevent SQL injection vulnerabilities. This ensures that functions operate in the correct schema context and cannot be manipulated to access unauthorized schemas.

### Updated Functions:

- `update_conversation_last_message()`
- `update_last_read_at()`
- `update_conversation_message_count()`
- `update_session_last_active()`
- `update_modified_column()`
- `update_article_cache()`
- `update_updated_at_column()`
- `handle_new_user()`
- `verify_admin()`
- `update_conversation_last_read()`

## 2. OTP Expiry Reduction

The One-Time Password (OTP) expiry time has been reduced from 900 seconds (15 minutes) to 300 seconds (5 minutes) to improve security by reducing the window of opportunity for attacks.

### Updated Settings:

- `email_otp_exp`: 300 seconds
- `sms_otp_exp`: 300 seconds
- `phone_otp_exp`: 300 seconds

## 3. Leaked Password Protection

Checks against known leaked password databases have been enabled to prevent users from using compromised passwords.

### Updated Settings:

- `enable_weak_passwords`: false
- `enable_leaked_passwords_check`: true

## 4. Rate Limiting Improvements

Rate limiting has been enhanced to prevent brute force attacks.

### Updated Settings:

- `max_otp_attempts`: 3
- `max_confirm_attempts`: 3
- `confirm_email_rate_limit_count`: 3
- `confirm_email_rate_limit_period`: 60 seconds
- `confirm_phone_rate_limit_count`: 3
- `confirm_phone_rate_limit_period`: 60 seconds

## Implementation Details

These changes were implemented through two migration files:

1. `20250503000000_security_updates.sql` - Updates SQL functions with `search_path = public`
2. `20250503000001_auth_security_updates.sql` - Updates Auth settings for OTP expiry and leaked password protection

Additionally, a centralized OTP configuration file (`src/lib/auth/otp.js`) was created to manage OTP settings across the application.

## Verification

After applying these migrations, you should verify that:

1. All database functions work correctly
2. OTP verification works with the new expiry time
3. Users cannot register with known leaked passwords
4. Rate limiting is working as expected

## Future Recommendations

1. Regularly update the leaked password database
2. Consider implementing additional security measures such as:
   - Multi-factor authentication
   - IP-based rate limiting
   - Suspicious activity monitoring
   - Regular security audits
