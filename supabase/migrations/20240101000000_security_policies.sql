-- Security Policies for Smilo Dental
-- This file sets up Row Level Security (RLS) policies for the database

-- Set search_path to public for security
SET search_path = public;

-- Enable Row Level Security on all tables
ALTER TABLE IF EXISTS articles ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS dental_schools ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS chat_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS reputable_sources ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS nda_notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS nda_acceptances ENABLE ROW LEVEL SECURITY;

-- Create a function to check if a user is authenticated
CREATE OR REPLACE FUNCTION auth.is_authenticated() 
RETURNS BOOLEAN AS $$
BEGIN
  RETURN (auth.uid() IS NOT NULL);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to check if a user is the owner of a record
CREATE OR REPLACE FUNCTION auth.is_owner(record_user_id UUID) 
RETURNS BOOLEAN AS $$
BEGIN
  RETURN (auth.uid() = record_user_id);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to check if a user has a specific role
CREATE OR REPLACE FUNCTION auth.has_role(required_role TEXT) 
RETURNS BOOLEAN AS $$
DECLARE
  current_role TEXT;
BEGIN
  SELECT role INTO current_role FROM auth.users WHERE id = auth.uid();
  RETURN (current_role = required_role);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Articles Table Policies
-- Allow anyone to read published articles
CREATE POLICY "Anyone can read published articles"
  ON articles
  FOR SELECT
  USING (is_published = TRUE);

-- Allow authenticated users to create articles
CREATE POLICY "Authenticated users can create articles"
  ON articles
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.is_authenticated());

-- Allow users to update their own articles
CREATE POLICY "Users can update their own articles"
  ON articles
  FOR UPDATE
  TO authenticated
  USING (auth.is_owner(user_id))
  WITH CHECK (auth.is_owner(user_id));

-- Allow users to delete their own articles
CREATE POLICY "Users can delete their own articles"
  ON articles
  FOR DELETE
  TO authenticated
  USING (auth.is_owner(user_id));

-- Dental Schools Table Policies
-- Allow anyone to read dental school information
CREATE POLICY "Anyone can read dental school information"
  ON dental_schools
  FOR SELECT
  TO anon, authenticated
  USING (TRUE);

-- Allow only admins to modify dental school information
CREATE POLICY "Only admins can modify dental school information"
  ON dental_schools
  FOR INSERT, UPDATE, DELETE
  TO authenticated
  USING (auth.has_role('admin'));

-- Chat Logs Table Policies
-- Allow users to read their own chat logs
CREATE POLICY "Users can read their own chat logs"
  ON chat_logs
  FOR SELECT
  TO authenticated
  USING (auth.is_owner(user_id));

-- Allow authenticated users to create chat logs
CREATE POLICY "Authenticated users can create chat logs"
  ON chat_logs
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.is_authenticated() AND auth.is_owner(user_id));

-- Allow users to update their own chat logs
CREATE POLICY "Users can update their own chat logs"
  ON chat_logs
  FOR UPDATE
  TO authenticated
  USING (auth.is_owner(user_id))
  WITH CHECK (auth.is_owner(user_id));

-- Allow users to delete their own chat logs
CREATE POLICY "Users can delete their own chat logs"
  ON chat_logs
  FOR DELETE
  TO authenticated
  USING (auth.is_owner(user_id));

-- User Profiles Table Policies
-- Allow users to read their own profile
CREATE POLICY "Users can read their own profile"
  ON user_profiles
  FOR SELECT
  TO authenticated
  USING (auth.is_owner(id));

-- Allow authenticated users to create their own profile
CREATE POLICY "Authenticated users can create their own profile"
  ON user_profiles
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.is_authenticated() AND auth.is_owner(id));

-- Allow users to update their own profile
CREATE POLICY "Users can update their own profile"
  ON user_profiles
  FOR UPDATE
  TO authenticated
  USING (auth.is_owner(id))
  WITH CHECK (auth.is_owner(id));

-- Reputable Sources Table Policies
-- Allow anyone to read reputable sources
CREATE POLICY "Anyone can read reputable sources"
  ON reputable_sources
  FOR SELECT
  TO anon, authenticated
  USING (TRUE);

-- Allow only admins to modify reputable sources
CREATE POLICY "Only admins can modify reputable sources"
  ON reputable_sources
  FOR INSERT, UPDATE, DELETE
  TO authenticated
  USING (auth.has_role('admin'));

-- NDA Notifications Table Policies
-- Allow users to read their own NDA notifications
CREATE POLICY "Users can read their own NDA notifications"
  ON nda_notifications
  FOR SELECT
  TO authenticated
  USING (auth.is_owner(user_id));

-- Allow authenticated users to create NDA notifications
CREATE POLICY "Authenticated users can create NDA notifications"
  ON nda_notifications
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.is_authenticated());

-- NDA Acceptances Table Policies
-- Allow users to read their own NDA acceptances
CREATE POLICY "Users can read their own NDA acceptances"
  ON nda_acceptances
  FOR SELECT
  TO authenticated
  USING (auth.is_owner(user_id));

-- Allow authenticated users to create NDA acceptances
CREATE POLICY "Authenticated users can create NDA acceptances"
  ON nda_acceptances
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.is_authenticated() AND auth.is_owner(user_id));

-- Create indexes for better security and performance
CREATE INDEX IF NOT EXISTS idx_articles_user_id ON articles(user_id);
CREATE INDEX IF NOT EXISTS idx_chat_logs_user_id ON chat_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_nda_notifications_user_id ON nda_notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_nda_acceptances_user_id ON nda_acceptances(user_id);
