/*
  # Article Metrics and Monitoring System

  1. New Tables
    - `article_metrics` for tracking article fetch statistics
    - `system_health` for monitoring system status
    - `error_logs` for tracking system errors

  2. Security
    - Enable RLS on all tables
    - Add policies for admin and service role access

  3. Performance
    - Add indexes for efficient querying
*/

-- Check if tables exist before creating
DO $$ 
BEGIN
  -- Create article metrics table if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM pg_tables WHERE tablename = 'article_metrics') THEN
    CREATE TABLE article_metrics (
      id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
      total_requests integer DEFAULT 0,
      successful_requests integer DEFAULT 0,
      failed_requests integer DEFAULT 0,
      error_count integer DEFAULT 0,
      created_at timestamptz DEFAULT now()
    );

    -- Enable RLS
    ALTER TABLE article_metrics ENABLE ROW LEVEL SECURITY;

    -- Create policies
    CREATE POLICY "Ad<PERSON> can read metrics"
      ON article_metrics
      FOR SELECT
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM user_profiles
          WHERE user_id = auth.uid()
          AND role = 'admin'
        )
      );

    CREATE POLICY "Only service role can insert metrics"
      ON article_metrics
      FOR INSERT
      TO service_role
      WITH CHECK (true);
  END IF;

  -- Create system health table if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM pg_tables WHERE tablename = 'system_health') THEN
    CREATE TABLE system_health (
      id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
      component text NOT NULL,
      health_score numeric(3,2),
      status text NOT NULL,
      details jsonb DEFAULT '{}'::jsonb,
      created_at timestamptz DEFAULT now()
    );

    -- Enable RLS
    ALTER TABLE system_health ENABLE ROW LEVEL SECURITY;

    -- Create policies
    CREATE POLICY "Admins can read system health"
      ON system_health
      FOR SELECT
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM user_profiles
          WHERE user_id = auth.uid()
          AND role = 'admin'
        )
      );

    CREATE POLICY "Only service role can update health"
      ON system_health
      FOR ALL
      TO service_role
      USING (true)
      WITH CHECK (true);
  END IF;

  -- Create error logs table if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM pg_tables WHERE tablename = 'error_logs') THEN
    CREATE TABLE error_logs (
      id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
      component text NOT NULL,
      message text NOT NULL,
      error_details jsonb DEFAULT '{}'::jsonb,
      stack_trace text,
      created_at timestamptz DEFAULT now()
    );

    -- Enable RLS
    ALTER TABLE error_logs ENABLE ROW LEVEL SECURITY;

    -- Create policies
    CREATE POLICY "Admins can read error logs"
      ON error_logs
      FOR SELECT
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM user_profiles
          WHERE user_id = auth.uid()
          AND role = 'admin'
        )
      );

    CREATE POLICY "Only service role can insert errors"
      ON error_logs
      FOR INSERT
      TO service_role
      WITH CHECK (true);
  END IF;
END $$;

-- Create indexes if they don't exist
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'article_metrics_created_at_idx') THEN
    CREATE INDEX article_metrics_created_at_idx ON article_metrics(created_at DESC);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'system_health_component_idx') THEN
    CREATE INDEX system_health_component_idx ON system_health(component, created_at DESC);
  END IF;

  IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'error_logs_component_idx') THEN
    CREATE INDEX error_logs_component_idx ON error_logs(component, created_at DESC);
  END IF;
END $$;