/*
  # Additional RLS Policies

  This migration adds Row Level Security (RLS) policies to the following tables:
  - public.reputable_sources
  - public.chat_logs
  - public.nda_notifications
  - public.nda_acceptances

  Each table has policies that:
  - Deny all access by default
  - Allow SELECT only if a user is authenticated
  - Allow INSERT/UPDATE/DELETE only if the record belongs to the current user (if applicable)
  - Prevent unauthenticated access completely
*/

-- Helper function to check if a user is an admin
CREATE OR REPLACE FUNCTION is_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM user_profiles
    WHERE user_id = auth.uid()
    AND role = 'admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = public;

-- Enable RLS on all tables
ALTER TABLE IF EXISTS reputable_sources ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS chat_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS nda_notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS nda_acceptances ENABLE ROW LEVEL SECURITY;

-- Drop any existing policies to avoid conflicts
DROP POLICY IF EXISTS "Authenticated users can read reputable sources" ON reputable_sources;
DROP POLICY IF EXISTS "Admins can manage reputable sources" ON reputable_sources;

DROP POLICY IF EXISTS "Users can read own chat logs" ON chat_logs;
DROP POLICY IF EXISTS "Users can insert own chat logs" ON chat_logs;
DROP POLICY IF EXISTS "Users can update own chat logs" ON chat_logs;
DROP POLICY IF EXISTS "Users can delete own chat logs" ON chat_logs;
DROP POLICY IF EXISTS "Admins can read all chat logs" ON chat_logs;

DROP POLICY IF EXISTS "Authenticated users can read nda notifications" ON nda_notifications;
DROP POLICY IF EXISTS "Admins can manage nda notifications" ON nda_notifications;

DROP POLICY IF EXISTS "Authenticated users can read own nda acceptances" ON nda_acceptances;
DROP POLICY IF EXISTS "Users can insert own nda acceptances" ON nda_acceptances;
DROP POLICY IF EXISTS "Admins can read all nda acceptances" ON nda_acceptances;
DROP POLICY IF EXISTS "Admins can manage all nda acceptances" ON nda_acceptances;

-- 1. reputable_sources policies
-- This table appears to be similar to article_sources, likely containing trusted information sources
CREATE POLICY "Authenticated users can read reputable sources"
  ON reputable_sources
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Admins can manage reputable sources"
  ON reputable_sources
  FOR ALL
  TO authenticated
  USING (is_admin())
  WITH CHECK (is_admin());

-- 2. chat_logs policies
-- This table likely stores chat logs with a user_id field
CREATE POLICY "Users can read own chat logs"
  ON chat_logs
  FOR SELECT
  TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "Users can insert own chat logs"
  ON chat_logs
  FOR INSERT
  TO authenticated
  WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update own chat logs"
  ON chat_logs
  FOR UPDATE
  TO authenticated
  USING (user_id = auth.uid())
  WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can delete own chat logs"
  ON chat_logs
  FOR DELETE
  TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "Admins can read all chat logs"
  ON chat_logs
  FOR SELECT
  TO authenticated
  USING (is_admin());

-- 3. nda_notifications policies
-- This table stores notifications related to NDA acceptances
CREATE POLICY "Authenticated users can read nda notifications"
  ON nda_notifications
  FOR SELECT
  TO authenticated
  USING (is_admin());

CREATE POLICY "Admins can manage nda notifications"
  ON nda_notifications
  FOR ALL
  TO authenticated
  USING (is_admin())
  WITH CHECK (is_admin());

-- 4. nda_acceptances policies
-- This table stores NDA acceptance records
CREATE POLICY "Authenticated users can read own nda acceptances"
  ON nda_acceptances
  FOR SELECT
  TO authenticated
  USING (
    -- If the table has an email field that can be matched to the user's email
    (email = (SELECT email FROM auth.users WHERE id = auth.uid()))
    OR is_admin()
  );

CREATE POLICY "Users can insert own nda acceptances"
  ON nda_acceptances
  FOR INSERT
  TO authenticated
  WITH CHECK (
    -- Users can only insert their own acceptances
    email = (SELECT email FROM auth.users WHERE id = auth.uid())
  );

CREATE POLICY "Admins can read all nda acceptances"
  ON nda_acceptances
  FOR SELECT
  TO authenticated
  USING (is_admin());

CREATE POLICY "Admins can manage all nda acceptances"
  ON nda_acceptances
  FOR ALL
  TO authenticated
  USING (is_admin())
  WITH CHECK (is_admin());
