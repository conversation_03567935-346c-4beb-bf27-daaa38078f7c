/*
  # Messaging System Schema

  1. New Tables
    - conversations
      - id (uuid, primary key)
      - type (text, individual/group)
      - created_at (timestamptz)
      - metadata (jsonb)
      - encrypted_key (text)
      - max_participants (integer)
    
    - conversation_participants
      - conversation_id (uuid)
      - user_id (uuid)
      - role (text)
      - joined_at (timestamptz)
      - last_read_at (timestamptz)
      
    - messages
      - id (uuid)
      - conversation_id (uuid)
      - sender_id (uuid)
      - encrypted_content (text)
      - content_type (text)
      - metadata (jsonb)
      - created_at (timestamptz)
      - edited_at (timestamptz)
      - deleted_at (timestamptz)
      
    - message_reactions
      - message_id (uuid)
      - user_id (uuid)
      - reaction (text)
      - created_at (timestamptz)

  2. Security
    - Enable RLS on all tables
    - Add policies for secure access control
    - Implement encryption for messages
*/

-- Create conversations table
CREATE TABLE conversations (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  type text NOT NULL CHECK (type IN ('individual', 'group')),
  created_at timestamptz DEFAULT now(),
  metadata jsonb DEFAULT '{}',
  encrypted_key text,
  max_participants integer DEFAULT 100,
  last_message_at timestamptz DEFAULT now()
);

-- Create conversation_participants table
CREATE TABLE conversation_participants (
  conversation_id uuid REFERENCES conversations ON DELETE CASCADE,
  user_id uuid REFERENCES auth.users ON DELETE CASCADE,
  role text NOT NULL DEFAULT 'member' CHECK (role IN ('admin', 'member')),
  joined_at timestamptz DEFAULT now(),
  last_read_at timestamptz DEFAULT now(),
  PRIMARY KEY (conversation_id, user_id)
);

-- Create messages table
CREATE TABLE messages (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  conversation_id uuid REFERENCES conversations ON DELETE CASCADE,
  sender_id uuid REFERENCES auth.users ON DELETE SET NULL,
  encrypted_content text NOT NULL,
  content_type text NOT NULL DEFAULT 'text' CHECK (content_type IN ('text', 'file', 'image')),
  metadata jsonb DEFAULT '{}',
  created_at timestamptz DEFAULT now(),
  edited_at timestamptz,
  deleted_at timestamptz,
  parent_id uuid REFERENCES messages(id)
);

-- Create message_reactions table
CREATE TABLE message_reactions (
  message_id uuid REFERENCES messages ON DELETE CASCADE,
  user_id uuid REFERENCES auth.users ON DELETE CASCADE,
  reaction text NOT NULL,
  created_at timestamptz DEFAULT now(),
  PRIMARY KEY (message_id, user_id, reaction)
);

-- Enable Row Level Security
ALTER TABLE conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE conversation_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE message_reactions ENABLE ROW LEVEL SECURITY;

-- Create policies for conversations
CREATE POLICY "Users can view conversations they're part of"
  ON conversations
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM conversation_participants
      WHERE conversation_id = id
      AND user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create conversations"
  ON conversations
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

-- Create policies for conversation_participants
CREATE POLICY "Users can view participants of their conversations"
  ON conversation_participants
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM conversation_participants cp
      WHERE cp.conversation_id = conversation_id
      AND cp.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can join conversations they're invited to"
  ON conversation_participants
  FOR INSERT
  TO authenticated
  WITH CHECK (
    auth.uid() = user_id AND
    EXISTS (
      SELECT 1 FROM conversations c
      WHERE c.id = conversation_id
      AND (
        c.type = 'group' OR
        (
          c.type = 'individual' AND
          (
            SELECT count(*) FROM conversation_participants
            WHERE conversation_id = c.id
          ) < 2
        )
      )
    )
  );

-- Create policies for messages
CREATE POLICY "Users can view messages in their conversations"
  ON messages
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM conversation_participants
      WHERE conversation_id = messages.conversation_id
      AND user_id = auth.uid()
    )
  );

CREATE POLICY "Users can send messages to their conversations"
  ON messages
  FOR INSERT
  TO authenticated
  WITH CHECK (
    sender_id = auth.uid() AND
    EXISTS (
      SELECT 1 FROM conversation_participants
      WHERE conversation_id = messages.conversation_id
      AND user_id = auth.uid()
    )
  );

CREATE POLICY "Users can edit their own messages"
  ON messages
  FOR UPDATE
  TO authenticated
  USING (
    sender_id = auth.uid() AND
    (EXTRACT(EPOCH FROM now() - created_at) < 300) -- 5 minutes
  );

-- Create policies for message_reactions
CREATE POLICY "Users can view reactions in their conversations"
  ON message_reactions
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM messages m
      JOIN conversation_participants cp ON cp.conversation_id = m.conversation_id
      WHERE m.id = message_id
      AND cp.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can react to messages in their conversations"
  ON message_reactions
  FOR INSERT
  TO authenticated
  WITH CHECK (
    user_id = auth.uid() AND
    EXISTS (
      SELECT 1 FROM messages m
      JOIN conversation_participants cp ON cp.conversation_id = m.conversation_id
      WHERE m.id = message_id
      AND cp.user_id = auth.uid()
    )
  );

-- Create indexes for performance
CREATE INDEX messages_conversation_id_idx ON messages(conversation_id);
CREATE INDEX messages_sender_id_idx ON messages(sender_id);
CREATE INDEX messages_created_at_idx ON messages(created_at DESC);
CREATE INDEX conversation_participants_user_id_idx ON conversation_participants(user_id);
CREATE INDEX message_reactions_message_id_idx ON message_reactions(message_id);

-- Create function to update last_message_at
CREATE OR REPLACE FUNCTION update_conversation_last_message()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE conversations
  SET last_message_at = NEW.created_at
  WHERE id = NEW.conversation_id;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for last_message_at
CREATE TRIGGER update_conversation_last_message_trigger
  AFTER INSERT ON messages
  FOR EACH ROW
  EXECUTE FUNCTION update_conversation_last_message();

-- Create function to update last_read_at
CREATE OR REPLACE FUNCTION update_last_read_at()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE conversation_participants
  SET last_read_at = now()
  WHERE conversation_id = NEW.conversation_id
  AND user_id = auth.uid();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for last_read_at
CREATE TRIGGER update_last_read_at_trigger
  AFTER INSERT OR UPDATE ON messages
  FOR EACH ROW
  EXECUTE FUNCTION update_last_read_at();