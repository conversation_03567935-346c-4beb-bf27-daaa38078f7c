/*
  # Article System RLS Policies

  This migration adds Row Level Security (RLS) policies to the article system tables:
  - article_categories
  - article_category_mapping
  - article_metrics
  - article_reviews
  - article_sources
  - article_tag_mapping
  - article_tags

  Each table has policies that:
  - Allow authenticated users to SELECT rows
  - Only allow INSERT/UPDATE/DELETE for users with appropriate ownership
  - Deny access by default for unauthenticated users
*/

-- Helper function to check if a user is an admin
CREATE OR REPLACE FUNCTION is_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM user_profiles
    WHERE user_id = auth.uid()
    AND role = 'admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = public;

-- Helper function to check if a user is the author of an article
CREATE OR REPLACE FUNCTION is_article_author(article_uuid UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM dental_articles
    WHERE id = article_uuid
    AND author_id = auth.uid()
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = public;

-- Helper function to check if a user is a reviewer of an article
CREATE OR REPLACE FUNCTION is_article_reviewer(article_uuid UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM article_reviews
    WHERE article_id = article_uuid
    AND reviewer_id = auth.uid()
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = public;

-- Enable RLS on all tables
ALTER TABLE article_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE article_category_mapping ENABLE ROW LEVEL SECURITY;
ALTER TABLE article_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE article_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE article_sources ENABLE ROW LEVEL SECURITY;
ALTER TABLE article_tag_mapping ENABLE ROW LEVEL SECURITY;
ALTER TABLE article_tags ENABLE ROW LEVEL SECURITY;

-- Drop any existing policies to avoid conflicts
DROP POLICY IF EXISTS "Authenticated users can read article categories" ON article_categories;
DROP POLICY IF EXISTS "Admins can manage article categories" ON article_categories;

DROP POLICY IF EXISTS "Authenticated users can read article category mappings" ON article_category_mapping;
DROP POLICY IF EXISTS "Authors can manage article category mappings" ON article_category_mapping;
DROP POLICY IF EXISTS "Admins can manage article category mappings" ON article_category_mapping;

DROP POLICY IF EXISTS "Authenticated users can read article metrics" ON article_metrics;
DROP POLICY IF EXISTS "Admins can manage article metrics" ON article_metrics;

DROP POLICY IF EXISTS "Authenticated users can read article reviews" ON article_reviews;
DROP POLICY IF EXISTS "Reviewers can manage their own reviews" ON article_reviews;
DROP POLICY IF EXISTS "Admins can manage all article reviews" ON article_reviews;

DROP POLICY IF EXISTS "Authenticated users can read article sources" ON article_sources;
DROP POLICY IF EXISTS "Admins can manage article sources" ON article_sources;

DROP POLICY IF EXISTS "Authenticated users can read article tag mappings" ON article_tag_mapping;
DROP POLICY IF EXISTS "Authors can manage article tag mappings" ON article_tag_mapping;
DROP POLICY IF EXISTS "Admins can manage article tag mappings" ON article_tag_mapping;

DROP POLICY IF EXISTS "Authenticated users can read article tags" ON article_tags;
DROP POLICY IF EXISTS "Admins can manage article tags" ON article_tags;

-- 1. article_categories policies
CREATE POLICY "Authenticated users can read article categories"
  ON article_categories
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Admins can manage article categories"
  ON article_categories
  FOR ALL
  TO authenticated
  USING (is_admin())
  WITH CHECK (is_admin());

-- 2. article_category_mapping policies
CREATE POLICY "Authenticated users can read article category mappings"
  ON article_category_mapping
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Authors can manage article category mappings"
  ON article_category_mapping
  FOR ALL
  TO authenticated
  USING (is_article_author(article_id))
  WITH CHECK (is_article_author(article_id));

CREATE POLICY "Admins can manage article category mappings"
  ON article_category_mapping
  FOR ALL
  TO authenticated
  USING (is_admin())
  WITH CHECK (is_admin());

-- 3. article_metrics policies
CREATE POLICY "Authenticated users can read article metrics"
  ON article_metrics
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Admins can manage article metrics"
  ON article_metrics
  FOR ALL
  TO authenticated
  USING (is_admin())
  WITH CHECK (is_admin());

-- 4. article_reviews policies
CREATE POLICY "Authenticated users can read article reviews"
  ON article_reviews
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Reviewers can manage their own reviews"
  ON article_reviews
  FOR ALL
  TO authenticated
  USING (reviewer_id = auth.uid())
  WITH CHECK (reviewer_id = auth.uid());

CREATE POLICY "Admins can manage all article reviews"
  ON article_reviews
  FOR ALL
  TO authenticated
  USING (is_admin())
  WITH CHECK (is_admin());

-- 5. article_sources policies
CREATE POLICY "Authenticated users can read article sources"
  ON article_sources
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Admins can manage article sources"
  ON article_sources
  FOR ALL
  TO authenticated
  USING (is_admin())
  WITH CHECK (is_admin());

-- 6. article_tag_mapping policies
CREATE POLICY "Authenticated users can read article tag mappings"
  ON article_tag_mapping
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Authors can manage article tag mappings"
  ON article_tag_mapping
  FOR ALL
  TO authenticated
  USING (is_article_author(article_id))
  WITH CHECK (is_article_author(article_id));

CREATE POLICY "Admins can manage article tag mappings"
  ON article_tag_mapping
  FOR ALL
  TO authenticated
  USING (is_admin())
  WITH CHECK (is_admin());

-- 7. article_tags policies
CREATE POLICY "Authenticated users can read article tags"
  ON article_tags
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Admins can manage article tags"
  ON article_tags
  FOR ALL
  TO authenticated
  USING (is_admin())
  WITH CHECK (is_admin());
