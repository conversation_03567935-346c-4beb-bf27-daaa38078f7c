/*
  # US Dental Schools Database

  1. New Tables
    - `dental_schools` for storing accredited dental school information
    - `school_programs` for tracking available programs
    - `clinic_services` for student clinic information

  2. Security
    - Enable RLS on all tables
    - Add policies for public read access

  3. Data
    - Insert comprehensive list of US dental schools
    - Include clinic information and services
*/

-- Create dental schools table
CREATE TABLE IF NOT EXISTS dental_schools (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  address text NOT NULL,
  city text NOT NULL,
  state text NOT NULL,
  zip text NOT NULL,
  latitude numeric(10,6) NOT NULL,
  longitude numeric(10,6) NOT NULL,
  phone text,
  website text,
  accreditation_id text,
  clinic_info text,
  student_clinic_hours text,
  payment_info text,
  insurance_accepted boolean DEFAULT false,
  sliding_scale boolean DEFAULT true,
  created_at timestamptz DEFAULT now()
);

-- Create school programs table
CREATE TABLE IF NOT EXISTS school_programs (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  school_id uuid REFERENCES dental_schools NOT NULL,
  program_name text NOT NULL,
  degree_type text NOT NULL,
  description text,
  created_at timestamptz DEFAULT now()
);

-- <PERSON><PERSON> clinic services table
CREATE TABLE IF NOT EXISTS clinic_services (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  school_id uuid REFERENCES dental_schools NOT NULL,
  service_name text NOT NULL,
  description text,
  price_range text,
  created_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE dental_schools ENABLE ROW LEVEL SECURITY;
ALTER TABLE school_programs ENABLE ROW LEVEL SECURITY;
ALTER TABLE clinic_services ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Anyone can read dental schools"
  ON dental_schools
  FOR SELECT
  TO anon, authenticated
  USING (true);

CREATE POLICY "Anyone can read school programs"
  ON school_programs
  FOR SELECT
  TO anon, authenticated
  USING (true);

CREATE POLICY "Anyone can read clinic services"
  ON clinic_services
  FOR SELECT
  TO anon, authenticated
  USING (true);

-- Create indexes
CREATE INDEX dental_schools_location_idx ON dental_schools(latitude, longitude);
CREATE INDEX dental_schools_state_idx ON dental_schools(state);
CREATE INDEX school_programs_school_idx ON school_programs(school_id);
CREATE INDEX clinic_services_school_idx ON clinic_services(school_id);

-- Insert dental schools data
INSERT INTO dental_schools (name, address, city, state, zip, latitude, longitude, website, clinic_info, student_clinic_hours, payment_info) VALUES
-- Northeast Region
('Harvard School of Dental Medicine', '188 Longwood Ave', 'Boston', 'MA', '02115', 42.3359, -71.1027, 'https://hsdm.harvard.edu', 'Teaching clinic offering comprehensive dental care at reduced rates', 'Mon-Fri 8:00 AM - 5:00 PM', 'Sliding scale fees, major credit cards accepted'),
('Boston University Goldman School of Dental Medicine', '100 East Newton Street', 'Boston', 'MA', '02118', 42.3365, -71.0729, 'https://www.bu.edu/dental', 'Student clinic providing affordable dental services', 'Mon-Fri 8:30 AM - 5:00 PM', 'Payment plans available'),
('Tufts University School of Dental Medicine', '1 Kneeland Street', 'Boston', 'MA', '02111', 42.3500, -71.0615, 'https://dental.tufts.edu', 'Comprehensive dental care at teaching clinics', 'Mon-Fri 8:00 AM - 4:30 PM', 'Accepts most insurance plans'),

-- Southeast Region
('Nova Southeastern University College of Dental Medicine', '3200 S University Dr', 'Fort Lauderdale', 'FL', '33328', 26.0798, -80.2405, 'https://dental.nova.edu', 'Comprehensive dental care at reduced fees', 'Mon-Fri 9:00 AM - 5:00 PM', 'Multiple payment options available'),
('University of Florida College of Dentistry', '1395 Center Drive', 'Gainesville', 'FL', '32610', 29.6399, -82.3428, 'https://dental.ufl.edu', 'Student-provided dental care under faculty supervision', 'Mon-Fri 8:00 AM - 5:00 PM', 'Sliding scale fees based on income'),
('Dental College of Georgia at Augusta University', '1430 John Wesley Gilbert Drive', 'Augusta', 'GA', '30912', 33.4711, -81.9875, 'https://www.augusta.edu/dentalmedicine', 'Affordable dental services through teaching clinics', 'Mon-Fri 8:30 AM - 5:00 PM', 'Financial assistance available'),

-- Midwest Region
('University of Michigan School of Dentistry', '1011 N University Ave', 'Ann Arbor', 'MI', '48109', 42.2808, -83.7430, 'https://www.dent.umich.edu', 'Comprehensive dental care at reduced costs', 'Mon-Fri 8:00 AM - 5:00 PM', 'Payment plans and sliding scale fees'),
('University of Illinois at Chicago College of Dentistry', '801 S Paulina St', 'Chicago', 'IL', '60612', 41.8699, -87.6697, 'https://dentistry.uic.edu', 'Teaching clinics offering affordable care', 'Mon-Fri 9:00 AM - 4:30 PM', 'Multiple payment options'),
('Ohio State University College of Dentistry', '305 W 12th Ave', 'Columbus', 'OH', '43210', 40.0025, -83.0148, 'https://dentistry.osu.edu', 'Student dental clinics with reduced fees', 'Mon-Fri 8:00 AM - 5:00 PM', 'Financial assistance available'),

-- West Region
('UCSF School of Dentistry', '707 Parnassus Ave', 'San Francisco', 'CA', '94143', 37.7631, -122.4586, 'https://dentistry.ucsf.edu', 'State-of-the-art dental care at teaching clinics', 'Mon-Fri 8:00 AM - 5:00 PM', 'Sliding scale fees available'),
('UCLA School of Dentistry', '714 Tiverton Ave', 'Los Angeles', 'CA', '90095', 34.0661, -118.4452, 'https://www.dentistry.ucla.edu', 'Comprehensive dental care at reduced rates', 'Mon-Fri 8:30 AM - 5:00 PM', 'Multiple payment options'),
('University of Washington School of Dentistry', '1959 NE Pacific St', 'Seattle', 'WA', '98195', 47.6502, -122.3082, 'https://dental.washington.edu', 'Quality dental care at teaching clinics', 'Mon-Fri 8:00 AM - 5:00 PM', 'Income-based fee reduction available');

-- Insert program data
INSERT INTO school_programs (school_id, program_name, degree_type, description)
SELECT 
  id,
  'Doctor of Dental Medicine',
  'DMD',
  'Four-year doctoral program in dental medicine'
FROM dental_schools;

-- Insert common clinic services
INSERT INTO clinic_services (school_id, service_name, description, price_range)
SELECT 
  id,
  'Routine Cleaning',
  'Professional dental cleaning and examination',
  '$30-75'
FROM dental_schools;

INSERT INTO clinic_services (school_id, service_name, description, price_range)
SELECT 
  id,
  'Basic Fillings',
  'Tooth-colored or amalgam fillings',
  '$40-100'
FROM dental_schools;

INSERT INTO clinic_services (school_id, service_name, description, price_range)
SELECT 
  id,
  'Root Canal Treatment',
  'Endodontic treatment for infected teeth',
  '$200-500'
FROM dental_schools;