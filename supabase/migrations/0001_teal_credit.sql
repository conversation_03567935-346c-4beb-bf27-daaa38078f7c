/*
  # Initial Schema Setup

  1. New Tables
    - chat_history
      - id (uuid, primary key)
      - user_id (uuid, references auth.users)
      - question (text)
      - response (text)
      - image_url (text, nullable)
      - created_at (timestamptz)

  2. Security
    - Enable RLS on chat_history table
    - Add policies for user access
*/

CREATE TABLE chat_history (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users NOT NULL,
  question text NOT NULL,
  response text NOT NULL,
  image_url text,
  created_at timestamptz DEFAULT now()
);

ALTER TABLE chat_history ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read own chat history"
  ON chat_history
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own chat history"
  ON chat_history
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);