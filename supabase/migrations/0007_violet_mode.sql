/*
  # Update Search Logs Table

  1. Changes
    - Add conditional checks for table and policy creation
    - Ensure idempotent migration that can be run multiple times safely

  2. Security
    - Maintain existing RLS policies if table exists
    - Add policies only if they don't exist
*/

-- Check if table exists before creating
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_tables 
    WHERE schemaname = 'public' 
    AND tablename = 'search_logs'
  ) THEN
    CREATE TABLE search_logs (
      id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
      latitude numeric(10,6),
      longitude numeric(10,6),
      status text NOT NULL CHECK (status IN ('granted', 'denied')),
      created_at timestamptz DEFAULT now(),
      user_id uuid REFERENCES auth.users
    );

    -- Enable RLS
    ALTER TABLE search_logs ENABLE ROW LEVEL SECURITY;
  END IF;
END $$;

-- Create policies if they don't exist
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'public' 
    AND tablename = 'search_logs' 
    AND policyname = 'Users can read own search logs'
  ) THEN
    CREATE POLICY "Users can read own search logs"
      ON search_logs
      FOR SELECT
      TO authenticated
      USING (auth.uid() = user_id);
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'public' 
    AND tablename = 'search_logs' 
    AND policyname = 'Anyone can insert search logs'
  ) THEN
    CREATE POLICY "Anyone can insert search logs"
      ON search_logs
      FOR INSERT
      TO anon, authenticated
      WITH CHECK (true);
  END IF;
END $$;