/*
  # Create article cache table

  1. New Tables
    - `article_cache`: Stores cached articles with metadata
      - `id` (int, primary key)
      - `articles` (jsonb, stores article data)
      - `created_at` (timestamp with timezone)

  2. Security
    - Enable RLS
    - Add policies for public read access
    - Add policy for service role updates
*/

-- Create article cache table with proper constraints
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_tables 
    WHERE schemaname = 'public' 
    AND tablename = 'article_cache'
  ) THEN
    CREATE TABLE article_cache (
      id int PRIMARY KEY DEFAULT 1,
      articles jsonb NOT NULL DEFAULT '[]'::jsonb,
      created_at timestamptz DEFAULT now(),
      CONSTRAINT single_row CHECK (id = 1)
    );

    -- Enable RLS
    ALTER TABLE article_cache ENABLE ROW LEVEL SECURITY;

    -- Create policies
    CREATE POLICY "Anyone can read article cache"
      ON article_cache
      FOR SELECT
      TO anon, authenticated
      USING (true);

    CREATE POLICY "Only service role can update article cache"
      ON article_cache
      FOR ALL
      TO service_role
      USING (true)
      WITH CHECK (true);

    -- Create index for faster timestamp lookups
    CREATE INDEX article_cache_created_at_idx 
      ON article_cache (created_at DESC);

    -- Insert initial empty cache
    INSERT INTO article_cache (id, articles, created_at)
    VALUES (1, '[]'::jsonb, now())
    ON CONFLICT (id) DO NOTHING;
  END IF;
END $$;