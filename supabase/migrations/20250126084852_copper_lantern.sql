/*
  # Article System Setup

  1. New Tables
    - `articles` - Stores fetched articles
    - `article_sources` - Stores trusted article sources
    - `article_metrics` - Tracks article system performance
    - `article_cache` - Caches recent articles
  
  2. Changes
    - Add automatic article fetching system
    - Add monitoring and metrics
    
  3. Security
    - Enable RLS
    - Add appropriate policies
*/

-- Drop existing tables if they exist
DROP TABLE IF EXISTS articles CASCADE;
DROP TABLE IF EXISTS article_sources CASCADE;
DROP TABLE IF EXISTS article_metrics CASCADE;
DROP TABLE IF EXISTS article_cache CASCADE;

-- Create articles table
CREATE TABLE articles (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  title text NOT NULL,
  summary text NOT NULL,
  content text NOT NULL,
  source text NOT NULL,
  link text NOT NULL,
  tags text[] DEFAULT '{}',
  status text NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
  published_at timestamptz,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create article sources table
CREATE TABLE article_sources (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  url text NOT NULL,
  is_trusted boolean DEFAULT false,
  last_fetch_at timestamptz,
  fetch_interval interval DEFAULT '1 hour',
  created_at timestamptz DEFAULT now()
);

-- Create article metrics table
CREATE TABLE article_metrics (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  total_requests integer DEFAULT 0,
  successful_requests integer DEFAULT 0,
  failed_requests integer DEFAULT 0,
  error_count integer DEFAULT 0,
  created_at timestamptz DEFAULT now()
);

-- Create article cache table
CREATE TABLE article_cache (
  id int PRIMARY KEY DEFAULT 1,
  articles jsonb NOT NULL DEFAULT '[]'::jsonb,
  updated_at timestamptz DEFAULT now(),
  CONSTRAINT single_row CHECK (id = 1)
);

-- Enable RLS
ALTER TABLE articles ENABLE ROW LEVEL SECURITY;
ALTER TABLE article_sources ENABLE ROW LEVEL SECURITY;
ALTER TABLE article_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE article_cache ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Anyone can read published articles"
  ON articles
  FOR SELECT
  USING (status = 'published');

CREATE POLICY "Anyone can read article cache"
  ON article_cache
  FOR SELECT
  USING (true);

-- Create function to update article cache
CREATE OR REPLACE FUNCTION update_article_cache()
RETURNS trigger AS $$
BEGIN
  -- Update the cache with latest published articles
  UPDATE article_cache
  SET 
    articles = (
      SELECT json_agg(
        json_build_object(
          'id', a.id,
          'title', a.title,
          'summary', a.summary,
          'source', a.source,
          'link', a.link,
          'tags', a.tags,
          'published_at', a.published_at
        )
      )
      FROM articles a
      WHERE a.status = 'published'
      ORDER BY a.published_at DESC
      LIMIT 50
    ),
    updated_at = NOW()
  WHERE id = 1;
  
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create trigger
CREATE TRIGGER update_article_cache_trigger
  AFTER INSERT OR UPDATE OR DELETE ON articles
  FOR EACH STATEMENT
  EXECUTE FUNCTION update_article_cache();

-- Insert initial trusted sources
INSERT INTO article_sources (name, url, is_trusted) VALUES
  ('Journal of Dental Research', 'https://journals.sagepub.com/home/<USER>', true),
  ('Journal of Clinical Periodontology', 'https://onlinelibrary.wiley.com/journal/1600051x', true),
  ('American Dental Association', 'https://www.ada.org/publications', true),
  ('International Journal of Dentistry', 'https://www.hindawi.com/journals/ijd/', true),
  ('Journal of Dental Education', 'https://onlinelibrary.wiley.com/journal/19307837', true)
ON CONFLICT DO NOTHING;

-- Insert initial empty cache
INSERT INTO article_cache (id, articles, updated_at)
VALUES (1, '[]'::jsonb, now())
ON CONFLICT (id) DO NOTHING;