/*
  # Admin System Tables

  1. New Tables
    - `user_profiles`
      - `user_id` (uuid, primary key)
      - `role` (text)
      - `created_at` (timestamp)
    
    - `system_settings`
      - `id` (int, primary key)
      - `maintenance_mode` (boolean)
      - `updated_by` (uuid)
      - `updated_at` (timestamp)

  2. Security
    - Enable <PERSON><PERSON> on both tables
    - Add policies for admin access
*/

-- Create user_profiles table
CREATE TABLE IF NOT EXISTS user_profiles (
  user_id uuid PRIMARY KEY REFERENCES auth.users,
  role text NOT NULL DEFAULT 'user',
  created_at timestamptz DEFAULT now()
);

-- Create system_settings table
CREATE TABLE IF NOT EXISTS system_settings (
  id int PRIMARY KEY DEFAULT 1,
  maintenance_mode boolean DEFAULT false,
  updated_by uuid REFERENCES auth.users,
  updated_at timestamptz DEFAULT now(),
  CONSTRAINT single_row CHECK (id = 1)
);

-- Enable RLS
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_settings ENABLE ROW LEVEL SECURITY;

-- Policies for user_profiles
CREATE POLICY "<PERSON><PERSON> can read all profiles"
  ON user_profiles
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE user_id = auth.uid() AND role = 'admin'
    )
  );

-- Policies for system_settings
CREATE POLICY "Anyone can read system settings"
  ON system_settings
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Only admins can update system settings"
  ON system_settings
  FOR UPDATE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE user_id = auth.uid() AND role = 'admin'
    )
  );