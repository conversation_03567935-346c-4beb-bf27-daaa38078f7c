-- Drop existing policies if they exist
DO $$ 
BEGIN
  DROP POLICY IF EXISTS "Anyone can read system settings" ON system_settings;
  DROP POLICY IF EXISTS "Only admins can update system settings" ON system_settings;
  DROP POLICY IF EXISTS "Service role has full access" ON system_settings;
EXCEPTION WHEN undefined_object THEN
  NULL;
END $$;

-- Create new policies
CREATE POLICY "Anyone can read system settings"
  ON system_settings
  FOR SELECT
  TO anon, authenticated
  USING (true);

CREATE POLICY "Only admins can update system settings"
  ON system_settings
  FOR UPDATE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_profiles
      WHERE user_id = auth.uid() AND role = 'admin'
    )
  );

CREATE POLICY "Service role has full access"
  ON system_settings
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- Insert default settings if they don't exist
INSERT INTO system_settings (id, maintenance_mode, updated_at)
VALUES (1, false, now())
ON CONFLICT (id) DO NOTHING;