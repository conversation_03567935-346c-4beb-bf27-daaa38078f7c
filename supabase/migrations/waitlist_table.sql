-- Waitlist Table for <PERSON><PERSON><PERSON> Assist

-- Create the waitlist table
CREATE TABLE public.waitlist_entries (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email TEXT NOT NULL UNIQUE,
  first_name TEXT,
  last_name TEXT,
  selected_plan TEXT NOT NULL,
  practice_name TEXT,
  practice_size TEXT,
  phone_number TEXT,
  message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Add constraint for valid selected_plan values
  CONSTRAINT valid_selected_plan CHECK (selected_plan IN ('basic', 'professional', 'enterprise'))
);

-- Create index for faster email lookups
CREATE INDEX waitlist_entries_email_idx ON public.waitlist_entries (email);

-- Enable RLS (Row Level Security)
ALTER TABLE public.waitlist_entries ENABLE ROW LEVEL SECURITY;

-- Create policy for inserting records (anyone can submit to waitlist)
CREATE POLICY insert_waitlist_entries ON public.waitlist_entries 
  FOR INSERT TO anon
  WITH CHECK (true);

-- Create policy for admin to view all records
CREATE POLICY admin_read_all_waitlist ON public.waitlist_entries
  FOR SELECT TO authenticated
  USING (auth.uid() IN (
    SELECT user_id FROM user_profiles WHERE role = 'admin'
  )); 