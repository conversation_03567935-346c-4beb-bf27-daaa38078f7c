// Follow Supabase Edge Function format
import { serve } from 'https://deno.land/std@0.131.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight request
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const { username, requestId } = await req.json();
    
    // Validate the request
    if (!username || !requestId || username !== 'MikeyMouse') {
      return new Response(
        JSON.stringify({ error: 'Unauthorized request' }),
        { 
          status: 401, 
          headers: { 
            ...corsHeaders,
            'Content-Type': 'application/json'
          } 
        }
      );
    }

    // Create a client with Admin privileges
    const supabaseUrl = Deno.env.get('SUPABASE_URL');
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
    const authHeader = req.headers.get('Authorization') || '';
    
    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error('Service role key or URL not available');
    }
    
    const supabaseAdmin = createClient(
      supabaseUrl,
      supabaseServiceKey,
      { global: { headers: { Authorization: authHeader } } }
    );

    // Set admin session
    const adminSession = {
      user_id: `admin-${requestId}`,
      role: 'admin',
      username: 'MikeyMouse',
      access_level: 'full',
      timestamp: new Date().toISOString()
    };

    // Store the admin session (in a table or Redis cache)
    const { error: sessionError } = await supabaseAdmin
      .from('admin_sessions')
      .insert([adminSession]);

    if (sessionError) throw sessionError;

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'Admin session established', 
        sessionId: requestId 
      }),
      { 
        headers: { 
          ...corsHeaders,
          'Content-Type': 'application/json'
        } 
      }
    );
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        status: 500, 
        headers: { 
          ...corsHeaders, 
          'Content-Type': 'application/json'
        } 
      }
    );
  }
}); 