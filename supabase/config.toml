# This file configures Supabase Auth settings
# Reference: https://supabase.com/docs/guides/auth/auth-self-hosting#configuration

[auth]
# General settings
site_url = "https://smilo.dental"
additional_redirect_urls = ["https://smilo.dental/*", "https://app.smilo.dental/*"]
jwt_expiry = 3600
enable_signup = true

# Security settings
enable_confirmations = true

# OTP settings (reduced from 900 to 300 seconds)
sms_otp_exp = 300
email_otp_exp = 300
phone_otp_exp = 300
otp_length = 6
max_otp_attempts = 3

# Password settings
enable_weak_passwords = false
enable_leaked_passwords_check = true
min_password_length = 12  # Increased from 8 to 12 for better security

# Rate limiting
max_confirm_attempts = 3
confirm_email_rate_limit_count = 3
confirm_email_rate_limit_period = 60
confirm_phone_rate_limit_count = 3
confirm_phone_rate_limit_period = 60

# Additional security settings
enable_mfa = true  # Enable multi-factor authentication
max_session_length = 604800  # 7 days in seconds
require_strong_passwords = true
