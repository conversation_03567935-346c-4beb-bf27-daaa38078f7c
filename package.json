{"name": "smilo-dental-assistant", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "node scripts/copy-seo-content.js && vite --host", "build": "node build.cjs", "build:ci": "node build.cjs --ci", "build:analyze": "ANALYZE=true node build.cjs", "build:optimized": "NODE_ENV=production npm run build", "build:fast": "npm run generate-icons && vite build --mode development", "preview": "vite preview --port 4173", "seo-preview": "vite preview --port 4173 --base=/resources/seo/", "build-seo": "npm run generate-icons && npm run copy-seo-content && VITE_SKIP_TF=true vite build", "copy-seo-content": "node scripts/copy-seo-content.js", "fetch-articles": "node src/scripts/fetchArticles.js", "fetch-articles-node": "node scripts/fetchArticlesNode.js", "setup-cron": "bash scripts/setup-cron.sh", "setup-knowledge-base": "node -r dotenv/config src/scripts/setupDentalKnowledgeBase.js", "articles": "node scripts/startArticleWorker.js", "generate-icons": "node scripts/generateIcons.js || echo 'Skipping icon generation'", "setup-article-system": "node src/scripts/setupArticleSystem.js", "test-article-fetch": "node src/scripts/testArticleFetch.js", "fix-articles-table": "node scripts/fix-articles-table.js", "fix-articles-direct": "node scripts/fix-articles-direct.js", "simple-article-fix": "node scripts/simple-article-fix.js", "add-missing-columns": "node scripts/add-missing-columns.js", "fix-database-direct": "node scripts/fix-database-direct.js", "check-articles": "node scripts/check-articles.js", "update-article-system": "node scripts/update-article-system.js", "test-fixed-article-fetch": "node scripts/test-fixed-article-fetch.js", "fetch-rss-only": "node scripts/fetch-rss-only.js", "check-env": "node scripts/check-env.js", "test-direct": "node scripts/test-direct.js", "test-wrapper": "node scripts/test-article-wrapper.js", "test-rss-feeds": "node scripts/test-rss-feeds.js", "test-updated-article-service": "node scripts/test-updated-article-service.js", "update-articles": "node scripts/scheduled-article-update.js", "server": "node server.js", "test-server": "node test-server.js", "dev:server": "nodemon src/server/server.js", "start": "npm run build:optimized && npm run server", "dev:full": "concurrently \"npm run dev\" \"npm run dev:server\"", "webpack": "webpack", "postinstall": "node scripts/check-env.js || echo 'Environment check skipped'", "performance:audit": "npm run build:analyze && echo 'Check stats.html for bundle analysis'", "performance:test": "npm run build && npm run preview", "clean": "rm -rf dist .vite node_modules/.vite", "clean:build": "rm -rf dist && npm run build"}, "dependencies": {"@googlemaps/google-maps-services-js": "^3.3.42", "@googlemaps/js-api-loader": "^1.16.8", "@heroicons/react": "^2.2.0", "@koa/cors": "^5.0.0", "@mediapipe/face_mesh": "^0.4.1633559619", "@nestjs/passport": "^11.0.5", "@react-three/drei": "^9.122.0", "@react-three/fiber": "^8.18.0", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/supabase-js": "^2.49.8", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "autoprefixer": "^10.4.16", "axios": "^1.9.0", "chart.js": "^4.4.8", "cheerio": "^1.0.0-rc.12", "compression": "^1.8.0", "date-fns": "^2.30.0", "dotenv": "^16.4.7", "emailjs-com": "^3.2.0", "express-rate-limit": "^7.1.5", "fast-xml-parser": "^4.5.3", "formidable": "^3.5.2", "framer-motion": "^10.18.0", "helmet": "^8.1.0", "html-entities": "^2.5.2", "js-cookie": "^3.0.5", "jsdom": "^22.1.0", "koa": "^2.16.1", "lodash": "^4.17.21", "lucide-react": "^0.503.0", "meyda": "^5.6.2", "micromark-util-encode": "^2.0.1", "ml-matrix": "^6.10.4", "nestjs-supabase-auth": "^1.0.9", "node-fetch": "^3.3.2", "nodemailer": "^6.10.0", "openai": "^4.85.4", "p-limit": "^5.0.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "postcss": "^8.5.4", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-calendar": "^5.1.0", "react-chartjs-2": "^5.3.0", "react-date-picker": "^11.0.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-error-boundary": "^4.1.2", "react-icons": "^5.5.0", "react-intersection-observer": "^9.5.3", "react-markdown": "^9.1.0", "react-native-base64": "^0.2.1", "react-native-ble-plx": "^3.5.0", "react-native-permissions": "^4.1.2", "react-router-dom": "^6.30.0", "react-select": "^5.8.0", "react-signature-canvas": "^1.1.0-alpha.1", "react-virtuoso": "^4.12.7", "recharts": "^2.10.3", "rehype-raw": "^7.0.0", "rehype-stringify": "^9.0.4", "remark": "^14.0.3", "remark-gfm": "^3.0.1", "remark-parse": "^10.0.2", "remark-rehype": "^10.1.0", "rss-parser": "^3.13.0", "sanitize-html": "^2.17.0", "swiper": "^11.2.8", "tailwindcss": "^3.3.5", "tar": "^6.2.1", "three": "^0.160.0", "three-mesh-bvh": "^0.8.0", "use-immer": "^0.11.0", "uuid": "^9.0.1", "vite-plugin-pwa": "^0.21.1", "web-namespaces": "^2.0.1", "ws": "^8.17.1", "xml2js": "^0.6.2"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/preset-env": "^7.26.9", "@babel/preset-react": "^7.26.3", "@rollup/pluginutils": "^5.1.4", "@types/express": "^4.17.21", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.16", "@types/node": "^22.15.3", "@types/passport-jwt": "^4.0.1", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@types/react-native": "^0.73.0", "@types/react-native-base64": "^0.2.2", "@vitejs/plugin-react": "^4.5.0", "babel-loader": "^10.0.0", "babel-plugin-transform-remove-console": "^6.9.4", "concurrently": "^8.2.2", "cors": "^2.8.5", "css-loader": "^7.1.2", "esbuild": "^0.25.5", "esm": "^3.2.25", "express": "^5.1.0", "http-proxy-middleware": "^3.0.5", "lightningcss": "^1.30.1", "nodemon": "^3.0.1", "postcss-loader": "^8.1.1", "rollup-plugin-visualizer": "^5.14.0", "sharp": "^0.32.6", "style-loader": "^4.0.0", "terser": "^5.40.0", "typescript": "^5.8.3", "vite": "^6.3.5", "webpack": "^5.99.5", "webpack-cli": "^6.0.1", "webpack-dev-middleware": "^7.0.0"}, "optionalDependencies": {"canvas": "^2.11.2"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "resolutions": {"@types/react": "^18.2.15", "next": "^15.2.3", "elliptic": "^6.5.4", "@babel/traverse": "^7.26.10", "@xmldom/xmldom": "^0.8.10", "path-to-regexp": "^6.2.1", "cross-spawn": "^7.0.3", "http-proxy-middleware": "^2.0.6", "image-size": "^1.1.1"}}