import forms from '@tailwindcss/forms';
import typography from '@tailwindcss/typography';

/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      screens: {
        'xs': '375px',
        'sm': '640px',
        'md': '768px',
        'lg': '1024px',
        'xl': '1280px',
        '2xl': '1536px',
        'iphone-se': '320px',
        'iphone-xr': '414px',
        'iphone-12': '390px',
        'portrait': {'raw': '(orientation: portrait)'},
        'landscape': {'raw': '(orientation: landscape)'},
        'tall': {'raw': '(min-height: 800px)'},
        'short': {'raw': '(max-height: 700px)'}
      },
      fontFamily: {
        sans: ['Plus Jakarta Sans', 'system-ui', 'sans-serif'],
        display: ['Playfair Display', 'serif'],
        heading: ['Montserrat', 'sans-serif'],
      },
      spacing: {
        'safe-top': 'env(safe-area-inset-top)',
        'safe-bottom': 'env(safe-area-inset-bottom)',
        'safe-left': 'env(safe-area-inset-left)',
        'safe-right': 'env(safe-area-inset-right)',
        'vh-fix': 'calc(var(--vh, 1vh) * 100)',
        'mobile-1': 'var(--spacing-mobile, 1rem)',
        'mobile-2': 'calc(var(--spacing-mobile, 1rem) * 2)',
        'mobile-3': 'calc(var(--spacing-mobile, 1rem) * 3)',
        'mobile-4': 'calc(var(--spacing-mobile, 1rem) * 4)',
      },
      colors: {
        primary: {
          50: '#eef2ff',
          100: '#e0e7ff',
          200: '#c7d2fe',
          300: '#a5b4fc',
          400: '#818cf8',
          500: '#6366f1',
          600: '#4f46e5',
          700: '#4338ca',
          800: '#3730a3',
          900: '#312e81',
          950: '#1e1b4b',
        },
        secondary: {
          50: '#f8fafc',
          100: '#f1f5f9',
          200: '#e2e8f0',
          300: '#cbd5e1',
          400: '#94a3b8',
          500: '#64748b',
          600: '#475569',
          700: '#334155',
          800: '#1e293b',
          900: '#0f172a',
          950: '#020617',
        },
        accent: {
          50: '#fdf4ff',
          100: '#fae8ff',
          200: '#f5d0fe',
          300: '#f0abfc',
          400: '#e879f9',
          500: '#d946ef',
          600: '#c026d3',
          700: '#a21caf',
          800: '#86198f',
          900: '#701a75',
          950: '#4a044e',
        },
        periwinkle: {
          50: '#f5f7ff',
          100: '#ebf0ff',
          200: '#d6e0ff',
          300: '#adc1ff',
          400: '#819dff',
          500: '#5475ff',
          600: '#3d5eff',
          700: '#2745ff',
          800: '#1e37e5',
          900: '#1a31cc'
        },
        royal: {
          50: '#f3f1ff',
          100: '#ebe5ff',
          200: '#d9ceff',
          300: '#bea6ff',
          400: '#9f75ff',
          500: '#843dff',
          600: '#7916ff',
          700: '#6600ff',
          800: '#5200cc',
          900: '#4400a3'
        },
        pearl: {
          50: '#fafafa',
          100: '#f5f5f5',
          200: '#eeeeee',
        },
      },
      backgroundImage: {
        'grid-pattern': "url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath opacity='.5' d='M96 95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9zm-1 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")",
        'mobile-gradient': "linear-gradient(135deg, rgba(79, 70, 229, 0.1), rgba(99, 102, 241, 0.1), rgba(168, 85, 247, 0.1))",
      },
      height: {
        'screen-small': '100vh',
        'screen-fixed': 'calc(var(--vh, 1vh) * 100)',
      },
      minHeight: {
        'screen-small': '100vh',
        'screen-fixed': 'calc(var(--vh, 1vh) * 100)',
      },
      maxWidth: {
        'mobile': '100%',
        'mobile-md': '90%',
        'mobile-lg': '95%',
      },
      fontSize: {
        'mobile-xs': '0.75rem',
        'mobile-sm': '0.875rem',
        'mobile-base': '1rem',
        'mobile-lg': '1.125rem',
        'mobile-xl': '1.25rem',
      },
      borderRadius: {
        'mobile': '0.75rem',
      },
      keyframes: {
        fadeInUp: {
          '0%': { opacity: '0', transform: 'translateY(10px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' }
        },
        gradient: {
          '0%, 100%': { backgroundPosition: '0% 50%' },
          '50%': { backgroundPosition: '100% 50%' }
        },
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' }
        },
        shimmer: {
          '0%': { backgroundPosition: '-200% 0' },
          '100%': { backgroundPosition: '200% 0' }
        },
        dash: {
          '0%': { strokeDashoffset: '20' },
          '100%': { strokeDashoffset: '0' }
        },
        'spin-slow': {
          '0%': { transform: 'rotate(0deg)' },
          '100%': { transform: 'rotate(360deg)' }
        },
        pulse: {
          '0%, 100%': { opacity: 0.6 },
          '50%': { opacity: 1 }
        },
        float: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-5px)' }
        },
        'float-slow': {
          '0%': { transform: 'translateY(0) translateX(0)' },
          '33%': { transform: 'translateY(-10px) translateX(5px)' },
          '66%': { transform: 'translateY(5px) translateX(-5px)' },
          '100%': { transform: 'translateY(0) translateX(0)' }
        },
        'float-medium': {
          '0%': { transform: 'translateY(0) translateX(0)' },
          '33%': { transform: 'translateY(10px) translateX(-10px)' },
          '66%': { transform: 'translateY(-5px) translateX(5px)' },
          '100%': { transform: 'translateY(0) translateX(0)' }
        },
        'float-fast': {
          '0%': { transform: 'translateY(0) translateX(0)' },
          '33%': { transform: 'translateY(-7px) translateX(-7px)' },
          '66%': { transform: 'translateY(7px) translateX(3px)' },
          '100%': { transform: 'translateY(0) translateX(0)' }
        },
        'pulse-soft': {
          '0%': { opacity: 0.5 },
          '50%': { opacity: 0.7 },
          '100%': { opacity: 0.5 }
        },
        'pulse-slow': {
          '0%, 100%': { opacity: 0.5 },
          '50%': { opacity: 0.7 }
        },
        wiggle: {
          '0%, 100%': { transform: 'rotate(-3deg)' },
          '50%': { transform: 'rotate(3deg)' }
        },
        bounce: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-15px)' }
        },
        glow: {
          '0%': { textShadow: '0 0 3px rgba(255,255,255,0.1)' },
          '50%': { textShadow: '0 0 5px rgba(255,255,255,0.2), 0 0 8px rgba(99,102,241,0.2)' },
          '100%': { textShadow: '0 0 3px rgba(255,255,255,0.1)' }
        },
        'scale-up': {
          '0%': { transform: 'scale(0.95)', opacity: '0.7' },
          '100%': { transform: 'scale(1)', opacity: '1' }
        },
        'border-glow': {
          '0%, 100%': { boxShadow: '0 0 3px rgba(99,102,241,0.2)' },
          '50%': { boxShadow: '0 0 8px rgba(99,102,241,0.4), 0 0 10px rgba(132,61,255,0.3)' }
        },
        'slide-in-left': {
          '0%': { transform: 'translateX(-20px)', opacity: '0' },
          '100%': { transform: 'translateX(0)', opacity: '1' }
        },
        'slide-in-right': {
          '0%': { transform: 'translateX(20px)', opacity: '0' },
          '100%': { transform: 'translateX(0)', opacity: '1' }
        },
        'float-orb': {
          '0%': { transform: 'translateY(0) translateX(0) scale(1)' },
          '25%': { transform: 'translateY(-15px) translateX(10px) scale(1.05)' },
          '50%': { transform: 'translateY(5px) translateX(15px) scale(0.95)' },
          '75%': { transform: 'translateY(10px) translateX(-5px) scale(1.02)' },
          '100%': { transform: 'translateY(0) translateX(0) scale(1)' }
        }
      },
      animation: {
        fadeInUp: 'fadeInUp 0.5s ease-out',
        fadeIn: 'fadeIn 0.3s ease-in',
        gradient: 'gradient 15s ease infinite',
        shimmer: 'shimmer 2s infinite linear',
        dash: 'dash 3s linear infinite',
        'spin-slow': 'spin-slow 15s linear infinite',
        pulse: 'pulse 4s ease-in-out infinite',
        float: 'float 5s ease-in-out infinite',
        'float-slow': 'float-slow 20s ease-in-out infinite',
        'float-medium': 'float-medium 15s ease-in-out infinite',
        'float-fast': 'float-fast 12s ease-in-out infinite',
        'pulse-soft': 'pulse-soft 8s ease-in-out infinite',
        'pulse-slow': 'pulse-slow 10s ease-in-out infinite',
        wiggle: 'wiggle 1s ease-in-out infinite',
        bounce: 'bounce 2s ease-in-out infinite',
        glow: 'glow 4s ease-in-out infinite',
        'scale-up': 'scale-up 0.4s ease-out',
        'border-glow': 'border-glow 3s ease-in-out infinite',
        'slide-in-left': 'slide-in-left 0.5s ease-out',
        'slide-in-right': 'slide-in-right 0.5s ease-out',
        'float-orb': 'float-orb 18s ease-in-out infinite'
      },
      typography: {
        DEFAULT: {
          css: {
            maxWidth: 'none',
            color: 'rgb(237 233 254)',
            fontFamily: 'Plus Jakarta Sans, sans-serif',
            h1: {
              color: 'rgb(129, 140, 248)',
              fontFamily: 'Montserrat, sans-serif',
              fontWeight: '600',
            },
            h2: {
              color: 'rgb(129, 140, 248)',
              fontFamily: 'Montserrat, sans-serif',
              fontWeight: '600',
            },
            h3: {
              color: 'rgb(255 255 255)',
              fontFamily: 'Montserrat, sans-serif',
              fontWeight: '500',
            },
            strong: {
              color: 'rgb(255 255 255)',
              fontWeight: '600',
            },
            a: {
              color: 'rgb(99 102 241)',
              '&:hover': {
                color: 'rgb(129 140 248)',
              },
            },
            code: {
              color: 'rgb(255 255 255)',
            },
            blockquote: {
              color: 'rgb(209 213 219)',
              fontFamily: 'Playfair Display, serif',
              fontStyle: 'italic',
            },
          },
        },
      },
      scale: {
        '98': '0.98',
        '102': '1.02',
      },
      transitionDuration: {
        '250': '250ms',
        '400': '400ms',
      },
      zIndex: {
        '60': '60',
        '70': '70',
        '80': '80',
        '90': '90',
        '100': '100',
        'modal': '100',
        'tooltip': '90',
        'dropdown': '80',
        'navbar': '70',
      },
    },
  },
  future: {
    hoverOnlyWhenSupported: true,
  },
  plugins: [
    forms,
    typography,
    function({ addComponents, theme }) {
      const components = {
        '.touch-enabled': {
          minHeight: 'var(--min-touch-target, 44px)',
          minWidth: 'var(--min-touch-target, 44px)',
          display: 'inline-flex',
          alignItems: 'center',
          justifyContent: 'center',
        },
        '.mobile-container': {
          width: '100%',
          paddingLeft: 'var(--spacing-mobile, 1rem)',
          paddingRight: 'var(--spacing-mobile, 1rem)',
        },
      };
      addComponents(components);
    },
    function({ addUtilities, theme, variants }) {
      const newUtilities = {
        '.safe-bottom': {
          paddingBottom: 'env(safe-area-inset-bottom, 0px)',
        },
        '.safe-top': {
          paddingTop: 'env(safe-area-inset-top, 0px)',
        },
        '.mobile-text-balance': {
          textWrap: 'balance',
        },
        '.portrait-fill': {
          '@media (orientation: portrait)': {
            width: '100%',
            height: 'auto',
          },
        },
        '.landscape-fill': {
          '@media (orientation: landscape)': {
            height: '100%',
            width: 'auto',
          },
        },
        '.scroll-snap-x': {
          scrollSnapType: 'x mandatory',
          scrollBehavior: 'smooth',
          '-webkit-overflow-scrolling': 'touch',
        },
        '.scroll-snap-center': {
          scrollSnapAlign: 'center',
        },
      };
      addUtilities(newUtilities, variants('responsive'));
    },
  ],
}