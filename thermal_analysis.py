#!/usr/bin/env python3
"""
Thermal Imaging Analysis for Dental Applications

This script trains a model to detect inflammation and other thermal abnormalities
in dental tissues using infrared thermography images.
"""

import os
import sys
import argparse
import json
import random
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
import time
import shutil
from pathlib import Path
from datetime import datetime

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
from torchvision import transforms, models
from torch.optim.lr_scheduler import ReduceLROnPlateau
from sklearn.model_selection import train_test_split
from sklearn.metrics import confusion_matrix, classification_report, accuracy_score, roc_auc_score

import cv2
from PIL import Image
import kagglehub

# Suppress warnings
import warnings
warnings.filterwarnings("ignore")

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description="Thermal Imaging Analysis for Dental Applications")
    parser.add_argument("--dataset", type=str, default="joebeachcapital/infrared-thermography-temperature",
                       help="Kaggle dataset identifier")
    parser.add_argument("--output_dir", type=str, default="./thermal_analysis_output",
                       help="Output directory for model and results")
    parser.add_argument("--batch_size", type=int, default=16,
                       help="Training batch size")
    parser.add_argument("--epochs", type=int, default=30,
                       help="Number of training epochs")
    parser.add_argument("--learning_rate", type=float, default=0.0001,
                       help="Learning rate")
    parser.add_argument("--model", type=str, default="resnet50",
                       choices=["resnet18", "resnet50", "densenet121"],
                       help="Model architecture")
    parser.add_argument("--image_size", type=int, default=256,
                       help="Image size for training")
    parser.add_argument("--device", type=str, default=None,
                       help="Device to use (cuda, mps, cpu, or None for auto)")
    parser.add_argument("--temperature_threshold", type=float, default=1.5,
                       help="Temperature difference threshold for abnormality in °C")
    return parser.parse_args()

def set_device(device_arg=None):
    """Set the device for PyTorch"""
    if device_arg:
        device = torch.device(device_arg)
    else:
        device = torch.device("cuda" if torch.cuda.is_available() else 
                             "mps" if torch.backends.mps.is_available() else 
                             "cpu")
    print(f"Using device: {device}")
    return device

class ThermalDataset(Dataset):
    """Dataset for thermal images"""
    def __init__(self, image_paths, labels, transform=None, image_size=256):
        self.image_paths = image_paths
        self.labels = labels
        self.transform = transform
        self.image_size = image_size
    
    def __len__(self):
        return len(self.image_paths)
    
    def __getitem__(self, idx):
        img_path = self.image_paths[idx]
        label = self.labels[idx]
        
        # Read image
        try:
            image = cv2.imread(img_path)
            # Convert BGR to RGB
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        except Exception as e:
            print(f"Error reading image {img_path}: {e}")
            # Return a placeholder image and label
            image = np.zeros((self.image_size, self.image_size, 3), dtype=np.uint8)
        
        # Apply transformations
        if self.transform:
            image = Image.fromarray(image)
            image = self.transform(image)
        
        # Return image and label
        return image, label

def download_and_prepare_dataset(dataset_id, output_dir, temperature_threshold=1.5):
    """Download and prepare dataset from Kaggle"""
    print(f"Downloading dataset {dataset_id}...")
    
    # Create directory for dataset
    dataset_dir = os.path.join(output_dir, "dataset")
    os.makedirs(dataset_dir, exist_ok=True)
    
    # Download dataset using Kaggle API
    try:
        dataset_path = kagglehub.model_download(dataset_id)
        print(f"Dataset downloaded to {dataset_path}")
    except Exception as e:
        print(f"Error downloading dataset: {e}")
        print("Creating synthetic dataset for testing...")
        return create_synthetic_dataset(dataset_dir, temperature_threshold)
    
    # Prepare dataset
    normal_dir = os.path.join(dataset_dir, "normal")
    abnormal_dir = os.path.join(dataset_dir, "abnormal")
    
    os.makedirs(normal_dir, exist_ok=True)
    os.makedirs(abnormal_dir, exist_ok=True)
    
    # Process downloaded images
    image_paths = []
    labels = []
    metadata = []
    
    # Walk through dataset directory
    for root, _, files in os.walk(dataset_path):
        for file in files:
            if file.lower().endswith(('.png', '.jpg', '.jpeg', '.tif', '.tiff')):
                img_path = os.path.join(root, file)
                
                # Extract temperature data if available (from filename or metadata)
                temp_data = extract_temperature_data(img_path, file)
                
                # Analyze image and determine if it's normal or abnormal
                is_abnormal, features = analyze_thermal_image(img_path, temp_data, temperature_threshold)
                
                # Destination directory based on classification
                dest_dir = abnormal_dir if is_abnormal else normal_dir
                
                # Copy image to appropriate directory
                dest_path = os.path.join(dest_dir, file)
                shutil.copy(img_path, dest_path)
                
                # Add to lists
                image_paths.append(dest_path)
                labels.append(1 if is_abnormal else 0)
                metadata.append({
                    "filename": file,
                    "features": features,
                    "label": "abnormal" if is_abnormal else "normal"
                })
    
    # Save metadata
    with open(os.path.join(dataset_dir, "metadata.json"), "w") as f:
        json.dump(metadata, f, indent=2)
    
    print(f"Dataset prepared. Total images: {len(image_paths)}")
    print(f"Normal images: {labels.count(0)}")
    print(f"Abnormal images: {labels.count(1)}")
    
    return image_paths, labels, metadata

def extract_temperature_data(img_path, filename):
    """Extract temperature data from image or filename"""
    temp_data = {
        "has_temperature": False,
        "max_temp": None,
        "min_temp": None,
        "avg_temp": None
    }
    
    # Try to extract from filename (example format: thermal_max38.5_min35.2.jpg)
    if "max" in filename and "min" in filename:
        try:
            max_part = filename.split("max")[1].split("_")[0]
            min_part = filename.split("min")[1].split("_")[0].split(".jpg")[0]
            temp_data["max_temp"] = float(max_part)
            temp_data["min_temp"] = float(min_part)
            temp_data["avg_temp"] = (temp_data["max_temp"] + temp_data["min_temp"]) / 2
            temp_data["has_temperature"] = True
        except:
            pass
    
    # If no temperature in filename, try to extract from image (pseudocode - implementation depends on specific thermal camera format)
    if not temp_data["has_temperature"]:
        # Placeholder for actual thermal image temperature extraction
        # This would depend on the specific format of your thermal images
        pass
    
    return temp_data

def analyze_thermal_image(img_path, temp_data, temperature_threshold):
    """Analyze thermal image to extract features and determine if abnormal"""
    # Read image
    image = cv2.imread(img_path)
    if image is None:
        print(f"Could not read image: {img_path}")
        return False, {}
    
    # Convert to grayscale for basic analysis
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # Extract features
    features = {}
    
    # If actual temperature data is available
    if temp_data["has_temperature"]:
        features["max_temp"] = temp_data["max_temp"]
        features["min_temp"] = temp_data["min_temp"]
        features["avg_temp"] = temp_data["avg_temp"]
        features["temp_range"] = temp_data["max_temp"] - temp_data["min_temp"]
        
        # Check for abnormality based on temperature
        is_abnormal = features["temp_range"] > temperature_threshold
    else:
        # Estimate temperature from pixel values (pseudocode)
        # In a real implementation, this would use calibration data from the thermal camera
        
        # Use image statistics as proxy for temperature
        features["max_value"] = int(np.max(gray))
        features["min_value"] = int(np.min(gray))
        features["avg_value"] = float(np.mean(gray))
        features["std_value"] = float(np.std(gray))
        
        # Detect hot spots (bright regions in thermal image)
        _, thresh = cv2.threshold(gray, 200, 255, cv2.THRESH_BINARY)
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        features["hotspot_count"] = len(contours)
        
        # Calculate histogram asymmetry
        hist = cv2.calcHist([gray], [0], None, [256], [0, 256])
        hist_norm = hist / hist.sum()
        features["histogram_skew"] = float(np.sum((np.arange(256) - features["avg_value"])**3 * hist_norm) / 
                                    (features["std_value"]**3))
        
        # Determine if abnormal based on image features
        is_abnormal = (features["hotspot_count"] > 3 or 
                       features["histogram_skew"] > 1.0 or
                       (features["max_value"] - features["min_value"]) > 100)
    
    return is_abnormal, features

def create_synthetic_dataset(dataset_dir, temperature_threshold):
    """Create synthetic dataset for testing"""
    print("Creating synthetic thermal dataset...")
    
    normal_dir = os.path.join(dataset_dir, "normal")
    abnormal_dir = os.path.join(dataset_dir, "abnormal")
    
    os.makedirs(normal_dir, exist_ok=True)
    os.makedirs(abnormal_dir, exist_ok=True)
    
    image_paths = []
    labels = []
    metadata = []
    
    # Create 100 synthetic images (50 normal, 50 abnormal)
    for i in range(100):
        is_abnormal = i >= 50
        
        # Create synthetic thermal image
        if is_abnormal:
            # Abnormal image with hot spots
            img = np.zeros((256, 256, 3), dtype=np.uint8)
            
            # Background temperature gradient
            for x in range(256):
                for y in range(256):
                    base_value = int(80 + (x + y) / 3)
                    img[y, x] = [base_value, base_value, base_value]
            
            # Add hot spots (bright regions)
            num_hotspots = random.randint(3, 7)
            for _ in range(num_hotspots):
                cx = random.randint(50, 206)
                cy = random.randint(50, 206)
                radius = random.randint(10, 30)
                intensity = random.randint(180, 255)
                cv2.circle(img, (cx, cy), radius, (intensity, intensity, intensity), -1)
                
            # Add random noise
            noise = np.random.normal(0, 15, img.shape).astype(np.uint8)
            img = cv2.add(img, noise)
            
            # Set temperature data
            max_temp = round(37.5 + random.uniform(1.0, 3.0), 1)
            min_temp = round(max_temp - random.uniform(1.5, 3.5), 1)
            
            filename = f"abnormal_thermal_{i:03d}_max{max_temp}_min{min_temp}.jpg"
            filepath = os.path.join(abnormal_dir, filename)
        else:
            # Normal image with uniform temperature
            img = np.zeros((256, 256, 3), dtype=np.uint8)
            
            # Background temperature gradient
            for x in range(256):
                for y in range(256):
                    base_value = int(100 + (x + y) / 5)
                    img[y, x] = [base_value, base_value, base_value]
            
            # Add minor variation
            num_variations = random.randint(1, 2)
            for _ in range(num_variations):
                cx = random.randint(50, 206)
                cy = random.randint(50, 206)
                radius = random.randint(10, 20)
                intensity = random.randint(130, 150)
                cv2.circle(img, (cx, cy), radius, (intensity, intensity, intensity), -1)
                
            # Add random noise
            noise = np.random.normal(0, 10, img.shape).astype(np.uint8)
            img = cv2.add(img, noise)
            
            # Set temperature data
            max_temp = round(36.5 + random.uniform(0.2, 0.8), 1)
            min_temp = round(max_temp - random.uniform(0.3, 1.0), 1)
            
            filename = f"normal_thermal_{i:03d}_max{max_temp}_min{min_temp}.jpg"
            filepath = os.path.join(normal_dir, filename)
        
        # Save image
        cv2.imwrite(filepath, img)
        
        # Create metadata
        avg_temp = round((max_temp + min_temp) / 2, 1)
        features = {
            "max_temp": max_temp,
            "min_temp": min_temp,
            "avg_temp": avg_temp,
            "temp_range": round(max_temp - min_temp, 1),
            "hotspot_count": num_hotspots if is_abnormal else num_variations
        }
        
        # Add to lists
        image_paths.append(filepath)
        labels.append(1 if is_abnormal else 0)
        metadata.append({
            "filename": filename,
            "features": features,
            "label": "abnormal" if is_abnormal else "normal"
        })
    
    # Save metadata
    with open(os.path.join(dataset_dir, "metadata.json"), "w") as f:
        json.dump(metadata, f, indent=2)
    
    print(f"Synthetic dataset created. Total images: {len(image_paths)}")
    print(f"Normal images: {labels.count(0)}")
    print(f"Abnormal images: {labels.count(1)}")
    
    return image_paths, labels, metadata

def create_model(model_name, num_classes=2):
    """Create a model for thermal image classification"""
    if model_name == "resnet18":
        model = models.resnet18(weights=models.ResNet18_Weights.DEFAULT)
        model.fc = nn.Linear(model.fc.in_features, num_classes)
    elif model_name == "resnet50":
        model = models.resnet50(weights=models.ResNet50_Weights.DEFAULT)
        model.fc = nn.Linear(model.fc.in_features, num_classes)
    elif model_name == "densenet121":
        model = models.densenet121(weights=models.DenseNet121_Weights.DEFAULT)
        model.classifier = nn.Linear(model.classifier.in_features, num_classes)
    else:
        raise ValueError(f"Unsupported model: {model_name}")
    
    return model

def create_data_loaders(image_paths, labels, batch_size=16, image_size=256):
    """Create training and validation data loaders"""
    # Split data into training and validation sets
    X_train, X_val, y_train, y_val = train_test_split(
        image_paths, labels, test_size=0.2, random_state=42, stratify=labels
    )
    
    # Define transforms
    train_transform = transforms.Compose([
        transforms.Resize((image_size, image_size)),
        transforms.RandomHorizontalFlip(),
        transforms.RandomRotation(10),
        transforms.ColorJitter(brightness=0.1, contrast=0.1),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    val_transform = transforms.Compose([
        transforms.Resize((image_size, image_size)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    # Create datasets
    train_dataset = ThermalDataset(X_train, y_train, transform=train_transform, image_size=image_size)
    val_dataset = ThermalDataset(X_val, y_val, transform=val_transform, image_size=image_size)
    
    # Create data loaders
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=4)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=4)
    
    return train_loader, val_loader, len(X_train), len(X_val)

def train_model(model, train_loader, val_loader, device, num_epochs=25, learning_rate=0.0001):
    """Train the model"""
    # Define loss function and optimizer
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=learning_rate)
    scheduler = ReduceLROnPlateau(optimizer, mode='max', factor=0.5, patience=5, verbose=True)
    
    # Initialize variables
    best_accuracy = 0.0
    best_model_weights = model.state_dict()
    
    # Training history
    history = {
        "train_loss": [],
        "train_acc": [],
        "val_loss": [],
        "val_acc": [],
        "val_auc": []
    }
    
    # Train model
    for epoch in range(num_epochs):
        epoch_start = time.time()
        print(f"Epoch {epoch+1}/{num_epochs}")
        
        # Training phase
        model.train()
        train_loss = 0.0
        train_correct = 0
        train_total = 0
        
        for inputs, labels in train_loader:
            inputs, labels = inputs.to(device), labels.to(device)
            
            # Zero the parameter gradients
            optimizer.zero_grad()
            
            # Forward pass
            outputs = model(inputs)
            loss = criterion(outputs, labels)
            
            # Backward pass and optimize
            loss.backward()
            optimizer.step()
            
            # Statistics
            train_loss += loss.item() * inputs.size(0)
            _, preds = torch.max(outputs, 1)
            train_correct += torch.sum(preds == labels.data)
            train_total += labels.size(0)
        
        epoch_train_loss = train_loss / train_total
        epoch_train_acc = train_correct.double() / train_total
        
        # Validation phase
        model.eval()
        val_loss = 0.0
        val_correct = 0
        val_total = 0
        val_preds = []
        val_labels = []
        val_probs = []
        
        with torch.no_grad():
            for inputs, labels in val_loader:
                inputs, labels = inputs.to(device), labels.to(device)
                
                # Forward pass
                outputs = model(inputs)
                loss = criterion(outputs, labels)
                
                # Statistics
                val_loss += loss.item() * inputs.size(0)
                _, preds = torch.max(outputs, 1)
                val_correct += torch.sum(preds == labels.data)
                val_total += labels.size(0)
                
                # Save predictions for AUC calculation
                val_preds.extend(preds.cpu().numpy())
                val_labels.extend(labels.cpu().numpy())
                val_probs.extend(F.softmax(outputs, dim=1)[:, 1].cpu().numpy())
        
        epoch_val_loss = val_loss / val_total
        epoch_val_acc = val_correct.double() / val_total
        
        # Calculate AUC
        try:
            epoch_val_auc = roc_auc_score(val_labels, val_probs)
        except:
            epoch_val_auc = 0.0
        
        # Update learning rate
        scheduler.step(epoch_val_acc)
        
        # Print epoch results
        epoch_time = time.time() - epoch_start
        print(f"Train Loss: {epoch_train_loss:.4f} Acc: {epoch_train_acc:.4f}")
        print(f"Val Loss: {epoch_val_loss:.4f} Acc: {epoch_val_acc:.4f} AUC: {epoch_val_auc:.4f}")
        print(f"Epoch Time: {epoch_time:.2f}s")
        print("-" * 60)
        
        # Save history
        history["train_loss"].append(epoch_train_loss)
        history["train_acc"].append(epoch_train_acc.item())
        history["val_loss"].append(epoch_val_loss)
        history["val_acc"].append(epoch_val_acc.item())
        history["val_auc"].append(epoch_val_auc)
        
        # Save best model
        if epoch_val_acc > best_accuracy:
            best_accuracy = epoch_val_acc
            best_model_weights = model.state_dict()
    
    # Load best model weights
    model.load_state_dict(best_model_weights)
    
    return model, history

def plot_training_history(history, output_dir):
    """Plot training history"""
    plt.figure(figsize=(15, 5))
    
    # Plot loss
    plt.subplot(1, 3, 1)
    plt.plot(history["train_loss"], label="Train Loss")
    plt.plot(history["val_loss"], label="Validation Loss")
    plt.xlabel("Epoch")
    plt.ylabel("Loss")
    plt.title("Loss Curves")
    plt.legend()
    
    # Plot accuracy
    plt.subplot(1, 3, 2)
    plt.plot(history["train_acc"], label="Train Accuracy")
    plt.plot(history["val_acc"], label="Validation Accuracy")
    plt.xlabel("Epoch")
    plt.ylabel("Accuracy")
    plt.title("Accuracy Curves")
    plt.legend()
    
    # Plot AUC
    plt.subplot(1, 3, 3)
    plt.plot(history["val_auc"], label="Validation AUC")
    plt.xlabel("Epoch")
    plt.ylabel("AUC")
    plt.title("AUC Curve")
    plt.legend()
    
    # Save figure
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "training_history.png"))
    plt.close()

def evaluate_model(model, val_loader, device, output_dir):
    """Evaluate the model on the validation set"""
    model.eval()
    all_preds = []
    all_labels = []
    all_probs = []
    
    with torch.no_grad():
        for inputs, labels in val_loader:
            inputs, labels = inputs.to(device), labels.to(device)
            
            # Forward pass
            outputs = model(inputs)
            _, preds = torch.max(outputs, 1)
            
            # Save predictions
            all_preds.extend(preds.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
            all_probs.extend(F.softmax(outputs, dim=1)[:, 1].cpu().numpy())
    
    # Calculate metrics
    accuracy = accuracy_score(all_labels, all_preds)
    conf_matrix = confusion_matrix(all_labels, all_preds)
    class_report = classification_report(all_labels, all_preds, target_names=["Normal", "Abnormal"], output_dict=True)
    
    try:
        auc = roc_auc_score(all_labels, all_probs)
    except:
        auc = 0.0
    
    # Print results
    print(f"Validation Accuracy: {accuracy:.4f}")
    print(f"Validation AUC: {auc:.4f}")
    print("Confusion Matrix:")
    print(conf_matrix)
    print("Classification Report:")
    print(classification_report(all_labels, all_preds, target_names=["Normal", "Abnormal"]))
    
    # Plot confusion matrix
    plt.figure(figsize=(10, 8))
    plt.imshow(conf_matrix, interpolation='nearest', cmap=plt.cm.Blues)
    plt.title('Confusion Matrix')
    plt.colorbar()
    
    classes = ["Normal", "Abnormal"]
    tick_marks = np.arange(len(classes))
    plt.xticks(tick_marks, classes, rotation=45)
    plt.yticks(tick_marks, classes)
    
    # Add text annotations
    thresh = conf_matrix.max() / 2.0
    for i in range(conf_matrix.shape[0]):
        for j in range(conf_matrix.shape[1]):
            plt.text(j, i, format(conf_matrix[i, j], 'd'),
                    horizontalalignment="center",
                    color="white" if conf_matrix[i, j] > thresh else "black")
    
    plt.tight_layout()
    plt.ylabel('True Label')
    plt.xlabel('Predicted Label')
    plt.savefig(os.path.join(output_dir, "confusion_matrix.png"))
    plt.close()
    
    # Save metrics to JSON
    metrics = {
        "accuracy": accuracy,
        "auc": auc,
        "confusion_matrix": conf_matrix.tolist(),
        "classification_report": class_report
    }
    
    with open(os.path.join(output_dir, "evaluation_metrics.json"), "w") as f:
        json.dump(metrics, f, indent=2)
    
    return metrics

def main():
    """Main function"""
    # Parse arguments
    args = parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Set device
    device = set_device(args.device)
    
    # Download and prepare dataset
    image_paths, labels, metadata = download_and_prepare_dataset(
        args.dataset, args.output_dir, args.temperature_threshold
    )
    
    # Create data loaders
    train_loader, val_loader, train_size, val_size = create_data_loaders(
        image_paths, labels, args.batch_size, args.image_size
    )
    
    print(f"Training dataset size: {train_size}")
    print(f"Validation dataset size: {val_size}")
    
    # Create model
    model = create_model(args.model)
    model = model.to(device)
    
    # Train model
    model, history = train_model(
        model, train_loader, val_loader, device, 
        num_epochs=args.epochs, learning_rate=args.learning_rate
    )
    
    # Plot training history
    plot_training_history(history, args.output_dir)
    
    # Evaluate model
    metrics = evaluate_model(model, val_loader, device, args.output_dir)
    
    # Save model
    model_path = os.path.join(args.output_dir, "model_best.pt")
    torch.save({
        "model_state_dict": model.state_dict(),
        "model_name": args.model,
        "image_size": args.image_size,
        "metrics": metrics,
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }, model_path)
    
    print(f"Model saved to {model_path}")
    
    # Save training configuration
    config = {
        "dataset": args.dataset,
        "output_dir": args.output_dir,
        "batch_size": args.batch_size,
        "epochs": args.epochs,
        "learning_rate": args.learning_rate,
        "model": args.model,
        "image_size": args.image_size,
        "device": str(device),
        "temperature_threshold": args.temperature_threshold,
        "train_size": train_size,
        "val_size": val_size,
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "best_accuracy": metrics["accuracy"],
        "best_auc": metrics["auc"]
    }
    
    with open(os.path.join(args.output_dir, "training_config.json"), "w") as f:
        json.dump(config, f, indent=2)
    
    print(f"Training configuration saved to {os.path.join(args.output_dir, 'training_config.json')}")
    print("Thermal analysis training complete!")

if __name__ == "__main__":
    main() 