# Database Setup for User History Tracking

This document provides instructions for setting up the necessary database tables to support user history tracking features.

## Current Status

The application has been updated to support user history tracking, but the required database tables haven't been created yet. Until these tables are created, you'll see the following error in the browser console:

```
POST https://your-supabase-url.supabase.co/rest/v1/user_page_history 404 (Not Found)
```

We've implemented a graceful fallback that will disable history tracking features until the tables are created.

## Setting Up the Database Tables

### Option 1: Using the Supabase Dashboard

1. Log in to your Supabase Dashboard (https://app.supabase.com)
2. Select your project
3. Go to the SQL Editor section
4. Create a new query
5. Copy and paste the contents of the `create_user_history_tables.sql` file
6. Run the query to create the tables

### Option 2: Using the Supabase CLI

If you have the Supabase CLI installed:

1. Navigate to your project directory
2. Run the following command:
   ```bash
   supabase db push --db-url=your_supabase_db_url
   ```

## Table Schemas

The following tables will be created:

### 1. user_page_history

Stores information about pages visited by logged-in users.

Fields:
- `id`: UUID (Primary Key)
- `user_id`: UUID (Foreign Key to auth.users)
- `path`: TEXT
- `title`: TEXT
- `timestamp`: TIMESTAMPTZ

### 2. user_search_history

Stores search queries made by logged-in users.

Fields:
- `id`: UUID (Primary Key)
- `user_id`: UUID (Foreign Key to auth.users)
- `query`: TEXT
- `category`: TEXT
- `timestamp`: TIMESTAMPTZ

## Security

Both tables have Row Level Security (RLS) policies enabled:
- Users can only view or insert their own history records
- The application server can view all records for analytics
- Anonymous users have no access

## Verification

After running the SQL script, you can verify the tables were created successfully by:

1. Going to the "Table Editor" in the Supabase Dashboard
2. You should see both tables listed
3. Check that RLS is enabled for both tables

Once the tables are created, the user history tracking features will automatically begin working. 