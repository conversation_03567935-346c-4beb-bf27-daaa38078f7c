FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies 
RUN npm install --legacy-peer-deps --no-optional --force

# Copy source code
COPY . .

# Pre-build check
RUN node railway-prebuild.cjs

# Build the application (skipping TensorFlow)
ENV VITE_SKIP_TF=true
ENV NODE_ENV=production
RUN npm run build

# Expose the port
EXPOSE 3000

# Start the server
CMD ["npm", "run", "server"] 