import express from 'express';
import path from 'path';
import compression from 'compression';
import { fileURLToPath } from 'url';
import openaiProxy from './src/server/openaiProxy.js';
import placesProxy from './src/server/placesProxy.js';
import locationProxy from './src/server/locationProxy.js';
import { OPENAI_API_KEY, GOOGLE_MAPS_API_KEY } from './src/lib/config.js';

// Get current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = process.env.PORT || 3000;

// Display API key status
console.log('Test server API key status:');
console.log('- OpenAI API Key:', OPENAI_API_KEY ? 'Loaded (OK)' : 'Missing (X)');
console.log('- Google Maps API Key:', GOOGLE_MAPS_API_KEY ? 'Loaded (OK)' : 'Missing (X)');

// Use compression
app.use(compression());

// Set security headers
app.use((req, res, next) => {
  // Set proper Content Security Policy
  res.setHeader(
    'Content-Security-Policy',
    "default-src 'self'; " +
    "script-src 'self' https://apis.google.com https://*.googleapis.com https://www.googletagmanager.com https://polyfill.io 'unsafe-inline' 'unsafe-eval'; " +
    "style-src 'self' https://fonts.googleapis.com 'unsafe-inline'; " +
    "img-src 'self' data: blob: https://*.googleapis.com https://*.gstatic.com https://twskhrwvdrebsghyczlu.supabase.co https://images.weserv.nl; " +
    "font-src 'self' data: https://fonts.gstatic.com; " +
    "connect-src 'self' https://twskhrwvdrebsghyczlu.supabase.co wss://twskhrwvdrebsghyczlu.supabase.co https://api.openai.com https://*.googleapis.com http://localhost:* wss://localhost:*; " +
    "frame-src 'self' https://www.google.com; " +
    "object-src 'none';"
  );
  
  // Other security headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'SAMEORIGIN');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  next();
});

// Register API proxy routes the same way as the main server
app.use('/api/openai', openaiProxy);
app.use('/api/places', placesProxy);
app.use('/api/location', locationProxy);

// Serve static files from the dist directory
app.use(express.static(path.join(__dirname, 'dist')));

// Serve the mobile debug page
app.get('/mobile-debug.html', (req, res) => {
  res.sendFile(path.join(__dirname, 'mobile-debug.html'));
});

// Handle all other routes by serving index.html
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist', 'index.html'));
});

// Start the server
app.listen(PORT, '0.0.0.0', () => {
  console.log(`Test server running on port ${PORT}`);
  console.log(`Open http://localhost:${PORT} in your browser`);
});
