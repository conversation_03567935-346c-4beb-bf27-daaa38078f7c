{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["DOM", "DOM.Iterable", "ESNext"], "types": ["lodash"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "Node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "typeRoots": ["./node_modules/@types", "./src/types"]}, "include": ["src", "src/types/*.d.ts"], "exclude": ["src/components/bluetooth/**/*", "**/*.native.ts", "**/*.native.tsx"], "references": [{"path": "./tsconfig.node.json"}]}