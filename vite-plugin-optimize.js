// Vite plugin to optimize JavaScript and CSS
import { createFilter } from '@rollup/pluginutils';

/**
 * Custom Vite plugin to optimize JavaScript and CSS
 * @param {Object} options - Plugin options
 * @returns {Object} - Vite plugin
 */
export default function optimizePlugin(options = {}) {
  const defaultOptions = {
    jsFilter: /\.(js|jsx|ts|tsx)$/,
    cssFilter: /\.css$/,
    exclude: /node_modules/,
    ...options
  };

  const jsFilter = createFilter(defaultOptions.jsFilter, defaultOptions.exclude);
  const cssFilter = createFilter(defaultOptions.cssFilter, defaultOptions.exclude);

  return {
    name: 'vite-plugin-optimize',
    
    // Transform JavaScript files
    transform(code, id) {
      if (jsFilter(id)) {
        // Remove console.log statements in production
        if (process.env.NODE_ENV === 'production') {
          code = code.replace(/console\.log\(.*?\);?/g, '');
        }
        
        return {
          code,
          map: null
        };
      }
      
      // Transform CSS files
      if (cssFilter(id)) {
        // Remove comments and whitespace in production
        if (process.env.NODE_ENV === 'production') {
          code = code.replace(/\/\*[\s\S]*?\*\//g, '') // Remove comments
                     .replace(/\s+/g, ' ') // Collapse whitespace
                     .trim();
        }
        
        return {
          code,
          map: null
        };
      }
    },
    
    // Configure Rollup options
    config(config) {
      // Ensure tree-shaking is enabled
      if (!config.build) config.build = {};
      if (!config.build.rollupOptions) config.build.rollupOptions = {};
      
      config.build.rollupOptions.treeshake = true;
      
      // Add manualChunks configuration if not already present
      if (!config.build.rollupOptions.output) {
        config.build.rollupOptions.output = {};
      }
      
      if (!config.build.rollupOptions.output.manualChunks) {
        config.build.rollupOptions.output.manualChunks = {
          vendor: ['react', 'react-dom', 'react-router-dom'],
          ui: ['framer-motion', 'tailwindcss'],
        };
      }
      
      return config;
    }
  };
}
