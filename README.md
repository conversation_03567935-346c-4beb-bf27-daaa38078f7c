# Smilo.Dental

A comprehensive dental platform with AI-powered tools for dental health education and analysis.

## Features

### Breath Sound Analysis Using Machine Learning

The Breath Analyzer DenTech tool uses machine learning techniques to analyze breath sounds captured by the device's microphone. This advanced feature helps detect breath phases (inhalation, exhalation, and pauses) and provides insights into potential oral health conditions.

**Technical Implementation:**
- **MFCC Feature Extraction**: Analyzes acoustic properties using Mel-Frequency Cepstral Coefficients
- **Spectrogram Classification**: Visualizes and analyzes frequency patterns over time
- **Zero-Crossing Analysis**: Detects frequency characteristics of breath sounds
- **VSC Detection**: Estimates volatile sulfur compound levels associated with halitosis

**User Experience:**
- Real-time feedback during breath recording
- Visual spectrogram display showing frequency patterns
- ML-based classification of breath phases
- Personalized recommendations based on analysis results

### Additional AI Tools

- Voice Analysis Tool
- Oral Scanner
- Symptom Checker

## Mobile Optimization

Smilo.Dental is fully optimized for mobile devices, ensuring a seamless experience across all screen sizes:

### Responsive Design Features
- **Adaptive Layouts**: Automatically adjusts to any screen size (mobile, tablet, desktop)
- **Touch-Optimized UI**: Larger touch targets (44px minimum) for better mobile interaction
- **Mobile Viewport Fixes**: Addresses iOS Safari viewport height issues
- **Performance Optimizations**: Reduced animations and effects on low-end devices
- **Cross-Browser Compatibility**: Tested on Safari, Chrome, Firefox, and Edge

### Mobile-Specific Enhancements
- **Optimized Touch Interactions**: Improved touch feedback and gesture support
- **Reduced Network Footprint**: Optimized image loading for mobile networks
- **Offline Support**: Core functionality works without constant internet connection
- **Adaptive Performance**: Automatically detects device capabilities and adjusts accordingly

For testing guidelines, see [CROSS_BROWSER_MOBILE_TESTING.md](./CROSS_BROWSER_MOBILE_TESTING.md)

## Getting Started

```bash
# Install dependencies
npm install

# Run development server
npm run dev
```

## Technologies

- React
- TensorFlow.js for ML-based audio analysis
- Meyda for audio feature extraction
- OpenAI for NLP-based analysis
- Supabase for backend storage
- Tailwind CSS for responsive styling
- Framer Motion for optimized animations

## Repository Initialization Notice

This repository was automatically initialized by the Bolt to GitHub extension.

**Auto-Generated Repository**
- Created to ensure a valid Git repository structure
- Serves as an initial commit point for your project