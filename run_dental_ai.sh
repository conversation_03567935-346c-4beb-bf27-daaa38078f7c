#!/bin/bash
# Dental AI Runner Script
# This script provides a convenient way to run various Dental AI scripts

# Set default paths
MODEL_DIR="$(pwd)/models"
FACE_MODEL="$MODEL_DIR/facial_analysis_model.pt"
THERMAL_MODEL="$MODEL_DIR/thermal_analysis_model.pt"
OUTPUT_DIR="$(pwd)/output"

# Create directories if they don't exist
mkdir -p "$MODEL_DIR"
mkdir -p "$OUTPUT_DIR"

# Check for Python 3
if ! command -v python3 &> /dev/null; then
    echo "Python 3 is required but not found on your system"
    exit 1
fi

# Display menu
function show_menu() {
    clear
    echo "==================================================="
    echo "           Dental AI - Tools and Analysis          "
    echo "==================================================="
    echo "TRAINING OPTIONS:"
    echo "1. Train Cavity Classification Model"
    echo "2. Train Food Cavity Risk Model"
    echo "3. Train Oral Cancer Detection Model"
    echo "4. Train Facial Analysis Model"
    echo "5. Train Thermal Imaging Analysis Model"
    echo ""
    echo "PREDICTION OPTIONS:"
    echo "6. Predict Cavity from Image"
    echo "7. Analyze Food Cavity Risk from Data"
    echo "8. Analyze Facial Features for Dental Health"
    echo "9. Analyze Thermal Images for Dental Health"
    echo ""
    echo "TEST OPTIONS:"
    echo "10. Run Test Script (Oral Cancer Detection)"
    echo "11. Run Test Script (Facial Analysis)"
    echo "12. Run Test Script (Thermal Analysis)"
    echo ""
    echo "13. Run Comprehensive Dental Health Advisor"
    echo "0. Exit"
    echo "==================================================="
    echo "Enter your choice: "
}

# Function to train cavity classification model
function train_cavity_classifier() {
    echo "Training cavity classification model..."
    
    # Prompt for dataset path
    read -p "Enter dataset path (or leave empty for default): " dataset_path
    dataset_path=${dataset_path:-"default_dataset"}
    
    # Prompt for model type
    read -p "Enter model type (resnet18, resnet50, efficientnet) [default: resnet50]: " model_type
    model_type=${model_type:-"resnet50"}
    
    # Prompt for batch size
    read -p "Enter batch size [default: 16]: " batch_size
    batch_size=${batch_size:-16}
    
    # Prompt for number of epochs
    read -p "Enter number of epochs [default: 25]: " epochs
    epochs=${epochs:-25}
    
    # Run the training script
    python3 cavity_classifier.py \
        --dataset "$dataset_path" \
        --model "$model_type" \
        --batch_size "$batch_size" \
        --epochs "$epochs" \
        --output_dir "${OUTPUT_DIR}/cavity_classifier_output"
    
    echo "Training complete! Model saved to ${OUTPUT_DIR}/cavity_classifier_output"
    read -n 1 -s -r -p "Press any key to continue..."
}

# Function to train food cavity risk model
function train_food_cavity_risk() {
    echo "Training food cavity risk model..."
    
    # Prompt for dataset ID
    read -p "Enter Kaggle dataset ID (or leave empty for default): " dataset_id
    dataset_id=${dataset_id:-"dsxavier/food-nutrients"}
    
    # Prompt for model type
    read -p "Enter model type (mlp, random_forest, xgboost) [default: random_forest]: " model_type
    model_type=${model_type:-"random_forest"}
    
    # Run the training script
    python3 food_cavity_classifier.py \
        --dataset "$dataset_id" \
        --model "$model_type" \
        --output_dir "${OUTPUT_DIR}/food_cavity_classifier_output"
    
    echo "Training complete! Model saved to ${OUTPUT_DIR}/food_cavity_classifier_output"
    read -n 1 -s -r -p "Press any key to continue..."
}

# Function to train oral cancer model
function train_oral_cancer_model() {
    echo "Training oral cancer detection model..."
    
    # Prompt for dataset ID
    read -p "Enter Kaggle dataset ID (or leave empty for default): " dataset_id
    dataset_id=${dataset_id:-"kbezrouk/oral-cancer-screening-data"}
    
    # Prompt for model type
    read -p "Enter model type (resnet18, resnet50, densenet121) [default: resnet50]: " model_type
    model_type=${model_type:-"resnet50"}
    
    # Prompt for batch size
    read -p "Enter batch size [default: 16]: " batch_size
    batch_size=${batch_size:-16}
    
    # Prompt for number of epochs
    read -p "Enter number of epochs [default: 30]: " epochs
    epochs=${epochs:-30}
    
    # Prompt for learning rate
    read -p "Enter learning rate [default: 0.0001]: " learning_rate
    learning_rate=${learning_rate:-0.0001}
    
    # Run the training script
    python3 oral_cancer_classifier.py \
        --dataset "$dataset_id" \
        --model "$model_type" \
        --batch_size "$batch_size" \
        --epochs "$epochs" \
        --learning_rate "$learning_rate" \
        --output_dir "${OUTPUT_DIR}/oral_cancer_classifier_output"
    
    echo "Training complete! Model saved to ${OUTPUT_DIR}/oral_cancer_classifier_output"
    read -n 1 -s -r -p "Press any key to continue..."
}

# Function to train facial analysis model
function train_facial_analysis_model() {
    echo "Training facial analysis model for dental health..."
    
    # Prompt for dataset ID
    read -p "Enter Kaggle dataset ID (or leave empty for default): " dataset_id
    dataset_id=${dataset_id:-"kpvisionlab/tufts-face-database"}
    
    # Prompt for model type
    read -p "Enter model type (resnet18, resnet50, densenet121) [default: resnet50]: " model_type
    model_type=${model_type:-"resnet50"}
    
    # Prompt for batch size
    read -p "Enter batch size [default: 16]: " batch_size
    batch_size=${batch_size:-16}
    
    # Prompt for number of epochs
    read -p "Enter number of epochs [default: 25]: " epochs
    epochs=${epochs:-25}
    
    # Prompt for learning rate
    read -p "Enter learning rate [default: 0.0001]: " learning_rate
    learning_rate=${learning_rate:-0.0001}
    
    # Prompt for features to analyze
    read -p "Enter features to analyze (comma-separated) [default: asymmetry,jaw_alignment,smile_analysis]: " features
    features=${features:-"asymmetry,jaw_alignment,smile_analysis"}
    
    # Convert comma-separated string to array format
    features="[\"${features//,/\",\"}\"]"
    
    # Run the training script
    python3 face_analysis.py \
        --dataset "$dataset_id" \
        --model "$model_type" \
        --batch_size "$batch_size" \
        --epochs "$epochs" \
        --learning_rate "$learning_rate" \
        --features "$features" \
        --output_dir "${OUTPUT_DIR}/facial_analysis_output"
    
    # Save the model path for future use
    FACE_MODEL="${OUTPUT_DIR}/facial_analysis_output/model_best.pt"
    
    echo "Training complete! Model saved to ${OUTPUT_DIR}/facial_analysis_output"
    read -n 1 -s -r -p "Press any key to continue..."
}

# Function to train thermal imaging analysis model
function train_thermal_analysis_model() {
    echo "Training thermal imaging analysis model for dental health..."
    
    # Prompt for dataset ID
    read -p "Enter Kaggle dataset ID (or leave empty for default): " dataset_id
    dataset_id=${dataset_id:-"joebeachcapital/infrared-thermography-temperature"}
    
    # Prompt for model type
    read -p "Enter model type (resnet18, resnet50, densenet121) [default: resnet50]: " model_type
    model_type=${model_type:-"resnet50"}
    
    # Prompt for batch size
    read -p "Enter batch size [default: 16]: " batch_size
    batch_size=${batch_size:-16}
    
    # Prompt for number of epochs
    read -p "Enter number of epochs [default: 30]: " epochs
    epochs=${epochs:-30}
    
    # Prompt for learning rate
    read -p "Enter learning rate [default: 0.0001]: " learning_rate
    learning_rate=${learning_rate:-0.0001}
    
    # Prompt for temperature threshold
    read -p "Enter temperature difference threshold (°C) [default: 1.5]: " temp_threshold
    temp_threshold=${temp_threshold:-1.5}
    
    # Run the training script
    python3 thermal_analysis.py \
        --dataset "$dataset_id" \
        --model "$model_type" \
        --batch_size "$batch_size" \
        --epochs "$epochs" \
        --learning_rate "$learning_rate" \
        --temperature_threshold "$temp_threshold" \
        --output_dir "${OUTPUT_DIR}/thermal_analysis_output"
    
    # Save the model path for future use
    THERMAL_MODEL="${OUTPUT_DIR}/thermal_analysis_output/model_best.pt"
    
    echo "Training complete! Model saved to ${OUTPUT_DIR}/thermal_analysis_output"
    read -n 1 -s -r -p "Press any key to continue..."
}

# Function to predict cavity from image
function predict_cavity() {
    echo "Predicting cavity from dental image..."
    
    # Prompt for model path
    read -p "Enter cavity model path (or leave empty for default): " model_path
    model_path=${model_path:-"${OUTPUT_DIR}/cavity_classifier_output/cavity_classifier_model.pt"}
    
    # Prompt for input image
    read -p "Enter path to dental image: " image_path
    
    if [ -z "$image_path" ]; then
        echo "Error: Image path is required"
        read -n 1 -s -r -p "Press any key to continue..."
        return
    fi
    
    # Run the prediction script
    python3 cavity_predict.py \
        --model_path "$model_path" \
        --input "$image_path" \
        --output_dir "${OUTPUT_DIR}/cavity_predictions"
    
    echo "Prediction complete! Results saved to ${OUTPUT_DIR}/cavity_predictions"
    read -n 1 -s -r -p "Press any key to continue..."
}

# Function to analyze food cavity risk
function analyze_food_cavity_risk() {
    echo "Analyzing food cavity risk..."
    
    # Prompt for model path
    read -p "Enter food cavity risk model path (or leave empty for default): " model_path
    model_path=${model_path:-"${OUTPUT_DIR}/food_cavity_classifier_output/model_best.pt"}
    
    # Prompt for food data
    read -p "Enter food name or path to nutrition data CSV: " food_data
    
    if [ -z "$food_data" ]; then
        echo "Error: Food data is required"
        read -n 1 -s -r -p "Press any key to continue..."
        return
    fi
    
    # Run the prediction script
    python3 food_cavity_predict.py \
        --model_path "$model_path" \
        --input "$food_data" \
        --output_dir "${OUTPUT_DIR}/food_risk_predictions"
    
    echo "Analysis complete! Results saved to ${OUTPUT_DIR}/food_risk_predictions"
    read -n 1 -s -r -p "Press any key to continue..."
}

# Function to analyze facial features
function analyze_facial_features() {
    echo "Analyzing facial features for dental health..."
    
    # Prompt for model path
    read -p "Enter facial analysis model path (or leave empty for default): " model_path
    model_path=${model_path:-"$FACE_MODEL"}
    
    # Check if default model exists
    if [ "$model_path" == "$FACE_MODEL" ] && [ ! -f "$FACE_MODEL" ]; then
        echo "Default facial analysis model not found. Please train or specify a valid model path."
        read -n 1 -s -r -p "Press any key to continue..."
        return
    fi
    
    # Prompt for input image
    read -p "Enter path to facial image: " image_path
    
    if [ -z "$image_path" ]; then
        echo "Error: Image path is required"
        read -n 1 -s -r -p "Press any key to continue..."
        return
    fi
    
    # Prompt for threshold
    read -p "Enter classification threshold [default: 0.5]: " threshold
    threshold=${threshold:-0.5}
    
    # Prompt for detailed analysis
    read -p "Show detailed analysis? (y/n) [default: y]: " detailed_input
    detailed_input=${detailed_input:-"y"}
    
    if [[ "$detailed_input" == "y" || "$detailed_input" == "Y" ]]; then
        detailed_flag="--detailed"
    else
        detailed_flag=""
    fi
    
    # Run the prediction script
    python3 face_predict.py \
        --model_path "$model_path" \
        --input "$image_path" \
        --threshold "$threshold" \
        --output_dir "${OUTPUT_DIR}/facial_analysis_predictions" \
        $detailed_flag
    
    echo "Analysis complete! Results saved to ${OUTPUT_DIR}/facial_analysis_predictions"
    read -n 1 -s -r -p "Press any key to continue..."
}

# Function to analyze thermal images
function analyze_thermal_images() {
    echo "Analyzing thermal images for dental health..."
    
    # Prompt for model path
    read -p "Enter thermal analysis model path (or leave empty for default): " model_path
    model_path=${model_path:-"$THERMAL_MODEL"}
    
    # Check if default model exists
    if [ "$model_path" == "$THERMAL_MODEL" ] && [ ! -f "$THERMAL_MODEL" ]; then
        echo "Default thermal analysis model not found. Please train or specify a valid model path."
        read -n 1 -s -r -p "Press any key to continue..."
        return
    fi
    
    # Prompt for input image or directory
    read -p "Enter path to thermal image or directory: " input_path
    
    if [ -z "$input_path" ]; then
        echo "Error: Input path is required"
        read -n 1 -s -r -p "Press any key to continue..."
        return
    fi
    
    # Prompt for threshold
    read -p "Enter abnormality threshold [default: 0.5]: " threshold
    threshold=${threshold:-0.5}
    
    # Prompt for detailed analysis
    read -p "Show detailed analysis? (y/n) [default: y]: " detailed_input
    detailed_input=${detailed_input:-"y"}
    
    if [[ "$detailed_input" == "y" || "$detailed_input" == "Y" ]]; then
        detailed_flag="--detailed"
    else
        detailed_flag=""
    fi
    
    # Run the prediction script
    python3 thermal_predict.py \
        --model_path "$model_path" \
        --input "$input_path" \
        --threshold "$threshold" \
        --output_dir "${OUTPUT_DIR}/thermal_predictions" \
        $detailed_flag
    
    echo "Analysis complete! Results saved to ${OUTPUT_DIR}/thermal_predictions"
    read -n 1 -s -r -p "Press any key to continue..."
}

# Function to run oral cancer test script
function run_oral_cancer_test() {
    echo "Running oral cancer detection test script..."
    
    # Prompt for model path
    read -p "Enter oral cancer model path (or leave empty for default): " model_path
    model_path=${model_path:-"${OUTPUT_DIR}/oral_cancer_classifier_output/model_best.pt"}
    
    # Prompt for download samples flag
    read -p "Download sample images if model not found? (y/n) [default: y]: " download_input
    download_input=${download_input:-"y"}
    
    if [[ "$download_input" == "y" || "$download_input" == "Y" ]]; then
        download_flag="--download_samples"
    else
        download_flag=""
    fi
    
    # Run the test script
    python3 test_oral_cancer.py \
        --model_path "$model_path" \
        $download_flag \
        --output_dir "${OUTPUT_DIR}/oral_cancer_test_results"
    
    echo "Test complete! Results saved to ${OUTPUT_DIR}/oral_cancer_test_results"
    read -n 1 -s -r -p "Press any key to continue..."
}

# Function to run facial analysis test script
function run_facial_analysis_test() {
    echo "Running facial analysis test script..."
    
    # Prompt for model path
    read -p "Enter facial analysis model path (or leave empty for default): " model_path
    model_path=${model_path:-"$FACE_MODEL"}
    
    # Prompt for download samples flag
    read -p "Download sample images if model not found? (y/n) [default: y]: " download_input
    download_input=${download_input:-"y"}
    
    if [[ "$download_input" == "y" || "$download_input" == "Y" ]]; then
        download_flag="--download_samples"
    else
        download_flag=""
    fi
    
    # Run the test script
    python3 test_face_analysis.py \
        --model_path "$model_path" \
        $download_flag \
        --output_dir "${OUTPUT_DIR}/facial_analysis_test_results"
    
    echo "Test complete! Results saved to ${OUTPUT_DIR}/facial_analysis_test_results"
    read -n 1 -s -r -p "Press any key to continue..."
}

# Function to run thermal analysis test script
function run_thermal_analysis_test() {
    echo "Running thermal imaging analysis test script..."
    
    # Prompt for model path
    read -p "Enter thermal analysis model path (or leave empty for default): " model_path
    model_path=${model_path:-"$THERMAL_MODEL"}
    
    # Prompt for download samples flag
    read -p "Download sample images if model not found? (y/n) [default: y]: " download_input
    download_input=${download_input:-"y"}
    
    if [[ "$download_input" == "y" || "$download_input" == "Y" ]]; then
        download_flag="--download_samples"
    else
        download_flag=""
    fi
    
    # Prompt for detailed analysis
    read -p "Show detailed analysis? (y/n) [default: y]: " detailed_input
    detailed_input=${detailed_input:-"y"}
    
    if [[ "$detailed_input" == "y" || "$detailed_input" == "Y" ]]; then
        detailed_flag="--detailed"
    else
        detailed_flag=""
    fi
    
    # Run the test script
    python3 test_thermal.py \
        --model_path "$model_path" \
        $download_flag \
        --output_dir "${OUTPUT_DIR}/thermal_test_results" \
        $detailed_flag
    
    echo "Test complete! Results saved to ${OUTPUT_DIR}/thermal_test_results"
    read -n 1 -s -r -p "Press any key to continue..."
}

# Function to run comprehensive dental health advisor
function run_dental_advisor() {
    echo "Running comprehensive dental health advisor..."
    
    # Prompt for cavity model
    read -p "Enter cavity model path (or leave empty for default): " cavity_model
    cavity_model=${cavity_model:-"${OUTPUT_DIR}/cavity_classifier_output/cavity_classifier_model.pt"}
    
    # Prompt for food risk model
    read -p "Enter food risk model path (or leave empty for default): " food_model
    food_model=${food_model:-"${OUTPUT_DIR}/food_cavity_classifier_output/model_best.pt"}
    
    # Prompt for cancer model
    read -p "Enter cancer model path (or leave empty for default): " cancer_model
    cancer_model=${cancer_model:-"${OUTPUT_DIR}/oral_cancer_classifier_output/model_best.pt"}
    
    # Prompt for facial analysis model
    read -p "Enter facial analysis model path (or leave empty for default): " face_model
    face_model=${face_model:-"$FACE_MODEL"}
    
    # Prompt for thermal analysis model
    read -p "Enter thermal analysis model path (or leave empty for default): " thermal_model
    thermal_model=${thermal_model:-"$THERMAL_MODEL"}
    
    # Prompt for input directory
    read -p "Enter path to directory containing input images and data: " input_dir
    
    if [ -z "$input_dir" ]; then
        echo "Error: Input directory is required"
        read -n 1 -s -r -p "Press any key to continue..."
        return
    fi
    
    # Run the advisor script
    python3 dental_health_advisor.py \
        --cavity_model "$cavity_model" \
        --food_model "$food_model" \
        --cancer_model "$cancer_model" \
        --face_model "$face_model" \
        --thermal_model "$thermal_model" \
        --inputs "$input_dir" \
        --output_dir "${OUTPUT_DIR}/dental_advisor_results"
    
    echo "Analysis complete! Results saved to ${OUTPUT_DIR}/dental_advisor_results"
    read -n 1 -s -r -p "Press any key to continue..."
}

# Main loop
while true; do
    show_menu
    read -r choice
    
    case $choice in
        1) train_cavity_classifier ;;
        2) train_food_cavity_risk ;;
        3) train_oral_cancer_model ;;
        4) train_facial_analysis_model ;;
        5) train_thermal_analysis_model ;;
        6) predict_cavity ;;
        7) analyze_food_cavity_risk ;;
        8) analyze_facial_features ;;
        9) analyze_thermal_images ;;
        10) run_oral_cancer_test ;;
        11) run_facial_analysis_test ;;
        12) run_thermal_analysis_test ;;
        13) run_dental_advisor ;;
        0) echo "Exiting..."; exit 0 ;;
        *) echo "Invalid option. Please try again."; read -n 1 -s -r -p "Press any key to continue..." ;;
    esac
done 