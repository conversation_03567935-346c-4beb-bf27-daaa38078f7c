// Check if required files exist before building
const fs = require('fs');
const path = require('path');

console.log('🔍 Running pre-build checks...');

// Check if MirrorModeCoach file exists
const mirrorModeCoachPath = path.join(__dirname, 'src/components/ai-tools/MirrorModeCoach.tsx');
if (!fs.existsSync(mirrorModeCoachPath)) {
  console.error('❌ MirrorModeCoach.tsx file missing at:', mirrorModeCoachPath);
  if (fs.existsSync(path.join(__dirname, 'src/components/ai-tools/MirrorModeCoach.jsx'))) {
    console.log('✅ Found MirrorModeCoach.jsx instead, renaming to .tsx...');
    fs.copyFileSync(
      path.join(__dirname, 'src/components/ai-tools/MirrorModeCoach.jsx'),
      mirrorModeCoachPath
    );
  }
}

// Verify the index.ts file has correct exports
const aiToolsIndexPath = path.join(__dirname, 'src/components/ai-tools/index.ts');
if (fs.existsSync(aiToolsIndexPath)) {
  const indexContent = fs.readFileSync(aiToolsIndexPath, 'utf8');
  console.log('✅ AI Tools index.ts content:', indexContent);
} else {
  console.error('❌ AI Tools index.ts file missing!');
}

console.log('✅ Pre-build checks completed'); 